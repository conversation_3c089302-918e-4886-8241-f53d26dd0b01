*.class

.DS\_Store
*.sw?
*.svn
*.log

# Package Files #
*.jar
*.war
*.ear

# Eclipse Files #
.classpath
.project
.settings/
.pydevproject

# Target Folder #
target/
deb-packaging/
/api/.springBeans
activemq-data
/processor/.springBeans/target
/.settings

# Frontend Files #
**/node_modules/*/
**/node/*/
**/build/*/
package-lock.json
/farfisa.iml
bin
bin/

/build.log
/.idea
/qa/*.iml
/qa/.idea
/capacity-tests/*.iml
/capacity-tests/.idea
/capacity-tests/jgiven-reports
/.springBeans
/farfisa-api/*.iml
/farfisa-common/*.iml
/farfisa-processor/*.iml
/farfisa-data/*.iml
/farfisa-calculator/*.iml
/farfisa-calculator/farfisa-calculator-common/*.iml
/farfisa-calculator/farfisa-calculator-server/*.iml
emotics.uploader.iml
/farfisa-common/src/test/resources/data/xbox.csv
farfisa-api/logs/
*.gz
farfisa-api/integration_test/csv/mahout/output/

farfisa-calculator/farfisa-calculator-common/farfisa-calculator-common.iml
farfisa-calculator/farfisa-calculator-server/farfisa-calculator-server.iml
*.iml
