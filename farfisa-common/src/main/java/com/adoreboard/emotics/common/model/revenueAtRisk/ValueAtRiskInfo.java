package com.adoreboard.emotics.common.model.revenueAtRisk;

import com.adoreboard.emotics.common.enums.ValueAtRiskType;
import com.adoreboard.emotics.common.enums.ValueAtRiskWeight;
import com.adoreboard.emotics.common.util.NumberUtils;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ValueAtRiskInfo extends RevenueAtRiskTemplateInfo {
    private BigDecimal valueAtRiskAmount;
    private List<Integer> selectedThemeIds = new ArrayList<>();

    public ValueAtRiskInfo(ValueAtRiskType valueAtRiskType, ValueAtRiskWeight valueAtRiskWeight) {
        this.valueAtRiskType = valueAtRiskType;
        this.valueAtRiskWeight = valueAtRiskWeight;
    }

    public BigDecimal getValueAtRiskAmount() {
        return valueAtRiskAmount != null ? valueAtRiskAmount.setScale(2, RoundingMode.HALF_UP) : null;
    }

    @JsonIgnoreProperties
    public String getValueAtRiskAmountString() {
        return getValueAtRiskAmount() != null
                && BigDecimal.ZERO.compareTo(getValueAtRiskAmount()) < 0
                ? NumberUtils.formatCurrencyNumber(this.getValueAtRiskAmount()) : "0.00";
    }
}
