/**
 * ~  Copyright 2022 Adoreboard Ltd. All rights reserved.
 * ~  ADOREBOARD PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 * Date: 17/11/2023
 * <p>
 * The properties of the com.adoreboard.emotics.common.model.workspace
 *
 * <AUTHOR>
 */
package com.adoreboard.emotics.common.model.workspace;

import com.adoreboard.emotics.common.model.enums.VarCurrency;
import com.adoreboard.emotics.common.model.storyteller.InsightSettings;
import com.adoreboard.emotics.common.model.storyteller.enums.ReportCategory;
import lombok.*;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class WorkspaceSetting {

    private String fontAwesomeIcon;
    private boolean allowToEditStopwords;

    private String organisationSector;

    @Builder.Default
    private ReportCategory reportCategory = ReportCategory.customer_experience;
    private String reportCategoryExtraInfo;

    private VarCurrency currency;
    private InsightSettings insightSettings;
}
