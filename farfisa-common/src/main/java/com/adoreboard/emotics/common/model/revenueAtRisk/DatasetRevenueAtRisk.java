package com.adoreboard.emotics.common.model.revenueAtRisk;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DatasetRevenueAtRisk {
    private Integer id;
    private Integer datasetId;
    private Integer templateId;
    private Integer createdBy;
    private ValueAtRiskInfo revenueAtRisk;
    private LocalDateTime createdAt = LocalDateTime.now();
    private LocalDateTime updateAt;

}
