package com.adoreboard.emotics.common.model.revenueAtRisk;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class RevenueAtRiskWithTemplate extends ValueAtRiskInfo {
    private Integer templateId; // todo - write a migration script to create a custom template for existing datasets
}
