package com.adoreboard.emotics.common.model.revenueAtRisk;

import com.adoreboard.emotics.common.enums.ValueAtRiskType;
import com.adoreboard.emotics.common.enums.ValueAtRiskWeight;
import com.adoreboard.emotics.common.model.enums.VarCurrency;
import com.adoreboard.emotics.common.util.NumberUtils;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Currency;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class RevenueAtRiskTemplateInfo {
    // CX Fields
    private Double customerSpendAvgAnnual = 0.0;
    private Double customerAdditionalCost = 0.0;
    private Integer numberOfCustomers = 1;
    private Integer totalYear = 1;
    // EX Fields
    private Double employeeSalary = 0.0;
    private Double employeeAdditionalCost = 0.0;
    private Integer numberOfEmployees = 1;

    private Boolean scaleToTotalPeople = Boolean.FALSE;

    protected ValueAtRiskType valueAtRiskType = ValueAtRiskType.CUSTOMER;
    protected ValueAtRiskWeight valueAtRiskWeight = ValueAtRiskWeight.MEDIUM;

    private VarCurrency currency = VarCurrency.USD;

    public RevenueAtRiskTemplateInfo(ValueAtRiskType valueAtRiskType, ValueAtRiskWeight valueAtRiskWeight) {
        this.valueAtRiskType = valueAtRiskType;
        this.valueAtRiskWeight = valueAtRiskWeight;
    }

    @JsonIgnoreProperties
    public double getTotalCustomerAmount() {
        double spend = customerSpendAvgAnnual == null ? 0 : customerSpendAvgAnnual;
        return spend + customerAdditionalCost ;
    }

    @JsonIgnoreProperties
    public double getTotalEmployeeAmount() {
        double salary = employeeSalary == null ? 0 : employeeSalary;
        return salary + employeeAdditionalCost;
    }

    @JsonIgnoreProperties
    public Integer getNumberOfScalePeople() {
        return valueAtRiskType.equals(ValueAtRiskType.CUSTOMER) ? numberOfCustomers : numberOfEmployees;
    }

    @JsonIgnoreProperties
    public double getTotalAmount() {
        return valueAtRiskType.equals(ValueAtRiskType.CUSTOMER) ? getTotalCustomerAmount() : getTotalEmployeeAmount();
    }

    public String getCurrencySymbol() {
        return currency != null ? Currency.getInstance(currency.name()).getSymbol() : Currency.getInstance("USD").getSymbol() ;
    }

    public int getTotalYear() {
        //only customer lifetime value has total year
        return valueAtRiskType.equals(ValueAtRiskType.CUSTOMER) ? totalYear : 1;
    }

}
