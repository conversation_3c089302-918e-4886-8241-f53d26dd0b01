/*
 * Copyright 2022 Adoreboard Ltd. All rights reserved.
 * ADOREBOARD PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */

package com.adoreboard.emotics.common.mybatis;

import com.adoreboard.emotics.common.model.revenueAtRisk.RevenueAtRiskTemplateStatus;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * MyBatis type handler for RevenueAtRiskTemplateStatus enum
 *
 * <AUTHOR> Chau
 */
public class RevenueAtRiskTemplateStatusTypeHandler extends BaseTypeHandler<RevenueAtRiskTemplateStatus> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, RevenueAtRiskTemplateStatus parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, parameter.getStatus());
    }

    @Override
    public RevenueAtRiskTemplateStatus getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String status = rs.getString(columnName);
        return getStatusFromString(status);
    }

    @Override
    public RevenueAtRiskTemplateStatus getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String status = rs.getString(columnIndex);
        return getStatusFromString(status);
    }

    @Override
    public RevenueAtRiskTemplateStatus getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String status = cs.getString(columnIndex);
        return getStatusFromString(status);
    }

    private RevenueAtRiskTemplateStatus getStatusFromString(String status) {
        if (status == null) {
            return null;
        }

        for (RevenueAtRiskTemplateStatus templateStatus : RevenueAtRiskTemplateStatus.values()) {
            if (templateStatus.getStatus().equals(status)) {
                return templateStatus;
            }
        }

        // Default to ACTIVE if unknown status
        return RevenueAtRiskTemplateStatus.ACTIVE;
    }
}
