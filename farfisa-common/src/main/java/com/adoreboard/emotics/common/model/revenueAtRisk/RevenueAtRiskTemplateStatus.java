package com.adoreboard.emotics.common.model.revenueAtRisk;

public enum RevenueAtRiskTemplateStatus {
    ACTIVE("active"),
    DELETED("deleted"),;

    private final String status;

    RevenueAtRiskTemplateStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    @Override
    public String toString() {
        return status;
    }
}
