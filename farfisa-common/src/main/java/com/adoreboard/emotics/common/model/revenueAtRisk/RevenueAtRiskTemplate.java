package com.adoreboard.emotics.common.model.revenueAtRisk;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class RevenueAtRiskTemplate {
    private Integer id;
    private String name;
    private Integer workspaceId;
    private Integer datasetId;
    private Integer linkedTemplateId;
    private Integer createdBy;
    @Builder.Default
    private LocalDateTime createdAt = LocalDateTime.now();
    private LocalDateTime updateAt;
    private boolean defaultTemplate;
    @Builder.Default
    private RevenueAtRiskTemplateStatus status = RevenueAtRiskTemplateStatus.ACTIVE;
    private RevenueAtRiskTemplateInfo revenueAtRisk;
}
