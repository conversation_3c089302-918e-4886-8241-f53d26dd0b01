/*
 * Copyright 2022 Adoreboard Ltd. All rights reserved.
 * ADOREBOARD PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */

package com.adoreboard.emotics.common.configuration;

import com.adoreboard.emotics.common.mapper.*;
import com.adoreboard.emotics.common.mapper.view.UserDocViewMapper;
import com.adoreboard.emotics.common.util.DBUtils;
import org.apache.commons.dbcp.ManagedBasicDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.core.io.Resource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

/**
 * Configuration of Database mappers
 *
 * <AUTHOR> Lee
 * <AUTHOR> Huy Vu
 */
@Configuration
@ComponentScan("com.adoreboard.emotics.common.mapper")
public class JdbcConfiguration {

    private static final Logger logger = LogManager.getLogger(JdbcConfiguration.class);

    @Value("${jdbc.driver}")
    private String driver;

    @Value("${jdbc.url}")
    private String url;

    @Value("${jdbc.username}")
    private String username;

    @Value("${jdbc.password}")
    private String password;

    @Value("${jdbc.poolsize:10}")
    private int poolsize;

    @Value("${jdbc.validationQuery:SELECT 1}")
    private String validationQuery;

    @Value("${jdbc.typeHandlersPackage}")
    private String typeHandlersPackage;

    @Value("${jdbc.mapperLocations}")
    private Resource[] mapperLocations;

    @Bean
    public ManagedBasicDataSource getDataSource() {
        ManagedBasicDataSource dataSource = new ManagedBasicDataSource();
        dataSource.setDriverClassName(driver);
        dataSource.setUrl(url);
        dataSource.setUsername(username);
        dataSource.setPassword(password);
        dataSource.setValidationQuery(validationQuery);
        dataSource.setMinIdle(1);
        dataSource.setMaxIdle(poolsize);
        dataSource.setMaxOpenPreparedStatements(500);
        dataSource.setInitialSize(poolsize);
        dataSource.setMaxActive(poolsize);

        return dataSource;
    }

    @Bean
    public JdbcTemplate jdbcTemplate() {
        JdbcTemplate jdbcTemplate = new JdbcTemplate(getDataSource());
        return jdbcTemplate;
    }

    @Bean
    public SqlSessionFactoryBean sqlSessionFactoryBean() {
        SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(getDataSource());
        sqlSessionFactoryBean.setTypeHandlersPackage(typeHandlersPackage);
        sqlSessionFactoryBean.setMapperLocations(mapperLocations);

        return sqlSessionFactoryBean;
    }

    @Bean
    public SqlSessionFactory sqlSessionFactory() {
        SqlSessionFactory sqlSessionFactory = null;
        try {
            sqlSessionFactory = sqlSessionFactoryBean().getObject();
            sqlSessionFactory.getConfiguration().setMapUnderscoreToCamelCase(true);
        }
        catch (Exception e) {
            logger.error(e.getMessage());
        }

        return sqlSessionFactory;
    }

    @Bean
    public DataSourceTransactionManager dataSourceTransactionManager() {
        DataSourceTransactionManager dataSourceTransactionManager = new DataSourceTransactionManager();
        dataSourceTransactionManager.setDataSource(getDataSource());
        return dataSourceTransactionManager;
    }

    @Bean
    public UserMapper userMapper() {
        return getMapper(UserMapper.class);
    }

    @Bean
    public UserTierMapper userTierMapper() {
        return getMapper(UserTierMapper.class);
    }

    @Bean
    public UserDocMapper userDocMapper() {
        return getMapper(UserDocMapper.class);
    }

    @Bean
    public UserDocViewMapper userDocViewMapper() {
        return getMapper(UserDocViewMapper.class);
    }

    @Bean
    public UserTopicMapper userTopicMapper() {
        return getMapper(UserTopicMapper.class);
    }

    @Bean
    public UserTopicDocMapper userTopicDocMapper() {
        return getMapper(UserTopicDocMapper.class);
    }

    @Bean
    public DatasetMapper datasetMapper() {
        return getMapper(DatasetMapper.class);
    }

    @Bean
    public DatasetDownloadDetailsMapper datasetDownloadDetailsMapper() { return getMapper(DatasetDownloadDetailsMapper.class); }

    @Bean
    public DatasetCustomChartsMapper datasetCustomChartsMapper() { return getMapper(DatasetCustomChartsMapper.class); }

    @Bean
    public InsightsScorecardMapper insightsScorecardMapper() { return getMapper(InsightsScorecardMapper.class); }

    @Bean
    public DatasetStatusMapper datasetStatusMapper() {
        return getMapper(DatasetStatusMapper.class);
    }

    @Bean
    public DatasetSummaryMapper datasetSummaryMapper() {
        return getMapper(DatasetSummaryMapper.class);
    }

    @Bean
    public DatasetTagMapper datasetTagMapper() {
        return getMapper(DatasetTagMapper.class);
    }

    @Bean
    public DatasetPermissionMapper datasetPermissionMapper() { return getMapper(DatasetPermissionMapper.class); }

    @Bean
    public RegistrationMapper registrationMapper() {
        return getMapper(RegistrationMapper.class);
    }

    @Bean
    public StopWordMapper stopWordMapper() {
        return getMapper(StopWordMapper.class);
    }

    @Bean
    public BatchMapper batchMapper() {
        return getMapper(BatchMapper.class);
    }

    @Bean
    public WrongContentMapper wrongContentMapper() {
        return getMapper(WrongContentMapper.class);
    }

    @Bean
    public UserContentMapper userContentMapper() {
        return getMapper(UserContentMapper.class);
    }

    @Bean
    public UserContentMetadataMapper userContentMetadataMapper() {
        return getMapper(UserContentMetadataMapper.class);
    }

    @Bean
    public DatasetContentsMapper datasetContentsMapper() {
        return getMapper(DatasetContentsMapper.class);
    }

    @Bean
    public TranslationDataMapper translationDataMapper() {
        return getMapper(TranslationDataMapper.class);
    }

    @Bean
    public UsageRecordMapper usageRecordMapper() {
        return getMapper(UsageRecordMapper.class);
    }

    @Bean
    public LoginDataMapper loginStatsMapper() { return getMapper(LoginDataMapper.class); }

    @Bean
    public JourneyMapper journeyMapper() { return getMapper(JourneyMapper.class); }

    @Bean
    public SearchComponentsMapper searchMapper() { return getMapper(SearchComponentsMapper.class); }

    @Bean
    public AggregationMapper aggregationMapper() { return getMapper(AggregationMapper.class); }

    @Bean
    public KeyPhraseMapper keyPhrasesMapper() { return getMapper(KeyPhraseMapper.class); }

    @Bean
    public RegressionModelMapper regressionModelMapper() { return getMapper(RegressionModelMapper.class); }

    @Bean
    public CustomTopicMapper customTopicMapper() { return getMapper(CustomTopicMapper.class); }

    @Bean
    public MetadataMapper metadataMapper() { return getMapper(MetadataMapper.class); }

    @Bean
    public SavedActionMapper savedActionMapper() { return getMapper(SavedActionMapper.class); }

    @Bean
    public SavedActionListMapper savedActionListMapper() { return getMapper(SavedActionListMapper.class); }

    @Bean
    public WorkspaceMapper workspaceMapper() { return getMapper(WorkspaceMapper.class); }

    @Bean
    public WorkspaceGroupMapper workspaceGroupMapper() { return getMapper(WorkspaceGroupMapper.class); }

    @Bean
    public StorytellerReportMapper storytellerReportMapper() { return getMapper(StorytellerReportMapper.class); }

    @Bean
    public StorytellerInsightSlideMapper storytellerSlideMapper() { return getMapper(StorytellerInsightSlideMapper.class); }

    @Bean
    public StorytellerActionPlanSlideMapper storytellerActionPlansMapper() { return getMapper(StorytellerActionPlanSlideMapper.class); }

    @Bean
    public ThemeSummaryMapper themeSummaryMapper() { return getMapper(ThemeSummaryMapper.class); }

    @Bean
    public SurveyMapper surveyMapper() { return getMapper(SurveyMapper.class); }

    @Bean
    public OrganisationMapper organisationMapper() {return  getMapper(OrganisationMapper.class); }

    @Bean
    public InsightsMapper insightsMapper() {return getMapper(InsightsMapper.class); }

    @Bean
    public RatingScaleMapper ratingScaleMapper() {
        return getMapper(RatingScaleMapper.class);
    }

    @Bean
    public ReportingJobMapper reportingJobMapper() {return getMapper(ReportingJobMapper.class); }

    @Bean
    public ReportingJobDataSourceMapper reportingJobDataSourceMapper() {return getMapper(ReportingJobDataSourceMapper.class); }

    @Bean
    public ReportingJobEmailConfigMapper reportingJobEmailRecipientMapper() {return getMapper(ReportingJobEmailConfigMapper.class); }

    @Bean
    public ReportingJobSplitRuleMapper reportingJobSplitRuleMapper() {return getMapper(ReportingJobSplitRuleMapper.class); }

    @Bean
    public ReportingJobWorkspaceRuleMapper reportingJobWorkspaceRuleMapper() {return getMapper(ReportingJobWorkspaceRuleMapper.class); }

    @Bean
    public ReportingJobSearchThemeMapper reportingJobSearchThemeMapper() {return getMapper(ReportingJobSearchThemeMapper.class); }

    @Bean
    public ReportingJobResultMapper reportingJobResultMapper() {return getMapper(ReportingJobResultMapper.class); }

    @Bean
    public ReportingJobExecutionStatusMapper reportingJobExecutionStatusMapper() {return getMapper(ReportingJobExecutionStatusMapper.class); }

    @Bean
    public CommentUIMapper getCommentUIMapper() {
        return getMapper(CommentUIMapper.class);
    }

    @Bean
    public RevenueAtRiskTemplateMapper revenueAtRiskTemplateMapper() {
        return getMapper(RevenueAtRiskTemplateMapper.class);
    }

    @Bean
    public DatasetRevenueAtRiskMapper datasetRevenueAtRiskMapper() {
        return getMapper(DatasetRevenueAtRiskMapper.class);
    }


    //Docker only method for setting up database only gets called when
    //spring profile is set to docker.
    @Profile("docker")
    @Bean(initMethod = "setupDB")
    public DBUtils getDBUtils() {
        DBUtils dbUtils = new DBUtils();
        dbUtils.setJdbcTemplate(jdbcTemplate());
        dbUtils.setDataSource(getDataSource());
        return dbUtils;
    }

    //

    /**
     *
     * @param type
     * @param <T>
     * @return
     */
    protected <T> T getMapper(Class<T> type) {
        try {
            return new SqlSessionTemplate(sqlSessionFactory()).getMapper(type);
        }
        catch (Exception e) {
            logger.error("Cannot create SqlSessionFactoryBean");
        }

        return null;
    }

}
