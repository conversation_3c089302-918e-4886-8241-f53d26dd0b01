package com.adoreboard.emotics.common.model.storyteller;

import com.adoreboard.emotics.common.model.revenueAtRisk.ValueAtRiskInfo;
import com.adoreboard.emotics.common.model.storyteller.enums.InsightSettingOptions;
import com.adoreboard.emotics.common.model.storyteller.enums.ReportCategory;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class StorytellerReportSettings {
    private String reportName;
    @Builder.Default
    private ReportCategory reportCategory = ReportCategory.customer_experience;
    private String reportCategoryExtraInfo;
    @Builder.Default
    private InsightSettings insightSettings = new InsightSettings();
    @Builder.Default
    private DisplaySettings displaySettings = new DisplaySettings();
    private Set<InsightSettingOptions> availableInsightSettings;
    private List<InsightThemeSlideData> insightThemesData;
    private ValueAtRiskInfo valueAtRiskInfo;
    private CommentSettings commentSettings = new CommentSettings();


    public String getReportCategoryValue() {
        return ReportCategory.isOtherType(reportCategory)
                ? reportCategoryExtraInfo
                : reportCategory.type;
    }

    public ValueAtRiskInfo getValueAtRiskInfo() {
        if(valueAtRiskInfo == null) {
            valueAtRiskInfo = new ValueAtRiskInfo();
        }
        return valueAtRiskInfo;
    }

    @JsonIgnoreProperties
    public boolean hasVar() {
        // return if dataset has Value at Risk and it is greater than 0
        return this.valueAtRiskInfo != null
                && this.valueAtRiskInfo.getValueAtRiskAmount() != null
                && this.valueAtRiskInfo.getValueAtRiskAmount().compareTo(BigDecimal.ZERO) > 0;
    }

    @JsonIgnoreProperties
    public boolean hasMetric() {
        return this.getInsightSettings().getAvailablePredictImprovements() != null;
    }
}
