package com.adoreboard.emotics.common.service.impl;

import com.adoreboard.emotics.common.mapper.DatasetMapper;
import com.adoreboard.emotics.common.mapper.OrganisationMapper;
import com.adoreboard.emotics.common.mapper.ReportingJobEmailConfigMapper;
import com.adoreboard.emotics.common.mapper.ReportingJobMapper;
import com.adoreboard.emotics.common.mapper.ReportingJobResultMapper;
import com.adoreboard.emotics.common.mapper.UserMapper;
import com.adoreboard.emotics.common.mapper.UserTopicMapper;
import com.adoreboard.emotics.common.mapper.view.UserDocViewMapper;
import com.adoreboard.emotics.common.model.Dataset;
import com.adoreboard.emotics.common.model.Snippet;
import com.adoreboard.emotics.common.model.User;
import com.adoreboard.emotics.common.model.analysis.AnalysisTopic;
import com.adoreboard.emotics.common.model.enums.EightEmotionalIndex;
import com.adoreboard.emotics.common.model.reporting.CommonEmailSettings;
import com.adoreboard.emotics.common.model.reporting.MetricCalculationSetting;
import com.adoreboard.emotics.common.model.reporting.ReportingJob;
import com.adoreboard.emotics.common.model.reporting.ReportingJobEmailConfig;
import com.adoreboard.emotics.common.model.reporting.ReportingJobResult;
import com.adoreboard.emotics.common.service.NotificationService;
import com.adoreboard.emotics.common.service.ReportingEmailTemplateProcessingService;
import com.adoreboard.emotics.common.service.SwotDecoratorService;
import com.adoreboard.emotics.common.service.themeSummary.ThemeSummaryAIPromptService;
import com.adoreboard.emotics.common.util.DateUtils;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ReportingEmailTemplateProcessingServiceImpl implements ReportingEmailTemplateProcessingService {
    private final Logger logger = LogManager.getLogger(ReportingEmailTemplateProcessingServiceImpl.class);

    // Add a qualifier to inject the string template engine specifically
    private final TemplateEngine emailTemplateEngine;
    private final TemplateEngine stringTemplateEngine;

    private final static String adoreboardLogoUrl = "https://emotics-media.s3.us-east-1.amazonaws.com/logo/pngs/logo-adoreboard.png";

    private final DatasetMapper datasetMapper;
    private final NotificationService notificationService;
    private final OrganisationMapper organisationMapper;
    private final ReportingJobEmailConfigMapper emailConfigMapper;
    private final ReportingJobMapper reportingJobMapper;
    private final ReportingJobResultMapper resultMapper;
    private final SwotDecoratorService swotDecoratorService;
    private final ThemeSummaryAIPromptService themeSummaryAIPromptService;
    private final UserDocViewMapper userDocViewMapper;
    private final UserMapper userMapper;
    private final UserTopicMapper topicMapper;
    private final S3TemplateServiceImpl s3TemplateService;

    private static final String REPORTING_EMAIL_TEMPLATE_FOLDER = "email-templates/reporting-email-template";

    @Override
    public String processReportingEmailTemplate(User user, String reportTemplateId, String reportName, ReportingJobResult result, ReportingJobEmailConfig.ContentSettings contentSettings) {
        logger.info("Processing email template for report: {}", reportName);
        final Context context = new Context(Locale.getDefault());
        //for report
        context.setVariable("reportName", reportName);
        context.setVariable("volume", result.getVolume());

        if(result.getStartDate() != null) {
            context.setVariable("startDate", DateUtils.formatOrdinalMonthYear(result.getStartDate()));
        } else {
            context.setVariable("startDate", "All Time");
        }
        context.setVariable("endDate", DateUtils.formatOrdinalMonthYear(result.getEndDate()));
        context.setVariable("logo_adoreboard", adoreboardLogoUrl);
        String logoUser = organisationMapper.selectById(user.getOrganisationId()).orElseThrow().getSettings().getLogoUrl();
        context.setVariable("logo_user", logoUser);

        //for metadata
        context.setVariable("adorescore", result.getAdorescore());
        context.setVariable("adorescoreChange", result.getAdorescoreChange());
        context.setVariable("adorescoreGrading", result.getAdorescoreGrading());
        context.setVariable("adorescoreGradingCss", result.getAdorescoreGradingCss());

        //NPS
        //find NPS metadata
        //todo - it's uncleared if we will have more than 1 NPS metric in the future
        ReportingJobResult.MetricResult npsMetric = result.getMetricResults().stream()
                .filter(metricResult -> metricResult.getCalculationType().equals(MetricCalculationSetting.CalculationType.NPS))
                .findFirst()
                .orElse(null);
        context.setVariable("npsMetric", npsMetric);

        context.setVariable("metricResults", result.getMetricResults().stream()
                .filter(metricResult -> !metricResult.getCalculationType().equals(MetricCalculationSetting.CalculationType.NPS))
                .collect(Collectors.toList())
        );

        List<ReportingJobResult.TopicResult> topicResults = result.getFilteredTopicNames().stream()
                .map(topicName -> {
                    // Try to find an existing topic with this name
                    return result.getTopicResults().stream()
                            .filter(topic -> topic.getName().equals(topicName))
                            .findFirst()
                            // If not found, create a default one
                            .orElseGet(() -> ReportingJobResult.TopicResult.builder()
                                    .name(topicName)
                                    .isTracked(true)
                                    .build());
                })
                .collect(Collectors.toList());

        // Find the 1st topic result that is not tracked and has a topicId
        int firstNonTrackedTopic = topicResults.stream()
                .filter(topicResult -> !topicResult.isTracked() && topicResult.getTopicId() != null)
                .findFirst()
                .map(ReportingJobResult.TopicResult::getTopicId)
                .orElse(0);
        //for topic
        context.setVariable("topicResults", topicResults);
        context.setVariable("firstNonTrackedTopic", firstNonTrackedTopic);

        //Filter out the topics comment, only keep the first 4 comments from the list filtered topics
        List<String> comments = topicResults.stream()
                .map(ReportingJobResult.TopicResult::getRepresentationComment)
                .filter(comment -> comment != null && !comment.isEmpty())
                .limit(contentSettings.getMaxCommentsToInclude())
                .collect(Collectors.toList());

        context.setVariable("comments", comments);
        logger.info("Topic results: {}, firstNonTrackedTopic: {}", topicResults, firstNonTrackedTopic);
        logger.info("End Processing email template for report: {}", reportName);

        // In case there will be more than 1 template in the future, just need to set the templateId to another folder
        context.setVariable("reportPath", REPORTING_EMAIL_TEMPLATE_FOLDER);

        try {
            // Get cached template content (first call fetches, subsequent calls use cache)
            String templateContent = s3TemplateService.getTemplateContent(reportTemplateId);

            if (templateContent != null) {
                logger.info("Processing S3 template for report: {}", reportName);
                return processTemplateContent(templateContent, context);
            } else {
                logger.warn("Template content is null for report: {}", reportName);
                // Fallback to local template
                logger.info("Using local template for report: {}", reportName);
                return emailTemplateEngine.process(String.format("%s/%s", REPORTING_EMAIL_TEMPLATE_FOLDER, reportTemplateId), context);
            }
        } catch (Exception e) {
            logger.error("Error processing S3 template for report: {}", reportName, e);
            logger.warn("Falling back to local template");
            // Fallback to local template
            logger.info("Using local template for report: {}", reportName);
            return emailTemplateEngine.process(String.format("%s/%s", REPORTING_EMAIL_TEMPLATE_FOLDER, reportTemplateId), context);
        }
    }

    @Override
    public String processReportingEmail(User user, String datasetName, CommonEmailSettings commonEmailSettings, ReportingJobEmailConfig.EmailSetting emailSetting, ReportingJobResult reportingJobResult) {
        String formattedDate = DateTimeFormatter.ofPattern("MMM yyyy").format(reportingJobResult.getEndDate());
        String reportingName = datasetName + " - " + formattedDate + " Report";
        return this.processReportingEmailTemplate(user, commonEmailSettings.getTemplateId(), reportingName, reportingJobResult, emailSetting.getContentSettings());
    }

    @Override
    public void sendReportEmail(int reportingId, int executionNumber) {
        this.sendReportEmail(reportingId, executionNumber, true, true, false);
    }

    @Override
    public void sendReportEmail(int reportingId, int executionNumber, boolean buildReportingJobResult, boolean generateSummary, boolean forceSend) {
        logger.info("Sending report email for reportingId: {} and executionNumber: {}", reportingId, executionNumber);
        sendReportEmailInternal(reportingId, executionNumber, buildReportingJobResult, generateSummary, null, forceSend);
        logger.info("End sending report email for reportingId: {} and executionNumber: {}", reportingId, executionNumber);
    }

    @Override
    public void sendReportEmail(int reportingId, int executionNumber, boolean buildReportingJobResult, boolean generateSummary, List<String> recipients) {
        logger.info("Sending report email for reportingId: {} and executionNumber: {} for custom recipients: [{}]", reportingId, executionNumber, recipients);
        sendReportEmailInternal(reportingId, executionNumber, buildReportingJobResult, generateSummary, recipients, true); // Force send if API call
        logger.info("End sending report email for reportingId: {} and executionNumber: {} for custom recipients: [{}]", reportingId, executionNumber, recipients);
    }

    @Override
    public void sendResultEmail(Integer reportingJobId, Long reportingResultId, boolean buildReportingJobResult,  boolean generateSummary, List<String> recipientEmails) {
        this.buildResultAndSendEmailsIfNeeded(reportingResultId, buildReportingJobResult, generateSummary, recipientEmails, true);
    }

    /**
     * Send a report email for a specific reporting job result
     *
     * @param user The user who owns the reporting job
     * @param datasetName The name of the dataset
     * @param commonEmailSettings Common email settings for the reporting job
     * @param emailSetting Specific email settings for the dataset
     * @param reportingJobResult The reporting job result to include in the email
     */
    private void sendReportEmail(User user, String datasetName, CommonEmailSettings commonEmailSettings, ReportingJobEmailConfig.EmailSetting emailSetting, ReportingJobResult reportingJobResult) {
        String formattedDate = DateTimeFormatter.ofPattern("MMM yyyy").format(reportingJobResult.getEndDate());
        String reportingName = datasetName + " - " + formattedDate + " Report";
        String senderEmail = commonEmailSettings.getSenderEmail() != null ? commonEmailSettings.getSenderEmail() : user.getEmail();
        String subject = commonEmailSettings.getSubject() != null ? commonEmailSettings.getSubject() : reportingName;
        String emailContent = this.processReportingEmailTemplate(user, commonEmailSettings.getTemplateId(), reportingName, reportingJobResult, emailSetting.getContentSettings());
        emailSetting.getRecipientEmails().forEach(recipient -> notificationService.sendReportingEmail(user, senderEmail, recipient, subject, emailContent));
    }

    private void sendReportEmailInternal(int reportingId, int executionNumber, boolean buildReportingJobResult, boolean generateSummary,
                                         List<String> overrideRecipients, boolean forceSend) {
        ReportingJob reportingJob = reportingJobMapper.findReportingJobById(reportingId)
                .orElseThrow(() -> new RuntimeException("Reporting job not found"));

        List<ReportingJobEmailConfig> configs = emailConfigMapper.findByReportingJobId(reportingId);
        if(configs.isEmpty()) {
            logger.warn("No email configurations found for reporting job ID: {}", reportingId);
            return;
        }
        ReportingJobEmailConfig config = configs.get(0);

        CommonEmailSettings commonEmailSettings = config.getCommonEmailSettings();

        List<ReportingJobEmailConfig.EmailSetting> globalConfigs = config.getEmailSettings().stream()
                .filter(e -> e.getDatasetNames() == null || e.getDatasetNames().isEmpty())
                .collect(Collectors.toList());

        List<ReportingJobEmailConfig.EmailSetting> customConfigs = config.getEmailSettings().stream()
                .filter(e -> e.getDatasetNames() != null && !e.getDatasetNames().isEmpty())
                .collect(Collectors.toList());

        List<ReportingJobResult> results = resultMapper.findCurrentResults(reportingId, executionNumber);
        User user = userMapper.selectUserById(reportingJob.getUserId());

        for (ReportingJobResult result : results) {
            this.sendEmailsForResult(user, commonEmailSettings, customConfigs, globalConfigs, result, buildReportingJobResult, generateSummary, overrideRecipients, forceSend);
        }
    }

    private void buildResultsAndSendEmailsIfNeeded (User user, String datasetName,
                                                    CommonEmailSettings commonEmailSettings,
                                                    List<ReportingJobEmailConfig.EmailSetting> emailSettings,
                                                    ReportingJobResult result,
                                                    boolean buildReportingJobResult,
                                                    boolean generateSummary,
                                                    List<String> overrideRecipients,
                                                    boolean forceSend) {
        for (ReportingJobEmailConfig.EmailSetting emailConfig : emailSettings) {
            Dataset dataset = datasetMapper.selectById(result.getDatasetId());
            boolean shouldBuildReportingJobResult = buildReportingJobResult || result.getFilteredTopicNames() == null || result.getFilteredTopicNames().isEmpty();

            if (shouldBuildReportingJobResult) {
                buildReportingJobResult(dataset, emailConfig.getContentSettings(), result, generateSummary);
            }

            if (overrideRecipients != null && !overrideRecipients.isEmpty()) {
                emailConfig.setRecipientEmails(overrideRecipients);
            }
            boolean shouldSendEmail = forceSend || commonEmailSettings.isAutoSend();

            if (shouldSendEmail) {
                logger.info("Sending report email for dataset {} with config {}", datasetName, emailConfig);
                // Update the result with the latest data
                result = resultMapper.findById(result.getId()).orElseThrow(() -> new RuntimeException("Reporting job result not found"));
                this.sendReportEmail(user, datasetName, commonEmailSettings, emailConfig, result);
            } else {
                logger.info("Report email for result id {} is not auto-send and not forced. Skipping.", result.getId());
            }
        }
    }

    /**
     * Send a report email for a specific reporting job result
     *
     * @param reportingJobResultId The ID of the specific result to send
     * @param generateSummary Whether to generate a summary
     * @param overrideRecipients Optional list of recipients that override the configured ones
     * @param forceSend Whether to force sending even if conditions would normally prevent it
     */
    private void buildResultAndSendEmailsIfNeeded(Long reportingJobResultId,
                                                  boolean buildReportingJobResult,
                                                  boolean generateSummary,
                                                  List<String> overrideRecipients,
                                                  boolean forceSend) {
        ReportingJobResult result = resultMapper.findById(reportingJobResultId)
                .orElseThrow(() -> new RuntimeException("Reporting job result not found"));

        int reportingId = result.getReportingJobId();

        ReportingJob reportingJob = reportingJobMapper.findReportingJobById(reportingId)
                .orElseThrow(() -> new RuntimeException("Reporting job not found"));

        List<ReportingJobEmailConfig> configs = emailConfigMapper.findByReportingJobId(reportingId);
        if (configs.isEmpty()) {
            logger.warn("No email configurations found for reporting job ID: {}", reportingId);
            return;
        }

        ReportingJobEmailConfig config = configs.get(0);

        List<ReportingJobEmailConfig.EmailSetting> globalConfigs = config.getEmailSettings().stream()
                .filter(e -> e.getDatasetNames() == null || e.getDatasetNames().isEmpty())
                .collect(Collectors.toList());

        List<ReportingJobEmailConfig.EmailSetting> customConfigs = config.getEmailSettings().stream()
                .filter(e -> e.getDatasetNames() != null && !e.getDatasetNames().isEmpty())
                .collect(Collectors.toList());

        User user = userMapper.selectUserById(reportingJob.getUserId());
        this.sendEmailsForResult(user, config.getCommonEmailSettings(), customConfigs, globalConfigs, result, buildReportingJobResult, generateSummary, overrideRecipients, forceSend);
    }

    /**
     * This method sends emails for a specific reporting job result.
     * It first filters the custom email settings based on the dataset name.
     * If no custom settings are found, it uses the global settings.
     *
     * @param user The user to whom the report is sent.
     * @param commonEmailSettings Common email settings for the reporting job.
     * @param customConfigs Custom email settings for the dataset.
     * @param globalConfigs Global email settings for the reporting job.
     * @param result The reporting job result to include in the email.
     * @param buildReportingJobResult Whether to build the reporting job result.
     * @param generateSummary Whether to generate a summary for the topics.
     * @param overrideRecipients Optional list of recipients that override the configured ones.
     * @param forceSend Whether to force sending even if conditions would normally prevent it.
     */
    private void sendEmailsForResult(User user,
                                     CommonEmailSettings commonEmailSettings,
                                     List<ReportingJobEmailConfig.EmailSetting> customConfigs,
                                     List<ReportingJobEmailConfig.EmailSetting> globalConfigs,
                                     ReportingJobResult result,
                                     boolean buildReportingJobResult,
                                     boolean generateSummary,
                                     List<String> overrideRecipients,
                                     boolean forceSend) {

        String datasetName = result.getDatasetOriginalName();
        customConfigs = customConfigs.stream()
                .filter(e -> e.getDatasetNames().contains(datasetName))
                .collect(Collectors.toList());

        if (!customConfigs.isEmpty()) {
            buildResultsAndSendEmailsIfNeeded(user, datasetName, commonEmailSettings, customConfigs, result, buildReportingJobResult, generateSummary, overrideRecipients, forceSend);
        } else {
            buildResultsAndSendEmailsIfNeeded(user, datasetName, commonEmailSettings, globalConfigs, result, buildReportingJobResult, generateSummary, overrideRecipients, forceSend);
        }
    }

    /**
     * This method builds a ReportingJobResult object by filtering the topic results based on the content settings
     * provided in the EmailMetadata object. It prioritizes tracked themes, then custom themes, and finally general themes.
     * <pre>
     * Input:
     * - MaxThemes: 5
     * - Tracked: ["Service", "Quality"]
     * - Custom: ["Price", "Support"]
     * - General: ["Location", "Staff", "Hours"]
     *
     * Output Priority:
     * 1. "Service" (tracked)
     * 2. "Quality" (tracked)
     * 3. "Price" (custom)
     * 4. "Support" (custom)
     * 5. "Location" (general)
     * [Staff, Hours excluded due to max limit]
     * </pre>
     * @param contentSettings      The EmailMetadata object containing content settings.
     * @param reportingJobResult The ReportingJobResult object to be filtered.
     */
    private void buildReportingJobResult(Dataset dataset, ReportingJobEmailConfig.ContentSettings contentSettings, ReportingJobResult reportingJobResult, boolean generateSummary) {
        // The highest priority is contentSettings.trackedThemes, then topCustomThemes, then topGeneralThemes , maximum list is maxThemesToInclude
        // If topicResults doesn't contain trackedThemes, return new Topic result object.
        logger.info("Building reporting job result for dataset: {} with content settings: {}", dataset.getId(), contentSettings);
        List<ReportingJobResult.TopicResult> filteredTopicResults = contentSettings.getTrackedThemes().stream().map(
                trackedTheme -> reportingJobResult.getTopicResults().stream().filter(
                                topicResult -> trackedTheme.equals(topicResult.getName())).findFirst()
                                .orElseGet(() -> ReportingJobResult.TopicResult.builder()
                                .name(trackedTheme)
                                .build())
        ).collect(Collectors.toList());
        filteredTopicResults.forEach(topicResult -> {
            topicResult.setTracked(true);
            if (topicResult.getTopicId() != null) {
                 resultMapper.updateTrackedTheme(reportingJobResult.getId(), topicResult.getTopicId(), true);
            }
        });
        logger.info("Result {} - Filtered tracked topics: {}", reportingJobResult.getId(), filteredTopicResults.stream().map(ReportingJobResult.TopicResult::getName).collect(Collectors.joining(", ")));
        // Filter out all the filtered topics from the reportingJobResult
        reportingJobResult.getTopicResults().removeAll(filteredTopicResults);

        if(filteredTopicResults.size() < contentSettings.getMaxThemesToInclude()) {
            // If there are not enough tracked topics, add the topCustomThemes
            List<ReportingJobResult.TopicResult> filteredCustomTopicResults = this.getTopThemes(
                    reportingJobResult.getTopicResults().stream().filter(ReportingJobResult.TopicResult::isCustom).collect(Collectors.toList())
                    , contentSettings.getTopCustomThemes().getSortBy()
                    , contentSettings.getMaxThemesToInclude() - filteredTopicResults.size()
            );
            filteredTopicResults.addAll(filteredCustomTopicResults);
        }
        logger.info("Result {} - Added Filtered custom topics: {}", reportingJobResult.getId(), filteredTopicResults.stream().map(ReportingJobResult.TopicResult::getName).collect(Collectors.joining(", ")));

        if(filteredTopicResults.size() < contentSettings.getMaxThemesToInclude()) {
            // If there are not enough tracked topics, add the topGeneralThemes
            List<ReportingJobResult.TopicResult> filteredGeneralTopicResults = this.getTopThemes(
                    reportingJobResult.getTopicResults().stream().filter(topicResult -> !topicResult.isCustom()).collect(Collectors.toList())
                    , contentSettings.getTopAutoGeneratedThemes().getSortBy()
                    , contentSettings.getMaxThemesToInclude() - filteredTopicResults.size()
            );
            filteredTopicResults.addAll(filteredGeneralTopicResults);
        }
        logger.info("Result {} - Added Filtered general topics: {}", reportingJobResult.getId(), filteredTopicResults.stream().map(ReportingJobResult.TopicResult::getName).collect(Collectors.joining(", ")));
        // Add the filtered topic results to the result
        reportingJobResult.setFilteredTopicNames(filteredTopicResults.stream()
                .map(ReportingJobResult.TopicResult::getName)
                .collect(Collectors.toList()));

        List<String> allTopicNames = filteredTopicResults.stream()
                .map(ReportingJobResult.TopicResult::getName)
                .collect(Collectors.toList());

        reportingJobResult.getTopicResults().forEach(topicResult -> {
            // Set the summary for each topic;
            if(topicResult.getTopicId() != null && reportingJobResult.getFilteredTopicNames().contains(topicResult.getName()) && generateSummary) {
                AnalysisTopic analysisTopic = topicMapper.selectById(dataset.getId(), topicResult.getTopicId());
                topicResult.setSummary(this.generateTopicSummary(dataset, analysisTopic, allTopicNames.stream().filter(topicName -> !topicName.equals(topicResult.getName())).collect(Collectors.toList())));
                // save topic-result-summary into db for reporting-job-result
                resultMapper.updateTopicResultSummary(reportingJobResult.getId(), topicResult.getTopicId(), topicResult.getSummary());
            }
        });
        resultMapper.updateFilteredTopicResults(reportingJobResult.getId(), reportingJobResult.getFilteredTopicNames());
    }


    private List<ReportingJobResult.TopicResult> getTopThemes(List<ReportingJobResult.TopicResult> themes, String sortBy, Integer maxThemesToInclude) {
        // If sort by volume, sort by ReportingJobResult.TopicResult.volume
        // If sort by adorescore, sort by ReportingJobResult.TopicResult.adorescore
        // Sort by adorescore (descending)
        // Default to volume (descending)
        return themes.stream()
                .sorted((t1, t2) ->
                        {
                            if (sortBy.equals("adorescore")) {
                                return Integer.compare(t2.getAdorescore(), t1.getAdorescore()); // Sort by adorescore (descending)
                            }
                            return Integer.compare(t2.getVolume(), t1.getVolume()); // Default to volume (descending)
                        })
                .limit(maxThemesToInclude)
                .collect(Collectors.toList());
    }

    private String generateTopicSummary(Dataset dataset, AnalysisTopic topic, List<String> otherTopicNames) {
        if(topic.getSwot() == null) {
            swotDecoratorService.decorateSwot(topic, dataset.getDocumentCount());
            topicMapper.updateTopicSummary(topic);
        }
        return themeSummaryAIPromptService.generateReportingThemeSummaryResponse(dataset, topic, getCommentIds(dataset.getId(), topic.getId(),null,5), otherTopicNames).getSummary();
    }

    public List<Integer> getCommentIds(int datasetId, Integer topicId, EightEmotionalIndex index, int size) {
        Boolean positive = null;
        Integer indexPos = null;

        if (index != null) {
            positive = EightEmotionalIndex.isGreen(index);
            indexPos = index.ordinal();
        }

        return userDocViewMapper.querySnippets(datasetId, topicId, indexPos, positive, size, 0)
                .stream()
                .map(Snippet::getUserDocId)
                .collect(Collectors.toList());
    }

    /**
     * Process template content as string using Thymeleaf
     */
    private String processTemplateContent(String templateContent, Context context) {
        try {
            // DEBUG: Check which template engine is being used
            logger.info("Processing template content with string template engine, content length: {}", templateContent.length());
            return stringTemplateEngine.process(templateContent, context);
        } catch (Exception e) {
            logger.error("Error processing template content: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to process template content", e);
        }
    }

}
