/*
 * Copyright 2022 Adoreboard Ltd. All rights reserved.
 * ADOREBOARD PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.adoreboard.emotics.common.mybatis;

import com.adoreboard.emotics.common.model.revenueAtRisk.RevenueAtRiskTemplateInfo;

/**
 * MyBatis type handler for converting JSONB to ValueAtRiskInfo
 *
 * <AUTHOR>
 */
public class MyBatisMapJsonbToRevenueAtRiskTemplateInfo extends AbstractMyBatisJsonToObject {

    public MyBatisMapJsonbToRevenueAtRiskTemplateInfo() {
        super();
        setType(RevenueAtRiskTemplateInfo.class);
    }
}
