package com.adoreboard.emotics.common.mapper;

import com.adoreboard.emotics.common.model.revenueAtRisk.RevenueAtRiskTemplate;
import com.adoreboard.emotics.common.model.revenueAtRisk.RevenueAtRiskTemplateStatus;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface RevenueAtRiskTemplateMapper {

    // Workspace template operations
    default List<RevenueAtRiskTemplate> getWorkspaceTemplates(@Param("workspaceId") Integer workspaceId) {
        return getWorkspaceTemplatesByStatus(workspaceId, RevenueAtRiskTemplateStatus.ACTIVE);
    }

    List<RevenueAtRiskTemplate> getWorkspaceTemplatesByStatus(@Param("workspaceId") Integer workspaceId, @Param("status") RevenueAtRiskTemplateStatus status);

    default RevenueAtRiskTemplate getTemplateById(@Param("templateId") Integer templateId) {
        return getTemplateByIdAndStatus(templateId, RevenueAtRiskTemplateStatus.ACTIVE);
    }

    RevenueAtRiskTemplate getTemplateByIdAndStatus(@Param("templateId") Integer templateId, @Param("status") RevenueAtRiskTemplateStatus status);

    void createWorkspaceTemplate(RevenueAtRiskTemplate template);

    void updateTemplate(RevenueAtRiskTemplate template);

    void deleteTemplate(@Param("templateId") Integer templateId);

    void softDeleteTemplate(@Param("templateId") Integer templateId);

    // Default template operations
    default RevenueAtRiskTemplate getDefaultTemplate(@Param("workspaceId") Integer workspaceId) {
        return getDefaultTemplateByStatus(workspaceId, RevenueAtRiskTemplateStatus.ACTIVE);
    }

    RevenueAtRiskTemplate getDefaultTemplateByStatus(@Param("workspaceId") Integer workspaceId, @Param("activeStatus") RevenueAtRiskTemplateStatus activeStatus);

    void unsetDefaultTemplate(@Param("workspaceId") Integer workspaceId);

    void setDefaultTemplate(@Param("templateId") Integer templateId);

    // Validation
    default boolean existsTemplateByName(@Param("workspaceId") Integer workspaceId, @Param("name") String name) {
        return existsTemplateByNameAndStatus(workspaceId, name, RevenueAtRiskTemplateStatus.ACTIVE);
    }

    boolean existsTemplateByNameAndStatus(@Param("workspaceId") Integer workspaceId, @Param("name") String name, @Param("activeStatus") RevenueAtRiskTemplateStatus activeStatus);
}
