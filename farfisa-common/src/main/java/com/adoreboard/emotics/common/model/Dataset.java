/*
 * Copyright 2022 Adoreboard Ltd. All rights reserved.
 * ADOREBOARD PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */

package com.adoreboard.emotics.common.model;

import com.adoreboard.emotics.common.enums.DatasetFeature;
import com.adoreboard.emotics.common.enums.DatasetPermissionType;
import com.adoreboard.emotics.common.enums.DatasetStatus;
import com.adoreboard.emotics.common.model.analysis.AnalysisSummary;
import com.adoreboard.emotics.common.model.reporting.MetricCalculationSetting;
import com.adoreboard.emotics.common.model.revenueAtRisk.RevenueAtRiskWithTemplate;
import org.joda.time.DateTime;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Dataset object.
 *
 * <AUTHOR> <PERSON>
 * <AUTHOR> Huy Vu
 */
public class Dataset {

    // Fields

    private int id;
    private int userId;
    private Integer parentId;
    private Integer workspaceId;
    protected String label;
    private int documentCount;
    private long contentCharacters;
    private AnalysisSummary summary;
    private DatasetStatus status;
    private DateTime uploadStart;
    private String downloadLocation;
    private Map<DatasetStatus, DatasetProgress> statusProgresses;
    private List<StopWord> stopWords;
    private DatasetError error;
    private int executionId;
    private boolean pendingChanges;
    private boolean archived;
    private String originalDownloadLocation;
    private List<DatasetFeature> features;
    private List<String> metadataHeaders;
    private List<Integer> metadataColumns;
    private List<String> metadataTypes;
    private Map<Integer, Map<String, Integer>> ratingScaleMaps;
    private Integer customThemeList;
    private boolean excludeAutoTopics;
    private List<Integer> savedActionListIds;
    private List<Integer> tagIds;
    private RevenueAtRiskWithTemplate valueAtRiskInfo;
    private Integer selectedRarTemplateId;
    private String datasetDomain;
    // Dataset Permission
    private DatasetPermissionType permissionType = DatasetPermissionType.PRIVATE;
    private Set<Integer> editorIds;
    private Set<Integer> viewerIds;
    private Integer groupId;
    private Boolean editable = false;
    private int reportCount;
    // Dataset metrics
    private List<MetricCalculationSetting> metricCalculationSettings;

    // Constructors

    public Dataset() {
    }

    public Dataset(int id) {
        this.id = id;
    }

    public Dataset(int userId, int documentCount, int contentCharacters, String label) {
        this.userId = userId;
        this.documentCount = documentCount;
        this.contentCharacters = contentCharacters;
        this.label = label;
    }

    // Support methods

    /**
     * @return
     */
    public DatasetProgress getLastStatus() {
        if (statusProgresses == null || statusProgresses.size() == 0 || !statusProgresses.containsKey(status)) {
            return null;
        }
        return statusProgresses.get(status);
    }

    /**
     * @return
     */
    public DatasetProgress getFirstStatus() {
        if (statusProgresses == null || statusProgresses.size() == 0) {
            return null;
        }
        for (DatasetStatus status : DatasetStatus.values()) {
            if (statusProgresses.containsKey(status)) {
                return statusProgresses.get(status);
            }
        }
        return null;
    }

    public boolean hasProgressStatus(String status) {
        if (statusProgresses == null) {
            return false;
        }

        return statusProgresses.containsKey(DatasetStatus.valueOf(status));

    }

    // Getters & Setters

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public Integer getWorkspaceId() {
        return workspaceId;
    }

    public void setWorkspaceId(Integer workspaceId) {
        this.workspaceId = workspaceId;
    }

    public int getDocumentCount() {
        return documentCount;
    }

    public void setDocumentCount(int documentCount) {
        this.documentCount = documentCount;
    }

    public long getContentCharacters() {
        return contentCharacters;
    }

    public void setContentCharacters(long contentCharacters) {
        this.contentCharacters = contentCharacters;
    }

    public AnalysisSummary getSummary() {
        return summary;
    }

    public void setSummary(AnalysisSummary summary) {
        this.summary = summary;
    }

    public boolean getPendingChanges() {
        return pendingChanges;
    }

    public void setPendingChanges(boolean pendingChanges) {
        this.pendingChanges = pendingChanges;
    }

    public DatasetStatus getStatus() {
        return status;
    }

    public void setStatus(DatasetStatus status) {
        this.status = status;
    }

    public Map<DatasetStatus, DatasetProgress> getStatusProgresses() {
        return statusProgresses;
    }

    public DateTime getUploadStart() {
        return uploadStart;
    }

    public void setUploadStart(DateTime uploadStart) {
        this.uploadStart = uploadStart;
    }

    public int getExecutionId() {
        return executionId;
    }

    public void setExecutionId(int executionId) {
        this.executionId = executionId;
    }

    public List<StopWord> getStopWords() {
        return stopWords;
    }

    public void setStopWords(List<StopWord> stopWords) {
        this.stopWords = stopWords;
    }

    public void setStatusProgresses(Map<DatasetStatus, DatasetProgress> statusProgresses) {
        this.statusProgresses = statusProgresses;
    }

    public DatasetProgress getProgressStatus(String status) {
        return statusProgresses.get(DatasetStatus.valueOf(status));
    }

    public String getDownloadLocation() {
        return downloadLocation;
    }

    public void setDownloadLocation(String downloadLocation) {
        this.downloadLocation = downloadLocation;
    }

    public DatasetError getError() {
        return error;
    }

    public void setError(DatasetError error) {
        this.error = error;
    }

    public boolean isArchived() {
        return archived;
    }

    public void setArchived(boolean archived) {
        this.archived = archived;
    }

    public String getOriginalDownloadLocation() {
        return originalDownloadLocation;
    }

    public void setOriginalDownloadLocation(String originalDownloadLocation) {
        this.originalDownloadLocation = originalDownloadLocation;
    }

    public List<DatasetFeature> getFeatures() {
        return features;
    }

    public void setFeatures(List<DatasetFeature> features) {
        this.features = features;
    }

    public List<String> getMetadataHeaders() {
        return metadataHeaders;
    }

    public void setMetadataHeaders(List<String> metadataHeaders) {
        this.metadataHeaders = metadataHeaders;
    }

    public List<Integer> getMetadataColumns() {
        return metadataColumns;
    }

    public void setMetadataColumns(List<Integer> metadataColumns) {
        this.metadataColumns = metadataColumns;
    }

    public List<String> getMetadataTypes() {
        return metadataTypes;
    }

    public void setMetadataTypes(List<String> metadataTypes) {
        this.metadataTypes = metadataTypes;
    }

    public Integer getCustomThemeList() {
        return customThemeList;
    }

    public void setCustomThemeList(Integer customThemeList) {
        this.customThemeList = customThemeList;
    }

    public boolean isExcludeAutoTopics() {
        return excludeAutoTopics;
    }

    public void setExcludeAutoTopics(boolean excludeAutoTopics) {
        this.excludeAutoTopics = excludeAutoTopics;
    }

    public List<Integer> getTagIds() {
        return tagIds;
    }

    public void setTagIds(List<Integer> tagIds) {
        this.tagIds = tagIds;
    }

    public List<Integer> getSavedActionListIds() { return savedActionListIds; }

    public void setSavedActionListIds(List<Integer> savedActionListIds) {
        this.savedActionListIds = savedActionListIds;
    }

    public DatasetPermissionType getPermissionType() {
        return permissionType;
    }

    public void setPermissionType(DatasetPermissionType permissionType) {
        this.permissionType = permissionType;
    }

    public Set<Integer> getEditorIds() {
        return editorIds;
    }

    public void setEditorIds(Set<Integer> editorIds) {
        this.editorIds = editorIds;
    }

    public Set<Integer> getViewerIds() {
        return viewerIds;
    }

    public void setViewerIds(Set<Integer> viewerIds) {
        this.viewerIds = viewerIds;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public Boolean getEditable() {
        return editable;
    }

    public void setEditable(Boolean editable) {
        this.editable = editable;
    }

    public int getReportCount() {
        return reportCount;
    }

    public void setReportCount(int reportCount) {
        this.reportCount = reportCount;
    }

    public RevenueAtRiskWithTemplate getValueAtRiskInfo() {
        return valueAtRiskInfo;
    }

    public void setValueAtRiskInfo(RevenueAtRiskWithTemplate valueAtRiskInfo) {
        this.valueAtRiskInfo = valueAtRiskInfo;
    }

    public Map<Integer, Map<String, Integer>> getRatingScaleMaps() {
        return ratingScaleMaps;
    }

    public void setRatingScaleMaps(Map<Integer, Map<String, Integer>> ratingScaleMaps) {
        this.ratingScaleMaps = ratingScaleMaps;
    }

    public String getDatasetDomain() {
        return datasetDomain;
    }

    public void setDatasetDomain(String datasetDomain) {
        this.datasetDomain = datasetDomain;
    }

    public List<MetricCalculationSetting> getMetricCalculationSettings() {
        return metricCalculationSettings;
    }

    public void setMetricCalculationSettings(List<MetricCalculationSetting> metricCalculationSettings) {
        this.metricCalculationSettings = metricCalculationSettings;
    }

    public Integer getSelectedRarTemplateId() {
        return selectedRarTemplateId;
    }

    public void setSelectedRarTemplateId(Integer selectedRarTemplateId) {
        this.selectedRarTemplateId = selectedRarTemplateId;
    }
}