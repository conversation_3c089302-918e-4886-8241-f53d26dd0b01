-- -----------------------------------------------------
-- Table `DATASET_REVENUE_AT_RISK`
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS dataset_revenue_at_risk
(
    id                      SERIAL PRIMARY KEY,
    dataset_id             INTEGER NOT NULL REFERENCES bulk_upload(id),
    template_id            INTEGER REFERENCES revenue_at_risk_templates(id),
    revenue_at_risk        JSONB,
    created_at             TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at             TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_dataset_template UNIQUE (dataset_id, template_id)
);


CREATE INDEX IF NOT EXISTS idx_dataset_revenue_at_risk_dataset_id ON dataset_revenue_at_risk(dataset_id);
CREATE INDEX IF NOT EXISTS idx_dataset_revenue_at_risk_template_id ON dataset_revenue_at_risk(template_id);