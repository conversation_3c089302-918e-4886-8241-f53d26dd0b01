
-- -----------------------------------------------------
-- Table `REVENUE_AT_RISK_TEMPLATES`
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS revenue_at_risk_templates
(
    id                      SERIAL PRIMARY KEY,
    name                    VARCHAR(255) NOT NULL,
    workspace_id           INTEGER NOT NULL REFERENCES workspace(id),
    creator                INTEGER NOT NULL REFERENCES users(id),
    revenue_at_risk_info   JSONB,
    status                 VARCHAR(20) NOT NULL DEFAULT 'active',
    default_template       BOOLEAN NOT NULL DEFAULT FALSE,
    created_at             TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at             TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_workspace_template_name UNIQUE (workspace_id, name, status)
);
-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_revenue_at_risk_templates_workspace_id ON revenue_at_risk_templates(workspace_id);
CREATE INDEX IF NOT EXISTS idx_revenue_at_risk_templates_status ON revenue_at_risk_templates(status);