-- Migration script for Revenue At Risk Template System
-- This script migrates existing datasets to use the new template system

-- Step 1: Create dataset_revenue_at_risk entries for existing datasets with valueAtRiskInfo
INSERT INTO dataset_revenue_at_risk (dataset_id, template_id, revenue_at_risk, created_at, updated_at)
SELECT
    id as dataset_id,
    NULL as template_id,  -- No linked workspace template for existing data
    value_at_risk_info as revenue_at_risk,
    CURRENT_TIMESTAMP as created_at,
    CURRENT_TIMESTAMP as updated_at
FROM bulk_upload
WHERE value_at_risk_info IS NOT NULL
AND value_at_risk_info != '{}'::jsonb
AND value_at_risk_info != 'null'::jsonb;

-- Step 2: Update selected_rar_id in bulk_upload to reference the newly created dataset_revenue_at_risk entries
UPDATE bulk_upload
SET selected_rar_id = dar.id
FROM dataset_revenue_at_risk dar
WHERE bulk_upload.id = dar.dataset_id
AND bulk_upload.value_at_risk_info IS NOT NULL
AND bulk_upload.value_at_risk_info != '{}'::jsonb
AND bulk_upload.value_at_risk_info != 'null'::jsonb;

-- Step 3: Verify the migration
-- This query should show datasets with their selected template IDs
SELECT
    bu.id as dataset_id,
    bu.label as dataset_name,
    bu.selected_rar_id,
    dar.id as template_id,
    CASE
        WHEN dar.revenue_at_risk IS NOT NULL THEN 'Has RAR Data'
        ELSE 'No RAR Data'
    END as rar_status
FROM bulk_upload bu
LEFT JOIN dataset_revenue_at_risk dar ON bu.selected_rar_id = dar.id
WHERE bu.value_at_risk_info IS NOT NULL
AND bu.value_at_risk_info != '{}'::jsonb
ORDER BY bu.id;

-- Optional: Clean up old value_at_risk_info column after migration is verified
-- Uncomment the following lines after verifying the migration was successful
-- UPDATE bulk_upload
-- SET value_at_risk_info = NULL
-- WHERE selected_rar_id IS NOT NULL;
