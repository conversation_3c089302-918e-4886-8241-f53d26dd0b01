-- Add created_by column to dataset_revenue_at_risk table
-- This column will track who created each dataset revenue at risk entry

-- First add the column as nullable
ALTER TABLE dataset_revenue_at_risk
ADD COLUMN created_by INTEGER REFERENCES users(id);

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_dataset_revenue_at_risk_created_by ON dataset_revenue_at_risk(created_by);

-- Update existing records to set created_by from the template creator where possible
-- For records that have a template_id, use the template's creator
UPDATE dataset_revenue_at_risk
SET created_by = rt.creator
FROM revenue_at_risk_templates rt
WHERE dataset_revenue_at_risk.template_id = rt.id
AND dataset_revenue_at_risk.created_by IS NULL;

-- For records without a template_id (legacy data), set created_by to the dataset owner
-- Get the user_id from the bulk_upload table
UPDATE dataset_revenue_at_risk
SET created_by = bu.user_id
FROM bulk_upload bu
WHERE dataset_revenue_at_risk.dataset_id = bu.id
AND dataset_revenue_at_risk.created_by IS NULL;

-- Now make the column NOT NULL since all records should have a created_by value
ALTER TABLE dataset_revenue_at_risk
ALTER COLUMN created_by SET NOT NULL;
