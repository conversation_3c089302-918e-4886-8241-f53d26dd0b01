<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~  Copyright 2022 Adoreboard Ltd. All rights reserved.
  ~  ADOREBOARD PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
  -->

<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!--
MyBatis Dataset mapper.

@formatter:off
-->
<mapper namespace="com.adoreboard.emotics.common.mapper.DatasetMapper">

    <!--
    ==============================================================================
        CRUD section
    ==============================================================================
    -->

    <!-- Insert a new Dataset -->

    <insert id="insert" parameterType="com.adoreboard.emotics.common.model.Dataset"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO bulk_upload (
            user_id,
            parent_id,
            workspace_id,
            label,
            content_characters,
            document_count,
            status,
            pending_changes,
            summary,
            features,
            metadata_headers,
            metadata_columns,
            metadata_types,
            custom_topic_list,
            exclude_auto_topics,
            saved_action_list_ids,
            rating_scale_maps,
            metric_calculation_settings,
            dataset_domain
        )
        VALUES (
            #{userId},
            #{parentId},
            #{workspaceId},
            #{label},
            #{contentCharacters},
            #{documentCount},
            #{status},
            #{pendingChanges},
            #{summary, typeHandler=com.adoreboard.emotics.common.mybatis.MyBatisMapJsonbToPreAnalysisSummary},
            #{features, typeHandler=com.adoreboard.emotics.common.mybatis.ListDatasetFeatureEnumTypeHandler},
            #{metadataHeaders, typeHandler=com.adoreboard.emotics.common.mybatis.StringListTypeHandler},
            #{metadataColumns, typeHandler=com.adoreboard.emotics.common.mybatis.IntegerListTypeHandler},
            #{metadataTypes, typeHandler=com.adoreboard.emotics.common.mybatis.StringListTypeHandler},
            #{customThemeList},
            #{excludeAutoTopics},
            #{savedActionListIds, typeHandler=com.adoreboard.emotics.common.mybatis.IntegerListTypeHandler},
            #{ratingScaleMaps, typeHandler=com.adoreboard.emotics.common.mybatis.MyBatisMapJsonbToIntegerObjectMap},
            #{metricCalculationSettings, typeHandler=com.adoreboard.emotics.common.mybatis.MyBatisMapJsonbToMetricCalculationSettings},
            #{datasetDomain}
        )
    </insert>

    <!-- Update a Dataset object -->

    <update id="update" parameterType="com.adoreboard.emotics.common.model.Dataset">
        UPDATE bulk_upload
        SET label                 = #{label},
            document_count        = #{documentCount},
            content_characters    = #{contentCharacters},
            status                = #{status},
            summary               = #{summary, typeHandler=com.adoreboard.emotics.common.mybatis.MyBatisMapJsonbToPreAnalysisSummary},
            pending_changes       = #{pendingChanges},
            exclude_auto_topics   = #{excludeAutoTopics},
            saved_action_list_ids = #{savedActionListIds, typeHandler=com.adoreboard.emotics.common.mybatis.IntegerListTypeHandler},
            rating_scale_maps     = #{ratingScaleMaps, typeHandler=com.adoreboard.emotics.common.mybatis.MyBatisMapJsonbToIntegerObjectMap},
            metric_calculation_settings = #{metricCalculationSettings, typeHandler=com.adoreboard.emotics.common.mybatis.MyBatisMapJsonbToMetricCalculationSettings},
            dataset_domain        = #{datasetDomain},
            workspace_id          = #{workspaceId}
        WHERE id = #{id}
    </update>

    <insert id="updatePendingChanges" parameterType="map">
        UPDATE bulk_upload
        SET pending_changes = #{hasPendingChanges}
        WHERE id = #{datasetId}
    </insert>

    <insert id="updateArchived" parameterType="map">
        UPDATE bulk_upload
        SET archived = #{isArchived}
        WHERE id IN <foreach item="item" index="i" collection="datasetIds" open="(" separator="," close=")">#{item}</foreach>
    </insert>

    <update id="updateMetadataHeader" parameterType="map">
        UPDATE bulk_upload
        SET metadata_headers[#{metadataHeaderIndex} + 1] = #{metadataHeaderLabel}
        WHERE id = #{datasetId}
    </update>

    <update id="updateMetadata" parameterType="com.adoreboard.emotics.common.model.Dataset">
        UPDATE bulk_upload
        SET  metadata_headers = #{metadataHeaders, typeHandler=com.adoreboard.emotics.common.mybatis.StringListTypeHandler},
             metadata_columns = #{metadataColumns, typeHandler=com.adoreboard.emotics.common.mybatis.IntegerListTypeHandler},
             metadata_types   = #{metadataTypes, typeHandler=com.adoreboard.emotics.common.mybatis.StringListTypeHandler}
        WHERE id = #{id}
    </update>

    <update id="rename" parameterType="map">
        UPDATE bulk_upload
           SET label = #{label}
         WHERE id = #{datasetId}
    </update>

    <update id="incrementExecutionId" parameterType="int">
        update bulk_upload
           set execution_id = execution_id + 1
         where id = #{datasetId}
    </update>

    <update id="updateStatus" parameterType="map">
        update bulk_upload
          set status = #{status}
        where id = #{datasetId}
        <if test="ignoreList != null and ignoreList.size &gt; 0">
            AND
              (
                  status IS NULL
                  OR status NOT IN
                     <foreach item="iStatus" index="i" collection="ignoreList" open="(" separator="," close=")">#{iStatus}</foreach>
              )
        </if>
    </update>

    <update id="updateError" parameterType="map">
        UPDATE bulk_upload
          SET error = #{datasetError, typeHandler=com.adoreboard.emotics.common.mybatis.MyBatisMapJsonbToDatasetError}
        WHERE id = #{datasetId}
    </update>

    <update id="updateDocCount" parameterType="int">
        UPDATE bulk_upload
          SET document_count =
            (
                SELECT COUNT(1)
                FROM
                <if test="isSummaryCount == true">user_doc</if>
                <if test="isSummaryCount == false">bulk_upload_contents</if>
                WHERE bulk_upload_id = #{datasetId}
            )
        WHERE id = #{datasetId}
    </update>

    <update id="updateCharCount" parameterType="map">
        UPDATE bulk_upload
        SET content_characters = #{charCount}
        WHERE id = #{datasetId}
    </update>

    <update id="updateSummary" parameterType="com.adoreboard.emotics.common.model.analysis.AnalysisSummary">
      UPDATE bulk_upload
        SET summary = #{summary, typeHandler=com.adoreboard.emotics.common.mybatis.MyBatisMapJsonbToPreAnalysisSummary}
      WHERE id = #{datasetId};
    </update>

    <update id="updateVarInfo" parameterType="com.adoreboard.emotics.common.model.revenueAtRisk.ValueAtRiskInfo">
        UPDATE bulk_upload
        SET value_at_risk_info = #{valueAtRiskInfo, typeHandler=com.adoreboard.emotics.common.mybatis.MyBatisMapJsonbToValueAtRiskInfo}
        WHERE id = #{datasetId};
    </update>

    <update id="updateRatingScaleMaps" parameterType="map">
        UPDATE bulk_upload
        SET  rating_scale_maps = #{ratingScaleMaps, typeHandler=com.adoreboard.emotics.common.mybatis.MyBatisMapJsonbToIntegerObjectMap}
        WHERE id = #{datasetId};
    </update>

    <update id="updateMetricCalculationSettings" parameterType="map">
        UPDATE bulk_upload
          SET  metric_calculation_settings = #{metricCalculationSettings, typeHandler=com.adoreboard.emotics.common.mybatis.MyBatisMapJsonbToMetricCalculationSettings}
        WHERE id = #{datasetId};
    </update>

    <!-- Delete single dataset entry -->

    <delete id="delete" parameterType="int">
        DELETE FROM bulk_upload
        WHERE id = #{datasetId}
    </delete>

    <delete id="deleteForUserId" parameterType="int">
        DELETE FROM bulk_upload
        WHERE user_id = #{id}
    </delete>

    <delete id="deleteIds" parameterType="list">
        delete from bulk_upload
        where id in
        <foreach item="item" index="i" collection="list" open="(" separator="," close=")">#{item}</foreach>
    </delete>

    <!-- select section -->

    <select id="selectByUserId" parameterType="int" resultMap="datasetQueryMap">
        WITH datasets AS (
            SELECT *
            FROM bulk_upload
            WHERE user_id = #{userId}
        ),
        <include refid="datasetCommon.bulk_upload_view"/>
        ORDER BY upload_start desc
    </select>

    <select id="selectById" parameterType="int" resultMap="datasetMap">
        WITH datasets AS (
            SELECT *
            FROM bulk_upload
            WHERE id = #{id}
        ),
        <include refid="datasetCommon.bulk_upload_view"/>
    </select>

    <select id="selectByIds" parameterType="list" resultMap="datasetMap">
        WITH datasets AS (
            SELECT *
            FROM bulk_upload
            WHERE id IN <foreach item="item" index="i" collection="list" open="(" separator="," close=")">#{item}</foreach>
        ),
        <include refid="datasetCommon.bulk_upload_view"/>
        ORDER BY upload_start desc
    </select>

    <select id="countStatusForIds" parameterType="map" resultType="int">
        select count (*)
         from bulk_upload
        where id in <foreach item="item" index="i" collection="datasetIds" open="(" separator="," close=")">#{item}</foreach>
          and status = #{status}
    </select>

    <select id="selectByUserIdAndIds" parameterType="map" resultMap="datasetMap">
        WITH datasets AS (
            SELECT *
            FROM bulk_upload
            WHERE id in <foreach item="item" index="i" collection="datasetIds" open="(" separator="," close=")">#{item}</foreach>
              AND user_id = ${userId}
        ),
        <include refid="datasetCommon.bulk_upload_view"/>
        ORDER BY upload_start desc
    </select>

    <select id="selectStatusCounts" resultType="com.adoreboard.emotics.common.model.KeyValue">
        SELECT
            status    AS key,
            count(id) AS value
        FROM bulk_upload
        GROUP BY status
    </select>

    <select id="selectLite" parameterType="map" resultMap="liteMap">
        SELECT
          bu.id,
          bu.user_id,
          bu.label,
          round((bu.summary->> 'adoreScore') :: DOUBLE PRECISION) as adore_score,
          bu.summary->> 'contribution' as contribution,
          COUNT(CASE WHEN ut.parent_id IS NULL THEN 1 END) AS topic_count,
          COUNT(CASE WHEN ut.parent_id IS NOT NULL THEN 1 END) AS subTopic_count,
          bu.document_count
        FROM
          (
            SELECT parent_id, bulk_upload_id
              FROM user_topic
              WHERE bulk_upload_id IN <foreach item="item" index="i" collection="datasetIds" open="(" separator="," close=")">#{item}</foreach>
              AND type = #{topicType}
          ) ut
          RIGHT JOIN bulk_upload bu ON ut.bulk_upload_id = bu.id
        WHERE
          bu.id IN <foreach item="item" index="i" collection="datasetIds" open="(" separator="," close=")">#{item}</foreach>
        GROUP BY bu.id
        ORDER BY bu.id ASC
    </select>

    <select id="selectDocumentCountByDatasetId" parameterType="int" resultType="int">
        SELECT document_count
        FROM bulk_upload
        WHERE id = #{datasetId}
    </select>

    <select id="countDatasets" parameterType="map" resultType="int">
        SELECT count(id)
        FROM bulk_upload
        WHERE id in <foreach item="item" index="i" collection="datasetIds" open="(" separator="," close=")">#{item}</foreach>
        AND (
            user_id = ${userId}
            <if test="workspaceIds != null and workspaceIds.size &gt; 0">
                OR workspace_id in
                <foreach item="item" index="i" collection="workspaceIds" open="(" separator="," close=")">#{item}</foreach>
            </if>
        )
    </select>

    <select id="countDatasetsByWorkspace" parameterType="map" resultType="int">
        SELECT count(id)
        FROM bulk_upload
        WHERE id in <foreach item="item" index="i" collection="datasetIds" open="(" separator="," close=")">#{item}</foreach>
        AND workspace_id = ${workspaceId}
    </select>

    <select id="selectDistinctSavedActionIdsByDatasetIds" parameterType="map" resultType="int">
        SELECT DISTINCT unnest(saved_action_list_ids)
        FROM bulk_upload
        WHERE id IN
        <foreach item="item" index="i" collection="datasetIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectWorkspaceIdsByDatasetIds" parameterType="map" resultType="int">
        SELECT DISTINCT workspace_id
        FROM bulk_upload
        WHERE id IN
        <foreach item="item" index="i" collection="datasetIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectWorkspaceIdByDatasetId" parameterType="map" resultType="int">
        SELECT DISTINCT workspace_id
        FROM bulk_upload
        WHERE id = #{datasetId}
    </select>

    <select id="selectUserIdById" parameterType="map" resultType="int">
        SELECT user_id
        FROM bulk_upload
        WHERE id = #{datasetId}
    </select>

    <select id="selectChildrenIdsByStatusAndParams" parameterType="map" resultType="int">
        SELECT bu.id
        FROM bulk_upload bu
        LEFT JOIN metadata_filters f ON bu.id = f.child_id
        WHERE bu.user_id = #{userId}
        AND bu.parent_id = #{parentId}
        AND bu.status = #{status}
        <if test="filterTypes != null and filterTypes.size &gt; 0">
            AND f.list_type IN
            <foreach item="fType" index="i" collection="filterTypes" open="(" separator="," close=")">#{fType}</foreach>
        </if>
        <if test="filterIds != null and filterIds.size &gt; 0">
            AND f.filter_list_id IN
            <foreach item="fid" index="i2" collection="filterIds" open="(" separator="," close=")">#{fid}</foreach>
        </if>
    </select>

    <select id="isOwner" parameterType="map" resultType="boolean">
        SELECT EXISTS(SELECT 1 FROM bulk_upload WHERE id = #{datasetId} AND user_id = #{userId})
    </select>

    <select id="selectValueAtRiskInfo" parameterType="int" resultMap="valueAtRiskInfoSingleObjectMap">
        SELECT value_at_risk_info
        FROM bulk_upload
        WHERE id = #{datasetId}
    </select>

    <select id="selectValueAtRiskAmount" parameterType="map" resultType="BigDecimal">
        select calculate_var_per_dataset (#{datasetId},#{costPerComment}, #{scaleToTotalPeople}, #{numberOfScalePeople}, #{totalYear})
    </select>

    <!--
    ==============================================================================
    Query section
    ==============================================================================
    -->

    <!-- Query user tiers -->
    <select id="query" parameterType="com.adoreboard.emotics.common.mapper.param.DatasetParam" resultMap="datasetQueryMap">
        WITH datasets AS (
            SELECT *
            FROM bulk_upload
            <include refid="queryAndCountWhereStatement"/>
        ),
        <include refid="datasetCommon.bulk_upload_view"/>
        order by ${sortItem} <if test="sortAsc == false">desc</if>
        <if test="rows > 0 and offset >= 0">limit ${rows} offset ${offset}</if>
    </select>

    <!-- Where statement for "query" / "count" - this is a nice feature of MyBatis which ensures the WHERE statement is the same for both -->
    <sql id="queryAndCountWhereStatement">
        <where>
            <if test="datasetId != -1">and id = #{datasetId}</if>
            <if test="userId != -1">and user_id = ${userId}</if>
            <if test="label != null and label != ''">and lower(label) like CONCAT('%', #{label}, '%')</if>
            <if test="inProgress != null">
                <if test="inProgress">and status != 'finished'</if>
                <if test="!inProgress">and status = 'finished'</if>
            </if>
            <if test="statuses != null and statuses.size &gt; 0 and statuses.size &lt; 8">
                and status in
                <foreach item="item" index="i" collection="statuses" open="(" separator="," close=")">#{item}</foreach>
            </if>
            <if test="deleted != null and deleted == false">
                AND status NOT IN ('deleted', 'filtered')
            </if>
            <if test="archived != null">
                AND archived = #{archived}
            </if>
            <if test="tagIds != null">
                AND #{tagIds,typeHandler=com.adoreboard.emotics.common.mybatis.IntegerListTypeHandler} &amp;&amp; dataset_tags.tag_ids
            </if>
            AND NOT(status = 'finished' AND summary IS NULL)
        </where>
    </sql>

    <sql id="selectDatasetTagIds">
        SELECT dt.dataset_id,
               array_agg(dt.tag_id) as tag_ids
        FROM bulk_upload_dataset_tag dt
        GROUP BY dt.dataset_id
    </sql>

    <!-- Result Map objects -->

    <resultMap id="datasetMap" type="com.adoreboard.emotics.common.model.Dataset" autoMapping="true">
        <id column="id" property="id"/>
        <result column="summary" property="summary" typeHandler="com.adoreboard.emotics.common.mybatis.MyBatisMapJsonbToPreAnalysisSummary"/>
        <result column="progress_status_map" property="statusProgresses" typeHandler="com.adoreboard.emotics.common.mybatis.MyBatisMapJsonbToStatusProgresses"/>
        <result column="stop_words" property="stopWords" typeHandler="com.adoreboard.emotics.common.mybatis.MyBatisMapJsonbToStopWords"/>
        <result column="error" property="error" typeHandler="com.adoreboard.emotics.common.mybatis.MyBatisMapJsonbToDatasetError"/>
        <result column="features" property="features" typeHandler="com.adoreboard.emotics.common.mybatis.ListDatasetFeatureEnumTypeHandler"/>
        <result column="metadata_headers" property="metadataHeaders" typeHandler="com.adoreboard.emotics.common.mybatis.StringListTypeHandler"/>
        <result column="metadata_columns" property="metadataColumns" typeHandler="com.adoreboard.emotics.common.mybatis.IntegerListTypeHandler"/>
        <result column="metadata_types" property="metadataTypes" typeHandler="com.adoreboard.emotics.common.mybatis.StringListTypeHandler"/>
        <result column="pending_changes" property="pendingChanges"/>
        <result column="analysis_download_location" property="downloadLocation"/>
        <result column="original_download_location" property="originalDownloadLocation"/>
        <result column="custom_topic_list" property="customThemeList"/>
        <result column="exclude_auto_topics" property="excludeAutoTopics"/>
        <result column="tags" property="tags" typeHandler="com.adoreboard.emotics.common.mybatis.StringListTypeHandler"/>
        <result column="saved_action_list_ids" property="savedActionListIds" typeHandler="com.adoreboard.emotics.common.mybatis.IntegerListTypeHandler"/>
        <result column="tag_ids" property="tagIds" typeHandler="com.adoreboard.emotics.common.mybatis.IntegerListTypeHandler"/>
        <result column="report_count" property="reportCount"/>
        <result column="value_at_risk_info" property="valueAtRiskInfo" typeHandler="com.adoreboard.emotics.common.mybatis.MyBatisMapJsonbToValueAtRiskInfo"/>
        <result column="rating_scale_maps" property="ratingScaleMaps" typeHandler="com.adoreboard.emotics.common.mybatis.MyBatisMapJsonbToIntegerObjectMap"/>
        <result column="metric_calculation_settings" property="metricCalculationSettings" typeHandler="com.adoreboard.emotics.common.mybatis.MyBatisMapJsonbToMetricCalculationSettings"/>
        <result column="dataset_domain" property="datasetDomain"/>
        <result column="workspace_id" property="workspaceId"/>
    </resultMap>

    <resultMap id="liteMap" type="com.adoreboard.emotics.common.model.query.DatasetLite" autoMapping="true">
        <id column="id" property="id"/>
        <result column="contribution" property="contribution" typeHandler="com.adoreboard.emotics.common.mybatis.MyBatisMapJsonbToAnalysisContribution"/>
    </resultMap>

    <resultMap id="datasetQueryMap" type="com.adoreboard.emotics.common.model.query.DatasetQuery" autoMapping="true" extends="datasetMap">
    </resultMap>

    <resultMap id="valueAtRiskInfoMap" type="com.adoreboard.emotics.common.model.revenueAtRisk.ValueAtRiskInfo" autoMapping="true">
    </resultMap>

    <resultMap id="valueAtRiskInfoSingleObjectMap" type="com.adoreboard.emotics.common.model.revenueAtRisk.ValueAtRiskInfoObject" autoMapping="true">
        <result column="value_at_risk_info" property="valueAtRiskInfo" typeHandler="com.adoreboard.emotics.common.mybatis.MyBatisMapJsonbToValueAtRiskInfo"/>
    </resultMap>

    <!-- Update selected Revenue At Risk template ID -->
    <update id="updateSelectedRarId">
        UPDATE bulk_upload
        SET selected_rar_id = #{selectedRarId}
        WHERE id = #{datasetId}
    </update>

</mapper>
