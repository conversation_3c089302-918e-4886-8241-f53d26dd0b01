<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.adoreboard.emotics.common.mapper.RevenueAtRiskTemplateMapper">

    <resultMap id="revenueAtRiskTemplateResultMap" type="com.adoreboard.emotics.common.model.revenueAtRisk.RevenueAtRiskTemplate">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="workspaceId" column="workspace_id"/>
        <result property="datasetId" column="dataset_id"/>
        <result property="linkedTemplateId" column="linked_template_id"/>
        <result property="createdBy" column="creator"/>
        <result property="createdAt" column="created_at"/>
        <result property="defaultTemplate" column="default_template"/>
        <result property="status" column="status"
                typeHandler="com.adoreboard.emotics.common.mybatis.RevenueAtRiskTemplateStatusTypeHandler"/>
        <result property="revenueAtRisk" column="revenue_at_risk_info"
                typeHandler="com.adoreboard.emotics.common.mybatis.MyBatisMapJsonbToRevenueAtRiskTemplateInfo"/>
    </resultMap>

    <!-- Get workspace templates -->
    <select id="getWorkspaceTemplatesByStatus" resultMap="revenueAtRiskTemplateResultMap">
        SELECT * FROM revenue_at_risk_templates
        WHERE workspace_id = #{workspaceId}
        AND status = #{status, typeHandler=com.adoreboard.emotics.common.mybatis.RevenueAtRiskTemplateStatusTypeHandler}
        ORDER BY default_template DESC, created_at ASC
    </select>

    <!-- Get template by ID -->
    <select id="getTemplateByIdAndStatus" resultMap="revenueAtRiskTemplateResultMap">
        SELECT * FROM revenue_at_risk_templates
        WHERE id = #{templateId}
        AND status = #{status, typeHandler=com.adoreboard.emotics.common.mybatis.RevenueAtRiskTemplateStatusTypeHandler}
    </select>

    <!-- Create workspace template -->
    <insert id="createWorkspaceTemplate" parameterType="com.adoreboard.emotics.common.model.revenueAtRisk.RevenueAtRiskTemplate"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO revenue_at_risk_templates (
            name, workspace_id, creator, revenue_at_risk_info,
            default_template, status, created_at, updated_at
        ) VALUES (
            #{name}, #{workspaceId}, #{createdBy},
            #{revenueAtRisk, typeHandler=com.adoreboard.emotics.common.mybatis.MyBatisMapJsonbToRevenueAtRisk},
            #{defaultTemplate},
            #{status, typeHandler=com.adoreboard.emotics.common.mybatis.RevenueAtRiskTemplateStatusTypeHandler},
            #{createdAt}, #{createdAt}
        )
    </insert>

    <!-- Update template -->
    <update id="updateTemplate" parameterType="com.adoreboard.emotics.common.model.revenueAtRisk.RevenueAtRiskTemplate">
        UPDATE revenue_at_risk_templates
        SET name = #{name},
            revenue_at_risk_info = #{revenueAtRisk, typeHandler=com.adoreboard.emotics.common.mybatis.MyBatisMapJsonbToRevenueAtRisk},
            default_template = #{defaultTemplate},
            status = #{status, typeHandler=com.adoreboard.emotics.common.mybatis.RevenueAtRiskTemplateStatusTypeHandler},
            updated_at = CURRENT_TIMESTAMP
        WHERE id = #{id}
    </update>

    <!-- Soft delete template -->
    <update id="softDeleteTemplate">
        UPDATE revenue_at_risk_templates
        SET status = 'deleted',
            updated_at = CURRENT_TIMESTAMP
        WHERE id = #{templateId}
    </update>

    <!-- Get default template for workspace -->
    <select id="getDefaultTemplateByStatus" resultMap="revenueAtRiskTemplateResultMap">
        SELECT * FROM revenue_at_risk_templates
        WHERE workspace_id = #{workspaceId}
        AND default_template = true
        AND status = #{activeStatus, typeHandler=com.adoreboard.emotics.common.mybatis.RevenueAtRiskTemplateStatusTypeHandler}
        LIMIT 1
    </select>

    <!-- Unset default template for workspace -->
    <update id="unsetDefaultTemplate">
        UPDATE revenue_at_risk_templates
        SET default_template = false, updated_at = CURRENT_TIMESTAMP
        WHERE workspace_id = #{workspaceId} AND default_template = true
    </update>

    <!-- Set default template -->
    <update id="setDefaultTemplate">
        UPDATE revenue_at_risk_templates
        SET default_template = true, updated_at = CURRENT_TIMESTAMP
        WHERE id = #{templateId}
    </update>

    <!-- Check if template name exists -->
    <select id="existsTemplateByNameAndStatus" resultType="boolean">
        SELECT COUNT(*) > 0 FROM revenue_at_risk_templates
        WHERE workspace_id = #{workspaceId}
        AND name = #{name}
        AND status = #{activeStatus, typeHandler=com.adoreboard.emotics.common.mybatis.RevenueAtRiskTemplateStatusTypeHandler}
    </select>
</mapper>
