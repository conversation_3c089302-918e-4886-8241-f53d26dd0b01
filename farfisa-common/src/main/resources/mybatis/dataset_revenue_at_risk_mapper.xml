<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.adoreboard.emotics.common.mapper.DatasetRevenueAtRiskMapper">

    <resultMap id="DatasetRevenueAtRiskResultMap" type="com.adoreboard.emotics.common.model.revenueAtRisk.DatasetRevenueAtRisk">
        <id property="id" column="id"/>
        <result property="datasetId" column="dataset_id"/>
        <result property="templateId" column="template_id"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdAt" column="created_at"/>
        <result property="revenueAtRisk" column="revenue_at_risk"
                typeHandler="com.adoreboard.emotics.common.mybatis.MyBatisMapJsonbToValueAtRiskInfo"/>
    </resultMap>

    <!-- Get dataset templates -->
    <select id="getDatasetTemplates" resultMap="DatasetRevenueAtRiskResultMap">
        SELECT * FROM dataset_revenue_at_risk
        WHERE dataset_id = #{datasetId}
        ORDER BY created_at ASC
    </select>

    <!-- Get dataset template by ID -->
    <select id="getDatasetRevenueAtRisk" resultMap="DatasetRevenueAtRiskResultMap">
        SELECT * FROM dataset_revenue_at_risk
        WHERE id = #{id}
    </select>

    <!-- Get dataset template by template ID -->
    <select id="getDatasetTemplateByTemplateId" resultMap="DatasetRevenueAtRiskResultMap">
        SELECT * FROM dataset_revenue_at_risk
        WHERE dataset_id = #{datasetId} AND template_id = #{templateId}
    </select>

    <!-- Create dataset template -->
    <insert id="createDatasetRevenueAtRisk" parameterType="com.adoreboard.emotics.common.model.revenueAtRisk.DatasetRevenueAtRisk"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO dataset_revenue_at_risk (
            dataset_id, template_id, created_by, revenue_at_risk, created_at, updated_at
        ) VALUES (
            #{datasetId}, #{templateId}, #{createdBy},
            #{revenueAtRisk, typeHandler=com.adoreboard.emotics.common.mybatis.MyBatisMapJsonbToValueAtRiskInfo},
            #{createdAt}, #{updatedAt}
        )
    </insert>

    <!-- Update dataset template -->
    <update id="updateDatasetRevenueAtRisk" parameterType="com.adoreboard.emotics.common.model.revenueAtRisk.DatasetRevenueAtRisk">
        UPDATE dataset_revenue_at_risk
        SET revenue_at_risk = #{revenueAtRisk, typeHandler=com.adoreboard.emotics.common.mybatis.MyBatisMapJsonbToValueAtRiskInfo},
            updated_at = CURRENT_TIMESTAMP
        WHERE id = #{id}
    </update>

    <!-- Delete dataset template -->
    <delete id="deleteDatasetRevenueAtRisk">
        DELETE FROM dataset_revenue_at_risk WHERE id = #{id}
    </delete>

    <!-- Delete all dataset templates for a dataset -->
    <delete id="deleteDatasetTemplatesByDatasetId">
        DELETE FROM dataset_revenue_at_risk WHERE dataset_id = #{datasetId}
    </delete>

    <!-- Clone workspace templates for a new dataset -->
    <insert id="cloneWorkspaceTemplatesForDataset">
        INSERT INTO dataset_revenue_at_risk (dataset_id, template_id, created_by, revenue_at_risk, created_at, updated_at)
        SELECT
            #{datasetId},
            rt.id,
            rt.creator,
            rt.revenue_at_risk_info,
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
        FROM revenue_at_risk_templates rt
        WHERE rt.workspace_id = #{workspaceId}
        AND rt.status = 'active'
    </insert>

    <!-- Count dataset templates -->
    <select id="countDatasetTemplates" resultType="int">
        SELECT COUNT(*) FROM dataset_revenue_at_risk
        WHERE dataset_id = #{datasetId}
    </select>

    <!-- Count dataset templates -->
    <select id="countDatasetWithTemplate" resultType="int">
        SELECT COUNT(*) FROM revenue_at_risk_templates
        WHERE template_id = #{templateId}
    </select>
</mapper>
