-- -----------------------------------------------
-- Base script file for testing the Dao / Mapper
-- intefaces
--
-- -----------------------------------------------
--@formatter:off
-- -----------------------------------------------
-- TABLE: USER_TIER
-- -----------------------------------------------

insert into user_tier (id, name, cost, duration, max_chars, max_uploads, max_comparable_data, product_features,
                       stripe_plan_id)
values (1, 'tier1', 123, 17, 10, 5, 2, '{swot, multilingual}', 'stripe_plan_id_1'),
       (2, 'tier2', 456, -1, 20, 6, 2, '{swot}', null),
       (3, 'tier3', 789, 0, 30, 7, 2, null, null),
       (4, 'tier4', 12, 4, 40, 8, 2, null, null);

UPDATE user_tier
SET max_translated_chars = 1000
WHERE id = 1;

-- -----------------------------------------------
-- TABLE: USERS
-- -----------------------------------------------

insert into users (id, tier_id, login_name, created_on, updated_on, secret, first_name, last_name, email, role,
                   login_count, active, workspace_ids)
values (98, 1, 'testuser', '2001-01-12 13:14:15', '2002-12-21 13:24:35', '$2a$04$Z8ij', 'Test', 'Person',
        '<EMAIL>', 1, 10, true, '{1}'),
       (99, 2, 'adoreuser', '2001-01-12 13:14:15', '2002-12-21 13:24:35', '5EmtaEi/RSg', 'Thor', 'Hammer God',
        '<EMAIL>', 0, 20, false, '{1}'),
       (100, 3, 'testuser2', '2001-01-12 13:14:15', '2002-12-21 13:24:35', 'd9pNP4mrR8d', 'Test', 'Person',
        '<EMAIL>', 0, 30, true, '{1}'),
       (101, 1, 'testuser3', '2001-01-12 13:14:15', '2002-12-21 13:24:35', 'rR8dTeMhC12', 'Test', 'Person',
        '<EMAIL>', 0, 40, true, '{1}'),
       (19, 1, 'testuser4', '2001-01-12 13:14:15', '2002-12-21 13:24:35', 'rR8dTeMhC12', 'Test', 'Person',
        '<EMAIL>', 0, 50, true, '{1}');

UPDATE users
SET survey_monkey_token = 'SM_Token'
WHERE id = 98;

-- -----------------------------------------------
-- TABLE: ORGANISATION
-- -----------------------------------------------

INSERT INTO organisation (id, name, owner_id, settings) VALUES (99, 'farfisa oganisation', 99, '{"label": "farfisa workspaces"}');

UPDATE users
SET organisation_id = 99
WHERE id = 98;

-- -----------------------------------------------
-- TABLE: USAGE_RECORD
-- -----------------------------------------------

INSERT INTO usage_record (id, tier_id, user_id, cycle_start, cycle_end, active, max_usage, current_usage,
                          max_translation_usage, current_translation_usage, upload_count)
VALUES (1, 1, 98, '2001-01-12 13:14:15', NULL, TRUE, 10000, 1000, 5000, 500, 1),
       (2, 2, 99, '2001-01-12 13:14:15', '2001-02-12 13:14:15', FALSE, 10000, 1000, 5000, 500, 1),
       (3, 2, 99, '2001-02-12 13:14:15', '2001-03-12 13:14:15', TRUE, 10000, 1000, 5000, 500, 1),
       (4, 2, 99, '2001-02-12 13:14:15', '2001-03-12 13:14:15', FALSE, 10000, 1000, 5000, 500, 1);

-- -----------------------------------------------
-- TABLE: DATASET
-- -----------------------------------------------

INSERT INTO bulk_upload (id, label, user_id, document_count, content_characters, status, execution_id, upload_start)
VALUES (1000, 'a', 98, 2000, 100000, 'uploading', 1, '2017-10-21T10:24:07.487Z'),
       (1001, 'b', 98, 1000, 200000, 'finished', 2, '2017-10-22T10:24:07.487Z'),
       (1002, 'c', 100, 1000, 200000, 'finished', 3, '2017-10-23T10:24:07.487Z'),
       (1337, 'd', 19, 4, 200, 'aggregating', 4, '2017-10-24T10:24:07.487Z'),
       (1338, 'e', 19, 4, 200, 'finished', 1, '2017-10-24T09:24:07.487Z');

-- A `summary` and `topics` to a `dataset` entry
UPDATE bulk_upload
SET summary  = '{
  "polarity": 0.01514619056833908,
  "adoreScore": 2,
  "timestamp": "2016-11-07T14:25:27.702Z",
  "wordCount": 74,
  "trustIndex": 0.14415499533146592,
  "activations": {
    "low": 0.37310690943043884,
    "mid": 0.06666666666666667,
    "high": 0.1602264239028945
  },
  "contribution": {
    "positive": 0.3,
    "neutral": 0.2,
    "negative": 0.5
  },
  "emotionIndexesAvg": [
    0.25,
    0.5,
    -0.75,
    -0.1,
    0.25,
    0.5,
    -0.75,
    -0.1
  ]
}',
    features = '{TimeSeries}'
WHERE id = 1337;

UPDATE bulk_upload
SET summary  = '{
  "polarity": 0.01514619056833908,
  "adoreScore": 2,
  "timestamp": "2016-11-07T14:25:27.702Z",
  "wordCount": 74,
  "trustIndex": 0.14415499533146592,
  "activations": {
    "low": 0.37310690943043884,
    "mid": 0.06666666666666667,
    "high": 0.1602264239028945
  },
  "contribution": {
    "positive": 0.3,
    "neutral": 0.2,
    "negative": 0.5
  },
  "emotionIndexesAvg": [
    0.25,
    0.5,
    -0.75,
    -0.1,
    0.25,
    0.5,
    -0.75,
    -0.1
  ]
}',
    features = '{TimeSeries}'
WHERE id = 1338;

UPDATE bulk_upload
SET pending_changes = TRUE
WHERE id = 1002;

UPDATE bulk_upload
SET summary = '{
  "polarity": 0.10
}'
WHERE id = 1000;

UPDATE bulk_upload
SET summary = '{
  "polarity": 0.15
}'
WHERE id = 1001;

UPDATE bulk_upload
SET pending_changes = TRUE
WHERE id = 1002;

UPDATE bulk_upload
SET error = '{
  "message": "errorMsg",
  "exception": "errorDetails",
  "timestamp": null
}'
WHERE id = 1337;

UPDATE bulk_upload
SET summary = '{
  "polarity": 0.10
}'
WHERE id = 1000;
UPDATE bulk_upload
SET summary = '{
  "polarity": 0.15
}'
WHERE id = 1001;
UPDATE bulk_upload
SET pending_changes = TRUE
WHERE id = 1002;

UPDATE bulk_upload
SET archived = TRUE
WHERE id = 1000;
UPDATE bulk_upload
SET archived = FALSE
WHERE id = 1001;

-- -----------------------------------------------
-- TABLE: DATASET_CUSTOM_CHARTS
-- -----------------------------------------------

INSERT INTO dataset_custom_charts (dataset_id, custom_chart_type, custom_topic_list)
VALUES (1000, 'SWOT', '[
  {
    "topicId": 1234
  },
  {
    "topicId": 2345
  }
]'::jsonb);

-- -----------------------------------------------
-- TABLE: DATASET_DOWNLOAD_DETAILS
-- -----------------------------------------------

INSERT INTO dataset_download_details (dataset_id, analysis_download_location, original_download_location)
VALUES (1000, null, null),
       (1001, 'http://awesome.place/analysis', 'http://awesome.place/original'),
       (1337, 'http://awesome.place/analysis', 'http://awesome.place/original'),
       (1002, 'http://awesome.space/analysis', 'http://awesome.place/original');

-- -----------------------------------------------
-- TABLE: USER_TOPIC
-- -----------------------------------------------

INSERT INTO user_topic (id, bulk_upload_id, topic_label, original_topic_label, type, emotion_index_id, emotion_indexes,
                        polarity, tone, topic_score, num_of_documents, num_of_positive_documents,
                        num_of_negative_documents, num_of_subtopics, swot)
VALUES (1, 1338, 'Bernie Hotdog', 'Bernie Hotdog', 'overview', 0, '{0.25, 0.5, -0.75, -0.1, 0.25, 0.5, -0.75, -0.1}',
        0.28088606221410992, 0.347251863893, 1.6150876044698905, 100, 40, 60, 12, '{
    "tone": -36.80586426716318,
    "intensity": 0.713364314699022,
    "attribute": "WEAKNESS"
  }'),
       (2, 1338, 'Bernie Bap', 'Bernie Bap', 'overview', 0, '{0.25, 0.5, -0.75, -0.1, 0.25, 0.5, -0.75, -0.1}',
        0.28088606221410992, 0.214551863893, 1.0446989056150876, 30, 25, 5, 12, null),
       (3, 1338, 'Bernie Burger', 'Bernie Burger', 'overview', null, null, 0.28088606221410992, 0.347251863893,
        1.7150876044698905, 10, 10, 0, 12, null),
       (4, 1338, 'Bernie Burger', 'Bernie Burger', 'overview', null, null, 0.28088606221410992, 0.347251863893,
        1.7150876044698905, 10, 10, 0, 12, null),
       (5, 1338, 'Bernie Burger', 'Bernie Burger', 'overview', null, null, 0.28088606221410992, 0.347251863893,
        1.7150876044698905, 10, 10, 0, 12, null);

INSERT INTO user_topic (id, bulk_upload_id, topic_label, original_topic_label, type, emotion_index_id, parent_id)
VALUES (6, 1338, 'Burger', 'Burger', 'index', 1, 3);
-- -----------------------------------------------
-- TABLE: USER_CONTENT
-- -----------------------------------------------

INSERT INTO user_content (id, user_id, original_content, content, timestamp, content_timestamp, language_iso, metadata)
VALUES (1, 98, 'original content1', 'Some test content 1', '2001-01-12 13:14:15', null, 'en', '{null, 2, 3}'),
       (2, 98, 'original content2', 'Some test content 2', '2001-01-12 14:14:15', null, 'it', '{4, 5, 6}'),
       (3, 98, 'original content3', 'Some test content 3', '2001-01-12 15:14:15', null, 'fr', '{7, 8, 9}'),
       (4, 98, 'original content4', 'Some test content 4', '2001-01-12 16:14:15', null, null, null),
       (5, 98, 'original content4', 'Some test content 4', '2001-01-12 16:14:15', null, null, null),
       (6, 19, 'We are going to practice Democracy. I know', 'We are going to practice Democracy. I know',
        '2016-08-21 12:39:39.615', null, 'en', null),
       (7, 19, 'As someone who has never run a negative political ad in his life',
        'As someone who has never run a negative political ad in his life', '2016-09-21 12:39:39.616', null, 'en',
        null),
       (8, 19, 'When you talk about heroes and heroines', 'When you talk about heroes and heroines',
        '2016-09-21 12:39:40.219', '2016-09-21 12:39:40.219', 'fr', null),
       (9, 19, 'Bernies birthday is September 8', 'Bernies birthday is September 8', '2016-09-21 12:39:39.791', null,
        'it', null);

INSERT INTO bulk_upload_contents (bulk_upload_id, user_content_id)
VALUES (1000, 1),
       (1000, 2),
       (1000, 3),
       (1000, 4),
       (1001, 5),
       (1338, 6),
       (1338, 7),
       (1338, 8),
       (1338, 9);

-- -----------------------------------------------
-- TABLE: USER_DOC
-- -----------------------------------------------

INSERT INTO user_doc
(id, user_id, bulk_upload_id, content_id, analysis_summary, timestamp)
VALUES (1, 98, 1000, 1, '{}', '2001-01-01 13:14:15'),
       (2, 98, 1000, 2, '{
         "polarity": 0.05,
         "keyPhrases": [
           "Awesome Option",
           "Date Range"
         ]
       }', '2001-01-02 13:14:15'),
       (3, 98, 1000, 3, '{
         "polarity": 0.10,
         "keyPhrases": [
           "Date Range",
           "Stuff Range"
         ],
         "emotionSums": [
           null,
           1,
           null,
           1,
           null,
           null,
           null,
           1,
           null,
           null,
           null,
           null,
           null,
           null,
           null,
           null,
           null,
           null,
           null,
           null,
           null,
           null,
           null,
           null
         ]
       }', '2001-01-03 13:14:15'),
       (4, 98, 1000, 4, NULL, '2001-01-04 13:14:15'),
       (5, 98, 1001, 5, '{}', '2001-01-05 13:14:15'),
       (9, 98, 1001, 1, '{}', '2001-01-09 13:14:15');

-- Set a real `analysis_summary` for item 9
update user_doc
set analysis_summary =
        '{
          "polarity": 0.9,
          "adoreScore": 90,
          "docDimensionAvg": [
            0.25,
            0.5,
            -0.75,
            -0.1
          ],
          "emotionIndexesAvg": [
            0.25,
            0.5,
            -0.75,
            -0.1,
            0.25,
            0.5,
            -0.75,
            -0.1
          ],
          "senEmotionIndexesAvg": [
            0.25,
            0.5,
            -0.75,
            -0.1,
            0.25,
            0.5,
            -0.75,
            -0.1
          ],
          "docEmotionIndexesAvg": [
            0.25,
            0.5,
            -0.75,
            -0.1,
            0.25,
            0.5,
            -0.75,
            -0.1
          ],
          "emotionSums": [
            0,
            1,
            2,
            3,
            4,
            5,
            6,
            7,
            20,
            21,
            22,
            23,
            24,
            25,
            26,
            27,
            99,
            98,
            97,
            96,
            95,
            94,
            93,
            92
          ],
          "polarity": 0.5
        }'
where id = 9;

-- -----------------------------------------------
-- TABLE: USER_DOC - AGGREGATION
-- -----------------------------------------------
INSERT INTO user_doc
(id, user_id, bulk_upload_id, content_id, timestamp)
VALUES (11, 19, 1338, 6, '2016-08-21 12:39:39.615'),
       (12, 19, 1338, 7, '2016-09-21 12:39:39.616'),
       (13, 19, 1338, 8, '2016-09-21 12:39:40.219'),
       (14, 19, 1338, 9, '2016-09-21 12:39:39.791');

-- -----------------------------------------------
-- TABLE: USER_DOC - ANALYSIS SUMMARY
-- -----------------------------------------------
UPDATE user_doc
SET analysis_summary = '
{
  "polarity": 0.10099999979138374,
  "timestamp": "2017-03-03T15:56:36.612Z",
  "wordCount": 8,
  "trustIndex": 0.2857142857142857,
  "activations": {
    "low": 1.0,
    "mid": 0.0,
    "high": 0.0
  },
  "emotionSums": [
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    1,
    1,
    null,
    2,
    1,
    null,
    null,
    2
  ],
  "passionIndex": 0.0,
  "totalConcepts": 2,
  "characterCount": 42,
  "numOfSentences": 2,
  "uniqueConcepts": 2,
  "docDimensionAvg": [
    0.032999997958540916,
    0.13950000330805779,
    -0.06700000166893005,
    0.19750000536441803
  ],
  "senDimensionAvg": [
    0.032999997958540916,
    0.13950000330805779,
    -0.06700000166893005,
    0.19750000536441803
  ],
  "emotionalCoverage": {
    "tokenCount": 9,
    "emotionalCoverage": 0.2222222222222222,
    "tokenWithEmotions": 2
  },
  "emotionSentenceSums": [
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    1,
    1,
    null,
    2,
    1,
    null,
    null,
    2
  ],
  "maxDocumentEmotions": 2,
  "maxSentenceEmotions": 2,
  "docEmotionIndexesAvg": [
    0.0,
    0.032999997958540916,
    0.0,
    0.13950000330805779,
    0.06700000166893005,
    0.0,
    0.0,
    0.19750000536441803
  ],
  "senEmotionIndexesAvg": [
    0.03200000151991844,
    0.09799999743700027,
    0.0,
    0.13950000330805779,
    0.1340000033378601,
    0.0,
    0.0,
    0.19750000536441803
  ],
  "emotionIndexesAvg": [
    0.03200000151991844,
    0.09799999743700027,
    0.0,
    0.13950000330805779,
    0.1340000033378601,
    0.0,
    0.0,
    0.19750000536441803
  ],
  "totalEmotionOccurrence": 7,
  "maxEmotionsSingleSentence": 1,
  "totalEmotionSentenceOccurrence": 7,
  "contribution": {
    "positive": 0.0,
    "negative": 0.8,
    "neutral": 0.2
  }
}
'
WHERE id = 11;

UPDATE user_doc
SET analysis_summary = '
{
  "polarity": 0.2019999921321869,
  "timestamp": "2017-03-03T15:56:36.613Z",
  "wordCount": 13,
  "trustIndex": 0.125,
  "activations": {
    "low": 0.75,
    "mid": 0.25,
    "high": 0.0
  },
  "emotionSums": [
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    1,
    null,
    1,
    null,
    null,
    null,
    null,
    null,
    1,
    1,
    null,
    1,
    1,
    1,
    1
  ],
  "passionIndex": 0.125,
  "totalConcepts": 2,
  "characterCount": 64,
  "numOfSentences": 1,
  "uniqueConcepts": 2,
  "docDimensionAvg": [
    0.3079999890178442,
    0.32300000498071313,
    -0.1260000029578805,
    0.1119999960064888
  ],
  "senDimensionAvg": [
    0.3079999890178442,
    0.32300000498071313,
    -0.1260000029578805,
    0.1119999960064888
  ],
  "emotionalCoverage": {
    "tokenCount": 13,
    "emotionalCoverage": 0.23076923076923078,
    "tokenWithEmotions": 3
  },
  "emotionSentenceSums": [
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    1,
    null,
    1,
    null,
    null,
    null,
    null,
    null,
    1,
    1,
    null,
    1,
    1,
    1,
    1
  ],
  "maxDocumentEmotions": 1,
  "maxSentenceEmotions": 1,
  "docEmotionIndexesAvg": [
    0.0,
    0.3079999890178442,
    0.0,
    0.32300000498071313,
    -0.1260000029578805,
    0.0,
    0.0,
    0.1119999960064888
  ],
  "senEmotionIndexesAvg": [
    0.00001,
    0.3079999890178442,
    0.0,
    0.32300000498071313,
    0.1260000029578805,
    0.0,
    0.0,
    0.1119999960064888
  ],
  "emotionIndexesAvg": [
    0.00001,
    0.3079999890178442,
    0.0,
    0.32300000498071313,
    0.1260000029578805,
    0.0,
    0.0,
    0.1119999960064888
  ],
  "totalEmotionOccurrence": 8,
  "maxEmotionsSingleSentence": 1,
  "totalEmotionSentenceOccurrence": 8,
  "contribution": {
    "positive": 0.5,
    "negative": 0.3,
    "neutral": 0.2
  }
}
'
WHERE id = 12;

UPDATE user_doc
SET analysis_summary = '
{
  "polarity": 0.6539999842643738,
  "timestamp": "2017-03-03T15:56:36.613Z",
  "wordCount": 7,
  "trustIndex": 0.4,
  "activations": {
    "low": 0.0,
    "mid": 0.2,
    "high": 0.8
  },
  "emotionSums": [
    null,
    1,
    null,
    1,
    null,
    null,
    null,
    2,
    null,
    null,
    1,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null
  ],
  "passionIndex": 0.2,
  "totalConcepts": 2,
  "characterCount": 39,
  "numOfSentences": 1,
  "uniqueConcepts": 2,
  "docDimensionAvg": [
    0.40299999713897705,
    0.20600000023841858,
    0.0,
    0.7740000188350677
  ],
  "senDimensionAvg": [
    0.40299999713897705,
    0.20600000023841858,
    0.0,
    0.7740000188350677
  ],
  "emotionalCoverage": {
    "tokenCount": 7,
    "emotionalCoverage": 0.2857142857142857,
    "tokenWithEmotions": 2
  },
  "emotionSentenceSums": [
    null,
    1,
    null,
    1,
    null,
    null,
    null,
    1,
    null,
    null,
    1,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null
  ],
  "maxDocumentEmotions": 2,
  "maxSentenceEmotions": 1,
  "docEmotionIndexesAvg": [
    0.0,
    0.40299999713897705,
    0.0,
    0.20600000023841858,
    0.0,
    0.0,
    0.0,
    0.7740000188350677
  ],
  "senEmotionIndexesAvg": [
    0.00002,
    0.40299999713897705,
    0.0,
    0.20600000023841858,
    0.0,
    0.0,
    0.0,
    0.7740000188350677
  ],
  "emotionIndexesAvg": [
    0.00002,
    0.40299999713897705,
    0.0,
    0.20600000023841858,
    0.0,
    0.0,
    0.0,
    0.7740000188350677
  ],
  "totalEmotionOccurrence": 5,
  "maxEmotionsSingleSentence": 2,
  "totalEmotionSentenceOccurrence": 4,
  "contribution": {
    "positive": 0.5,
    "negative": 0.3,
    "neutral": 0.2
  }
}
'
WHERE id = 13;

UPDATE user_doc
SET analysis_summary = '
{
  "polarity": 0.351500004529953,
  "timestamp": "2017-03-03T15:56:36.613Z",
  "wordCount": 4,
  "trustIndex": 0.5,
  "activations": {
    "low": 0.5,
    "mid": 0.0,
    "high": 0.5
  },
  "emotionSums": [
    null,
    1,
    null,
    null,
    null,
    null,
    null,
    1,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    1,
    null,
    null,
    null,
    null,
    null,
    1
  ],
  "passionIndex": 0.25,
  "totalConcepts": 2,
  "characterCount": 15,
  "numOfSentences": 1,
  "uniqueConcepts": 2,
  "docDimensionAvg": [
    0.5199999995529652,
    0.0,
    0.0,
    0.533500000834465
  ],
  "senDimensionAvg": [
    0.5199999995529652,
    0.0,
    0.0,
    0.533500000834465
  ],
  "emotionalCoverage": {
    "tokenCount": 4,
    "emotionalCoverage": 0.5,
    "tokenWithEmotions": 2
  },
  "emotionSentenceSums": [
    null,
    1,
    null,
    null,
    null,
    null,
    null,
    1,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    1,
    null,
    null,
    null,
    null,
    null,
    1
  ],
  "maxDocumentEmotions": 1,
  "maxSentenceEmotions": 1,
  "docEmotionIndexesAvg": [
    0.0,
    0.5199999995529652,
    0.0,
    0.0,
    0.0,
    0.0,
    0.0,
    0.533500000834465
  ],
  "senEmotionIndexesAvg": [
    0.00003,
    0.5199999995529652,
    0.0,
    0.0,
    0.0,
    0.0,
    0.0,
    0.533500000834465
  ],
  "emotionIndexesAvg": [
    0.00003,
    0.5199999995529652,
    0.0,
    0.0,
    0.0,
    0.0,
    0.0,
    0.533500000834465
  ],
  "totalEmotionOccurrence": 4,
  "maxEmotionsSingleSentence": 1,
  "totalEmotionSentenceOccurrence": 4,
  "contribution": {
    "positive": 0.5,
    "negative": 0.3,
    "neutral": 0.2
  }
}
'
WHERE id = 14;

-- -------------------------------
-- TABLE: USER_TOPIC_DOC
-- -------------------------------
INSERT INTO user_topic_doc (user_topic_id, user_doc_id)
VALUES (1, 11),
       (1, 12),
       (2, 13),
       (2, 14),
       (3, 13);

-- -------------------------------
-- TABLE: REGISTRATION
-- -------------------------------

INSERT INTO registration (id, user_id, email, hash, registration_status)
VALUES (10, 98, '<EMAIL>', 'nasjhdbuyehryubhl', 'registered');

-- -------------------------------
-- TABLE: DATASET_STATUS
-- -------------------------------

INSERT INTO bulk_upload_status (bulk_upload_id, started_at, updated_at, status, num_of_items, current_item)
VALUES (1000, '2002-12-21 13:24:35', '2002-12-21 13:24:35', 'uploading', 5, 0),
       (1000, '2002-12-21 13:24:35', '2002-12-21 13:24:35', 'analysing', 5, 0),
       (1001, '2002-12-21 13:24:35', '2002-12-21 13:24:35', 'aggregating', 5, 0),
       (1002, '2002-12-21 13:24:35', '2002-12-21 13:24:35', 'uploading', 5, 0),
       (1337, '2002-12-21 13:24:35', '2002-12-21 13:24:35', 'uploading', 5, 0),
       (1337, '2002-12-21 14:24:35', '2002-12-21 13:24:35', 'aggregating', 5, 0);

-- -------------------------------
-- TABLE: TRANSLATION
-- -------------------------------

INSERT INTO translation_data (id, original_data, translated_data, hash_code)
VALUES (1, 'Polish data', 'Something translated', '123');

-- -------------------------------
-- TABLE: SEARCH
-- -------------------------------

INSERT INTO search_components (id, query, score_filters, exclude_content_ids, exact, distinct_content)
VALUES (1, '(buying & purchasing)', '[
  {
    "type": "SADNESS",
    "value": 0.0,
    "operator": "GREATER"
  }
]', '{1,2}', true, false),
       (2, '(bought & purchased)', '[
         {
           "type": "SADNESS",
           "value": 0.0,
           "operator": "GREATER"
         }
       ]', '{3,4}', true, true);

-- -------------------------------
-- TABLE: JOURNEY
-- -------------------------------

INSERT INTO journey (id, bulk_upload_id, label, status)
VALUES (1, 1000, 'Journey 1 of User 98', 'CREATED');

-- -------------------------------
-- TABLE: JOURNEY_STAGE
-- -------------------------------

INSERT INTO journey_stage (id, journey_id, label, exemplar_snippet_id, exemplar_topic_id, search_id, "position",
                           "status")
VALUES (1, 1, 'PRE PURCHASE', 1, 2, 1, 0, 'FINISHED'),
       (2, 1, 'POST PURCHASE', 3, 4, 2, 1, 'FINISHED')
;

-- Survey --
INSERT INTO survey (id, survey_uuid, user_id, dataset_id, namespace, label, description, status, questions, configurations, total_responses, total_analysed_responses, created_at, updated_at)
VALUES (1, '123-456', 98, 1000, 'Emotics', 'Sample Survey', 'Sample description', 'DRAFT', '[{"description":"how do you feel about the service.", "metricRange":[1,5], "rangeLabel":["Bad", "Good"]},{"description":"why do you feel that way.", "metricRange":null}]', '{"respondentInfo": {"displayed": true, "properties":[{"key": "email", "description": "abc", "type": "EMAIL", "required": true}, {"key": "phone number", "required": false}]}}', 0, 0, {ts '2016-08-02 11:24:09.626'}, {ts '2016-08-02 11:24:09.626'});

-- Workspace--
INSERT INTO workspace (id, label, owner_id ,user_limit, organisation_id) VALUES (99, 'farfisa', 99,  5, 99);
INSERT INTO workspace (id, label, owner_id ,user_limit, organisation_id) VALUES (98, 'farfisa-2', 99,  5, 99);

-- -----------------------------------------------
-- TABLE: STOP_WORD
-- -----------------------------------------------

INSERT INTO stop_word (id, bulk_upload_id, stop_word, status)
VALUES (3000, 1000, 'bernie', 'applied'),
       (3001, 1000, 'bun', 'deleted'),
       (3002, 1000, 'burger', 'inserted'),

       (3003, 1001, 'iphone', 'inserted'),
       (3004, 1001, 'iphone6', 'inserted'),
       (3005, 1001, 'phone', 'deleted'),
       (3006, 1001, 'mobile', 'deleted'),
       (3007, 1001, 'nexus4', 'applied'),
       (3008, 1001, 'nexus5', 'applied'),

       (3009, 1337, 'fight', 'inserted'),
       (3010, 1337, 'blabla', 'inserted');

INSERT INTO stop_word (id, workspace_id, stop_word, status)
VALUES (4000, 99, 'bernie', 'applied'),
       (4001, 99, 'bun', 'deleted'),
       (4002, 99, 'burger', 'inserted'),

       (4003, 99, 'iphone', 'inserted'),
       (4004, 99, 'iphone6', 'inserted'),
       (4005, 99, 'phone', 'deleted'),
       (4006, 99, 'mobile', 'deleted'),
       (4007, 99, 'nexus4', 'applied'),
       (4008, 99, 'nexus5', 'applied'),

       (4009, 99, 'fight', 'inserted'),
       (4010, 99, 'blabla', 'inserted');
--Saved Action
INSERT INTO saved_actions (id, user_id, type, components, workspace_id)
    VALUES (1, 98, 'CREATE_SEARCH_THEME', '{"newLabel": "Theme2", "searchExact": false, "searchQuery": "simple", "searchDistinctContent": false}', 99);

INSERT INTO saved_action_lists (id, user_id, label, list, workspace_id) VALUES (1, 98, 'ABC', '{1}', 99);

-- Tags
INSERT INTO dataset_tag (id, user_id, tag_name, workspace_id)
    VALUES (1, 98, 'Tag1', 99), (2, 98, 'Tag2', 99);

INSERT INTO bulk_upload_dataset_tag (dataset_id, tag_id)
    VALUES (1337, 1), (1337, 2);

-- Permissions
INSERT INTO dataset_permission (dataset_id, workspace_id, permission_type)
    VALUES (1337, 99, 'PUBLIC');

--Storyteller
INSERT INTO storyteller_report
( dataset_id
, user_id
, report_type
, theme_ids
, settings
, id)
VALUES ( 1337
       , 98
       , 'storyteller'
       , '{1,2,3}'
       , '{"title": "Storyteller"}'
       , 99
       );
INSERT INTO storyteller_report
( dataset_id
, user_id
, report_type
, theme_ids
, settings
, id)
VALUES ( 1338
       , 98
       , 'storyteller'
       , '{1,2,3}'
       , '{"title": "Storyteller"}'
       , 98
       );

INSERT INTO reporting_job
(id, user_id, "name", schedule_type, schedule_time, schedule_day, schedule_month_in_quarter, schedule_month_in_year, execution_number, created_at, workspace_id)
VALUES(99, 98, 'Test Daily', 'DAILY', '11:00:00', NULL, NULL, NULL, 0, '2025-03-31 11:42:59.145', 99);

--Rating Scale
INSERT INTO rating_scale
(id, user_id, name,workspace_id, rating_scale_map)
VALUES (99, 98, 'abc', 99, '{"1": {"ALWAYS": 5, "NOT_RECENT": 2}}');
-- -------------------------------
-- MISCELLANEOUS
-- -------------------------------

-- Reset sequence id's (note this isn't required for MySQL databases)
-- See http://www.postgresql.org/docs/8.1/static/functions-sequence.html for more information
SELECT setval(pg_get_serial_sequence('bulk_upload', 'id'), coalesce(max(id), 0) + 1, FALSE)
FROM bulk_upload;
SELECT setval(pg_get_serial_sequence('registration', 'id'), coalesce(max(id), 0) + 1, FALSE)
FROM registration;
SELECT setval(pg_get_serial_sequence('stop_word', 'id'), coalesce(max(id), 0) + 1, FALSE)
FROM stop_word;
SELECT setval(pg_get_serial_sequence('user_doc', 'id'), coalesce(max(id), 0) + 1, FALSE)
FROM user_doc;
SELECT setval(pg_get_serial_sequence('user_content', 'id'), coalesce(max(id), 0) + 1, FALSE)
FROM user_content;
SELECT setval(pg_get_serial_sequence('usage_record', 'id'), coalesce(max(id), 0) + 1, FALSE)
FROM usage_record;
SELECT setval(pg_get_serial_sequence('user_tier', 'id'), coalesce(max(id), 0) + 1, FALSE)
FROM user_tier;
SELECT setval(pg_get_serial_sequence('user_topic', 'id'), coalesce(max(id), 0) + 1, FALSE)
FROM user_topic;
SELECT setval(pg_get_serial_sequence('users', 'id'), coalesce(max(id), 0) + 1, FALSE)
FROM users;
SELECT setval(pg_get_serial_sequence('translation_data', 'id'), coalesce(max(id), 0) + 1, FALSE)
FROM translation_data;
SELECT setval(pg_get_serial_sequence('search_components', 'id'), coalesce(max(id), 0) + 1, FALSE)
FROM search_components;
SELECT setval(pg_get_serial_sequence('journey', 'id'), coalesce(max(id), 0) + 1, FALSE)
FROM journey;
SELECT setval(pg_get_serial_sequence('journey_stage', 'id'), coalesce(max(id), 0) + 1, FALSE)
FROM journey_stage;
SELECT setval(pg_get_serial_sequence('survey', 'id'), coalesce(max(id), 0) + 1, FALSE)
FROM survey;
SELECT setval(pg_get_serial_sequence('dataset_tag', 'id'), coalesce(max(id), 0) + 1, FALSE)
FROM dataset_tag;
SELECT setval(pg_get_serial_sequence('storyteller_report', 'id'), coalesce(max(id), 0) + 1, FALSE)
FROM storyteller_report;
SELECT setval(pg_get_serial_sequence('storyteller_slide', 'id'), coalesce(max(id), 0) + 1, FALSE)
FROM storyteller_slide;

-- -------------------------------
-- TABLE: Spring Batch Job
-- -------------------------------

INSERT INTO "public"."batch_job_instance" (job_instance_id, version, job_name, job_key)
VALUES (3, 0, 'consumingJob', '0138a77c026a7d87c5329a902f9a9267');
INSERT INTO "public"."batch_job_execution" (job_execution_id, version, job_instance_id, create_time, start_time,
                                            end_time, status, exit_code, exit_message, last_updated,
                                            job_configuration_location)
VALUES (3, 1, 3, {ts '2016-08-02 11:24:09.603'}, {ts '2016-08-02 11:24:09.626'}, null, 'COMPLETED', 'COMPLETED', '',
        {ts '2016-08-02 11:24:09.627'}, {ts '2016-08-02 11:24:09.627'});
INSERT INTO "public"."batch_job_execution_context" (job_execution_id, short_context, serialized_context)
VALUES (3, '{"map":[""]}', null);
INSERT INTO "public"."batch_job_execution_params" (job_execution_id, type_cd, key_name, string_val, date_val, long_val,
                                                   double_val, identifying)
VALUES (3, 'LONG', 'datasetId', '', {ts '1970-01-01 01:00:00.0'}, 1001, 0.0, 'Y');