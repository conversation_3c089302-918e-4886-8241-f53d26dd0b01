package com.adoreboard.emotics.common.mapper;

import org.junit.After;
import org.junit.Before;
import org.springframework.beans.factory.annotation.Autowired;

public class DatasetRevenueAtRiskMapperTest extends AbstractMapperTest {
    @Autowired
    private DatasetRevenueAtRiskMapper datasetRevenueAtRiskMapper;

    @Before
    public void setup() throws Exception {
        insertPostgreSqlTestData();
    }

    @After
    public void tearDown() throws Exception {
        deletePostgreSqlTestData();
    }
}