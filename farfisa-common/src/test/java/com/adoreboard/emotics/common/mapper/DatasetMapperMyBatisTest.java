/*
 * Copyright 2022 Adoreboard Ltd. All rights reserved.
 * ADOREBOARD PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */

package com.adoreboard.emotics.common.mapper;

import com.adoreboard.emotics.common.enums.DatasetFeature;
import com.adoreboard.emotics.common.enums.DatasetPermissionType;
import com.adoreboard.emotics.common.enums.DatasetStatus;
import com.adoreboard.emotics.common.enums.TopicType;
import com.adoreboard.emotics.common.enums.ValueAtRiskType;
import com.adoreboard.emotics.common.enums.ValueAtRiskWeight;
import com.adoreboard.emotics.common.mapper.param.DatasetParam;
import com.adoreboard.emotics.common.model.Dataset;
import com.adoreboard.emotics.common.model.DatasetError;
import com.adoreboard.emotics.common.model.DatasetProgress;
import com.adoreboard.emotics.common.model.KeyValue;
import com.adoreboard.emotics.common.model.revenueAtRisk.ValueAtRiskInfo;
import com.adoreboard.emotics.common.model.analysis.AnalysisSummary;
import com.adoreboard.emotics.common.model.enums.VarCurrency;
import com.adoreboard.emotics.common.model.query.DatasetLite;
import com.adoreboard.emotics.common.model.query.DatasetQuery;
import org.joda.time.DateTime;
import org.json.JSONObject;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.skyscreamer.jsonassert.JSONAssert;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static java.util.Arrays.asList;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.*;

/**
 * Unit test to test the MyBatis mappings to the DatasetMapper interface.
 * <p>
 * This is an integration test as it requires a live database to run on.
 * <p>
 * NOTE: if this unit test fails it may indicate that the test database hasn't been created i.e. "farfisa_test". Run the maven "mvn install" to set up the test database.
 *
 * <AUTHOR> Marks
 * <AUTHOR> Huy Vu
 */
public class DatasetMapperMyBatisTest extends AbstractMapperTest {

    // Constants

    private static final String DATASET = "bulk_upload";

    private static final double[] sampleIndexScores = new double[]{0.00857677298669365, 0.368202200031074, 0.0, 0.19435113916768537,
            0.07562678029020044, 0.0, 0.0, 0.4996888016397918};

    // Fields

    @Autowired
    private DatasetMapper datasetMapper;

    @Before
    public void setup() throws Exception {
        insertPostgreSqlTestData();
    }

    @After
    public void tearDown() throws Exception {
        deletePostgreSqlTestData();
    }

    // ===================================================================
    // CRUD methods
    // ===================================================================

    @Test
    public void shouldInsertDataset() throws Exception {

        // Setup
        AnalysisSummary summary = new AnalysisSummary();
        summary.setPolarity(1d);

        // Given

        clearTables("storyteller_report", "user_topic_doc", "user_topic", "user_doc", "user_content", "stop_word", "dataset_download_details",
                "dataset_custom_charts", "dataset_permission", "bulk_upload");

        DateTime uploadStart = new DateTime("2001-01-11T13:24:35.000Z");
        DateTime uploadEnd = new DateTime("2001-01-11T14:24:35.000Z");
        Dataset dataset = new Dataset(98, 2000, 25000, "dave");

        dataset.setFeatures(Arrays.asList(DatasetFeature.TimeSeries));
        dataset.setMetadataHeaders(Arrays.asList("ABC", "EDF"));
        dataset.setSummary(summary);
        dataset.setDatasetDomain("Education");
        //        dataset.addProgressStatus(new ProgressStatus(DatasetStatus.uploading, uploadStart, uploadEnd, 25000, 0l ));

        // When

        datasetMapper.insert(dataset);

        // Then

        Map<String, Object> createdDocument = jdbcTemplate.queryForMap("SELECT * FROM bulk_upload");
        Integer buId = (Integer) createdDocument.get("id");
        assertThat(buId).isNotNull();
        assertEquals("dave", createdDocument.get("label"));
        assertEquals(98, createdDocument.get("user_id"));
        assertEquals(2000, createdDocument.get("document_count"));
        assertEquals(25000, createdDocument.get("content_characters"));
        assertEquals("{TimeSeries}", createdDocument.get("features").toString());
        assertEquals("{ABC,EDF}", createdDocument.get("metadata_headers").toString());
        assertEquals("Education", createdDocument.get("dataset_domain"));
        String actualJsonString = createdDocument.get("summary").toString();
        JSONObject actualObj = new JSONObject(actualJsonString);
        // actualObj.remove("timestamp");

        assertTrue(BigDecimal.valueOf(1.0).compareTo((BigDecimal) actualObj.get("polarity")) == 0);
    }

    @Test
    public void shouldUpdateDataset() throws Exception {

        // Setup

        String location = "http://awesome.place";
        AnalysisSummary summary = new AnalysisSummary();
        summary.setPolarity(1d);
        summary.setTimestamp(null);

        // Given

        Dataset dataset = new Dataset(-1, 10, 8000000, "awesomeness");
        dataset.setId(1000);

        dataset.setSummary(summary);
        dataset.setStatus(DatasetStatus.finished);
        dataset.setDownloadLocation(location);
        dataset.setDatasetDomain("metadataValue");

        // When

        datasetMapper.update(dataset);

        // Then

        Map<String, Object> createdDocument = jdbcTemplate.queryForMap("SELECT * FROM bulk_upload WHERE id = 1000");
        assertEquals(1000, createdDocument.get("id"));
        assertEquals(98, createdDocument.get("user_id"));
        assertEquals(10, createdDocument.get("document_count"));
        assertEquals("awesomeness", createdDocument.get("label"));
        assertEquals("metadataValue", createdDocument.get("dataset_domain"));
        JSONAssert.assertEquals(
                "{\"polarity\": 1.0, \"emotionalCoverage\": {\"tokenCount\": 0, \"emotionalCoverage\": 0.0, \"tokenWithEmotions\": 0}}",
                createdDocument.get("summary").toString(), true);
    }

    @Test
    public void shouldUpdateError() throws Exception {

        // Given

        DatasetError datasetError = new DatasetError("errorMsg", "errorDetails");
        datasetError.setTimestamp(null);

        // When

        datasetMapper.updateError(1000, datasetError);

        // Then

        Map<String, Object> createdDocument = jdbcTemplate.queryForMap("SELECT * FROM bulk_upload WHERE id = 1000");
        JSONAssert.assertEquals("{\"message\": \"errorMsg\", \"exception\": \"errorDetails\", \"timestamp\": null}",
                createdDocument.get("error").toString(), true);

    }

    @Test
    public void shouldUpdateStatus() throws Exception {

        // Given

        assertThat(selectMap("bulk_upload", "id = 1000")).containsEntry("status", "uploading");
        assertThat(selectMap("bulk_upload", "id = 1001")).containsEntry("status", "finished");

        // When / Then

        datasetMapper.updateStatus(1000, DatasetStatus.finished, null);
        assertThat(selectMap("bulk_upload", "id = 1000")).containsEntry("status", "finished");

        datasetMapper.updateStatus(1001, DatasetStatus.aggregating, null);
        assertThat(selectMap("bulk_upload", "id = 1001")).containsEntry("status", "aggregating");

    }

    @Test
    public void shouldUpdateDocCountBasedOnContent() throws Exception {

        // When

        datasetMapper.updateDocCount(1001, false);

        // Then

        int count = jdbcTemplate.queryForObject("SELECT document_count FROM bulk_upload WHERE id = 1001", Integer.class);

        assertEquals(1, count);
    }

    @Test
    public void shouldUpdateDocCountBasedOnSummary() throws Exception {

        // When

        datasetMapper.updateDocCount(1001, true);

        // Then

        int count = jdbcTemplate.queryForObject("SELECT document_count FROM bulk_upload WHERE id = 1001", Integer.class);

        assertEquals(2, count);

    }

    @Test
    public void shouldCountStatusForIds() throws Exception {

        // Given => `ID|STATUS` => 1000|uploading, 1001|finished, 1002|finished, 1337|aggregating

        // When / Then

        assertThat(datasetMapper.countStatusForIds("uploading", asList(1000, 1001, 1002, 1337))).isEqualTo(1);
        assertThat(datasetMapper.countStatusForIds("finished", asList(1000, 1001, 1002, 1337))).isEqualTo(2);
        assertThat(datasetMapper.countStatusForIds("aggregating", asList(1000, 1001, 1002, 1337))).isEqualTo(1);

    }

    @Test
    public void shouldUpdateSummary() throws Exception {

        // Setup
        Integer[] emotionSums = new Integer[]{0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10};

        AnalysisSummary summary = new AnalysisSummary();
        summary.setEmotionSums(emotionSums);
        summary.setEmotionIndexesAvg(
                new Double[]{-0.35634520208635413424, 0.24862323656177362780, -0.14152598278796744731, 0.19865340823564179279,
                        -0.18065600361941729905, 0.27713825177588224514, -0.35794412718349311113, 0.28116380178799979041});
        summary.finalise();

        // Given

        // When

        datasetMapper.updateSummary(1337, summary);

        // Then

        AnalysisSummary newSummary = selectObject("bulk_upload", "summary", "id = 1337", AnalysisSummary.class);
        assertEquals(0.2347267522624342, newSummary.getContribution().getPositive(), PRECISION);
        assertEquals(0.31624136739014636, newSummary.getContribution().getNegative(), PRECISION);
        assertEquals(0.4490318803474195, newSummary.getContribution().getNeutral(), PRECISION);
        assertEquals(-0.18908421601511108, newSummary.getPolarity(), PRECISION);
        assertArrayEquals(emotionSums, newSummary.getEmotionSums());
    }

    @Test
    public void shouldUpdatePendingChanges() throws Exception {

        // Given
        Map<String, Object> datasetBefore = jdbcTemplate.queryForMap("SELECT * FROM bulk_upload WHERE id = 1001");
        assertEquals(false, datasetBefore.get("pending_changes"));

        // When
        datasetMapper.updatePendingChanges(1001, true);

        // Then
        Map<String, Object> datasetAfter = jdbcTemplate.queryForMap("SELECT * FROM bulk_upload WHERE id = 1001");
        assertEquals(true, datasetAfter.get("pending_changes"));

    }

    @Test
    public void shouldSelectByUserId() throws Exception {

        // When

        List<Dataset> datasets = datasetMapper.selectByUserId(19);

        // Then

        assertEquals(2, datasets.size());

        Dataset dataset = datasets.get(0);

        assertEquals(1337, dataset.getId());
        assertEquals(19, dataset.getUserId());
        assertEquals(200, dataset.getContentCharacters());
        assertEquals(4, dataset.getDocumentCount());
        assertEquals(DatasetPermissionType.PUBLIC, dataset.getPermissionType());
        assertEquals("errorMsg", dataset.getError().getMessage());
        assertEquals(DatasetStatus.aggregating, dataset.getStatus());
        assertEquals("http://awesome.place/analysis", dataset.getDownloadLocation());
        assertEquals("http://awesome.place/original", dataset.getOriginalDownloadLocation());

        assertEquals(2, dataset.getTagIds().size());
        assertEquals(1, dataset.getTagIds().get(0).intValue());
        assertEquals(2, dataset.getTagIds().get(1).intValue());

        assertEquals(2, dataset.getStopWords().size());
        assertEquals("fight", dataset.getStopWords().get(0).getStopWord());
        assertEquals("blabla", dataset.getStopWords().get(1).getStopWord());

        assertEquals(2, dataset.getStatusProgresses().size());
        DatasetProgress p1 = dataset.getStatusProgresses().get(DatasetStatus.aggregating);
        assertEquals(5, p1.getNumOfItems());
        assertEquals(0, p1.getCurrentItem());
        Assert.assertEquals(DatasetStatus.aggregating, p1.getStatus());
        DatasetProgress p2 = dataset.getStatusProgresses().get(DatasetStatus.uploading);
        assertEquals(5, p2.getNumOfItems());
        assertEquals(0, p2.getCurrentItem());
        Assert.assertEquals(DatasetStatus.uploading, p2.getStatus());
    }

    @Test
    public void shouldSelectByIds() throws Exception {

        // Setup
        Integer id100 = 100;
        Integer id98 = 98;

        // When

        List<Dataset> datasets = datasetMapper.selectByIds(Arrays.asList(1000, 1001, 1002));

        // Then

        assertEquals(3, datasets.size());
        assertEquals(1, datasets.stream().filter(item -> id100.equals(item.getUserId())).count());
        assertEquals(2, datasets.stream().filter(item -> id98.equals(item.getUserId())).count());
    }

    @Test
    public void shouldSelectByUserIdAnIds() throws Exception {

        // Setup

        Integer id98 = 98;

        // When

        List<Dataset> datasets = datasetMapper.selectByUserIdAndIds(98, Arrays.asList(1000, 1001, 1002));

        // Then

        assertEquals(2, datasets.size());
        assertEquals(2, datasets.stream().filter(item -> id98.equals(item.getUserId())).count());
    }

    @Test
    public void shouldSelectByUserIdAnIdsAndIncludeSummary() throws Exception {

        // Setup

        Integer id19 = 19;

        // When

        List<Dataset> datasets = datasetMapper.selectByUserIdAndIds(id19, Arrays.asList(1337));

        // Then

        assertEquals(1, datasets.size());
        assertEquals(1, datasets.stream().filter(item -> id19.equals(item.getUserId())).count());
        assertThat(datasets).extracting(Dataset::getSummary).doesNotContainNull();
        assertThat(datasets.get(0).getExecutionId()).isEqualTo(4);
        assertTrue(datasets.get(0).getFeatures().contains(DatasetFeature.TimeSeries));
        assertThat(datasets.get(0).getReportCount()).isEqualTo(1);
    }

    @Test
    public void shouldSelectById() throws Exception {

        // When

        Dataset dataset = datasetMapper.selectById(1337);

        // Then

        assertEquals(1337, dataset.getId());
        assertEquals(19, dataset.getUserId());
        assertEquals(200, dataset.getContentCharacters());
        assertEquals(4, dataset.getDocumentCount());
        assertEquals(DatasetPermissionType.PUBLIC, dataset.getPermissionType());
        assertEquals("errorMsg", dataset.getError().getMessage());
        assertEquals(DatasetStatus.aggregating, dataset.getStatus());
        assertEquals("http://awesome.place/analysis", dataset.getDownloadLocation());
        assertEquals("http://awesome.place/original", dataset.getOriginalDownloadLocation());

        assertEquals(2, dataset.getTagIds().size());
        assertEquals(1, dataset.getTagIds().get(0).intValue());
        assertEquals(2, dataset.getTagIds().get(1).intValue());

        assertEquals(2, dataset.getStopWords().size());
        assertEquals("fight", dataset.getStopWords().get(0).getStopWord());
        assertEquals("blabla", dataset.getStopWords().get(1).getStopWord());

        assertEquals(2, dataset.getStatusProgresses().size());
        DatasetProgress p1 = dataset.getStatusProgresses().get(DatasetStatus.aggregating);
        assertEquals(5, p1.getNumOfItems());
        assertEquals(0, p1.getCurrentItem());
        Assert.assertEquals(DatasetStatus.aggregating, p1.getStatus());
        DatasetProgress p2 = dataset.getStatusProgresses().get(DatasetStatus.uploading);
        assertEquals(5, p2.getNumOfItems());
        assertEquals(0, p2.getCurrentItem());
        Assert.assertEquals(DatasetStatus.uploading, p2.getStatus());


    }

    // ===================================================================
    // CRUD methods
    // ===================================================================

    @Test
    public void shouldQueryAndCountDatasetAll() throws Exception {

        DatasetParam param = new DatasetParam(98, -1, null, null, null, getCSP(true, "id", 0, 10));

        List<DatasetQuery> query = datasetMapper.query(param);

        assertThat(query).extracting("id").containsExactly(1000, 1001);
    }

    @Test
    public void shouldQueryAndCountDatasetAllSortDesc() throws Exception {

        DatasetParam param = new DatasetParam(98, -1, null, null, null, getCSP(true, "id", 0, 10));
        param.setSortItem("id");
        param.setSortAsc(false);

        List<DatasetQuery> query = datasetMapper.query(param);

        assertThat(query).extracting("id").containsExactly(1001, 1000);
    }

    @Test
    public void shouldQueryAndCountDatasetAllWithOffset() throws Exception {

        DatasetParam param = new DatasetParam(98, -1, null, null, null, getCSP(true, "id", 0, 10));
        param.setRows(2);
        param.setOffset(2);

        List<DatasetQuery> query = datasetMapper.query(param);

        assertEquals(0, query.size());
        param.setOffset(-1);
        param.setRows(-1);
    }

    @Test
    public void shouldQueryAndCountDatasetAllWithSearchTerm() throws Exception {

        DatasetParam param = new DatasetParam(98, -1, null, null, null, getCSP(true, "id", 0, 10));
        param.setLabel("a");

        List<DatasetQuery> query = datasetMapper.query(param);

        assertThat(query).extracting("id").containsExactly(1000);
    }

    @Test
    public void shouldQueryForNonArchived() throws Exception {

        DatasetParam param = new DatasetParam(98, -1, null, null, null, getCSP(true, "id", 0, 10));
        param.setArchived(false);

        List<DatasetQuery> query = datasetMapper.query(param);

        // Then
        assertThat(query).extracting("id").containsExactly(1001);
    }

    @Test
    public void shouldQueryForArchived() throws Exception {

        DatasetParam param = new DatasetParam(98, -1, null, null, null, getCSP(true, "id", 0, 10));
        param.setArchived(true);

        List<DatasetQuery> query = datasetMapper.query(param);

        // Then
        assertThat(query).extracting("id").containsExactly(1000);
    }

    @Test
    public void shouldQueryForBothArchivedAndNonArchived() throws Exception {

        DatasetParam param = new DatasetParam(98, -1, null, null, null, getCSP(true, "id", 0, 10));
        param.setArchived(null);

        // When
        List<DatasetQuery> query = datasetMapper.query(param);

        // Then
        assertThat(query).extracting("id").containsExactly(1000, 1001);
    }

    @Test
    public void shouldSelectLite() throws Exception {

        // Given

        // When
        List<DatasetLite> results = datasetMapper.selectLite(asList(1338), TopicType.overview);

        // Then

        assertEquals(1, results.size());
        assertEquals(1338, results.get(0).getId());
        assertEquals("e", results.get(0).getLabel());
        assertEquals(2, results.get(0).getAdoreScore());
        assertEquals(5, results.get(0).getTopicCount());
        assertEquals(4, results.get(0).getDocumentCount());
        assertEquals(0.3, results.get(0).getContribution().getPositive(), PRECISION);
        assertEquals(0.2, results.get(0).getContribution().getNeutral(), PRECISION);
        assertEquals(0.5, results.get(0).getContribution().getNegative(), PRECISION);
    }

    @Test
    public void shouldSetArchivedToTrue() throws Exception {

        // Setup
        String sql = "SELECT archived FROM bulk_upload WHERE id = 1001";

        Boolean archived = jdbcTemplate.queryForObject(sql, Boolean.class);
        assertFalse(archived);

        // When

        datasetMapper.updateArchived(Arrays.asList(1001), true);

        // Then
        archived = jdbcTemplate.queryForObject(sql, Boolean.class);
        assertTrue(archived);

    }

    @Test
    public void shouldSetArchivedToFalse() throws Exception {

        // Setup
        String sql = "SELECT archived FROM bulk_upload WHERE id = 1000";

        Boolean archived = jdbcTemplate.queryForObject(sql, Boolean.class);
        assertTrue(archived);

        // When
        datasetMapper.updateArchived(Arrays.asList(1000), false);

        // Then
        archived = jdbcTemplate.queryForObject(sql, Boolean.class);
        assertFalse(archived);
    }

    @Test
    public void shouldRenameDataset() throws Exception {

        // Given
        Map<String, Object> datasetBefore = jdbcTemplate.queryForMap("SELECT * FROM bulk_upload WHERE id = 1000");
        assertEquals("a", datasetBefore.get("label"));

        // When
        datasetMapper.rename(1000, "renamed");

        // Then
        Map<String, Object> datasetAfter = jdbcTemplate.queryForMap("SELECT * FROM bulk_upload WHERE id = 1000");
        assertEquals("renamed", datasetAfter.get("label"));

    }

    @Test
    public void shouldDeleteById() throws Exception {

        clearTables("dataset_download_details");

        // Given
        assertThat(numberOfRowsIn(DATASET)).isEqualTo(5);

        // When
        datasetMapper.delete(1002);

        // Then
        assertThat(numberOfRowsIn(DATASET)).isEqualTo(4);

    }

    @Test
    public void shouldDeleteForUserId() throws Exception {

        // Setup
        clearTables("user_topic_doc", "user_topic", "user_doc", "user_content", "stop_word", "dataset_download_details",
                "dataset_custom_charts");

        // Given
        assertThat(numberOfRowsIn(DATASET)).isEqualTo(5);

        // When
        datasetMapper.deleteForUserId(98);

        // Then
        assertThat(numberOfRowsIn(DATASET)).isEqualTo(3);

    }

    //
    @Test
    public void shouldDeleteForMultipleIds() throws Exception {

        // Setup
        clearTables("user_topic_doc", "user_topic", "user_doc", "user_content", "stop_word", "dataset_download_details",
                "dataset_custom_charts");

        // Given
        assertThat(numberOfRowsIn(DATASET)).isEqualTo(5);

        // When
        datasetMapper.deleteIds(asList(1002, 1000));

        // Then
        assertThat(numberOfRowsIn(DATASET)).isEqualTo(3);

    }

    @Test
    public void shouldGetStatusCounts() {

        // Given

        // When
        Map<String, KeyValue<String, Long>> statusCounts = datasetMapper.selectStatusCounts();

        // Then
        assertEquals(3, statusCounts.size());
        assertEquals(new Long(1), statusCounts.get(DatasetStatus.aggregating.toString()).getValue());
        assertEquals(new Long(1), statusCounts.get(DatasetStatus.uploading.toString()).getValue());
        assertEquals(new Long(3), statusCounts.get(DatasetStatus.finished.toString()).getValue());

    }

    @Test
    public void shouldIncrementExecutionId() throws Exception {

        // Given
        executeSql("UPDATE bulk_upload SET execution_id = 123 WHERE id = 1001");

        // When
        datasetMapper.incrementExecutionId(1001);

        // Then
        int executionId = getSqlInteger("SELECT execution_id FROM bulk_upload WHERE id = 1001");
        assertThat(executionId).isEqualTo(124);

    }

    @Test
    public void shouldUpdateDatasetVar() {

        // Given
        ValueAtRiskInfo valueAtRiskInfo = new ValueAtRiskInfo();
        valueAtRiskInfo.setValueAtRiskAmount(new BigDecimal(3000.0));
        valueAtRiskInfo.setValueAtRiskType(ValueAtRiskType.CUSTOMER);
        valueAtRiskInfo.setValueAtRiskWeight(ValueAtRiskWeight.HIGH);
        valueAtRiskInfo.setCustomerSpendAvgAnnual((double)1000);
        valueAtRiskInfo.setCustomerAdditionalCost((double)100);
        valueAtRiskInfo.setNumberOfCustomers(10);
        valueAtRiskInfo.setTotalYear(1);
        valueAtRiskInfo.setCurrency(VarCurrency.USD);
        // When
        datasetMapper.updateVarInfo(1000, valueAtRiskInfo);

        // Then
        Dataset dataset = datasetMapper.selectById(1000);
        ValueAtRiskInfo valueAtRiskInfo1 = datasetMapper.selectValueAtRiskInfo(1000).getValueAtRiskInfo();
        assertThat(valueAtRiskInfo1.getValueAtRiskAmount()).isEqualTo(valueAtRiskInfo.getValueAtRiskAmount());
        assertThat(dataset.getValueAtRiskInfo()).isEqualTo(valueAtRiskInfo);
        assertThat(dataset.getValueAtRiskInfo().getCurrencySymbol()).isEqualTo("$");

        valueAtRiskInfo.setCurrency(VarCurrency.CAD);
        datasetMapper.updateVarInfo(1000, valueAtRiskInfo);
        dataset = datasetMapper.selectById(1000);
        assertThat(dataset.getValueAtRiskInfo().getCurrencySymbol()).isEqualTo("CA$");
    }

    @Test
    public void updateRatingScaleMaps() throws Exception {
        // Given
        Map<Integer, Map<String, Integer>> ratingScaleMaps = Map.of(
                1, Map.of("Very Bad", 0, "Bad", 1),
                2, Map.of("Good", 2, "Very Good", 3)
        );

        // When
        datasetMapper.updateRatingScaleMaps(1000, ratingScaleMaps);

        // Then
        Dataset dataset = datasetMapper.selectById(1000);
        assertThat(dataset.getRatingScaleMaps()).isEqualTo(ratingScaleMaps);
    }

}