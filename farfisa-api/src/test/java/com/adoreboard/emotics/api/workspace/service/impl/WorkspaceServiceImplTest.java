package com.adoreboard.emotics.api.workspace.service.impl;

import com.adoreboard.emotics.api.user.service.UserValidatorService;
import com.adoreboard.emotics.api.workspace.service.WorkspaceValidatorService;
import com.adoreboard.emotics.common.enums.WorkspaceRole;
import com.adoreboard.emotics.common.mapper.OrganisationMapper;
import com.adoreboard.emotics.common.mapper.RegistrationMapper;
import com.adoreboard.emotics.common.mapper.UserMapper;
import com.adoreboard.emotics.common.mapper.WorkspaceMapper;
import com.adoreboard.emotics.common.model.User;
import com.adoreboard.emotics.common.model.organisation.Organisation;
import com.adoreboard.emotics.common.model.storyteller.InsightSettings;
import com.adoreboard.emotics.common.model.storyteller.enums.ReportCategory;
import com.adoreboard.emotics.common.model.workspace.Workspace;
import com.adoreboard.emotics.common.model.workspace.WorkspaceSetting;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.Optional;

import static org.junit.Assert.assertEquals;
import static org.mockito.BDDMockito.given;
import static org.mockito.Matchers.anyInt;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class WorkspaceServiceImplTest {

    @InjectMocks
    private WorkspaceServiceImpl workspacesService;

    @Mock
    private RegistrationMapper registrationMapper;
    @Mock
    private UserMapper userMapper;
    @Mock
    private UserValidatorService userValidatorService;
    @Mock
    private WorkspaceMapper workspaceMapper;
    @Mock
    private OrganisationMapper organisationMapper;
    @Mock
    private WorkspaceValidatorService workspaceValidatorService;

    @Test
    public void shouldAddUsersToWorkspace() {
        Organisation organisation = new Organisation();
        organisation.setOwnerId(99);
        organisation.setName("Organisation");

        Workspace workspace = new Workspace();
        workspace.setId(100);
        workspace.setAdministratorIds(new HashSet<>());
        workspace.setUserLimit(10);
        workspace.setOrganisationId(1);

        int addUserId = 2;

        User user = new User();
        user.setId(addUserId);
        user.setWorkspaceIds(new HashSet<>());

        given(userMapper.selectUserById(addUserId)).willReturn(user);
        given(workspaceMapper.selectById(anyInt())).willReturn(workspace);
        given(organisationMapper.selectById(anyInt())).willReturn(Optional.of(organisation));

        workspacesService.addUserToWorkspace(1, workspace.getId(), addUserId, WorkspaceRole.ADMIN);

        verify(workspaceMapper).updateWorkspace(workspace);
        verify(userMapper).updateWorkspaceIds(addUserId, new HashSet<>(Collections.singletonList(workspace.getId())));
    }

    @Test
    public void shouldRemoveWorkspaceToUser() {
        Workspace workspace = new Workspace();
        workspace.setId(100);
        workspace.setAdministratorIds(new HashSet<>(Arrays.asList(1, 2, 3)));

        int removeUserId = 2;

        User user = new User();
        user.setId(removeUserId);
        user.setWorkspaceIds(new HashSet<>(Arrays.asList(100, 200, 300)));

        given(userMapper.selectUserById(removeUserId)).willReturn(user);
        given(workspaceMapper.selectById(anyInt())).willReturn(workspace);

        workspacesService.removeUserFromWorkspace(1, workspace.getId(), removeUserId);

        verify(workspaceMapper).updateWorkspace(workspace);
        verify(userMapper).updateWorkspaceIds(removeUserId, new HashSet<>(Arrays.asList(200, 300)));
    }

    @Test
    public void shouldUpdateWorkspace() {
        Workspace workspace = new Workspace();
        workspace.setId(100);
        workspace.setAdministratorIds(new HashSet<>(Arrays.asList(1, 2, 3)));

        int removeUserId = 2;

        User user = new User();
        user.setId(removeUserId);
        user.setWorkspaceIds(new HashSet<>(Arrays.asList(100, 200, 300)));

        given(userMapper.selectUserById(1)).willReturn(user);
        given(workspaceMapper.selectById(anyInt())).willReturn(workspace);

        WorkspaceSetting settings = WorkspaceSetting.builder()
                .fontAwesomeIcon("Save")
                .allowToEditStopwords(true)
                .insightSettings(InsightSettings.builder().build())
                 .reportCategory(ReportCategory.customer_experience)
                .build();

        workspacesService.updateWorkspace(1, workspace.getId(), "New Label", settings);

        verify(workspaceMapper).updateWorkspace(workspace);
    }
}