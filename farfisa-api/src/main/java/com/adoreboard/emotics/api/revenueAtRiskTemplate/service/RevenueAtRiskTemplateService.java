package com.adoreboard.emotics.api.revenueAtRiskTemplate.service;

import com.adoreboard.emotics.common.model.User;
import com.adoreboard.emotics.common.model.revenueAtRisk.RevenueAtRiskTemplate;

import java.util.List;

public interface RevenueAtRiskTemplateService {

    // Workspace template operations
    List<RevenueAtRiskTemplate> getWorkspaceTemplates(User user, Integer workspaceId);

    RevenueAtRiskTemplate getTemplateById(User user, Integer templateId);

    RevenueAtRiskTemplate createWorkspaceTemplate(User user, RevenueAtRiskTemplate template);

    RevenueAtRiskTemplate updateTemplate(User user, RevenueAtRiskTemplate template);

    void deleteTemplate(User user, Integer templateId);

    // Default template operations
    RevenueAtRiskTemplate getDefaultTemplate(User user, Integer workspaceId);

    void setDefaultTemplate(User user, Integer templateId, Integer workspaceId);

    // Validation
    boolean isTemplateNameValid(Integer workspaceId, String name, Integer excludeTemplateId);
}
