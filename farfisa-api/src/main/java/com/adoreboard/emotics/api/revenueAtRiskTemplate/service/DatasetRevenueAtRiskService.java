package com.adoreboard.emotics.api.revenueAtRiskTemplate.service;

import com.adoreboard.emotics.common.model.User;
import com.adoreboard.emotics.common.model.revenueAtRisk.DatasetRevenueAtRisk;

import java.util.List;

public interface DatasetRevenueAtRiskService {

    // Dataset template operations
    List<DatasetRevenueAtRisk> getDatasetRevenueAtRisks(User user, Integer datasetId);

    DatasetRevenueAtRisk getDatasetRevenueAtRisk(User user, Integer id);

    DatasetRevenueAtRisk createDatasetRevenueAtRisk(User user, DatasetRevenueAtRisk datasetTemplate);

    DatasetRevenueAtRisk updateDatasetRevenueAtRisk(User user, DatasetRevenueAtRisk datasetTemplate);

    void deleteDatasetRevenueAtRisk(User user, Integer id);

    // Dataset creation operations
    void cloneWorkspaceTemplatesForDataset(User user, Integer datasetId, Integer workspaceId);

    Integer getSelectedRevenueAtRiskIdForDataset(User user, Integer datasetId);

    void setActiveDatasetRevenueAtRisk(User user, Integer datasetId, Integer templateId);
}
