package com.adoreboard.emotics.api.revenueAtRiskTemplate.service.impl;

import com.adoreboard.emotics.api.revenueAtRiskTemplate.service.DatasetRevenueAtRiskService;
import com.adoreboard.emotics.api.revenueAtRiskTemplate.service.RevenueAtRiskTemplateService;
import com.adoreboard.emotics.api.valueAtRisk.service.ValueAtRiskService;
import com.adoreboard.emotics.common.mapper.DatasetMapper;
import com.adoreboard.emotics.common.mapper.DatasetRevenueAtRiskMapper;
import com.adoreboard.emotics.common.model.Dataset;
import com.adoreboard.emotics.common.model.User;
import com.adoreboard.emotics.common.model.revenueAtRisk.DatasetRevenueAtRisk;
import com.adoreboard.emotics.common.model.revenueAtRisk.RevenueAtRiskTemplate;
import com.adoreboard.emotics.common.model.revenueAtRisk.ValueAtRiskInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
public class DatasetRevenueAtRiskServiceImpl implements DatasetRevenueAtRiskService {
    private final DatasetRevenueAtRiskMapper datasetRevenueAtRiskMapper;
    private final RevenueAtRiskTemplateService revenueAtRiskTemplateService;
    private final ValueAtRiskService valueAtRiskService;
    private final DatasetMapper datasetMapper;

    @Override
    public List<DatasetRevenueAtRisk> getDatasetRevenueAtRisks(User user, Integer datasetId) {
        // Validate user has access to dataset
        return datasetRevenueAtRiskMapper.getDatasetTemplates(datasetId);
    }

    @Override
    public DatasetRevenueAtRisk getDatasetRevenueAtRisk(User user, Integer id) {
        return datasetRevenueAtRiskMapper.getDatasetRevenueAtRisk(id);
    }

    @Override
    public DatasetRevenueAtRisk createDatasetRevenueAtRisk(User user, DatasetRevenueAtRisk datasetRevenueAtRisk) {
        // Validate user has editor access to dataset
        Dataset dataset = getDatasetById(datasetRevenueAtRisk.getDatasetId());

        datasetRevenueAtRisk.setCreatedAt(LocalDateTime.now());
        datasetRevenueAtRisk.setCreatedBy(user.getId());

        // Calculate Revenue At Risk for the new template
        if (datasetRevenueAtRisk.getRevenueAtRisk() != null) {
            ValueAtRiskInfo calculatedRar = valueAtRiskService.recalculateVarInfo(
                datasetRevenueAtRisk.getDatasetId(), datasetRevenueAtRisk.getRevenueAtRisk());
            datasetRevenueAtRisk.setRevenueAtRisk(calculatedRar);
        }

        datasetRevenueAtRiskMapper.createDatasetRevenueAtRisk(datasetRevenueAtRisk);
        return datasetRevenueAtRisk;
    }

    @Override
    public DatasetRevenueAtRisk updateDatasetRevenueAtRisk(User user, DatasetRevenueAtRisk datasetRevenueAtRisk) {
        // Validate user has editor access to dataset
        Dataset dataset = getDatasetById(datasetRevenueAtRisk.getDatasetId());

        // Recalculate Revenue At Risk for the updated template
        if (datasetRevenueAtRisk.getRevenueAtRisk() != null) {
            ValueAtRiskInfo calculatedRar = valueAtRiskService.recalculateVarInfo(
                datasetRevenueAtRisk.getDatasetId(), datasetRevenueAtRisk.getRevenueAtRisk());
            datasetRevenueAtRisk.setRevenueAtRisk(calculatedRar);
        }

        datasetRevenueAtRiskMapper.updateDatasetRevenueAtRisk(datasetRevenueAtRisk);
        return datasetRevenueAtRisk;
    }

    @Override
    public void deleteDatasetRevenueAtRisk(User user, Integer id) {
        // Get template to validate dataset access
        DatasetRevenueAtRisk datasetRevenueAtRisk = datasetRevenueAtRiskMapper.getDatasetRevenueAtRisk(id);
        datasetRevenueAtRiskMapper.deleteDatasetRevenueAtRisk(id);
    }

    @Override
    public void cloneWorkspaceTemplatesForDataset(User user, Integer datasetId, Integer workspaceId) {
        // Clone all workspace templates to the dataset
        datasetRevenueAtRiskMapper.cloneWorkspaceTemplatesForDataset(datasetId, workspaceId);

        // Get the default template for this workspace
        RevenueAtRiskTemplate defaultTemplate = revenueAtRiskTemplateService.getDefaultTemplate(user, workspaceId);

        if (defaultTemplate != null) {
            // Find the cloned template and set it as active
            DatasetRevenueAtRisk clonedTemplate = datasetRevenueAtRiskMapper
                .getDatasetTemplateByTemplateId(datasetId, defaultTemplate.getId());

            if (clonedTemplate != null) {
                // Calculate Revenue At Risk for the cloned template
                Dataset dataset = getDatasetById(datasetId);
                ValueAtRiskInfo calculatedRar = valueAtRiskService.recalculateVarInfo(
                    dataset.getId(), clonedTemplate.getRevenueAtRisk());

                clonedTemplate.setRevenueAtRisk(calculatedRar);
                updateDatasetRevenueAtRisk(user, clonedTemplate);

                // Set this as the selected template for the dataset
                setActiveDatasetRevenueAtRisk(user, datasetId, clonedTemplate.getId());
            }
        }
    }

    @Override
    public Integer getSelectedRevenueAtRiskIdForDataset(User user, Integer datasetId) {
        // Validate user has access to dataset
        Dataset dataset = getDatasetById(datasetId);
        return dataset != null ? dataset.getSelectedRarTemplateId() : null;
    }

    @Override
    public void setActiveDatasetRevenueAtRisk(User user, Integer datasetId, Integer templateId) {
        // Update the dataset's selected_rar_id field
        datasetMapper.updateSelectedRarId(datasetId, templateId);
    }

    // Helper method to get dataset by ID
    private Dataset getDatasetById(Integer datasetId) {
        return datasetMapper.selectById(datasetId);
    }
}
