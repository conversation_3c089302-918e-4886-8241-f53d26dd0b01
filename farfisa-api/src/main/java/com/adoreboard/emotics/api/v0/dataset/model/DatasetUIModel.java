package com.adoreboard.emotics.api.v0.dataset.model;

import com.adoreboard.emotics.api.v0.dataset.enums.DatasetUIStatus;
import com.adoreboard.emotics.common.enums.DatasetFeature;
import com.adoreboard.emotics.common.enums.DatasetPermissionType;
import com.adoreboard.emotics.common.model.DatasetProgress;
import com.adoreboard.emotics.common.model.StopWord;
import com.adoreboard.emotics.common.model.revenueAtRisk.ValueAtRiskInfo;
import com.adoreboard.emotics.common.model.analysis.AnalysisSummary;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.joda.time.DateTime;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Description:
 * @Date: 17/01/2020
 * @author: Tan Tran
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class DatasetUIModel {

    private int id;
    private int userId;
    private String label;

    private int documentCount;
    private Integer adoreScore;
    private AnalysisSummary summary;

    private DatasetUIStatus status;
    private DateTime uploadStart;
    private String originalDownloadLocation;

    private boolean archived;
    private boolean pendingChanges;
    private int executionId;
    private boolean excludeAutoTopics;
    private List<DatasetFeature> features;
    private Map<DatasetUIStatus, DatasetProgress> statusProgresses;
    private List<StopWord> stopWords;
    private List<String> metadataHeaders;
    private List<Integer> metadataColumns;
    private List<String> metadataTypes;

    private List<Integer> tagIds;

    // Dataset Permission
    private DatasetPermissionType permissionType = DatasetPermissionType.PRIVATE;
    private Set<Integer> editorIds;
    private Set<Integer> viewerIds;
    private Integer groupId;
    private String groupLabel;
    private Boolean editable = false;
    private Integer childDatasetCount;
    private Integer filterViewCount;
    private int reportCount;
    @JsonIgnore
    private ValueAtRiskInfo valueAtRiskInfo;

    public DatasetUIModel() {
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public int getDocumentCount() {
        return documentCount;
    }

    public void setDocumentCount(int documentCount) {
        this.documentCount = documentCount;
    }

    public Integer getAdoreScore() {
        return adoreScore;
    }

    public void setAdoreScore(Integer adoreScore) {
        this.adoreScore = adoreScore;
    }

    public AnalysisSummary getSummary() {
        return summary;
    }

    public void setSummary(AnalysisSummary summary) {
        this.summary = summary;
    }

    public DatasetUIStatus getStatus() {
        return status;
    }

    public void setStatus(DatasetUIStatus status) {
        this.status = status;
    }

    public DateTime getUploadStart() {
        return uploadStart;
    }

    public void setUploadStart(DateTime uploadStart) {
        this.uploadStart = uploadStart;
    }

    public String getOriginalDownloadLocation() {
        return originalDownloadLocation;
    }

    public void setOriginalDownloadLocation(String originalDownloadLocation) {
        this.originalDownloadLocation = originalDownloadLocation;
    }

    public boolean isArchived() {
        return archived;
    }

    public void setArchived(boolean archived) {
        this.archived = archived;
    }

    public boolean isPendingChanges() {
        return pendingChanges;
    }

    public void setPendingChanges(boolean pendingChanges) {
        this.pendingChanges = pendingChanges;
    }

    public int getExecutionId() {
        return executionId;
    }

    public void setExecutionId(int executionId) {
        this.executionId = executionId;
    }

    public List<DatasetFeature> getFeatures() {
        if (features == null) {
            features = new ArrayList<>();
        }
        return features;
    }

    public Map<DatasetUIStatus, DatasetProgress> getStatusProgresses() {
        return statusProgresses;
    }

    public void setStatusProgresses(Map<DatasetUIStatus, DatasetProgress> statusProgresses) {
        this.statusProgresses = statusProgresses;
    }

    public List<StopWord> getStopWords() {
        if (stopWords == null) {
            stopWords = new ArrayList<>();
        }
        return stopWords;
    }

    public List<String> getMetadataHeaders() {
        return metadataHeaders;
    }

    public void setMetadataHeaders(List<String> metadataHeaders) {
        this.metadataHeaders = metadataHeaders;
    }

    public List<Integer> getMetadataColumns() {
        return metadataColumns;
    }

    public void setMetadataColumns(List<Integer> metadataColumns) {
        this.metadataColumns = metadataColumns;
    }

    public List<String> getMetadataTypes() {
        return metadataTypes;
    }

    public void setMetadataTypes(List<String> metadataTypes) {
        this.metadataTypes = metadataTypes;
    }

    public List<Integer> getTagIds() {
        return tagIds;
    }

    public void setTagIds(List<Integer> tagIds) {
        this.tagIds = tagIds;
    }

    public boolean isExcludeAutoTopics() {
        return excludeAutoTopics;
    }

    public void setExcludeAutoTopics(boolean excludeAutoTopics) {
        this.excludeAutoTopics = excludeAutoTopics;
    }

    public DatasetPermissionType getPermissionType() {
        return permissionType;
    }

    public void setPermissionType(DatasetPermissionType permissionType) {
        this.permissionType = permissionType;
    }

    public Set<Integer> getEditorIds() {
        return editorIds;
    }

    public void setEditorIds(Set<Integer> editorIds) {
        this.editorIds = editorIds;
    }

    public Set<Integer> getViewerIds() {
        return viewerIds;
    }

    public void setViewerIds(Set<Integer> viewerIds) {
        this.viewerIds = viewerIds;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public String getGroupLabel() {
        return groupLabel;
    }

    public void setGroupLabel(String groupLabel) {
        this.groupLabel = groupLabel;
    }

    public Boolean getEditable() {
        return editable;
    }

    public void setEditable(Boolean editable) {
        this.editable = editable;
    }

    public Integer getChildDatasetCount() {
        return childDatasetCount;
    }

    public void setChildDatasetCount(Integer childDatasetCount) {
        this.childDatasetCount = childDatasetCount;
    }

    public Integer getFilterViewCount() {
        return filterViewCount;
    }

    public void setFilterViewCount(Integer filterViewCount) {
        this.filterViewCount = filterViewCount;
    }

    public int getReportCount() {
        return reportCount;
    }

    public void setReportCount(int reportCount) {
        this.reportCount = reportCount;
    }

    public void setRelativeCount(DatasetUIRelativeCount relativeCount) {
        if (relativeCount != null && this.id == relativeCount.getId()) {
            this.childDatasetCount = relativeCount.getChildDatasetCount();
            this.filterViewCount = relativeCount.getFilterViewCount();
        }
    }

    public boolean isVarCalculated() {
        return this.valueAtRiskInfo != null && this.valueAtRiskInfo.getValueAtRiskAmount() != null;
    }

    public ValueAtRiskInfo getValueAtRiskInfo() {
        return valueAtRiskInfo;
    }

    public void setValueAtRiskInfo(ValueAtRiskInfo valueAtRiskInfo) {
        this.valueAtRiskInfo = valueAtRiskInfo;
    }
}
