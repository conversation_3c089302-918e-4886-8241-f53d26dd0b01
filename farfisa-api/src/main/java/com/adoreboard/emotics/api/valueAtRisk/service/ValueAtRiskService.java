package com.adoreboard.emotics.api.valueAtRisk.service;

import com.adoreboard.emotics.common.model.revenueAtRisk.ValueAtRiskInfo;

public interface ValueAtRiskService {

    void saveDatasetVar(int datasetId);
    ValueAtRiskInfo getVarInfo(int datasetId);
    ValueAtRiskInfo setVarInfo(int datasetId, ValueAtRiskInfo info);
    ValueAtRiskInfo previewVarInfo(int datasetId, ValueAtRiskInfo info);
    ValueAtRiskInfo recalculateVarInfo(int datasetId, ValueAtRiskInfo info);
}
