package com.adoreboard.emotics.api.revenueAtRiskTemplate.controller;

import com.adoreboard.emotics.api.authentication.AuthenticationUser;
import com.adoreboard.emotics.api.revenueAtRiskTemplate.service.DatasetRevenueAtRiskService;
import com.adoreboard.emotics.api.revenueAtRiskTemplate.service.RevenueAtRiskTemplateService;
import com.adoreboard.emotics.api.valueAtRisk.service.ValueAtRiskService;
import com.adoreboard.emotics.api.workspace.service.WorkspaceValidatorService;
import com.adoreboard.emotics.common.model.User;
import com.adoreboard.emotics.common.model.revenueAtRisk.DatasetRevenueAtRisk;
import com.adoreboard.emotics.common.model.revenueAtRisk.RevenueAtRiskTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/workspaces")
public class RevenueAtRiskTemplateController {

    @Autowired
    private RevenueAtRiskTemplateService revenueAtRiskTemplateService;

    // ============================================================================
    // Workspace Template Endpoints
    // ============================================================================

    /**
     * Get all Revenue At Risk templates for a workspace
     */
    @GetMapping("/{workspaceId}/revenue-at-risk-templates")
    @PreAuthorize("@workspaceValidator.isAuthorisedViewer(#user, #workspaceId)")
    public ResponseEntity<List<RevenueAtRiskTemplate>> getWorkspaceTemplates(
            @AuthenticationUser User user,
            @PathVariable Integer workspaceId) {
        List<RevenueAtRiskTemplate> templates = revenueAtRiskTemplateService.getWorkspaceTemplates(user, workspaceId);
        return ResponseEntity.ok(templates);
    }

    /**
     * Create a new workspace Revenue At Risk template
     */
    @PostMapping("/{workspaceId}/revenue-at-risk-templates")
    @PreAuthorize("@workspaceValidator.isAuthorisedEditor(#user, #workspaceId)")
    public ResponseEntity<RevenueAtRiskTemplate> createWorkspaceTemplate(
            @AuthenticationUser User user,
            @PathVariable Integer workspaceId,
            @RequestBody RevenueAtRiskTemplate template) {

        template.setWorkspaceId(workspaceId);
        RevenueAtRiskTemplate createdTemplate = revenueAtRiskTemplateService.createWorkspaceTemplate(user, template);
        return ResponseEntity.ok(createdTemplate);
    }

    /**
     * Update a Revenue At Risk template
     */
    @PutMapping("/{workspaceId}/revenue-at-risk-templates/{templateId}")
    @PreAuthorize("@workspaceValidator.isAuthorisedEditor(#user, #workspaceId)")
    public ResponseEntity<RevenueAtRiskTemplate> updateTemplate(
            @AuthenticationUser User user,
            @PathVariable Integer workspaceId,
            @PathVariable Integer templateId,
            @RequestBody RevenueAtRiskTemplate template) {

        template.setId(templateId);
        template.setWorkspaceId(workspaceId);
        RevenueAtRiskTemplate updatedTemplate = revenueAtRiskTemplateService.updateTemplate(user, template);
        return ResponseEntity.ok(updatedTemplate);
    }

    /**
     * Delete a Revenue At Risk template
     */
    @DeleteMapping("/{workspaceId}/revenue-at-risk-templates/{templateId}")
    @PreAuthorize("@workspaceValidator.isAuthorisedEditor(#user, #workspaceId)")
    public ResponseEntity<Void> deleteTemplate(
            @AuthenticationUser User user,
            @PathVariable Integer workspaceId,
            @PathVariable Integer templateId) {
        revenueAtRiskTemplateService.deleteTemplate(user, templateId);
        return ResponseEntity.ok().build();
    }

    /**
     * Set a template as default for the workspace
     */
    @PostMapping("/{workspaceId}/revenue-at-risk-templates/{templateId}/set-default")
    @PreAuthorize("@workspaceValidator.isAuthorisedEditor(#user, #workspaceId)")
    public ResponseEntity<Void> setDefaultTemplate(
            @AuthenticationUser User user,
            @PathVariable Integer workspaceId,
            @PathVariable Integer templateId) {

        revenueAtRiskTemplateService.setDefaultTemplate(user, templateId, workspaceId);
        return ResponseEntity.ok().build();
    }
}
