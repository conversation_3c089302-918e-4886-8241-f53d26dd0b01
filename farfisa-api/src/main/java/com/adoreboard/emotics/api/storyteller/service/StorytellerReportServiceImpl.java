package com.adoreboard.emotics.api.storyteller.service;

import com.adoreboard.emotics.api.dataset.service.TopicService;
import com.adoreboard.emotics.api.storyteller.model.ReportStatus;
import com.adoreboard.emotics.api.storyteller.util.StorytellerUtils;
import com.adoreboard.emotics.api.valueAtRisk.service.ValueAtRiskService;
import com.adoreboard.emotics.common.enums.TopicType;
import com.adoreboard.emotics.common.exception.EmoticsException;
import com.adoreboard.emotics.common.mapper.DatasetMapper;
import com.adoreboard.emotics.common.mapper.StorytellerReportMapper;
import com.adoreboard.emotics.common.mapper.StorytellerInsightSlideMapper;
import com.adoreboard.emotics.common.model.Dataset;
import com.adoreboard.emotics.common.model.revenueAtRisk.ValueAtRiskInfo;
import com.adoreboard.emotics.common.model.analysis.AnalysisTopic;
import com.adoreboard.emotics.common.model.query.UserTopicQuery;
import com.adoreboard.emotics.common.model.storyteller.InsightThemeSlideData;
import com.adoreboard.emotics.common.model.storyteller.StorytellerReport;
import com.adoreboard.emotics.common.model.storyteller.StorytellerReportSettings;
import com.adoreboard.emotics.common.model.storyteller.StorytellerReportWithUser;
import com.adoreboard.emotics.common.model.storyteller.enums.TitleSelection;
import org.apache.http.HttpStatus;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class StorytellerReportServiceImpl implements StorytellerReportService {
    private static final Logger logger = LogManager.getLogger(StorytellerReportServiceImpl.class);

    @Autowired private StorytellerReportMapper storytellerReportMapper;
    @Autowired private StorytellerInsightSlideMapper storytellerInsightSlideMapper;
    @Autowired private ValueAtRiskService valueAtRiskService;
    @Autowired private TopicService topicService;
    @Autowired private DatasetMapper datasetMapper;

    @Override
    public List<StorytellerReportWithUser> getStorytellerReports(int userId, int datasetId) {
        logger.info("Retrieving storyteller reports for user {} and dataset {} ", userId, datasetId);
        return storytellerReportMapper.getReportByDatasetId(datasetId);
    }

    @Override
    public StorytellerReport getStorytellerReport(int userId, int datasetId, int reportId) {
        logger.info("Retrieving storyteller report {} for user {} and dataset {} ", reportId, userId, datasetId);
        Optional<StorytellerReport> rs = storytellerReportMapper.getReportById(datasetId, reportId);
        if (rs.isPresent()) {
            return rs.get();
        }
        throw new EmoticsException(String.format("Report ID is invalid %d", reportId), HttpStatus.SC_NOT_FOUND);
    }

    @Override
    public StorytellerReport updateStorytellerReportName(int userId, int datasetId, int reportId, String reportName) {
        logger.info("Updating storyteller report name {} - {} ", reportId, reportName);

        StorytellerReport storytellerReport = this.getStorytellerReport(userId, datasetId, reportId);
        StorytellerReportSettings reportSettings = storytellerReport.getSettings();
        reportSettings.setReportName(reportName);
        storytellerReportMapper.updateReport(reportId, storytellerReport);
        return storytellerReport;
    }

    @Override
    public ReportStatus checkReportStatus(int userId, int datasetId, int reportId) {
        ReportStatus reportStatus = new ReportStatus();
        StorytellerReport report = this.getStorytellerReport(userId, datasetId, reportId);
        //Keep dataset var info
        ValueAtRiskInfo currentDatasetVar = valueAtRiskService.getVarInfo(datasetId);
        ValueAtRiskInfo reportVar = report.getSettings().getValueAtRiskInfo();
        //Recalculate var info
        valueAtRiskService.recalculateVarInfo(datasetId, reportVar);

        UserTopicQuery query = new UserTopicQuery(Collections.singletonList(datasetId), TopicType.overview);
        query.setTopicIds(report.getThemeIds());
        query.setParentId(null);
        List<AnalysisTopic> topics = topicService.retrieveTopics(query);
        List<Integer> themeIdToRemove = report.getThemeIds().stream().filter( id -> !topics.stream().anyMatch(topicNameResponse -> topicNameResponse.getId()== id)).collect(Collectors.toList());
        List<Integer> themeNameUpdated = new ArrayList<>();
        List<Integer> themeVarUpdate = new ArrayList<>();

        report.getSettings().getInsightThemesData().forEach(insightThemeSlideData -> {
            if(!themeIdToRemove.contains(insightThemeSlideData.getTopicId())) {
                AnalysisTopic latestAnalysisTopic = topics.stream().filter(topicNameResponse -> topicNameResponse.getId() == insightThemeSlideData.getTopicId()).findFirst().get();
                String topicName = latestAnalysisTopic.getTopicLabel();
                String reportTopicName = insightThemeSlideData.getInsightsTopicsModel().getTopicLabel();
                if (!topicName.equals(reportTopicName)) {
                    logger.info("Theme {} - {} renamed to {}", insightThemeSlideData.getTopicId(), reportTopicName, topicName);
                    themeNameUpdated.add(insightThemeSlideData.getTopicId());
                }
                BigDecimal latestThemeVar = latestAnalysisTopic.getValueAtRiskAmount() != null ? latestAnalysisTopic.getValueAtRiskAmount() : BigDecimal.ZERO;
                BigDecimal reportThemeVar = insightThemeSlideData.getInsightsTopicsModel().getValueAtRiskAmount() != null ? insightThemeSlideData.getInsightsTopicsModel().getValueAtRiskAmount() : BigDecimal.ZERO;
                //compare 2 nullable value at risk
                if (latestThemeVar.compareTo(reportThemeVar) != 0) {
                    logger.info("Theme {} Value at risk amount changed from {} to {}", insightThemeSlideData.getTopicId(), reportThemeVar, latestThemeVar);
                    themeVarUpdate.add(insightThemeSlideData.getTopicId());
                }
            }
        });
        boolean changed = !themeIdToRemove.isEmpty() || !themeNameUpdated.isEmpty() || !themeVarUpdate.isEmpty();

        if(changed) {
            reportStatus.getRemovedThemeIds().addAll(themeIdToRemove);
            reportStatus.getRenamedThemeIds().addAll(themeNameUpdated);
            reportStatus.getVarChangedThemeIds().addAll(themeVarUpdate);
        }

        // compare the value at risk amount
        if (reportVar != null && currentDatasetVar != null) {
            BigDecimal reportVarAmount = reportVar.getValueAtRiskAmount() != null ? reportVar.getValueAtRiskAmount() : BigDecimal.ZERO;
            BigDecimal currentDatasetVarAmount = currentDatasetVar.getValueAtRiskAmount() != null ? currentDatasetVar.getValueAtRiskAmount() : BigDecimal.ZERO;
            if (reportVarAmount.compareTo(currentDatasetVarAmount) != 0) {
                logger.info("Value at risk amount changed from {} to {}", reportVar.getValueAtRiskAmount(), currentDatasetVar.getValueAtRiskAmount());
                reportStatus.setVarChanged(true);
            }
        }
        //reset it to current dataset var
        valueAtRiskService.recalculateVarInfo(datasetId, currentDatasetVar);

        return reportStatus;
    }

    @Override
    public List<TitleSelection> getAvailableTitleSelectors(int userId, int datasetId, int reportId) {
        Dataset dataset = datasetMapper.selectById(datasetId);
        StorytellerReport storytellerReport = this.getStorytellerReport(userId, datasetId, reportId);
        List<InsightThemeSlideData> insightThemesSlideData = storytellerReport.getSettings().getInsightThemesData();
        return StorytellerUtils.getAvailableTitleSelectors(dataset, storytellerReport, insightThemesSlideData);
    }

    @Override
    public void updateReport(int reportId, StorytellerReport storytellerReport) {
        logger.info("Updating storyteller report {} ", reportId);
        storytellerReportMapper.updateReport(reportId, storytellerReport);
    }

    @Override
    public void insertReport(StorytellerReport storytellerReport) {
        logger.info("Inserting storyteller report {} ", storytellerReport.getId());
        storytellerReportMapper.insertReport(storytellerReport);
    }

    @Override
    public void deleteReport(int userId, int datasetId, int reportId) {
        storytellerInsightSlideMapper.deleteSlides(reportId, null);
        storytellerReportMapper.deleteReport(datasetId, reportId);
    }

    @Override
    public void deleteReports(int userId, int datasetId) {
        logger.info("Removing Storyteller reports for dataset {}", datasetId);
        getStorytellerReports(userId, datasetId).forEach(report -> this.deleteReport(userId, datasetId, report.getId()));
    }
}
