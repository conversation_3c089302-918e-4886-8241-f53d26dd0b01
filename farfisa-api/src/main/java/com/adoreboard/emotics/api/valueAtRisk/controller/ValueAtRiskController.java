package com.adoreboard.emotics.api.valueAtRisk.controller;

import com.adoreboard.emotics.api.common.controller.AbstractEmoticsController;
import com.adoreboard.emotics.api.valueAtRisk.service.ValueAtRiskService;
import com.adoreboard.emotics.common.model.revenueAtRisk.ValueAtRiskInfo;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

@Controller
@ResponseBody
@RequestMapping(value = "/api/var")
public class ValueAtRiskController extends AbstractEmoticsController {

    @Autowired private ValueAtRiskService valueAtRiskService;

    @ApiOperation(value = "Get Value at Risk info for a given dataset")
    @GetMapping(value = "/{datasetId}")
    public ValueAtRiskInfo getVarInfo(@PathVariable int datasetId) {
        return valueAtRiskService.getVarInfo(datasetId);
    }

    @ApiOperation(value = "Update VAR information")
    @PostMapping(value = "/{datasetId}")
    public ValueAtRiskInfo setVarInfo(@PathVariable int datasetId, @RequestBody ValueAtRiskInfo varInfo) {
        return valueAtRiskService.setVarInfo(datasetId, varInfo);
    }

    @ApiOperation(value = "Review VAR information")
    @PostMapping(value = "/{datasetId}/preview")
    public ValueAtRiskInfo previewVarInfo(@PathVariable int datasetId, @RequestBody ValueAtRiskInfo varInfo) {
        return valueAtRiskService.previewVarInfo(datasetId, varInfo);
    }

}
