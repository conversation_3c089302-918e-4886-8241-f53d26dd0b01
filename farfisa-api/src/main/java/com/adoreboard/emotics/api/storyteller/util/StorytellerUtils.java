package com.adoreboard.emotics.api.storyteller.util;

import com.adoreboard.emotics.api.storyteller.model.CommentSettingsRequest;
import com.adoreboard.emotics.common.enums.SwotAttribute;
import com.adoreboard.emotics.common.exception.EmoticsException;
import com.adoreboard.emotics.common.model.Dataset;
import com.adoreboard.emotics.common.model.analysis.AnalysisSwot;
import com.adoreboard.emotics.common.model.enums.EightEmotionalIndex;
import com.adoreboard.emotics.common.model.storyteller.CommentSettings;
import com.adoreboard.emotics.common.model.storyteller.InsightThemeSlideData;
import com.adoreboard.emotics.common.model.storyteller.StorytellerInsightSlide;
import com.adoreboard.emotics.common.model.storyteller.StorytellerReport;
import com.adoreboard.emotics.common.model.storyteller.enums.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.adoreboard.emotics.api.storyteller.util.BusinessTerms.getAllSwotTerms;

public class StorytellerUtils {
    private static final Logger logger = LogManager.getLogger(StorytellerUtils.class);

    public static Map<Integer, String> getMetricHeaders(Dataset dataset) {
        List<String> headers = dataset.getMetadataHeaders();
        List<String> type = dataset.getMetadataTypes();
        Map<Integer, String>  result = new HashMap<>();
        for (int i = 0; i < type.size(); i++) {
            if ("SCORE".equals(type.get(i))) {
                result.put(i, headers.get(i));
            }
        }
        return result;
    }

    public static boolean hasVar(Dataset dataset) {
        // return if dataset has Value at Risk and it is greater than 0
        return dataset.getSelectedRarTemplateId() != null;
    }

    public static boolean hasMetric(Dataset dataset) {
        return dataset.getMetadataTypes().contains("SCORE");
    }

    public static String toBold(String text) {
        return String.format("<strong>%s</strong>", text);
    }

    public static String toComment(String text) {
        return  String.format("<comment>%s</comment>", text);
    }

    public static List<String> parseComment(String text) {
        Pattern pattern = Pattern.compile("<comment>(.*?)</comment>");
        Matcher matcher = pattern.matcher(text);

        // Collect the comments into a list
        List<String> comments = new ArrayList<>();
        while (matcher.find()) {
            comments.add(matcher.group(1));
        }
        return comments;
    }

    public static String toColorSpan(String text, String colorCode) {
        return  String.format("<span style=\"color: %s\">%s</span>", colorCode, text );
    }

    public static String getColorCode(boolean more, boolean isGreen) {
        if((more && isGreen) || (!more && !isGreen) ) {
            return "#62CC50";
        } else {
            return "#E30613";
        }
    }

    public static String getColorCode(SwotAttribute swot) {
        switch (swot) {
            case OPPORTUNITY: return  "#3981F7";
            case STRENGTH: return  "#62CC50";
            case THREAT: return  "#FFAB5C";
            case WEAKNESS: return "#EB534C";
            default: return "";
        }
    }

    public static String toParagraph(String text) {
        return String.format("<p>%s</p>", text);
    }

    public static String toRemoveParagraph(String text) {
        return text.replaceAll("<p>", "").replaceAll("</p>", "").trim();
    }

    public static double totalOfScoreIncreased(List<InsightThemeSlideData> insightThemesSlideData) {
        return insightThemesSlideData.stream()
                .mapToDouble(InsightThemeSlideData::getOptimiseChange)
                .map(value -> Math.round(value * 100)).sum();
    }

    public static int findThemeIndex(List<InsightThemeSlideData> insightThemeSlideData, int themeId) {
        return insightThemeSlideData.stream().mapToInt(InsightThemeSlideData::getTopicId).boxed().collect(Collectors.toList()).indexOf(themeId);
    }

    public static Optional<StorytellerInsightSlide> getSlideByType(List<StorytellerInsightSlide> storytellerInsightSlides, StorytellerSlideType slideType) {
        return storytellerInsightSlides.stream()
                .filter(slide -> slide.getSlideType() == slideType).findFirst();
    }

    public static List<StorytellerInsightSlide> getSlidesByType(List<StorytellerInsightSlide> storytellerInsightSlides, StorytellerSlideType slideType) {
        return storytellerInsightSlides.stream()
                .filter(slide -> slide.getSlideType() == slideType).collect(Collectors.toList());
    }

    public static InsightThemeSlideData findTheme(List<InsightThemeSlideData> insightThemeSlideData, int themeId) {
        return insightThemeSlideData.stream().filter(theme -> theme.getTopicId() == themeId).findFirst().orElse(null);
    }

    public static Map<String, Object> calculateEmotionPercentage(
            Dataset dataset, double themeEmoScore, int emoIndex) {

        double datasetScore = dataset.getSummary().getEmotionIndexesAvg()[emoIndex];
        double gapScore = Math.abs(themeEmoScore) - Math.abs(datasetScore);

        double percentNumb = Math.round(Math.abs(gapScore / datasetScore) * 100);
        String percent = percentNumb < 1 ? "<1" : String.valueOf((int) percentNumb);

        Map<String, Object> map = new HashMap<>();

        map.put("datasetScore", datasetScore);
        map.put("gapScore", gapScore);
        map.put("gapPct", percentNumb);
        map.put("gapPctText", percent);
        map.put("indexName", EightEmotionalIndex.nameByApiIndex(emoIndex));

        logger.info("Emotion Percentage {} ", map.toString());
        return map;
    }

    public static String calculateVolumePercentage(Dataset dataset, int numOfDocuments) {
        int rs = Math.round((float) numOfDocuments / dataset.getDocumentCount() * 100);
        return rs == 0 ? "<1" : String.valueOf(rs); // Assuming '<1' is equivalent to 1 in Java context
    }

    public static int findMaxIndex(Number[] scoreIndex, Boolean isGreen) {
        OptionalInt maxIndex = IntStream.range(0, EightEmotionalIndex.values().length)
                .filter(i -> isGreen == null || isGreen == EightEmotionalIndex.isGreen(EightEmotionalIndex.values()[i]))
                .reduce((a, b) -> Math.abs(scoreIndex[a].doubleValue()) > Math.abs(scoreIndex[b].doubleValue()) ? a : b);

        return maxIndex.orElse(-1);
    }

    public static String generateAddressKeyIssueTitle(String organisationName, Dataset dataset) {
        int keyPhrase = StorytellerUtils.findMaxIndex(dataset.getSummary().getIndexScores(), false );

        String[] titles = new String[]{
                String.format("How can %s %s", organisationName, toBold("address " + "key issues")),
                String.format("How can %s %s", organisationName, toBold("address " + "weaknesses")),
                String.format("How can %s %s", organisationName, toBold("address " + "key themes")),
                String.format("How can %s %s", organisationName, toBold("address " + "key insights")),
                String.format("How can %s %s", organisationName, toBold("address " + EightEmotionalIndex.values()[keyPhrase].titleCase())),
        };
        int randomNum = ThreadLocalRandom.current().nextInt(0, titles.length);

        return titles[randomNum];
    }

    public static String generateIncreaseEmotionTitle(String organisationName) {
        String[] positiveEmotions = new String[]{"Joy", "Trust", "Interest", "Surprise"};
        int randomNum = ThreadLocalRandom.current().nextInt(0, positiveEmotions.length);
        return String.format("How can %s %s", organisationName, StorytellerUtils.toBold("increase " + positiveEmotions[randomNum]));
    }

    public static Optional<InsightThemeSlideData> themeHasScoreIncreaseMoreThanOnePctId(List<InsightThemeSlideData> insightThemesSlideData ) {
        return insightThemesSlideData.stream().filter(data -> data.getOptimiseChange().compareTo(0.01) >= 0).findFirst();
    }

    public static List<ImplicationTitleSelection> getAvailableImplicationTitleSelectors(Dataset dataset, InsightThemeSlideData insightThemesSlideData) {
        List<ImplicationTitleSelection> titleSelections = new ArrayList<>();
        if(StorytellerUtils.hasMetric(dataset)) {
            double metricImprovement = Math.round(insightThemesSlideData.getOptimiseChange() * 100);
            logger.info("Metric Improvement {}", metricImprovement);
            if (metricImprovement >= 1) {
                titleSelections.add(ImplicationTitleSelection.metric_improvement);
            }
        }
        AnalysisSwot swot = insightThemesSlideData.getInsightsTopicsModel().getSwot();

        if(swot != null && !swot.getAttribute().equals(SwotAttribute.NONE)){
            logger.info("Swot is available for theme {}", insightThemesSlideData.getTopicId());
            titleSelections.add(ImplicationTitleSelection.swot_improvement);
        }

        titleSelections.add(ImplicationTitleSelection.emotion);
        return titleSelections;
    }

    public static Set<InsightSettingOptions> getAvailableInsightSettings(Dataset dataset) {
        Set<InsightSettingOptions> availableInsightSettings = new HashSet<>();
        if(StorytellerUtils.hasVar(dataset)) {
            availableInsightSettings.add(InsightSettingOptions.value_at_risk);
        }
        if(StorytellerUtils.hasMetric(dataset)) {
            availableInsightSettings.add(InsightSettingOptions.predict_improvement);
        }
        availableInsightSettings.add(InsightSettingOptions.swot);
        availableInsightSettings.add(InsightSettingOptions.score);
        availableInsightSettings.add(InsightSettingOptions.volume);
        return availableInsightSettings;
    }

    public static List<ImplicationSwitchGraphic> getAvailableImplicationGraphicSelectors(List<ImplicationTitleSelection> implicationTitleSelections) {
        List<ImplicationSwitchGraphic> switchGraphics = new ArrayList<>();
        if(implicationTitleSelections.contains(ImplicationTitleSelection.metric_improvement)){
            switchGraphics.add(ImplicationSwitchGraphic.metric_improvement);
        }
        if(implicationTitleSelections.contains(ImplicationTitleSelection.swot_improvement)){
            switchGraphics.add(ImplicationSwitchGraphic.swot_improvement);
        }
        switchGraphics.add(ImplicationSwitchGraphic.no_graphic);
        return switchGraphics;
    }

    public static List<TitleSelection> getAvailableTitleSelectors(Dataset dataset, StorytellerReport report, List<InsightThemeSlideData> insightThemesSlideData) {
        List<TitleSelection> titleSelections = new ArrayList<>();
        if(report.getSettings().hasVar()) {
            titleSelections.add(TitleSelection.var);
        }
        if(StorytellerUtils.hasMetric(dataset)) {
            long totalScoreIncreased = (long) StorytellerUtils.totalOfScoreIncreased(insightThemesSlideData);
            logger.info("Total Score Increased {}%", totalScoreIncreased);

            if(totalScoreIncreased >= 1) {
                titleSelections.add(TitleSelection.metric);
            }
            if(StorytellerUtils.themeHasScoreIncreaseMoreThanOnePctId(insightThemesSlideData).isPresent()){
                logger.info("Has theme Score Increase More Than One Pct");
                titleSelections.add(TitleSelection.increase);
            }
        }
        titleSelections.addAll(Arrays.asList(TitleSelection.address, TitleSelection.theme_insight, TitleSelection.number_of_themes));
        return titleSelections;
    }

    public static String evaluateGapScore(boolean moreOrLess) {
        if (moreOrLess) {
            return "more";
        } else {
            return "less";
        }
    }

    @NotNull
    public static CommentSettings getCommentSettings(Dataset dataset, CommentSettingsRequest settingsRequest) {
        try {
            int metadataIndex = settingsRequest.getMetadataIndex();
            if(metadataIndex < 0 || dataset.getMetadataHeaders().contains(metadataIndex)) {
                throw new EmoticsException("Invalid metadata index");
            }
        } catch (Exception e) {
            throw new EmoticsException("Invalid metadata index");
        }
        CommentSettings commentSettings = new CommentSettings();
        commentSettings.setMetadataIndex(settingsRequest.getMetadataIndex());
        commentSettings.setDisplayMetadata(settingsRequest.isDisplayMetadata());
        return commentSettings;
    }

    public static List<String> extractComments(String text) {
        // Define the regex pattern to match <comment>...</comment>
        Pattern pattern = Pattern.compile("<comment>([\\s\\S]*?)</comment>");
        Matcher matcher = pattern.matcher(text);

        // Initialize a list to hold the extracted comments
        List<String> comments = new ArrayList<>();

        // Find all matches and add the captured groups to the list
        while (matcher.find()) {
            comments.add(matcher.group(1));
        }

        return comments;
    }

    public static String applyDidYouKnowHighlighting(String aiText, String percentageValue, String directionWord, String emotionWord, String colorCode) {
        // If already has color tags, don't modify
        if (aiText.contains("color:") || aiText.contains("style=")) {
            return aiText;
        }

        String result = aiText;

        // Get all possible synonyms for direction word
        List<String> directionSynonyms = getDirectionSynonyms(directionWord);

        // 1. Highlight complete metric phrases first (highest priority)
        for (String synonym : directionSynonyms) {
            // Clean percentage value (remove % if present)
            String cleanPercentage = percentageValue.replace("%", "");

            String[] patterns = {
                    // "reduces apprehension by 29%" - ANY_WORD + emotion + by + percentage
                    String.format("\\b\\w+\\s+%s\\s+by\\s+%s%%", emotionWord, cleanPercentage),

                    // "apprehension by 29%" - emotion + by + percentage (standalone)
                    String.format("(?<!\\w)%s\\s+by\\s+%s%%", emotionWord, cleanPercentage),

                    // "reduces apprehension by 29%" - synonym + emotion + by + percentage
                    String.format("\\b%s\\s+%s\\s+by\\s+%s%%", synonym, emotionWord, cleanPercentage),

                    // "29% more Anger" - percentage + synonym + emotion
                    String.format("\\b%s%%\\s+%s\\s+%s\\b", cleanPercentage, synonym, emotionWord),

                    // "boost Anger 29%" - synonym + emotion + percentage
                    String.format("\\b%s\\s+%s\\s+%s%%", synonym, emotionWord, cleanPercentage),

                    // "boost 29% Anger" - synonym + percentage + emotion
                    String.format("\\b%s\\s+%s%%\\s+%s\\b", synonym, cleanPercentage, emotionWord)
            };

            for (String pattern : patterns) {
                Pattern regex = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                Matcher matcher = regex.matcher(result);

                if (matcher.find()) {
                    // Found a complete phrase match - highlight it and stop processing this synonym
                    result = matcher.replaceAll(match -> createHighlight(match.group(), colorCode));
                    break; // Stop after first match to avoid double highlighting
                }
            }
        }

        // 2. Highlight direction + emotion pairs (only if not already in a span)
        for (String synonym : directionSynonyms) {
            String pattern1 = String.format("\\b%s\\s+%s\\b", synonym, emotionWord);
            String pattern2 = String.format("\\b%s\\s+%s\\b", emotionWord, synonym);

            result = highlightIfNotInSpan(result, pattern1, colorCode, Pattern.CASE_INSENSITIVE);
            result = highlightIfNotInSpan(result, pattern2, colorCode, Pattern.CASE_INSENSITIVE);
        }

        // 3. Highlight standalone percentage (only if not already in a span)
        String percentPattern = Pattern.quote(percentageValue); // safer
        result = highlightIfNotInSpan(result, percentPattern, colorCode, Pattern.CASE_INSENSITIVE);

        // 4. Highlight standalone emotion (only if not already in a span)
        String emotionPattern = String.format("\\b%s\\b", emotionWord);
        result = highlightIfNotInSpan(result, emotionPattern, colorCode, null);

        return result;
    }

    public static String highlightIfNotInSpan(String text, String pattern, String colorCode, Integer regexFlags) {
        Pattern regex = regexFlags == null
                ? Pattern.compile(pattern)
                : Pattern.compile(pattern, regexFlags);

        StringBuilder result = new StringBuilder();
        Matcher matcher = regex.matcher(text);

        while (matcher.find()) {
            String match = matcher.group();
            String beforeMatch = text.substring(0, matcher.start());

            // Check if we're inside a span tag
            int lastOpenSpan = beforeMatch.lastIndexOf("<span");
            int lastCloseSpan = beforeMatch.lastIndexOf("</span>");

            if (lastOpenSpan <= lastCloseSpan) {
                // We're not inside a span, safe to highlight
                matcher.appendReplacement(result, createHighlight(match, colorCode));
            } else {
                // We're inside a span, don't highlight
                matcher.appendReplacement(result, match);
            }
        }
        matcher.appendTail(result);
        return result.toString();
    }

    public static String removeParensAroundPercentageIfNotInSpan(String text) {
        Pattern regex = Pattern.compile("\\((\\d+%)\\)");
        StringBuilder result = new StringBuilder();
        Matcher matcher = regex.matcher(text);

        while (matcher.find()) {
            String match = matcher.group(1);  // the percentage inside the parentheses
            String beforeMatch = text.substring(0, matcher.start());

            // Check if inside a <span>
            int lastOpenSpan = beforeMatch.lastIndexOf("<span");
            int lastCloseSpan = beforeMatch.lastIndexOf("</span>");

            if (lastOpenSpan <= lastCloseSpan) {
                // Not inside a span
                matcher.appendReplacement(result, match);
            } else {
                // Inside a span
                matcher.appendReplacement(result, matcher.group());
            }
        }
        matcher.appendTail(result);
        return result.toString();
    }

    public static List<String> getDirectionSynonyms(String directionWord) {
        return BusinessTerms.getDirectionSynonyms(directionWord);
    }

    public static String createHighlight(String text, String colorCode) {
        if(colorCode == null || colorCode.isEmpty()) {
            return toBold(text); // Fallback to bold if no color code provided
        }
        return toBold(toColorSpan(text, colorCode));
    }

    // Implication highlighting example
    /**
     * Applies SWOT-based highlighting to text, following the pattern used in generateInsightImplicationText
     * @param text The input text to highlight
     * @param swotTerms Map of SWOT terms to highlight with their corresponding SwotAttribute
     * @return The text with SWOT terms highlighted using bold and color spans
     */
    private static String applySwotHighlighting(String text, Map<String, SwotAttribute> swotTerms) {
        String result = text;

        // Sort by length descending to handle longer phrases first
        List<String> sortedTerms = swotTerms.keySet().stream()
                .sorted((a, b) -> b.length() - a.length())
                .collect(Collectors.toList());

        for (String term : sortedTerms) {
            SwotAttribute attribute = swotTerms.get(term);
            String colorCode = getColorCode(attribute);
            // Create case-insensitive pattern with word boundaries
            String pattern = String.format("\\b%s\\b", Pattern.quote(term));
            result = highlightIfNotInSpan(result, pattern, colorCode, Pattern.CASE_INSENSITIVE);
        }

        return result;
    }

    /**
     * Advanced SWOT highlighting with business context terms
     *
     * @param text The input text to highlight
     * @return The text with SWOT and business terms highlighted
     */
    public static String applyImplicationHighlighting(String text, String metricName, Long metricImprovement, String currentSwot, String potentialSwot, String varText
    ) {
        String result = text;

        // 1. Apply VAR highlighting first
        result = applyVarHighlighting(result, varText);

        // 2. Apply metric improvement highlighting (e.g., "increasing NPS by 1%")
        if (metricName != null && metricImprovement != null && metricImprovement > 0) {
            result = applyMetricImprovementHighlighting(result, metricName, metricImprovement);
        }

        // 3. Apply targeted SWOT highlighting
        Map<String, SwotAttribute> targetedSwotTerms = getTargetedSwotTerms(currentSwot, potentialSwot);
        result = applySwotHighlighting(result, targetedSwotTerms);

        return result;
    }


    /**
     * Advanced SWOT highlighting with business context terms
     *
     * @param text The input text to highlight
     * @return The text with SWOT and business terms highlighted
     */
    public static String applyInsightHighlighting(String text, String currentSwot) {
        String result = text;
        // 3. Apply targeted SWOT highlighting
        Map<String, SwotAttribute> targetedSwotTerms = getTargetedSwotTerms(currentSwot);
        result = applySwotHighlighting(result, targetedSwotTerms);
        return result;
    }

    public static String applyMetricImprovementHighlighting(String text, String metricName, long metricImprovement) {
        String result = text;
        // Patterns to match metric improvement phrases (ordered by specificity)
        String[] patterns = {
                // "potentially increasing NPS by 1%" - most specific
                String.format("\\b(potentially\\s+increasing\\s+%s\\s+by\\s+%d%%)\\b", Pattern.quote(metricName), metricImprovement),

                // "increasing NPS by 1%" - common pattern
                String.format("\\b(increasing\\s+%s\\s+by\\s+%d%%)\\b", Pattern.quote(metricName), metricImprovement),

                // "increase NPS by 1%" - base pattern
                String.format("\\b(increase\\s+%s\\s+by\\s+%d%%)\\b", Pattern.quote(metricName), metricImprovement),

                // Other action words
                String.format("\\b(boosting\\s+%s\\s+by\\s+%d%%)\\b", Pattern.quote(metricName), metricImprovement),
                String.format("\\b(improving\\s+%s\\s+by\\s+%d%%)\\b", Pattern.quote(metricName), metricImprovement),
                String.format("\\b(enhancing\\s+%s\\s+by\\s+%d%%)\\b", Pattern.quote(metricName), metricImprovement),

                // Fallback patterns
                String.format("\\b(%s\\s+by\\s+%d%%)\\b", Pattern.quote(metricName), metricImprovement), // "NPS by 1%"
                String.format("\\b(by\\s+%d%%)\\b", metricImprovement), // "by 1%"
        };

        // Try patterns in order of specificity
        for (String pattern : patterns) {
            Pattern regex = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
            Matcher matcher = regex.matcher(result);

            if (matcher.find()) {
                result = matcher.replaceAll(match -> createHighlight(match.group(1), null));
                return result; // Stop after first successful match
            }
        }

        // If no complete phrase matched, try highlighting just the metric name
        if (!result.contains("<span")) {
            String metricPattern = String.format("\\b(%s)\\b", Pattern.quote(metricName));
            result = highlightIfNotInSpan(result, metricPattern, null, Pattern.CASE_INSENSITIVE);
        }

        return result;
    }

    private static Map<String, SwotAttribute> getTargetedSwotTerms(String currentSwot, String potentialSwot) {
        Map<String, SwotAttribute> targetedTerms = new HashMap<>();
        Map<String, SwotAttribute> allSwotTerms = getAllSwotTerms();
        // Add terms for current SWOT attribute
        if (currentSwot != null && !currentSwot.isEmpty()) {
            SwotAttribute currentAttribute = SwotAttribute.valueOf(currentSwot.toUpperCase());
            addTermsForAttribute(targetedTerms, allSwotTerms, currentAttribute);
        }

        // Add terms for potential SWOT attribute (if different from current)
        if (potentialSwot != null && !potentialSwot.isEmpty() && !potentialSwot.equals(currentSwot)) {
            SwotAttribute potentialAttribute = SwotAttribute.valueOf(potentialSwot.toUpperCase());
            addTermsForAttribute(targetedTerms, allSwotTerms, potentialAttribute);
        }

        return targetedTerms;
    }

    private static Map<String, SwotAttribute> getTargetedSwotTerms(String swot) {
        Map<String, SwotAttribute> targetedTerms = new HashMap<>();
        Map<String, SwotAttribute> allSwotTerms = getAllSwotTerms();

        // Add terms for current SWOT attribute
        if (swot != null && !swot.isEmpty()) {
            SwotAttribute currentAttribute = SwotAttribute.valueOf(swot.toUpperCase());
            addTermsForAttribute(targetedTerms, allSwotTerms, currentAttribute);
        }

        return targetedTerms;
    }

    private static void addTermsForAttribute(Map<String, SwotAttribute> targetedTerms,
                                             Map<String, SwotAttribute> allSwotTerms,
                                             SwotAttribute attribute) {
        for (Map.Entry<String, SwotAttribute> entry : allSwotTerms.entrySet()) {
            if (entry.getValue() == attribute) {
                targetedTerms.put(entry.getKey(), entry.getValue());
            }
        }
    }

    private static String applyVarHighlighting(String text, String varText) {
        if (varText == null || varText.isEmpty()) {
            return text; // No VAR text to highlight
        }
        return text.replace(varText, StorytellerUtils.toBold(varText));
    }

    public static void main(String[] args) {
        // Example usage
        String aiText = "Mentions of 'Helpfulness' (5%) significantly boost Trust (57%).";
        String percentageValue = "57%";
        String directionWord = "more";
        String emotionWord = "Trust";
        String colorCode = "#62CC50";

        String highlightedText = applyDidYouKnowHighlighting(aiText, percentageValue, directionWord, emotionWord, colorCode);
        System.out.println(highlightedText);

        // Example usage
        aiText = "Mentions of 'Vitae' 4% decrease Surprise <1%. Minimal surprise impact from 'Vitae' mentions.";
        percentageValue = "<1%";
        directionWord = "less";
        emotionWord = "Surprise";
        colorCode = "#62CC50";

        highlightedText =  applyDidYouKnowHighlighting(aiText, percentageValue, directionWord, emotionWord, colorCode);
        System.out.println(highlightedText);

        // Example usage
        aiText = "Company named in 13% of responses reduces apprehension by 29%. Employees praise culture, support, and growth.";
        percentageValue = "29%";
        directionWord = "less";
        emotionWord = "Apprehension";
        colorCode = "#62CC50";

        highlightedText =  applyDidYouKnowHighlighting(aiText, percentageValue, directionWord, emotionWord, colorCode);
        System.out.println(highlightedText);

        // Test business context highlighting
        String text2 = "Market opportunities present risks, but our competitive edge helps address challenges.";
        System.out.println("\nOriginal: " + text2);
        System.out.println("Highlighted: " + applyImplicationHighlighting(text2,
                null, 0L, SwotAttribute.OPPORTUNITY.toTitleCase(), SwotAttribute.THREAT.toTitleCase(), null));

        // Test with custom terms
        Map<String, SwotAttribute> customTerms = new HashMap<>();
        customTerms.put("innovation", SwotAttribute.STRENGTH);
        customTerms.put("market disruption", SwotAttribute.THREAT);
        customTerms.put("customer loyalty", SwotAttribute.STRENGTH);

        String text3 = "Innovation and customer loyalty help us face market disruption.";
        System.out.println("\nOriginal: " + text3);
        System.out.println("Highlighted: " + applySwotHighlighting(text3, customTerms));
    }
}
