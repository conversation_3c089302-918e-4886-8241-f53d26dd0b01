package com.adoreboard.emotics.api.dataset.controller;

import com.adoreboard.emotics.api.authentication.AuthenticationUser;
import com.adoreboard.emotics.api.revenueAtRiskTemplate.service.DatasetRevenueAtRiskService;
import com.adoreboard.emotics.api.revenueAtRiskTemplate.service.RevenueAtRiskTemplateService;
import com.adoreboard.emotics.common.model.User;
import com.adoreboard.emotics.common.model.revenueAtRisk.DatasetRevenueAtRisk;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping(value = "/api/datasets")
public class DatasetRevenueAtRiskController {

    @Autowired
    private RevenueAtRiskTemplateService revenueAtRiskTemplateService;

    @Autowired
    private DatasetRevenueAtRiskService datasetRevenueAtRiskService;

    // ============================================================================
    // Dataset Template Endpoints
    // ============================================================================

    /**
     * Get all Revenue At Risk templates for a dataset
     */
    @GetMapping("/{datasetId}/revenue-at-risks")
    @PreAuthorize("@datasetValidator.isAuthorised(#user, #datasetId)")
    public ResponseEntity<List<DatasetRevenueAtRisk>> getDatasetTemplates(
            @AuthenticationUser User user,
            @PathVariable Integer datasetId) {
        List<DatasetRevenueAtRisk> datasetRevenueAtRisks = datasetRevenueAtRiskService.getDatasetRevenueAtRisks(user, datasetId);
        return ResponseEntity.ok(datasetRevenueAtRisks);
    }

    /**
     * Create a new dataset Revenue At Risk template
     */
    @PostMapping("/{datasetId}/revenue-at-risks")
    @PreAuthorize("@datasetValidator.isAuthorisedEditor(#user, #datasetId)")
    public ResponseEntity<DatasetRevenueAtRisk> createDatasetTemplate(
            @AuthenticationUser User user,
            @PathVariable Integer datasetId,
            @RequestBody DatasetRevenueAtRisk datasetRevenueAtRisk) {

        datasetRevenueAtRisk.setDatasetId(datasetId);
        DatasetRevenueAtRisk revenueAtRisk = datasetRevenueAtRiskService.createDatasetRevenueAtRisk(user, datasetRevenueAtRisk);
        return ResponseEntity.ok(revenueAtRisk);
    }

    /**
     * Update a dataset Revenue At Risk template
     */
    @PutMapping("/{datasetId}/revenue-at-risks/{rarId}")
    @PreAuthorize("@datasetValidator.isAuthorisedEditor(#user, #datasetId)")
    public ResponseEntity<DatasetRevenueAtRisk> updateDatasetTemplate(
            @AuthenticationUser User user,
            @PathVariable Integer datasetId,
            @PathVariable Integer rarId,
            @RequestBody DatasetRevenueAtRisk datasetTemplate) {

        datasetTemplate.setId(rarId);
        datasetTemplate.setDatasetId(datasetId);
        DatasetRevenueAtRisk updatedTemplate = datasetRevenueAtRiskService.updateDatasetRevenueAtRisk(user, datasetTemplate);
        return ResponseEntity.ok(updatedTemplate);
    }

    /**
     * Set a dataset template as active
     */
    @PostMapping("/{datasetId}/revenue-at-risks/{rarId}/activate")
    @PreAuthorize("@datasetValidator.isAuthorisedEditor(#user, #datasetId)")
    public ResponseEntity<Void> setActiveTemplate(
            @AuthenticationUser User user,
            @PathVariable Integer datasetId,
            @PathVariable Integer rarId) {

        datasetRevenueAtRiskService.setActiveDatasetRevenueAtRisk(user, datasetId, rarId);
        return ResponseEntity.ok().build();
    }

    /**
     * Delete a dataset Revenue At Risk template
     */
    @DeleteMapping("/{datasetId}/revenue-at-risks/{rarId}")
    @PreAuthorize("@datasetValidator.isAuthorisedEditor(#user, #datasetId)")
    public ResponseEntity<Void> deleteDatasetTemplate(
            @AuthenticationUser User user,
            @PathVariable Integer datasetId,
            @PathVariable Integer rarId) {

        datasetRevenueAtRiskService.deleteDatasetRevenueAtRisk(user, rarId);
        return ResponseEntity.ok().build();
    }
}
