package com.adoreboard.emotics.api.storyteller.service;

import com.adoreboard.emotics.api.storyteller.model.*;
import com.adoreboard.emotics.api.storyteller.util.StorytellerUtils;
import com.adoreboard.emotics.api.valueAtRisk.service.ValueAtRiskService;
import com.adoreboard.emotics.common.enums.SwotAttribute;
import com.adoreboard.emotics.common.mapper.*;
import com.adoreboard.emotics.common.model.revenueAtRisk.ValueAtRiskInfo;
import com.adoreboard.emotics.common.model.analysis.AnalysisSwot;
import com.adoreboard.emotics.common.model.enums.EightEmotionalIndex;
import com.adoreboard.emotics.common.model.insights.InsightsTopicsModel;
import com.adoreboard.emotics.common.model.organisation.Organisation;
import com.adoreboard.emotics.api.insights.service.InsightsService;
import com.adoreboard.emotics.api.v0.genesis.model.GenesisChange;
import com.adoreboard.emotics.api.v0.genesis.service.GenesisService;
import com.adoreboard.emotics.common.exception.EmoticsException;
import com.adoreboard.emotics.common.model.Dataset;
import com.adoreboard.emotics.common.model.insights.*;
import com.adoreboard.emotics.common.model.prompt.Prompt;
import com.adoreboard.emotics.common.model.storyteller.*;
import com.adoreboard.emotics.common.model.storyteller.enums.*;
import com.adoreboard.emotics.common.service.comment.CommentService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jsoup.Jsoup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.adoreboard.emotics.api.storyteller.util.StorytellerUtils.getCommentSettings;


@Service
public class StorytellerServiceImpl implements StorytellerService {

    private static final Logger logger = LogManager.getLogger(StorytellerServiceImpl.class);
    @Autowired private CommentService commentService;
    @Autowired private DatasetMapper datasetMapper;
    @Autowired private GenesisService genesisService;
    @Autowired private InsightsService insightsService;
    @Autowired private OrganisationMapper organisationMapper;
    @Autowired private StorytellerAIPromptService storytellerAIPromptService;
    @Autowired private StorytellerActionPlanService actionPlanSlideService;
    @Autowired private StorytellerInsightSlideService insightSlideService;
    @Autowired private StorytellerReportService reportService;
    @Autowired private ValueAtRiskService valueAtRiskService;

    private final Map<EightEmotionalIndex, Boolean> POS_THEME_PRIORITY_MAP = new LinkedHashMap<>() {
        {
            put(EightEmotionalIndex.trust, true);
            put(EightEmotionalIndex.joy, true);
            put(EightEmotionalIndex.apprehension, false);
            put(EightEmotionalIndex.anger, false);
            put(EightEmotionalIndex.sadness, false);
            put(EightEmotionalIndex.interest, true);
            put(EightEmotionalIndex.surprise, true);
            put(EightEmotionalIndex.disgust, false);
        }
    };

    private final Map<EightEmotionalIndex, Boolean> NEG_THEME_PRIORITY_MAP = new LinkedHashMap<>() {
        {
            put(EightEmotionalIndex.trust, false);
            put(EightEmotionalIndex.apprehension, true);
            put(EightEmotionalIndex.anger, true);
            put(EightEmotionalIndex.sadness, true);
            put(EightEmotionalIndex.joy, false);
            put(EightEmotionalIndex.interest, false);
            put(EightEmotionalIndex.surprise, false);
            put(EightEmotionalIndex.disgust, true);
        }
    };

    @Override
    public StorytellerReport generateStorytellerReport(int userId, int datasetId, StorytellerReportRequest storytellerReportRequest) {
        logger.info("Generating storyteller report for user {} and dataset {} - {} ", userId, datasetId, storytellerReportRequest);

        int reportId = this.doGenerateStorytellerSlides(userId, datasetId, storytellerReportRequest);
        return reportService.getStorytellerReport(userId, datasetId, reportId);
    }

    @Override
    public StorytellerReport getStorytellerReport(int userId, int dataset, int reportId) {
        return reportService.getStorytellerReport(userId, dataset, reportId);
    }

    @Override
    public StorytellerReport updateStorytellerReportSettings(int userId, int datasetId, int reportId, StorytellerReportSettingsRequest request) {
        logger.info("Updating storyteller report {} - {} ", reportId, request.toString());

        StorytellerReport storytellerReport = reportService.getStorytellerReport(userId, datasetId, reportId);

        boolean isUpdateMetric = false;

        if (StorytellerUtils.hasMetric(datasetMapper.selectById(datasetId))) {
            isUpdateMetric =  !Objects.equals(storytellerReport.getSettings().getInsightSettings().getPredictImprovementBy().getKey(), request.getInsightSettings().getPredictImprovementBy().getKey())
                    || !Objects.equals(storytellerReport.getSettings().getInsightSettings().getPredictImprovementBy().getValue(), request.getInsightSettings().getPredictImprovementBy().getValue());
        }

        boolean isUpdateCategory =
                !Objects.equals(storytellerReport.getSettings().getReportCategoryValue(), request.getReportCategoryValue())
                ;

        storytellerReport.getSettings().setDisplaySettings(request.getDisplaySettings());
        storytellerReport.getSettings().setReportCategory(request.getReportCategory());
        storytellerReport.getSettings().setReportCategoryExtraInfo(request.getReportCategoryExtraInfo());
        storytellerReport.getSettings().setInsightSettings(request.getInsightSettings());
        storytellerReport.getSettings().setReportName(request.getReportName());
        reportService.updateReport(reportId, storytellerReport);

        if(isUpdateMetric || isUpdateCategory) {
            doReGenerateStorytellerSlides(userId, datasetId, storytellerReport, isUpdateMetric, isUpdateCategory, false);
        }

        if(isUpdateMetric && !storytellerReport.getActionPlanThemeIds().isEmpty()){
            return actionPlanSlideService.updateActionPlanPotentialImpact(userId, datasetId, reportId);
        }
        return storytellerReport;
    }

    @Override
    public StorytellerSlide getStorytellerSlide(int userId, int dataset, int reportId, int slideId) {
       return this.getStorytellerSlides(userId, dataset, reportId).stream().filter(slide -> slide.getId() == slideId).findFirst().orElseThrow(() -> new EmoticsException("Slide not found"));
    }

    @Override
    public String generateReportTitle(int userId, int datasetId, int reportId, int slideId, TitleSelection titleSelection) {
        Dataset dataset = datasetMapper.selectById(datasetId);
        Organisation organisation = organisationMapper.selectByWorkspaceId(dataset.getWorkspaceId()).orElseGet(() -> {
            Organisation org = new Organisation();
            // Initialize org if necessary
            logger.error("Organisation is invalid for Workspace {}", dataset.getWorkspaceId());
            org.setName(dataset.getLabel());
            return org;
        });
        logger.info("Generating storyteller slide title for report {} - slide {} for user {} and dataset {} ", reportId ,slideId, userId, datasetId);

        StorytellerReport storytellerReport = reportService.getStorytellerReport(userId, datasetId, reportId);
        StorytellerInsightSlide storytellerInsightSlide = insightSlideService.getInsightSlide(userId, datasetId, reportId, slideId);
        storytellerInsightSlide.getSlideData().setSelectedTitle(titleSelection);

        String title = this.generateIntroSlideText(dataset
                , storytellerReport
                , storytellerInsightSlide
                , organisation.getName()
              );
        storytellerInsightSlide.getSlideData().setText(title);
        insightSlideService.updateSlide(reportId, slideId, storytellerInsightSlide);
        return title;
    }

    @Override
    public StorytellerInsightSlide generateDidYouKnowSlideWithEmotionIndex(int id, int datasetId, int reportId, int slideId, EightEmotionalIndex selectedEmotion) {
        Dataset dataset = datasetMapper.selectById(datasetId);
        StorytellerReport storytellerReport = reportService.getStorytellerReport(id, datasetId, reportId);
        StorytellerInsightSlide storytellerInsightSlide = insightSlideService.getInsightSlide(id, datasetId, reportId, slideId);
        generateDidYouKnowSlideWithEmotion(dataset, storytellerReport, storytellerInsightSlide, selectedEmotion);
        insightSlideService.updateSlide(reportId, slideId, storytellerInsightSlide);
        return storytellerInsightSlide;
    }

    @Override
    public void deleteReport(int userId, int datasetId, int reportId) {
        reportService.deleteReport(userId, datasetId, reportId);
    }

    @Override
    public void deleteReports(int userId, int datasetId) {
        reportService.deleteReports(userId, datasetId);
    }

    @Override
    public void deleteSlide(int userId, int datasetId, int reportId, int slideId) {
        insightSlideService.deleteSlide(userId, datasetId, reportId, slideId);
    }

    @Override
    public ReportStatus checkReportStatus(int userId, int datasetId, int reportId) {
        return reportService.checkReportStatus(userId, datasetId, reportId);
    }

    @Override
    public List<String> invokeDidYouKnowResponses(int userId, int datasetId, int reportId, int slideId, Prompt customPrompt) {

        StorytellerReport storytellerReport = reportService.getStorytellerReport(userId, datasetId, reportId);
        StorytellerInsightSlide storytellerInsightSlide = insightSlideService.getInsightSlide(userId, datasetId, reportId, slideId);

        if(storytellerInsightSlide.getSlideType() != StorytellerSlideType.DID_YOU_KNOW){
            throw new EmoticsException("Wrong slide type");
        }
        return storytellerAIPromptService.invokeDidYouKnowResponses(
                  storytellerReport.getSettings().getReportCategoryValue()
                , StorytellerUtils.toRemoveParagraph(storytellerInsightSlide.getSlideData().getText())
                , new ArrayList<>(Objects.requireNonNull(commentService.getTopComments(datasetId, storytellerInsightSlide.getSlideData().getThemeId(), storytellerInsightSlide.getSlideData().getSelectedEmotion(), false, 3)).values())
                , 3
                , customPrompt.getInput());
    }

    @Override
    public List<String> invokeImplicationResponses(int userId, int datasetId, int reportId, int slideId, Prompt customPrompt) {
        StorytellerReport storytellerReport = reportService.getStorytellerReport(userId, datasetId, reportId);
        StorytellerInsightSlide storytellerInsightSlide = insightSlideService.getInsightSlide(userId, datasetId, reportId, slideId);
        if(storytellerInsightSlide.getSlideType() != StorytellerSlideType.IMPLICATION){
            throw new EmoticsException("Wrong slide type");
        }
        return storytellerAIPromptService.invokeImplicationResponses(
                  storytellerReport.getSettings().getReportCategoryValue()
                , StorytellerUtils.toRemoveParagraph(storytellerInsightSlide.getSlideData().getText())
                , new ArrayList<>(Objects.requireNonNull(commentService.getTopComments(datasetId, storytellerInsightSlide.getSlideData().getThemeId(), null, false, 3)).values())
                , 3
                , customPrompt.getInput());
    }

    @Override
    public List<String> invokeDecisionReadyInsightResponses(int userId, int datasetId, int reportId, int slideId, Prompt customPrompt) {
        StorytellerReport storytellerReport = reportService.getStorytellerReport(userId, datasetId, reportId);
        StorytellerInsightSlide storytellerInsightSlide = insightSlideService.getInsightSlide(userId, datasetId, reportId, slideId);
        if(storytellerInsightSlide.getSlideType() != StorytellerSlideType.INSIGHT){
            throw new EmoticsException("Wrong slide type");
        }
        List<StorytellerInsightSlide> slides = insightSlideService.getInsightSlidesByReportAndThemeId(reportId, storytellerInsightSlide.getSlideData().getThemeId());
        StorytellerInsightSlide didYouKnowSlide = StorytellerUtils.getSlideByType(slides, StorytellerSlideType.DID_YOU_KNOW).orElseThrow(() -> new EmoticsException("Did you know slide not found"));
        StorytellerInsightSlide implicationSlide = StorytellerUtils.getSlideByType(slides, StorytellerSlideType.IMPLICATION).orElseThrow(() -> new EmoticsException("Implication slide not found"));

        return storytellerAIPromptService.invokeDecisionReadyInsightResponses(
                storytellerReport.getSettings().getReportCategoryValue()
                , StorytellerUtils.toRemoveParagraph(storytellerInsightSlide.getSlideData().getText())
                , didYouKnowSlide.getSlideData().getText()
                , implicationSlide.getSlideData().getText()
                , 3
                , customPrompt.getInput());
    }

    @Override
    public List<String> invokeAIResponses(int userId, int datasetId, int reportId, int slideId, Prompt customPrompt) {
        StorytellerInsightSlide storytellerInsightSlide = insightSlideService.getInsightSlide(userId, datasetId, reportId, slideId);
        switch (storytellerInsightSlide.getSlideType()) {
            case DID_YOU_KNOW:
                return this.invokeDidYouKnowResponses(userId, datasetId, reportId, slideId, customPrompt);
            case IMPLICATION:
                return this.invokeImplicationResponses(userId, datasetId, reportId, slideId, customPrompt);
            case INSIGHT:
                return this.invokeDecisionReadyInsightResponses(userId, datasetId, reportId, slideId, customPrompt);
            default:
                throw new EmoticsException("Not supported yet");
        }
    }

    @Override
    public StorytellerInsightSlide updateSlide(int userId, int datasetId, int reportId, int slideId, StorytellerSlideRequest slideText) {
        return insightSlideService.updateSlide(userId, datasetId, reportId, slideId, slideText);
    }

    @Override
    public StorytellerInsightSlide updateImplicationSlideSettings(int userId, int datasetId, int reportId, int slideId, ImplicationSlideDataRequest request) {
        logger.info("Start updating Storyteller ImplicationSlideSettings Report {} - Slide {}", reportId, slideId);
        StorytellerReport storytellerReport = reportService.getStorytellerReport(userId, datasetId, reportId);
        StorytellerInsightSlide storytellerInsightSlide = insightSlideService.getInsightSlide(userId, datasetId, reportId, slideId);
        if(storytellerInsightSlide.getSlideType() == StorytellerSlideType.IMPLICATION) {
            if(request.getSelectedTitle() != null && storytellerInsightSlide.getSlideData().getImplicationSlideData().getSelectedTitle() != request.getSelectedTitle()) {
                //if change title selection, regenerate the content
                int themeIndex = StorytellerUtils.findThemeIndex(storytellerReport.getSettings().getInsightThemesData(), storytellerInsightSlide.getSlideData().getThemeId());
                storytellerInsightSlide.getSlideData().getImplicationSlideData().setSelectedTitle(request.getSelectedTitle());
                storytellerInsightSlide.getSlideData().setText(generateInsightImplicationText(
                          datasetMapper.selectById(datasetId)
                        , storytellerReport
                        , storytellerInsightSlide
                        , storytellerReport.getSettings().getInsightThemesData().get(themeIndex)
                        ));
            }
            if(request.getSelectedGraphic() != null) {
                storytellerInsightSlide.getSlideData().getImplicationSlideData().setSelectedGraphic(request.getSelectedGraphic());
            }
        } else {
            throw new EmoticsException("Not supported yet");
        }
        insightSlideService.updateSlide(reportId, slideId, storytellerInsightSlide);
        return storytellerInsightSlide;
    }

    @Override
    public StorytellerReport updateCommentSettings(int userId, int datasetId, int reportId, CommentSettingsRequest settingsRequest) {
        logger.info("Start updating Storyteller CommentSettings Report {} - {}", reportId, settingsRequest);
        Dataset dataset = datasetMapper.selectById(datasetId);
        StorytellerReport storytellerReport = reportService.getStorytellerReport(userId, datasetId, reportId);
        CommentSettings commentSettings = getCommentSettings(dataset, settingsRequest);
        storytellerReport.getSettings().setCommentSettings(commentSettings);
        reportService.updateReport(reportId, storytellerReport);
        if(commentSettings.isDisplayMetadata()) {
            insightSlideService.updateMetadataForCommentSlides(userId, datasetId, reportId);
        }
        return storytellerReport;
    }

    @Override
    public StorytellerInsightSlide updateSelectedComments(int userId, int datasetId, int reportId, int slideId, Set<Integer> commentIds) {
        return insightSlideService.updateSelectedComments(userId, datasetId, reportId, slideId, commentIds);
    }

    @Override
    public StorytellerInsightSlide revertSlide(int id, int datasetId, int reportId, int slideId) {
        return insightSlideService.revertSlide(id, datasetId, reportId, slideId);
    }

    @Override
    public StorytellerReport updateSelectedThemes(int userId, int datasetId, int reportId, List<Integer> themeIds) {
        ReportStatus reportStatus = checkReportStatus(userId, datasetId, reportId);
        StorytellerReport storytellerReport = getStorytellerReport(userId, datasetId, reportId);
        return this.updateSelectedThemes(userId, datasetId, storytellerReport, themeIds, reportStatus);
    }

    private StorytellerReport updateSelectedThemes(int userId, int datasetId, StorytellerReport storytellerReport, List<Integer> themeIds, ReportStatus reportStatus) {
        int reportId = storytellerReport.getId();
        logger.info("Start generating Storyteller Report {} - with themes {}", reportId, themeIds);

        Dataset dataset = datasetMapper.selectById(datasetId);
        Organisation organisation = organisationMapper.selectByWorkspaceId(dataset.getWorkspaceId()).orElseGet(() -> {
            Organisation org = new Organisation();
            // Initialize org if necessary
            logger.error("Organisation is invalid for Workspace {}", dataset.getWorkspaceId());
            org.setName(dataset.getLabel());
            return org;
        });
        List<Integer> themeIdToRemove = storytellerReport.getThemeIds().stream().filter( id -> !themeIds.contains(id)).collect(Collectors.toList());
        logger.info("Removing themes {} for Storyteller Report {}", themeIdToRemove, reportId);
        storytellerReport.setThemeIds(insightsService.getTopics(datasetId, new ArrayList<>(themeIds)).stream().mapToInt(InsightsTopicsModel::getId).boxed().collect(Collectors.toList()));
        List<InsightThemeSlideData> insightThemesSlideData = this.generateThemeSlideData(dataset, storytellerReport.getThemeIds(), storytellerReport.getSettings().getInsightSettings().getPredictImprovementBy());
        storytellerReport.getSettings().setInsightThemesData(insightThemesSlideData);
        reportService.updateReport(reportId, storytellerReport);

        themeIdToRemove.forEach( id -> {
            final List<StorytellerInsightSlide> toRemove = insightSlideService.getInsightSlidesByReportAndThemeId(reportId, id);
            insightSlideService.deleteSlides(reportId, toRemove.stream().mapToInt(StorytellerInsightSlide::getId).boxed().collect(Collectors.toList()));
        });

        final List<StorytellerInsightSlide> storytellerInsightSlides = insightSlideService.getInsightSlides(userId, datasetId, reportId);

        if(storytellerReport.getSettings().hasMetric()
                || storytellerReport.getSettings().hasVar()) {
            StorytellerUtils.getSlideByType(storytellerInsightSlides, StorytellerSlideType.INTRO)
                    .ifPresent(
                            coverSlide -> {
                                logger.info("Re-generating Cover slide {} for Storyteller Report {}", coverSlide.getId(), coverSlide.getId());
                                StorytellerInsightSlide updateSlide = regenerateIntroSlide(userId, dataset, storytellerReport, coverSlide, organisation.getName());
                                insightSlideService.updateSlide(storytellerReport.getId(), coverSlide.getId(), updateSlide);
                            }
                    );
        }

        StorytellerUtils.getSlideByType(storytellerInsightSlides, StorytellerSlideType.THEMES_INSIGHTS)
                .ifPresent(
                        scorecardSlide -> {
                            logger.info("Re-generating Themes Insights slide {} for Storyteller Report {}", scorecardSlide.getId(), scorecardSlide.getId());
                            StorytellerInsightSlide updateSlide = regenerateThemesInsightsSlide(dataset, storytellerReport, scorecardSlide);
                            insightSlideService.updateSlide(storytellerReport.getId(), scorecardSlide.getId(), updateSlide);
                        }
                );

        AtomicInteger nextSlideOrder = new AtomicInteger(4);
        for (int i = 0; i < insightThemesSlideData.size(); i++) {
            InsightThemeSlideData theme = insightThemesSlideData.get(i);
            List<StorytellerInsightSlide> slides = insightSlideService.getInsightSlidesByReportAndThemeId(reportId, theme.getTopicId());
            logger.info("Re-generating slides for Theme {}" , theme.getTopicId());
            if(reportStatus.getRenamedThemeIds().contains(theme.getTopicId())) {
                logger.info("Re-generating renamed slides for Theme {} - {}" , theme.getTopicId(), theme.getInsightsTopicsModel().getTopicLabel());
                AtomicReference<StorytellerInsightSlide> insightSlide = new AtomicReference<>(new StorytellerInsightSlide());
                AtomicReference<String> didYouKnowText = new AtomicReference<>();
                AtomicReference<String> implicationText = new AtomicReference<>();
                slides.forEach( slide -> {
                    regenerateSlide(userId, dataset, storytellerReport, slide, organisation.getName());

                    switch (slide.getSlideType()) {
                        case INSIGHT:
                            insightSlide.set(slide);
                            break;
                        case DID_YOU_KNOW:
                            didYouKnowText.set(slide.getSlideData().getText());
                            break;
                        case IMPLICATION:
                            implicationText.set(slide.getSlideData().getText());
                            break;
                        default:
                            // do nothing
                    }
                });

               // Re-generate insight slide with AI
                insightSlide.get().getSlideData().setText(
                        this.generateInsightText(
                          storytellerReport.getSettings().getReportCategoryValue()
                        , theme.getInsightsTopicsModel().getTopicLabel()
                        , didYouKnowText.get()
                        , implicationText.get()));

                insightSlideService.updateSlide(storytellerReport.getId(), insightSlide.get().getId(), insightSlide.get());
            }

            if(!slides.isEmpty()) {
                slides.forEach( slide -> {
                    int themeIndex = StorytellerUtils.findThemeIndex(insightThemesSlideData, theme.getTopicId());
                    logger.info("Update {} slide {} for Storyteller Report {}", slide.getSlideType(), slide.getId(),  reportId);
                    StorytellerInsightSlide updateSlide = this.buildThemeSlideDataWithThemeIndex(slide, themeIndex, nextSlideOrder.getAndIncrement());
                    if(reportStatus.isVarChanged()
                            || reportStatus.getVarChangedThemeIds().contains(theme.getTopicId())) {
                        //update Implication Slide
                        if(slide.getSlideType() == StorytellerSlideType.IMPLICATION
                        && slide.getSlideData().getImplicationSlideData().getSelectedTitle() == ImplicationTitleSelection.metric_improvement) {
                            updateSlide.getSlideData().setText(generateInsightImplicationText(
                                      dataset
                                    , storytellerReport
                                    , updateSlide
                                    , storytellerReport.getSettings().getInsightThemesData().get(themeIndex)
                                    ));
                        }
                    }
                    insightSlideService.updateSlide(storytellerReport.getId(), slide.getId(), updateSlide);
                });
            }
            else {
                int insightSlideOrder = nextSlideOrder.getAndIncrement();

                StorytellerInsightSlide didYouKnowSlide = this.generateDidYouKnowSlide(dataset, storytellerReport, new StorytellerInsightSlide(storytellerReport.getId(), nextSlideOrder.getAndIncrement()), i);

                StorytellerInsightSlide implicationSlide = this.generateImplicationSlide(dataset, storytellerReport,  new StorytellerInsightSlide(storytellerReport.getId(), nextSlideOrder.getAndIncrement(),
                        StorytellerUtils.getAvailableImplicationTitleSelectors(dataset, insightThemesSlideData.get(i)).get(0)), i);

                StorytellerInsightSlide commentsSlide = this.generateCommentsSlide(dataset, storytellerReport, new StorytellerInsightSlide(storytellerReport.getId(), nextSlideOrder.getAndIncrement()), i);

                // Re-generate insight slide with AI
                StorytellerInsightSlide insightSlide =  this.generateInsightSlide(dataset, storytellerReport, new StorytellerInsightSlide(storytellerReport.getId(),insightSlideOrder ), i);
                insightSlide.getSlideData().setText(this.generateInsightText(
                          storytellerReport.getSettings().getReportCategoryValue()
                        , insightThemesSlideData.get(i).getInsightsTopicsModel().getTopicLabel()
                        , didYouKnowSlide.getSlideData().getText()
                        , implicationSlide.getSlideData().getText()));


                insightSlideService.upsertSlide(insightSlide);
                insightSlideService.upsertSlide(didYouKnowSlide);
                insightSlideService.upsertSlide(implicationSlide);
                insightSlideService.upsertSlide(commentsSlide);
            }
            logger.info("End-generating slides for Theme {}" , theme.getTopicId());
        }

        actionPlanSlideService.reorderActionPlanSlides(userId, datasetId, reportId);

        //save latest state
        reportService.updateReport(reportId, storytellerReport);
        logger.info("End re-generating Storyteller Report dataset {} -> report id {}", datasetId, storytellerReport.getId());
        return storytellerReport;
    }

    @Override
    public StorytellerReport refreshReport(int userId, int datasetId, int reportId) {
        logger.info("Start refreshing Storyteller Report {} - {}", datasetId, reportId);
        StorytellerReport storytellerReport = reportService.getStorytellerReport(userId, datasetId, reportId);
        ReportStatus status = reportService.checkReportStatus(userId, datasetId, reportId);

        ValueAtRiskInfo varInfo = valueAtRiskService.getVarInfo(datasetId);
        storytellerReport.getSettings().setValueAtRiskInfo(varInfo);
        if(varInfo != null) {
            storytellerReport.getSettings().getAvailableInsightSettings().add(InsightSettingOptions.value_at_risk);
        }
        //reportService.updateReport(reportId, storytellerReport);
        if(status.isThemeChanged()) {
            List<Integer> themeIds= storytellerReport.getThemeIds().stream().filter(integer -> !status.getRemovedThemeIds().contains(integer)).collect(Collectors.toList());
            this.updateSelectedThemes(userId, datasetId, storytellerReport,themeIds, status);
        } else if(status.isVarChanged()) {
            doReGenerateStorytellerSlides(userId, datasetId, storytellerReport, false, false, true);
        }
        if(storytellerReport.getActionPlanThemeIds().size() > 0){
            return actionPlanSlideService.refreshActionPlan(userId, datasetId, reportId, status);
        }

        return reportService.getStorytellerReport(userId, datasetId, reportId);
    }

    @Override
    public StorytellerReport reorderSelectedThemes(int userId, int datasetId, int reportId, List<Integer> themeIds) {
        StorytellerReport storytellerReport = reportService.getStorytellerReport(userId, datasetId, reportId);
        logger.info("Reorder theme ids From {} - To {}", storytellerReport.getThemeIds(), themeIds);

        if(!new HashSet<>(storytellerReport.getThemeIds()).containsAll(themeIds)) {
            throw new EmoticsException("Invalid theme ids");
        }

        // Reorder the data based on reorderedThemeIds

        List<InsightThemeSlideData> originalThemesData = new ArrayList<>(storytellerReport.getSettings().getInsightThemesData());

        storytellerReport.getSettings().setInsightThemesData(
                themeIds.stream()
                        .map(id -> originalThemesData.get(storytellerReport.getThemeIds().indexOf(id)))
                        .collect(Collectors.toList())
        );
        storytellerReport.setThemeIds(new ArrayList<>(themeIds));

        reportService.updateReport(reportId, storytellerReport);

        List<StorytellerInsightSlide> storytellerInsightSlides = insightSlideService.getInsightSlides(userId, datasetId, reportId);

        storytellerInsightSlides.stream().filter(slide -> slide.getSlideType() == StorytellerSlideType.THEMES_INSIGHTS).findFirst().ifPresent(slide -> {
            slide.getSlideData().setInsightThemesData(storytellerReport.getSettings().getInsightThemesData());
            insightSlideService.updateSlide(reportId, slide.getId(), slide);
        });

        AtomicInteger nextSlideOrder = new AtomicInteger(4);
        themeIds.stream()
                .map(id -> storytellerInsightSlides.stream()
                        .filter(slide -> slide.getSlideType() != StorytellerSlideType.INTRO && slide.getSlideType() != StorytellerSlideType.THEMES_INSIGHTS && Objects.equals(slide.getSlideData().getThemeId(), id))
                        .collect(Collectors.toList()))
                .flatMap(List::stream)
                .forEach(slide -> {
                   // logger.warn("Reorder slide {} - {} - {}", slide.getId(), slide.getSlideOrder(),  slide.getSlideData().getAlias());
                    slide.setSlideOrder(nextSlideOrder.getAndIncrement());
                    slide.getSlideData().setAlias(buildThemeSlideDataWithThemeIndex(slide, themeIds.indexOf(slide.getSlideData().getThemeId()), slide.getSlideOrder()).getSlideData().getAlias());
                    //logger.warn("To {} - {} - {}", slide.getId(), slide.getSlideOrder(),  slide.getSlideData().getAlias());
                    insightSlideService.updateSlide(reportId, slide.getId(), slide);
                });

        return storytellerReport;
    }

    @Override
    public StorytellerReport updateStorytellerReportName(int userId, int datasetId, int reportId, String label) {
        return reportService.updateStorytellerReportName(userId, datasetId, reportId, label);
    }

    @Override
    public List<ImplicationTitleSelection> getAvailableImplicationTitleSelectors(int userId, int datasetId, int reportId, int slideId) {

        Dataset dataset = datasetMapper.selectById(datasetId);
        StorytellerReport storytellerReport = reportService.getStorytellerReport(userId, datasetId, reportId);
        StorytellerInsightSlide storytellerInsightSlide = insightSlideService.getInsightSlide(userId, datasetId, reportId, slideId);

        Optional<InsightThemeSlideData> insightThemeSlideData = storytellerReport.getSettings().getInsightThemesData().stream().filter(insightTheme -> insightTheme.getTopicId() == storytellerInsightSlide.getSlideData().getThemeId()).findFirst();

        if(insightThemeSlideData.isEmpty()) {
            throw new EmoticsException("Theme id not selected in this report");
        }

        return StorytellerUtils.getAvailableImplicationTitleSelectors(dataset, insightThemeSlideData.get());
    }

    @Override
    public List<StorytellerReportWithUser> getStorytellerReports(int userId, int dataset) {
        return reportService.getStorytellerReports(userId, dataset);
    }

    @Override
    public List<StorytellerSlide> getStorytellerSlides(int userId, int datasetId, int reportId) {
        List<StorytellerSlide> slides = new ArrayList<>();
        List<StorytellerInsightSlide> insightSlides = insightSlideService.getInsightSlides(userId, datasetId, reportId);
        List<StorytellerActionPlanSlide> actionPlanSlides = actionPlanSlideService.getActionPlanSlides(userId, datasetId, reportId);
        slides.addAll(insightSlides);
        slides.addAll(actionPlanSlides);

        logger.info("Retrieving storyteller slides of report {} for user {} and dataset {} ", reportId, userId, datasetId);
        return slides;
    }

    @Override
    public List<TitleSelection> getAvailableTitleSelectors(int userId, int datasetId, int reportId) {
        return reportService.getAvailableTitleSelectors(userId, datasetId, reportId);
    }

    private void doReGenerateStorytellerSlides(int userId, int datasetId, StorytellerReport storytellerReport, boolean isUpdateIntroSlide, boolean isUpdateThemeInsightsSlide, boolean isUpdateInsightSlide, boolean isUpdateDidyouknowSlide, boolean isUpdateImplicationSlide, boolean isUpdateMetric, boolean isUpdateVar) {

        Dataset dataset = datasetMapper.selectById(datasetId);

        int reportId = storytellerReport.getId();
        List<StorytellerInsightSlide> storytellerInsightSlides = insightSlideService.getInsightSlides(userId, datasetId, reportId);

        if(isUpdateThemeInsightsSlide) {
            List<InsightThemeSlideData> insightThemesSlideData = generateThemeSlideData(
                    dataset,
                    storytellerReport.getThemeIds(),
                    storytellerReport.getSettings().getInsightSettings().getPredictImprovementBy()
            );
            storytellerReport.getSettings().setInsightThemesData(insightThemesSlideData);
            logger.info("Update ThemeInsights Slide");
        }

        Map<Integer, StorytellerInsightSlide> insightSlide = new HashMap<>();
        Map<Integer, String> didYouKnowText = new HashMap<>();
        Map<Integer, String> implicationText = new HashMap<>();

        storytellerInsightSlides.forEach(slide -> {
            switch (slide.getSlideType()) {
                //INTRO , THEME_INSIGHTS only re-generate when update metric
                case INTRO:
                    if(isUpdateIntroSlide) {
                        if((isUpdateMetric && TitleSelection.metric == slide.getSlideData().getSelectedTitle())
                            || (isUpdateVar && TitleSelection.var == slide.getSlideData().getSelectedTitle())) {
                            regenerateSlide(userId, dataset, storytellerReport, slide, this.introSlideTitle(dataset));
                        }
                    }
                    break;
                case THEMES_INSIGHTS: {
                    if(isUpdateThemeInsightsSlide) {
                        regenerateSlide(userId, dataset, storytellerReport, slide, this.introSlideTitle(dataset));
                    }
                    break;
                }
                case INSIGHT:{
                    if(isUpdateInsightSlide) {
                        regenerateSlide(userId, dataset, storytellerReport, slide, this.introSlideTitle(dataset));
                    }
                    insightSlide.put(slide.getSlideData().getThemeId(), slide);
                    break;
                }
                case DID_YOU_KNOW: {
                    if (isUpdateDidyouknowSlide) {
                        regenerateSlide(userId, dataset, storytellerReport, slide, this.introSlideTitle(dataset));
                    }
                    didYouKnowText.put(slide.getSlideData().getThemeId(), slide.getSlideData().getText());
                    break;
                }
                case IMPLICATION:
                {
                    if(isUpdateImplicationSlide
                           || (isUpdateMetric
                                    && ImplicationTitleSelection.metric_improvement == slide.getSlideData().getImplicationSlideData().getSelectedTitle())
                    ) {
                        regenerateSlide(userId, dataset, storytellerReport, slide, this.introSlideTitle(dataset));
                    }
                    implicationText.put(slide.getSlideData().getThemeId(), slide.getSlideData().getText());
                    break;
                }
                default:
                    // do nothing
            }
        });
        // Re-generate insight slide with AI

        if(isUpdateInsightSlide){
            for (Map.Entry<Integer, StorytellerInsightSlide> entry : insightSlide.entrySet()) {
                StorytellerInsightSlide slide = entry.getValue();
                slide.getSlideData().setText(
                        this.generateInsightText(
                                storytellerReport.getSettings().getReportCategoryValue(),
                                StorytellerUtils.findTheme(storytellerReport.getSettings().getInsightThemesData(), slide.getSlideData().getThemeId()).getInsightsTopicsModel().getTopicLabel(),
                                didYouKnowText.get(entry.getKey()),
                                implicationText.get(entry.getKey())
                        )
                );
                insightSlideService.updateSlide(storytellerReport.getId(), slide.getId(), slide);
            }
        }
        reportService.updateReport(reportId, storytellerReport);
        logger.info("End re-generating Storyteller Report dataset {} -> report id {}", datasetId, storytellerReport.getId());
    }

    //Private
    private void doReGenerateStorytellerSlides(int userId, int datasetId, StorytellerReport storytellerReport, boolean isUpdateMetric, boolean isUpdateReportType, boolean isUpdateVar) {
        boolean isUpdateIntroSlide = false, isUpdateThemeInsightsSlide = false, isUpdateInsightSlide = false, isUpdateDidyouknowSlide = false, isUpdateImplicationSlide = false;
        if(isUpdateMetric || isUpdateVar) {
            isUpdateIntroSlide = true;
            isUpdateThemeInsightsSlide = true;
            isUpdateImplicationSlide = true;
        }
        if(isUpdateReportType) {
            isUpdateImplicationSlide = true;
            isUpdateInsightSlide = true;
            isUpdateDidyouknowSlide = true;
        }

        this.doReGenerateStorytellerSlides(userId, datasetId, storytellerReport, isUpdateIntroSlide, isUpdateThemeInsightsSlide, isUpdateInsightSlide, isUpdateDidyouknowSlide, isUpdateImplicationSlide, isUpdateMetric, isUpdateVar);
    }

    private void doGenerateStorytellerSlides(int userId, Dataset dataset, StorytellerReport storytellerReport) {
        List<InsightThemeSlideData> insightThemesSlideData = storytellerReport.getSettings().getInsightThemesData();

        if(storytellerReport.getId() == null ) {
           throw new EmoticsException(String.format("Storyteller report  for dataset %s insert failed", dataset.getId()));
        }

        StorytellerInsightSlide coverSlide = this.generateIntroSlide(dataset, storytellerReport,  new StorytellerInsightSlide(storytellerReport.getId(), 1, StorytellerUtils.getAvailableTitleSelectors(dataset
                        , storytellerReport
                        , insightThemesSlideData).get(0))
                        , this.introSlideTitle(dataset)
        );
        StorytellerInsightSlide themesInsightsSlide = this.generateThemesInsightsSlide(dataset, storytellerReport, new StorytellerInsightSlide(storytellerReport.getId(), 2));
        StorytellerInsightSlide presentationFlowSlide = this.generatePresentationFlowSlide(dataset, storytellerReport, new StorytellerInsightSlide(storytellerReport.getId(), 3));

        insightSlideService.upsertSlide(coverSlide);
        insightSlideService.upsertSlide(themesInsightsSlide);
        insightSlideService.upsertSlide(presentationFlowSlide);

        int slideOrder = 4;
        //loop
        for (int i = 0; i < insightThemesSlideData.size(); i++) {
            int insightSlideOrder = slideOrder++;
            StorytellerInsightSlide didYouKnowSlide = this.generateDidYouKnowSlide(dataset, storytellerReport, new StorytellerInsightSlide(storytellerReport.getId(), slideOrder++), i);
            StorytellerInsightSlide implicationSlide = this.generateImplicationSlide(dataset, storytellerReport, new StorytellerInsightSlide(storytellerReport.getId(), slideOrder++, StorytellerUtils.getAvailableImplicationTitleSelectors(dataset
                            , insightThemesSlideData.get(i)).get(0))
                    , i
                    );

            StorytellerInsightSlide commentsSlide = this.generateCommentsSlide(dataset, storytellerReport, new StorytellerInsightSlide(storytellerReport.getId(), slideOrder++), i);

            // Re-generate insight slide with AI
            StorytellerInsightSlide insightSlide =  this.generateInsightSlide(dataset, storytellerReport, new StorytellerInsightSlide(storytellerReport.getId(),insightSlideOrder ), i);
            insightSlide.getSlideData().setText(this.generateInsightText(
                      storytellerReport.getSettings().getReportCategoryValue()
                    , insightThemesSlideData.get(i).getInsightsTopicsModel().getTopicLabel()
                    , didYouKnowSlide.getSlideData().getText()
                    , implicationSlide.getSlideData().getText()));

            insightSlideService.upsertSlide(insightSlide);
            insightSlideService.upsertSlide(didYouKnowSlide);
            insightSlideService.upsertSlide(implicationSlide);
            insightSlideService.upsertSlide(commentsSlide);
        }
    }

    private int doGenerateStorytellerSlides(int userId, int datasetId, StorytellerReportRequest storytellerReportRequest) {
        logger.info("Start creating Storyteller Report dataset {}", datasetId);
        Dataset dataset = datasetMapper.selectById(datasetId);

        InsightsScorecard insightsScorecard = insightsService.generateInsightsScorecard(datasetId, 7, 7);
        // If the dataset contains a score column, use potential change to calculate key areas
        StorytellerReport storytellerReport = new StorytellerReport();
        storytellerReport.setUserId(userId);
        storytellerReport.setDatasetId(datasetId);
        storytellerReport.setReportType(storytellerReportRequest.getReportType());
        storytellerReport.setThemeIds(insightsScorecard.getPersistedComments().stream().map(ScorecardPersistedComment::getThemeId).collect(Collectors.toList()));
        StorytellerReportSettings storytellerReportSettings = new StorytellerReportSettings();
        storytellerReportSettings.setReportName(storytellerReportRequest.getReportName());
        storytellerReportSettings.setReportCategory(storytellerReportRequest.getReportCategory());
        storytellerReportSettings.setReportCategoryExtraInfo(storytellerReportRequest.getReportCategoryExtraInfo());
        storytellerReportSettings.setValueAtRiskInfo(dataset.getValueAtRiskInfo());

        storytellerReportSettings.setAvailableInsightSettings(StorytellerUtils.getAvailableInsightSettings(dataset));

        if(!storytellerReportSettings.getAvailableInsightSettings().contains(InsightSettingOptions.predict_improvement)){
            storytellerReportSettings.getInsightSettings().setDisplayPredictImprovement(false);
        } else {
            Map<Integer, String> availablePredictImprovements = StorytellerUtils.getMetricHeaders(dataset);
            storytellerReportSettings.getInsightSettings().setAvailablePredictImprovements(availablePredictImprovements);
            storytellerReportSettings.getInsightSettings().setPredictImprovementBy(availablePredictImprovements.entrySet().iterator().next());
        }
        if(storytellerReportSettings.hasVar()) {
            storytellerReportSettings.getInsightSettings().setDisplayValueAtRisk(true);
        } else if(!StorytellerUtils.hasMetric(dataset)) {
            storytellerReportSettings.getInsightSettings().setDisplaySwot(true);
        }

        storytellerReport.setSettings(storytellerReportSettings);
        List<InsightThemeSlideData> insightThemesSlideData = this.generateThemeSlideData(dataset, storytellerReport.getThemeIds(),storytellerReportSettings.getInsightSettings().getPredictImprovementBy() );
        storytellerReportSettings.setInsightThemesData(insightThemesSlideData);
        reportService.insertReport(storytellerReport);
        doGenerateStorytellerSlides(userId, dataset, storytellerReport);

        logger.info("End creating Storyteller Report dataset {} -> report id {}", dataset.getId(), storytellerReport.getId());

        return storytellerReport.getId();
    }

    //Generate cover slide title
    private String generateIntroSlideText(Dataset dataset, StorytellerReport report, StorytellerInsightSlide slide, String organisationName ) {
        TitleSelection titleSelection = slide.getSlideData().getSelectedTitle();
        List<TitleSelection> availableTitleSelectors = slide.getSlideData().getAvailableTitles();
        List<InsightThemeSlideData> insightThemesSlideData = report.getSettings().getInsightThemesData();
        Map.Entry<Integer, String> predictImprovementBy = report.getSettings().getInsightSettings().getPredictImprovementBy();
        if(!availableTitleSelectors.contains(titleSelection)) {
            logger.error("This title selection {} is not supported for this dataset {}", titleSelection, dataset.getId());
            throw new EmoticsException(String.format("%s title selection is not supported for dataset %s", titleSelection.name(), dataset.getId()));
        }
        logger.info("Start creating Storyteller Intro Slide Title 1 for type {}", titleSelection.name());
        switch (titleSelection){
            case var:
                ValueAtRiskInfo valueAtRiskInfo = report.getSettings().getValueAtRiskInfo();
                return StorytellerUtils.toParagraph(String.format("How can %s %s", organisationName, StorytellerUtils.toBold(String.format("reduce Revenue at Risk by %s%s", valueAtRiskInfo.getCurrencySymbol() , valueAtRiskInfo.getValueAtRiskAmountString()))));
            case metric:
                long totalScoreIncreased = (long) StorytellerUtils.totalOfScoreIncreased(insightThemesSlideData);
                return StorytellerUtils.toParagraph(String.format("How can %s %s", organisationName, StorytellerUtils.toBold(String.format("increase %s by %d%%", predictImprovementBy.getValue(), totalScoreIncreased))));
            case increase:
                return StorytellerUtils.toParagraph(StorytellerUtils.generateIncreaseEmotionTitle(organisationName));
            case address:
                return StorytellerUtils.toParagraph(StorytellerUtils.generateAddressKeyIssueTitle(organisationName, dataset));
            case theme_insight:
                return StorytellerUtils.toParagraph(StorytellerUtils.toBold(String.format("Themes, Insights & Implications for %s", organisationName)));
            case number_of_themes:
                return StorytellerUtils.toParagraph(String.format(StorytellerUtils.toBold("%d Decision Ready Insights") + " for %s", insightThemesSlideData.size(), dataset.getLabel()));
            default:
                return StorytellerUtils.toParagraph(StorytellerUtils.toBold(insightThemesSlideData.size() + "  Decision Ready Insights for ")) + dataset.getLabel();
        }
    }

    private String generateInsightText(Dataset dataset, InsightThemeSlideData insightThemeSlideData) {
        String text = String.format("'%s' is ", insightThemeSlideData.getInsightsTopicsModel().getTopicLabel() );
        SwotAttribute primaryAttribute = insightThemeSlideData.getInsightsTopicsModel().getSwot().getAttribute();
        logger.info("Start generating Storyteller Insight Slide 3 with key theme {} - attribute {} ", insightThemeSlideData.getInsightsTopicsModel().getTopicLabel(), primaryAttribute.name());

        switch (primaryAttribute) {
            case WEAKNESS:
                text += String.format("a %s of the experience that...", StorytellerUtils.toBold(StorytellerUtils.toColorSpan("weakness", StorytellerUtils.getColorCode(primaryAttribute))));
                break;
            case STRENGTH:
                text += String.format("a %s of the experience that...", StorytellerUtils.toBold(StorytellerUtils.toColorSpan("key strength", StorytellerUtils.getColorCode(primaryAttribute))));
                break;
            case OPPORTUNITY:
                text += String.format("an %s to capitalise on the experience that...", StorytellerUtils.toBold((StorytellerUtils.toColorSpan("opportunity", StorytellerUtils.getColorCode(primaryAttribute)))));
                break;
            case THREAT:
                text +=  String.format("a %s to the experience that...", StorytellerUtils.toBold(StorytellerUtils.toColorSpan("threat", StorytellerUtils.getColorCode(primaryAttribute))));
                break;
            default:
                int keyPhrase = StorytellerUtils.findMaxIndex(dataset.getSummary().getIndexScores(), null );
                text += "driving " + EightEmotionalIndex.nameByApiIndex(keyPhrase) + ", which means that...";
                break;
        }

        logger.info("End generating Storyteller Insight Slide with {} ", text);

        return StorytellerUtils.toParagraph(text);
    }

    private String generateInsightText(String presentationType, String themeName, String didYouKnowText, String implicationText) {
        return storytellerAIPromptService.invokeDecisionReadyInsightResponse(presentationType, themeName, didYouKnowText, implicationText);
    }

    private String getPrimaryAttribute(Dataset dataset, InsightThemeSlideData insightThemeSlideData) {
        SwotAttribute primaryAttribute = insightThemeSlideData.getInsightsTopicsModel().getSwot().getAttribute();
        if(primaryAttribute == SwotAttribute.NONE) {
            // If primary attribute is not set, find the max index from dataset summary
            int keyPhrase = StorytellerUtils.findMaxIndex(dataset.getSummary().getIndexScores(), null);
            return EightEmotionalIndex.nameByApiIndex(keyPhrase);
        }
        return primaryAttribute.toTitleCase();
    }

    private Map.Entry<EightEmotionalIndex, String> generateDidYouKnowText(Dataset dataset, StorytellerReport report, InsightThemeSlideData keyTheme, EightEmotionalIndex selectedIndex) {
        EightEmotionalIndex emotionalIndex = EightEmotionalIndex.trust; // Set emotionalIndex to selectedIndex if provided
        String keyThemeName = keyTheme.getInsightsTopicsModel().getTopicLabel();
        String keyThemeResponseNumber = " represents " + StorytellerUtils
                .calculateVolumePercentage(dataset, keyTheme.getInsightsTopicsModel().getNumOfDocuments()) + "% of responses ";
        final String[] keyThemeInsight = new String[1];
        double themeScore = keyTheme.getInsightsTopicsModel().getPolarity();
        boolean lowestScore = false;
        double gapScore = 0.0;
        // Store key components for highlighting
        String percentageValue = "";
        String directionWord = "";
        String coloredCode = "";
        boolean moreOrLess;

        // If selectedIndex is null, use existing logic to find the emotional index
        if (selectedIndex == null) {
            if (themeScore >= 0) {
                for (Map.Entry<EightEmotionalIndex, Boolean> entry : POS_THEME_PRIORITY_MAP.entrySet()) {
                    double emoThemeScore = keyTheme.getInsightsTopicsModel().getEmotionIndexes()[entry.getKey().ordinal()];
                    Map<String, Object> themeEmoPct = StorytellerUtils.calculateEmotionPercentage(dataset, emoThemeScore, entry.getKey().ordinal());
                    gapScore =  (double) themeEmoPct.get("gapScore");
                    moreOrLess = gapScore > 0;
                    if (moreOrLess == entry.getValue()) {
                        emotionalIndex = EightEmotionalIndex.values()[entry.getKey().ordinal()];
                        coloredCode = StorytellerUtils.getColorCode(moreOrLess, EightEmotionalIndex.isGreen(entry.getKey()));
                        percentageValue = String.format("%s%%", themeEmoPct.get("gapPctText"));
                        directionWord = StorytellerUtils.evaluateGapScore(moreOrLess);
                        //String insight = StorytellerUtils.toBold(StorytellerUtils.toColorSpan(String.format("%s%% %s %s", themeEmoPct.get("gapPctText"), StorytellerUtils.evaluateGapScore(moreOrLess), emotionalIndex.titleCase()), StorytellerUtils.getColorCode(moreOrLess, EightEmotionalIndex.isGreen(entry.getKey()))));
                        String insight = String.format("%s%% %s %s", themeEmoPct.get("gapPctText"), StorytellerUtils.evaluateGapScore(moreOrLess), emotionalIndex.titleCase());
                        keyThemeInsight[0] = String.format("driving %s", insight);
                        break;
                    }
                }
            } else {
                for (Map.Entry<EightEmotionalIndex, Boolean> entry : NEG_THEME_PRIORITY_MAP.entrySet()) {
                    double emoThemeScore = keyTheme.getInsightsTopicsModel().getEmotionIndexes()[entry.getKey().ordinal()];
                    Map<String, Object> themeEmoPct = StorytellerUtils.calculateEmotionPercentage(dataset, emoThemeScore, entry.getKey().ordinal());
                    gapScore =  (double) themeEmoPct.get("gapScore");
                    moreOrLess = gapScore > 0;
                    if (moreOrLess == entry.getValue()) {
                        emotionalIndex = EightEmotionalIndex.values()[entry.getKey().ordinal()];
                        coloredCode = StorytellerUtils.getColorCode(moreOrLess, EightEmotionalIndex.isGreen(entry.getKey()));
                        percentageValue = String.format("%s%%", themeEmoPct.get("gapPctText"));
                        directionWord = StorytellerUtils.evaluateGapScore(moreOrLess);
                        //String insight = StorytellerUtils.toBold(StorytellerUtils.toColorSpan(String.format("%s%% %s %s", themeEmoPct.get("gapPctText"), StorytellerUtils.evaluateGapScore(moreOrLess), emotionalIndex.titleCase()), StorytellerUtils.getColorCode(moreOrLess, EightEmotionalIndex.isGreen(entry.getKey()))));
                        String insight = String.format("%s%% %s %s", themeEmoPct.get("gapPctText"), StorytellerUtils.evaluateGapScore(moreOrLess), emotionalIndex.titleCase());
                        keyThemeInsight[0] = String.format("driving %s", insight);
                        break;
                    }
                }
            }
        } else {
            emotionalIndex = selectedIndex;
            Map<String, Object> themeEmoPct = StorytellerUtils.calculateEmotionPercentage(dataset, keyTheme.getInsightsTopicsModel().getEmotionIndexes()[emotionalIndex.ordinal()], emotionalIndex.ordinal());
            gapScore =  (double) themeEmoPct.get("gapScore");
            moreOrLess = gapScore > 0;
            coloredCode = StorytellerUtils.getColorCode(moreOrLess, EightEmotionalIndex.isGreen(emotionalIndex));
            percentageValue = String.format("%s%%", themeEmoPct.get("gapPctText"));
            directionWord = StorytellerUtils.evaluateGapScore(moreOrLess);
            //keyThemeInsight[0] = String.format("driving %s", StorytellerUtils.toBold(StorytellerUtils.toColorSpan(String.format("%s%% %s %s", themeEmoPct.get("gapPctText"), StorytellerUtils.evaluateGapScore(moreOrLess), emotionalIndex.titleCase()), StorytellerUtils.getColorCode(moreOrLess, EightEmotionalIndex.isGreen(emotionalIndex)))));
            keyThemeInsight[0] = String.format("driving %s", String.format("%s%% %s %s", themeEmoPct.get("gapPctText"), StorytellerUtils.evaluateGapScore(moreOrLess), emotionalIndex.titleCase()));
        }

        logger.info("End generating Storyteller Insight Slide 4 with key theme {} - Theme Score {} ", keyTheme.getInsightsTopicsModel().getTopicLabel(), themeScore);

        if(gapScore >= 0 && NEG_THEME_PRIORITY_MAP.get(emotionalIndex) != null
        || gapScore < 0 && POS_THEME_PRIORITY_MAP.get(emotionalIndex) != null) {
            lowestScore = true;
        }

        // Combine the information into the final insight
        //'Mentions of [Theme Name] account for [Percentage]% of responses, driving [Percentage Increase/Decrease]% [more/less] [Emotion]
        String insight = String.format("Mentions of '%s' account for %s, %s", keyThemeName, keyThemeResponseNumber, keyThemeInsight[0]);
        String aiText = storytellerAIPromptService.invokeDidYouKnowResponse(
                report.getSettings().getReportCategoryValue(),
                insight,
                new ArrayList<>(Objects.requireNonNull(commentService.getTopComments(dataset.getId(), keyTheme.getTopicId(), emotionalIndex, lowestScore, 3)).values())
        );

        String formattedAiText = StorytellerUtils.applyDidYouKnowHighlighting(StorytellerUtils.removeParensAroundPercentageIfNotInSpan(aiText), percentageValue, directionWord, emotionalIndex.titleCase(), coloredCode);

        // Return the emotionalIndex and toParagraph(aiText)
        return new AbstractMap.SimpleEntry<>(emotionalIndex, StorytellerUtils.toParagraph(formattedAiText));
    }

    private String generateInsightImplicationText(Dataset dataset, StorytellerReport report, StorytellerInsightSlide implicationSlide, InsightThemeSlideData keyTheme) {
        ImplicationTitleSelection implicationTitleSelection = implicationSlide.getSlideData().getImplicationSlideData().getSelectedTitle();
        if(implicationTitleSelection == null) {
            implicationTitleSelection = implicationSlide.getSlideData().getImplicationSlideData().getAvailableTitles().get(0);
            implicationSlide.getSlideData().getImplicationSlideData().setSelectedTitle(implicationTitleSelection);
        }
        ImplicationSlideData implicationSlideData = implicationSlide.getSlideData().getImplicationSlideData();
        Map.Entry<Integer, String> predictImprovementBy = report.getSettings().getInsightSettings().getPredictImprovementBy();
        if(!implicationSlideData.getAvailableTitles().contains(implicationTitleSelection)) {
            logger.error("This title selection {} is not supported for this dataset {} and theme {}", implicationTitleSelection, dataset.getId(), keyTheme.getTopicId());
            throw new EmoticsException(String.format("%s title selection is not supported for dataset %s", implicationTitleSelection.name(), dataset.getId()));
        }

        logger.info("Start generating Storyteller Insight Slide 5 with key theme {} ", keyTheme.getInsightsTopicsModel().getTopicLabel());

        StringBuilder stringBuilder = new StringBuilder();
        String valueAtRisk = "";
        long metricImprovement = 0;
        switch (implicationTitleSelection) {
            case metric_improvement:
                if(StorytellerUtils.hasMetric(dataset)) {
                    //(1) if metric is available
                    metricImprovement = Math.round(keyTheme.getOptimiseChange() * 100);
                    implicationSlideData.setOptimiseChange(keyTheme.getOptimiseChange());
                    logger.info("Metric Improvement {}", metricImprovement);
                    if(metricImprovement >= 1) {
                        String[] titles = new String[]{
                                String.format("Continuing to build on '%s'", keyTheme.getInsightsTopicsModel().getTopicLabel()),
                                String.format("Continuing to improve '%s'", keyTheme.getInsightsTopicsModel().getTopicLabel()),
                                String.format("Continuing to put emphasis on '%s'", keyTheme.getInsightsTopicsModel().getTopicLabel()),
                                String.format("Addressing '%s'", keyTheme.getInsightsTopicsModel().getTopicLabel()),
                        };
                        int randomNum = ThreadLocalRandom.current().nextInt(0, titles.length);

                        AnalysisSwot analysisSwot = keyTheme.getInsightsTopicsModel().getSwot();
                        if(analysisSwot != null) {
                            SwotAttribute primaryAttribute = analysisSwot.getAttribute();
                            implicationSlideData.setSwot(primaryAttribute.toTitleCase());
                            switch (primaryAttribute) {
                                case STRENGTH:
                                    stringBuilder.append(titles[randomNum]);
                                    stringBuilder.append(" could maintain this strength");
                                    break;
                                case OPPORTUNITY:
                                    stringBuilder.append(titles[randomNum]);
                                    stringBuilder.append(" could turn this opportunity into a strength");
                                    implicationSlideData.setPotentialSwot(SwotAttribute.STRENGTH.toTitleCase());
                                    break;
                                case THREAT:
                                    stringBuilder.append(titles[3]);
                                    stringBuilder.append(" could turn this threat into an opportunity");
                                    implicationSlideData.setPotentialSwot(SwotAttribute.OPPORTUNITY.toTitleCase());
                                    break;
                                case WEAKNESS:
                                    stringBuilder.append(titles[3]);
                                    stringBuilder.append(" the impact of this weakness");
                                    break;
                                default:
                                    break;
                            }
                        }

                        if(report.getSettings().hasVar() && keyTheme.hasVar()) {
                            valueAtRisk = String.format("%s%s", report.getSettings().getValueAtRiskInfo().getCurrencySymbol(), keyTheme.getValueAtRiskAmountString());
                            stringBuilder.append(", ").append(String.format("increase %s by %d%%", predictImprovementBy.getValue(), Math.round(metricImprovement)));
                            stringBuilder.append(" and reduce Revenue at Risk by up to ").append(valueAtRisk);
                        } else {
                            stringBuilder.append(" and ").append(String.format("increase %s by %d%%", predictImprovementBy.getValue(), Math.round(metricImprovement)));
                        }
                        stringBuilder.append(".");
                        break;
                    }
                }

            case swot_improvement:
                SwotAttribute primaryAttribute = keyTheme.getInsightsTopicsModel().getSwot().getAttribute();
                implicationSlideData.setSwot(primaryAttribute.toTitleCase());
                switch (primaryAttribute) {
                    case STRENGTH:
                    {
                        String[] titles = new String[]{
                                String.format("Continue to put emphasis on '%s'", keyTheme.getInsightsTopicsModel().getTopicLabel()),
                                String.format("Continue to maintain '%s'", keyTheme.getInsightsTopicsModel().getTopicLabel()),
                        };
                        int randomNum = ThreadLocalRandom.current().nextInt(0, titles.length);
                        stringBuilder.append(titles[randomNum]);
                        stringBuilder.append(" could maintain this strength");
                        if(report.getSettings().hasVar() && keyTheme.hasVar()) {
                            valueAtRisk = String.format("%s%s", report.getSettings().getValueAtRiskInfo().getCurrencySymbol(), keyTheme.getValueAtRiskAmountString());
                            stringBuilder.append(", to increase trust and reduce Revenue at Risk by up to ").append(valueAtRisk);
                        } else {
                            stringBuilder.append(" to increase trust");
                        }
                        stringBuilder.append(".");
                        break;
                    }
                    case OPPORTUNITY:
                    {
                        String[] titles = new String[]{
                                String.format("Continue to build on '%s'", keyTheme.getInsightsTopicsModel().getTopicLabel()),
                                String.format("Continue to improve '%s'", keyTheme.getInsightsTopicsModel().getTopicLabel()),
                        };
                        int randomNum = ThreadLocalRandom.current().nextInt(0, titles.length);
                        stringBuilder.append(titles[randomNum]);
                        stringBuilder.append(" to turn this from an opportunity into a strength");
                        implicationSlideData.setPotentialSwot(SwotAttribute.STRENGTH.toTitleCase());

                        if(report.getSettings().hasVar() && keyTheme.hasVar()) {
                            valueAtRisk = String.format("%s%s", report.getSettings().getValueAtRiskInfo().getCurrencySymbol(), keyTheme.getValueAtRiskAmountString());
                            stringBuilder.append(", increase trust and reduce Revenue at Risk by up to ").append(valueAtRisk);
                        } else {
                            stringBuilder.append(" and increase trust");
                        }
                        stringBuilder.append(".");
                        break;
                    }
                    case THREAT:
                    {
                        String[] titles = new String[]{
                                String.format("A focus on '%s' could turn these concerns from a ", keyTheme.getInsightsTopicsModel().getTopicLabel()),
                                String.format("A focus on '%s' could turn this ", keyTheme.getInsightsTopicsModel().getTopicLabel()),
                                String.format("Focussing on '%s' could turn this ", keyTheme.getInsightsTopicsModel().getTopicLabel()),
                                String.format("Addressing '%s' could turn this ", keyTheme.getInsightsTopicsModel().getTopicLabel()),
                                String.format("Addressing '%s' could improve perception to ", keyTheme.getInsightsTopicsModel().getTopicLabel()),
                                String.format("Addressing '%s' could improve perception ", keyTheme.getInsightsTopicsModel().getTopicLabel()),
                        };
                        int randomNum = ThreadLocalRandom.current().nextInt(0, titles.length);
                        stringBuilder.append(titles[randomNum]);
                        if (randomNum <= 3) {
                            stringBuilder.append("threat into an opportunity");
                            implicationSlideData.setPotentialSwot(SwotAttribute.OPPORTUNITY.toTitleCase());
                            if(report.getSettings().hasVar() && keyTheme.hasVar()) {
                                valueAtRisk = String.format("%s%s", report.getSettings().getValueAtRiskInfo().getCurrencySymbol(), keyTheme.getValueAtRiskAmountString());
                                stringBuilder.append(", increase trust and reduce Revenue at Risk by up to ").append(valueAtRisk);
                            } else {
                                stringBuilder.append(" and increase trust");
                            }
                        } else {
                            stringBuilder.append("avoid this threat becoming a weakness");
                            implicationSlideData.setPotentialSwot(SwotAttribute.WEAKNESS.toTitleCase());
                            if(!report.getSettings().hasVar()) {
                                stringBuilder.append(" and rebuild trust");
                            }
                        }
                        stringBuilder.append(".");
                        break;
                    }
                    case WEAKNESS: {
                        String[] titles = new String[]{
                                String.format("Address this '%s' to reduce the impact of this weakness",
                                        keyTheme.getInsightsTopicsModel().getTopicLabel()),
                        };
                        int randomNum = ThreadLocalRandom.current().nextInt(0, titles.length);
                        stringBuilder.append(titles[randomNum]);
                        if(report.getSettings().hasVar() && keyTheme.hasVar()) {
                            valueAtRisk = String.format("%s%s", report.getSettings().getValueAtRiskInfo().getCurrencySymbol(), keyTheme.getValueAtRiskAmountString());
                            stringBuilder.append(", rebuild trust and reduce Revenue at Risk by up to ").append(valueAtRisk);
                        } else {
                            stringBuilder.append(" and rebuild trust");
                        }
                        stringBuilder.append(".");
                        break;
                    }
                }
            default:
                logger.info("No implication title selected");
        }

        String insight = stringBuilder.toString();

        // Apply SWOT highlighting to the constructed text
        String aiText = storytellerAIPromptService.invokeImplicationResponse(
                report.getSettings().getReportCategoryValue()
                , insight
                , new ArrayList<>(Objects.requireNonNull(commentService.getTopComments(dataset.getId(), keyTheme.getTopicId(), null, false, 3)).values()));

        aiText = StorytellerUtils.applyImplicationHighlighting(
                aiText,
                Optional.ofNullable(predictImprovementBy).map(Map.Entry::getValue).orElse(""),
                metricImprovement,
                Optional.ofNullable(implicationSlideData).map(ImplicationSlideData::getSwot).orElse(""),
                Optional.ofNullable(implicationSlideData).map(ImplicationSlideData::getPotentialSwot).orElse(""),
                Objects.toString(valueAtRisk, "")
        );

        return StorytellerUtils.toParagraph(aiText);
    }

    private List<InsightThemeSlideData> generateThemeSlideData (Dataset dataset, List<Integer> themeIds, Map.Entry<Integer, String> predictImprovementBy) {
        final List<InsightsTopicsModel> insightsTopics = insightsService.getTopics(dataset.getId(), themeIds);

        List<GenesisChange> potentialChanges = (predictImprovementBy != null && predictImprovementBy.getKey() >= 0) ?
                genesisService.calculateMetadataChanges(dataset.getId(), themeIds, predictImprovementBy.getKey()) :
                new ArrayList<>();

        return insightsTopics.stream().map(topic -> {
            InsightThemeSlideData insightThemeSlideData = new InsightThemeSlideData();
            insightThemeSlideData.setInsightsTopicsModel(topic);
            insightThemeSlideData.setTopicId(topic.getId());
            potentialChanges.stream().filter(potentialChange -> potentialChange.getTopicId() == topic.getId()).findFirst().ifPresent(genesisChange -> {
                insightThemeSlideData.setOptimiseChange(genesisChange.getOptimiseChange());
                insightThemeSlideData.setResolveChange(genesisChange.getResolveChange());
            });
            insightThemeSlideData.setValueAtRiskAmount(topic.getValueAtRiskAmount());
            return insightThemeSlideData;
        }).collect(Collectors.toList());
    }

    private StorytellerInsightSlide generateIntroSlide(Dataset dataset, StorytellerReport report , StorytellerInsightSlide coverSlide, String organisationLabel) {
        StorytellerSlideData coverSlideData = coverSlide.getSlideData();
        coverSlide.setReportId(coverSlide.getReportId());
        coverSlide.setSlideOrder(coverSlide.getSlideOrder());
        coverSlide.setSlideType(StorytellerSlideType.INTRO);
        coverSlide.setSlideData(coverSlideData);
        coverSlideData.setAvailableTitles(StorytellerUtils.getAvailableTitleSelectors(dataset, report, report.getSettings().getInsightThemesData()));
        coverSlideData.setText(this.generateIntroSlideText(dataset
                , report
                , coverSlide
                , organisationLabel
        ));
        coverSlideData.setHeader("DECISION READY INSIGHTS");
        coverSlideData.setCustomCssStyle(new CustomCssStyle(100, "start"));
        coverSlideData.setAlias(coverSlide.getSlideType().getAlias());
        return coverSlide;
    }

    private StorytellerInsightSlide generateThemesInsightsSlide(Dataset dataset, StorytellerReport report, StorytellerInsightSlide themesInsightsSlide) {
        StorytellerSlideData themesInsightsSlideData = themesInsightsSlide.getSlideData();
        themesInsightsSlide.setReportId(themesInsightsSlide.getReportId());
        themesInsightsSlide.setSlideOrder(themesInsightsSlide.getSlideOrder());
        themesInsightsSlide.setSlideType(StorytellerSlideType.THEMES_INSIGHTS);
        themesInsightsSlide.setSlideData(themesInsightsSlideData);
        themesInsightsSlideData.setInsightThemesData(report.getSettings().getInsightThemesData());
        themesInsightsSlideData.setHeader("THEMES AND INSIGHTS");
        themesInsightsSlideData.setAlias(themesInsightsSlide.getSlideType().getAlias());
        return themesInsightsSlide;
    }

    private StorytellerInsightSlide generatePresentationFlowSlide(Dataset dataset, StorytellerReport storytellerReport, StorytellerInsightSlide storytellerInsightSlide) {
        logger.info("Generating Presentation Flow slide for Storyteller Report {}", storytellerReport.getId());
        storytellerInsightSlide.setSlideType(StorytellerSlideType.PRESENTATION_FLOW);
        storytellerInsightSlide.setSlideData(new StorytellerSlideData());
        storytellerInsightSlide.getSlideData().setAlias(storytellerInsightSlide.getSlideType().getAlias());
        return storytellerInsightSlide;

    }

    private StorytellerInsightSlide generateInsightSlide(Dataset dataset, StorytellerReport report, StorytellerInsightSlide insightSlide, int themeIndex) {
        StorytellerSlideData insightSlideData = insightSlide.getSlideData();
        insightSlide.setReportId(insightSlide.getReportId());
        insightSlide.setSlideType(StorytellerSlideType.INSIGHT);
        insightSlide.setSlideData(insightSlideData);
        insightSlideData.setThemeId(report.getSettings().getInsightThemesData().get(themeIndex).getTopicId());
        insightSlideData.setText(generateInsightText(dataset, report.getSettings().getInsightThemesData().get(themeIndex)));
        insightSlideData.setCustomCssStyle(new CustomCssStyle(85, "center"));
        return this.buildThemeSlideDataWithThemeIndex(insightSlide, themeIndex, insightSlide.getSlideOrder());
    }

    private StorytellerInsightSlide generateDidYouKnowSlide(Dataset dataset, StorytellerReport report, StorytellerInsightSlide didYouKnowSlide, int themeIndex) {
        StorytellerSlideData didYouKnowSlideData = didYouKnowSlide.getSlideData();
        didYouKnowSlide.setReportId(didYouKnowSlide.getReportId());
        didYouKnowSlide.setSlideType(StorytellerSlideType.DID_YOU_KNOW);
        didYouKnowSlide.setSlideData(didYouKnowSlideData);
        didYouKnowSlideData.setThemeId(report.getSettings().getInsightThemesData().get(themeIndex).getTopicId());
        Map.Entry<EightEmotionalIndex, String> didYouKnowText = generateDidYouKnowText(dataset, report, report.getSettings().getInsightThemesData().get(themeIndex), null);
        didYouKnowSlideData.setText(didYouKnowText.getValue());
        didYouKnowSlideData.setSelectedEmotion(didYouKnowText.getKey());
        didYouKnowSlideData.setCustomCssStyle(new CustomCssStyle(65, "start"));
        return this.buildThemeSlideDataWithThemeIndex(didYouKnowSlide, themeIndex, didYouKnowSlide.getSlideOrder());
    }

    private StorytellerInsightSlide generateDidYouKnowSlideWithEmotion(Dataset dataset, StorytellerReport report, StorytellerInsightSlide didYouKnowSlide, EightEmotionalIndex selectedEmotion) {
        StorytellerSlideData didYouKnowSlideData = didYouKnowSlide.getSlideData();
        InsightThemeSlideData keyTheme = StorytellerUtils.findTheme(report.getSettings().getInsightThemesData(), didYouKnowSlideData.getThemeId());
        Map.Entry<EightEmotionalIndex, String> didYouKnowText = generateDidYouKnowText(dataset, report, keyTheme, selectedEmotion);
        didYouKnowSlideData.setText(didYouKnowText.getValue());
        didYouKnowSlideData.setSelectedEmotion(didYouKnowText.getKey());
        return didYouKnowSlide;
    }

    private StorytellerInsightSlide generateImplicationSlide(Dataset dataset, StorytellerReport report, StorytellerInsightSlide implicationSlide, int themeIndex) {
        StorytellerSlideData implicationSlideSlideData = implicationSlide.getSlideData();
        implicationSlide.setReportId(implicationSlide.getReportId());
        implicationSlide.setSlideType(StorytellerSlideType.IMPLICATION);
        implicationSlide.setSlideData(implicationSlideSlideData);
        implicationSlideSlideData.setThemeId(report.getSettings().getInsightThemesData().get(themeIndex).getTopicId());
        implicationSlideSlideData.getImplicationSlideData().setAvailableTitles(StorytellerUtils.getAvailableImplicationTitleSelectors(dataset, report.getSettings().getInsightThemesData().get(themeIndex)));
        implicationSlideSlideData.getImplicationSlideData().setAvailableGraphics(StorytellerUtils.getAvailableImplicationGraphicSelectors(implicationSlideSlideData.getImplicationSlideData().getAvailableTitles()));
        if(implicationSlideSlideData.getImplicationSlideData().getSelectedGraphic() == null) {
            switch (implicationSlideSlideData.getImplicationSlideData().getSelectedTitle()) {
                case metric_improvement:
                    implicationSlideSlideData.getImplicationSlideData().setSelectedGraphic(ImplicationSwitchGraphic.metric_improvement);
                    break;
                case swot_improvement:
                    implicationSlideSlideData.getImplicationSlideData().setSelectedGraphic(ImplicationSwitchGraphic.swot_improvement);
                    break;
            }
        }
        implicationSlideSlideData.setText(generateInsightImplicationText(dataset, report, implicationSlide, report.getSettings().getInsightThemesData().get(themeIndex)));
        String implicationText = this.generateInsightImplicationText(dataset, report, implicationSlide, report.getSettings().getInsightThemesData().get(themeIndex));

        // Calculate font size for implication text based on length
        String implicationPlainText = Jsoup.parse(implicationText).text();
        int textLength = implicationPlainText.length();
        int baseFontSize = 50;
        int minFontSize = 25;

        if (textLength <= 250) {
            implicationSlideSlideData.setCustomCssStyle(new CustomCssStyle(baseFontSize, "start"));
        } else {
            int adjustedFontSize = baseFontSize - (textLength - 250) / 25; // Decrease by 1 for every 25 characters
            if (adjustedFontSize < minFontSize) {
                adjustedFontSize = minFontSize;
            }
            implicationSlideSlideData.setCustomCssStyle(new CustomCssStyle(adjustedFontSize, "start"));
        }

        implicationSlideSlideData.setText(implicationText);
        return this.buildThemeSlideDataWithThemeIndex(implicationSlide, themeIndex, implicationSlide.getSlideOrder());
    }

    private StorytellerInsightSlide generateCommentsSlide(Dataset dataset, StorytellerReport report, StorytellerInsightSlide commentSlide, int themeIndex) {
        List<InsightThemeSlideData> insightThemesSlideData = report.getSettings().getInsightThemesData();
        StorytellerSlideData commentSlideSlideData = commentSlide.getSlideData();
        commentSlide.setReportId(commentSlide.getReportId());
        commentSlide.setSlideType(StorytellerSlideType.COMMENTS);
        commentSlide.setSlideData(commentSlideSlideData);
        commentSlideSlideData.setThemeId(insightThemesSlideData.get(themeIndex).getTopicId());
        Map<Integer, String> comments = commentService.getTopComments(dataset.getId(), insightThemesSlideData.get(themeIndex).getTopicId(), null, false, 3);
        if(comments != null && !comments.isEmpty()) {
            // Map all the comments to the slide data
            commentSlideSlideData.setText(comments.values().stream().map(StorytellerUtils::toComment).collect(Collectors.joining("")));
            commentSlideSlideData.setCommentSlideData(CommentSlideData.fromSelectedCommentIds(new ArrayList<>(comments.keySet())));
        }
        commentSlideSlideData.setCustomCssStyle(new CustomCssStyle(50, "center"));
        return this.buildThemeSlideDataWithThemeIndex(commentSlide, themeIndex, commentSlide.getSlideOrder());
    }

    private StorytellerInsightSlide buildThemeSlideDataWithThemeIndex(StorytellerInsightSlide slide, int themeIndex, int slideOrder) {
        slide.setSlideOrder(slideOrder);
        slide.getSlideData().setHeader(String.format("DECISION READY INSIGHT %d", themeIndex + 1));
        slide.getSlideData().setAlias(String.format("%d%s", themeIndex + 1, slide.getSlideType().getAlias()));
        return slide;
    }

    private void regenerateSlide(
            int userId,
            Dataset dataset,
            StorytellerReport report,
            StorytellerInsightSlide slide,
            String label
            ) {
        int reportId = report.getId();
        logger.info("Re-generating slide {} - {} for Storyteller Report {}", slide.getSlideType(), slide.getId(), report.getId());

        switch (slide.getSlideType()) {
            case INTRO:
                slide = regenerateIntroSlide(userId, dataset, report, slide, label);
                break;
            case THEMES_INSIGHTS:
                slide = regenerateThemesInsightsSlide(dataset, report, slide);
                break;
            case INSIGHT:
                slide = regenerateInsightSlide(dataset, report, slide);
                break;
            case DID_YOU_KNOW:
                slide = regenerateDidYouKnowSlide(dataset, report, slide);
                break;
            case IMPLICATION:
                slide = regenerateImplicationSlide(dataset, report, slide);
                break;
            case COMMENTS:
                slide = regenerateCommentsSlide(dataset, report, slide);
                break;
        }
        insightSlideService.updateSlide(reportId, slide.getId(), slide);
    }

    private StorytellerInsightSlide regenerateIntroSlide(
            int userId,
            Dataset dataset,
            StorytellerReport report,
            StorytellerInsightSlide slide,
            String label
    ) {
        List<TitleSelection> titleSelectors = reportService.getAvailableTitleSelectors(userId, dataset.getId(), report.getId());
        if (!titleSelectors.contains(slide.getSlideData().getSelectedTitle())) {
            slide.getSlideData().setSelectedTitle(titleSelectors.get(0));
        }
        return generateIntroSlide(dataset, report, slide, label);
    }

    private StorytellerInsightSlide regenerateThemesInsightsSlide(
            Dataset dataset,
            StorytellerReport report,
            StorytellerInsightSlide slide
    ) {
        return generateThemesInsightsSlide(dataset, report, slide);
    }

    private StorytellerInsightSlide regenerateInsightSlide(
            Dataset dataset,
            StorytellerReport storytellerReport,
            StorytellerInsightSlide slide
    ) {
        List<InsightThemeSlideData> insightThemesSlideData = storytellerReport.getSettings().getInsightThemesData();
        int themeIndex = StorytellerUtils.findThemeIndex(insightThemesSlideData, slide.getSlideData().getThemeId());
        return generateInsightSlide(dataset, storytellerReport, slide, themeIndex);
    }

    private StorytellerInsightSlide regenerateDidYouKnowSlide(
            Dataset dataset,
            StorytellerReport report,
            StorytellerInsightSlide slide
    ) {
        List<InsightThemeSlideData> insightThemesSlideData = report.getSettings().getInsightThemesData();
        int themeIndex = StorytellerUtils.findThemeIndex(insightThemesSlideData, slide.getSlideData().getThemeId());
        return generateDidYouKnowSlide(dataset, report, slide, themeIndex);
    }

    private StorytellerInsightSlide regenerateImplicationSlide(
            Dataset dataset,
            StorytellerReport report,
            StorytellerInsightSlide slide
    ) {
        List<InsightThemeSlideData> insightThemesSlideData = report.getSettings().getInsightThemesData();
        int themeIndex = StorytellerUtils.findThemeIndex(insightThemesSlideData, slide.getSlideData().getThemeId());
        List<ImplicationTitleSelection> titleSelections = StorytellerUtils.getAvailableImplicationTitleSelectors(dataset, insightThemesSlideData.get(themeIndex));
        slide.getSlideData().getImplicationSlideData().setAvailableTitles(titleSelections);
        slide.getSlideData().getImplicationSlideData().setSelectedTitle(titleSelections.get(0));
        return generateImplicationSlide(dataset, report, slide, themeIndex);
    }

    private StorytellerInsightSlide regenerateCommentsSlide(
            Dataset dataset,
            StorytellerReport report,
            StorytellerInsightSlide slide
    ) {
        List<InsightThemeSlideData> insightThemesSlideData = report.getSettings().getInsightThemesData();
        int themeIndex = StorytellerUtils.findThemeIndex(insightThemesSlideData, slide.getSlideData().getThemeId());
        return generateCommentsSlide(dataset, report, slide, themeIndex);
    }


    public String introSlideTitle(Dataset dataset) {
        Organisation organisation = organisationMapper.selectByWorkspaceId(dataset.getWorkspaceId()).orElseGet(() -> {
            Organisation org = new Organisation();
            // Initialize org if necessary
            logger.error("Organisation is invalid for Workspace {}", dataset.getWorkspaceId());
            org.setName(dataset.getLabel());
            return org;
        });

        return organisation.getName();
    }

}
