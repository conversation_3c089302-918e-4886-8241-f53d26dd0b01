// =======================================================================================
// Compile
// =======================================================================================

stage name: 'Compile'
node {
    deleteDir()
    git url: '*****************:adoreboard/adoreboard-farfisa.git', branch: 'release'
    sh 'aws s3 cp s3://com.adoreboard.bash-scripts bin --recursive && chmod +x bin/*'
    env.JAVA_HOME = tool 'Java 11'
    String mvnHome = tool 'Maven'
    env.NODEJS_HOME = tool 'nodejs'
    env.PATH = "${mvnHome}/bin:${env.PATH}"
    env.PATH = "${env.NODEJS_HOME}/bin:${env.PATH}"
    sh 'npm --version'
    sh 'mvn clean compile'
}
stage name: 'Unit Test'
node {
    sh 'mvn test'
}
stage name: 'Integ. Test'
node {
    sh 'mvn install -Dtest=NotExists -Dsurefire.failIfNoSpecifiedTests=false'
}

// =======================================================================================
// Staging
// =======================================================================================

stage name: 'Docker Build'
node {
    sh "./bin/upload-docker-compose-files.sh adoreboard-farfisa"
    dir ('farfisa-api') {
        sh "../bin/docker-build.sh emotics-api-staging"
    }
    // dir ('farfisa-data') {
    //     sh "../bin/docker-build.sh emotics-data-staging"
    // }
    dir ('farfisa-processor') {
        sh "../bin/docker-build.sh emotics-processor-staging"
    }
}
stage name: 'Staging Deploy'
node {
    sh "./bin/docker-deploy-via-ssh.sh <EMAIL> adoreboard-farfisa '-f docker-compose-staging.yml'"
}
stage name: 'Staging Smoke Tests'
node {
    sh "./bin/smoke-test.sh https://staging-api.emotics.io/pulse"
    // Need to configure reverse proxy to be able to route secure traffic to non-:80 traffic
    // sh "./bin/smoke-test.sh https://staging-api.emotics.io:81/pulse"
    // sh "./bin/smoke-test.sh http://staging-api.emotics.io:90/pulse"
    sh "./bin/smoke-test.sh https://staging.emotics.io"
}

// =======================================================================================
// Merge to master
// =======================================================================================

stage name: 'Merge to master'
node {
    def bumpType = input message: 'Choose a bump type', parameters: [choice(choices: ['patch', 'minor', 'major'], description: 'Bump Type', name: 'BUMP_TYPE')]
    sh 'git checkout master'
    sh 'git pull'
    sh 'git merge release -m "Jenkins merges release to master"'
    sh 'git push'
    build job: 'Emotics-Master', parameters: [string(name: 'bumpType', value: bumpType)]
}