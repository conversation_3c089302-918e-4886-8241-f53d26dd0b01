version: '2'

services:
  api:
    image: 587748902937.dkr.ecr.eu-west-1.amazonaws.com/adoreboard:emotics-api-staging
    container_name: fa-api
    ports:
       - "80:8080"
       - "4560:4560"
    depends_on:
      - postgresql
      - rabbitmq
      - redis
    environment:
      SPRING_PROFILES_ACTIVE: "docker"
      LOG_HOST: "**************"
      LOG_PORT: "9125"
      FARFISA_BACKUP_DELETEDSNIPPETS_SCHEDULER_CRON: "0 30 11 * * ?"
      JAVA_OPTS: "-Dab.module=api -Dab.environment=staging -Dlog4j.configurationFile=configuration/log4j-staging.xml"
#
#  data:
#    image: 587748902937.dkr.ecr.eu-west-1.amazonaws.com/adoreboard:emotics-data-staging
#    container_name: fa-data
#    ports:
#      - "90:8080"
#      - "4562:4560"
#    depends_on:
#      - postgresql
#      - rabbitmq
#    environment:
#      SPRING_PROFILES_ACTIVE: "docker"
#      LOG_HOST: "**************"
#      LOG_PORT: "9125"
#      JAVA_OPTS: "-Dab.application=emotics -Dab.module=data -Dab.environment=staging -Dlog4j.configurationFile=configuration/log4j-staging.xml"

  processor:
    image: 587748902937.dkr.ecr.eu-west-1.amazonaws.com/adoreboard:emotics-processor-staging
    container_name: fa-processor
    ports:
      - "81:8080"
      - "4561:4560"
    depends_on:
      - postgresql
      - rabbitmq
      - redis
    environment:
      SPRING_PROFILES_ACTIVE: "docker"
      LOG_HOST: "**************"
      LOG_PORT: "9125"
      JAVA_OPTS: "-Xms1g -Xmx2g -Dab.application=emotics -Dab.module=processor -Dab.environment=staging -Dlog4j.configurationFile=configuration/log4j-staging.xml"

  rabbitmq:
    image: 587748902937.dkr.ecr.eu-west-1.amazonaws.com/adoreboard:rabbit
    container_name: fa-rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"

  postgresql:
    image: postgres
    container_name: fa-postgresql
    ports:
      - "5432:5432"
    environment:
      POSTGRES_PASSWORD: manager
      POSTGRES_USER: postgres
      POSTGRES_DB: farfisa

  redis:
    image: redis
    container_name: fa-redis
    command: [ "redis-server", "--protected-mode", "no" ]
    ports:
      - "6379:6379"