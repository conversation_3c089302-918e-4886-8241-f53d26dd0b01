// =======================================================================================
// Parameters:
// + bumpType (patch, minor, major)
// =======================================================================================
stage name: 'Build with Parameters'
node {
    echo "Building Emotics with BumpType [ ${bumpType} ]"
}

// =======================================================================================
// Compile
// =======================================================================================

stage name: 'Compile'
node {
    deleteDir()
    git url: '*****************:adoreboard/adoreboard-farfisa.git', branch: 'master'
    sh 'aws s3 cp s3://com.adoreboard.bash-scripts bin --recursive && chmod +x bin/*'
    env.JAVA_HOME = tool 'Java 11'
    String mvnHome = tool 'Maven'
    env.NODEJS_HOME = tool 'nodejs'
    env.PATH = "${mvnHome}/bin:${env.PATH}"
    env.PATH = "${env.NODEJS_HOME}/bin:${env.PATH}"
    sh 'npm --version'
    def version = version()
    sh 'mvn clean compile'
}
stage name: 'Unit Test'
node {
    sh 'mvn test'
}

// =======================================================================================
// Bumping Version
// =======================================================================================

stage name: 'Bump Version'
node {
    def oldVersion = version()
    if(bumpType == "patch"){
        sh "mvn build-helper:parse-version versions:set -DnewVersion='\${parsedVersion.majorVersion}'.'\${parsedVersion.minorVersion}'.'\${parsedVersion.nextIncrementalVersion}' versions:commit"
    } else if(bumpType == "minor"){
        sh "mvn build-helper:parse-version versions:set -DnewVersion='\${parsedVersion.majorVersion}'.'\${parsedVersion.nextMinorVersion}'.0 versions:commit"
    } else if(bumpType == "major"){
        sh "mvn build-helper:parse-version versions:set -DnewVersion='\${parsedVersion.nextMajorVersion}'.0.0 versions:commit"
    }
    def newVersion = version()
    sh "git add ."
    sh "git tag 'v${newVersion}'"
    sh "git commit -m 'Bumped version from [ ${oldVersion} ] to [ ${newVersion} ]'"
    sh 'git push origin master --follow-tags'
    echo "Bump ${bumpType} completed!"
}

stage name: 'Rebuild war files'
node {
    sh 'mvn install -DskipTests'
}

// =======================================================================================
// Production
// =======================================================================================

stage name: 'Prod Deploy'
input 'Do you want to deploy to Production?'
node {
    def version = version()
    sh "./bin/aws-elasticbeanstalk-deploy.sh farfisa-api/target/farfisa-api-${version}.war emotics-api-prod emotics-api-prod emotics-api-${version}"
    sh "./bin/aws-elasticbeanstalk-deploy.sh farfisa-data/target/farfisa-data-${version}.war farfisa-data-prod farfisa-data-prod-us emotics-data-${version}"
    sh "./bin/aws-elasticbeanstalk-deploy.sh farfisa-processor/target/farfisa-processor-${version}.war farfisa-processor-prod farfisa-processor-prod emotics-processor-${version}"
    sh "./bin/aws-elasticbeanstalk-deploy.sh farfisa-calculator/farfisa-calculator-server/target/farfisa-calculator-server-${version}.war emotics-calculator-prod emotics-calculator-prod-us emotics-calculator-${version}"
}

stage name:'Prod Smoke Tests'
node {
    sh "./bin/smoke-test.sh https://api.emotics.io/pulse"
    sh "./bin/smoke-test.sh http://data.internal.adoreboard.com/pulse 90"
    sh "./bin/smoke-test.sh http://processor.internal.adoreboard.com/pulse 90"
}

// Grab first version in `pom.xml` file.

def version() {
    def matcher = readFile('pom.xml') =~ '<version>(.+)</version>'
    matcher ? matcher[0][1] : null
}
