#!/bin/bash

if [ $# -eq 0 ]
    then
        echo "Error. Please make sure you've indicated correct parameters";
elif [ $1 == "production" ]
    then
        echo "Running production deploy"
        # Public URL: https://admin.emotics.io.s3-website-us-east-1.amazonaws.com
        BUILD_ENV=prod npm run build && aws s3 cp ./dist s3://admin.emotics.io --recursive --acl public-read --region us-east-1
elif [ $1 == "production-test" ]
    then
        echo "Running production-test deploy"
        # Public URL: https://admin-test.emotics.io.s3-website-us-east-1.amazonaws.com
        BUILD_ENV=prod-test npm run build && aws s3 cp ./dist s3://admin-test.emotics.io --recursive --acl public-read --region us-east-1
elif [ $1 == "staging" ]
    then
        echo "Running staging deploy"
        # Public URL: https://staging-admin.emotics.io.s3-website-eu-west-1.amazonaws.com
        BUILD_ENV=staging npm run build && aws s3 cp ./dist s3://staging-admin.emotics.io --recursive --acl public-read --region eu-west-1
elif [ $1 == "staging-test" ]
    then
        echo "Running staging-test deploy"
        # Public URL: https://staging-test-admin.emotics.io.s3-website-eu-west-2.amazonaws.com
        BUILD_ENV=staging-test npm run build && aws s3 cp ./dist s3://staging-test-admin.emotics.io --recursive --acl public-read --region eu-west-2
elif [ $1 == "local" ]
    then
        echo "Running local deploy build"
        npm run build
fi
