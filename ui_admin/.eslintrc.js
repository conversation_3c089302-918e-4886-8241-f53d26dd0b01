// http://eslint.org/docs/user-guide/configuring

module.exports = {
  root: true,

  parserOptions: {
    parser: '@babel/eslint-parser',
    sourceType: 'module',
    ecmaFeatures: {
      legacyDecorators: true,
    },
  },
  env: {
    browser: true,
  },
  extends: ['plugin:vue/base', 'airbnb'],
  // required to lint *.vue files
  plugins: ['vue'],
  // check if imports actually resolve
  settings: {
    'import/resolver': {
      webpack: {
        config: 'build/webpack.base.conf.js',
      },
    },
  },
  // add your custom rules here
  rules: {
    'array-bracket-newline': ['error', { multiline: true }],
    'array-element-newline': [
      'error', {
        minItems: 3,
      },
    ],
    'arrow-body-style': 'off',
    'arrow-parens': 'off',
    'implicit-arrow-linebreak': 'off',
    'import/order': 'off', // TODO: review this rule later
    'linebreak-style': 'off',
    'no-mixed-operators': 'off',
    'no-underscore-dangle': ['error', { allowAfterThis: true }],
    'object-curly-newline': 'off',
    'operator-linebreak': 'off',
    'prefer-object-spread': 'off',
    'class-methods-use-this': 'off',
    'max-classes-per-file': 'off',
    'max-len': 'off', // TODO: AGGGHGHHGHGH! Need to fix this. For now, make best effort to stay below 100 characters
    // don't require .vue extension when importing
    'import/extensions': [
      'error',
      'always',
      {
        js: 'never',
        vue: 'never',
      },
    ],
    // allow optionalDependencies
    'import/no-extraneous-dependencies': [
      'error', {
        optionalDependencies: ['test/unit/index.js'],
      },
    ],
    // allow debugger during development
    'no-debugger': process.env.NODE_ENV === 'production' ? 2 : 0,
    'no-param-reassign': 0,
  },
};
