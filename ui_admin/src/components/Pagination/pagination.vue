<template>
  <section class="pagination">
      <i class="fa-solid fa-backward button icon-right"  @click="onBtPrevious()" :class="{ first }"></i>
      <base-input :value="page" class="current-page" @input="onSetPage"></base-input>
      <i class="fa-solid fa-forward button icon-left"  @click="onBtNext()" :class="{ last }"></i>
  </section>
</template>
<script>
import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';

export default {
  name: 'pagination',
  components: {
    BaseInput,
    BaseButton,
  },

  props: {
    currentPage: 0,
    last: false,
  },

  computed: {
    first() {
      return this.page === 1;
    },

    page() {
      return this.currentPage + 1;
    },

  },

  methods: {
    onBtPrevious() {
      this.$emit('onBtPrevious');
    },

    onBtNext() {
      this.$emit('onBtNext');
    },

    onSetPage(e) {
      this.$emit('onSetPage', Number(e.target.value) - 1);
    },
  },
};

</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.pagination {
  @include flex("block", "row", "start", "center");

  .current-page {
    width: 40px;
    text-align: center;
    font-size: 12px;
  }

  .button {
    cursor: pointer;

    &.last {
      pointer-events: none;
      opacity: 0.5;
    }

    &.first {
      pointer-events: none;
      opacity: 0.5;
    }

    &.icon-right {
      margin-right: 0.5rem;
    }

    &.icon-left {
      margin-left: 0.5rem;
    }
  }
}
</style>
