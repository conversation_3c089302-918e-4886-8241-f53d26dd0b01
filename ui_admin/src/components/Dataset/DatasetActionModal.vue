<template>
  <section class="dataset-action-modal">
    <section class="header">
      <span>Selected Datasets ({{ selectedDatasets.length }}):</span>
      <ul>
        <li v-for="(dataset, index) in selectedDatasets" :key="index">
          {{ dataset.label }} (id={{ dataset.id }})
        </li>
      </ul>
    </section>

    <section class="body">
      <base-button class="action cancel" colour="light" @click="performDatasetAction('cancel')">Cancel <br/>(stop re-analysis)</base-button>
      <base-button class="action regenerate" colour="warning" @click="performDatasetAction('regenerate')">Regenerate <br/> (regenerate Excel docs)</base-button>
      <base-button class="action reaggregate" colour="dark" @click="performDatasetAction('reaggregate')">Reaggregate <br/> (recalculate scores + regenerate Excel)</base-button>
      <base-button class="action remodel" colour="base" @click="performDatasetAction('remodel')">Remodel <br/> (rework dataset without reanalysis)</base-button>
      <base-button class="action reanalyse" colour="success" @click="performDatasetAction('reanalyse')">Reanalyse <br/> (fully rework dataset)</base-button>
      <base-button class="action intensity" colour="data" @click="performDatasetAction('intensity')">Recalculate Theme Intensity</base-button>
      <base-button class="action delete" colour="danger" @click="performDatasetAction('delete')">Delete</base-button>
    </section>

    <section class="footer">
      <base-button class="cancel" colour="light" type="link" @click="closeModal">Cancel</base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import DatasetDeleteModal from '@/components/Dataset/DatasetDeleteModal';
import DatasetToast from '@/components/Toast/DatasetToast';
import DatasetRequest from '@/services/DatasetRequest';

export default {
  name: 'dataset-action-modal',

  components: {
    BaseButton,
  },

  computed: {
    ...mapState('dataset', ['selectedDatasets']),
  },

  methods: {
    ...mapActions('modal', ['closeModal', 'setModal']),

    ...mapActions('toast', ['addToast']),

    performDatasetAction(action) {
      switch (action) {
        case 'cancel':
          DatasetRequest.cancelDataset();
          this.close(action);
          break;
        case 'regenerate':
          DatasetRequest.regenerateDataset();
          this.close(action);
          break;
        case 'reaggregate':
          DatasetRequest.reaggregateDataset();
          this.close(action);
          break;
        case 'remodel':
          DatasetRequest.remodelDataset();
          this.close(action);
          break;
        case 'reanalyse':
          DatasetRequest.reanalyseDataset();
          this.close(action);
          break;
        case 'intensity':
          DatasetRequest.intensityDataset();
          this.close(action);
          break;
        default:
          this.setModal(DatasetDeleteModal);
          break;
      }
    },

    close(action) {
      this.closeModal();
      if (action === 'deleted') {
        this.addToast({
          toastComponent: {
            component: DatasetToast,
            id: 'dataset-deleted',
          },
          toastData: {
            label: this.datasets,
          },
        });
      } else {
        this.addToast({
          toastComponent: {
            component: DatasetToast,
            id: 'dataset-updated',
          },
          toastData: {
            label: this.datasets,
          },
        });
      }

      DatasetRequest.fetchDatasets();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.dataset-action-modal {
  @include modal;

  width: 630px;

  .header {
    @include flex("block", "column", "start", "start");

    padding: 1.5rem;

    ul {
      margin: 1rem 0 0 0;
    }
  }

  .body {
    @include flex("block", "row", "start", "start");

    flex-wrap: wrap;
    gap: 1rem;
    padding: $search-modal-padding;

    .text {
      font-size: $font-size-xs;
      margin: 0.5rem 0;

      .label {
        font-weight: $font-weight-bold;
      }
    }

    .action {
      @include flex("block", "column", "center", "center");

      height: 80px;
      width: 180px;
    }
  }

  .footer {
    padding: $search-modal-padding;

    .cancel {
      font-weight: $font-weight-normal;
      padding-left: 0;
    }
  }
}
</style>
