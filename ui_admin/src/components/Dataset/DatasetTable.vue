<template>
  <section class="dataset-table">
    <section class="dataset-table-gird">
      <ag-grid-vue
        style="width: 100%; height: 100%"
        class="ag-theme-alpine"
        :columnDefs="columnDefs"
        :rowData="datasetsMap"
        :rowClassRules="rowClassRules"
        rowSelection='multiple'
        @filter-changed="onFilterChanged"
        @grid-ready="onGridReady"
        @selection-changed="onSelectionChanged"
      />
    </section>
  </section>
</template>

<script>
import { AgGridVue } from 'ag-grid-vue3';
import { mapActions, mapState } from 'vuex';

import DatasetActionCellRender from '@/components/Dataset/DatasetActionCellRender';
import DatasetErrorCellRender from '@/components/Dataset/DatasetErrorCellRender';
import DatasetRequest from '@/services/DatasetRequest';
import DateTimeRenderer from '@/components/BaseAgGrid/DateTimeRenderer';

export default {
  name: 'dataset-table',

  components: {
    AgGridVue,
  },

  data() {
    return {
      gridApi: null,
      rowClassRules: null,
    };
  },

  computed: {
    ...mapState('dataset', ['datasets']),

    selectedDatasets() {
      return this.gridApi.getSelectedRows();
    },

    columnDefs() {
      return [
        {
          id: 'Id',
          field: 'id',
          width: 100,
          wrapText: true,
          autoHeight: true,
          checkboxSelection: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
            filterOptions: [
              {
                displayName: 'Separate ids by ,',
                displayKey: 'contains',
                predicate() {
                  return true;
                },
                numberOfInputs: 1,
              },
            ],
          },
        },
        {
          headerName: 'User',
          field: 'user',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: ['contains'],
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
        },
        {
          headerName: 'Label',
          field: 'label',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: ['contains'],
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
        },
        {
          headerName: 'Status',
          field: 'status',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            defaultOption: 'choose_one',
            filterOptions: [
              {
                displayName: 'Choose one',
                displayKey: 'choose_one',
                predicate() {
                  return true;
                },
                numberOfInputs: 0,
              },
              {
                displayName: 'Uploading',
                displayKey: 'uploading',
                predicate(_, cellValue) {
                  return null || /uploading/gm.test(cellValue?.toLowerCase());
                },
                numberOfInputs: 0,
              },
              {
                displayName: 'Analysing',
                displayKey: 'analysing',
                predicate(_, cellValue) {
                  return null || /analysing/gm.test(cellValue?.toLowerCase());
                },
                numberOfInputs: 0,
              },
              {
                displayName: 'Topic Analysing',
                displayKey: 'topic_analysing',
                predicate(_, cellValue) {
                  return null || /topic_analysing/gm.test(cellValue?.toLowerCase());
                },
                numberOfInputs: 0,
              },
              {
                displayName: 'Generating',
                displayKey: 'generating',
                predicate(_, cellValue) {
                  return null || /generating/gm.test(cellValue?.toLowerCase());
                },
                numberOfInputs: 0,
              },
              {
                displayName: 'S3 Uploading',
                displayKey: 's3_uploading',
                predicate(_, cellValue) {
                  return null || /s3_uploading/gm.test(cellValue?.toLowerCase());
                },
                numberOfInputs: 0,
              },
              {
                displayName: 'Applying Actions',
                displayKey: 'applying_actions',
                predicate(_, cellValue) {
                  return null || /applying_actions/gm.test(cellValue?.toLowerCase());
                },
                numberOfInputs: 0,
              },
              {
                displayName: 'Cancelling',
                displayKey: 'cancelling',
                predicate(_, cellValue) {
                  return null || /cancelling/gm.test(cellValue?.toLowerCase());
                },
                numberOfInputs: 0,
              },
              {
                displayName: 'Finished',
                displayKey: 'finished',
                predicate(_, cellValue) {
                  return null || /finished/gm.test(cellValue?.toLowerCase());
                },
                numberOfInputs: 0,
              },
              {
                displayName: 'Deleted',
                displayKey: 'deleted',
                predicate(_, cellValue) {
                  return null || /deleted/gm.test(cellValue.toLowerCase());
                },
                numberOfInputs: 0,
              },
              {
                displayName: 'Filtered',
                displayKey: 'filtered',
                predicate(_, cellValue) {
                  return null || /filtered/gm.test(cellValue.toLowerCase());
                },
                numberOfInputs: 0,
              },
            ],
            maxNumConditions: 1,
            buttons: ['reset'],
          },
        },
        {
          headerName: 'Having Error?',
          field: 'error',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: DatasetErrorCellRender };
          },
          filter: 'agTextColumnFilter',
          filterParams: {
            defaultOption: 'choose_one',
            filterOptions: [
              {
                displayName: 'Choose one',
                displayKey: 'choose_one',
                predicate() {
                  return true;
                },
                numberOfInputs: 0,
              },
              {
                displayName: 'True',
                displayKey: 'true',
                predicate() {
                  return true;
                },
                numberOfInputs: 0,
              },
              {
                displayName: 'False',
                displayKey: 'false',
                predicate() {
                  return true;
                },
                numberOfInputs: 0,
              },
            ],
            maxNumConditions: 1,
            buttons: ['reset'],
          },
        },
        {
          headerName: 'Documents Characters',
          headerClass: 'header-documents-characters',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          cellRenderer: params => {
            return `
              <div>
                <div>${params.data.documents}</div>
                <div>${params.data.characters}</div>
              </div>
            `;
          },
        },
        {
          headerName: 'Upload Start',
          flex: 1,
          field: 'uploadStart',
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: DateTimeRenderer };
          },
        },
        {
          headerName: 'Adorescore',
          field: 'adoreScore',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          cellStyle: { 'font-weight': '700' },
        },
        {
          headerName: 'Workspace ID',
          field: 'workspaceId',
          flex: 1,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Actions',
          field: '',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: DatasetActionCellRender };
          },
        },
      ];
    },

    datasetsMap() {
      return this.datasets.map(o => ({
        ...o,
        user: `${o.userName}_[${o.userId}]`,
        characters: this.parseCharacters(o),
        documents: this.parseDocuments(o),
      }));
    },
  },

  beforeMount() {
    this.doCallApiFetchDatasets();
  },

  beforeUnmount() {
    this.reset();
  },

  methods: {
    ...mapActions('dataset', [
      'reset',
      'setFilterHavingError',
      'setFilterIds',
      'setFilterLabel',
      'setFilterStatus',
      'setFilterUserIds',
      'setSelectedDatasets',
    ]),

    ...mapActions('modal', ['setModal']),

    async doCallApiFetchDatasets() {
      await DatasetRequest.fetchDatasets();
    },

    parseDocuments(dataset) {
      if (dataset) {
        const formatter = new Intl.NumberFormat();
        if (dataset.status === 'analysing') {
          return `${formatter.format(dataset.lastProgressStatus.currentItem)}/${formatter.format(dataset.lastProgressStatus.numOfItems)}`;
        }
        return formatter.format(dataset.documentCount);
      }
      return null;
    },

    parseCharacters(dataset) {
      if (dataset) {
        const formatter = new Intl.NumberFormat();
        if (dataset.status === 'analysing') {
          return formatter.format(dataset.contentCharacters);
        }
        if (dataset.status === 'uploading') {
          return `${formatter.format(dataset.lastProgressStatus.currentItem)}/${formatter.format(dataset.lastProgressStatus.numOfItems)}`;
        }
        return formatter.format(dataset.contentCharacters);
      }
      return null;
    },

    onSelectionChanged() {
      this.setSelectedDatasets({ selectedDatasets: this.gridApi.getSelectedRows() });
    },

    onGridReady(params) {
      this.gridApi = params.api;
      this.rowClassRules = {
        'table-danger': 'data.error',
        'table-primary': 'data?.status !== \'finished\'',
        'table-filter': 'data?.status === \'filtered\'',
      };
    },

    onFilterChanged(e) {
      const model = e.api.getFilterModel();
      this.setFilterUserIds('user' in model ? [model.user.filter] : []);
      this.setFilterLabel(model.label?.filter || '');
      this.setFilterStatus((model.status?.type === 'choose_one' ? '' : model.status?.type) || '');
      this.setFilterHavingError((model.error?.type === 'choose_one' ? null : model.error?.type) || null);
      this.setFilterIds('id' in model ? [model.id.filter.replaceAll(/[^0-9,]+/g, '')] : []);

      DatasetRequest.fetchDatasets();
    },

  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.dataset-table {
  @include flex("block", "column", "start", "start");

  height: 100%;
  margin-top: 0.6rem;
  width: 100%;

  .dataset-table-gird {
    @include flex("block", "row", "start", "start");

    height: 100%;
    width: 100%;

    .header-documents-characters {
      white-space: normal !important;
      max-width: 7rem;
    }

    .table-primary {
      background-color:#b8daff;
    }

    .table-danger {
      background-color:#f5c6cb;
    }

    .table-filter {
      background-color:  #98e1be;
    }
  }
}
</style>
