<template>
  <section class="dataset-error-status-modal">
    <section class="header">
      <h2>Dataset Error</h2>
    </section>

    <section class="body">
      <section class="error-item">
        <strong>Timestamp: </strong>
        <span>{{ dataset.error.timestamp }}</span>
      </section>
      <section class="error-item">
        <strong>Message: </strong>
        <span>{{ dataset.error.message }}</span>
      </section>
      <section class="error-item">
        <strong>Exception: </strong>
        <span class="exception">{{ dataset.error.exception }}</span>
      </section>
    </section>

    <section class="footer">
      <base-button class="cancel" colour="light" type="link" @click="closeModal">Cancel</base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';

export default {
  name: 'dataset-error-status-modal',

  components: {
    BaseButton,
  },

  computed: {
    ...mapState('dataset', ['selectedDataset']),

    dataset() {
      return this.selectedDataset;
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.dataset-error-status-modal {
  @include modal;

  .header {
    @include flex("block", "row", "between", "center");

    padding: 1.5rem;

    h2 {
      @include flex("block", "row", "start", "center");

      font-weight: $font-weight-bold;
    }
  }

  .body {
    padding: 1.5rem;

    .error-item {
      font-size: $font-size-xs;
      margin: 0.5rem 0;
    }
  }

  .footer {
    font-size: $font-size-md;
    padding: 1.5rem;

    .cancel {
      font-weight: $font-weight-normal;
      padding-left: 0;
    }
  }
}
</style>
