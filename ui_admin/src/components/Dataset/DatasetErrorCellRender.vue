<template>
  <section class="dataset-error-cell-render">
    <section v-if="dataset.error">
      <base-button :size="'small'" :colour="'danger'" @click="onClickDetail">
        <i class="fa-solid fa-circle-exclamation icon" />
        Error
      </base-button>
    </section>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import DatasetErrorModal from '@/components/Dataset/DatasetErrorModal';

export default {
  name: 'dataset-error-cell-render',

  components: {
    BaseButton,
  },

  computed: {
    dataset() {
      return this.params.data;
    },
  },

  methods: {
    ...mapActions('modal', ['setModal']),

    ...mapActions('dataset', ['setSelectedDataset']),

    onClickDetail() {
      this.setSelectedDataset({ selectedDataset: this.dataset });
      this.setModal(DatasetErrorModal);
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.dataset-error-cell-render {
  @include flex("block", "column", "start", "start");

  .base-button {
    @include flex("block", "row", "center", "center");

    height: 2rem;
    margin-top: 0.2rem;

    .icon {
      margin-right: 0.2em;
    }
  }
}
</style>
