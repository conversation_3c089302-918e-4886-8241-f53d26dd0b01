<template>
  <section class="dataset-delete-modal">
    <section class="header">
      <h2>Delete Dataset</h2>
    </section>

    <section class="body">
      <section class="text">
        Are you sure you would like to delete these datasets?
        <span class="label">&lsquo;{{ label }}&rsquo;</span>
      </section>
    </section>

    <section class="footer">
      <base-button class="cancel" colour="light" type="link" @click="closeModal">Cancel</base-button>
      <base-button class="delete" colour="danger" @click="onClickDelete">Delete Dataset</base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import DatasetToast from '@/components/Toast/DatasetToast';
import datasetRequest from '@/services/DatasetRequest';

export default {
  name: 'dataset-delete-modal',

  components: {
    BaseButton,
  },

  computed: {
    ...mapState('dataset', ['selectedDatasets']),

    label() {
      return this.selectedDatasets.map(t => t.label).join(',');
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('toast', ['addToast']),

    async onClickDelete() {
      const error = await datasetRequest.deleteDataset(this.selectedDatasets);
      if (error) {
        await this.closeModal();
        await this.addToast({
          toastComponent: {
            component: DatasetToast,
            id: 'dataset-deleted-error',
          },
          toastData: {
            label: this.label,
          },
        });
      } else {
        await datasetRequest.fetchDatasets();
        await this.closeModal();
        await this.addToast({
          toastComponent: {
            component: DatasetToast,
            id: 'dataset-deleted',
          },
          toastData: {
            label: this.label,
          },
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.dataset-delete-modal {
  @include modal;

  .body {
    padding: $search-modal-padding;

    .text {
      font-size: $font-size-xs;
      margin: 0.5rem 0;

      .label {
        font-weight: $font-weight-bold;
      }
    }
  }

  .footer {
    font-size: $font-size-md;

    padding: $search-modal-padding;

    .cancel {
      font-weight: $font-weight-normal;
      padding-left: 0;
    }

    .delete {
      font-weight: $font-weight-medium;
    }
  }

  .header {
    @include flex("block", "row", "between", "center");

    padding: $search-modal-padding;

    h2 {
      @include flex("block", "row", "start", "center");

      font-weight: $font-weight-bold;
    }

    .icon {
      @include flex("block", "row", "end", "center");

      cursor: pointer;
      height: 1.25rem;
      opacity: 0.3;
      &:hover {
        opacity: 1;
      }

      &:active,
      &:focus {
        border: none;
        outline: none;
      }
    }
  }
}
</style>
