<template>
  <section class="dataset-action-cell-render">
    <section class="item">
      <base-button :size="'small'" :colour="'dark'" @click="onClickActions">
        <i class="fa-regular fa-gear icon"></i>
        Actions
      </base-button>
    </section>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import DatasetActionModal from '@/components/Dataset/DatasetActionModal';

export default {
  name: 'dataset-action-cell-render',

  components: {
    BaseButton,
  },

  computed: {
    dataset() {
      return this.params.data;
    },

  },

  methods: {
    ...mapActions('modal', ['setModal']),

    ...mapActions('dataset', ['setSelectedDatasets']),

    onClickActions() {
      this.setSelectedDatasets({ selectedDatasets: [this.dataset] });
      this.setModal(DatasetActionModal);
    },

  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";
.dataset-action-cell-render {
  @include flex("block", "column", "start", "start");

  .item {
    @include flex("block", "row", "center", "center");

    border-radius: 4px;
    color: clr('white');
    height: 2rem;
    margin: 0.2rem;

    .base-button {
      height: 2rem;
    }

    .icon {
      margin-right: 0.2em;
    }
  }
}
</style>
