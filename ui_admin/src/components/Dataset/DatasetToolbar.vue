<template>
  <section class="dataset-toolbar">
    <section class="dataset-actions">
      <base-button :colour="'dark'" :size="'small'" @click="onClickActions" :disabled="actionsDisabled">
        <i class="fa-regular fa-gear icon" />
        Actions
      </base-button>
    </section>
    <section class="dataset-pagination">
      <pagination
        :current-page="page"
        :last="datasets.length < rows"
        @onBtPrevious="onBtPrevious"
        @onBtNext="onBtNext"
        @onSetPage="onSetPage"
      />
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import Pagination from '@/components/Pagination/pagination';
import DatasetActionModal from '@/components/Dataset/DatasetActionModal';
import DatasetRequest from '@/services/DatasetRequest';

export default {
  name: 'dataset-toolbar',

  components: {
    BaseButton,
    Pagination,
  },

  computed: {
    ...mapState('dataset', [
      'datasets',
      'page',
      'rows',
    ]),

    ...mapState('dataset', ['selectedDatasets']),

    actionsDisabled() {
      return this.selectedDatasets.length === 0;
    },
  },

  methods: {
    ...mapActions('dataset', ['setPage']),

    ...mapActions('modal', ['setModal']),

    onBtPrevious() {
      this.setPage({ page: this.page - 1 });
      DatasetRequest.fetchDatasets();
    },

    onBtNext() {
      this.setPage({ page: this.page + 1 });
      DatasetRequest.fetchDatasets();
    },

    onClickActions() {
      if (!this.actionsDisabled) this.setModal(DatasetActionModal);
    },

    onSetPage(page) {
      this.setPage({ page });
      DatasetRequest.fetchDatasets();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.dataset-toolbar {
  @include flex("block", "row", "start", "start");

  margin-top: 0.6rem;
  width: 100%;

  .dataset-actions {
    @include flex("block", "row", "start", "start");

    .icon {
      margin-right: 0.2em;
    }
  }

  .dataset-pagination {
    @include flex("block", "row", "end", "start");

    padding-right: 2rem;
    width: 100%;
  }
}
</style>
