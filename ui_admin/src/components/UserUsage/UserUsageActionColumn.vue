<template>
  <section class="user-usage-action-column">
    <section class="item">
      <base-button colour="base" size="small" @click="onExtendUsage">
        <span>Extend Usage</span>
      </base-button>
    </section>
    <section class="item">
      <base-button colour="base" size="small" @click="onRenewUsage">
        <span>Renew Usage</span>
      </base-button>
    </section>
    <section class="item">
      <base-button colour="base" size="small" @click="onAddActiveUsage">
        <span>Add Active Usage</span>
      </base-button>
    </section>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import AddActiveUsageModal from '@/components/UserUsage/AddActiveUsageModal';
import BaseButton from '@/components/Base/BaseButton';
import ExtendUsageModal from '@/components/UserUsage/ExtendUsageModal';
import RenewUsageModal from '@/components/UserUsage/RenewUsageModal';

export default {
  name: 'user-usage-action-column',

  components: {
    BaseButton,
  },

  data() {
    return {
      updating: false,
    };
  },

  computed: {
    user() {
      return this.params.data;
    },
  },

  methods: {
    ...mapActions('modal', ['setModal']),

    ...mapActions('user', ['setSelectedUser']),

    onAddActiveUsage() {
      this.setSelectedUser(this.user);
      this.setModal(AddActiveUsageModal);
    },

    onExtendUsage() {
      this.setSelectedUser(this.user);
      this.setModal(ExtendUsageModal);
    },

    onRenewUsage() {
      this.setSelectedUser(this.user);
      this.setModal(RenewUsageModal);
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.user-usage-action-column {
  @include flex("block", "column", "start", "start");

  .item {
    @include flex("block", "row", "center", "center");

    border-radius: 4px;
    color: clr('white');
    height: 2rem;
    margin: 0.2rem;

    .base-button {
      height: 2rem;
    }

    .icon {
      margin-right: 0.2em;
    }
  }
}
</style>
