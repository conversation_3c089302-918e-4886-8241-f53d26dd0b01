<template>
  <section class="user-usage-actions">
    <base-button :size="'small'" @click="onClickQuery">
      <i class="fa-regular fa-user-magnifying-glass icon" />
      Query
    </base-button>
  </section>
</template>

<script>
import BaseButton from '@/components/Base/BaseButton';
import Route from '@/enum/route';

export default {
  name: 'user-usage-actions',

  components: {
    BaseButton,
  },

  methods: {
    onClickQuery() {
      this.$router.push({ name: Route.USER_USAGE_QUERY });
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.user-usage-actions {
  @include flex("block", "row", "start", "start");

  column-gap: 1rem;
  min-width: max-content;

  .icon {
    margin-right: 0.2em;
  }
}

</style>
