<template>
  <section class="renew-usage-modal">
    <section class="header">
      <h2>Renew Usage Record</h2>
    </section>
    <section class="body">
      <span>Are you sure you want to renew usage for user <b>{{selectedUser.loginName}}</b>?</span>
    </section>
    <section class="footer">
      <base-button class="cancel" colour="light" size="small" type="link" @click="onCancel">Cancel</base-button>
      <loading-blocks-overlay :size="'small'" v-if="updating" />
      <base-button v-else class="submit" colour="base" size="small" type="link" @click="onRenewUsage">Renew</base-button>
    </section>
  </section>
</template>
<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import UserRequest from '@/services/UserRequest';
import UserToast from '@/components/Toast/UserToast';

export default {
  name: 'renew-usage-modal',

  components: {
    BaseButton,
    LoadingBlocksOverlay,
  },

  data() {
    return {
      updating: false,
    };
  },

  computed: {
    ...mapState('user', ['selectedUser']),
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('toast', ['addToast']),

    onCancel() {
      this.closeModal();
    },

    async onRenewUsage() {
      this.updating = true;
      await UserRequest.renewUsage(this.selectedUser.id);
      await UserRequest.fetchUser();
      this.closeModal();
      this.addToast({
        toastComponent: {
          component: UserToast,
          id: 'user-renew-usage',
        },
        toastData: {
          label: this.selectedUser.loginName,
        },
      });
      this.updating = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.renew-usage-modal {
  @include modal;

  position: relative;
}
</style>
