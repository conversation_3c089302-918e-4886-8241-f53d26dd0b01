<template>
  <section class="user-usage-toolbar">
    <user-usage-actions />
    <user-usage-pagination />
  </section>
</template>

<script>
import UserUsageActions from '@/components/UserUsage/UserUsageActions';
import UserUsagePagination from '@/components/UserUsage/UserUsagePagination';

export default {
  name: 'user-usage-toolbar',

  components: {
    UserUsageActions,
    UserUsagePagination,
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.user-usage-toolbar {
  @include flex('block', 'row', 'space-between', 'stretch');

  margin-top: 0.6rem;
  width: 100%;
}

</style>
