<template>
  <section class="user-usage-table">
    <ag-grid-vue
      :columnDefs="columnDefs"
      :rowData="usersMap"
      @cellValueChanged="updateUser"
      @grid-ready="onGridReady"
      @filter-changed="onFilterChanged"
      @selection-changed="onSelectionChanged"
      class="ag-theme-alpine"
      rowSelection='multiple'
      style="width: 100%; height: 100%; min-width: max-content"
    />
  </section>
</template>

<script>
import { AgGridVue } from 'ag-grid-vue3';
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import UsagePercentageCellRender from '@/components/User/UsagePercentageCellRender';
import UserRequest from '@/services/UserRequest';
import UserTierRequest from '@/services/UserTierRequest';
import UserUsageActionColumn from '@/components/UserUsage/UserUsageActionColumn';
import UserToast from '@/components/Toast/UserToast';

export default {
  name: 'user-usage-table',

  components: {
    AgGridVue,
    BaseButton,
  },

  data() {
    return {
      gridApi: null,
    };
  },

  beforeMount() {
    this.doCallApiFetchUsers();
    this.doCallApiFetchUserTiers();
  },

  beforeUnmount() {
    this.resetFilter();
  },

  computed: {
    ...mapState('user', ['users']),

    ...mapState('userTier', ['userTiers']),

    columnDefs() {
      return [
        {
          id: 'Id',
          field: 'id',
          wrapText: true,
          autoHeight: true,
          headerCheckboxSelection: true,
          checkboxSelection: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
            filterOptions: [
              {
                displayName: 'Separate ids by ,',
                displayKey: 'contains',
                predicate() {
                  return true;
                },
                numberOfInputs: 1,
              },
            ],
          },
          sortable: true,
          resizable: true,
          minWidth: 100,
          width: 100,
        },
        {
          headerName: 'Username',
          field: 'loginName',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: ['contains'],
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
          resizable: true,
          minWidth: 250,
        },
        {
          headerName: 'User Tier',
          id: 'userTier',
          field: 'tierName',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          editable: true,
          cellEditor: 'agSelectCellEditor',
          cellEditorParams: {
            values: this.userTiers.map(t => { return t.name; }),
          },
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: ['contains'],
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
          resizable: true,
          minWidth: 120,
        },
        {
          headerName: 'Usage',
          field: 'usagePercentage',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: UsagePercentageCellRender };
          },
          resizable: true,
          minWidth: 150,
          width: 150,
        },
        {
          headerName: 'Uploads',
          field: 'uploadCount',
          wrapText: true,
          autoHeight: true,
          resizable: true,
          minWidth: 100,
          width: 100,
        },
        {
          headerName: 'Logins',
          field: 'loginCount',
          wrapText: true,
          autoHeight: true,
          resizable: true,
          minWidth: 100,
          width: 100,
        },
        {
          headerName: 'Active',
          field: 'active',
          cellClass: 'editable',
          autoHeight: true,
          editable: true,
          filter: 'agNumberColumnFilter',
          filterParams: {
            filterOptions: [
              'Choose one',
              {
                displayKey: 'true',
                displayName: 'Active',
                predicate: (_, cellValue) => +cellValue === 1,
                numberOfInputs: 0,
              },
              {
                displayKey: 'false',
                displayName: 'De-active',
                predicate: (_, cellValue) => +cellValue === 0,
                numberOfInputs: 0,
              },
            ],
          },
          resizable: true,
          minWidth: 100,
          width: 100,
        },
        {
          headerName: 'Actions',
          field: '',
          wrapText: true,
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: UserUsageActionColumn };
          },
          resizable: true,
          minWidth: 150,
          width: 150,
          pinned: 'right',
        },
      ];
    },

    usersMap() {
      return this.users.map(o => ({
        ...o,
        usagePercentage: this.getUsagePercentage(o.lastUsageRecord.currentUsage, o.lastUsageRecord.maxUsage),
        uploadCount: o.lastUsageRecord.uploadCount,
        loginCount: o.loginCount,
        tierName: o.userTier.name,
      }));
    },
  },

  methods: {
    ...mapActions('modal', ['setModal']),

    ...mapActions('user', [
      'resetFilter',
      'setFilterTierId',
      'setSearch',
      'setSelectedUsers',
      'setFilterIds',
    ]),

    ...mapActions('toast', ['addToast']),

    async doCallApiFetchUsers() {
      await UserRequest.fetchUser();
    },

    async doCallApiFetchUserTiers() {
      await UserTierRequest.getUserTiers();
    },

    getUsagePercentage(currentUsage, maxUsage) {
      // If user exists on a tier (internal/demo) where no max usage is specified
      if (maxUsage === 0) {
        return 'N/A';
      }
      return `${Math.round((currentUsage / maxUsage) * 100)}%`;
    },

    onFilterChanged(e) {
      const model = e.api.getFilterModel();

      // Use optional chaining and nullish coalescing for loginName
      this.setSearch(model.loginName?.filter ?? '');

      // Use optional chaining, nullish coalescing, and find's ability to search by value
      const tierId = this.userTiers.find(tier => tier.name === model.tierName?.filter)?.id ?? '';
      this.setFilterTierId(tierId);
      this.setFilterIds('id' in model ? [model.id.filter.replaceAll(/[^0-9,]+/g, '')] : []);

      UserRequest.fetchUser();
    },

    onGridReady(params) {
      this.gridApi = params.api;
    },

    onSelectionChanged() {
      this.setSelectedUsers(this.gridApi.getSelectedRows());
    },

    updateUser(params) {
      const colId = params.column.getId();
      if (colId === 'active') {
        this.updateActive(params.data, params.newValue);
      }
    },

    async updateActive(user, checked) {
      await UserRequest.updateActive(user.id, checked).then(() => {
        this.addToast({
          toastComponent: {
            component: UserToast,
            id: 'user-activated',
          },
          toastData: {
            label: user.name,
            active: checked,
          },
        });
        UserRequest.fetchUser();
      });
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.user-usage-table {
  @include flex("block", "row", "start", "start");

  height: 100%;
  margin-top: 0.6rem;
  width: 100%;
}

</style>
