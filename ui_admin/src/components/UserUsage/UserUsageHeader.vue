<template>
  <section class="user-usage-header">
    <section class="title">
      <i class="fa-solid fa-chart-pie icon" />
      <span>User Usage</span>
    </section>
  </section>
</template>

<script>
export default {
  name: 'user-usage-header',
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.user-usage-header {
  @include flex("block", "row", "start", "start");

  height: fit-content;
  width: 100%;

  .title {
    @include flex("block", "row", "center", "start");

    font-size: 1.5rem;
    font-weight: $font-weight-bold;
  }

  .icon {
    margin-right: 0.4rem;
  }
}
</style>
