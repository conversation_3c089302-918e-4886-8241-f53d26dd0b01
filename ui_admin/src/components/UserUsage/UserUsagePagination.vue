<template>
  <section class="user-usage-pagination">
    <pagination :current-page="page" :last="last" @onBtPrevious="onBtPrevious" @onBtNext="onBtNext" @onSetPage="onSetPage" />
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import Pagination from '@/components/Pagination/pagination';
import UserRequest from '@/services/UserRequest';

export default {
  name: 'user-usage-pagination',

  components: {
    Pagination,
  },

  computed: {
    ...mapState('user', [
      'page',
      'rows',
      'users',
    ]),

    last() {
      return this.users.length < this.rows;
    },
  },

  methods: {
    ...mapActions('user', ['setPage']),

    async doCallApiFetchUsers() {
      await UserRequest.fetchUser();
    },

    onBtPrevious() {
      this.setPage({ page: this.page - 1 });
      this.doCallApiFetchUsers();
    },

    onBtNext() {
      this.setPage({ page: this.page + 1 });
      this.doCallApiFetchUsers();
    },

    onSetPage(page) {
      this.setPage({ page });
      this.doCallApiFetchUsers();
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.user-usage-pagination {
  @include flex("block", "row", "end", "start");
}

</style>
