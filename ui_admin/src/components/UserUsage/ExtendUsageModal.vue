<template>
  <section class="extend-usage-modal">
    <section class="header">
      <h2>Extend Usage Record</h2>
    </section>
    <section class="body">
      <section class="field row">
        Extend by:
      </section>
      <section class="field row">
        <base-input placeholder="Characters" :value="localCharacters" @input="onInputCharacters" />
      </section>
    </section>
    <section class="footer">
      <base-button class="cancel" colour="light" size="small" type="link" @click="onCancel">Cancel</base-button>
      <base-button class="submit" colour="base" size="small" type="link" @click="onExtendUsage">Extend</base-button>
      <loading-blocks-overlay v-if="updating" />
    </section>
  </section>
</template>
<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import UserToast from '@/components/Toast/UserToast';
import userRequest from '@/services/UserRequest';

export default {
  name: 'extend-usage-modal',

  components: {
    BaseButton,
    BaseInput,
    LoadingBlocksOverlay,
  },

  data() {
    return {
      updating: false,
      localCharacters: '',
    };
  },

  computed: {
    ...mapState('user', ['selectedUser']),
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('toast', ['addToast']),

    onInputCharacters(e) {
      this.localCharacters = e.target.value;
    },

    onExtendUsage() {
      this.updating = true;
      if (this.localCharacters?.length) {
        userRequest.extendUsage(this.selectedUser.id, this.localCharacters.trim());
        this.closeModal();
        this.addToast({
          toastComponent: {
            component: UserToast,
            id: 'user-extend-usage',
          },
          toastData: {
            label: this.selectedUser.loginName,
          },
        });
      }

      this.updating = false;
    },

    onCancel() {
      this.closeModal();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.extend-usage-modal {
  @include modal;
  position: relative;

  .field {
    margin-bottom: 0.6rem;
  }
}

</style>
