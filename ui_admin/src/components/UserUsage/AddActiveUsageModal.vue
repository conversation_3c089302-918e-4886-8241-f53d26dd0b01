<template>
  <section class="add-active-usage-modal">
    <section class="header">
      <h2>Add Active Usage Record</h2>
    </section>
    <section class="body">
      <span>Are you sure you want to add active usage for user <b>{{selectedUser.loginName}}</b>?</span>
    </section>
    <section class="footer">
      <base-button class="cancel" colour="light" size="small" type="link" @click="onCancel">Cancel</base-button>
      <loading-blocks-overlay :size="'small'" v-if="updating" />
      <base-button v-else class="submit" colour="base" size="small" type="link" @click="onAddActiveUsage">Add Active</base-button>
    </section>
  </section>
</template>
<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import UserRequest from '@/services/UserRequest';
import UserToast from '@/components/Toast/UserToast';

export default {
  name: 'add-active-usage-modal',

  components: {
    BaseButton,
    LoadingBlocksOverlay,
  },

  data() {
    return {
      updating: false,
    };
  },

  computed: {
    ...mapState('user', ['selectedUser']),
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('toast', ['addToast']),

    onCancel() {
      this.closeModal();
    },

    async onAddActiveUsage() {
      this.updating = true;

      UserRequest.addActiveUsage(this.selectedUser.id).then((res) => {
        this.closeModal();
        if (res.status) {
          this.addToast({
            toastComponent: {
              component: UserToast,
              id: 'user-error',
            },
            toastData: {
              label: res.data,
            },
          });
        } else {
          this.closeModal();
          this.addToast({
            toastComponent: {
              component: UserToast,
              id: 'user-active-usage',
            },
            toastData: {
              label: this.localUser.loginName,
            },
          });
        }
      });

      this.updating = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.add-active-usage-modal {
  @include modal;

  position: relative;
}
</style>
