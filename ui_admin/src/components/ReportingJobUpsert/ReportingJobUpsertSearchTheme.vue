<template>
  <section class="reporting-job-upsert-search-theme">
    <loading-blocks-overlay v-if="loading" />
    <section v-else class="wrapper">
      <section class="label">
        Step 5: Search Themes
      </section>
      <section class="search-theme global">
        <span class="search-theme-title">Global: </span>
        <section class="item" v-for="(item, index) in localSearchThemesGlobal" :key="index">
          <section class="rule">
            <section class="saved-action-list">
              <span class="rule-title">Saved action list: </span>
              <v-select
                class="value"
                v-model="item.savedActionListId"
                :clearable="false"
                :options="savedActionListOptions"
                :reduce="option => option.id"
                @search="fetchSavedActionList"
              />
            </section>
          </section>
          <i class="fa-solid fa-trash icon-delete" @click="onClickRemove(item)" />
        </section>
        <section class="add-btn" @click="onAddGlobal">
          <i class="fa-solid fa-plus" />
        </section>
      </section>

      <section class="search-theme custom">
        <span class="search-theme-title">Custom: </span>
        <section class="item" v-for="(item, index) in localSearchThemesCustom" :key="index">
          <section class="rule">
            <section class="dataset-name">
              <span class="rule-title">Dataset names: </span>
              <base-input class="value" :value="item.datasetNames" @input="onSetDatasetNames($event, item)" />
            </section>
            <section class="saved-action-list">
              <span class="rule-title">Saved action list: </span>
              <v-select
                class="value"
                v-model="item.savedActionListId"
                :clearable="false"
                :options="savedActionListOptions"
                :reduce="option => option.id"
                @search="fetchSavedActionList"
              />
            </section>
            <i class="fa-solid fa-trash icon-delete" @click="onClickRemove(item)" />
          </section>
        </section>
        <section class="add-btn" @click="onAddCustom">
          <i class="fa-solid fa-plus" />
        </section>
      </section>

      <section class="actions">
        <base-button colour="light" size="small" @click="onClickBack">
          <span>Back</span>
        </base-button>
        <loading-blocks-overlay v-if="saving" />
        <section v-else class="btn-group">
          <base-button class="btn" colour="danger" size="small" @click="onClickDiscard">
            <span>Discard changes</span>
          </base-button>
          <base-button class="btn" colour="base" size="small" @click="onClickSave">
            <span>Save changes</span>
          </base-button>
          <base-button class="btn" colour="base" size="small" @click="onClickNext">
            <span>Next</span>
          </base-button>
        </section>
      </section>
    </section>
  </section>
</template>

<script>
import { debounce } from 'lodash-es';
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import ReportingJobRequest from '@/services/ReportingJobRequest';
import VSelect from 'vue-select';
import SavedActionListRequest from '@/services/SavedActionListRequest';

export default {
  name: 'reporting-job-upsert-search-theme',

  components: {
    BaseButton,
    BaseInput,
    LoadingBlocksOverlay,
    VSelect,
  },

  data() {
    return {
      loading: true,
      localSearchThemeSettings: [],
      savedActionListOptions: [],
      saving: false,
    };
  },

  computed: {
    ...mapState('reportingJob', ['selectedId']),

    localSearchThemesCustom() {
      return this.localSearchThemeSettings.filter(st => st.datasetNames !== null);
    },

    localSearchThemesGlobal() {
      return this.localSearchThemeSettings.filter(st => st.datasetNames === null);
    },
  },

  async created() {
    this.setFilterCollectionLabel(null);
    this.doCallApiFetchSavedActionList();
    const foundSearchThemes = await ReportingJobRequest.getSearchThemes(this.selectedId);
    if (foundSearchThemes.length !== 0) {
      this.localSearchThemeSettings = foundSearchThemes[0].searchThemeSettings;
      this.convertResponseToLocalSearchThemes();
    }
    this.loading = false;
  },

  methods: {
    ...mapActions('savedActionList', ['setFilterCollectionLabel']),

    async doCallApiFetchSavedActionList() {
      this.savedActionListOptions = (await SavedActionListRequest.fetchSavedActionLists()).map(w => ({
        id: w.id,
        label: `${w.label} (Id: ${w.id} )`,
      }));
    },

    fetchSavedActionList: debounce(async function fetchSavedActionList(search, loading) {
      this.setFilterCollectionLabel(search);
      await this.doCallApiFetchSavedActionList();
      loading(false);
    }, 500),

    onAddCustom() {
      const newSearchThemeSetting = {
        datasetNames: '',
        savedActionListId: null,
      };

      this.localSearchThemeSettings.push(newSearchThemeSetting);
    },

    onAddGlobal() {
      const newSearchThemeSetting = {
        datasetNames: null,
        savedActionListId: null,
      };

      this.localSearchThemeSettings.push(newSearchThemeSetting);
    },

    onClickBack() {
      this.$emit('back');
    },

    async onClickDiscard() {
      this.saving = true;
      const foundSearchThemes = await ReportingJobRequest.getSearchThemes(this.selectedId);
      if (foundSearchThemes.length !== 0) {
        this.localSearchThemeSettings = foundSearchThemes[0].searchThemeSettings;
        this.convertResponseToLocalSearchThemes();
      } else {
        this.localSearchThemeSettings = [];
      }
      this.saving = false;
    },

    onClickRemove(item) {
      const index = this.localSearchThemeSettings.indexOf(item);
      if (index > -1) {
        this.localSearchThemeSettings.splice(index, 1);
      }
    },

    async onClickSave() {
      this.saving = true;
      this.convertLocalSearchThemesToRequest();
      await ReportingJobRequest.updateSearchThemes(
        this.selectedId,
        { searchThemeSettings: this.localSearchThemeSettings },
      );
      this.convertResponseToLocalSearchThemes();
      this.saving = false;
    },

    async onClickNext() {
      this.$emit('next');
    },

    convertLocalSearchThemesToRequest() {
      this.localSearchThemeSettings.forEach(item => {
        item.datasetNames = item.datasetNames ? item.datasetNames.split(',').map(name => name.trim()) : null;
      });
    },

    convertResponseToLocalSearchThemes() {
      this.localSearchThemeSettings.forEach(item => {
        item.datasetNames = item.datasetNames ? item.datasetNames?.join(', ') : null;
      });
    },

    onSetDatasetNames(e, item) {
      item.datasetNames = e.target.value;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-upsert-search-theme {
  @include flex("block", "column", "start", "start");

  width: 35rem;

  .label {
    font-size: $font-size-md;
    font-weight: $font-weight-bold;
    margin-bottom: 0.8rem;
  }

  .search-theme {
    @include flex("block", "column", "start", "start");

    margin-top: 1rem;
    width: 100%;

    .search-theme-title {
      font-weight: $font-weight-bold;
    }

    .item {
      @include flex("block", "row", "start", "center");

      font-size: $font-size-sm;
      margin-bottom: 0.4rem;

      .title {
        min-width: 3rem;
      }

      .rule {
        @include flex("block", "row", "start", "center");

        .dataset-name {
          @include flex("block", "column", "start", "start");

          .rule-title {
            font-size: $font-size-xs;
          }

          .value {
            margin-right: 0.8rem;
            width: 35rem;
          }
        }

        .saved-action-list {
          @include flex("block", "column", "start", "start");

          .rule-title {
            font-size: $font-size-xs;
          }

          .value {
            width: 25rem;
          }
        }
      }

      .icon-delete {
        color: clr('red');
        cursor: pointer;
        margin-left: 0.4rem;

        &:hover {
          opacity: 0.8;
        }
      }
    }

    .add-btn {
      @include flex("block", "row", "center", "center");

      border: 1px solid clr('purple');
      color: clr('purple');
      cursor: pointer;
      border-radius: $border-radius-medium;
      height: 2rem;
      width: 2rem;

      &:hover {
        border-color: lighten(clr('purple'), 10%);
        color: lighten(clr('purple'), 10%);
      }
    }
  }

  .actions {
    @include flex("block", "row", "space-between", "start");

    margin-top: 0.6rem;
    width: 35rem;

    .base-button {
      .icon {
        margin-right: 0.4rem;
      }
    }

    .btn-group {
      .base-button {
        margin-left: 0.8rem;
      }
    }
  }
}
</style>
