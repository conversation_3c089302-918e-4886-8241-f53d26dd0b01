<template>
  <section class="reporting-job-upsert-general-information">
    <loading-blocks-overlay v-if="loading" />
    <section v-else class="wrapper">
      <section class="label">
        Step 1: General Information
      </section>
      <section class="item name">
        <span class="title">Name:</span>
        <base-input class="value" :value="localReportingJob.name" @input="onSetName" />
      </section>
      <section class="item user">
        <span class="title">User:</span>
        <v-select class="value"
          :clearable="false"
          :options="userOptions"
          :reduce="option => option.id"
          v-model="localReportingJob.userId"
          @search="fetchUsers"
        />
      </section>
      <section class="item workspace">
        <span class="title">Workspace:</span>
        <v-select class="value"
          :clearable="false"
          :options="workspaceOptions"
          :reduce="option => option.id"
          v-model="localReportingJob.workspaceId"
          @search="fetchWorkspaces"
        />
      </section>
      <section class="item schedule-type">
        <span class="title">Schedule type:</span>
        <v-select class="value"
          :options="scheduleTypeOptions"
          :clearable="false"
          :reduce="option => option.value"
          v-model="localReportingJob.scheduleType"
        />
      </section>
      <section class="item schedule-time" v-if="localReportingJob.scheduleType !== 'MANUALLY'">
        <span class="title">Schedule time (UTC timezone):</span>
        <v-select class="value"
          :options="scheduleTimeOptions"
          :clearable="false"
          :reduce="option => option.value"
          v-model="localReportingJob.scheduleTime"
        />
      </section>
      <section class="item schedule-day" v-if="localReportingJob.scheduleType === 'MONTHLY'">
        <span class="title">Schedule day:</span>
        <v-select class="value"
          :options="scheduleDayOptions"
          :clearable="false"
          :reduce="option => option.value"
          v-model="localReportingJob.scheduleDay"
        />
      </section>

      <section class="actions">
        <base-button colour="light" size="small" @click="onClickCancel">
          <span>Cancel</span>
        </base-button>
        <loading-blocks-overlay v-if="saving" />
        <section v-else-if="selectedId" class="btn-group">
          <base-button class="btn" colour="danger" size="small" @click="onClickDiscard">
            <span>Discard changes</span>
          </base-button>
          <base-button class="btn" colour="base" size="small" @click="onClickSave">
            <span>Save changes</span>
          </base-button>
          <base-button class="btn" colour="base" size="small" @click="onClickNext">
            <span>Next</span>
          </base-button>
        </section>
        <section v-else class="btn-group">
          <base-button class="btn" colour="base" size="small" @click="onClickCreateAndNext">
            <span>Create & Next</span>
          </base-button>
        </section>
      </section>
    </section>
  </section>
</template>

<script>
import { debounce } from 'lodash-es';
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import ReportingJobRequest from '@/services/ReportingJobRequest';
import UserRequest from '@/services/UserRequest';
import VSelect from 'vue-select';
import WorkspaceRequest from '@/services/WorkspaceRequest';

export default {
  name: 'reporting-job-upsert-general-information',

  components: {
    BaseButton,
    BaseInput,
    LoadingBlocksOverlay,
    VSelect,
  },

  data() {
    return {
      loading: true,
      localReportingJob: {
        id: null,
        userId: null,
        workspaceId: null,
        scheduleType: 'MONTHLY',
        scheduleTime: null,
        scheduleDay: null,
        dataCollectionType: null,
      },
      saving: false,
      scheduleTypeOptions: [
        { label: 'Daily', value: 'DAILY' },
        { label: 'Monthly', value: 'MONTHLY' },
        { label: 'Manually', value: 'MANUALLY' },
      ],
      userOptions: [],
      workspaceOptions: [],
    };
  },

  computed: {
    ...mapState('reportingJob', ['selectedId']),

    disabledCreate() {
      return false;
    },

    scheduleDayOptions() {
      return Array.from({ length: 31 }, (_, i) => {
        const day = i + 1;
        let suffix = 'th';

        if (day >= 11 && day <= 13) {
          suffix = 'th';
        } else {
          const lastDigit = day % 10;
          if (lastDigit === 1) {
            suffix = 'st';
          } else if (lastDigit === 2) {
            suffix = 'nd';
          } else if (lastDigit === 3) {
            suffix = 'rd';
          }
        }

        return { label: `${day}${suffix}`, value: day };
      });
    },

    scheduleTimeOptions() {
      const times = [];
      for (let hour = 0; hour < 24; hour += 1) {
        const hourString = hour.toString().padStart(2, '0');
        times.push({ label: `${hourString}:00`, value: `${hourString}:00` });
      }
      return times;
    },
  },

  async created() {
    this.setSearchUser('');
    this.setSearchWorkspace('');
    this.doCallApiFetchUsers();
    this.doCallApiFetchWorkspaces();
    if (this.selectedId) {
      this.localReportingJob = await ReportingJobRequest.getReportingJob(this.selectedId);
    }
    this.loading = false;
  },

  methods: {
    ...mapActions('user', {
      setSearchUser: 'setSearch',
    }),

    ...mapActions('workspace', {
      setSearchWorkspace: 'setSearch',
    }),

    async doCallApiFetchUsers() {
      this.userOptions = (await UserRequest.getUsers()).map(u => ({
        id: u.id,
        label: `${u.loginName} (Id: ${u.id} )`,
      }));
    },

    async doCallApiFetchWorkspaces() {
      this.workspaceOptions = (await WorkspaceRequest.getWorkspaces()).map(w => ({
        id: w.id,
        label: `${w.label} (Id: ${w.id} )`,
      }));
    },

    fetchUsers: debounce(async function fetchUsers(search, loading) {
      this.setSearchUser(search);
      await this.doCallApiFetchUsers();
      loading(false);
    }, 500),

    fetchWorkspaces: debounce(async function fetchWorkspaces(search, loading) {
      this.setSearchWorkspace(search);
      await this.doCallApiFetchWorkspaces();
      loading(false);
    }, 500),

    onClickCancel() {
      this.$emit('back');
    },

    async onClickCreateAndNext() {
      if (this.disabledCreate) return;
      this.saving = true;
      await ReportingJobRequest.createReportingJob(this.localReportingJob);

      this.$emit('next');
      this.saving = false;
    },

    async onClickDiscard() {
      this.saving = true;
      this.localReportingJob = await ReportingJobRequest.getReportingJob(this.selectedId);
      this.saving = false;
    },

    onClickNext() {
      this.$emit('next');
    },

    async onClickSave() {
      this.saving = true;
      await ReportingJobRequest.updateReportingJob(this.selectedId, this.localReportingJob);
      this.saving = false;
    },

    onSetName(e) {
      this.localReportingJob.name = e.target.value;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-upsert-general-information {
  @include flex("block", "column", "start", "start");

  width: 35rem;

  .wrapper {
    height: 100%;
    width: 100%;
  }

  .label {
    font-size: $font-size-md;
    font-weight: $font-weight-bold;
    margin-bottom: 0.8rem;
  }

  .item {
    @include flex("block", "row", "start", "center");

    font-size: $font-size-sm;
    margin-bottom: 0.4rem;
    width: 100%;

    .title {
      min-width: 10rem;
    }

    .value {
      width: 100%;
    }
  }

  .actions {
    @include flex("block", "row", "space-between", "start");

    margin-top: 0.6rem;
    width: 35rem;

    .base-button {
      .icon {
        margin-right: 0.4rem;
      }
    }

    .btn-group {
      .base-button {
        margin-left: 0.8rem;
      }
    }
  }
}
</style>
