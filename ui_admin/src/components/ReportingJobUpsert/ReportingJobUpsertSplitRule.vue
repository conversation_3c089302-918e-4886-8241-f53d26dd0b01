<template>
  <section class="reporting-job-upsert-split-rule">
    <loading-blocks-overlay v-if="loading" />
    <section v-else class="wrapper">
      <section class="label">
        Step 3: Split Rules
      </section>
      <section class="item">
        <span class="title">Metadata key:</span>
        <v-select class="value"
          :options="localDataSource.preProcessingAcknowledgement.metadataHeaders"
          v-model="localSplitRules[0].metadataKey"
        />
      </section>

      <section class="item-settings">
        <span class="search-theme-title">Result settings: </span>
        <section class="item-setting" v-for="(item, index) in localSplitRules[0].splitSettings" :key="index">
          <section class="item-setting-header">
            <h5>Result Setting {{ index + 1 }}</h5>
            <base-button size="small" colour="danger" @click="onClickRemoveSetting(index)" class="btn btn-sm btn-danger">
              Remove
            </base-button>
          </section>
          <section class="dataset-name item">
            <span class="title">Dataset names:</span>
            <base-input :value="item.datasetNames" @input="onSetDatasetNames($event, item)" />
          </section>
          <section class="dataset-domain item">
            <span class="title">Dataset domain:</span>
            <base-input :value="item.datasetDomain" @input="onSetDatasetDomain($event, item)" />
          </section>
          <reporting-job-upsert-data-source-metric-calculation-settings
            :metricCalculationSettings="item.metricCalculationSettings || []"
            :metadataHeaders="localDataSource.preProcessingAcknowledgement.metadataHeaders"
            :metadataColumns="localDataSource.preProcessingAcknowledgement.metadataColumns"
            :datasetNames="splitDatasetNames(item.datasetNames)"
            @update:metricCalculationSettings="onSetMetricCalculationSettings($event, item)"
          />
        </section>
        <section>
          <base-button @click="onAddSetting" size="small" class="btn btn-sm btn-primary">
            <i class="fa-solid fa-plus" />
            <span>Add Setting</span>
          </base-button>
        </section>
      </section>

      <section class="actions">
        <base-button colour="light" size="small" @click="onClickBack">
          <span>Back</span>
        </base-button>
        <loading-blocks-overlay v-if="saving" />
        <section v-else class="btn-group">
          <base-button class="btn" colour="danger" size="small" @click="onClickDiscard">
            <span>Discard changes</span>
          </base-button>
          <base-button class="btn" colour="base" size="small" @click="onClickSave">
            <span>Save changes</span>
          </base-button>
          <base-button class="btn" colour="base" size="small" @click="onClickNext">
            <span>Next</span>
          </base-button>
        </section>
      </section>
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import ReportingJobRequest from '@/services/ReportingJobRequest';
import ReportingJobUpsertDataSourceMetricCalculationSettings from '@/components/ReportingJobMetricCalculationSetting/ReportingJobUpsertDataSourceMetricCalculationSettings';

import VSelect from 'vue-select';

export default {
  name: 'reporting-job-upsert-split-rule',

  components: {
    BaseButton,
    BaseInput,
    LoadingBlocksOverlay,
    ReportingJobUpsertDataSourceMetricCalculationSettings,
    VSelect,
  },

  data() {
    return {
      loading: true,
      localSplitRules: [
        {
          metadataKey: '',
          splitSettings: [],
        },
      ],
      localDataSource: {},
      saving: false,
    };
  },

  computed: {
    ...mapState('reportingJob', ['selectedId']),
  },

  async created() {
    // Fetching the split rules
    const foundSplitRules = await ReportingJobRequest.getSplitRules(this.selectedId);
    if (foundSplitRules.length !== 0) {
      this.localSplitRules = foundSplitRules;
      this.convertResponseToLocalSplitRules();
    }

    // Fetching the data sources
    const foundDataSources = await ReportingJobRequest.getDataSources(this.selectedId);
    if (foundDataSources.length !== 0) {
      [this.localDataSource] = foundDataSources;
    }

    this.loading = false;
  },

  methods: {
    onAddSetting() {
      this.localSplitRules[0].splitSettings.push({
        datasetNames: '',
        datasetDomain: '',
      });
    },

    onClickBack() {
      this.$emit('back');
    },

    async onClickDiscard() {
      this.loading = true;
      const foundSplitRules = await ReportingJobRequest.getSplitRules(this.selectedId);
      if (foundSplitRules.length !== 0) {
        this.localSplitRules = foundSplitRules;
        this.convertResponseToLocalSplitRules();
      } else {
        this.localSplitRules = [
          {
            metadataKey: '',
            splitSettings: [],
          },
        ];
      }
      this.loading = false;
    },

    async onClickNext() {
      this.$emit('next');
    },

    onClickRemoveSetting(index) {
      this.localSplitRules[0].splitSettings.splice(index, 1);
    },

    async onClickSave() {
      this.saving = true;
      if (this.localSplitRules[0].metadataKey !== '') { // TODO, find a solution here
        this.convertLocalSplitRulesToRequest();
        await ReportingJobRequest.updateSplitRules(this.selectedId, this.localSplitRules);
        this.convertResponseToLocalSplitRules();
      }
      this.saving = false;
    },

    convertLocalSplitRulesToRequest() {
      this.localSplitRules.forEach(rule => {
        rule.splitSettings.forEach(item => {
          item.datasetNames = item.datasetNames ? item.datasetNames.split(',').map(name => name.trim()) : null;
        });
      });
    },

    convertResponseToLocalSplitRules() {
      this.localSplitRules.forEach(rule => {
        rule.splitSettings.forEach(item => {
          item.datasetNames = item.datasetNames ? item.datasetNames.join(', ') : null;
        });
      });
    },

    onSetDatasetNames(e, item) {
      item.datasetNames = e.target.value;
    },

    onSetDatasetDomain(e, item) {
      item.datasetDomain = e.target.value;
    },

    onSetMetricCalculationSettings(e, item) {
      item.metricCalculationSettings = e;
    },

    splitDatasetNames(datasetNames) {
      // check if datasetNames is array
      if (Array.isArray(datasetNames)) {
        return datasetNames;
      }
      return datasetNames ? datasetNames.split(',').map(name => name.trim()) : [];
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-upsert-split-rule {
  @include flex("block", "column", "start", "start");

  width: 100%;

  .wrapper {
    width: 100%;
  }

  .label {
    font-size: $font-size-md;
    font-weight: $font-weight-bold;
    margin-bottom: 0.8rem;
  }

  .item {
    @include flex("block", "row", "start", "center");

    font-size: $font-size-sm;
    margin-bottom: 0.4rem;

    .title {
      min-width: 10rem;
    }

    .value {
      width: 25rem;
    }
  }

  .item-setting {
    @include flex("block", "column", "start", "start");

    border-radius: 4px;
    border: 2px solid $border-color-purple;
    margin-bottom: 0.8rem;
    padding: 0.6rem;

    .item-setting-header {
      @include flex("block", "row", "space-between", "center");

      width: 100%;

      h5 {
        margin-bottom: 0;
      }
    }
  }

  .base-input {
    width: 25rem;
  }

  .actions {
    @include flex("block", "row", "space-between", "start");

    margin-top: 0.6rem;
    width: 35rem;

    .base-button {
      .icon {
        margin-right: 0.4rem;
      }
    }

    .btn-group {
      .base-button {
        margin-left: 0.8rem;
      }
    }
  }
}
</style>
