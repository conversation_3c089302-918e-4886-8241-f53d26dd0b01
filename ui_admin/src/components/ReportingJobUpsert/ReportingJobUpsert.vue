<template>
  <section class="reporting-job-upsert">
    <reporting-job-upsert-general-information v-if="currentStep === 'general_information'"  class="step" @back="onBack" @next="onNext" />
    <reporting-job-upsert-data-source         v-else-if="currentStep === 'data_sources'"    class="step" @back="onBack" @next="onNext" />
    <reporting-job-upsert-split-rule          v-else-if="currentStep === 'split_rules'"     class="step" @back="onBack" @next="onNext" />
    <reporting-job-upsert-workspace-rule      v-else-if="currentStep === 'workspace_rules'" class="step" @back="onBack" @next="onNext" />
    <reporting-job-upsert-search-theme        v-else-if="currentStep === 'search_themes'"   class="step" @back="onBack" @next="onNext" />
    <reporting-job-upsert-email-config        v-else-if="currentStep === 'email_config'"    class="step" @back="onBack" @next="onNext" />
  </section>
</template>

<script>
import ReportingJobUpsertDataSource from '@/components/ReportingJobUpsertDataSource/ReportingJobUpsertDataSource';
import ReportingJobUpsertEmailConfig from '@/components/ReportingJobUpsert/ReportingJobUpsertEmailConfig';
import ReportingJobUpsertGeneralInformation from '@/components/ReportingJobUpsert/ReportingJobUpsertGeneralInformation';
import ReportingJobUpsertSearchTheme from '@/components/ReportingJobUpsert/ReportingJobUpsertSearchTheme';
import ReportingJobUpsertSplitRule from '@/components/ReportingJobUpsert/ReportingJobUpsertSplitRule';
import ReportingJobUpsertWorkspaceRule from '@/components/ReportingJobUpsert/ReportingJobUpsertWorkspaceRule';
import Route from '@/enum/route';

export default {
  name: 'reporting-job-upsert',

  components: {
    ReportingJobUpsertDataSource,
    ReportingJobUpsertEmailConfig,
    ReportingJobUpsertGeneralInformation,
    ReportingJobUpsertSearchTheme,
    ReportingJobUpsertSplitRule,
    ReportingJobUpsertWorkspaceRule,
  },

  data() {
    return {
      currentStep: 'general_information',
      steps: [
        'general_information',
        'data_sources',
        'split_rules',
        'workspace_rules',
        'search_themes',
        'email_config',
      ],
    };
  },

  computed: {
    disabledCreate() {
      return false;
    },
  },

  methods: {
    onBack() {
      const currentIndex = this.steps.indexOf(this.currentStep);

      if (currentIndex > 0) {
        this.currentStep = this.steps[currentIndex - 1];
      } else {
        this.$router.push({ name: Route.REPORTING_JOB });
      }
    },

    onNext() {
      const currentIndex = this.steps.indexOf(this.currentStep);
      if (currentIndex < this.steps.length - 1) {
        this.currentStep = this.steps[currentIndex + 1];
      } else {
        this.$router.push({ name: Route.REPORTING_JOB });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-upsert {
  @include flex("block", "column", "start", "start");

  height: 100%;
  margin-top: 0.6rem;
  width: 100%;

  .step {
    margin-bottom: 0.6rem;
  }
}
</style>
