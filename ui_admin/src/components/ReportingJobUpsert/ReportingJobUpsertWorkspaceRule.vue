<template>
  <section class="reporting-job-upsert-workspace-rule">
    <loading-blocks-overlay v-if="loading" />
    <section v-else class="wrapper">
      <section class="label">
        Step 4: Workspace Rules
      </section>
      <section class="item" v-for="(item, index) in localWorkspaceRuleSettings" :key="index">
        <span class="title">Rule:</span>
        <section class="rule">
          <section class="dataset-name">
            <span class="rule-title">Dataset names: </span>
            <base-input class="value" :value="item.datasetNames" @input="onSetDatasetNames($event, index)" />
          </section>
          <section class="workspace">
            <span class="rule-title">Workspace: </span>
            <v-select
              class="value"
              :clearable="false"
              :options="workspaceOptions"
              :reduce="option => option.id"
              v-model="item.workspaceId"
              @search="fetchWorkspaces"
            />
          </section>
        </section>
        <i class="fa-solid fa-trash icon-delete" @click="onClickRemove(index)" />
      </section>
      <section class="add-btn" @click="onAdd">
        <i class="fa-solid fa-plus" />
      </section>

      <section class="actions">
        <base-button colour="light" size="small" @click="onClickBack">
          <span>Back</span>
        </base-button>
        <loading-blocks-overlay v-if="saving" />
        <section v-else class="btn-group">
          <base-button class="btn" colour="danger" size="small" @click="onClickDiscard">
            <span>Discard changes</span>
          </base-button>
          <base-button class="btn" colour="base" size="small" @click="onClickSave">
            <span>Save changes</span>
          </base-button>
          <base-button class="btn" colour="base" size="small" @click="onClickNext">
            <span>Next</span>
          </base-button>
        </section>
      </section>
    </section>
  </section>
</template>

<script>
import { debounce } from 'lodash-es';
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import ReportingJobRequest from '@/services/ReportingJobRequest';
import VSelect from 'vue-select';
import WorkspaceRequest from '@/services/WorkspaceRequest';

export default {
  name: 'reporting-job-upsert-workspace-rule',

  components: {
    BaseButton,
    BaseInput,
    LoadingBlocksOverlay,
    VSelect,
  },

  data() {
    return {
      loading: true,
      localWorkspaceRuleSettings: [],
      saving: false,
      workspaceOptions: [],
    };
  },

  computed: {
    ...mapState('reportingJob', ['selectedId']),
  },

  async created() {
    this.setSearchWorkspace('');
    this.doCallApiFetchWorkspaces();
    const foundWorkspaceRules = await ReportingJobRequest.getWorkspaceRules(this.selectedId);
    if (foundWorkspaceRules.length !== 0) {
      this.localWorkspaceRuleSettings = foundWorkspaceRules[0].workspaceRuleSettings;
      this.convertResponseToLocalWorkspaceRules();
    }
    this.loading = false;
  },

  methods: {
    ...mapActions('workspace', {
      setSearchWorkspace: 'setSearch',
    }),

    async doCallApiFetchWorkspaces() {
      this.workspaceOptions = (await WorkspaceRequest.getWorkspaces()).map(w => ({
        id: w.id,
        label: `${w.label} (Id: ${w.id} )`,
      }));
    },

    fetchWorkspaces: debounce(async function fetchWorkspaces(search, loading) {
      this.setSearchWorkspace(search);
      await this.doCallApiFetchWorkspaces();
      loading(false);
    }, 500),

    onAdd() {
      const newWorkspaceRuleSetting = {
        datasetNames: '',
        workspaceId: null,
      };

      this.localWorkspaceRuleSettings.push(newWorkspaceRuleSetting);
    },

    onClickBack() {
      this.$emit('back');
    },

    async onClickDiscard() {
      this.saving = true;
      const foundWorkspaceRules = await ReportingJobRequest.getWorkspaceRules(this.selectedId);
      if (foundWorkspaceRules.length !== 0) {
        this.localWorkspaceRuleSettings = foundWorkspaceRules[0].workspaceRuleSettings;
        this.convertResponseToLocalWorkspaceRules();
      } else {
        this.localWorkspaceRuleSettings = [];
      }
      this.saving = false;
    },

    async onClickNext() {
      this.$emit('next');
    },

    onClickRemove(index) {
      this.localWorkspaceRuleSettings.splice(index, 1);
    },

    async onClickSave() {
      this.saving = true;
      this.convertLocalWorkspaceRulesToRequest();
      await ReportingJobRequest.updateWorkspaceRules(
        this.selectedId,
        { workspaceRuleSettings: this.localWorkspaceRuleSettings },
      );
      this.convertResponseToLocalWorkspaceRules();
      this.saving = false;
    },

    convertLocalWorkspaceRulesToRequest() {
      this.localWorkspaceRuleSettings.forEach(item => {
        item.datasetNames = item.datasetNames ? item.datasetNames.split(',').map(name => name.trim()) : null;
      });
    },

    convertResponseToLocalWorkspaceRules() {
      this.localWorkspaceRuleSettings.forEach(item => {
        item.datasetNames = item.datasetNames ? item.datasetNames.join(', ') : null;
      });
    },

    onSetDatasetNames(e, index) {
      this.localWorkspaceRuleSettings[index].datasetNames = e.target.value;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-upsert-workspace-rule {
  @include flex("block", "column", "start", "start");

  width: 35rem;

  .label {
    font-size: $font-size-md;
    font-weight: $font-weight-bold;
    margin-bottom: 0.8rem;
  }

  .item {
    @include flex("block", "row", "start", "center");

    font-size: $font-size-sm;
    margin-bottom: 0.8rem;

    .title {
      min-width: 3rem;
    }

    .rule {
      @include flex("block", "row", "start", "center");

      .dataset-name {
        @include flex("block", "column", "start", "start");

        .rule-title {
          font-size: $font-size-xs;
        }

        .value {
          margin-right: 0.8rem;
          width: 35rem;
        }
      }

      .workspace {
        @include flex("block", "column", "start", "start");

        .rule-title {
          font-size: $font-size-xs;
        }

        .value {
          width: 25rem;
        }
      }
    }

    .icon-delete {
      color: clr('red');
      cursor: pointer;
      margin-left: 0.4rem;

      &:hover {
        opacity: 0.8;
      }
    }
  }

  .add-btn {
    @include flex("block", "row", "center", "center");

    border: 1px solid clr('purple');
    color: clr('purple');
    cursor: pointer;
    border-radius: $border-radius-medium;
    height: 2rem;
    width: 2rem;

    &:hover {
      border-color: lighten(clr('purple'), 10%);
      color: lighten(clr('purple'), 10%);
    }
  }

  .actions {
    @include flex("block", "row", "space-between", "start");

    margin-top: 0.6rem;
    width: 35rem;

    .base-button {
      .icon {
        margin-right: 0.4rem;
      }
    }

    .btn-group {
      .base-button {
        margin-left: 0.8rem;
      }
    }
  }
}
</style>
