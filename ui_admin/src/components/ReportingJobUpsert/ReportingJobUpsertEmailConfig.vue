<template>
  <section class="reporting-job-upsert-config-email-config">
    <loading-blocks-overlay v-if="loading" />
    <section v-else class="wrapper">
      <section class="label">
        Step 6: Email Configurations
      </section>
      <section class="recipient-email common">
        <section class="auto-send">
          <span class="title">Auto-Send: </span>
          <base-checkbox class="checkbox" :value="localCommonEmailSettings.autoSend" @input="onToggleAutoSend" />
        </section>
        <section class="sender-email">
          <span class="title">Sender Email: </span>
          <base-input class="value" :value="localCommonEmailSettings.senderEmail" @input="onSetSenderEmail($event)" />
        </section>
        <section class="email-template">
          <span class="title">Template Id: </span>
          <base-input class="value" :value="localCommonEmailSettings.templateId" @input="onSetTemplateId($event)" />
        </section>
      </section>
      <section class="recipient-email global">
        <span class="recipient-email-title">Global: </span>
        <section class="item" v-for="(item, index) in localEmailConfigsGlobal" :key="index">
          <section class="item-setting-header">
            <h5>Global Setting {{ index + 1 }}</h5>
            <base-button size="small" colour="danger" @click="onClickRemove(item)" class="btn btn-sm btn-danger">
              Remove
            </base-button>
          </section>
          <section class="recipient-emails">
            <span class="title">Emails: </span>
            <base-input class="value" :value="item.recipientEmails" @input="onSetEmails($event, item)" />
          </section>
          <section class="tracked-themes">
            <span class="title">Tracked Themes: </span>
            <base-input class="value" :value="item.contentSettings.trackedThemes" @input="onSetTrackedThemes($event, item)" />
          </section>
          <section class="max-comments-to-include">
            <span class="title">Max Comments Number: </span>
            <base-input step="1" type="number" class="value" :value="item.contentSettings.maxCommentsToInclude" @input="onSetMaxCommentsToInclude($event, item)" />
          </section>
        </section>
        <section class="add-btn" @click="onAddGlobal" v-if="localEmailConfigsGlobal.length === 0">
          <i class="fa-solid fa-plus" />
        </section>
      </section>
      <section class="recipient-email custom">
        <span class="recipient-email-title">Custom: </span>
        <section class="item" v-for="(item, index) in localEmailConfigsCustom" :key="index">
          <section class="item-setting-header">
            <h5>Custom Setting {{ index + 1 }}</h5>
            <base-button size="small" colour="danger" @click="onClickRemove(item)" class="btn btn-sm btn-danger">
              Remove
            </base-button>
          </section>
          <section class="dataset-name">
            <span class="title">Dataset Names: </span>
            <base-input class="value dataset-name" :value="item.datasetNames" @input="onSetDatasetNames($event, item)" />
          </section>
          <section class="recipient-emails">
            <span class="title">Emails: </span>
            <base-input class="value" :value="item.recipientEmails" @input="onSetEmails($event, item)" />
          </section>
          <section class="tracked-themes">
            <span class="title">Tracked Themes: </span>
            <base-input class="value" :value="item.contentSettings.trackedThemes" @input="onSetTrackedThemes($event, item)" />
          </section>
          <section class="max-comments-to-include">
            <span class="title">Max Comments Number: </span>
            <base-input step="1" type="number" class="value" :value="item.contentSettings.maxCommentsToInclude" @input="onSetMaxCommentsToInclude($event, item)" />
          </section>
        </section>
        <section class="add-btn" @click="onAddCustom">
          <i class="fa-solid fa-plus" />
        </section>
      </section>

      <section class="actions">
        <base-button colour="light" size="small" @click="onClickBack">
          <span>Back</span>
        </base-button>
        <loading-blocks-overlay v-if="saving" />
        <section v-else class="btn-group">
          <base-button class="btn" colour="danger" size="small" @click="onClickDiscard">
            <span>Discard changes</span>
          </base-button>
          <base-button class="btn" colour="base" size="small" @click="onClickSave">
            <span>Save changes</span>
          </base-button>
          <base-button class="btn" colour="base" size="small" @click="onClickNext">
            <span>Next</span>
          </base-button>
        </section>
      </section>
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseCheckbox from '@/components/Base/BaseCheckbox';
import BaseInput from '@/components/Base/BaseInput';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import ReportingJobRequest from '@/services/ReportingJobRequest';
import VSelect from 'vue-select';

export default {
  name: 'reporting-job-upsert-config-email-config',

  components: {
    BaseButton,
    BaseCheckbox,
    BaseInput,
    LoadingBlocksOverlay,
    VSelect,
  },

  data() {
    return {
      loading: true,
      localEmailSettings: [],
      localCommonEmailSettings: {
        autoSend: false,
      },
      saving: false,
    };
  },

  computed: {
    ...mapState('reportingJob', ['selectedId']),

    localEmailConfigsCustom() {
      return this.localEmailSettings.filter(st => st.datasetNames !== null);
    },

    localEmailConfigsGlobal() {
      return this.localEmailSettings.filter(st => st.datasetNames === null);
    },
  },

  async created() {
    const foundConfigList = await ReportingJobRequest.getEmailConfigs(this.selectedId);
    if (foundConfigList.length !== 0) {
      this.localEmailSettings = foundConfigList[0].emailSettings;
      this.localCommonEmailSettings = foundConfigList[0].commonEmailSettings;
      this.convertResponseToLocalEmailConfigs();
    } else {
      this.localCommonEmailSettings = {
        templateId: 'reporting-email-template', // TODO: need to implement this instead of hard code here
        subject: null, // TODO
        senderEmail: null, // TODO
        autoSend: false,
      };
    }
    this.loading = false;
  },

  methods: {
    onAddCustom() {
      const newEmailSetting = {
        datasetNames: '',
        recipientEmails: '',
        contentSettings: {
          trackedThemes: '', // TODO
          topCustomThemes: {
            sortBy: 'volume', // TODO
            maxThemesToInclude: 2, // TODO
          },
          topAutoGeneratedThemes: {
            sortBy: 'volume', // TODO
            maxThemesToInclude: 0, // TODO
          },
          maxThemesToInclude: 7,
        },
      };

      this.localEmailSettings.push(newEmailSetting);
    },

    onAddGlobal() {
      const newEmailSetting = {
        datasetNames: null,
        recipientEmails: '',
        contentSettings: {
          trackedThemes: '', // TODO
          topCustomThemes: {
            sortBy: 'volume', // TODO
            maxThemesToInclude: 2, // TODO
          },
          topAutoGeneratedThemes: {
            sortBy: 'volume', // TODO
            maxThemesToInclude: 0, // TODO
          },
          maxThemesToInclude: 7,
          maxCommentsToInclude: 4,
        },
      };

      this.localEmailSettings.push(newEmailSetting);
    },

    onClickBack() {
      this.$emit('back');
    },

    async onClickDiscard() {
      this.saving = true;
      const foundConfigList = await ReportingJobRequest.getEmailConfigs(this.selectedId);
      if (foundConfigList.length !== 0) {
        this.localEmailSettings = foundConfigList[0].emailSettings;
        this.localCommonEmailSettings = foundConfigList[0].commonEmailSettings;
        this.convertResponseToLocalEmailConfigs();
      } else {
        this.localEmailSettings = [];
        this.localCommonEmailSettings = {
          templateId: 'reporting-email-template', // TODO: need to implement this instead of hard code here
          subject: null, // TODO
          senderEmail: null, // TODO
          autoSend: false,
        };
      }
      this.saving = false;
    },

    async onClickNext() {
      this.$emit('next');
    },

    onClickRemove(item) {
      const index = this.localEmailSettings.indexOf(item);
      if (index > -1) {
        this.localEmailSettings.splice(index, 1);
      }
    },

    async onClickSave() {
      this.saving = true;

      this.convertLocalEmailConfigsToRequest();
      await ReportingJobRequest.updateEmailConfigs(
        this.selectedId,
        { emailSettings: this.localEmailSettings, commonEmailSettings: this.localCommonEmailSettings },
      );
      this.convertResponseToLocalEmailConfigs();

      this.saving = false;
    },

    convertLocalEmailConfigsToRequest() {
      this.localEmailSettings.forEach(item => {
        item.recipientEmails = item.recipientEmails.split(',').map(email => email.trim());
        item.datasetNames = item.datasetNames ? item.datasetNames.split(',').map(name => name.trim()) : null;
        item.contentSettings.trackedThemes = item.contentSettings.trackedThemes.split(',').map(theme => theme.trim());
      });
    },

    convertResponseToLocalEmailConfigs() {
      this.localEmailSettings.forEach(item => {
        item.recipientEmails = item.recipientEmails.join(', ');
        item.datasetNames = item.datasetNames ? item.datasetNames.join(', ') : null;
        item.contentSettings.trackedThemes = item.contentSettings.trackedThemes.join(', ');
      });
    },

    onSetDatasetNames(e, item) {
      item.datasetNames = e.target.value;
    },

    onSetEmails(e, item) {
      item.recipientEmails = e.target.value;
    },

    onSetTrackedThemes(e, item) {
      item.contentSettings.trackedThemes = e.target.value;
    },

    // Value must greater than or equal to 0 and less than or equal to maxThemesToInclude
    onSetMaxCommentsToInclude(e, item) {
      if (e.target.value < 0) {
        e.target.value = 0; // Reset to previous value if out of bounds
        return;
      }
      if (e.target.value > item.contentSettings.maxThemesToInclude) {
        e.target.value = item.contentSettings.maxThemesToInclude; // Reset to previous value if out of bounds
        return;
      }
      item.contentSettings.maxCommentsToInclude = e.target.value;
    },

    onSetSenderEmail(e) {
      this.localCommonEmailSettings.senderEmail = e.target.value;
    },

    onSetTemplateId(e) {
      this.localCommonEmailSettings.templateId = e.target.value;
    },

    onToggleAutoSend() {
      this.localCommonEmailSettings.autoSend = !this.localCommonEmailSettings.autoSend;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-upsert-config-email-config {
  @include flex("block", "column", "start", "start");

  width: 35rem;

  .label {
    font-size: $font-size-md;
    font-weight: $font-weight-bold;
    margin-bottom: 0.8rem;
  }

  .recipient-email {
    @include flex("block", "column", "start", "start");

    margin-top: 1rem;
    width: 100%;

    .recipient-email-title {
      font-weight: $font-weight-bold;
    }

    .item {
      @include flex("block", "column", "start", "start");

      border-radius: 4px;
      border: 1px solid;
      font-size: $font-size-sm;
      margin-bottom: 0.4rem;
      padding: 0.5rem;

      .item-setting-header {
        @include flex("block", "row", "space-between", "center");

        width: 100%;
      }

      .title {
        font-size: $font-size-xs;
      }

      .value {
        width: 30rem;

        &.dataset-name {
          margin-right: 0.8rem;
          width: 30rem;
        }
      }

      .recipient-emails  {
        @include flex("block", "column", "start", "start");
      }

      .auto-send {
        @include flex("block", "row", "start", "start");
        margin-bottom: 0.4rem;
        margin-top: 0.4rem;

        .checkbox {
          width: 1rem;
          height: 1rem;
          margin-left: 1rem;
        }
      }
    }

    .add-btn {
      @include flex("block", "row", "center", "center");

      border: 1px solid clr('purple');
      color: clr('purple');
      cursor: pointer;
      border-radius: $border-radius-medium;
      height: 2rem;
      width: 2rem;

      &:hover {
        border-color: lighten(clr('purple'), 10%);
        color: lighten(clr('purple'), 10%);
      }
    }
  }

  .actions {
    @include flex("block", "row", "space-between", "start");

    margin-top: 0.6rem;
    width: 35rem;

    .base-button {
      .icon {
        margin-right: 0.4rem;
      }
    }

    .btn-group {
      .base-button {
        margin-left: 0.8rem;
      }
    }
  }
}
</style>
