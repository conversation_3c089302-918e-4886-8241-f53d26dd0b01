<template>
  <section class="date-time-renderer">
    <section v-if="dateTime" class="date-time">
      <section>{{date}}</section>
      <section>{{time}}</section>
    </section>
    <span v-else>-</span>
  </section>
</template>

<script>
import { DateTime } from 'luxon';

export default {
  name: 'date-time-renderer',

  computed: {
    dateTime() {
      return this.params.value;
    },

    date() {
      return DateTime.fromISO(this.dateTime).toFormat('dd/MM/yyyy');
    },

    time() {
      return DateTime.fromISO(this.dateTime).toFormat('HH:mm:ss');
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.date-time-renderer {
  @include flex("block", "row", "start", "start");

  .date-time {
    @include flex("block", "column", "start", "start");
  }
}
</style>
