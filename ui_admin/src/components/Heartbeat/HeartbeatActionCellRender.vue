<template>
  <section class="heartbeat-cell-render">
    <section class="item">
      <base-button :size="'small'" :colour="'base'" @click="onClickNamespace">
        <i class="fa-regular fa-save icon"></i> Namespace
      </base-button>
    </section>
    <section class="item">
      <base-button :size="'small'" :colour="'danger'" @click="onClickDelete">
        <i class="fa-regular fa-trash icon"></i> Delete
      </base-button>
    </section>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import HeartbeatDeleteModal from '@/components/Heartbeat/HeartbeatDeleteModal';
import HeartbeatEditNamespaceModal from '@/components/Heartbeat/HeartbeatEditNamespaceModal';

export default {
  name: 'heartbeat-cell-render',

  components: {
    BaseButton,
  },

  computed: {
    heartbeat() {
      return this.params.data;
    },
  },

  methods: {
    ...mapActions('modal', ['setModal']),

    ...mapActions('toast', ['addToast']),

    ...mapActions('heartbeat', ['setSelectedHeartbeats', 'setSelectedHeartbeat']),

    async onClickDelete() {
      await this.setSelectedHeartbeats([this.heartbeat]);
      await this.setModal(HeartbeatDeleteModal);
    },

    async onClickNamespace() {
      await this.setSelectedHeartbeat(this.heartbeat);
      await this.setModal(HeartbeatEditNamespaceModal);
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.heartbeat-cell-render {
  @include flex("block", "column", "start", "start");

  .item {
    @include flex("block", "row", "center", "center");

    border-radius: 4px;
    color: clr('white');
    height: 2rem;
    margin: 0.2rem;

    .base-button {
      height: 2rem;
    }

    .icon {
      margin-right: 0.2rem;
    }
  }
}
</style>
