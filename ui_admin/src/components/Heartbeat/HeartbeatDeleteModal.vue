<template>
  <section class="heartbeat-delete-modal">
    <section class="header">
      <h2>Delete Saved Actions</h2>
    </section>

    <section class="body">
      <section class="text">
        This/these heartbeats will be deleted
        <span class="label">{{ label }}</span>
        . Are you sure you wish to continue?
      </section>
    </section>

    <section class="footer">
      <base-button class="cancel" colour="light" type="link" @click="closeModal">Cancel</base-button>
      <base-button class="delete" colour="danger" @click="onClickDelete">Delete</base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import HeartbeatToast from '@/components/Toast/HeartbeatToast';
import heartbeatRequest from '@/services/HeartbeatRequest';

export default {
  name: 'heartbeat-delete-modal',

  components: {
    BaseButton,
  },

  computed: {
    ...mapState('heartbeat', ['selectedHeartbeats']),

    label() {
      return this.selectedHeartbeats.map(action => action.id);
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('toast', ['addToast']),

    async onClickDelete() {
      await heartbeatRequest.deleteHeartbeats([...this.selectedHeartbeats.map(action => action.id)]);

      await this.closeModal();
      await this.addToast({
        toastComponent: {
          component: HeartbeatToast,
          id: 'heartbeat-deleted',
        },
        toastData: {
          label: this.label,
        },
      });

      await heartbeatRequest.fetchHeartbeats();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.heartbeat-delete-modal {
  @include modal;

  .body {
    padding: $search-modal-padding;

    .text {
      font-size: $font-size-xs;
      margin: 0.5rem 0;

      .label {
        font-weight: $font-weight-bold;
      }
    }
  }

  .footer {
    font-size: $font-size-md;

    padding: $search-modal-padding;

    .cancel {
      font-weight: $font-weight-normal;
      padding-left: 0;
    }

    .delete {
      font-weight: $font-weight-medium;
    }
  }

  .header {
    @include flex("block", "row", "between", "center");

    padding: $search-modal-padding;

    h2 {
      @include flex("block", "row", "start", "center");

      font-weight: $font-weight-bold;
    }

    .icon {
      @include flex("block", "row", "end", "center");

      cursor: pointer;
      height: 1.25rem;
      opacity: 0.3;
      &:hover {
        opacity: 1;
      }

      &:active,
      &:focus {
        border: none;
        outline: none;
      }
    }
  }
}
</style>
