<template>
  <section class="heartbeat-table">
    <section class="heartbeat-toolbar">
      <section class="heartbeat-actions">
        <base-button :colour="'danger'" :size="'small'" @click="onClickDelete">
          <i class="fa-regular fa-trash icon"></i>
          Delete
        </base-button>
      </section>
      <section class="heartbeat-pagination">
        <pagination  :current-page="page" :last="last" @onBtPrevious="onBtPrevious" @onBtNext="onBtNext" @onSetPage="onSetPage"></pagination>
      </section>
    </section>
    <section class="heartbeat-gird">
      <ag-grid-vue
          style="width: 100%; height: 100%"
          class="ag-theme-alpine"
          :columnDefs="columnDefs"
          :rowData="heartbeatsMap"
          rowSelection='multiple'
          @grid-ready="onGridReady"
          @filter-changed="onFilterChanged"
      />
    </section>
  </section>
</template>

<script>
import { AgGridVue } from 'ag-grid-vue3';

import BaseButton from '@/components/Base/BaseButton';
import HeartbeatDeleteModal from '@/components/Heartbeat/HeartbeatDeleteModal';
import Pagination from '@/components/Pagination/pagination';
import HeartbeatActionCellRender from '@/components/Heartbeat/HeartbeatActionCellRender';
import heartbeatRequest from '@/services/HeartbeatRequest';
import { mapActions, mapState } from 'vuex';

export default {
  name: 'heartbeat-table',

  components: {
    AgGridVue,
    BaseButton,
    Pagination,
  },

  data() {
    return {
      gridApi: null,
      defaultColDef: {
        filter: true,
      },
    };
  },

  computed: {
    ...mapState('heartbeat', [
      'heartbeats',
      'pages',
      'rows',
      'page',
    ]),

    columnDefs() {
      return [
        {
          id: 'Id',
          field: 'id',
          width: 100,
          wrapText: true,
          autoHeight: true,
          headerCheckboxSelection: true,
          checkboxSelection: true,
        },
        {
          headerName: 'Workspace label',
          field: 'workspace',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: ['contains'],
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
        },
        {
          headerName: 'User Name',
          field: 'user',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: ['contains'],
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
        },
        {
          headerName: 'Status',
          field: 'status',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          filterParams: {
            filterOptions: [
              {
                displayKey: 'choose_one',
                displayName: 'Choose one',
                predicate() {
                  return true;
                },
                numberOfInputs: 0,
              },
              {
                displayKey: 'DRAFT',
                displayName: 'DRAFT',
                predicate(_, cellValue) {
                  return null || /draft/gm.test(cellvalue?.tolowercase());
                },
                numberOfInputs: 0,
              },
              {
                displayKey: 'LIVE',
                displayName: 'LIVE',
                predicate(_, cellValue) {
                  return null || /live/gm.test(cellValue?.toLowerCase());
                },
                numberOfInputs: 0,
              },
              {
                displayKey: 'PAUSED',
                displayName: 'PAUSED',
                predicate(_, cellValue) {
                  return null || /paused/gm.test(cellValue?.toLowerCase());
                },
                numberOfInputs: 0,
              },
              {
                displayKey: 'CLOSED',
                displayName: 'CLOSED',
                predicate(_, cellValue) {
                  return null || /closed/gm.test(cellValue?.toLowerCase());
                },
                numberOfInputs: 0,
              },
            ],
            defaultOption: 'choose_one',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
        },
        {
          headerName: 'Label',
          field: 'label',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: ['contains'],
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
        },
        {
          headerName: 'Namespace',
          field: 'namespace',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: ['contains'],
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
        },
        {
          headerName: 'Total Responses',
          field: 'totalResponses',
          flex: 1,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Total Analysed Responses',
          field: 'totalAnalysedResponses',
          flex: 1,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Dataset Id',
          field: 'datasetId',
          flex: 1,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Actions',
          field: '',
          width: 200,
          wrapText: true,
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: HeartbeatActionCellRender };
          },
        },
      ];
    },

    heartbeatsMap() {
      return this.heartbeats.map(o => ({
        ...o,
        user: o.user.loginName,
        workspace: o.workspaceLabel,
      }));
    },

    last() {
      return this.heartbeats.length < this.rows;
    },

    selectedHeartbeats() {
      return this.gridApi.getSelectedRows();
    },
  },

  beforeMount() {
    this.doCallApiFetchHeartbeats();
  },

  methods: {
    ...mapActions('heartbeat', [
      'setFilterIds',
      'setFilterLabel',
      'setFilterOwner',
      'setFilterStatus',
      'setFilterSurveyUuid',
      'setFilterWorkspaceLabel',
      'setPage',
      'setSelectedHeartbeat',
      'setSelectedHeartbeats',
    ]),

    ...mapActions('toast', ['addToast']),

    ...mapActions('modal', ['setModal']),

    async doCallApiFetchHeartbeats() {
      await heartbeatRequest.fetchHeartbeats();
    },

    onBtPrevious() {
      this.setPage({ page: this.page - 1 });
      this.doCallApiFetchHeartbeats();
    },

    onBtNext() {
      this.setPage({ page: this.page + 1 });
      this.doCallApiFetchHeartbeats();
    },

    onSetPage(page) {
      this.setPage({ page });
      this.doCallApiFetchHeartbeats();
    },

    onGridReady(params) {
      this.gridApi = params.api;
    },

    onFilterChanged(e) {
      const model = e.api.getFilterModel();
      this.setFilterStatus((model.status?.type === 'choose_one' ? '' : model.status?.type) || '');
      this.setFilterOwner(model.user?.filter || '');
      this.setFilterLabel(model.label?.filter || '');
      this.setFilterWorkspaceLabel(model.workspace?.filter || '');

      this.doCallApiFetchHeartbeats();
    },

    async onClickDelete() {
      this.setSelectedHeartbeats(this.selectedHeartbeats);
      await this.setModal(HeartbeatDeleteModal);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.heartbeat-table {
  @include flex("block", "column", "start", "start");

  height: 100%;
  width: 100%;

  .heartbeat-toolbar {
    @include flex("block", "row", "start", "start");

    width: 100%;

    .heartbeat-actions {
      @include flex("block", "row", "start", "start");
      padding-left: 1rem;
      column-gap: 1em;
      margin: 0.2em;

      .icon {
        margin-right: 0.2em;
      }
    }

    .heartbeat-pagination {
      @include flex("block", "row", "end", "start");

      padding-right: 2rem;
      width: 100%;
    }
  }

  .heartbeat-gird {
    @include flex("block", "row", "start", "start");

    height: 100%;
    padding: 1rem 0.6rem 0 0.6rem;
    width: 100%;

    .title {
      @include flex("block", "row", "center", "start");

      font-size: 1.5rem;
      font-weight: $font-weight-bold;
    }

    .base-button {
      margin-left: auto;
    }
  }
}
</style>
