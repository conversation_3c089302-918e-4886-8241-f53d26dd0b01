<template>
  <section class="heartbeat-details-modal">
    <section class="header">
      <h2>Update Heartbeat Namespace</h2>
    </section>
    <section class="body">
      <section class="field row">
        <span>Namespace</span>
        <base-input placeholder="Enter namespace" :value="namespace" @input="onInputNamespace"/>
      </section>
    </section>
    <section class="footer">
      <base-button class="cancel" colour="light" size="small" type="link"  @click="onCancel">Cancel</base-button>
      <section class="confirm">
        <base-button colour="base" size="small" @click="onUpdate">
          <span>Save</span>
        </base-button>
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseCheckbox from '@/components/Base/BaseCheckbox';
import BaseInput from '@/components/Base/BaseInput';
import HeartbeatToast from '@/components/Toast/HeartbeatToast';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import VSelect from 'vue-select';
import heartbeatRequest from '@/services/HeartbeatRequest';

export default {
  name: 'heartbeat-details-modal',

  components: {
    BaseButton,
    BaseCheckbox,
    BaseInput,
    LoadingBlocksOverlay,
    VSelect,
  },

  computed: {
    ...mapState('heartbeat', ['selectedHeartbeat']),
  },

  beforeMount() {
    this.namespace = this.selectedHeartbeat.namespace;
  },

  beforeDestroy() {
    this.setSelectedHeartbeat({ item: null });
  },

  data() {
    return {
      creating: false,
      namespace: '',
    };
  },

  methods: {
    ...mapActions('modal', ['closeModal']),
    ...mapActions('toast', ['addToast']),
    ...mapActions('heartbeat', ['setSelectedHeartbeat']),

    onInputNamespace(e) {
      this.namespace = e.target.value;
    },

    async onUpdate() {
      await heartbeatRequest.updateHeartbeatNamespace(this.selectedHeartbeat.id, this.namespace);
      await heartbeatRequest.fetchHeartbeats();
      await this.closeModal();
      await this.addToast({
        toastComponent: {
          component: HeartbeatToast,
          id: 'heartbeat-updated',
        },
        toastData: {
          label: this.selectedHeartbeat.label,
        },
      });
    },

    onCancel() {
      this.closeModal();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.heartbeat-details-modal {
  @include modal;

  position: relative;

  .field {
    margin-bottom: 0.6rem;
  }

  .row {
    display: grid;
    grid-template-columns: 40% 60%
  }

  .wrapper-select {
    height: 30px;
  }

  .v-select {
    background-color: clr('white');
    position: absolute;
    width: 500px;
  }
}
</style>
