<template>
  <section class="grid-list-cell">
    <div class="item tooltip" v-for="item in params.value">{{item}}
    </div>
  </section>
</template>

<script>
export default {
  name: 'grid-list-cell',
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.grid-list-cell {
  @include flex("block", "column", "start", "start");

  .item {
    @include flex("block", "row", "center", "center");

    background-color: #7184B0;
    border-radius: 4px;
    color: clr('white');
    height: 2rem;
    margin: 0.2rem;
    padding: 0.2rem;
    min-width: max-content;
  }

  min-width: max-content;
}
</style>
