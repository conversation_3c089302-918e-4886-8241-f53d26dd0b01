<template>
  <input
    class="base-checkbox"
    type="checkbox"
    :checked="this.$props.value"
    :class="{ background: this.$props.background, disabled: this.$props.disabled, size1x: this.$props.size1x }"
  />
</template>

<script>
export default {
  name: 'base-checkbox',

  props: {
    background: {
      type: <PERSON>olean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    size1x: {
      type: Boolean,
      default: false,
    },
    value: {
      type: Boolean,
      default: false,
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.base-checkbox {
  @include flex("inline", "row", "center", "center");

  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  border: $border-light solid clr("white");
  border-radius: $border-radius-medium;
  color: clr("white");
  cursor: pointer;
  position: relative;
  height:1rem;
  width:1rem;
  transition: $transition-base;

  &.background {
    background-color: white;
    border: $checkbox-bdr 1px solid;
  }

  &.disabled {
    pointer-events: none;
    opacity: 0.5;
  }

  &:active,
  &:checked,
  &:focus {
    outline: none;
  }

  &:hover {
    border-color: $border-color-hover;
    box-shadow: $box-shadow-hover;
  }

  &:checked {
    &.background {
      background-color: clr("blue");
      border-color: darken(clr("blue"), 5%);
    }

    &::after {
      content: "✓";
      font-family: sans-serif;
      font-size: 0.7em;
      font-weight: $font-weight-bold;
      position: absolute;
    }
  }

  &.size1x {
    height: 1rem;
    width: 1rem;
  }
}
</style>
