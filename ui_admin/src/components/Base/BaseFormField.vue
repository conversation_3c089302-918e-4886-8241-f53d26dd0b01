<template>
  <section class="base-form-field">
    <label :for="id ? id : _uid">{{label}}</label>
    <input
      ref="input"
      :class="{ failure, success }"
      :id="id ? id : _uid"
      :type="type"
      :value="modelValue"
      @input="onInput"
      @keydown.enter="$emit('keydown.enter')"
      :placeholder ="placeholder"
    >
  </section>
</template>

<script>
import { ref } from 'vue';

export default {
  name: 'base-form-field',

  props: {
    focus: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      required: false,
    },
    failure: {
      type: Boolean,
      default: false,
    },
    label: {
      type: String,
      required: true,
    },
    modelValue: {
      type: [String, Number],
      default: '',
    },
    success: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      required: true,
    },
    placeholder: {
      type: String,
      required: false,
    },
  },

  mounted() {
    if (this.focus) {
      this.$refs.input.focus();
    }
  },

  emits: ['update:modelValue', 'keydown.enter'],

  setup(props, { emit }) {
    const inputRef = ref(null);

    const onInput = (event) => {
      const inputValue = event.target.value;
      emit('update:modelValue', inputValue); // Emit the update event with the new input value
    };

    return {
      inputRef,
      onInput,
    };
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.base-form-field {
  @include flex("block", "column", "start", "start");
  @include grow;

  margin: 0 0 2em;

  label {
    @include flex("block", "row", "start", "start");
    @include grow;

    font-size: 0.75em;
    font-weight: $font-weight-normal;
  }

  input {
    appearance: none;
    border: none;
    box-shadow: none;
    box-sizing: border-box;
    font-family: $font-stack;
    font-size: 1rem;
    margin: 0;
    outline: none;
    padding: 0.6rem;
    transition: all $interaction-transition-time;
    width: 100%;
    border-radius: 8px;

    &::placeholder {
      opacity: 0.8;
    }

    &:active,
    &:focus {
    }

    &.failure {
      border-bottom: $border-light solid clr("red");
    }

    &.success {
      border-bottom: $border-light solid clr("green");
    }
  }

  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  input:-webkit-autofill,
  textarea:-webkit-autofill,
  textarea:-webkit-autofill:hover textarea:-webkit-autofill:focus,
  select:-webkit-autofill,
  select:-webkit-autofill:hover,
  select:-webkit-autofill:focus {
    border: none;
    border-bottom: $border-light solid clr("white");
    -webkit-box-shadow: 0 0 0 2rem clr("white") inset;
  }
}
</style>
