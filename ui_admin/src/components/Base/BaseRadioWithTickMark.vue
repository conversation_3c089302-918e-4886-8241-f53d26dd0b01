<template>
  <section class="base-radio-with-tick-mark">
    <input
      class="input-checkbox"
      type="checkbox"
      :checked="this.$props.value"
      :class="{ background: this.$props.background, disabled: this.$props.disabled, size1x: this.$props.size1x }"
    />
    <i v-show="this.$props.value" class="fa-solid fa-check checked-icon"></i>
  </section>
</template>

<script>
export default {
  name: 'base-radio-with-tick-mark',

  props: {
    background: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: <PERSON><PERSON>an,
      default: false,
    },
    size1x: {
      type: Boolean,
      default: false,
    },
    value: {
      type: <PERSON><PERSON>an,
      default: false,
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.base-radio-with-tick-mark {
  @include flex("inline", "row", "center", "center");

  height: fit-content;
  position: relative;
  width: fit-content;

  .input-checkbox {
    @include flex("inline", "row", "center", "center");

    -moz-appearance: none;
    -webkit-appearance: none;
    appearance: none;
    border-radius: 50%;
    border: $border-light solid clr("white");
    color: clr("white");
    cursor: pointer;
    height: 1rem;
    position: relative;
    width: 1rem;

    &.background {
      background-color: white;
      border: $checkbox-bdr 1px solid;
    }

    &.disabled {
      opacity: 0.5;
      pointer-events: none;
    }

    &:active,
    &:checked,
    &:focus {
      outline: none;
    }

    &:hover {
      box-shadow: $box-shadow-hover;
    }

    &:checked {
      &.background {
        background-color: #438EF2;
        border-color: darken(#438EF2, 15%);
        opacity: 1;
      }

      &::after {
        font-family: sans-serif;
        font-size: 0.5em;
        font-weight: $font-weight-bold;
        position: absolute;
      }
    }

    &.size1x {
      height: 1rem;
      width: 1rem;
    }
  }

  .checked-icon {
    @include flex("inline", "row", "center", "center");

    -moz-transform: translate(-50%,-50%);
    -ms-transform: translate(-50%,-50%);
    -webkit-transform: translate(-50%,-50%);
    color: clr("white");
    font-size: 0.5rem;
    left: 50%;
    pointer-events: none;
    position: absolute;
    top: 50%;
    transform: translate(-50%,-50%);
  }
}
</style>
