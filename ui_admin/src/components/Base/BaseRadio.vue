<template>
  <section class="base-radio" :class="[radioSize, radioStyle, { active: value }]">
    <section class="inner" :class="[radioSize]"></section>
  </section>
</template>

<script>
export default {
  name: 'base-radio',

  props: {
    radioSize: {
      type: String,
      default: 'normal',
    },
    radioStyle: {
      type: String,
      default: 'style-default',
    },
    value: {
      type: Boolean,
      required: true,
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

$size: $font-size-lg;
$size-sm: $font-size-sm;

.base-radio {
  @include flex("block", "row", "center", "center");

  background-color: clr("white");
  border: $border-standard;
  transition: all $interaction-transition-time;

  &.normal {
    border-radius: $size;
    height: $size;
    width: $size;

    &.active {
      border: calc(#{$size * 0.15}) solid clr("blue");
    }
  }

  &.small {
    border-radius: $size-sm;
    height: $size-sm;
    width: $size-sm;

    &.active {
      border: calc(#{$size-sm * 0.15}) solid clr("blue");
    }
  }

  .inner {
    height: 100%;
    width: 100%;

    .normal {
      border-radius: $size;
    }

    .small {
      border-radius: $size-sm;
    }
  }

  &.style-dark-purple {
    background-color: clr("grey");
    border: clr("grey", "dark");

    &.active {
      background-color: $dataimport;
    }

    &.normal.active {
      border: calc(#{$size * 0.15}) solid clr("white");
    }
    &.small.active {
      border: calc(#{$size-sm * 0.15}) solid clr("white");
    }
  }
}
</style>
