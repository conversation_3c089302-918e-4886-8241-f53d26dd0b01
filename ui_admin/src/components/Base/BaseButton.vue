<template>
  <section
    class="base-button"
    :class="this.$options.classList(this.$data, this.$props)"
    tabindex="0"
    @click="this.$options.clickEvent(this.$props, this.$attrs)(this.$event)"
    @keydown.enter="this.$options.clickEvent(this.$props, this.$attrs)(this.$event)">
    <section>
      <component v-if="this.$props.icon" :is="this.$options.iconComponent(this.$props.icon)" class="base-icon" />
      <slot></slot>
    </section>
  </section>
</template>

<script>
import { camelCase, upperFirst } from 'lodash-es';
import * as feather from 'vue-feather-icons';

const colourValidator = value =>
  [
    'base',
    'dark',
    'data',
    'dropdown',
    'danger',
    'light',
    'qualtrics',
    'success',
    'surveymonkey',
    'warning',
    'zendesk',
  ].includes(
    value,
  );

export default {
  name: 'base-button',

  props: {
    colour: {
      type: String,
      default: 'base',
      validator: colourValidator,
    },

    disabled: {
      type: Boolean,
      default: false,
    },

    layout: {
      type: String,
      default: 'inline',
      validator: value => ['block', 'inline'].includes(value),
    },

    loading: {
      type: Boolean,
      default: false,
    },

    size: {
      type: String,
      default: 'base',
      validator: value => [
        'small',
        'base',
        'large',
      ].includes(value),
    },

    type: {
      type: String,
      default: 'base',
      validator: value => [
        'base',
        'outline',
        'link',
      ].includes(value),
    },

    icon: {
      type: String,
      required: false,
    },

    shadow: {
      type: String,
      default: 'base',
      validator: value => ['base', 'large'].includes(value),
    },
  },

  iconComponent: val => feather[`${upperFirst(camelCase(val))}Icon`],

  classList: (data, props) => {
    return `${data.staticClass ? data.staticClass : ''} ${Object.entries(props)
      .filter(entry => !['icon'].includes(entry[0]))
      .map(entry => entry.join('-'))
      .join(' ')}`;
  },

  clickEvent: (props, attrs) => {
    if (props.disabled && !attrs.onClick) {
      return attrs.onStop;
    }
    return () => {};
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.base-button {
  @include flex("block", "row", "center", "center");

  border-radius: 3px;
  cursor: pointer;
  font-size: 0.65rem;
  font-weight: $font-weight-bold;
  outline: none;
  padding: 0.8em;
  transition: all $interaction-transition-time;

  // Block or inline
  &.layout-block {
    @include flex("block", "row", "center", "center");
    @include grow;
  }

  &.layout-inline {
    @include flex("inline", "row", "center", "center");
  }

  // Size options
  &.size-small {
    font-size: 0.7rem;
    padding: 0.6rem 0.7rem;
  }

  &.size-base {
    font-size: $font-size-sm;
    padding: 0.8rem 1.8rem;
  }

  &.size-large {
    font-size: $font-size-base;
    padding: 1rem 2.6rem;
  }

  &.disabled-true {
    cursor: default;
    opacity: 0.5;
  }

  // Colour options
  &.colour-base {
    &.type-base {
      background-color: clr("purple");
      color: clr("white");

      &:hover,
      &:focus {
        background-color: darken(clr("purple"), 15%);
        outline: none;
      }
    }

    &.type-outline {
      border: $border-light solid clr("purple");
      color: clr("purple");

      &:hover,
      &:focus {
        background-color: clr("purple");
        color: clr("white");
        outline: none;
      }
    }

    &.type-link {
      color: clr("purple");

      &:hover,
      &:focus {
        color: darken(clr("purple"), 5%);
        outline: none;
      }
    }
  }

  &.colour-dark {
    &.type-base {
      background-color: clr("purple", "rich");
      color: clr("white");

      &:hover,
      &:focus {
        background-color: darken(clr("purple", "rich"), 12%);
        outline: none;
      }
    }

    &.type-outline {
      border: $border-light solid clr("purple", "rich");
      color: clr("purple", "rich");

      &:hover,
      &:focus {
        background-color: clr("purple", "rich");
        color: clr("white");
        outline: none;
      }
    }

    &.type-link {
      color: clr("purple");
    }
  }

  &.colour-dropdown {
    font-weight: $font-weight-normal;

    > span {
      justify-content: space-between;
    }

    &.type-base {
      /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#ffffff+0,f9fafb+100 */
      background: rgb(255, 255, 255); /* Old browsers */
      background: -moz-linear-gradient(
        top,
        rgba(255, 255, 255, 1) 0%,
        rgba(249, 250, 251, 1) 100%
      ); /* FF3.6-15 */
      background: -webkit-linear-gradient(
        top,
        rgba(255, 255, 255, 1) 0%,
        rgba(249, 250, 251, 1) 100%
      ); /* Chrome10-25,Safari5.1-6 */
      background: linear-gradient(
        to bottom,
        rgba(255, 255, 255, 1) 0%,
        rgba(249, 250, 251, 1) 100%
      ); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
      filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#f9fafb',GradientType=0 ); /* IE6-9 */

      border: $border-standard;
      color: $body-copy;

      &:hover,
      &:focus {
        /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#ffffff+0,f4f7f9+100 */
        background: rgb(255, 255, 255); /* Old browsers */
        background: -moz-linear-gradient(
          top,
          rgba(255, 255, 255, 1) 0%,
          rgba(244, 247, 249, 1) 100%
        ); /* FF3.6-15 */
        background: -webkit-linear-gradient(
          top,
          rgba(255, 255, 255, 1) 0%,
          rgba(244, 247, 249, 1) 100%
        ); /* Chrome10-25,Safari5.1-6 */
        background: linear-gradient(
          to bottom,
          rgba(255, 255, 255, 1) 0%,
          rgba(244, 247, 249, 1) 100%
        ); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#f4f7f9',GradientType=0 ); /* IE6-9 */
      }
    }
  }

  &.colour-light {
    &.type-base {
      background-color: clr("blue", "lighter");
      color: $body-copy-light;

      &:hover {
        background-color: darken(clr("blue", "lighter"), 5%);
        color: darken($body-copy-light, 20%);
      }
    }

    &.type-link {
      color: $body-copy-light;

      &:hover {
        color: $body-copy;
      }
    }

    &.type-outline {
      border: $border-standard;
      color: $body-copy-light;

      &:hover,
      &:focus {
        background-color: $body-copy-light;
        color: clr("white");
        outline: none;
      }
    }
  }

  &.colour-danger {
    &.type-base {
      background-color: clr("red");
      color: clr("white");

      &:hover,
      &:focus {
        background-color: darken(clr("red"), 10%);
      }
    }

    &.type-outline {
      border: $border-light solid clr("red");
      color: clr("red");

      &:hover,
      &:focus {
        background-color: clr("red");
        color: clr("white");
      }
    }

    &.type-link {
      color: clr("red");

      &:hover,
      &:focus {
        color: darken(clr("red"), 10%);
      }
    }
  }

  &.colour-success {
    &.type-base {
      background-color: darken(clr("green"), 8%);
      color: clr("white");

      &:hover,
      &:focus {
        background-color: darken(clr("green"), 12%);
      }
    }

    &.type-outline {
      border: $border-light solid darken(clr("green"), 8%);
      color: darken(clr("green"), 8%);

      &:hover,
      &:focus {
        background-color: darken(clr("green"), 8%);
        color: clr("white");
      }
    }

    &.type-link {
      color: darken(clr("green"), 8%);

      &:hover,
      &:focus {
        color: darken(clr("green"), 12%);
      }
    }
  }

  &.colour-warning {
    &.type-base {
      background-color: clr("yellow");
      color: clr("white");

      &:hover,
      &:focus {
        background-color: darken(clr("yellow"), 10%);
      }
    }

    &.type-outline {
      border: $border-light solid clr("yellow");
      color: clr("yellow");

      &:hover,
      &:focus {
        background-color: clr("yellow");
        color: clr("white");
      }
    }

    &.type-link {
      color: clr("yellow");

      &:hover,
      &:focus {
        color: darken(clr("yellow"), 10%);
      }
    }
  }

  &.colour-qualtrics {
    background-color: $qualtrics;
    color: clr('white');

    &:hover,
    &:focus {
      background-color: darken($qualtrics, 5%);
    }
  }

  &.colour-data {
    &.type-base {
      background-color: $dataimport;
      color: clr("white");

      &:hover,
      &:focus {
        background-color: lighten($dataimport, 10%);
        outline: none;
      }
    }

    &.type-link {
      color: $dataimport;

      &:hover,
      &:focus {
        color: lighten($dataimport, 15%);
        outline: none;
      }
    }

    &.type-outline {
      border: $border-light solid $dataimport;
      color: $dataimport;

      &:hover,
      &:focus {
        background-color: $dataimport;
        color: clr("white");
        outline: none;
      }
    }
  }

  &.colour-surveymonkey {
    background-color: $surveymonkey;
    color: clr('white');

    &:hover,
    &:focus {
      background-color: darken($surveymonkey, 5%);
    }
  }

  &.colour-zendesk {
    background-color: $zendesk;
    color: clr('white');

    &:hover,
    &:focus {
      background-color: darken($zendesk, 5%);
    }
  }

  &.shadow-large {
    box-shadow: 0 2px 50px 0 rgba(45, 23, 87, 0.2);

    &:hover,
    &:focus {
      box-shadow: 0 2px 100px 0 rgba(45, 23, 87, 0.2);
    }
  }

  .base-icon {
    height: 1.2em;
    margin-right: 0.3em;
  }

  > section {
    @include flex("block", "row", "center", "center");
    @include grow;
  }
}
</style>
