<template>
  <input
    class="base-input"
    :class="[classList, {disable: isDisabled}]"
    :disabled="isDisabled"
    :placeholder="placeholder"
    :type="type"
    :value="value"
    @blur="onBlur"
    @change="onChange"
    @input="onInput"
    @keydown.prevent.enter="onSubmit"
  />
</template>

<script>
export default {
  name: 'base-input',

  props: {
    focus: {
      type: Boolean,
      default: false,
    },
    isDisabled: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: String,
      required: false,
    },
    type: {
      type: String,
      default: 'text',
    },
    value: {
      value: [Number, String],
      default: '',
    },
  },

  computed: {
    classFilter() {
      return [
        'placeholder',
        'type',
        'value',
      ];
    },

    classList() {
      return Object.entries(this.$props)
        .filter(entry => !this.classFilter.includes(entry[0]))
        .map(entry => entry.join('-'))
        .join(' ');
    },
  },

  mounted() {
    if (this.focus) this.$el.focus();
  },

  methods: {
    onBlur(e) {
      this.$emit('blur', e.target.value, e.target);
    },

    onChange(e) {
      this.$emit('change', e.target.value, e.target);
    },

    onInput(e) {
      this.$emit('input', e);
    },

    onSubmit(e) {
      this.$emit('change', e.target.value, e.target);
      this.$emit('submit', e.target.value, e.target);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.base-input {
  background-color: clr('white');
  border: $border-standard;
  border-radius: $border-radius-medium;
  color: $body-copy;
  padding: 0.5rem;
  transition: all $interaction-transition-time;
  width: 100%;

  &:hover {
    border: $border-light solid mix($border-color, clr("purple"), 75%);
  }

  &:focus {
    border: $border-light solid clr("purple");
    outline: none;
  }

  &.disable {
    background-color: #d3d3d3;
    color: #4a4a4a;
    cursor: not-allowed;
  }
}
</style>
