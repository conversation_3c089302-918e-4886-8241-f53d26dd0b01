<template>
  <section class="registration-table">
    <section class="registration-toolbar">
      <section class="registration-pagination">
        <pagination  :current-page="page" :last="last" @onBtPrevious="onBtPrevious" @onBtNext="onBtNext" @onSetPage="onSetPage"></pagination>
      </section>
    </section>
    <section class="registration-gird">
      <ag-grid-vue
          style="width: 100%; height: 100%"
          class="ag-theme-alpine"
          :columnDefs="columnDefs"
          :rowData="registrationsMap"
          rowSelection='single'
          @grid-ready="onGridReady"
          @filter-changed="onFilterChanged"
      />
    </section>
  </section>
</template>

<script>
import { AgGridVue } from 'ag-grid-vue3';
import { mapActions, mapState } from 'vuex';

import GridListCell from '@/components/Base/GridListCell';
import registrationRequest from '@/services/RegistrationRequest';
import BaseButton from '@/components/Base/BaseButton';
import Pagination from '@/components/Pagination/pagination';

export default {
  name: 'registration-table',

  components: {
    Pagination,
    BaseButton,
    AgGridVue,
    GridListCell,
  },

  data() {
    return {
      gridApi: null,
    };
  },

  computed: {
    ...mapState('registration', [
      'registrations',
      'page',
      'rows',
    ]),

    columnDefs() {
      return [
        {
          id: 'Id',
          field: 'id',
          width: 100,
          wrapText: true,
          autoHeight: true,
          checkboxSelection: true,
        },
        {
          headerName: 'Email',
          field: 'email',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: ['contains'],
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
        },
        {
          headerName: 'Registration Status',
          field: 'registrationStatus',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: [
              {
                displayKey: 'choose_one',
                displayName: 'Choose one',
                predicate() {
                  return true;
                },
                numberOfInputs: 0,
              },
              {
                displayKey: 'registered',
                displayName: 'REGISTERED',
                predicate(_, cellValue) {
                  return null || /registered/gm.test(cellValue?.toLowerCase());
                },
                numberOfInputs: 0,
              },
              {
                displayKey: 'verified',
                displayName: 'VERIFIED',
                predicate(_, cellValue) {
                  return null || /verified/gm.test(cellValue?.toLowerCase());
                },
                numberOfInputs: 0,
              },
              {
                displayKey: 'invite_pending',
                displayName: 'INVITE PENDING',
                predicate(_, cellValue) {
                  return null || /invite_pending/gm.test(cellValue?.toLowerCase());
                },
                numberOfInputs: 0,
              },
            ],
            defaultOption: 'choose_one',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
        },
        {
          headerName: 'First Name',
          field: 'firstName',
          flex: 1,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Last Name',
          field: 'lastName',
          flex: 1,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Workspace label',
          field: 'workspaceLabel',
          flex: 1,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Workspace Role',
          field: 'role',
          flex: 1,
          wrapText: true,
          autoHeight: true,
        },
      ];
    },

    registrationsMap() {
      return this.registrations.map(o => ({
        ...o,
        workspace: `${o.workspaceLabel}_[${o.workspaceId}]`,
      }));
    },

    last() {
      return this.registrations.length < this.rows;
    },
  },

  beforeMount() {
    this.doCallApiFetchRegistrations();
  },

  methods: {
    ...mapActions('registration', [
      'setPage',
      'setFilterStatus',
      'setSearch',
    ]),

    async doCallApiFetchRegistrations() {
      await registrationRequest.fetchRegistrations();
    },

    onBtPrevious() {
      this.setPage({ page: this.page - 1 });
      this.doCallApiFetchSavedActions();
    },

    onBtNext() {
      this.setPage({ page: this.page + 1 });
      this.doCallApiFetchSavedActions();
    },

    onSetPage(page) {
      this.setPage({ page });
      this.doCallApiFetchSavedActions();
    },

    onGridReady(params) {
      this.gridApi = params.api;
    },

    onFilterChanged(e) {
      const model = e.api.getFilterModel();
      this.setFilterStatus((model.status?.type === 'choose_one' ? '' : model.type?.type) || '');
      this.setSearch(model.email?.filter || '');

      this.doCallApiFetchSavedActions();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.registration-table {
  @include flex("block", "column", "start", "start");

  height: 100%;
  width: 100%;

  .registration-toolbar {
    @include flex("block", "row", "start", "start");

    width: 100%;

    .registration-actions {
      @include flex("block", "row", "start", "start");
      padding-left: 1rem;
      column-gap: 1em;
      margin: 0.2em;

      .icon {
        margin-right: 0.2em;
      }
    }

    .registration-pagination {
      @include flex("block", "row", "end", "start");

      padding-right: 2rem;
      width: 100%;
    }
  }

  .registration-gird {
    @include flex("block", "row", "start", "start");

    height: 100%;
    padding: 1rem 0.6rem 0 0.6rem;
    width: 100%;

    .title {
      @include flex("block", "row", "center", "start");

      font-size: 1.5rem;
      font-weight: $font-weight-bold;
    }

    .base-button {
      margin-left: auto;
    }
  }
}
</style>
