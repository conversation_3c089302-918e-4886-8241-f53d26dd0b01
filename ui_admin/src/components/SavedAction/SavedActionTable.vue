<template>
  <section class="saved-action-table">
    <section class="saved-action-toolbar">
      <section class="saved-action-actions">
        <base-button :colour="'danger'" :size="'small'" @click="onClickDelete">
          <i class="fa-regular fa-trash icon"></i>
          Delete
        </base-button>
      </section>
      <section class="saved-action-pagination">
        <pagination  :current-page="page" :last="last" @onBtPrevious="onBtPrevious" @onBtNext="onBtNext" @onSetPage="onSetPage"></pagination>
      </section>
    </section>
    <section class="saved-action-gird">
      <ag-grid-vue
          style="width: 100%; height: 100%"
          class="ag-theme-alpine"
          :columnDefs="columnDefs"
          :rowData="savedActionsMap"
          rowSelection='multiple'
          @grid-ready="onGridReady"
          @filter-changed="onFilterChanged"
      />
    </section>
  </section>
</template>

<script>
import { AgGridVue } from 'ag-grid-vue3';

import BaseButton from '@/components/Base/BaseButton';
import Pagination from '@/components/Pagination/pagination';
import savedActionRequest from '@/services/SavedActionRequest';
import { mapActions, mapState } from 'vuex';
import SavedActionCellRender from '@/components/SavedAction/SavedActionCellRender';
import SavedActionDeleteModal from '@/components/SavedAction/SavedActionDeleteModal';

export default {
  name: 'saved-action-table',

  components: {
    AgGridVue,
    BaseButton,
    Pagination,
  },

  data() {
    return {
      gridApi: null,
      defaultColDef: {
        filter: true,
      },
    };
  },

  computed: {
    ...mapState('savedAction', [
      'savedActions',
      'pages',
      'rows',
      'page',
    ]),

    columnDefs() {
      return [
        {
          id: 'Id',
          field: 'id',
          width: 100,
          wrapText: true,
          autoHeight: true,
          headerCheckboxSelection: true,
          checkboxSelection: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
            filterOptions: [
              {
                displayName: 'Separate ids by ,',
                displayKey: 'contains',
                predicate() {
                  return true;
                },
                numberOfInputs: 1,
              },
            ],
          },
        },
        {
          headerName: 'Workspace label',
          field: 'workspace',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: ['contains'],
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
        },
        {
          headerName: 'User Name',
          field: 'user',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: ['contains'],
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
        },
        {
          headerName: 'Action Type',
          field: 'type',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: [
              {
                displayKey: 'choose_one',
                displayName: 'Choose one',
                predicate() {
                  return true;
                },
                numberOfInputs: 0,
              },
              {
                displayKey: 'CREATE_SEARCH_THEME',
                displayName: 'Search',
                predicate(_, cellValue) {
                  return null || /search/gm.test(cellValue?.toLowerCase());
                },
                numberOfInputs: 0,
              },
              {
                displayKey: 'DELETE_THEME',
                displayName: 'Delete',
                predicate(_, cellValue) {
                  return null || /delete/gm.test(cellValue?.toLowerCase());
                },
                numberOfInputs: 0,
              },
              {
                displayKey: 'MERGE_THEMES',
                displayName: 'Merge',
                predicate(_, cellValue) {
                  return null || /merge/gm.test(cellValue?.toLowerCase());
                },
                numberOfInputs: 0,
              },
              {
                displayKey: 'RENAME_THEME',
                displayName: 'Rename',
                predicate(_, cellValue) {
                  return null || /rename/gm.test(cellValue?.toLowerCase());
                },
                numberOfInputs: 0,
              },
            ],
            defaultOption: 'choose_one',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
        },
        {
          headerName: 'New Theme',
          field: 'newThemeLabel',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: ['contains'],
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
        },
        {
          headerName: 'Action Components',
          field: 'components',
          flex: 1,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Actions',
          field: '',
          width: 200,
          wrapText: true,
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: SavedActionCellRender };
          },
        },
      ];
    },

    savedActionsMap() {
      return this.savedActions.map(o => ({
        ...o,
        components: this.parseComponents(o.type, o.components),
        newThemeLabel: o.components.newLabel,
        type: this.convertSavedActionType(o.type),
        user: o.user.loginName,
        workspace: o.workspace.label,
      }));
    },

    last() {
      return this.savedActions.length < this.rows;
    },

    selectedSavedActions() {
      return this.gridApi.getSelectedRows();
    },
  },

  beforeMount() {
    this.doCallApiFetchSavedActions();
  },

  methods: {
    ...mapActions('savedAction', [
      'setFilterIds',
      'setFilterName',
      'setFilterThemeLabel',
      'setFilterType',
      'setFilterWorkspaceLabel',
      'setPage',
      'setSelectedSavedActions',
    ]),

    ...mapActions('toast', ['addToast']),

    ...mapActions('modal', ['setModal']),

    async doCallApiFetchSavedActions() {
      await savedActionRequest.fetchSavedActions();
    },

    onBtPrevious() {
      this.setPage({ page: this.page - 1 });
      this.doCallApiFetchSavedActions();
    },

    onBtNext() {
      this.setPage({ page: this.page + 1 });
      this.doCallApiFetchSavedActions();
    },

    onSetPage(page) {
      this.setPage({ page });
      this.doCallApiFetchSavedActions();
    },

    convertSavedActionType(type) {
      switch (type) {
        case 'MERGE_THEMES':
          return 'Merge';
        case 'CREATE_SEARCH_THEME':
          return 'Search';
        case 'RENAME_THEME':
          return 'Rename';
        case 'DELETE_THEME':
          return 'Delete';
        default:
          return type;
      }
    },

    parseComponents(type, components) {
      switch (type) {
        case 'MERGE_THEMES':
          return `${components.mergeThemesLabels}`;
        case 'CREATE_SEARCH_THEME':
          return `${components.searchQuery}`;
        case 'RENAME_THEME':
          return `${components.originalLabel}`;
        case 'DELETE_THEME':
          return '';
        default:
          return null;
      }
    },

    onGridReady(params) {
      this.gridApi = params.api;
    },

    onFilterChanged(e) {
      const model = e.api.getFilterModel();
      this.setFilterType((model.type?.type === 'choose_one' ? '' : model.type?.type) || '');
      this.setFilterThemeLabel(model.newThemeLabel?.filter || '');
      this.setFilterName(model.user?.filter || '');
      this.setFilterWorkspaceLabel(model.workspace?.filter || '');
      this.setFilterIds('id' in model ? [model.id.filter.replaceAll(/[^0-9,]+/g, '')] : []);

      this.doCallApiFetchSavedActions();
    },

    async onClickDelete() {
      if (this.gridApi.getSelectedRows().length < 1) {
        return;
      }
      this.setSelectedSavedActions(this.selectedSavedActions);
      await this.setModal(SavedActionDeleteModal);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.saved-action-table {
  @include flex("block", "column", "start", "start");

  height: 100%;
  width: 100%;

  .saved-action-toolbar {
    @include flex("block", "row", "start", "start");

    width: 100%;

    .saved-action-actions {
      @include flex("block", "row", "start", "start");
      padding-left: 1rem;
      column-gap: 1em;
      margin: 0.2em;

      .icon {
        margin-right: 0.2em;
      }
    }

    .saved-action-pagination {
      @include flex("block", "row", "end", "start");

      padding-right: 2rem;
      width: 100%;
    }
  }

  .saved-action-gird {
    @include flex("block", "row", "start", "start");

    height: 100%;
    padding: 1rem 0.6rem 0 0.6rem;
    width: 100%;

    .title {
      @include flex("block", "row", "center", "start");

      font-size: 1.5rem;
      font-weight: $font-weight-bold;
    }

    .base-button {
      margin-left: auto;
    }
  }
}
</style>
