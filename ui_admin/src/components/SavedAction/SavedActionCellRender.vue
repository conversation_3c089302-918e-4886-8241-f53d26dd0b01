<template>
  <section class="saved-action-cell-render">
    <section class="item">
      <base-button :size="'small'" :colour="'danger'" @click="onClickDelete">
        <i class="fa-regular fa-trash icon"></i> Delete
      </base-button>
    </section>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import SavedActionDeleteModal from '@/components/SavedAction/SavedActionDeleteModal';

export default {
  name: 'saved-action-cell-render',

  components: {
    BaseButton,
  },

  computed: {
    savedAction() {
      return this.params.data;
    },

  },

  methods: {
    ...mapActions('modal', ['setModal']),

    ...mapActions('toast', ['addToast']),

    ...mapActions('savedAction', ['setSelectedSavedActions']),

    async onClickDelete() {
      await this.setSelectedSavedActions([this.savedAction]);
      await this.setModal(SavedActionDeleteModal);
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.saved-action-cell-render {
  @include flex("block", "column", "start", "start");

  .item {
    @include flex("block", "row", "center", "center");

    border-radius: 4px;
    color: clr('white');
    height: 2rem;
    margin: 0.2rem;

    .base-button {
      height: 2rem;
    }

    .icon {
      margin-right: 0.2rem;
    }
  }
}
</style>
