<template>
  <transition-group name="toast-tr" tag="section" class="the-toast" :class="{ visible }">
    <component
      v-for="toast in queue"
      :key="toast.id"
      :is="toast.component"
      :id="toast.id"
      class="toast"
    >{{ toast.message || '' }}</component>
  </transition-group>
</template>

<script>
import { mapState } from 'vuex';

export default {
  name: 'the-toast',

  computed: {
    ...mapState('toast', ['queue']),

    visible() {
      return this.queue.length > 0;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.the-toast {
  padding: 1rem;
  pointer-events: none;
  position: absolute;
  width: 100%;

  &.visible {
    pointer-events: inherit;
  }

  .toast {
    margin-bottom: 0.5rem;
  }

  .toast-tr-enter-active,
  .toast-tr-leave-active {
    transition: transform 0.2s, opacity 0.2s;
  }

  .toast-tr-enter,
  .toast-tr-leave-to {
    transform: translateY(-100%);
    opacity: 0;
  }
}
</style>
