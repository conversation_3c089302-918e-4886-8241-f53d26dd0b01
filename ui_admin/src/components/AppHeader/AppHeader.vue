<template>
  <section class="app-header">
    <span>Hi {{fullName}}</span>
    <base-button :colour="'danger'" :size="'small'" @click.stop="logOut">Log out</base-button>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import AuthRequest from '@/services/AuthRequest';
import BaseButton from '@/components/Base/BaseButton';
import Route from '@/enum/route';

export default {
  name: 'app-header',

  components: {
    BaseButton,
  },

  computed: {
    ...mapState('user', ['user']),

    fullName() {
      if (this.user) return `${this.user.firstName} ${this.user.lastName}`;
      return '';
    },
  },

  methods: {
    async logOut() {
      await AuthRequest.logout();
      await this.$router.push({ name: Route.LOGIN });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.app-header {
  @include flex("block", "row", "start", "center");
  @include rigid;

  background: #DCC3FF;
  height: 50px;
  padding: 0 1rem;
  width: 100%;
  z-index: 1;

  .base-button {
    margin-left: auto;
  }
}
</style>
