<template>
  <section class="the-modal" @mousedown.self="closeModal">
    <component :is="comp" :componentProps="componentProps" class="component" />
  </section>
</template>

<script>
import { mapActions } from 'vuex';
import { markRaw } from 'vue';

export default {
  name: 'the-modal',

  data() {
    return {
      comp: markRaw(this.component),
    };
  },

  props: {
    component: {
      type: Object,
      required: true,
    },

    componentProps: {
      type: Object,
      required: false,
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

@keyframes fade {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes grow-fade {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.the-modal {
  @include flex("block", "row", "center", "center");

  animation: fade 0.1s ease-out;
  background-color: rgba(0, 0, 0, 0.8);
  position: absolute;
  height: 100%;
  overflow: auto;
  width: 100%;
  z-index: 100;

  .component {
    animation: grow-fade 0.1s ease-out;
  }
  .base-button {
    text-transform: uppercase !important;
    background: #FF0000;
  }
}
</style>
