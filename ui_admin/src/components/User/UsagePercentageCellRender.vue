<template>
  <section class="user-percentage-cell-render">
    <section id="progress-bar"
      class="progress-bar bg-primary"
      @click="displayUsage"
    >
      <section  class="progress-bar-fill" :style="{ width: usagePercentage}">
        <span>{{usagePercentage}}</span>
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import CompareUsageModal from '@/components/User/UserUsageModal';

export default {
  name: 'user-percentage-cell-render',
  components: { BaseButton },

  computed: {
    user() {
      return this.params.data;
    },

    usagePercentage() {
      return this.user.usagePercentage;
    },
  },

  methods: {
    ...mapActions('modal', ['setModal']),

    ...mapActions('user', ['setSelectedUsers']),

    displayUsage() {
      this.setSelectedUsers([this.user]);
      this.setModal(CompareUsageModal);
    },
  },
};

</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.user-percentage-cell-render {

  margin: 0.2rem;
  cursor: pointer;

  .progress-bar {
    background-color: $border-color;
    border-radius: 4px;
    width: 100%;
  }

  .progress-bar-fill {
    @include flex("block", "column", "center", "center");

    background: linear-gradient(90deg, #561c94, #a15aef) repeat-x;
    background-size: 100% 100%;
    border-radius: 4px;
    height: 22px;
    padding: 3px;

    transition: width 500ms ease-in-out;

    span {
      color: white;
      font-size: 12px;
    }
  }
}

</style>
