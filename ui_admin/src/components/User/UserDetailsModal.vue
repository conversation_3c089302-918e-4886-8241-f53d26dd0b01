<template>
  <section class="user-details-modal">
  <section class="header">
    <h2>{{label}}</h2>
  </section>
  <section class="body">
    <section class="field row" v-if="!isEditMode">
      <span>Login name</span>
      <base-input placeholder="Enter login name" :value="localUser.loginName" @input="onInputLoginName"/>
    </section>
    <section class="field row">
      <span>Email</span>
      <base-input placeholder="Enter a email" :value="localUser.email" @input="onInputEmail"/>
    </section>
    <section class="field row">
      <span>First Name</span>
      <base-input placeholder="First Name" :value="localUser.firstName" @input="onInputFirstName"/>
    </section>
    <section class="field row">
      <span>Last Name</span>
      <base-input placeholder="Last Name" :value="localUser.lastName" @input="onInputLastName"/>
    </section>
    <section class="field row" v-if="!isEditMode">
      <span>Contact Number</span>
      <base-input placeholder="Contact Number" :value="localUser.contactNumber" @input="onInputContactNumber"/>
    </section>
    <section class="field row" v-if="!isEditMode">
      <span>Password</span>
      <base-input placeholder="Leave empty to send the user an email allowing them to set their own password" :value="localUser.password" @input="onInputPassword"/>
    </section>
    <section class="field row">
      <span>Sectors</span>
      <section class="wrapper-select">
        <v-select v-model="localUser.sector" class="v-select" :options="sectorsMap"/>
      </section>
    </section>
    <section class="field row">
      <span>Tier</span>
      <section class="wrapper-select">
        <v-select v-model="localUser.userTier" class="v-select" :options="userTiersMap" />
      </section>
    </section>
    <section class="field row" v-if="isEditMode">
      <section>Organisation/Workspace</section>
      <section class="button-groups">
        <base-button colour="success" size="small" @click="onClickOrganisation">
          <span>Organisation</span>
        </base-button>
        <base-button colour="qualtrics" size="small" @click="onClickWorkspaces">
          <span>Workspaces</span>
        </base-button>
      </section>
      <section class="message" v-if="!hasWorkspaces" >
        * Remember to add workspace for user.
      </section>
    </section>
  </section>
  <section class="footer">
    <base-button class="cancel" colour="light" size="small" type="link"  @click="onCancel">Cancel</base-button>
    <loading-blocks-overlay v-if="creating" />
    <section class="confirm" v-else>
      <base-button v-if="isEditMode" colour="base" size="small" :disabled="createDisabled" @click="onUpdate">
        <span>Update User</span>
      </base-button>
      <base-button v-else colour="base" size="small" :disabled="createDisabled" @click="onCreate">
        <span>Create User</span>
      </base-button>
    </section>
  </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import AddToOrganisationModal from '@/components/User/AddToOrganisationModal';
import AddToWorkspacesModal from '@/components/User/AddToWorkspacesModal';
import BaseButton from '@/components/Base/BaseButton';
import BaseCheckbox from '@/components/Base/BaseCheckbox';
import BaseInput from '@/components/Base/BaseInput';
import BaseRadio from '@/components/Base/BaseRadio';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import userRequest from '@/services/UserRequest';
import UserSectors from '@/enum/user-sectors';
import userTierRequest from '@/services/UserTierRequest';
import UserToast from '@/components/Toast/UserToast';
import UserWorkspaceModal from '@/components/User/UserWorkspaceModal';
import VSelect from 'vue-select';

export default {
  name: 'user-details-modal',
  components: {
    BaseButton,
    BaseCheckbox,
    BaseInput,
    BaseRadio,
    LoadingBlocksOverlay,
    UserWorkspaceModal,
    VSelect,
  },

  data() {
    return {
      localUser: {
        contactNumber: '',
        email: '',
        firstName: '',
        lastName: '',
        sector: '',
        tierId: '',
        userRole: 'ROLE_USER',
        userTier: {},
      },
      creating: false,
    };
  },

  beforeMount() {
    this.doFetchLocalUser();
  },

  beforeDestroy() {
    this.selectedUser({ u: null });
  },

  computed: {
    ...mapState('userTier', ['userTiers']),
    ...mapState('user', ['selectedUser']),
    ...mapState('workspace', ['workspaces']),
    ...mapState('organisation', ['organisations']),

    isEditMode() {
      return this.selectedUser !== null;
    },

    label() {
      return this.isEditMode ? 'Update User' : 'Create User';
    },

    userTiersMap() {
      return this.userTiers.map(u => ({
        id: u.id,
        label: `${u.name} (Id: ${u.id} )`,
      }));
    },

    sectorsMap() {
      return UserSectors.enumValues.map(u => {
        return {
          ...u,
          label: u.titleCase(),
          checked: this.localUser?.sector === u.name,
        };
      });
    },

    createDisabled() {
      return !this.localUser.email
          || !this.localUser.lastName
          || !this.localUser.firstName
          || !this.localUser.userTier;
    },

    hasWorkspaces() {
      return this.localUser.workspaces && this.localUser.workspaces.length > 0;
    },
  },

  methods: {
    ...mapActions('modal', ['setModal', 'closeModal']),

    ...mapActions('toast', ['addToast']),

    ...mapActions('user', ['setSelectedUser']),

    onInputEmail(e) {
      this.localUser.email = e.target.value;
    },

    onInputLoginName(e) {
      this.localUser.loginName = e.target.value;
    },

    onInputFirstName(e) {
      this.localUser.firstName = e.target.value;
    },

    onInputLastName(e) {
      this.localUser.lastName = e.target.value;
    },

    onInputContactNumber(e) {
      this.localUser.contactNumber = e.target.value;
    },

    onInputPassword(e) {
      this.localUser.password = e.target.value;
    },

    doFetchLocalUser() {
      userTierRequest.getUserTiers();
      if (this.isEditMode) {
        this.localUser = this.selectedUser;
        this.localUser.userTier = {
          id: this.selectedUser.userTier.id,
          label: `${this.selectedUser.userTier.name} (Id: ${this.selectedUser.userTier.id} )`,
        };
      }
    },

    async onUpdate() {
      if (this.createDisabled) {
        return;
      }
      this.localUser.tierId = this.localUser.userTier.id;
      this.localUser.sector = this.localUser.sector.name;
      await userRequest.updateUser(this.localUser.id, this.localUser);
      this.closeModal();
      await this.addToast({
        toastComponent: {
          component: UserToast,
          id: 'user-updated',
        },
        toastData: {
          label: this.localUser.email,
        },
      });
      await userRequest.fetchUser();
    },

    async onCreate() {
      if (this.createDisabled) {
        return;
      }
      this.localUser.tierId = this.localUser.userTier.id;
      this.localUser.sector = this.localUser.sector.name;
      this.localUser.userRole = 'ROLE_USER';
      await userRequest.createUser(this.localUser).then((res) => {
        if (res.status) {
          this.addToast({
            toastComponent: {
              component: UserToast,
              id: 'user-error',
            },
            toastData: {
              label: res.data,
            },
          });
        } else {
          this.addToast({
            toastComponent: {
              component: UserToast,
              id: 'user-created',
            },
            toastData: {
              label: this.localUser.email,
            },
          });
          this.setSelectedUser(res);
          this.onClickOrganisation();
        }
      });
    },

    onCancel() {
      this.closeModal();
    },

    onClickOrganisation() {
      this.setModal(AddToOrganisationModal);
    },

    onClickWorkspaces() {
      this.setModal(AddToWorkspacesModal);
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.user-details-modal {
  @include modal;

  position: relative;

  .button-groups {
    @include flex('block', 'row', 'start', 'center');

    column-gap: 1em;
  }

  .field {
    margin-bottom: 0.6rem;

    .sectors {
      @include flex('block', 'row', 'start', 'center');

      margin-top: 0.6rem;
      margin-left: 0.6rem;
    }
  }

  .row {
    display: grid;
    grid-template-columns: 40% 60%
  }

  .wrapper-select {
    height: 30px;
  }

  .v-select {
    background-color: clr('white');
    width: 300px;
  }

  .message {
    font-size: 10px;
    color: red;
  }
}
</style>
