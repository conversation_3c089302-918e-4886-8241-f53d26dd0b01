<template>
  <section class="user-delete-modal">
    <section class="header">
      <h2>Delete User Tier</h2>
    </section>

    <section class="body">
      <section class="text">
        <span v-if="isMultipleUsers"> These users will be delete </span>
        <span v-else> This user will be delete </span>
        <span class="label">&lsquo;{{ label }}&rsquo;</span>
        . Are you sure you wish to continue?
      </section>
    </section>

    <section class="footer">
      <base-button class="cancel" colour="light" type="link" @click="closeModal">Cancel</base-button>
      <base-button class="delete" colour="danger" @click="onClickDelete">Delete</base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import UserToast from '@/components/Toast/UserToast';
import userRequest from '@/services/UserRequest';

export default {
  name: 'user-delete-modal',

  components: {
    BaseButton,
  },

  computed: {
    ...mapState('user', ['selectedUsers']),

    label() {
      return this.selectedUsers.map(u => u.loginName);
    },

    isMultipleUsers() {
      return this.selectedUsers.length > 1;
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('toast', ['addToast']),

    async onClickDelete() {
      await userRequest.deleteUsers([...this.selectedUsers.map(u => u.id)]);
      await userRequest.fetchUser();
      await this.closeModal();
      await this.addToast({
        toastComponent: {
          component: UserToast,
          id: 'user-deleted',
        },
        toastData: {
          label: this.label,
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.user-delete-modal {
  @include modal;

  .body {
    padding: $search-modal-padding;

    .text {
      font-size: $font-size-xs;
      margin: 0.5rem 0;

      .label {
        font-weight: $font-weight-bold;
      }
    }
  }

  .footer {
    font-size: $font-size-md;

    padding: $search-modal-padding;

    .cancel {
      font-weight: $font-weight-normal;
      padding-left: 0;
    }

    .delete {
      font-weight: $font-weight-medium;
    }
  }

  .header {
    @include flex("block", "row", "between", "center");

    padding: $search-modal-padding;

    h2 {
      @include flex("block", "row", "start", "center");

      font-weight: $font-weight-bold;
    }

    .icon {
      @include flex("block", "row", "end", "center");

      cursor: pointer;
      height: 1.25rem;
      opacity: 0.3;
      &:hover {
        opacity: 1;
      }

      &:active,
      &:focus {
        border: none;
        outline: none;
      }
    }
  }
}
</style>
