<template>
  <section class="user-table">
    <ag-grid-vue
      :columnDefs="columnDefs"
      :rowClassRules="rowClassRules"
      :rowData="usersMap"
      :tooltipHideDelay="tooltipHideDelay"
      :tooltipShowDelay="tooltipShowDelay"
      @cellDoubleClicked="updateUser"
      @cellValueChanged="updateUser"
      @grid-ready="onGridReady"
      @filter-changed="onFilterChanged"
      @selection-changed="onSelectionChanged"
      class="ag-theme-alpine"
      rowSelection='multiple'
      style="width: 100%; height: 100%; min-width: max-content"
    />
  </section>
</template>

<script>
import { AgGridVue } from 'ag-grid-vue3';
import { mapActions, mapState } from 'vuex';

import AddToOrganisationModal from '@/components/User/AddToOrganisationModal';
import BaseButton from '@/components/Base/BaseButton';
import GridListCell from '@/components/Base/GridListCell';
import UserActionCellRender from '@/components/User/UserActionCellRender';
import UserRequest from '@/services/UserRequest';
import UserToast from '@/components/Toast/UserToast';

export default {
  name: 'user-table',

  components: {
    AgGridVue,
    BaseButton,
    UserActionCellRender,
  },

  data() {
    return {
      gridApi: null,
      rowClassRules: null,
      tooltipHideDelay: { value: 2000 },
      tooltipShowDelay: { value: 0 },
    };
  },

  beforeUnmount() {
    this.resetFilter();
  },

  computed: {
    ...mapState('user', ['users']),

    ...mapState('userTier', ['userTiers']),

    columnDefs() {
      return [
        {
          id: 'Id',
          field: 'id',
          wrapText: true,
          autoHeight: true,
          headerCheckboxSelection: true,
          checkboxSelection: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
            filterOptions: [
              {
                displayName: 'Separate ids by ,',
                displayKey: 'contains',
                predicate() {
                  return true;
                },
                numberOfInputs: 1,
              },
            ],
          },
          sortable: true,
          resizable: true,
          minWidth: 100,
          width: 100,
        },
        {
          headerName: 'Username',
          field: 'loginName',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: ['contains'],
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
          resizable: true,
          minWidth: 250,
        },
        {
          headerName: 'User Tier',
          id: 'userTier',
          field: 'tierName',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          editable: true,
          cellEditor: 'agSelectCellEditor',
          cellEditorParams: {
            values: this.userTiers.map(t => { return t.name; }),
          },
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: ['contains'],
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
          resizable: true,
          minWidth: 120,
        },
        {
          headerName: 'First & Last Name',
          field: 'name',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          resizable: true,
          minWidth: 150,
        },
        {
          headerName: 'Organisation',
          field: 'organisationName',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: GridListCell };
          },
          tooltipField: 'organisationRole',
          resizable: true,
          minWidth: 150,
        },
        {
          headerName: 'Workspaces',
          field: 'workspaceLabels',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: GridListCell };
          },
          resizable: true,
          minWidth: 150,
        },
        {
          headerName: 'Email',
          field: 'email',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          resizable: true,
          minWidth: 250,
        },
        {
          headerName: 'Admin',
          field: 'admin',
          wrapText: true,
          autoHeight: true,
          resizable: true,
          minWidth: 100,
          width: 100,
        },
        {
          headerName: 'Actions',
          field: '',
          wrapText: true,
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: UserActionCellRender };
          },
          resizable: true,
          minWidth: 150,
          width: 150,
          pinned: 'right',
        },
      ];
    },

    usersMap() {
      return this.users.map(o => ({
        ...o,
        admin: o.role === 1,
        name: `${o.firstName} ${o.lastName}`,
        workspaceLabels: o.workspaces ? o.workspaces.map(w => { return w?.label; }) : [],
        organisationName: o.organisation ? [`${o.organisation.name}`] : [],
        organisationRole: o.organisation ? `Role: ${o.organisation.role}` : '',
        tierName: o.userTier.name,
      }));
    },
  },

  methods: {
    ...mapActions('modal', ['setModal']),

    ...mapActions('user', [
      'resetFilter',
      'setFilterTierId',
      'setSearch',
      'setSelectedUser',
      'setSelectedUsers',
      'setFilterIds',
    ]),

    ...mapActions('toast', ['addToast']),

    onFilterChanged(e) {
      const model = e.api.getFilterModel();

      // Use optional chaining and nullish coalescing for loginName
      this.setSearch(model.loginName?.filter ?? '');

      // Use optional chaining, nullish coalescing, and find's ability to search by value
      const tierId = this.userTiers.find(tier => tier.name === model.tierName?.filter)?.id ?? '';
      this.setFilterTierId(tierId);
      this.setFilterIds('id' in model ? [model.id.filter.replaceAll(/[^0-9,]+/g, '')] : []);

      UserRequest.fetchUser();
    },

    onGridReady(params) {
      this.gridApi = params.api;
      this.rowClassRules = {
        'table-danger': 'data.workspaces.length <=0',
      };
    },

    onSelectionChanged() {
      this.setSelectedUsers(this.gridApi.getSelectedRows());
    },

    updateUser(params) {
      const colId = params.column.getId();
      if (colId === 'tierName') {
        if (params.oldValue !== params.newValue) {
          this.updateUserTier(params.data, params.newValue);
        }
      }

      if (colId === 'organisation') {
        this.setSelectedUser(params.data);
        this.setModal(AddToOrganisationModal);
      }
    },

    updateUserTier(user, tierName) {
      const userTierId = this.userTiers.find(tier => { return tier.name === tierName; })?.id;

      if (userTierId) {
        UserRequest.updateTier(user.id, userTierId).then(() => {
          this.addToast({
            toastComponent: {
              component: UserToast,
              id: 'user-tier-updated',
            },
            toastData: {
              label: user.name,
              tier: tierName,
            },
          });

          UserRequest.fetchUser();
        });
      }
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.user-table {
  @include flex("block", "row", "start", "start");

  height: 100%;
  margin-top: 0.6rem;
  width: 100%;

  .table-danger {
    background-color:#f5c6cb;
  }

  &.editable {
    cursor: pointer; // TODO: this is not worked, double check again later
  }
}
</style>
