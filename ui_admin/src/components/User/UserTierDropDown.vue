<template>
  <section class="user-tier-dropdown">
    <div :style="{overflow: 'hidden', textOverflow: 'ellipsis'}">
      <span :style="{paddingRight: '5px', borderLeft: '10px solid ' + params.value.name}"></span>
      {{params.value.name}}
    </div>
  </section>
</template>
<script>

import VSelect from 'vue-select';
import { mapState } from 'vuex';

export default {
  name: 'user-tier-dropdown',

  components: { VSelect },

  data() {
    return {
      localUserTier: '',
      userTierOptions: [],
    };
  },

  created() {

  },

  computed: {
    ...mapState('userTier', ['userTiers']),
    ...mapState('user', ['selectedUser']),

    user() {
      return this.selectedUser;
    },

    userTiersMap() {
      return this.userTiers.map(o => ({
        ...o,
        label: o.name,
      }));
    },
  },

  methods: {
    updateTier() {

    },
  },

};
</script>
<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.user-tier-dropdown {

  @include modal;

  margin-top: 0.5rem;

  .wrapper-select {
    height: 30px;
  }

  .v-select {
    background-color: clr('white');
    position: absolute;
    width: 150px;
    height: 30px;
  }

  ul {
    max-height: 200px;
    z-index: 99;
    overflow-y: auto;
  }
}

</style>
