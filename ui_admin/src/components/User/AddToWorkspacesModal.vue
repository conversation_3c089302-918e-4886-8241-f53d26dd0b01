<template>
  <section class="add-to-workspace-modal">
    <template v-if="addToExisting" class="body">
      <section class="header">
        Add to workspaces
      </section>
      <section class="body">
        <section class="workspace-item field"
                 v-for="(item, index) in localWorkspaces"
                 :key="index"
        >
          <section class="row field">
            <span> Select workspace</span>
            <section class="wrapper-select">
              <v-select v-model="localWorkspaces[index]"
                        class="v-select"
                        :options="workspaceOptions"
                        :selectable="isOptionSelectable"
                        @search="fetchWorkspaces"
              />
            </section>
          </section>
          <section class="row field">
            <span>Add as role</span>
            <section class="roles field">
              <v-select v-model="localWorkspaces[index].role"
                        class="v-select"
                        :options="workspaceRoleOptions"
              />
            </section>
          </section>
          <section class="row field">
            <span></span>
            <base-button colour="danger" class="remove" @click="onRemoveWorkspace(index)">
              <i class="fa fa-trash"></i>
            </base-button>
          </section>
        </section>
        <section class="row field">
          <span></span>
          <section class="button-groups">
            <base-button @click="onAddToWorkspace">Add a workspace</base-button>
          </section>
        </section>
      </section>
    </template>
    <template v-else class="body">
      <section class="header">
        Create new workspace
      </section>
      <section class="body">
        <section class="field row">
          <span>Workspace label</span>
          <base-input placeholder="Enter workspace label" @input="onInputName"/>
        </section>
        <section class="field row">
          <span>User limit</span>
          <base-input placeholder="Set the workspace user limit" :type="'number'" @input="onInputUserCount"/>
        </section>
        <section class="field row">
          <span>Owner</span>
          <base-input :value="selectedUser.loginName" :is-disabled="true"/>
        </section>
      </section>
    </template>
    <section class="footer">
      <base-button class="cancel" colour="light" type="link" @click="closeModal">Cancel</base-button>
      <base-button @click="onAddToExisting" v-if="!addToExisting">Add to existing workspaces</base-button>
      <base-button @click="onAddNewWorkspace" v-else>Create new workspace</base-button>
      <base-button colour="base" @click="onSave" :disabled="saveDisabled">Save</base-button>
    </section>
  </section>

</template>
<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import BaseRadio from '@/components/Base/BaseRadio';
import UserToast from '@/components/Toast/UserToast';
import VSelect from 'vue-select';
import WorkspaceToast from '@/components/Toast/WorkspaceToast';
import userRequest from '@/services/UserRequest';
import workspaceRequest from '@/services/WorkspaceRequest';
import { debounce } from 'lodash-es';

export default {
  name: 'add-to-workspace-modal',
  components: { BaseInput, VSelect, BaseRadio, BaseButton },

  computed: {
    ...mapState('organisation', ['organisations']),
    ...mapState('workspace', ['workspaces']),
    ...mapState('user', ['selectedUser']),

    workspaceRoleOptions() {
      return [
        'ADMIN',
        'EDITOR',
        'VIEWER',
      ];
    },

    saveDisabled() {
      if (this.addToExisting) {
        return this.localWorkspaces.find(w => !w.label || !w.role) != null;
      }
      return !this.newWorkspace.label || !this.newWorkspace.userLimit;
    },
  },

  created() {
    this.fetchLocalWorkspaces();
  },

  beforeUnmount() {
    this.reset();
  },

  data() {
    return {
      localWorkspaces: [],
      localWorkspace: {
        id: '',
        label: '',
        role: '',
      },
      workspaceOptions: [],
      addToExisting: true,
      newWorkspace: {
        label: '',
        userLimit: 0,
        organisationId: '',
        ownerId: '',
      },
    };
  },

  methods: {
    ...mapActions('modal', ['setModal', 'closeModal']),

    ...mapActions('toast', ['addToast']),
    ...mapActions('workspace', ['setSearch', 'reset']),

    fetchWorkspaces: debounce(async function fetchWorkspaces(search, loading) {
      this.setSearch(search);
      await this.doCallApiFetchWorkspaces();
      loading(false);
    }, 500),

    async doCallApiFetchWorkspaces() {
      this.workspaceOptions = (await workspaceRequest.getWorkspaces())
        .filter(w => w.organisationId === this.selectedUser.organisationId)
        .map(w => ({
          id: w.id,
          label: `${w.label} (Id: ${w.id} )`,
          organisationId: w.organisationId,
          role: w.role,
        }));
    },

    fetchLocalWorkspaces() {
      this.localWorkspaces = this.selectedUser?.workspaces ?? [];
    },

    onAddNewWorkspace() {
      this.addToExisting = false;
    },

    onAddToExisting() {
      this.addToExisting = true;
    },

    async onAddUserToWorkspaces() {
      await userRequest.addUserToWorkspaces(this.selectedUser.id, this.localWorkspaces);
      await userRequest.fetchUser();
      this.closeModal();
      await this.addToast({
        toastComponent: {
          component: UserToast,
          id: 'user-workspace-updated',
        },
        toastData: {
          label: this.selectedUser.email,
          workspace: this.localWorkspaces.map(w => { return w.label; }),
        },
      });
    },

    async onCreateNewWorkspace() {
      if (this.saveDisabled) {
        return;
      }
      this.newWorkspace.organisationId = this.selectedUser.organisationId;
      this.newWorkspace.ownerId = this.selectedUser.id;
      await workspaceRequest.createWorkspace(this.newWorkspace).then((workspace) => {
        if (workspace) {
          this.closeModal();
          this.addToast({
            toastComponent: {
              component: WorkspaceToast,
              id: 'workspace-created',
            },
            toastData: {
              label: this.newWorkspace.label,
            },
          });
        }
      });
      await userRequest.fetchUser();
      this.closeModal();
    },

    async onSave() {
      if (this.saveDisabled) {
        return;
      }
      if (this.addToExisting) {
        await this.onAddUserToWorkspaces();
      } else {
        await this.onCreateNewWorkspace();
      }
    },

    onAddToWorkspace() {
      this.localWorkspaces.push(this.localWorkspace);
    },

    onRemoveWorkspace(index) {
      this.localWorkspaces.splice(index, 1); // 2nd parameter means remove one item only
    },

    onInputName(e) {
      this.newWorkspace.label = e.target.value;
    },

    onInputUserCount(e) {
      this.newWorkspace.userLimit = e.target.value;
    },

    isOptionSelectable(e) {
      return this.selectedUser.organisationId === e.organisationId
          && !this.localWorkspaces.find(t => t.id === e.id);
    },
  },
};
</script>
<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";
.add-to-workspace-modal {
  @include modal;
  position: relative;

  .field {
    margin-bottom: 0.6rem;
  }

  .row {
    display: grid;
    grid-template-columns: 40% 60%
  }

  .wrapper-select {
    height: 30px;
  }

  .v-select {
    background-color: white;
    width: 400px;
  }

  .workspace-item {
    .remove {
      width: 400px;
    }
  }

  .button-groups {
    @include flex("block", "row", "center", "start");

    .base-button {
      margin-right: 2rem;
    }
  }

}
</style>
