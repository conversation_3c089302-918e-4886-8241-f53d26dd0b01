<template>
  <section class="user-action-cell-render">
    <section class="item">
      <base-button :size="'small'" :colour="'dark'" @click="onClickDetail">
        <i class="fa-regular fa-save icon"></i>
        Edit
      </base-button>
    </section>

    <section class="item">
      <base-button :size="'small'" :colour="'base'" @click="onClickUpdatePassword">
        <i class="fa-regular fa-lock icon"></i>
        Password
      </base-button>
    </section>

    <section class="item">
      <base-button :size="'small'" :colour="'danger'" @click="onClickDelete">
        <i class="fa-regular fa-trash icon"></i>
        Delete
      </base-button>
    </section>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import AddToOrganisationModal from '@/components/User/AddToOrganisationModal';
import AddToWorkspacesModal from '@/components/User/AddToWorkspacesModal';
import BaseButton from '@/components/Base/BaseButton';
import UpdatePasswordModal from '@/components/User/UpdatePasswordModal';
import UserDetailsModal from '@/components/User/UserDetailsModal';
import UserDeleteModal from '@/components/User/UserDeleteModal';

export default {
  name: 'user-action-cell-render',

  components: {
    BaseButton,
  },

  computed: {
    user() {
      return this.params.data;
    },
  },

  methods: {
    ...mapActions('modal', ['setModal']),

    ...mapActions('user', ['setSelectedUser', 'setSelectedUsers']),

    onClickDetail() {
      this.setSelectedUser(this.user);
      this.setModal(UserDetailsModal);
    },

    onClickUpdatePassword() {
      this.setSelectedUser(this.user);
      this.setModal(UpdatePasswordModal);
    },

    onClickOrganisation() {
      this.setSelectedUser(this.user);
      this.setModal(AddToOrganisationModal);
    },

    onClickWorkspaces() {
      this.setSelectedUser(this.user);
      this.setModal(AddToWorkspacesModal);
    },

    onClickDelete() {
      this.setSelectedUsers([this.user]);
      this.setModal(UserDeleteModal);
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";
.user-action-cell-render {
  @include flex("block", "column", "start", "start");

  .item {
    @include flex("block", "row", "center", "center");

    border-radius: 4px;
    color: clr('white');
    height: 2rem;
    margin: 0.2rem;

    .base-button {
      height: 2rem;
    }

    .icon {
      margin-right: 0.2em;
    }
  }
}
</style>
