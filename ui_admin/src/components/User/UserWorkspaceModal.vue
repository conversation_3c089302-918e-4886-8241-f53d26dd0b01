<template>
  <section class="user-workspace-modal">
    <section class="field row">
     <section class="row" :class="{ disable: !this.organisationId }">
       <base-radio :value="!isCreateNewWorkspace" @click="addNewWorkspace = false"/>Add to existing workspace
     </section>
      <section class="field row">
        <section class="existing-workspace" v-if="!isCreateNewWorkspace">
          <v-select v-model="localWorkspace"
                    class="v-select"
                    :options="workspacesMap"
          />
        </section>
      </section>
      <section class="field row role" v-if="!isCreateNewWorkspace">
        <section>Role</section>
        <section class="row" v-if="localWorkspace.label">
          <v-select v-model="localRole"
                    class="v-select"
                    :options="workspaceRoles"
          />
      </section>
      </section>
    </section>

    <section class="field row">
      <section class="row">
        <base-radio :value="isCreateNewWorkspace" @click="addNewWorkspace = true"/> Create new workspace
      </section>
    </section>

    <section class="field row">
      <section class="new-workspace" v-if="isCreateNewWorkspace">
        <section class="field row">
          <span>Workspace label</span>
          <base-input placeholder="Enter workspace label" :value="localWorkspace.label" @input="onInputName"/>
        </section>
        <section class="field row">
          <span>User limit</span>
          <base-input placeholder="Set the workspace user limit" :type="'number'" :value="localWorkspace.userLimit" @input="onInputUserCount"/>
        </section>
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseCheckbox from '@/components/Base/BaseCheckbox';
import BaseInput from '@/components/Base/BaseInput';
import BaseRadio from '@/components/Base/BaseRadio';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import VSelect from 'vue-select';
import WorkspaceRole from '@/enum/workspace-role';

export default {
  name: 'user-workspace-modal',
  components: { BaseRadio, VSelect, BaseInput, BaseCheckbox, BaseButton, LoadingBlocksOverlay },

  data() {
    return {
      localUser: null,
      creating: false,
      localWorkspace: {
        role: WorkspaceRole.ADMIN,
        label: '',
        id: null,
        administratorIds: [],
        editorIds: [],
        viewerIds: [],
      },
      localRole: '',
      addNewWorkspace: false,
      WorkspaceRole,
    };
  },

  props: {
    organisationId: {
      type: Number,
      default: null,
    },
    index: {
      type: Number,
    },
    workspace: {
      type: Object,
      required: false,
    },
  },

  created() {

  },

  computed: {
    ...mapState('userTier', ['userTiers']),
    ...mapState('user', ['selectedUser']),
    ...mapState('workspace', ['workspaces']),
    ...mapState('organisation', ['organisations']),

    isCreateNewWorkspace() {
      return !this.organisationId || this.addNewWorkspace;
    },

    workspacesMap() {
      this.localWorkspace = this.workspace;
      return this.workspaces.filter(w => w.organisationId === this.organisationId).map(w => ({
        id: w.id,
        label: `${w.label} (Id: ${w.id} )`,
      }));
    },

    workspaceRoles() {
      return WorkspaceRole.enumValues.map(v => ({
        ...v,
        label: v.name,
      }));
    },

    isAdmin() {
      return this.localWorkspace.administratorIds.includes(this.selectedUser?.id);
    },

    isEditor() {
      return this.localWorkspace.editorIds.includes(this.selectedUser?.id);
    },

    isViewer() {
      return this.localWorkspace.viewerIds.includes(this.selectedUser?.id);
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),
    ...mapActions('user', ['setSelectedUser']),

    selectRole(role) {
      this.localWorkspace.role = role;
    },

    onCancel() {
      this.closeModal();
    },

    onInputName(e) {
      this.localWorkspace.label = e.target.value;
    },

    onInputUserCount(e) {
      this.localWorkspace.userLimit = e.target.value;
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";
.user-workspace-modal {

  //position: relative;

  .field {
    margin-bottom: 0.6rem;
  }

  .row {
    @include flex('block', 'row', 'start', 'center');
  }

  .disable {
    pointer-events: none;
    opacity: 0.5;
  }

  .column {
    @include flex('block', 'column', 'start', 'center');
  }

  .wrapper-select {
    height: 30px;
  }

  .v-select {
    background-color: clr('white');
    //position: absolute;
    width: 300px;
  }

  .role {
    margin-left: 2rem;
  }
}
</style>
