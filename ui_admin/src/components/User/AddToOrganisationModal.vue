<template>
  <section class="add-to-organisation-modal">
    <template v-if="addToExisting" class="body">
      <section class="header">
        <h2>Add to organisation</h2>
      </section>
      <section class="body">
        <section class="row field">
          Select organisation
          <section class="wrapper-select">
            <v-select v-model="localOrganisation"
                      class="v-select"
                      :options="organisationsMap"
                      label="name"
                      @option:selected="onSelectOrganisation"
            />
          </section>
        </section>
        <section class="row field">
          <span>Add as role</span>
          <section class="wrapper-select">
            <v-select v-model="localOrganisation.role"
                      class="v-select"
                      :options="['ADMIN', 'MEMBER']"
            />
          </section>
        </section>
        <base-button @click="onAddNew" v-if="!hasOrganisation">Add new organisation</base-button>
      </section>
    </template>
    <template v-else class="body">
      <section class="header">
        <h2>Create New Organisation</h2>
      </section>
      <section class="body">
        <section class="field">
          <span>Name</span>
          <base-input placeholder="Enter a organisation name" @input="onInputName" />
        </section>
        <section class="field">
          <span>Label</span>
          <base-input placeholder="Enter a organisation label" @input="onInputLabel"/>
        </section>
        <section class="field">
          <span>Owner</span>
          <base-input :value="newOrganisation.owner" :is-disabled="true"/>
        </section>
        <base-button @click="onAddToExisting">Add to existing organisation</base-button>
      </section>
    </template>
    <section class="footer">
      <base-button class="cancel" colour="light" type="link" @click="closeModal">Cancel</base-button>
      <base-button class="delete" colour="danger" :disabled="saveDisabled" @click="onSave">Save</base-button>
    </section>
  </section>
</template>
<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseRadio from '@/components/Base/BaseRadio';
import userRequest from '@/services/UserRequest';
import UserToast from '@/components/Toast/UserToast';
import VSelect from 'vue-select';
import BaseInput from '@/components/Base/BaseInput';
import organisationRequest from '@/services/OrganisationRequest';

export default {
  name: 'add-to-organisation-modal',
  components: { BaseInput, BaseRadio, BaseButton, VSelect },

  data() {
    return {
      localOrganisation: {
        id: '',
        label: '',
        role: '',
      },
      newOrganisation: {
        ownerId: '',
        name: '',
        settings: {
          label: '',
        },
      },
      changeOrganisationWarning: false,
      addToExisting: true,
    };
  },

  beforeMount() {
    organisationRequest.fetchOrganisations();
    this.fetchLocalOrganisation();
  },

  computed: {
    ...mapState('organisation', ['organisations']),
    ...mapState('user', ['selectedUser']),

    organisationsMap() {
      return this.organisations.map(u => ({
        id: u.id,
        name: `${u.name} (Id: ${u.id} )`,
        role: '',
      }));
    },

    isEditMode() {
      return !this.selectedUser;
    },

    hasOrganisation() {
      return this.selectedUser?.organisation;
    },

    saveDisabled() {
      if (this.addToExisting) {
        return !this.localOrganisation.id || !this.localOrganisation.role;
      }
      return !this.newOrganisation.label;
    },
  },

  methods: {
    ...mapActions('modal', ['setModal', 'closeModal']),
    ...mapActions('toast', ['addToast']),
    ...mapActions('organisation', ['setSelectedOrganisation']),

    onAddNew() {
      this.addToExisting = false;
      this.newOrganisation.ownerId = this.selectedUser.id;
      this.newOrganisation.owner = this.selectedUser.loginName;
    },

    onAddToExisting() {
      this.addToExisting = true;
    },

    fetchLocalOrganisation() {
      if (this.selectedUser.organisation) {
        this.localOrganisation = this.selectedUser.organisation;
      }
    },

    async onAddUserToOrganisation() {
      await userRequest.addUserToOrganisation(this.selectedUser.id, this.localOrganisation).then(async (res) => {
        if (res.status) {
          this.addToast({
            toastComponent: {
              component: UserToast,
              id: 'user-error',
            },
            toastData: {
              label: res.data,
            },
          });
        } else {
          await userRequest.fetchUser();
          await this.addToast({
            toastComponent: {
              component: UserToast,
              id: 'user-organisation-updated',
            },
            toastData: {
              label: this.selectedUser.email,
              organisation: this.localOrganisation.label,
            },
          });
          this.closeModal();
        }
      });
    },

    onSelectOrganisation() {
      if (this.selectedUser && this.selectedUser.workspaces?.length > 0) {
        this.changeOrganisationWarning = true;
      }
    },

    onInputLabel(e) {
      this.newOrganisation.settings.label = e.target.value;
    },

    onInputName(e) {
      this.newOrganisation.name = e.target.value;
    },

    async onSave() {
      if (this.saveDisabled) {
        return;
      }
      if (this.addToExisting) {
        await this.onAddUserToOrganisation();
      } else {
        await this.setSelectedOrganisation(this.newOrganisation);
        await organisationRequest.createOrganisation();
        await this.addToast({
          toastComponent: {
            component: UserToast,
            id: 'user-organisation-updated',
          },
          toastData: {
            label: this.selectedUser.email,
            organisation: this.newOrganisation.label,
          },
        });
        await userRequest.fetchUser();
        this.closeModal();
      }
    },
  },
};
</script>
<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";
.add-to-organisation-modal {
  @include modal;
  position: relative;

  .field {
    margin-bottom: 0.6rem;
  }

  .row {
    display: grid;
    grid-template-columns: 40% 60%
  }

  .wrapper-select {
    height: 30px;
  }

  .v-select {
    background-color: white;
    //position: absolute;
    width: 400px;

    &.disabled {
      cursor: none;
      opacity: 0.5;
      pointer-events: none;
    }
  }

  &.disabled {
    cursor: none;
    opacity: 0.5;
    pointer-events: none;
  }

  .warning {
    color: red;
  }
}
</style>
