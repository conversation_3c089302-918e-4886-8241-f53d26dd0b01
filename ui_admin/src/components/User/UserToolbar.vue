<template>
  <section class="user-toolbar">
    <section class="user-actions">
      <base-button :size="'small'" @click="onClickCreate">
        <i class="fa-regular fa-plus icon" />
        Create User
      </base-button>
      <base-button :size="'small'" :colour="'dark'" :disabled="disabledCompareUsage" @click="onClickCompare">
        <i class="fa-regular fa-code-compare icon" />
        Compare Usage
      </base-button>
      <base-button :size="'small'" :colour="'danger'" :disabled="disabledDelete" @click="onClickDelete">
        <i class="fa-regular fa-trash icon" />
        Delete Users
      </base-button>
    </section>
    <section class="user-pagination">
      <pagination :current-page="page" :last="last" @onBtPrevious="onBtPrevious" @onBtNext="onBtNext" @onSetPage="onSetPage" />
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import CompareUsageModal from '@/components/User/UserUsageModal';
import Pagination from '@/components/Pagination/pagination';
import UserDeleteModal from '@/components/User/UserDeleteModal';
import UserDetailsModal from '@/components/User/UserDetailsModal';
import UserRequest from '@/services/UserRequest';

export default {
  name: 'user-toolbar',

  components: {
    BaseButton,
    Pagination,
  },

  computed: {
    ...mapState('user', [
      'page',
      'rows',
      'selectedUsers',
      'users',
    ]),

    disabledCompareUsage() {
      return this.selectedUsers.length < 2;
    },

    disabledDelete() {
      return this.selectedUsers.length < 1;
    },

    last() {
      return this.users.length < this.rows;
    },
  },

  methods: {
    ...mapActions('modal', ['setModal']),

    ...mapActions('user', ['setPage', 'setSelectedUser']),

    ...mapActions('toast', ['addToast']),

    async doCallApiFetchUsers() {
      await UserRequest.fetchUser();
    },

    onBtPrevious() {
      this.setPage({ page: this.page - 1 });
      this.doCallApiFetchUsers();
    },

    onBtNext() {
      this.setPage({ page: this.page + 1 });
      this.doCallApiFetchUsers();
    },

    onSetPage(page) {
      this.setPage({ page });
      this.doCallApiFetchUsers();
    },

    onClickCompare() {
      if (!this.disabledCompareUsage) {
        this.setModal(CompareUsageModal);
      }
    },

    onClickCreate() {
      this.setSelectedUser(null);
      this.setModal(UserDetailsModal);
    },

    onClickDelete() {
      if (!this.disabledDelete) {
        this.setModal(UserDeleteModal);
      }
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.user-toolbar {
  @include flex("block", "row", "start", "start");

  margin-top: 0.6rem;
  width: 100%;

  .user-actions {
    @include flex("block", "row", "start", "start");

    column-gap: 1rem;
    min-width: max-content;

    .icon {
      margin-right: 0.2em;
    }
  }

  .user-pagination {
    @include flex("block", "row", "end", "start");

    width: 100%;
  }
}

</style>
