<template>
  <section class="update-password-modal">
    <section class="header">
      <h2>Update Password</h2>
    </section>
    <section class="body">
      <section class="field row">
        <base-input placeholder="Leave this field empty to send user a Reset Password email" :value="localPassword" @input="onInputPassword" />
      </section>
    </section>
    <section class="footer">
      <base-button class="cancel" colour="light" size="small" type="link"  @click="onCancel">Cancel</base-button>
      <base-button class="submit" colour="base" size="small" type="link"  @click="onUpdatePassword">Update</base-button>
      <loading-blocks-overlay v-if="updating" />
    </section>
  </section>
</template>

<script>
import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import UserToast from '@/components/Toast/UserToast';
import userRequest from '@/services/UserRequest';
import { mapActions, mapState } from 'vuex';

export default {
  name: 'update-password-modal',
  components: { BaseButton, LoadingBlocksOverlay, BaseInput },

  data() {
    return {
      updating: false,
      localPassword: '',
    };
  },

  computed: {
    ...mapState('user', ['selectedUser']),

  },

  methods: {
    ...mapActions('modal', ['closeModal']),
    ...mapActions('toast', ['addToast']),

    onInputPassword(e) {
      this.localPassword = e.target.value;
    },

    onUpdatePassword() {
      this.updating = true;
      if (this.localPassword?.length > 2) {
        userRequest.updatePassword(this.selectedUser.id, this.localPassword.trim());
        this.closeModal();
        this.addToast({
          toastComponent: {
            component: UserToast,
            id: 'user-password-updated',
          },
          toastData: {
            label: this.selectedUser.name,
          },
        });
      } else {
        userRequest.resetPassword(this.selectedUser.email);
        this.closeModal();
        this.addToast({
          toastComponent: {
            component: UserToast,
            id: 'user-password-reset',
          },
          toastData: {
            label: this.selectedUser.name,
          },
        });
      }

      this.updating = false;
    },

    onCancel() {
      this.closeModal();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";
.update-password-modal {
  @include modal;
  position: relative;

  .field {
    margin-bottom: 0.6rem;
  }
}

</style>
