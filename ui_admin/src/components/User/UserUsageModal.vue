<template>
<section class="compare-usage-modal">
  <section class="header">{{label}}</section>
  <section class="body">
    <section class="login-data-modal">
      <base-button class="collapsible" :class="{active: displayLoginData  }" colour="base" type="base" @click="onClickDisplayLoginData" v-if="!isCompareUsage">Login Count {{loginCount}}</base-button>

      <section class="login-data-list" v-if="displayLoginData">
        <section
            v-for="(login, index) in loginData"
            :key="index"
            class="login-item"
        >
          <section><b>ID: </b> {{login.id}}</section>
          <section><b> User ID: </b>{{login.userId}}}</section>
          <section><b> Request Type: </b> {{login.requestType}}</section>
          <section><b> Request Timestamp:</b> {{login.requestTimestamp}}</section>
          <section><b> IP:</b> {{login.ipAddress}}</section>
          <section><b> Browser:</b> {{login.browser}}</section>
        </section>
      </section>
    </section>
    <section class="compare-usage-modal-chart">
      <VuePlotly :data="usageBarChartsData" :layout="usageBarChartsLayout" :display-mode-bar="false"></VuePlotly>

      <section class="usage-comparison-timescale-button-group">
        <base-button :size="'small'" @click="compareUsage('Daily')" class="daily-usage-comparison">
          <i class="fa-regular fa-plus icon"></i>
          Daily
        </base-button>
        <base-button :size="'small'" @click="compareUsage('Weekly')" class="weekly-usage-comparison">
          <i class="fa-regular fa-plus icon"></i>
          Weekly
        </base-button>
        <base-button :size="'small'" @click="compareUsage('Monthly')" class="monthly-usage-comparison">
          <i class="fa-regular fa-plus icon"></i>
          Monthly
        </base-button>
      </section>
      <section class="header">{{timescale}}</section>
      <section class="compare-usage-modal-line-chart">
      <VuePlotly :data="uploadCharLinePlotData" :layout="uploadCharLinePlotLayout" :display-mode-bar="false"></VuePlotly>
      <VuePlotly :data="loginDataLinePlotData" :layout="loginDataLinePlotLayout" :display-mode-bar="false"></VuePlotly>
      </section>
    </section>
  </section>
  <section class="footer">
    <base-button class="cancel" colour="light" type="link" @click="closeModal">Cancel</base-button>
  </section>
</section>
</template>

<script>
import BaseButton from '@/components/Base/BaseButton';
import datasetRequest from '@/services/DatasetRequest';
import userRequest from '@/services/UserRequest';
// eslint-disable-next-line import/no-extraneous-dependencies
import { VuePlotly } from 'vue3-plotly';
// eslint-disable-next-line import/no-extraneous-dependencies
import moment from 'moment';
import { mapActions, mapGetters, mapState } from 'vuex';

export default {
  name: 'compare-usage-modal',
  components: { BaseButton, VuePlotly },

  computed: {
    ...mapState('user', ['selectedUsers', 'selectedUser']),
    ...mapGetters('user', ['getSelectedUserIds']),

    isCompareUsage() {
      return this.getSelectedUserIds.length > 1;
    },

    label() {
      if (this.isCompareUsage) return 'Usage Comparison';
      return `${this.users[0].loginName} Usage`;
    },

    users() {
      return this.selectedUsers;
    },
  },

  data() {
    return {
      usageBarChartsLayout: null,
      usageBarChartsData: [],
      uploadCharLinePlotLayout: null,
      uploadCharLinePlotData: [],
      loginDataLinePlotLayout: null,
      loginDataLinePlotData: [],
      timescale: 'Daily,',
      loginCount: 0,
      loginData: [],
      displayLoginData: false,
    };
  },

  created() {
    this.compareUsage('Daily');
  },

  beforeUnmount() {
    this.reset();
  },

  methods: {
    ...mapActions('dataset', [
      'setFilterUserIds',
      'setRows',
      'setSort',
      'reset',
    ]),

    ...mapActions('modal', ['closeModal']),

    compareUsage(timescale) {
      this.constructUsageBarCharts(this.users);
      this.constructUploadLinePlots(this.users, timescale);
      this.constructLoginLinePlots(this.users, timescale);
      this.timescale = timescale;
    },

    constructUsageBarCharts(users) {
      const usernames = [];
      const consumedUsageValues = [];
      const remainingUsageValues = [];

      users.forEach((user) => {
        // Store username to display on x-axis
        usernames.push(user.loginName);

        const { lastUsageRecord } = user;
        const remainingUsage = lastUsageRecord.maxUsage - lastUsageRecord.currentUsage;

        // Store current usage and remaining usages to display on y-axis.
        // (stored separately as there will be one bar for consumed usage and one for remaining usage)
        consumedUsageValues.push(lastUsageRecord.currentUsage);
        remainingUsageValues.push(remainingUsage);
      });

      const consumedUsageBars = {
        x: usernames,
        y: consumedUsageValues,
        name: 'Consumed Usage',
        type: 'bar',
        textposition: 'auto',
        hoverinfo: 'none',
        marker: {
          color: 'rgba(49, 0, 110, 1)',
        },
      };

      // Overlay plot of remaining usages against usernames
      const remainingUsageBars = {
        x: usernames,
        y: remainingUsageValues,
        name: 'Remaining Usage',
        type: 'bar',
        textposition: 'auto',
        hoverinfo: 'none',
        marker: {
          color: 'rgba(101, 79, 140, 1)',
        },
      };

      // Combine the plots to pass to .newPlot() function
      this.usageBarChartsData = [consumedUsageBars, remainingUsageBars];

      // Set layout parameters
      this.usageBarChartsLayout = {
        title: 'Consumed and Remaining Usage',
        barmode: 'stack',
        width: 500,
        height: 500,
        showlegend: true,
        hovermode: 'closest',
      };
    },

    async constructUploadLinePlots(users, timescale) {
      const promises = [];
      users.forEach((user) => promises.push(this.getUploadLinePlotValues(user, timescale)));

      const uploadLinePlotValuesList = await Promise.all(promises);
      // Process the results in the order they were requested
      uploadLinePlotValuesList.forEach((uploadLinePlotValues, i) => {
        const u = users[i];
        const uploadCharLinePlot = {
          x: Object.keys(uploadLinePlotValues),
          y: Object.values(uploadLinePlotValues),
          name: u.firstName,
          type: 'scatter',
          line: {
            color: this.getColour(i),
            width: 1.0,
          },
        };
        this.uploadCharLinePlotData.push(uploadCharLinePlot);
      });

      this.uploadCharLinePlotLayout = {
        title: 'Uploaded Characters Over Time',
        width: 500,
        height: 600,
      };
    },

    async constructLoginLinePlots(users, timescale) {
      const promises = [];
      users.forEach((user) => promises.push(this.getLoginDataLinePlotValues(user.id, timescale)));

      const loginDataLinePlotValueList = await Promise.all(promises);
      loginDataLinePlotValueList.forEach((loginDataLinePlotValues, i) => {
        const u = users[i];
        const loginDataLinePlot = {
          x: Object.keys(loginDataLinePlotValues),
          y: Object.values(loginDataLinePlotValues),
          name: u.firstName,
          type: 'scatter',
          line: {
            color: this.getColour(i),
            width: 1.0,
          },
        };
        this.loginDataLinePlotData.push(loginDataLinePlot);
      });

      this.loginDataLinePlotLayout = {
        title: 'Logins Over Time',
        width: 500,
        height: 600,
      };
    },

    async getUploadLinePlotValues(user, timescale) {
      this.setFilterUserIds({ filterUserIds: user.id });
      this.setRows({ rows: user.lastUsageRecord });
      this.setSort({ sorting: 'bu.upload_start' });

      const uploads = await datasetRequest.getDatasets();

      const formattedUploads = uploads
        .filter(Boolean) // Filter out non-successful uploads
        .map((upload) => ({
          date: this.getDateForTimescale(upload.uploadStart, timescale), // Extract formatted date
          usage: upload.contentCharacters,
        }));

      return formattedUploads.reduce((acc, curr) => {
        acc[curr.date] = (acc[curr.date] || 0) + curr.usage; // Combine usage for each date
        return acc;
      }, {});
    },

    async getLoginDataLinePlotValues(id, timescale) {
      const logins = await userRequest.getLoginData(id);

      const loginDates = logins
        .filter(Boolean) // Filter out non-successful uploads
        .filter((login) => login.requestType === 'login')
        .map((login) => {
          switch (timescale) {
            case 'Daily':
              return login.requestTimestamp.split('T')[0];
            case 'Weekly':
              return moment(login.requestTimestamp).startOf('week').format('MMM D Y');
            case 'Monthly':
              return moment(login.requestTimestamp).format('MMM Y');
            default:
              return login.requestTimestamp.split('T')[0];
          }
        });

      if (!this.isCompareUsage) {
        this.loginCount = loginDates.length;
        this.loginData = logins;
      }

      return loginDates.reduce((acc, curr) => {
        acc[curr] = (acc[curr] || 0) + 1;
        return acc;
      }, {});
    },

    onClickDisplayLoginData() {
      this.displayLoginData = !this.displayLoginData;
    },

    getColour(index) {
      const colours = [
        'rgb(57, 106, 177)',
        'rgb(218, 124, 48)',
        'rgb(62, 150, 81)',
        'rgb(204, 37, 41)',
        'rgb(83, 81, 84)',
        'rgb(107, 76, 154)',
        'rgb(146, 36, 40)',
        'rgb(148, 139, 61)',
      ];
      return colours[index];
    },

    getDateForTimescale(uploadStart, timescale) {
      switch (timescale) {
        case 'Daily':
          return moment(uploadStart).format('YYYY-MM-DD');
        case 'Weekly':
          return moment(uploadStart).startOf('week').format('MMM D Y');
        case 'Monthly':
          return moment(uploadStart).format('MMM Y');
        default:
          return moment(uploadStart).format('YYYY-MM-DD');
      }
    },

  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.compare-usage-modal {
  @include modal;

  width: 1100px;
  height: 800px;
  position: relative;

  .body {
    max-height: 800px;

    .usage-comparison-timescale-button-group {
      @include flex("inline", "row", "start", "stretch");

      column-gap: 1rem;
      margin-bottom: 1rem;
      margin-top: 1rem;
    }

    .compare-usage-modal-chart {
      @include flex("inline", "column", "center", "stretch");

      .compare-usage-modal-line-chart {
        @include flex("inline", "row", "start", "stretch");

      }
    }

    .login-data-modal {

      .collapsible {
        background-color: #eee;
        color: #444;
        cursor: pointer;
        padding: 18px;
        width: 100%;
        border: none;
        text-align: left;
        outline: none;
        font-size: 15px;
      }

      .collapsible:after {
        content: '\002B';
        color: white;
        font-weight: bold;
        float: right;
        margin-left: 5px;
      }

      .active, .collapsible:hover {
        background-color: #ccc;
      }

      .collapsible.active:after {
          content: "\2212";
      }

      .login-data-list {
        @include flex("inline", "column", "center", "stretch");

          border-radius: 0.25rem;
          margin-bottom: 0;
          padding-left: 0;

        .login-data-list:first-child {
          border-top-left-radius: inherit;
          border-top-right-radius: inherit;
        }

        .login-item {
          background-color: #fff;
          border: 1px solid rgba(0,0,0,.125);
          display: block;
          font-size: small;
          padding: 0.75rem 1.25rem;
          position: relative;
        }
      }

    }
  }

}
</style>
