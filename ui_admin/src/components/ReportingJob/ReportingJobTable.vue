<template>
  <section class="reporting-job-table">
    <loading-blocks-overlay v-if="loading" />
    <ag-grid-vue v-else
      style="width: 100%; height: 100%"
      class="ag-theme-alpine"
      :columnDefs="columnDefs"
      :rowData="reportingJobMap"
      rowSelection='multiple'
      :rowClassRules="rowClassRules"
    />
  </section>
</template>

<script>
import { AgGridVue } from 'ag-grid-vue3';
import { mapState } from 'vuex';

import DateTimeRenderer from '@/components/BaseAgGrid/DateTimeRenderer';
import GridListCell from '@/components/Base/GridListCell';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import Pagination from '@/components/Pagination/pagination';
import ReportingJobActionCellRender from '@/components/ReportingJob/ReportingJobActionCellRender';
import ReportingJobRequest from '@/services/ReportingJobRequest';

export default {
  name: 'reporting-job-table',

  components: {
    LoadingBlocksOverlay,
    Pagination,
    AgGridVue,
    GridListCell,
  },

  data() {
    return {
      loading: true,
      gridApi: null,
      rowClassRules: null,
    };
  },

  computed: {
    ...mapState('reportingJob', ['reportingJobs']),

    selectedDatasets() {
      return this.gridApi.getSelectedRows();
    },

    columnDefs() {
      return [
        {
          id: 'Id',
          field: 'id',
          width: 70,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Name',
          field: 'name',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          filter: 'agTextColumnFilter',
        },
        {
          headerName: 'Type',
          field: 'scheduleType',
          width: 110,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Schedule Time (UTC)',
          field: 'scheduleTime',
          flex: 1,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Schedule Day',
          field: 'scheduleDay',
          flex: 1,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Execution Number',
          field: 'executionNumber',
          flex: 1,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Created At',
          field: 'createdAt',
          wrapText: true,
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: DateTimeRenderer };
          },
        },
        {
          headerName: 'Actions',
          field: '',
          width: 150,
          wrapText: true,
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: ReportingJobActionCellRender };
          },
        },
      ];
    },

    reportingJobMap() {
      return this.reportingJobs;
    },
  },

  async created() {
    await ReportingJobRequest.fetchReportingJobs();
    this.loading = false;
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-table {
  @include flex("block", "column", "start", "start");

  height: 100%;
  margin-top: 0.6rem;
  width: 100%;
}
</style>
