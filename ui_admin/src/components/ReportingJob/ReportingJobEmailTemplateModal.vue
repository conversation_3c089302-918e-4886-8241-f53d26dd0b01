<template>
  <section class="reporting-job-email-templates-modal">
    <section class="header">
      <h2>Cached Email Templates</h2>
    </section>

    <section class="body">
      <loading-blocks-overlay v-if="loading" />

      <div v-if="!loading && templates.length > 0" class="templates-table">
        <table>
          <thead>
          <tr>
            <th>Template ID</th>
            <th>Actions</th>
          </tr>
          </thead>
          <tbody>
          <tr v-for="template in templates" :key="template">
            <td>{{ template }}</td>
            <td>
              <base-button
                  colour="base"
                  size="small"
                  :disabled="reloadingTemplates[template]"
                  @click="reloadTemplate(template)"
              >
                <i v-if="reloadingTemplates[template]" class="fa-solid fa-spinner fa-spin icon" />
                <i v-else class="fa-solid fa-refresh icon" />
                <span>{{ reloadingTemplates[template] ? 'Reloading...' : 'Reload' }}</span>
              </base-button>
            </td>
          </tr>
          </tbody>
        </table>
      </div>

      <div v-if="!loading && !error && templates.length === 0" class="empty-message">
        <p>There are no cached templates available.</p>
      </div>

      <div v-if="!loading && error" class="error-message">
        <p>{{ error }}</p>
      </div>
    </section>

    <section class="footer">
      <base-button class="cancel" colour="light" type="link" @click="closeModal">
        Close
      </base-button>
    </section>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import ReportingJobRequest from '@/services/ReportingJobRequest';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import ReportingJobToast from '@/components/ReportingJob/ReportingJobToast';

export default {
  name: 'reporting-job-email-templates-modal',

  components: {
    LoadingBlocksOverlay,
    BaseButton,
  },

  props: {
    componentProps: {
      type: Object,
      default: () => ({}),
    },
  },

  data() {
    return {
      templates: [],
      loading: true,
      reloadingTemplates: {},
      error: null,
    };
  },

  async created() {
    await this.loadTemplates();
  },

  methods: {
    ...mapActions('modal', ['closeModal']),
    ...mapActions('toast', ['addToast']),

    async loadTemplates() {
      try {
        this.loading = true;
        this.error = null;

        const response = await ReportingJobRequest.getEmailTemplates();
        this.templates = response || [];
      } catch (error) {
        this.error = 'Failed to load email templates. Please try again.';
        this.templates = [];
      } finally {
        this.loading = false;
      }
    },

    async reloadTemplate(templateId) {
      try {
        // Vue 2 compatible way to set reactive property
        this.reloadingTemplates = {
          ...this.reloadingTemplates,
          [templateId]: true,
        };

        await ReportingJobRequest.reloadEmailTemplate(templateId);

        await this.addToast({
          toastComponent: {
            component: ReportingJobToast,
            id: 'reporting-job-email-template-reloaded',
          },
          toastData: {
            label: templateId,
          },
        });
      } catch (error) {
        await this.addToast({
          toastComponent: {
            component: ReportingJobToast,
            id: 'reporting-job-email-template-reloaded-failed',
          },
          toastData: {
            label: templateId,
          },
        });
      } finally {
        await this.loadTemplates();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-email-templates-modal {
  @include modal;
  max-width: 600px;
  max-height: 90vh;

  .body {
    padding: 1.5rem;
    overflow-y: auto;
    max-height: 70vh;
    position: relative;

    .templates-table {
      table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 1rem;

        th, td {
          padding: 0.75rem;
          text-align: left;
          border-bottom: 1px solid #e0e0e0;
        }

        th {
          background-color: #f5f5f5;
          font-weight: $font-weight-bold;
          font-size: $font-size-sm;
          color: #333;
        }

        td {
          font-size: $font-size-sm;
          vertical-align: middle;
        }

        tbody tr:hover {
          background-color: #f9f9f9;
        }

        .icon {
          margin-right: 0.4rem;
        }
      }
    }

    .empty-message {
      text-align: center;
      padding: 3rem 2rem;
      color: #666;
      font-size: $font-size-md;
    }

    .error-message {
      text-align: center;
      padding: 2rem;
      color: #d32f2f;
      font-size: $font-size-md;
    }
  }

  .footer {
    font-size: $font-size-md;
    padding: $search-modal-padding;
    display: flex;
    justify-content: flex-end;
    align-items: center;

    .cancel {
      font-weight: $font-weight-normal;
      padding-left: 0;
    }
  }

  .header {
    @include flex("block", "row", "between", "center");
    padding: $search-modal-padding;
    border-bottom: 1px solid #e0e0e0;

    h2 {
      @include flex("block", "row", "start", "center");
      font-weight: $font-weight-bold;
      margin: 0;
    }
  }
}
</style>
