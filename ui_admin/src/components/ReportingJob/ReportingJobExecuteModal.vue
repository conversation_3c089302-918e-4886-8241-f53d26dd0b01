<template>
  <section class="reporting-job-execute-modal">
    <section class="header">
      <h2>Execute Reporting Job</h2>
    </section>

    <section class="body">
      <section class="text">
        <span>You are about to execute the reporting job</span>
        <span class="label">&lsquo;{{ reportingJob.name }}&rsquo;</span>.
        Are you sure you want to proceed?
      </section>
    </section>

    <section class="footer">
      <base-button class="cancel" colour="light" type="link" @click="closeModal">Cancel</base-button>
      <loading-blocks-overlay v-if="loading" />
      <base-button v-else class="confirm" colour="dark" @click="onClickConfirm">Execute</base-button>
    </section>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import ReportingJobRequest from '@/services/ReportingJobRequest';
import ReportingJobToast from '@/components/ReportingJob/ReportingJobToast';

export default {
  name: 'reporting-job-execute-modal',

  components: {
    BaseButton,
    LoadingBlocksOverlay,
  },

  props: {
    componentProps: {
      reportingJob: Object,
    },
  },

  data() {
    return {
      loading: false,
    };
  },

  computed: {
    reportingJob() {
      return this.componentProps.reportingJob;
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('toast', ['addToast']),

    async onClickConfirm() {
      this.loading = true;
      await ReportingJobRequest.executeReportingJob(this.reportingJob.id);
      await this.closeModal();
      await this.addToast({
        toastComponent: {
          component: ReportingJobToast,
          id: 'reporting-job-executed',
        },
        toastData: {
          label: this.reportingJob.name,
        },
      });
      this.loading = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-execute-modal {
  @include modal;

  .body {
    padding: 1.5rem;

    .text {
      font-size: $font-size-xs;
      margin: 0.5rem 0;

      .label {
        font-weight: $font-weight-bold;
      }
    }
  }

  .footer {
    font-size: $font-size-md;
    padding: $search-modal-padding;

    .cancel {
      font-weight: $font-weight-normal;
      padding-left: 0;
    }

    .confirm {
      font-weight: $font-weight-medium;
    }
  }

  .header {
    @include flex("block", "row", "between", "center");

    padding: $search-modal-padding;

    h2 {
      @include flex("block", "row", "start", "center");

      font-weight: $font-weight-bold;
    }

    .icon {
      @include flex("block", "row", "end", "center");

      cursor: pointer;
      height: 1.25rem;
      opacity: 0.3;
      &:hover {
        opacity: 1;
      }

      &:active,
      &:focus {
        border: none;
        outline: none;
      }
    }
  }
}
</style>
