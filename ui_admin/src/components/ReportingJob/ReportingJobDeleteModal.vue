<template>
  <section class="reporting-job-delete-modal">
    <section class="header">
      <h2>Delete Reporting Job</h2>
    </section>

    <section class="body">
      <section class="text">
        <span> This reporting job will be delete </span>
        <span class="label">&lsquo;{{ reportingJob.name }}&rsquo;</span>
        . Are you sure you wish to continue?
      </section>
    </section>

    <section class="footer">
      <base-button class="cancel" colour="light" type="link" @click="closeModal">Cancel</base-button>
      <base-button class="delete" colour="danger" @click="onClickDelete">Delete</base-button>
    </section>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import ReportingJobRequest from '@/services/ReportingJobRequest';
import ReportingJobToast from '@/components/ReportingJob/ReportingJobToast';

export default {
  name: 'reporting-job-delete-modal',

  components: {
    BaseButton,
  },

  props: {
    componentProps: {
      reportingJob: Object,
    },
  },

  data() {
    return {
      deleting: false,
    };
  },

  computed: {
    reportingJob() {
      return this.componentProps.reportingJob;
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('toast', ['addToast']),

    async onClickDelete() {
      this.deleting = true;
      await ReportingJobRequest.deleteReportingJob(this.reportingJob.id);
      await ReportingJobRequest.fetchReportingJobs();
      await this.closeModal();
      await this.addToast({
        toastComponent: {
          component: ReportingJobToast,
          id: 'reporting-job-deleted',
        },
        toastData: {
          label: this.reportingJob.name,
        },
      });
      this.deleting = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-delete-modal {
  @include modal;

  .body {
    padding: 1.5rem;

    .text {
      font-size: $font-size-xs;
      margin: 0.5rem 0;

      .label {
        font-weight: $font-weight-bold;
      }
    }
  }

  .footer {
    font-size: $font-size-md;
    padding: $search-modal-padding;

    .cancel {
      font-weight: $font-weight-normal;
      padding-left: 0;
    }

    .delete {
      font-weight: $font-weight-medium;
    }
  }

  .header {
    @include flex("block", "row", "between", "center");

    padding: $search-modal-padding;

    h2 {
      @include flex("block", "row", "start", "center");

      font-weight: $font-weight-bold;
    }

    .icon {
      @include flex("block", "row", "end", "center");

      cursor: pointer;
      height: 1.25rem;
      opacity: 0.3;
      &:hover {
        opacity: 1;
      }

      &:active,
      &:focus {
        border: none;
        outline: none;
      }
    }
  }
}
</style>
