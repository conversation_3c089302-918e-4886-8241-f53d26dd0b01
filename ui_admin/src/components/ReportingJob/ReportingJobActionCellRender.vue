<template>
  <section class="reporting-job-action-cell-render">
    <section class="item">
      <base-button :size="'small'" :colour="'dark'" @click="onClickEdit">
        <i class="fa-solid fa-pen-to-square icon" />
        Edit
      </base-button>
    </section>
    <section class="item">
      <base-button :size="'small'" :colour="'dark'" @click="onClickExecute">
        <i class="fa-solid fa-play icon" />
        Execute
      </base-button>
    </section>
    <section class="item">
      <base-button :size="'small'" :colour="'dark'" @click="onClickResults">
        <i class="fa-solid fa-square-poll-vertical icon" />
        Results
      </base-button>
    </section>
    <section class="item">
      <base-button :size="'small'" :colour="'dark'" @click="onClickExecutionStatuses">
        <i class="fa-solid fa-signal icon" />
        Statuses
      </base-button>
    </section>
    <section class="item">
      <base-button :size="'small'" :colour="'danger'" @click="onClickDelete">
        <i class="fa-regular fa-trash icon" />
        Delete
      </base-button>
    </section>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import ReportingJobDeleteModal from '@/components/ReportingJob/ReportingJobDeleteModal';
import ReportingJobExecuteModal from '@/components/ReportingJob/ReportingJobExecuteModal';
import Route from '@/enum/route';

export default {
  name: 'reporting-job-action-cell-render',

  components: {
    BaseButton,
  },

  computed: {
    reportingJob() {
      return this.params.data;
    },
  },

  methods: {
    ...mapActions('modal', ['setModalAndProps']),

    ...mapActions('reportingJob', ['setSelectedId']),

    ...mapActions('toast', ['addToast']),

    onClickDelete() {
      this.setModalAndProps({
        component: ReportingJobDeleteModal,
        props: {
          reportingJob: this.reportingJob,
        },
      });
    },

    onClickEdit() {
      this.setSelectedId(this.reportingJob.id);
      this.$router.push({
        name: Route.REPORTING_JOB_EDIT,
        query: {
          id: this.reportingJob.id,
        },
      });
    },

    onClickExecute() {
      this.setModalAndProps({
        component: ReportingJobExecuteModal,
        props: {
          reportingJob: this.reportingJob,
        },
      });
    },

    onClickExecutionStatuses() {
      this.setSelectedId(this.reportingJob.id);
      this.$router.push({
        name: Route.REPORTING_JOB_EXECUTION_STATUS,
        query: {
          id: this.reportingJob.id,
        },
      });
    },

    onClickResults() {
      this.setSelectedId(this.reportingJob.id);
      this.$router.push({
        name: Route.REPORTING_JOB_RESULT,
        query: {
          id: this.reportingJob.id,
        },
      });
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-action-cell-render {
  @include flex("block", "column", "start", "start");

  .item {
    @include flex("block", "row", "center", "center");

    border-radius: 4px;
    color: clr('white');
    height: 2rem;
    margin: 0.2rem;

    .base-button {
      height: 2rem;
    }

    .icon {
      margin-right: 0.2em;
    }
  }
}
</style>
