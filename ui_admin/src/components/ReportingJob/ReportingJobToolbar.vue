<template>
  <section class="reporting-job-toolbar">
    <section class="reporting-job-actions">
      <base-button colour="base" size="small" @click="onCreate">
        <i class="fa-solid fa-plus icon" />
        <span>Create</span>
      </base-button>
      <base-button colour="qualtrics" size="small" @click="onClickTemplates">
        <i class="fa-solid fa-dashboard icon" />
        <span>Templates</span>
      </base-button>
    </section>
    <section class="reporting-job-pagination">
      <pagination
        :current-page="page"
        :last="isLastPage"
        @onBtPrevious="onBtPrevious"
        @onBtNext="onBtNext"
        @onSetPage="onSetPage"
      />
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import Pagination from '@/components/Pagination/pagination';
import ReportingJobEmailTemplateModal from '@/components/ReportingJob/ReportingJobEmailTemplateModal';
import ReportingJobRequest from '@/services/ReportingJobRequest';
import Route from '@/enum/route';

export default {
  name: 'reporting-job-toolbar',

  components: {
    BaseButton,
    Pagination,
  },

  computed: {
    ...mapState('reportingJob', [
      'reportingJobs',
      'page',
      'rows',
    ]),

    isLastPage() {
      return this.reportingJobs?.length < this.rows;
    },
  },

  methods: {
    ...mapActions('reportingJob', ['setPage', 'setSelectedId']),
    ...mapActions('modal', ['setModalAndProps']),

    onBtPrevious() {
      this.setPage(this.page - 1);
      ReportingJobRequest.fetchReportingJobs();
    },

    onBtNext() {
      this.setPage(this.page + 1);
      ReportingJobRequest.fetchReportingJobs();
    },

    onCreate() {
      this.setSelectedId(null);
      this.$router.push({ name: Route.REPORTING_JOB_NEW });
    },

    onClickTemplates() {
      this.setModalAndProps({
        component: ReportingJobEmailTemplateModal,
        props: {},
      });
    },

    onSetPage(page) {
      this.setPage(page);
      ReportingJobRequest.fetchReportingJobs();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-toolbar {
  @include flex("block", "row", "start", "start");

  margin-top: 0.6rem;
  width: 100%;

  .reporting-job-actions {
    @include flex("block", "row", "start", "start");

    .icon {
      margin-right: 0.4rem;
    }
  }

  .reporting-job-pagination {
    @include flex("block", "row", "end", "start");

    width: 100%;
  }
}
</style>
