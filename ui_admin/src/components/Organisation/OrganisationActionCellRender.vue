<template>
  <section class="organisation-action-cell-render">
    <section class="item">
      <base-button :size="'small'" :colour="'dark'" @click="onClickUpdate">
        <i class="fa-regular fa-save icon"></i> Edit
      </base-button>
    </section>
    <section class="item">
      <base-button :size="'small'" :colour="'base'" @click="onClickWorkspaces">
        <i class="fa-regular fa-sitemap icon"></i> Workspaces
      </base-button>
    </section>
    <section class="item">
      <base-button :size="'small'" :colour="'success'" @click="onClickUpload">
        <i class="fa-regular fa-user-plus icon"></i> Upload logo
      </base-button>
    </section>
    <section class="item">
      <base-button :size="'small'" :colour="'danger'" @click="onClickDelete" v-if="deletable">
        <i class="fa-regular fa-trash icon"></i> Delete
      </base-button>
    </section>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import OrganisationDetailModal from '@/components/Organisation/OrganisationDetailModal';
import OrganisationDeleteModal from '@/components/Organisation/OrganisationDeleteModal';
import AddWorkspaceModal from '@/components/Organisation/AddWorkspaceModal';
import UploadOrganisationLogoModal from '@/components/Organisation/UploadOrganisationLogoModal';

export default {
  name: 'organisation-action-cell-render',

  components: {
    BaseButton,
  },

  computed: {
    organisation() {
      return this.params.data;
    },

    deletable() {
      return this.organisation.workspaces.length === 0
          && this.organisation.administratorIds.length === 0
          && this.organisation.memberIds.length === 0;
    },
  },

  methods: {
    ...mapActions('modal', ['setModal']),
    ...mapActions('organisation', ['setSelectedOrganisation']),

    onClickUpdate() {
      this.setSelectedOrganisation(this.organisation);
      this.setModal(OrganisationDetailModal);
    },

    onClickDelete() {
      this.setSelectedOrganisation(this.organisation);
      this.setModal(OrganisationDeleteModal);
    },

    onClickWorkspaces() {
      this.setSelectedOrganisation(this.organisation);
      this.setModal(AddWorkspaceModal);
    },

    onClickUpload() {
      this.setSelectedOrganisation(this.organisation);
      this.setModal(UploadOrganisationLogoModal);
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";
.organisation-action-cell-render {
  @include flex("block", "column", "start", "start");

  .item {
    @include flex("block", "row", "center", "center");

    border-radius: 4px;
    color: clr('white');
    height: 2rem;
    margin: 0.2rem;

    .base-button {
      height: 2rem;

      .icon {
        margin-right: 0.2rem;
      }
    }
  }
}
</style>
