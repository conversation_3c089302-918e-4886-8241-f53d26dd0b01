<template>
  <section class="organisation-new-modal">
    <section class="header">
      <h2>Create New Organisation</h2>
    </section>
    <section class="body">
      <section class="field">
        <span>Name</span>
        <base-input placeholder="Enter a organisation name" @input="onInputName" />
      </section>
      <section class="field">
        <span>Label</span>
        <base-input placeholder="Enter a organisation label" @input="onInputLabel"/>
      </section>
      <section class="field owner">
        <span>Owner</span>
        <section class="wrapper-select">
          <v-select v-model="localOwner"
                    class="v-select"
                    :options="userOptions"
                    :searchable="true"
                    :selectable="isUserOptionSelectable"
                    @search="fetchUsers"/>
        </section>
      </section>
      <section class="field administrators">
        <span>Administrators</span>
        <section class="wrapper-select">
          <v-select v-model="localAdministrators"
                    class="v-select"
                    multiple
                    :options="userOptions"
                    :searchable="true"
                    :selectable="isUserOptionSelectable"
                    @search="fetchUsers"/>
        </section>
      </section>
      <section class="field members">
        <span>Members</span>
        <section class="wrapper-select">
          <v-select v-model="localMembers"
                    class="v-select"
                    multiple
                    :options="userOptions"
                    :searchable="true"
                    :selectable="isUserOptionSelectable"
                    @search="fetchUsers"/>
        </section>
      </section>
    </section>
    <section class="footer">
      <base-button class="cancel" colour="light" size="small" type="link"  @click="onCancel">Cancel</base-button>
      <loading-blocks-overlay v-if="creating" />
      <base-button v-else class="confirm" colour="base" size="small" :disabled="createDisabled" @click="onCreate">
        <span>Create Organisation</span>
      </base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { debounce } from 'lodash-es';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import OrganisationToast from '@/components/Toast/OrganisationToast';
import VSelect from 'vue-select';
import organisationRequest from '@/services/OrganisationRequest';
import userRequest from '@/services/UserRequest';

export default {
  name: 'organisation-new-modal',

  components: {
    BaseButton,
    BaseInput,
    LoadingBlocksOverlay,
    VSelect,
  },

  data() {
    return {
      creating: false,
      localAdministrators: [],
      localLabel: '',
      localMembers: [],
      localName: '',
      localOwner: null,
      userOptions: [],
    };
  },

  computed: {
    ...mapState('user', ['selectedUser']),

    createDisabled() {
      if (this.localName.trim() === '') return true;
      if (this.localLabel.trim() === '') return true;
      return this.localOwner === null;
    },
  },

  created() {
    this.reset();
    this.doCallApiFetchUsers();
  },

  beforeUnmount() {
    this.reset();
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('toast', ['addToast']),

    ...mapActions('organisation', ['setSelectedOrganisation']),

    ...mapActions('user', ['setSearch', 'reset']),

    async doCallApiFetchUsers() {
      this.userOptions = (await userRequest.getUsers()).map(u => ({
        id: u.id,
        label: `${u.loginName} (Id: ${u.id} )`,
        selectable: !u.organisationId,
      }));
    },

    fetchUsers: debounce(async function fetchUsers(search, loading) {
      this.setSearch(search);
      await this.doCallApiFetchUsers();
      loading(false);
    }, 500),

    onCancel() {
      this.closeModal();
    },

    async onCreate() {
      if (this.createDisabled) return;

      this.creating = true;

      const newOrg = {
        name: this.localName,
        ownerId: this.localOwner.id,
        administratorIds: this.localAdministrators.map(admin => admin.id),
        memberIds: this.localMembers.map(member => member.id),
        settings: {
          label: this.localLabel,
        },
      };

      await this.setSelectedOrganisation(newOrg);

      await organisationRequest.updateOrganisation();
      await organisationRequest.getOrganisations();
      this.creating = false;
      await this.addToast({
        toastComponent: {
          component: OrganisationToast,
          id: 'organisation-created',
        },
        toastData: {
          label: this.label,
        },
      });
      await this.closeModal();
    },

    onInputLabel(e) {
      this.localLabel = e.target.value;
    },

    onInputName(e) {
      this.localName = e.target.value;
    },

    isUserOptionSelectable(user) {
      return user.selectable
          && !this.localAdministrators.find(t => t.id === user.id)
          && !this.localMembers.find(t => t.id === user.id)
          && !this.localOwner.id !== user.id;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.organisation-new-modal {
  @include modal;

  position: relative;

  .field {
    margin-bottom: 0.6rem;
  }

  .wrapper-select {
    height: 30px;
  }

  .v-select {
    background-color: clr('white');
    position: absolute;
    width: 500px;
  }
}
</style>
