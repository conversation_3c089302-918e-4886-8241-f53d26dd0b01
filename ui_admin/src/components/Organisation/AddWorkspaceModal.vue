<template>
  <section class="add-workspace-modal">
    <section class="header">
      Add to organisation
    </section>
    <section class="body">
      <section class="row field">
        <span>Select workspace</span>
        <section class="wrapper-select">
          <v-select v-model="localWorkspaces"
                    class="v-select read-only"
                    multiple
                    :options="workspaceOptions"
                    :searchable="true"
                    :selectable="isOptionSelectable"
                    @search="fetchWorkspaces"
          />
        </section>
      </section>
    </section>
    <section class="footer">
      <base-button class="cancel" colour="light" type="link" @click="closeModal">Cancel</base-button>
      <loading-blocks-overlay v-if="saving" />
      <base-button class="save" colour="base" @click="onSave">Save</base-button>
    </section>
  </section>
</template>
<script>
import BaseButton from '@/components/Base/BaseButton';
import BaseRadio from '@/components/Base/BaseRadio';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import OrganisationToast from '@/components/Toast/OrganisationToast';
import VSelect from 'vue-select';
import organisationRequest from '@/services/OrganisationRequest';
import workspaceRequest from '@/services/WorkspaceRequest';
import { mapActions, mapState } from 'vuex';
import { debounce } from 'lodash-es';

export default {
  name: 'add-workspace-modal',
  components: { LoadingBlocksOverlay, BaseRadio, BaseButton, VSelect },

  data() {
    return {
      workspaceOptions: [],
      localWorkspaces: [],
      saving: false,
    };
  },

  beforeMount() {
    this.doCallApiFetchWorkspaces();
    this.fetchLocalWorkspaces();
  },

  computed: {
    ...mapState('organisation', ['selectedOrganisation']),

  },

  methods: {

    ...mapActions('modal', ['setModal', 'closeModal']),

    ...mapActions('toast', ['addToast']),

    ...mapActions('workspace', { setFilterWorkspace: 'setSearch', resetFilterWorkspace: 'reset' }),

    fetchLocalWorkspaces() {
      this.localWorkspaces = this.selectedOrganisation.workspaces;
    },

    async doCallApiFetchWorkspaces() {
      this.workspaceOptions = (await workspaceRequest.getWorkspaces()).map(w => ({
        id: w.id,
        label: `${w.label} (Id: ${w.id} )`,
        organisationId: w.organisationId,
        role: w.role,
        selectable: !w.organisationId,
      }));
    },

    fetchWorkspaces: debounce(async function fetchWorkspaces(search, loading) {
      this.setFilterWorkspace(search);
      await this.doCallApiFetchWorkspaces();
      loading(false);
    }, 500),

    async onSave() {
      this.saving = true;
      await organisationRequest.addWorkspacesToOrganisation(this.localWorkspaces.map(w => w.id)).then(async (response) => {
        if (response.status) {
          await this.addToast({
            toastComponent: {
              component: OrganisationToast,
              id: 'organisation-error',
            },
            toastData: {
              label: response.data,
            },
          });
        } else {
          await this.addToast({
            toastComponent: {
              component: OrganisationToast,
              id: 'organisation-updated',
            },
            toastData: {
              label: this.label,
            },
          });
          this.saving = false;
          await organisationRequest.fetchOrganisations();
          this.closeModal();
        }
      });
    },

    isOptionSelectable(workspace) {
      return workspace.selectable;
    },
  },
};
</script>
<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";
.add-workspace-modal {

  @include modal;

  position: relative;

  .field {
    margin-bottom: 0.6rem;
  }

  .wrapper-select {
    min-height: 30px;
  }

  .v-select {
    background-color: clr('white');
    width: 500px;
  }

  .footer {
    .loading-blocks-overlay {
      height: 2rem;
    }
  }

}
</style>
