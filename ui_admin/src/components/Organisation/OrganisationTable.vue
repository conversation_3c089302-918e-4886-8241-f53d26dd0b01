<template>
  <section class="organisation-table">
    <section class="organisation-toolbar">
      <section class="organisation-actions">
        <base-button :size="'small'" @click="onClickCreate">
          <i class="fa-regular fa-plus icon"></i>
          Create Organisation
        </base-button>
      </section>
      <section class="organisation-pagination">
        <pagination :current-page="page" :last="last" @onBtPrevious="onBtPrevious" @onBtNext="onBtNext" @onSetPage="onSetPage"></pagination>
      </section>
    </section>
    <section class="organisation-table-gird">
      <ag-grid-vue
          style="width: 100%; height: 100%"
          class="ag-theme-alpine"
          :columnDefs="columnDefs"
          :rowData="organisationsMap"
          @filter-changed="onFilterChanged"
      />
    </section>
  </section>
</template>

<script>
import { AgGridVue } from 'ag-grid-vue3';
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import GridListCell from '@/components/Base/GridListCell';
import OrganisationActionCellRender from '@/components/Organisation/OrganisationActionCellRender';
import OrganisationNewModal from '@/components/Organisation/OrganisationNewModal';
import organisationRequest from '@/services/OrganisationRequest';
import Pagination from '@/components/Pagination/pagination';

export default {
  name: 'organisation-table',

  components: {
    BaseButton,
    Pagination,
    AgGridVue,
  },

  data() {
    return {
      gridApi: null,
    };
  },

  computed: {
    ...mapState('organisation', [
      'rows',
      'page',
      'organisations',
    ]),

    columnDefs() {
      return [
        {
          id: 'Id',
          field: 'id',
          width: 100,
          wrapText: true,
          autoHeight: true,
          headerCheckboxSelection: true,
          checkboxSelection: true,
        },
        {
          headerName: 'Name',
          field: 'name',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: ['contains'],
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
        },
        {
          headerName: 'Owner',
          field: 'ownerName',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: ['contains'],
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
        },
        {
          headerName: 'Administrators',
          field: 'administratorNames',
          flex: 1,
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: GridListCell };
          },
        },
        {
          headerName: 'Members',
          field: 'memberNames',
          flex: 1,
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: GridListCell };
          },
        },
        {
          headerName: 'Workspaces',
          field: 'workspacesLabel',
          flex: 1,
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: GridListCell };
          },
        },
        {
          headerName: 'Label',
          field: 'label',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: ['contains'],
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
        },
        {
          headerName: 'Actions',
          field: '',
          width: 200,
          wrapText: true,
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: OrganisationActionCellRender };
          },
        },
      ];
    },

    last() {
      return this.organisations.length < this.rows;
    },

    organisationsMap() {
      return this.organisations.map(o => ({
        ...o,
        ownerName: o.owner.loginName,
        administratorNames: o.administrators.map(user => user.loginName),
        memberNames: o.members.map(user => user.loginName),
        workspacesLabel: o.workspaces.map(w => w.label),
        workspaces: o.workspaces.map(w => {
          return {
            ...w,
            label: `${w.label} (Id: ${w.id} )`,
          };
        }),
        label: o.settings.label,
      }));
    },
  },

  beforeMount() {
    this.doCallApiFetchOrganisations();
  },

  beforeUnmount() {
    this.reset();
  },

  methods: {
    ...mapActions('modal', ['setModal']),

    ...mapActions('organisation', [
      'setPage',
      'setFilterName',
      'setFilterLabel',
      'setFilterOwner',
      'reset',
    ]),

    onClickCreate() {
      this.setModal(OrganisationNewModal);
    },

    async doCallApiFetchOrganisations() {
      await organisationRequest.fetchOrganisations();
    },

    onBtPrevious() {
      this.setPage({ page: this.page - 1 });
      this.doCallApiFetchOrganisations();
    },

    onBtNext() {
      this.setPage({ page: this.page + 1 });
      this.doCallApiFetchOrganisations();
    },

    onSetPage(page) {
      this.setPage({ page });
      this.doCallApiFetchOrganisations();
    },

    onFilterChanged(e) {
      const model = e.api.getFilterModel();

      if ('ownerName' in model) {
        this.setFilterOwner(model.ownerName.filter);
      } else {
        this.setFilterOwner('');
      }

      if ('name' in model) {
        this.setFilterName(model.name.filter);
      } else {
        this.setFilterName('');
      }

      if ('label' in model) {
        this.setFilterLabel(model.label.filter);
      } else {
        this.setFilterLabel('');
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.organisation-table {
  @include flex("block", "column", "start", "start");
  height: 100%;
  width: 100%;

  .organisation-toolbar {
    @include flex("block", "row", "start", "start");

    width: 100%;
    margin-top: 2rem;

    .organisation-actions {
      @include flex("block", "row", "start", "start");
      padding-left: 1rem;
      column-gap: 1em;
      margin: 0.2em;
      min-width: max-content;

      .icon {
        margin-right: 0.2em;
      }
    }

    .organisation-pagination {
      @include flex("block", "row", "end", "start");

      padding-right: 2rem;
      width: 100%;
    }
  }

  .organisation-table-gird {
    @include flex("block", "row", "start", "start");

    height: 100%;
    padding: 1rem 0.6rem 0 0.6rem;
    width: 100%;

    .title {
      @include flex("block", "row", "center", "start");

      font-size: 1.5rem;
      font-weight: $font-weight-bold;
    }

    .base-button {
      margin-left: auto;
    }
  }
}
</style>
