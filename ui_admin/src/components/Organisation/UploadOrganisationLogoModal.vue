<template>
  <section class="upload-organisation-logo-modal">
    <section class="header">
      Upload logo
    </section>
    <section class="body">
      <section class="row field">
        <section class="wrapper-select">
          <section class="uploader">
            <label for="custom-themes-file-input" class="label">
              <section class="button">Select File</section>
              <section class="file">{{ customFileName || 'No file selected.' }}</section>
            </label>
            <input id="custom-themes-file-input"
                   accept=".jpg,.jpeg,.png,.svg"
                   type="file"
                   @change="onInputFile"
            />
            <section v-if="file != null" class="remove-file" @click="onRemoveFile">
              <i class="fa-regular fa-trash-can icon"></i>
            </section>
          </section>
        </section>
      </section>
    </section>
    <section class="footer">
      <base-button class="cancel" colour="light" type="link" @click="closeModal">Cancel</base-button>
      <loading-blocks-overlay v-if="saving" />
      <base-button class="save" colour="base" @click="onSave">Save</base-button>
    </section>
  </section>
</template>
<script>
import BaseButton from '@/components/Base/BaseButton';
import BaseRadio from '@/components/Base/BaseRadio';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import OrganisationToast from '@/components/Toast/OrganisationToast';
import organisationRequest from '@/services/OrganisationRequest';
import { mapActions, mapState } from 'vuex';

export default {
  name: 'upload-organisation-logo-modal',
  components: { LoadingBlocksOverlay, BaseRadio, BaseButton },

  data() {
    return {
      workspaceOptions: [],
      localWorkspaces: [],
      saving: false,
      file: null,
    };
  },

  computed: {
    ...mapState('organisation', ['selectedOrganisation']),
    customFileName() {
      if (this.file == null) return 'No File Selected.';

      return this.file.name;
    },
  },

  methods: {

    ...mapActions('modal', ['setModal', 'closeModal']),

    ...mapActions('toast', ['addToast']),

    onInputFile(e) {
      [this.file] = e.target.files;
    },

    onRemoveFile() {
      this.file = null;
    },

    async onSave() {
      if (this.file == null) {
        await this.addToast({
          toastComponent: {
            component: OrganisationToast,
            id: 'organisation-error',
          },
          toastData: {
            label: 'Please select a file',
          },
        });
        return;
      }
      this.saving = true;
      await organisationRequest.uploadLogoOrganisation(this.file).then(async (response) => {
        if (response.status) {
          await this.addToast({
            toastComponent: {
              component: OrganisationToast,
              id: 'organisation-error',
            },
            toastData: {
              label: response.data,
            },
          });
        } else {
          await this.addToast({
            toastComponent: {
              component: OrganisationToast,
              id: 'organisation-updated',
            },
            toastData: {
              label: this.label,
            },
          });
          this.saving = false;
          await organisationRequest.fetchOrganisations();
          this.closeModal();
        }
      });
    },

  },
};
</script>
<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";
.upload-organisation-logo-modal {

  @include modal;

  position: relative;

  .field {
    margin-bottom: 0.6rem;
  }

  .wrapper-select {
    .uploader {
      @include flex("block", "row", "start", "center");
      @include stretch;

      margin-top: 1rem;

      .remove-file {
        @include flex("block", "row", "center", "center");

        color: clr('red');
        cursor: pointer;
        margin-left: 0.5rem;
        transition: all $interaction-transition-time;

        .icon {
          height: $font-size-base;
          width: $font-size-base;
        }

        &:hover {
          opacity: 0.8;
        }
      }

      .label {
        @include flex("block", "row", "start", "stretch");
        @include stretch;

        cursor: pointer;
        font-size: $font-size-xs;
        font-weight: $font-weight-bold;

        .button {
          @include flex("block", "row", "center", "center");
          @include rigid;

          background-color: $uploads-purple;
          border: 2px solid darken($uploads-purple, 20%);
          border-top-left-radius: $border-radius-medium;
          border-bottom-left-radius: $border-radius-medium;
          color: clr('white');
          padding: 0.5rem 1.5rem;
          transition: opacity $interaction-transition-time;

          &:hover {
            opacity: 0.8;
          }
        }

        .file {
          @include flex("block", "row", "center", "center");
          @include stretch;

          border-left-style: none;
          border-top-right-radius: $border-radius-medium;
          border-bottom-right-radius: $border-radius-medium;
          color: $uploads-purple;
        }
      }

      input {
        display: none;
      }
    }
  }

  .v-select {
    background-color: clr('white');
    width: 500px;
  }

  .footer {
    .loading-blocks-overlay {
      height: 2rem;
    }
  }

}
</style>
