<template>
  <section class="organisation-detail-modal">
    <section class="header">
      <h2>Update Organisation</h2>
    </section>
    <section class="body">
      <section class="field">
        <span>Name</span>
        <base-input placeholder="Enter a organisation name" @input="onInputName" :value="localName"/>
      </section>
      <section class="field">
        <span>Label</span>
        <base-input placeholder="Enter a organisation label" @input="onInputLabel" :value="localLabel"/>
      </section>

      <section class="field owner">
        <span>Owner</span>
        <base-input :is-disabled=true :value="localOwner.loginName"/>
      </section>
      <section class="field administrators">
        <span>Administrators</span>
        <section class="wrapper-select">
          <v-select v-model="localAdministrators"
                    class="v-select"
                    multiple
                    :options="userOptions"
                    :searchable="true"
                    :selectable="isUserOptionSelectable"
                    label="loginName"
                    @search="fetchUsers"/>
        </section>
      </section>
      <section class="field members">
        <span>Members</span>
        <section class="wrapper-select">
          <v-select v-model="localMembers"
                    class="v-select"
                    multiple
                    :options="userOptions"
                    :searchable="true"
                    :selectable="isUserOptionSelectable"
                    label="loginName"
                    @search="fetchUsers"/>
        </section>
      </section>
    </section>
    <section class="footer">
      <base-button class="cancel" colour="light" size="small" type="link"  @click="onCancel">Cancel</base-button>
      <loading-blocks-overlay v-if="saving" />
      <base-button v-else class="confirm" colour="base" size="small" :disabled="saveDisabled" @click="onSave">
        <span>Save</span>
      </base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { debounce } from 'lodash-es';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import organisationRequest from '@/services/OrganisationRequest';
import OrganisationToast from '@/components/Toast/OrganisationToast';
import userRequest from '@/services/UserRequest';
import VSelect from 'vue-select';

export default {
  name: 'organisation-detail-modal',

  components: {
    BaseButton,
    BaseInput,
    LoadingBlocksOverlay,
    VSelect,
  },

  data() {
    return {
      saving: false,
      localAdministrators: [],
      localLabel: '',
      localMembers: [],
      localName: '',
      localOwner: null,
      localWorkspaces: [],
      userOptions: [],
      workspaceOptions: [],
    };
  },

  computed: {
    ...mapState('organisation', ['selectedOrganisation']),

    saveDisabled() {
      if (this.localName.trim() === '') return true;
      if (this.localLabel.trim() === '') return true;
      return this.localOwner === null;
    },
  },

  created() {
    this.reset();
    this.doCallApiFetchUsers();
    this.fetchLocalOrganisation();
  },

  beforeUnmount() {
    this.reset();
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('organisation', ['setSelectedOrganisation']),

    ...mapActions('user', ['setSearch', 'reset']),

    ...mapActions('toast', ['addToast']),

    fetchLocalOrganisation() {
      this.localName = this.selectedOrganisation.name;
      this.localLabel = this.selectedOrganisation.settings.label;
      this.localOwner = this.selectedOrganisation.owner;
      this.localMembers = this.selectedOrganisation.members;
      this.localAdministrators = this.selectedOrganisation.administrators;
    },

    async doCallApiFetchUsers() {
      this.userOptions = (await userRequest.getUsers()).map(u => ({
        id: u.id,
        loginName: u.loginName,
        selectable: !u.organisationId,
      }));
    },

    fetchUsers: debounce(async function fetchUsers(search, loading) {
      this.setSearch(search);
      await this.doCallApiFetchUsers();
      loading(false);
    }, 500),

    onCancel() {
      this.closeModal();
    },

    async onSave() {
      if (this.saveDisabled) return;

      this.saving = true;

      const newOrg = {
        administratorIds: this.localAdministrators.map(admin => admin.id),
        id: this.selectedOrganisation.id,
        memberIds: this.localMembers.map(member => member.id),
        name: this.localName,
        ownerId: this.localOwner.id,
        workspaceIds: this.localWorkspaces.map(workspace => workspace.id),
        settings: {
          label: this.localLabel,
        },
      };

      await this.setSelectedOrganisation(newOrg);

      await organisationRequest.updateOrganisation();
      await organisationRequest.getOrganisations();

      this.saving = false;

      await this.addToast({
        toastComponent: {
          component: OrganisationToast,
          id: 'organisation-updated',
        },
        toastData: {
          label: this.label,
        },
      });

      await this.closeModal();
    },

    onInputLabel(e) {
      this.localLabel = e.target.value;
    },

    onInputName(e) {
      this.localName = e.target.value;
    },

    isUserOptionSelectable(user) {
      return user.selectable
          && !this.localAdministrators.find(t => t.id === user.id)
          && !this.localMembers.find(t => t.id === user.id)
          && !this.localOwner.id !== user.id;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.organisation-detail-modal {
  @include modal;

  position: relative;

  .field {
    margin-bottom: 0.6rem;

    &.disable {
      opacity: 0.5;
      pointer-events: none;
    }
  }

  .wrapper-select {
    height: 30px;
  }

  .v-select {
    background-color: clr('white');
    position: absolute;
    width: 500px;
  }
}
</style>
