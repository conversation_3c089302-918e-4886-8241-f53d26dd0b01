<template>
  <section class="reporting-job-upsert-data-source-rating-new-modal">
    <section class="header">
      <h2>Create Rating Scale</h2>
    </section>
    <section class="body">
      <base-input placeholder="Enter a name" @input="onInputName" :value="localRating.name"/>
      <section class="text">Mapping: </section>
      <section class="item" v-for="(item, index) in localRating.ratingScaleMap" :key="index">
        <base-input class="input" :value="item.entry" @input="onSetEntry($event, item)" />
        <base-input class="input" :value="item.value" @input="onSetValue($event, item)" />
      </section>
      <section class="add-btn" @click="onAddFormat">
        <i class="fa-solid fa-plus" />
      </section>
    </section>
    <section class="footer">
      <base-button class="cancel" colour="light" size="small" type="link" @click="onCancel">Cancel</base-button>
      <loading-blocks-overlay :size="'small'" v-if="saving" />
      <base-button v-else class="submit" colour="base" size="small" type="link" @click="onCreate">Create</base-button>
    </section>
  </section>
</template>
<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import MetadataRequest from '@/services/MetadataRequest';
import VSelect from 'vue-select';

export default {
  name: 'reporting-job-upsert-data-source-rating-new-modal',

  components: {
    VSelect,
    BaseInput,
    BaseButton,
    LoadingBlocksOverlay,
  },

  data() {
    return {
      reportingJob: {},
      saving: false,
      localRating: {
        userId: null,
        workspaceId: null,
        name: '',
        ratingScaleMap: [],
      },
    };
  },

  computed: {
    ...mapState('reportingJob', ['selectedReportingJob']),
  },

  async created() {
    this.localRating.userId = this.selectedReportingJob.userId;
    this.localRating.workspaceId = this.selectedReportingJob.workspaceId;
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    onAddFormat() {
      this.localRating.ratingScaleMap.push({
        entry: '',
        value: '',
      });
    },

    onCancel() {
      this.closeModal();
    },

    async onCreate() {
      this.saving = true;

      // Transform ratingScaleMap from array to object
      const mapped = {};
      this.localRating.ratingScaleMap.forEach(item => {
        if (item.entry) {
          mapped[item.entry] = item.value;
        }
      });

      const payload = {
        ...this.localRating,
        ratingScaleMap: mapped,
      };

      await MetadataRequest.createRatingScale(payload);

      this.closeModal();
      this.saving = false;
    },

    onInputName(e) {
      this.localRating.name = e.target.value;
    },

    onSetEntry(e, item) {
      item.entry = e.target.value;
    },

    onSetValue(e, item) {
      item.value = e.target.value;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-upsert-data-source-rating-new-modal {
  @include modal;

  position: relative;

  .body {
    .text {
      margin-bottom: 0.4rem;
      margin-top: 0.4rem;
    }

    .item {
      @include flex("block", "row", "start", "center");

      font-size: $font-size-sm;
      margin-bottom: 0.4rem;

      .input {
        margin-right: 0.8rem;
        width: 15rem;
      }
    }

    .add-btn {
      @include flex("block", "row", "center", "center");

      border: 1px solid clr('purple');
      color: clr('purple');
      cursor: pointer;
      border-radius: $border-radius-medium;
      height: 2rem;
      width: 2rem;

      &:hover {
        border-color: lighten(clr('purple'), 10%);
        color: lighten(clr('purple'), 10%);
      }
    }
  }
}
</style>
