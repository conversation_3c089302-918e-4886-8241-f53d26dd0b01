<template>
  <section class="reporting-job-upsert-data-source-preview-info">
    <section class="btn-select"
      :class="{isSelected}"
      @click="openDropdownDataType = !openDropdownDataType"
      v-click-outside="handleClickOutsideDataType"
    >
      <span v-if="isSelected && metadataType">{{metadataType}}</span>
      <span v-else>Select type</span>
      <i class="fa-solid fa-chevron-down icon-down" />
      <reporting-job-upsert-data-source-data-type-dropdown v-if="openDropdownDataType" @close="openDropdownDataType = false" @select="onSelectDataType" />
    </section>

    <section class="btn-select"
      :class="{isSelected}"
      v-if="isDate"
    >
      <span v-if="isSelected">{{dateFormatValue}}</span>
    </section>

    <section class="btn-select"
      :class="{isSelected}"
      v-if="isRating"
      @click="openDropdownRating = !openDropdownRating"
      v-click-outside="handleClickOutsideRating"
    >
      <span v-if="isSelected && ratingScaleMaps">Selected ✔</span>
      <span v-else>Please a select type</span>
      <i class="fa-solid fa-chevron-down icon-down" />
      <reporting-job-upsert-data-source-rating-dropdown v-if="openDropdownRating" @close="openDropdownRating = false" @select="onSelectRating" />
    </section>
  </section>
</template>

<script>
import DatetimeFormat from '@/helpers/datetime-format';
import ReportingJobUpsertDataSourceDataTypeDropdown from '@/components/ReportingJobUpsertDataSource/ReportingJobUpsertDataSourceDataTypeDropdown';
import ReportingJobUpsertDataSourceRatingDropdown from '@/components/ReportingJobUpsertDataSource/ReportingJobUpsertDataSourceRatingDropdown';

import BaseButton from '@/components/Base/BaseButton';
import DataType from '@/enum/data-type';

export default {
  name: 'reporting-job-upsert-data-source-preview-info',

  components: {
    BaseButton,
    ReportingJobUpsertDataSourceDataTypeDropdown,
    ReportingJobUpsertDataSourceRatingDropdown,
  },

  props: {
    dateFormat: {
      type: String,
      required: false,
    },
    isSelected: {
      type: Boolean,
      required: true,
    },
    ratingScaleMaps: {
      type: Object,
      required: false,
    },
    metadataType: {
      type: String,
      required: false,
    },
  },

  data() {
    return {
      openDropdownDataType: false,
      openDropdownRating: false,
    };
  },

  computed: {
    dateFormatValue() {
      return DatetimeFormat[this.dateFormat][0];
    },

    isDate() {
      return this.metadataType === DataType.DATE.value();
    },

    isRating() {
      return this.metadataType === DataType.RATING.value();
    },
  },

  methods: {
    handleClickOutsideDataType() {
      this.openDropdownDataType = false;
    },

    handleClickOutsideRating() {
      this.openDropdownRating = false;
    },

    onSelectRating(ratingScaleMap) {
      this.$emit('selectRating', ratingScaleMap);
    },

    onSelectDataType(dataType) {
      this.$emit('selectType', dataType);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-upsert-data-source-preview-info {
  @include flex("block", "column", "start", "start");

  .btn-select {
    @include flex("block", "row", "space-between", "start");

    background-color: clr('white');
    border-radius: 4px;
    border: $border-standard;
    cursor: pointer;
    font-size: $font-size-xxs;
    font-weight: $font-weight-bold;
    margin-top: 0.4rem;
    min-width: 10rem;
    outline: none;
    padding: 0.3rem 0.35rem;
    position: relative;

    &.isSelected {
      border: $border-purple;
      color: $border-color-purple;
    }

    &:hover,
    &:focus {
      border: $border-purple;
      color: $border-color-purple;
      outline: none;
    }
  }
}
</style>
