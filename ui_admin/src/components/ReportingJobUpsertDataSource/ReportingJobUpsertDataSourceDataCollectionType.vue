<template>
  <section class="reporting-job-upsert-data-source-data-collection-type">
    <section class="item">
      <span class="title">Data collection type:</span>
      <v-select class="value"
        :options="dataCollectionTypeOptions"
        :clearable="false"
        :reduce="option => option.value"
        v-model="localDataCollectionType"
      />
    </section>
    <section v-if="localDataCollectionType === 'CUSTOM'" class="item">
      <span class="title">Data custom start date:</span>
      <vue-date-picker v-model="localDataCustomStartDate" :format="'yyyy-MM-dd'" />
    </section>
    <section v-if="localDataCollectionType === 'CUSTOM'" class="item">
      <span class="title">Data custom end date:</span>
      <vue-date-picker v-model="localDataCustomEndDate" :format="'yyyy-MM-dd'" />
    </section>
  </section>
</template>

<script>
import VSelect from 'vue-select';
import VueDatePicker from '@vuepic/vue-datepicker';

export default {
  name: 'reporting-job-upsert-data-source-data-collection-type',

  components: {
    VSelect,
    VueDatePicker,
  },

  props: {
    dataCollectionType: {
      type: String,
      required: true,
    },
    dataCustomStartDate: {
      type: Date,
      required: false,
    },
    dataCustomEndDate: {
      type: Date,
      required: false,
    },
  },

  data() {
    return {
      dataCollectionTypeOptions: [
        { label: 'Entire life', value: 'ENTIRE_LIFE' },
        { label: 'Custom', value: 'CUSTOM' },
        { label: 'Today', value: 'TODAY' },
        { label: 'Yesterday', value: 'YESTERDAY' },
        { label: 'Last 24 hours', value: 'LAST_24_HOURS' },
        { label: 'This Month', value: 'THIS_MONTH' },
        { label: 'Last Month', value: 'LAST_MONTH' },
        { label: 'Last 30 Days', value: 'LAST_30_DAYS' },
      ],
      localDataCollectionType: this.dataCollectionType,
      localDataCustomStartDate: this.dataCustomStartDate,
      localDataCustomEndDate: this.dataCustomEndDate,
    };
  },

  watch: {
    localDataCollectionType(newVal) {
      this.$emit('selectDataCollectionType', newVal);
    },

    localDataCustomStartDate(newVal) {
      const date = new Date(newVal);
      const utcDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
      this.$emit('selectDataCustomStartDate', utcDate);
    },

    localDataCustomEndDate(newVal) {
      const date = new Date(newVal);
      const utcEndOfDay = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999));
      this.$emit('selectDataCustomEndDate', utcEndOfDay);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-upsert-data-source-data-collection-type {
  @include flex("block", "column", "start", "start");

  margin-top: 1rem;
  width: 100%;

  .item {
    @include flex("block", "row", "start", "center");

    font-size: $font-size-sm;
    margin-bottom: 0.4rem;

    .title {
      min-width: 10rem;
    }

    .value {
      width: 25rem;
    }
  }
}
</style>
