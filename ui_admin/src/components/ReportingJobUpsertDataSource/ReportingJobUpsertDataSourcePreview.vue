<template>
  <section class="reporting-job-upsert-data-source-preview">
    <reporting-job-upsert-data-source-preview-column
      v-for="(header, index) in localHeaders"
      :column-index="index"
      :header="header"
      :key="index"
      :pre-processing-acknowledgement="preProcessingAcknowledgement"
      :values="columnPreviews[index].values"
      @selectColumn="onSelectColumn"
      @selectType="onSelectType"
      @selectRating="onSelectRating"
      @updateHeader="onUpdateHeader"
    />
  </section>
</template>

<script>
import ReportingJobUpsertDataSourcePreviewColumn from '@/components/ReportingJobUpsertDataSource/ReportingJobUpsertDataSourcePreviewColumn';

export default {
  name: 'reporting-job-upsert-data-source-preview',

  components: {
    ReportingJobUpsertDataSourcePreviewColumn,
  },

  props: {
    preProcessingAcknowledgement: {
      type: Object,
      required: true,
    },
    localHeaders: {
      type: Array,
      required: true,
    },
  },

  computed: {
    columnPreviews() {
      return this.preProcessingAcknowledgement.columnPreviews;
    },
  },

  methods: {
    onSelectColumn(columnIndex) {
      this.$emit('selectColumn', columnIndex);
    },

    onSelectRating(columnIndex, rating) {
      this.$emit('selectRating', columnIndex, rating);
    },

    onSelectType(columnIndex, dataType) {
      this.$emit('selectType', columnIndex, dataType);
    },

    onUpdateHeader(columnIndex, header) {
      this.$emit('updateHeader', columnIndex, header);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-upsert-data-source-preview {
  @include rigid;

  border-radius: $border-radius-medium;
  display: grid;
  grid-auto-columns: minmax(250px, 1fr);
  grid-auto-flow: column;
  overflow-x: auto;
  width: 100%;
}
</style>
