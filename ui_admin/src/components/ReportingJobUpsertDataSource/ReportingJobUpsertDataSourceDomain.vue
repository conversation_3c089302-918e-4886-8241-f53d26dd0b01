<template>
  <section class="reporting-job-upsert-data-source-domain">
    <span>Dataset domain: </span>
    <base-input :value="domain" @input="onSetDomain" />
  </section>
</template>

<script>
import BaseInput from '@/components/Base/BaseInput';

export default {
  name: 'reporting-job-upsert-data-source-domain',

  components: {
    BaseInput,
  },

  props: {
    domain: {
      type: String,
      required: true,
    },
  },

  methods: {
    onSetDomain(e) {
      this.$emit('setDomain', e.target.value);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-upsert-data-source-domain {
  @include flex("block", "row", "start", "center");

  margin-top: 1rem;
  width: 100%;

  span {
    margin-right: 0.4rem;
  }

  .base-input {
    width: 15rem;
  }
}
</style>
