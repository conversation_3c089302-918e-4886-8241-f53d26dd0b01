<template>
  <section class="reporting-job-upsert-data-source-data-type-dropdown">
    <section class="item comment" @click="onClickComment">
      <i class="fa-regular fa-comment icon" />
      <section class="text">
        <span class="title">COMMENT</span>
      </section>
    </section>
    <section class="item score" @click="onClickScore">
      <i class="fa-solid fa-chart-simple icon" />
      <section class="text">
        <span class="title">SCORE</span>
      </section>
    </section>
    <section class="item rating-scale" @click="onClickRating">
      <i class="fa-solid fa-face-laugh-beam icon" />
      <section class="text">
        <span class="title">RATING</span>
      </section>
    </section>
    <section class="item numeric" @click="onClickNumber">
      <i class="fa-solid fa-hashtag icon" />
      <section class="text">
        <span class="title">NUMERIC</span>
      </section>
    </section>
    <section class="item datetime" @click="onClickDateTime">
      <i class="fa-regular fa-calendar-check icon" />
      <section class="text">
        <span class="title">DATE</span>
      </section>
    </section>
    <section class="item text" @click="onClickText">
      <i class="fa-solid fa-font icon" />
      <section class="text">
        <span class="title">TEXT</span>
      </section>
    </section>
  </section>
</template>

<script>
import BaseButton from '@/components/Base/BaseButton';
import DataType from '@/enum/data-type';

export default {
  name: 'reporting-job-upsert-data-source-data-type-dropdown',

  components: {
    BaseButton,
  },

  methods: {
    onClickComment() {
      this.$emit('select', DataType.COMMENT.value());
    },

    onClickDateTime() {
      this.$emit('select', DataType.DATE.value());
    },

    onClickNumber() {
      this.$emit('select', DataType.NUMERIC.value());
    },

    onClickRating() {
      this.$emit('select', DataType.RATING.value());
    },

    onClickScore() {
      this.$emit('select', DataType.SCORE.value());
    },

    onClickText() {
      this.$emit('select', DataType.TEXT.value());
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-upsert-data-source-data-type-dropdown {
  @include flex("block", "column", "start", "start");
  @include panel-dropdown;

  font-weight: $font-weight-normal;
  left: 0;
  top: 28px;
  width: 14rem;

  .item {
    @include flex("block", "row", "start", "center");
    @include hover-element-dropdown;

    padding: 0.6rem;
    width: 100%;

    &:hover{
      font-weight: unset;
    }

    .icon {
      font-size: $font-size-sm;
      margin-right: 0.6rem;
    }

    .text {
      @include flex("block", "column", "start", "start");

      font-size: $font-size-xs;

      .title {
        font-weight: $font-weight-bold;
      }
    }
  }
}
</style>
