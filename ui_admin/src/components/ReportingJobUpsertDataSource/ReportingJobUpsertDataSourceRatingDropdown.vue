<template>
  <section class="reporting-job-upsert-data-source-rating-dropdown">
    <loading-blocks-overlay v-if="loading" />
    <section v-else class="item" v-for="(item, index) in localRatingScales" :key="index" @click="onClickItem(item)">
      <span class="title">{{item.name}}</span>
    </section>

    <section class="item new" @click="onClickNew">
      <span class="text">New Format</span>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import MetadataRequest from '@/services/MetadataRequest';
import ReportingJobUpsertDataSourceRatingNewModal from '@/components/ReportingJobUpsertDataSource/ReportingJobUpsertDataSourceRatingNewModal';

export default {
  name: 'reporting-job-upsert-data-source-rating-dropdown',

  components: {
    BaseButton,
    LoadingBlocksOverlay,
  },

  data() {
    return {
      loading: false,
      localRatingScales: [],
    };
  },

  computed: {
    ...mapState('reportingJob', ['selectedReportingJob']),
  },

  async created() {
    this.loading = true;
    this.localRatingScales = await MetadataRequest.fetchRatingScales(this.selectedReportingJob.userId);
    this.loading = false;
  },

  methods: {
    ...mapActions('modal', ['setModal']),

    onClickItem(item) {
      this.$emit('select', item);
    },

    onClickNew() {
      this.setModal(ReportingJobUpsertDataSourceRatingNewModal);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-upsert-data-source-rating-dropdown {
  @include flex("block", "column", "start", "start");
  @include panel-dropdown;

  font-weight: $font-weight-normal;
  left: 0;
  top: 28px;
  width: 14rem;

  .item {
    @include flex("block", "row", "start", "center");
    @include hover-element-dropdown;

    padding: 0.6rem;
    width: 100%;

    &.new {
      border-top: $border-purple;
    }
  }
}
</style>
