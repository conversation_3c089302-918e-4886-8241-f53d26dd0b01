<template>
  <section class="reporting-job-upsert-data-source-preview-column" :class="{isSelectedComment, isSelectedMetadata} ">
    <section class="header">
      <section class="header-left">
        <base-radio-with-tick-mark :value="isSelected" @click="onSelectColumn" />
      </section>
      <section class="header-right">
        <section v-if="!labelEditing" class="header-right-text">
          <section class="text" v-html="localHeader"></section>
          <section class="button" @click.stop="labelEditing = true">
            <i class="fa-solid fa-pen-to-square icon" />
          </section>
        </section>
        <section v-else>
          <base-input
            :value="localHeader"
            :focus="true"
            class="text"
            @input="onUpdateHeader"
            @blur="labelEditing=false"
            @submit="labelEditing=false"
          />
        </section>
        <reporting-job-upsert-data-source-preview-info
          :date-format="dateFormat"
          :is-selected="isSelected"
          :rating-scale-maps="ratingScaleMaps"
          :metadata-type="metadataType"
          @selectType="onSelectType"
          @selectRating="onSelectRating"
        />
      </section>
    </section>

    <section v-for="(row, i) in rows" :key="i" class="row">
      <span v-if="row === ''" class="empty">[Empty Row]</span>
      <span class="v-else" :title="row">{{ row }}</span>
    </section>
  </section>
</template>

<script>
import BaseRadioWithTickMark from '@/components/Base/BaseRadioWithTickMark';
import BaseInput from '@/components/Base/BaseInput';
import ReportingJobUpsertDataSourcePreviewInfo from '@/components/ReportingJobUpsertDataSource/ReportingJobUpsertDataSourcePreviewInfo';
import DataType from '@/enum/data-type';

export default {
  name: 'reporting-job-upsert-data-source-preview-column',

  components: {
    BaseRadioWithTickMark,
    BaseInput,
    ReportingJobUpsertDataSourcePreviewInfo,
  },

  props: {
    columnIndex: {
      type: Number,
      required: true,
    },
    header: {
      type: String,
      required: true,
    },
    preProcessingAcknowledgement: {
      type: Object,
      required: true,
    },
    values: {
      type: Array,
      required: true,
    },
  },

  data() {
    return {
      labelEditing: false,
      localHeader: '',
    };
  },

  created() {
    this.localHeader = this.header;
  },

  computed: {
    dateFormat() {
      return this.preProcessingAcknowledgement.matchedFormats[this.columnIndex].list[0];
    },

    isSelected() {
      return this.isSelectedComment || this.isSelectedMetadata;
    },

    isSelectedComment() {
      return this.preProcessingAcknowledgement.commentColumns.includes(this.columnIndex);
    },

    isSelectedMetadata() {
      return this.preProcessingAcknowledgement.metadataColumns.includes(this.columnIndex);
    },

    ratingScaleMaps() {
      const { ratingScaleMaps } = this.preProcessingAcknowledgement;
      if (ratingScaleMaps) {
        return ratingScaleMaps[this.columnIndex];
      }
      return undefined;
    },

    metadataType() {
      const comments = this.preProcessingAcknowledgement.commentColumns;
      let index = comments.indexOf(this.columnIndex);

      if (index !== -1) {
        return DataType.COMMENT.value();
      }

      const columns = this.preProcessingAcknowledgement.metadataColumns;
      index = columns.indexOf(this.columnIndex);

      if (index !== -1) {
        return this.preProcessingAcknowledgement.metadataTypes[index];
      }

      return undefined;
    },

    rows() {
      const copy = [...this.values];
      return copy.splice(1);
    },
  },

  methods: {
    onSelectColumn() {
      this.$emit('selectColumn', this.columnIndex);
    },

    onSelectRating(ratingScaleMap) {
      this.$emit('selectRating', this.columnIndex, ratingScaleMap);
    },

    onSelectType(metadataType) {
      this.$emit('selectType', this.columnIndex, metadataType);
    },

    onUpdateHeader(e) {
      this.localHeader = e.target.value;
      this.$emit('updateHeader', this.columnIndex, this.localHeader);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-upsert-data-source-preview-column {
  @include flex("block", "column", "start", "stretch");

  .header {
    @include flex("block", "row", "start", "stretch");

    border-radius: $border-radius-medium;
    border: 1px solid clr('purple', 'light');
    height: 7rem;
    padding: 1rem;

    &.isComment {
      background-color: rgba(80, 204, 96, 0.5);
    }

    .header-left {
      margin-top: 0.4rem;

      .checkbox {
        height: 1.4rem;
        width: 1.4rem;
      }
    }

    .header-right {
      padding-left: 0.2rem;
      padding-right: 0.2rem;
      width: 100%;

      .header-right-text {
        @include flex("block", "row", "start", "stretch");

        .icon {
          cursor: pointer;
          font-size: $font-size-sm;
          margin-left: 0.4rem;
          margin-top: 0.2rem;
        }
      }

      .text {
        @include truncate;

        font-size: $font-size-sm;
        font-weight: $font-weight-bold;
      }
    }
  }

  .row {
    @include truncate;

    border-top: none;
    border: $border-standard;
    font-size: $font-size-xs;
    height: 37px;
    padding: 0.7rem 1.2rem;
    z-index: 0;

    &.isComment {
      background-color: rgba(80, 204, 96, 0.2);
    }

    .empty {
      font-style: italic;
      opacity: 0.5;
    }
  }

  &.isSelectedComment {
    .header {
      background-color: #b2e7ad;
    }

    .row {
      background-color: #eaf9e9;
    }
  }

  &.isSelectedMetadata {
    .header {
      background-color: #d6e0ff;
    }

    .row {
      background-color: #e2e6ff;
    }
  }
}
</style>
