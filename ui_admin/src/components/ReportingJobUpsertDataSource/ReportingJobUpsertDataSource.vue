<template>
  <section class="reporting-job-upsert-data-source">
    <loading-blocks-overlay v-if="loading" />
    <section v-else class="wrapper">
      <section class="label">
        Step 2: Data Sources
      </section>
      <section class="item source-type">
        <span class="title">Source type:</span>
        <v-select class="value"
          :clearable="false"
          :options="sourceTypeOptions"
          :reduce="option => option.value"
          v-model="localDataSource.dataType"
          @option:selected="onSelectSourceType"
        />
      </section>
      <loading-blocks-overlay v-if="loadingSurveys" />
      <section v-else class="item survey">
        <span class="title">Survey:</span>
        <v-select class="value"
          :clearable="false"
          :options="surveyOptions"
          :reduce="option => option.id"
          v-model="localDataSource.dataId"
          @option:selected="onSelectSurvey"
        />
      </section>

      <!-- Survey Overview Section -->
      <section v-if="localDataSource.dataId" class="item survey-overview">
        <span class="title">Survey Status:</span>
        <loading-blocks-overlay v-if="loadingSurveyOverview" />
        <input
            v-else
            type="text"
            class="value readonly-input"
            readonly
            :value="surveyOverviewText"
        />
      </section>

      <loading-blocks-overlay v-if="loadingPreProcessingAcknowledgement" />
      <reporting-job-upsert-data-source-preview v-else-if="localDataSource.dataId && surveyOverview && !loadingSurveyOverview"
                                                :pre-processing-acknowledgement="localDataSource.preProcessingAcknowledgement"
                                                :local-headers="localHeaders"
                                                @selectColumn="onSelectColumn"
                                                @selectType="onSelectType"
                                                @selectRating="onSelectRating"
                                                @updateHeader="onUpdateHeader"
      />

      <reporting-job-upsert-data-source-data-collection-type
          v-if="localDataSource.dataId && surveyOverview && !loadingSurveyOverview"
          :data-collection-type="localDataSource.dataCollectionType"
          :data-custom-start-date="localDataSource.dataCustomStartDate"
          :data-custom-end-date="localDataSource.dataCustomEndDate"
          @selectDataCollectionType="onSelectDataCollectionType"
          @selectDataCustomStartDate="onSelectDataCustomStartDate"
          @selectDataCustomEndDate="onSelectDataCustomEndDate"
      />

      <reporting-job-upsert-data-source-exclude-auto-topics
          v-if="localDataSource.dataId && surveyOverview && !loadingSurveyOverview"
          :exclude-adore-themes="localDataSource.preProcessingAcknowledgement.excludeAutoTopics"
          @select="onSelectExcludeAutoTopics"
      />

      <reporting-job-upsert-data-source-domain
          v-if="localDataSource.dataId && surveyOverview && !loadingSurveyOverview"
          :domain="localDataSource.preProcessingAcknowledgement.datasetDomain"
          @setDomain="onSetDomain"
      />

      <!-- Metric Calculation Settings Component -->
      <reporting-job-upsert-data-source-metric-calculation-settings
          v-if="localDataSource.dataId && surveyOverview && !loadingSurveyOverview"
          :metricCalculationSettings="localDataSource.preProcessingAcknowledgement.metricCalculationSettings || []"
          :metadataHeaders="localDataSource.preProcessingAcknowledgement.metadataHeaders"
          :metadataColumns="localDataSource.preProcessingAcknowledgement.metadataColumns"
          @update:metricCalculationSettings="updateMetricCalculationSettings"
      />

      <section class="actions">
        <base-button colour="light" size="small" @click="onClickBack">
          <span>Back</span>
        </base-button>
        <loading-blocks-overlay v-if="saving" />
        <section v-else class="btn-group">
          <base-button class="btn" colour="danger" size="small" @click="onClickDiscard">
            <span>Discard changes</span>
          </base-button>
          <base-button class="btn" colour="base" size="small" @click="onClickSave" :disabled="!surveyOverview || loadingSurveyOverview">
            <span>Save changes</span>
          </base-button>
          <base-button class="btn" colour="base" size="small" @click="onClickNext" :disabled="!surveyOverview || loadingSurveyOverview">
            <span>Next</span>
          </base-button>
        </section>
      </section>
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import DataType from '@/enum/data-type';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import ReportingJobRequest from '@/services/ReportingJobRequest';
import ReportingJobUpsertDataSourceDataCollectionType from '@/components/ReportingJobUpsertDataSource/ReportingJobUpsertDataSourceDataCollectionType';
import ReportingJobUpsertDataSourceDomain from '@/components/ReportingJobUpsertDataSource/ReportingJobUpsertDataSourceDomain';
import ReportingJobUpsertDataSourceExcludeAutoTopics from '@/components/ReportingJobUpsertDataSource/ReportingJobUpsertDataSourceExcludeAutoTopics';
import ReportingJobUpsertDataSourceMetricCalculationSettings from '@/components/ReportingJobMetricCalculationSetting/ReportingJobUpsertDataSourceMetricCalculationSettings';
import ReportingJobUpsertDataSourcePreview from '@/components/ReportingJobUpsertDataSource/ReportingJobUpsertDataSourcePreview';
import VSelect from 'vue-select';

export default {
  name: 'reporting-job-upsert-data-source',

  components: {
    BaseButton,
    LoadingBlocksOverlay,
    ReportingJobUpsertDataSourceDataCollectionType,
    ReportingJobUpsertDataSourceDomain,
    ReportingJobUpsertDataSourceExcludeAutoTopics,
    ReportingJobUpsertDataSourceMetricCalculationSettings,
    ReportingJobUpsertDataSourcePreview,
    VSelect,
  },

  data() {
    return {
      loading: true,
      loadingSurveys: false,
      loadingPreProcessingAcknowledgement: true,
      loadingSurveyOverview: false,
      localDataSource: {
        // dataType: 'QUALTRICS',
        dataType: null,
        dataId: null,
        dataCollectionType: null,
        dataCustomStartDate: null,
        dataCustomEndDate: null,
        preProcessingAcknowledgement: {},
      },
      localHeaders: [],
      saving: false,
      sourceTypeOptions: [{ label: 'Qualtrics', value: 'QUALTRICS' }],
      surveyOptions: [],
      surveyOverview: undefined, // undefined = not loaded, null = permission issue, object = success
    };
  },

  computed: {
    ...mapState('reportingJob', ['selectedReportingJob']),

    surveyOverviewText() {
      if (!this.localDataSource.dataId) {
        return '';
      }

      if (this.loadingSurveyOverview) {
        return 'Loading survey overview...';
      }

      if (this.surveyOverview === null) {
        return 'Survey not found due to permission issue';
      }

      if (this.surveyOverview && typeof this.surveyOverview.responseCount === 'number') {
        return `Response count: ${this.surveyOverview.responseCount}`;
      }

      if (this.surveyOverview) {
        return 'Survey overview available';
      }

      return '';
    },
  },

  async created() {
    // Fetching the data sources
    const foundDataSources = await ReportingJobRequest.getDataSources(this.selectedReportingJob.id);
    if (foundDataSources.length !== 0) {
      [this.localDataSource] = foundDataSources;
      if (this.localDataSource.dataType === 'QUALTRICS') {
        // Fetching the survey options
        await this.fetchSurveyOptions();

        // If there's already a selected survey, fetch its overview
        if (this.localDataSource.dataId) {
          await this.fetchSurveyOverview(this.localDataSource.dataId);
        }
      }

      // Only fetch column headers if we have a valid survey overview
      if (this.surveyOverview) {
        this.fetchColumnHeaders();
      }
    }
    this.loading = false;
    this.loadingPreProcessingAcknowledgement = false;
  },

  methods: {
    deselectCommentColumn(columnIndex) {
      const index = this.localDataSource.preProcessingAcknowledgement.commentColumns.indexOf(columnIndex);
      if (index !== -1) {
        this.localDataSource.preProcessingAcknowledgement.commentColumns.splice(index, 1);
        this.localDataSource.preProcessingAcknowledgement.commentColumnsHeaders.splice(index, 1);
      }
    },

    deselectMetadataColumn(columnIndex) {
      const index = this.localDataSource.preProcessingAcknowledgement.metadataColumns.indexOf(columnIndex);
      if (index !== -1) {
        this.localDataSource.preProcessingAcknowledgement.metadataColumns.splice(index, 1);
        this.localDataSource.preProcessingAcknowledgement.metadataTypes.splice(index, 1);
        this.localDataSource.preProcessingAcknowledgement.metadataHeaders.splice(index, 1);
      }
    },

    fetchColumnHeaders() {
      this.localHeaders = [...this.localDataSource.preProcessingAcknowledgement.columnPreviews.map(item => item.values[0])];
      for (let i = 0; i < this.localHeaders.length; i += 1) {
        const index = this.localDataSource.preProcessingAcknowledgement.metadataColumns.indexOf(i);
        if (index !== -1) {
          this.localHeaders[i] = this.localDataSource.preProcessingAcknowledgement.metadataHeaders[index];
        }
      }
    },

    async fetchSurveyOptions() {
      this.surveyOptions = await ReportingJobRequest.getDataSourceOverviews(this.selectedReportingJob.id);
      this.surveyOptions = this.surveyOptions.map((item) => ({
        id: item.id,
        label: `${item.title} ----- (${item.id}) `,
      }));
    },

    async fetchSurveyOverview(surveyId) {
      this.loadingSurveyOverview = true;
      try {
        this.surveyOverview = await ReportingJobRequest.getDataSourceOverview(this.selectedReportingJob.id, surveyId);
      } catch (error) {
        this.surveyOverview = null;
      } finally {
        this.loadingSurveyOverview = false;
      }
    },

    isSelectedColumn(columnIndex) {
      return this.isSelectedCommentColumn(columnIndex) || this.isSelectedMetadataColumn(columnIndex);
    },

    isSelectedCommentColumn(columnIndex) {
      return this.localDataSource.preProcessingAcknowledgement.commentColumns.includes(columnIndex);
    },

    isSelectedMetadataColumn(columnIndex) {
      return this.localDataSource.preProcessingAcknowledgement.metadataColumns.includes(columnIndex);
    },

    onClickBack() {
      this.$emit('back');
    },

    async onClickDiscard() {
      this.saving = true;
      this.loading = true;
      this.loadingPreProcessingAcknowledgement = true;
      const foundDataSources = await ReportingJobRequest.getDataSources(this.selectedReportingJob.id);
      if (foundDataSources.length !== 0) {
        [this.localDataSource] = foundDataSources;

        // Re-fetch survey overview if needed
        if (this.localDataSource.dataId) {
          await this.fetchSurveyOverview(this.localDataSource.dataId);
        }
      }
      this.saving = false;
      this.loading = false;
      this.loadingPreProcessingAcknowledgement = false;
    },

    async onClickNext() {
      this.$emit('next');
    },

    async onClickSave() {
      this.saving = true;
      this.localDataSource.preProcessingAcknowledgement.chosenFormats = this.localDataSource.preProcessingAcknowledgement.matchedFormats.map((item) => item.list[0]);
      await ReportingJobRequest.updateDataSources(this.selectedReportingJob.id, [this.localDataSource]);
      this.saving = false;
    },

    onSelectColumn(columnIndex) {
      if (this.isSelectedMetadataColumn(columnIndex)) {
        this.deselectMetadataColumn(columnIndex);
      } else if (this.isSelectedCommentColumn(columnIndex)) {
        this.deselectCommentColumn(columnIndex);
      } else {
        this.selectCommentColumn(columnIndex);
      }
    },

    onSelectDataCollectionType(dataCollectionType) {
      this.localDataSource.dataCollectionType = dataCollectionType;
    },

    onSelectDataCustomStartDate(date) {
      this.localDataSource.dataCustomStartDate = date;
    },

    onSelectDataCustomEndDate(date) {
      this.localDataSource.dataCustomEndDate = date;
    },

    onSelectRating(columnIndex, rating) {
      this.localDataSource.preProcessingAcknowledgement.ratingScaleMaps[columnIndex] = rating.ratingScaleMap;
    },

    async onSelectSourceType(sourceType) {
      this.loadingSurveys = true;
      if (sourceType.value === 'QUALTRICS') {
        await this.fetchSurveyOptions();
      }
      this.loadingSurveys = false;
    },

    async onSelectSurvey(survey) {
      this.loadingPreProcessingAcknowledgement = true;

      // Fetch survey overview first
      await this.fetchSurveyOverview(survey.id);

      // Only proceed with preview if survey overview is available
      if (this.surveyOverview) {
        this.localDataSource.preProcessingAcknowledgement = await ReportingJobRequest.previewDataSourceResponses(this.selectedReportingJob.id, survey.id);
        this.fetchColumnHeaders();
      } else {
        // Clear preprocessing acknowledgement if no access
        this.localDataSource.preProcessingAcknowledgement = {};
        this.localHeaders = [];
      }

      this.loadingPreProcessingAcknowledgement = false;
    },

    onSelectType(columnIndex, type) {
      if (!this.isSelectedColumn(columnIndex)) {
        if (type === DataType.COMMENT.value()) {
          this.selectCommentColumn(columnIndex);
        } else {
          this.selectMetadataColumn(columnIndex, type);
        }
      } else if (this.isSelectedCommentColumn(columnIndex)) {
        if (type === DataType.COMMENT.value()) {
          // do nothing
        } else {
          this.deselectCommentColumn(columnIndex);
          this.selectMetadataColumn(columnIndex, type);
        }
      } else if (this.isSelectedMetadataColumn(columnIndex)) {
        this.deselectMetadataColumn(columnIndex);
        if (type === DataType.COMMENT.value()) {
          this.selectCommentColumn(columnIndex);
        } else {
          this.selectMetadataColumn(columnIndex, type);
        }
      }
    },

    onSelectExcludeAutoTopics(excludeAutoTopics) {
      this.localDataSource.preProcessingAcknowledgement.excludeAutoTopics = excludeAutoTopics;
    },

    onSetDomain(value) {
      this.localDataSource.preProcessingAcknowledgement.datasetDomain = value;
    },

    updateMetricCalculationSettings(value) {
      this.localDataSource.preProcessingAcknowledgement.metricCalculationSettings = value;
    },

    onUpdateHeader(columnIndex, header) {
      this.localHeaders[columnIndex] = header;
      const index = this.localDataSource.preProcessingAcknowledgement.metadataColumns.indexOf(columnIndex);
      if (index !== -1) {
        this.localDataSource.preProcessingAcknowledgement.metadataHeaders[index] = header;
      }
    },

    selectCommentColumn(columnIndex) {
      this.localDataSource.preProcessingAcknowledgement.commentColumns.push(columnIndex);
      this.localDataSource.preProcessingAcknowledgement.commentColumnsHeaders.push(this.localHeaders[columnIndex]);
    },

    selectMetadataColumn(columnIndex, metadataType) {
      this.localDataSource.preProcessingAcknowledgement.metadataColumns.push(columnIndex);
      this.localDataSource.preProcessingAcknowledgement.metadataTypes.push(metadataType);
      this.localDataSource.preProcessingAcknowledgement.metadataHeaders.push(this.localHeaders[columnIndex]);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-upsert-data-source {
  @include flex("block", "column", "start", "start");

  width: 100%;

  .wrapper {
    width: 100%;
  }

  .label {
    font-size: $font-size-md;
    font-weight: $font-weight-bold;
    margin-bottom: 0.8rem;
  }

  .item {
    @include flex("block", "row", "start", "center");

    font-size: $font-size-sm;
    margin-bottom: 0.4rem;

    .title {
      min-width: 10rem;
    }

    .value {
      width: 25rem;
    }

    .readonly-input {
      width: 25rem;
      padding: 0.5rem;
      border: 1px solid #ddd;
      border-radius: 4px;
      background-color: #f9f9f9;
      color: #666;
      font-size: $font-size-sm;
    }
  }

  .actions {
    @include flex("block", "row", "space-between", "start");

    margin-top: 0.6rem;
    width: 35rem;

    .base-button {
      .icon {
        margin-right: 0.4rem;
      }
    }

    .btn-group {
      .base-button {
        margin-left: 0.8rem;
      }
    }
  }
}
</style>
