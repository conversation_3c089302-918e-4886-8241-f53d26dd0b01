<template>
  <section class="reporting-job-upsert-data-source-exclude-auto-topics" @click="onClick">
    <base-checkbox :value="excludeAdoreThemes" />
    <span>Exclude Adoreboard-generated themes</span>
  </section>
</template>

<script>
import BaseCheckbox from '@/components/Base/BaseCheckbox';

export default {
  name: 'reporting-job-upsert-data-source-exclude-auto-topics',

  components: {
    BaseCheckbox,
  },

  props: {
    excludeAdoreThemes: {
      type: Boolean,
      required: true,
    },
  },

  methods: {
    onClick() {
      this.$emit('select', !this.excludeAdoreThemes);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-upsert-data-source-exclude-auto-topics {
  @include flex("block", "row", "start", "center");

  cursor: pointer;
  margin-top: 1rem;
  width: fit-content;

  span {
    margin-left: 0.4rem;
  }
}
</style>
