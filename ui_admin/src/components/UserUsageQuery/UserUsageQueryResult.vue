<template>
  <section class="user-usage-query-result">
    <span v-if="usages == null"><i>Enter your conditions and click 'Submit' to view the results here.</i></span>
    <ag-grid-vue v-else
      :columnDefs="columnDefs"
      :rowData="usages"
      class="ag-theme-alpine"
      style="width: 100%; height: 100%; min-width: max-content"
    />
  </section>
</template>

<script>
import { AgGridVue } from 'ag-grid-vue3';
import { mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';

export default {
  name: 'user-usage-query-result',

  components: {
    AgGridVue,
    BaseButton,
  },

  computed: {
    ...mapState('usage', ['usages']),

    columnDefs() {
      return [
        {
          id: 'Id',
          field: 'id',
          wrapText: true,
          autoHeight: true,
          resizable: true,
          minWidth: 100,
          width: 100,
        },
        {
          headerName: 'Name',
          field: 'name',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          resizable: true,
          minWidth: 250,
        },
        {
          headerName: 'Login Count',
          field: 'loginCount',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          resizable: true,
          minWidth: 200,
        },
        {
          headerName: 'Rows Uploaded',
          field: 'rowsUploaded',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          resizable: true,
          minWidth: 200,
        },
        {
          headerName: 'Storyteller Count',
          field: 'storytellerCount',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          resizable: true,
          minWidth: 200,
        },
        {
          headerName: 'Last Login Time',
          field: 'lastLoginTime',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          resizable: true,
          minWidth: 200,
        },
      ];
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.user-usage-query-result {
  @include flex("block", "row", "center", "start");

  height: 100%;
  width: 100%;
}

</style>
