<template>
  <section class="user-usage-query-builder">
    <section class="item">
      <section class="type">
        <span>Type</span>
        <v-select v-model="type" class="v-select" :options="typeOptions"/>
      </section>
      <section class="date start-date">
        <span>Start Date</span>
        <vue-date-picker class="date-picker" v-model="query.startDate" :format="'yyyy-MM-dd'" />
      </section>
      <section class="date end-date">
        <span>End Date</span>
        <vue-date-picker class="date-picker" v-model="query.endDate" :format="'yyyy-MM-dd'" />
      </section>
      <section class="row-upload min">
        <span>Min Rows</span>
        <base-input :value="query.uploadedMinRows" @input="onInputMinRows" />
      </section>
      <section class="row-upload max">
        <span>Max Rows</span>
        <base-input :value="query.uploadedMaxRows" @input="onInputMaxRows" />
      </section>
    </section>

    <section class="item">
      <loading-blocks-overlay v-if="loading" :size="'small'" />

      <template v-else>
        <base-button :size="'small'" :disabled="disableSubmit" @click="onClickSubmit">
          <i class="fa-regular fa-user-magnifying-glass icon" />
          Submit
        </base-button>

        <loading-blocks-overlay :size="'small'" class="download" v-if="downloading" />
        <base-button v-else-if="usages" :size="'small'" :colour="'success'" class="download" @click="onClickDownload">
          <i class="fa-duotone fa-solid fa-download icon" />
          Download
        </base-button>
      </template>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import UsageRequest from '@/services/UsageRequest';
import VSelect from 'vue-select';
import VueDatePicker from '@vuepic/vue-datepicker';

export default {
  name: 'user-usage-query-builder',

  components: {
    BaseButton,
    BaseInput,
    LoadingBlocksOverlay,
    VSelect,
    VueDatePicker,
  },

  data() {
    return {
      downloading: false,
      loading: false,
      query: {
        startDate: null,
        endDate: null,
        uploadedMaxRows: null,
        uploadedMinRows: null,
      },
      type: 'user',
      typeOptions: ['user', 'workspace'],
    };
  },

  computed: {
    ...mapState('usage', ['usages']),

    disableSubmit() {
      return !this.type || !this.query.startDate || !this.query.endDate || this.query.uploadedMinRows === null;
    },
  },

  created() {
    this.reset();
  },

  methods: {
    ...mapActions('usage', ['reset']),

    async onClickDownload() {
      this.downloading = true;
      await UsageRequest.downloadUsage(this.type, this.query);
      this.downloading = false;
    },

    async onClickSubmit() {
      if (this.disableSubmit) return;

      this.loading = true;

      const formatDate = (date) => {
        if (!date) return null;
        const d = new Date(date);
        return d.toISOString().split('T')[0];
      };

      this.query.startDate = formatDate(this.query.startDate);
      this.query.endDate = formatDate(this.query.endDate);

      await UsageRequest.queryUsage(this.type, this.query);

      this.loading = false;
    },

    onInputMaxRows(e) {
      this.query.uploadedMaxRows = e.target.value;
    },

    onInputMinRows(e) {
      this.query.uploadedMinRows = e.target.value;
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.user-usage-query-builder {
  @include flex('block', 'column', 'start', 'start');

  margin-top: 1rem;
  width: 100%;

  .item {
    @include flex("block", "row", "start", "start");

    margin-bottom: 0.8rem;
    width: 100%;

    .type {
      margin-right: 0.6rem;

      .v-select {
        background-color: clr('white');
        margin-top: 0.2rem;
        width: 170px;
      }
    }

    .date {
      margin-right: 0.6rem;

      .date-picker {
        margin-top: 0.2rem;
      }
    }

    .row-upload {
      @include flex("block", "column", "start", "start");

      margin-right: 0.6rem;

      .base-input {
        margin-top: 0.2rem;
        width: 200px;
      }
    }
  }

  .icon {
    margin-right: 0.2rem;
  }

  .download {
    margin-left: auto;
  }
}

</style>
