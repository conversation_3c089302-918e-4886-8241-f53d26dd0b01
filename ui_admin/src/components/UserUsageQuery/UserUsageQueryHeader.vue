<template>
  <section class="user-usage-query-header">
    <i class="fa-regular fa-user-magnifying-glass icon" />
    <span>Usage Query</span>
    <i class="fa-regular fa-circle-xmark icon icon-close" @click="onClickClose" />
  </section>
</template>

<script>
import Route from '@/enum/route';

export default {
  name: 'user-usage-query-header',

  methods: {
    onClickClose() {
      this.$router.push({ name: Route.USER_USAGE });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.user-usage-query-header {
  @include flex("block", "row", "start", "start");

  font-size: 1.5rem;
  font-weight: $font-weight-bold;
  height: fit-content;
  width: 100%;

  .icon {
    margin-right: 0.4rem;

    &.icon-close {
      cursor: pointer;
      margin-left: auto;

      &:hover {
        opacity: 0.8;
      }
    }
  }
}
</style>
