<template>
  <section class="workspace-toast">
    <section class="toast">
      <section class="left" v-show="toastData.id === 'workspace-deleted'">
        <i class="fa-regular fa-trash icon"></i>
        <span class="text" >Workspace <b>{{label}}</b> Successfully Deleted</span>
      </section>
      <section class="left" v-show="toastData.id === 'workspace-updated'">
        <i class="fa-regular fa-save icon"></i>
        <span class="text" >Workspace <b>{{label}}</b> Successfully Updated</span>
      </section>
      <section class="left" v-show="toastData.id === 'workspace-created'">
        <i class="fa-regular fa-plus icon"></i>
        <span class="text" >Workspace <b>{{label}}</b> Successfully Created</span>
      </section>
      <section class="left" v-show="toastData.id === 'workspace-transferred'">
        <i class="fa-regular fa-plus icon"></i>
        <span class="text" >Workspace <b>{{label}}</b> Successfully Transferred</span>
      </section>
      <section class="right">
        <base-button class="dismiss" colour="light" @click="onDismiss">Dismiss</base-button>
      </section>
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import Toast from '@/components/Mixins/Toast';

export default {
  name: 'workspace-toast',

  mixins: [Toast],

  components: {
    BaseButton,
  },

  computed: {
    ...mapState('toast', ['toastData']),

    label() {
      return this.toastData.label;
    },
  },

  data() {
    return {
      count: 5,
    };
  },

  created() {
    setTimeout(this.countDown, 1000);
  },

  methods: {
    countDown() {
      this.count -= 1;

      if (this.count === 0) this.close();
      else setTimeout(this.countDown, 1000);
    },

    onDismiss() {
      this.close();
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.workspace-toast {
  @include flex("block", "row", "end", "start");

  .toast {
    @include toast;

    border-radius: $border-radius-medium;
    padding: 1rem 1.5rem;

    .left {
      @include flex("block", "row", "start", "center");

      margin-right: 4rem;

      span {
        font-weight: $font-weight-normal;
        margin-left: 0.3rem;
      }
    }

    .right {
      @include flex("block", "row", "end", "center");

      .base-button {
        padding: 0.6rem 1.6rem;
        margin-left: 1rem;
      }
    }
  }
}
</style>
