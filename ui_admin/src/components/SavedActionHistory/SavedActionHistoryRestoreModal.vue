<template>
  <section class="saved-action-history-restore-modal">
    <section class="header">
      <h2>Restore Saved Actions</h2>
    </section>

    <section class="body">
      <section class="text">
        This/these history of saved actions will be Restored.
        <span class="label">{{ label }}</span>
        . Are you sure you wish to continue?
      </section>
    </section>

    <section class="footer">
      <base-button class="cancel" colour="light" type="link" @click="closeModal">Cancel</base-button>
      <base-button class="delete" colour="danger" @click="onClickRestore">Restore</base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import savedActionHistoryRequest from '@/services/SavedActionHistoryRequest';
import SavedActionHistoryToast from '@/components/Toast/SavedActionHistoryToast';

export default {
  name: 'saved-action-history-restore-modal',

  components: {
    BaseButton,
  },

  computed: {
    ...mapState('savedActionHistory', ['selectedSavedActionsHistory']),

    label() {
      return this.selectedSavedActionsHistory.map(action => action.id);
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('toast', ['addToast']),

    async onClickRestore() {
      await savedActionHistoryRequest.restoreSavedActionHistory([...this.selectedSavedActionsHistory.map(action => action.id)]);

      await this.closeModal();
      await this.addToast({
        toastComponent: {
          component: SavedActionHistoryToast,
          id: 'saved-action-history-restored',
        },
        toastData: {
          label: this.label,
        },
      });

      await savedActionHistoryRequest.fetchSavedActionsHistory();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.saved-action-history-restore-modal {
  @include modal;

  .body {
    padding: $search-modal-padding;

    .text {
      font-size: $font-size-xs;
      margin: 0.5rem 0;

      .label {
        font-weight: $font-weight-bold;
      }
    }
  }

  .footer {
    font-size: $font-size-md;

    padding: $search-modal-padding;

    .cancel {
      font-weight: $font-weight-normal;
      padding-left: 0;
    }

    .delete {
      font-weight: $font-weight-medium;
    }
  }

  .header {
    @include flex("block", "row", "between", "center");

    padding: $search-modal-padding;

    h2 {
      @include flex("block", "row", "start", "center");

      font-weight: $font-weight-bold;
    }

    .icon {
      @include flex("block", "row", "end", "center");

      cursor: pointer;
      height: 1.25rem;
      opacity: 0.3;
      &:hover {
        opacity: 1;
      }

      &:active,
      &:focus {
        border: none;
        outline: none;
      }
    }
  }
}
</style>
