<template>
  <section class="saved-action-history-table">
    <section class="saved-action-history-toolbar">
      <section class="saved-action-history-actions">
        <base-button :size="'small'" @click="onClickRestore">
          <i class="fa-regular fa-undo icon"></i>
          Restore
        </base-button>
        <base-button colour="danger" :size="'small'" @click="onClickDelete">
          <i class="fa-regular fa-trash icon"></i>
          Delete
        </base-button>
      </section>
      <section class="saved-action-history-pagination">
        <pagination  :current-page="page" :last="last" @onBtPrevious="onBtPrevious" @onBtNext="onBtNext" @onSetPage="onSetPage"></pagination>
      </section>
    </section>
    <section class="saved-action-history-gird">
      <ag-grid-vue
          style="width: 100%; height: 100%"
          class="ag-theme-alpine"
          :columnDefs="columnDefs"
          :rowData="savedActionsHistoryMap"
          rowSelection='multiple'
          @grid-ready="onGridReady"
          @filter-changed="onFilterChanged"
      />
    </section>
  </section>
</template>

<script>
import { AgGridVue } from 'ag-grid-vue3';

import { mapActions, mapState } from 'vuex';
import BaseButton from '@/components/Base/BaseButton';
import Pagination from '@/components/Pagination/pagination';
import savedActionHistoryRequest from '@/services/SavedActionHistoryRequest';
import SavedActionHistoryCellRender from '@/components/SavedActionHistory/SavedActionHistoryCellRender';
import SavedActionHistoryDeleteModal from '@/components/SavedActionHistory/SavedActionHistoryDeleteModal';
import SavedActionHistoryRestoreModal from '@/components/SavedActionHistory/SavedActionHistoryRestoreModal';

export default {
  name: 'saved-action-history-table',

  components: {
    AgGridVue,
    BaseButton,
    Pagination,
  },

  data() {
    return {
      gridApi: null,
      defaultColDef: {
        filter: true,
      },
    };
  },

  computed: {
    ...mapState('savedActionHistory', [
      'pages',
      'rows',
      'page',
      'savedActionsHistory',
    ]),

    columnDefs() {
      return [
        {
          id: 'Id',
          field: 'id',
          width: 100,
          wrapText: true,
          autoHeight: true,
          headerCheckboxSelection: true,
          checkboxSelection: true,
        },
        {
          headerName: 'Workspace label',
          field: 'workspace',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: ['contains'],
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
        },
        {
          headerName: 'User Name',
          field: 'userName',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: ['contains'],
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
        },
        {
          headerName: 'Action Type',
          field: 'type',
          width: 150,
          wrapText: true,
          autoHeight: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: [
              {
                displayKey: 'choose_one',
                displayName: 'Choose one',
                predicate() {
                  return true;
                },
                numberOfInputs: 0,
              },
              {
                displayKey: 'CREATE_SEARCH_THEME',
                displayName: 'Search',
                predicate(_, cellValue) {
                  return null || /search/gm.test(cellValue?.toLowerCase());
                },
                numberOfInputs: 0,
              },
              {
                displayKey: 'DELETE_THEME',
                displayName: 'Delete',
                predicate(_, cellValue) {
                  return null || /delete/gm.test(cellValue?.toLowerCase());
                },
                numberOfInputs: 0,
              },
              {
                displayKey: 'MERGE_THEMES',
                displayName: 'Merge',
                predicate(_, cellValue) {
                  return null || /merge/gm.test(cellValue?.toLowerCase());
                },
                numberOfInputs: 0,
              },
              {
                displayKey: 'RENAME_THEME',
                displayName: 'Rename',
                predicate(_, cellValue) {
                  return null || /rename/gm.test(cellValue?.toLowerCase());
                },
                numberOfInputs: 0,
              },
            ],
            defaultOption: 'choose_one',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
        },
        {
          headerName: 'New Theme',
          field: 'newThemeLabel',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: ['contains'],
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
        },
        {
          headerName: 'Action Components',
          field: 'components',
          flex: 1,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Deleted By',
          field: 'deleter.loginName',
          flex: 1,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Deleted Date',
          field: 'deletedDate',
          flex: 1,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Actions',
          field: '',
          width: 200,
          wrapText: true,
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: SavedActionHistoryCellRender };
          },
        },
      ];
    },

    savedActionsHistoryMap() {
      return this.savedActionsHistory.map(o => ({
        ...o,
        components: this.parseComponents(o.type, o.components),
        newThemeLabel: o.components.newLabel,
        type: this.convertSavedActionType(o.type),
        userName: o.user.loginName,
        workspace: o.workspace.label,
      }));
    },

    last() {
      return this.savedActionsHistory.length < this.rows;
    },

    selectedSavedActionsHistory() {
      return this.gridApi.getSelectedRows();
    },
  },

  beforeMount() {
    this.doCallApiFetchSavedActionsHistory();
  },

  methods: {
    ...mapActions('savedActionHistory', [
      'setFilterName',
      'setFilterType',
      'setFilterWorkspaceLabel',
      'setPage',
      'setSelectedSavedActionsHistory',
    ]),

    ...mapActions('modal', ['setModal']),

    async doCallApiFetchSavedActionsHistory() {
      await savedActionHistoryRequest.fetchSavedActionsHistory();
    },

    onBtPrevious() {
      this.setPage({ page: this.page - 1 });
      this.doCallApiFetchSavedActionsHistory();
    },

    onBtNext() {
      this.setPage({ page: this.page + 1 });
      this.doCallApiFetchSavedActionsHistory();
    },

    onSetPage(page) {
      this.setPage({ page });
      this.doCallApiFetchSavedActionsHistory();
    },

    convertSavedActionType(type) {
      switch (type) {
        case 'MERGE_THEMES':
          return 'Merge';
        case 'CREATE_SEARCH_THEME':
          return 'Search';
        case 'RENAME_THEME':
          return 'Rename';
        case 'DELETE_THEME':
          return 'Delete';
        default:
          return type;
      }
    },

    parseComponents(type, components) {
      switch (type) {
        case 'MERGE_THEMES':
          return `${components.mergeThemesLabels}`;
        case 'CREATE_SEARCH_THEME':
          return `${components.searchQuery}`;
        case 'RENAME_THEME':
          return `${components.originalLabel}`;
        case 'DELETE_THEME':
          return '';
        default:
          return null;
      }
    },

    onGridReady(params) {
      this.gridApi = params.api;
    },

    onFilterChanged(e) {
      const model = e.api.getFilterModel();

      this.setFilterType((model.type?.type === 'choose_one' ? '' : model.type?.type) || '');
      this.setFilterName(model.user?.filter || '');
      this.setFilterWorkspaceLabel(model.workspace?.filter || '');

      this.doCallApiFetchSavedActionsHistory();
    },

    async onClickDelete() {
      if (this.gridApi.getSelectedRows().length < 1) {
        return;
      }
      await this.setSelectedSavedActionsHistory([...this.selectedSavedActionsHistory]);
      await this.setModal(SavedActionHistoryDeleteModal);
    },

    async onClickRestore() {
      if (this.gridApi.getSelectedRows().length < 1) {
        return;
      }
      await this.setSelectedSavedActionsHistory([...this.selectedSavedActionsHistory]);
      await this.setModal(SavedActionHistoryRestoreModal);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.saved-action-history-table {
  @include flex("block", "column", "start", "start");

  height: 100%;
  width: 100%;

  .saved-action-history-toolbar {
    @include flex("block", "row", "start", "start");

    width: 100%;
    margin-top: 2rem;

    .saved-action-history-pagination {
      @include flex("block", "row", "end", "start");

      padding-right: 2rem;
      width: 100%;
    }

    .saved-action-history-actions {
      @include flex("block", "row", "start", "start");
      padding-left: 1rem;
      column-gap: 1em;
      margin: 0.2em;
      min-width: max-content;

      .icon {
        margin-right: 0.2em;
      }
    }
  }

  .saved-action-history-gird {
    @include flex("block", "row", "start", "start");

    height: 100%;
    padding: 1rem 0.6rem 0 0.6rem;
    width: 100%;

    .title {
      @include flex("block", "row", "center", "start");

      font-size: 1.5rem;
      font-weight: $font-weight-bold;
    }

    .base-button {
      margin-left: auto;
    }
  }
}
</style>
