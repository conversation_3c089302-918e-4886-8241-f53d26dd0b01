<template>
  <section class="saved-action-history-cell-render">
    <section class="item">
      <base-button :size="'small'" :colour="'base'" @click="onClickRestore">
        <i class="fa-regular fa-undo icon"></i> Restore
      </base-button>
    </section>
    <section class="item">
      <base-button :size="'small'" :colour="'danger'" @click="onClickDelete">
        <i class="fa-regular fa-trash icon"></i> Delete
      </base-button>
    </section>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import SavedActionHistoryRestoreModal from '@/components/SavedActionHistory/SavedActionHistoryRestoreModal';
import SavedActionHistoryDeleteModal from '@/components/SavedActionHistory/SavedActionHistoryDeleteModal';

export default {
  name: 'saved-action-history-cell-render',

  components: {
    BaseButton,
  },

  computed: {
    savedActionHistory() {
      return this.params.data;
    },

  },

  methods: {
    ...mapActions('modal', ['setModal']),

    ...mapActions('toast', ['addToast']),

    ...mapActions('savedActionHistory', ['setSelectedSavedActionsHistory']),

    async onClickDelete() {
      await this.setSelectedSavedActionsHistory([this.savedActionHistory]);
      await this.setModal(SavedActionHistoryDeleteModal);
    },

    async onClickRestore() {
      await this.setSelectedSavedActionsHistory([this.savedActionHistory]);
      await this.setModal(SavedActionHistoryRestoreModal);
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.saved-action-history-cell-render {
  @include flex("block", "column", "start", "start");

  .item {
    @include flex("block", "row", "center", "center");

    border-radius: 4px;
    color: clr('white');
    height: 2rem;
    margin: 0.2rem;

    .base-button {
      height: 2rem;
    }

    .icon {
      margin-right: 0.2rem;
    }
  }
}
</style>
