<template>
  <section class="reporting-job-result-edit-metrics">
    <span class="title">Metric Results:</span>
    <section v-for="(metric, index) in selectedResult.metricResults" :key="index" class="metric-item">
      <h5 class="metric-title">Metric {{ index + 1 }}</h5>
      <section class="value-item">
        <span class="title">Metadata Column:</span>
        <base-input class="value" :value="metric.metadataColumn" :is-disabled="true" />
      </section>
      <section class="value-item">
        <span class="title">Calculation Type:</span>
        <base-input class="value" :value="metric.calculationType" :is-disabled="true" />
      </section>
      <section class="value-item">
        <span class="title">Name:</span>
        <base-input class="value" :value="metric.name" @input="onSetMetricField(index, 'name', $event)" placeholder="Name" />
      </section>
      <section class="value-item">
        <span class="title">Value:</span>
        <base-input class="value" :value="metric.value" @input="onSetMetricField(index, 'value', $event)" placeholder="Value" />
      </section>
      <section class="value-item">
        <span class="title">Value Change:</span>
        <base-input class="value" :value="metric.valueChange" @input="onSetMetricField(index, 'valueChange', $event)" placeholder="Value Change" />
      </section>
      <section class="value-item">
        <span class="title">Target:</span>
        <base-input class="value" :value="metric.target" @input="onSetMetricField(index, 'target', $event)" placeholder="Target" />
      </section>
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import BaseInput from '@/components/Base/BaseInput';

export default {
  name: 'reporting-job-result-edit-metrics',

  components: {
    BaseInput,
  },

  data() {
    return {
      updating: false,
    };
  },

  computed: {
    ...mapState('reportingJob', ['selectedResult']),
  },

  methods: {
    onSetMetricField(index, field, e) {
      this.selectedResult.metricResults[index][field] = e.target.value;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-result-edit-metrics {
  @include flex("block", "column", "start", "start");

  width: 100%;

  .metric-item {
    @include flex("block", "column", "start", "start");

    border-radius: 4px;
    border: $border-purple;
    margin-bottom: 0.5rem;
    padding: 0.5rem;

    .value-item {
      @include flex("block", "row", "start", "center");

      margin-bottom: 0.2rem;

      .title {
        width: 200px;
      }

      .value {
        width: 500px;
      }
    }
  }
}
</style>
