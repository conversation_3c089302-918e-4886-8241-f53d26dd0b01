<template>
  <section class="reporting-job-result-edit-header">
    <i class="fa-solid fa-file-contract icon" />
    <span>Edit result of {{selectedReportingJob.name}}</span>
    <i class="fa-regular fa-circle-xmark icon icon-close" @click="onClickClose" />
  </section>
</template>

<script>
import { mapState } from 'vuex';

import Route from '@/enum/route';

export default {
  name: 'reporting-job-result-edit-header',

  methods: {
    onClickClose() {
      this.$router.push({
        name: Route.REPORTING_JOB_RESULT,
        query: {
          id: this.selectedReportingJob.id,
        },
      });
    },
  },

  computed: {
    ...mapState('reportingJob', ['selectedReportingJob']),
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-result-edit-header {
  @include flex("block", "row", "start", "start");

  font-size: 1.5rem;
  font-weight: $font-weight-bold;
  height: fit-content;
  width: 100%;

  .icon {
    margin-right: 0.4rem;

    &.icon-close {
      cursor: pointer;
      margin-left: auto;

      &:hover {
        opacity: 0.8;
      }
    }
  }
}
</style>
