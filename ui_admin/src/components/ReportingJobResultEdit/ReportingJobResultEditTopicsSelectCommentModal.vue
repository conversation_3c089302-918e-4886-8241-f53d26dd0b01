<template>
  <section class="reporting-job-result-edit-topics-select-comment-modal">
    <section class="header">
      <h2>Select comment for {{topic.name}}</h2>
    </section>

    <section class="body">
      <loading-blocks-overlay v-if="loading" />
      <span v-for="(comment, index) in comments" class="comment" :key="index" @click="onSelectComment(comment)">{{comment.content}}</span>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import CommentRequest from '@/services/CommentRequest';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';

export default {
  name: 'reporting-job-result-edit-topics-select-comment-modal',

  components: {
    BaseButton,
    LoadingBlocksOverlay,
  },

  props: {
    componentProps: {
      topic: Number,
    },
  },

  data() {
    return {
      comments: [],
      loading: true,
    };
  },

  computed: {
    ...mapState('reportingJob', ['selectedResult']),

    topic() {
      return this.componentProps.topic;
    },
  },

  async created() {
    this.loading = true;
    this.comments = await CommentRequest.fetchComments(this.selectedResult.datasetId, this.topic.topicId);
    this.loading = false;
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('toast', ['addToast']),

    onSelectComment(comment) {
      this.topic.representationComment = comment.content;
      this.closeModal();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-result-edit-topics-select-comment-modal {
  @include modal;

  .header {
    @include flex("block", "row", "between", "center");

    padding: $search-modal-padding;

    h2 {
      @include flex("block", "row", "start", "center");

      font-weight: $font-weight-bold;
    }
  }

  .body {
    @include flex("block", "column", "start", "start");

    padding: 0;
    width: 100%;

    .comment {
      border-bottom: 1px solid #ddd;
      cursor: pointer;
      font-size: $font-size-xs;
      padding: 1rem;
      width: 100%;

      &:hover {
        background-color: darken(clr("blue", "lighter"), 5%);
      }
    }
  }
}
</style>
