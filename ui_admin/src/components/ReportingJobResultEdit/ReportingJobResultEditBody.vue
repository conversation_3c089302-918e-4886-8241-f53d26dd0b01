<template>
  <section class="reporting-job-result-edit-body">
    <section class="item">
      <span class="title">Dataset Original Name:</span>
      <base-input class="value" :value="selectedResult.datasetOriginalName" @input="onSetDatasetOriginalName" />
    </section>
    <section class="item">
      <span class="title">Volume:</span>
      <base-input class="value" :value="selectedResult.volume" @input="onSetVolume" />
    </section>
    <section class="item">
      <span class="title">Volume Change:</span>
      <base-input class="value" :value="selectedResult.volumeChange" @input="onSetVolumeChange" />
    </section>
    <section class="item">
      <span class="title">Adorescore:</span>
      <base-input class="value" :value="selectedResult.adorescore" @input="onSetAdorescore" />
    </section>
    <section class="item">
      <span class="title">Adorescore Change:</span>
      <base-input class="value" :value="selectedResult.adorescoreChange" @input="onSetAdorescoreChange" />
    </section>
    <section class="item">
      <base-button v-if="!updating" class="update" size="small" @click="onClickUpdate">Update</base-button>
      <reporting-job-result-preview-email-button :reporting-job-result="selectedResult" />
      <reporting-job-result-send-result-button :reporting-job-result="selectedResult" />
    </section>
    <reporting-job-result-edit-metrics />
    <reporting-job-result-edit-topics />

    <section class="footer">
      <loading-blocks-overlay v-if="updating" />
      <base-button v-if="!updating" class="btn" colour="danger" size="small" @click="onClickDiscard">Discard changes</base-button>
      <base-button v-if="!updating" class="update" size="small" @click="onClickUpdate">Update</base-button>
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import ReportingJobRequest from '@/services/ReportingJobRequest';
import ReportingJobResultEditMetrics from '@/components/ReportingJobResultEdit/ReportingJobResultEditMetrics';
import ReportingJobResultEditTopics from '@/components/ReportingJobResultEdit/ReportingJobResultEditTopics';
import ReportingJobResultPreviewEmailButton from '@/components/ReportingJobResult/ReportingJobResultPreviewEmailButton';
import ReportingJobResultSendResultButton from '@/components/ReportingJobResult/ReportingJobResultSendResultButton';

export default {
  name: 'reporting-job-result-edit-body',

  components: {
    BaseButton,
    BaseInput,
    LoadingBlocksOverlay,
    ReportingJobResultEditMetrics,
    ReportingJobResultEditTopics,
    ReportingJobResultPreviewEmailButton,
    ReportingJobResultSendResultButton,
  },

  data() {
    return {
      updating: false,
    };
  },

  computed: {
    ...mapState('reportingJob', [
      'selectedId',
      'selectedResult',
      'selectedResultId',
    ]),
  },

  methods: {
    onSetDatasetOriginalName(e) {
      this.selectedResult.datasetOriginalName = e.target.value;
    },

    onSetVolume(e) {
      this.selectedResult.volume = e.target.value;
    },

    onSetVolumeChange(e) {
      this.selectedResult.volumeChange = e.target.value;
    },

    onSetAdorescore(e) {
      this.selectedResult.adorescore = e.target.value;
    },

    onSetAdorescoreChange(e) {
      this.selectedResult.adorescoreChange = e.target.value;
    },

    async onClickDiscard() {
      this.updating = true;
      await ReportingJobRequest.getResult(this.selectedId, this.selectedResultId);
      this.updating = false;
    },

    async onClickUpdate() {
      this.updating = true;
      await ReportingJobRequest.updateResult(this.selectedId, this.selectedResultId, this.selectedResult);
      await ReportingJobRequest.getResult(this.selectedId, this.selectedResultId);
      this.updating = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-result-edit-body {
  @include flex("block", "column", "start", "start");

  height: 100%;
  margin-top: 0.5rem;
  width: 100%;

  .item {
    @include flex("block", "row", "start", "center");

    margin-bottom: 0.5rem;

    .title {
      width: 200px;
    }

    .value {
      width: 500px;
    }
  }

  .base-button {
    margin-right: 0.5rem;
  }
}
</style>
