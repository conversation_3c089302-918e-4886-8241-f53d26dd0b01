<template>
  <section class="reporting-job-result-edit-topics">
    <span class="title">Topic Results ({{ selectedResult.topicResults.length }} topics found, {{ selectedResult.filteredTopicNames.length }} topics filtered):</span>
    <section class="is-filtered" @click="isFilter = !isFilter">
      <base-checkbox :value="isFilter" />
      <span class="filter-info">
        Show Filtered Topic Only
      </span>
    </section>
    <section class="topic-item">
      <section class="value-item">
        <span class="title">Filtered Topic Names:</span>
        <base-input class="value" :value="selectedResult.filteredTopicNames" @input="onSetFilteredTopicNames($event)" placeholder="Name" />
      </section>
    </section>
    <section v-for="(topic, index) in filteredTopics" :key="index" class="topic-item">
      <h5 class="topic-title">Topic {{ index + 1 }}</h5>
      <section class="value-item">
        <span class="title">Topic Id:</span>
        <base-input class="value" :value="topic.topicId" :is-disabled="true" />
      </section>
      <section class="value-item">
        <span class="title">Name:</span>
        <base-input class="value" :value="topic.name" @input="onSetTopicField(index, 'name', $event)" placeholder="Name" />
      </section>
      <section class="value-item">
        <span class="title">Volume:</span>
        <base-input class="value" :value="topic.volume" @input="onSetTopicField(index, 'volume', $event)" placeholder="Volume" />
      </section>
      <section class="value-item">
        <span class="title">Volume Percentage:</span>
        <base-input class="value" :value="topic.volumePct" @input="onSetTopicField(index, 'volumePct', $event)" placeholder="Volume Percentage" />
      </section>
      <section class="value-item">
        <span class="title">Volume Percentage Change:</span>
        <base-input class="value" :value="topic.volumePctChange" @input="onSetTopicField(index, 'volumePctChange', $event)" placeholder="Volume Percentage Change" />
      </section>
      <section class="value-item">
        <span class="title">Adorescore:</span>
        <base-input class="value" :value="topic.adorescore" @input="onSetTopicField(index, 'adorescore', $event)" placeholder="Adorescore" />
      </section>
      <section class="value-item">
        <span class="title">Adorescore Change:</span>
        <base-input class="value" :value="topic.adorescoreChange" @input="onSetTopicField(index, 'adorescoreChange', $event)" placeholder="Adorescore Change" />
      </section>
      <section class="value-item">
        <span class="title">Summary:</span>
        <base-input class="value" :value="topic.summary" @input="onSetTopicField(index, 'summary', $event)" placeholder="Summary" />
      </section>
      <section class="value-item">
        <span class="title">Representation Comment:</span>
        <textarea class="value comment" rows="4" v-model="topic.representationComment" />
        <base-button size="small" @click="onClickSelectComment(topic)">Select Comment</base-button>
      </section>
      <section class="value-item">
        <span class="title">Custom:</span>
        <base-input class="value" :value="topic.custom" :is-disabled="true" />
      </section>
      <section class="value-item">
        <span class="title">Tracked:</span>
        <base-input class="value" :value="topic.tracked" :is-disabled="true" />
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseCheckbox from '@/components/Base/BaseCheckbox';
import BaseInput from '@/components/Base/BaseInput';
import ReportingJobResultEditTopicsSelectCommentModal from '@/components/ReportingJobResultEdit/ReportingJobResultEditTopicsSelectCommentModal';

export default {
  name: 'reporting-job-result-edit-topics',

  components: {
    BaseButton,
    BaseCheckbox,
    BaseInput,
  },

  data() {
    return {
      isFilter: false,
      updating: false,
    };
  },

  computed: {
    ...mapState('reportingJob', ['selectedResult']),

    filteredTopics() {
      return this.isFilter ? this.selectedResult.topicResults.filter(topic => this.selectedResult.filteredTopicNames.includes(topic.name)) : this.selectedResult.topicResults;
    },
  },

  methods: {
    ...mapActions('modal', ['setModalAndProps']),

    onClickSelectComment(topic) {
      this.setModalAndProps({
        component: ReportingJobResultEditTopicsSelectCommentModal,
        props: {
          topic,
        },
      });
    },

    onSetTopicField(index, field, e) {
      this.selectedResult.topicResults[index][field] = e.target.value;
    },

    onSetFilteredTopicNames(e) {
      this.selectedResult.filteredTopicNames = e.target.value.split(',').map(name => name.trim());
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-result-edit-topics {
  @include flex("block", "column", "start", "start");

  width: 100%;

  .is-filtered {
    cursor: pointer;
  }

  .topic-item {
    @include flex("block", "column", "start", "start");

    border-radius: 4px;
    border: $border-purple;
    margin-bottom: 0.5rem;
    padding: 0.5rem;

    .value-item {
      @include flex("block", "row", "start", "center");

      margin-bottom: 0.2rem;

      .title {
        width: 200px;
      }

      .value {
        width: 500px;

        &.comment {
          font-style: italic;
        }
      }
    }
  }
}
</style>
