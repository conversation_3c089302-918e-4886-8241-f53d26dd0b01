<template>
  <section class="reporting-job-execution-status-table">
    <loading-blocks-overlay v-if="loading" />
    <section v-else v-for="item in executionStatuses" class="execution-status-item">
      <span>Execution Number: {{item.executionNumber}}</span>
      <ag-grid-vue
        style="width: 100%; height: 350px"
        class="ag-theme-alpine"
        :columnDefs="columnDefs"
        :rowData="item.statuses"
      />
    </section>
  </section>
</template>

<script>
import { AgGridVue } from 'ag-grid-vue3';
import { mapState } from 'vuex';

import DateTimeRenderer from '@/components/BaseAgGrid/DateTimeRenderer';
import GridListCell from '@/components/Base/GridListCell';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import Pagination from '@/components/Pagination/pagination';
import ReportingJobRequest from '@/services/ReportingJobRequest';

export default {
  name: 'reporting-job-execution-status-table',

  components: {
    AgGridVue,
    GridList<PERSON>ell,
    LoadingBlocksOverlay,
    Pagination,
  },

  data() {
    return {
      executionStatuses: [],
      loading: true,
    };
  },

  computed: {
    ...mapState('reportingJob', ['selectedId']),

    columnDefs() {
      return [
        {
          headerName: 'Step',
          field: 'step',
          width: 110,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Created At',
          field: 'createdAt',
          wrapText: true,
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: DateTimeRenderer };
          },
        },
        {
          headerName: 'Finished At',
          field: 'finishedAt',
          wrapText: true,
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: DateTimeRenderer };
          },
        },
        {
          headerName: 'Status',
          field: 'status',
          width: 110,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Error Message',
          field: 'errorMessage',
          flex: 1,
          wrapText: true,
          autoHeight: true,
        },
      ];
    },
  },

  async created() {
    this.loading = true;
    this.executionStatuses = await ReportingJobRequest.getExecutionStatuses(this.selectedId);
    this.loading = false;
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-execution-status-table {
  @include flex("block", "column", "start", "start");

  height: 100%;
  margin-top: 0.6rem;
  width: 100%;

  .execution-status-item {
    height: 100%;
    margin-top: 0.6rem;
    width: 100%;
  }
}
</style>
