<template>
  <section class="nav-bar">
    <img alt="logo" class="logo" :src="logoAdoreboard" />
    <section class="navigator">
      <template v-for="(tab, index) in tabs" :key="index">
        <nav-bar-tab
          v-if="!tab.children"
          :route="tab.route"
          :icon="tab.fontAwesomeIcon"
        >{{ tab.text }}</nav-bar-tab>

        <section v-else class="nav-group">
          <nav-bar-tab
            :icon="tab.fontAwesomeIcon"
            @click="tab.expanded = !tab.expanded"
          >
            {{ tab.text }}
            <i :class="tab.expanded ? 'fa-solid fa-chevron-down icon' : 'fa-solid fa-chevron-right icon'" />
          </nav-bar-tab>
          <section v-if="tab.expanded" class="sub-tabs">
            <nav-bar-tab
              v-for="subTab in tab.children"
              :key="subTab.route.path"
              :route="subTab.route"
              :icon="subTab.fontAwesomeIcon"
            >{{ subTab.text }}</nav-bar-tab>
          </section>
        </section>
      </template>
    </section>
  </section>
</template>

<script>
import logoAdoreboard from '@/assets/logo/logo-adoreboard-white.svg';
import NavBarTab from '@/components/NavBar/NavBarTab';
import Route from '@/enum/route';

export default {
  name: 'nav-bar',

  components: {
    NavBarTab,
  },

  data() {
    return {
      logoAdoreboard,
      tabs: [
        {
          text: 'User Management',
          fontAwesomeIcon: 'fa-solid fa-user',
          expanded: true,
          children: [
            {
              text: 'Users',
              route: Route.USER,
              fontAwesomeIcon: 'fa-solid fa-users',
            },
            {
              text: 'User Usage',
              route: Route.USER_USAGE,
              fontAwesomeIcon: 'fa-solid fa-chart-pie',
            },
            {
              text: 'User Tiers',
              route: Route.USER_TIER,
              fontAwesomeIcon: 'fa-solid fa-layer-group',
            },
            {
              text: 'Organisations',
              route: Route.ORGANISATION,
              fontAwesomeIcon: 'fa-solid fa-briefcase',
            },
            {
              text: 'Workspaces',
              route: Route.WORKSPACE,
              fontAwesomeIcon: 'fa-solid fa-sitemap',
            },
            {
              text: 'Registrations',
              route: Route.REGISTRATION,
              fontAwesomeIcon: 'fa-solid fa-registered',
            },
          ],
        },
        {
          text: 'Dataset Management',
          fontAwesomeIcon: 'fa-solid fa-server',
          expanded: false,
          children: [
            {
              text: 'Datasets',
              route: Route.DATASET,
              fontAwesomeIcon: 'fa-solid fa-database',
            }, {
              text: 'Dataset Statuses',
              route: Route.DATASET_STATUS,
              fontAwesomeIcon: 'fa-solid fa-microchip',
            },
          ],
        },
        {
          text: 'Save Action Management',
          fontAwesomeIcon: 'fa-solid fa-save',
          expanded: false,
          children: [
            {
              text: 'Saved Action Lists',
              route: Route.SAVED_ACTION_LIST,
              fontAwesomeIcon: 'fa-solid fa-album-collection',
            },
            {
              text: 'Saved Action',
              route: Route.SAVED_ACTION,
              fontAwesomeIcon: 'fa-solid fa-save',
            },
            {
              text: 'Saved Actions History',
              route: Route.SAVED_ACTION_HISTORY,
              fontAwesomeIcon: 'fa-solid fa-trash-undo',
            },
          ],
        },
        {
          text: 'Reporting Jobs',
          route: Route.REPORTING_JOB,
          fontAwesomeIcon: 'fa-solid fa-file-contract',
        },
        {
          text: 'Batches',
          route: Route.BATCH,
          fontAwesomeIcon: 'fa-solid fa-code-simple',
        },
        {
          text: 'Heartbeats',
          route: Route.HEARTBEAT,
          fontAwesomeIcon: 'fa-solid fa-heart',
        },
      ],
    };
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.nav-bar {
  @include flex("block", "column", "start", "center");
  @include rigid;

  background: #131C29;
  color: #898EA5;
  height: 100%;
  padding: 0 0.5rem;
  width: 16rem;
  z-index: 1;

  .logo {
    width: 100%;
    height: 50px;
  }

  .navigator {
    @include flex("block", "column", "start", "start");

    width: 100%;

    .nav-group {
      .icon {
        margin-left: 0.4rem;
      }

      .sub-tabs {
        border-left: 2px solid rgba(255, 255, 255, 0.2);
        margin-bottom: 0.4rem;
        margin-left: 1rem;
        padding-left: 0.5rem;
      }
    }
  }
}
</style>
