<template>
  <section class="nav-bar-tab" :class="{ active }" @click.stop="onClick">
    <i :class="icon" class="icon"></i>
    <section class="text">
      <slot></slot>
    </section>
  </section>
</template>

<script>
export default {
  name: 'nav-bar-tab',

  props: {
    icon: {
      type: String,
      required: false,
    },
    route: {
      type: Object,
      required: false,
    },
  },

  computed: {
    active() {
      return this.$route.name.name === this.route?.name;
    },
  },

  methods: {
    onClick() {
      if (this.route) this.$router.push({ path: this.route.url() });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.nav-bar-tab {
  @include flex("block", "row", "start", "center");
  @include shrink;

  border-radius: 4px;
  color: clr('white');
  cursor: pointer;
  font-size: $font-size-sm;
  font-weight: $font-weight-normal;
  letter-spacing: 0.5px;
  padding: 0.4rem;
  position: relative;
  text-align:center;
  width: 100%;

  .icon {
    margin-right: 0.4rem;
  }

  .text {
    @include flex("block", "row", "start", "center");
  }

  &:hover,
  &:focus {
    &:not(.active) {
      background-color: #7184B0;
    }
  }

  &.active {
    background-color: #7184B0;
  }
}
</style>
