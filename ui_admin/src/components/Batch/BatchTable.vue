<template>
  <section class="batch-table">
    <ag-grid-vue
        style="width: 100%; height: 100%"
        class="ag-theme-alpine"
        :columnDefs="columnDefs"
        :rowData="batches"
        rowSelection='multiple'
    />
  </section>
</template>

<script>
import { AgGridVue } from 'ag-grid-vue3';
import { mapState } from 'vuex';

import BatchCellRender from '@/components/Batch/BatchCellRender';

export default {
  name: 'batch-table',

  components: {
    AgGridVue,
    BatchCellRender,
  },

  data() {
    return {
      gridApi: null,
    };
  },

  computed: {
    ...mapState('batch', ['batches']),

    ...mapState('userTier', ['userTiers']),

    columnDefs() {
      return [
        {
          id: 'Id',
          field: 'jobExecutionId',
          width: 100,
          wrapText: true,
          autoHeight: true,
          headerCheckboxSelection: true,
          checkboxSelection: true,
        },
        {
          headerName: 'Job Name',
          field: 'jobName',
          flex: 1,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Status',
          field: 'user',
          flex: 1,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Last Update',
          field: 'lastUpdated',
          flex: 1,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Error Message',
          field: 'exitMessage',
          flex: 1,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Actions',
          field: '',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: BatchCellRender };
          },
        },

      ];
    },
  },

  methods: {

  },

};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.batch-table {
  @include flex("block", "row", "start", "start");

  height: 100%;
  padding: 1rem 0.6rem 0 0.6rem;
  width: 100%;

  .title {
    @include flex("block", "row", "center", "start");

    font-size: 1.5rem;
    font-weight: $font-weight-bold;
  }

  .base-button {
    margin-left: auto;
  }
}
</style>
