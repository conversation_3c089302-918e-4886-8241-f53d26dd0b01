<template>
  <section class="batch-delete-modal">
    <section class="header">
      <h2>Delete All Completed Batch</h2>
    </section>

    <section class="body">
      <section class="text">
        All Completed Batch will be delete.
        Are you sure you wish to continue?
      </section>
    </section>

    <section class="footer">
      <base-button class="cancel" colour="light" type="link" @click="closeModal">Cancel</base-button>
      <base-button class="delete" colour="danger" @click="onClickDelete">Delete Batch</base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BatchToast from '@/components/Toast/BatchToast';
import batchRequest from '@/services/BatchRequest';

export default {
  name: 'batch-delete-modal',

  components: {
    BaseButton,
  },

  computed: {
    ...mapState('batch', ['selectedBatch']),

    jobExecutionId() {
      return this.selectedBatch.jobExecutionId;
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('toast', ['addToast']),

    async onClickDelete() {
      await batchRequest.deleteAllBatches();
      await batchRequest.fetchBatches();
      this.closeModal();
      this.addToast({
        toastComponent: {
          component: BatchToast,
          id: 'completed-batch-deleted',
        },
        toastData: {
          // label: this.selectedBatch.jobExecutionId,
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.batch-delete-modal {
  @include modal;

  .body {
    padding: $search-modal-padding;

    .text {
      font-size: $font-size-xs;
      margin: 0.5rem 0;

      .label {
        font-weight: $font-weight-bold;
      }
    }
  }

  .footer {
    font-size: $font-size-md;

    padding: $search-modal-padding;

    .cancel {
      font-weight: $font-weight-normal;
      padding-left: 0;
    }

    .delete {
      font-weight: $font-weight-medium;
    }
  }

  .header {
    @include flex("block", "row", "between", "center");

    padding: $search-modal-padding;

    h2 {
      @include flex("block", "row", "start", "center");

      font-weight: $font-weight-bold;
    }

    .icon {
      @include flex("block", "row", "end", "center");

      cursor: pointer;
      height: 1.25rem;
      opacity: 0.3;
      &:hover {
        opacity: 1;
      }

      &:active,
      &:focus {
        border: none;
        outline: none;
      }
    }
  }
}
</style>
