<template>
  <section class="batch-cell-render">
    <section class="item">
      <base-button :size="'small'" :colour="'dark'" @click="onClickDetail">
        <i class="fa-regular fa-save icon"></i>
      </base-button>
    </section>
    <section class="item">
      <base-button :size="'small'" :colour="'danger'" @click="onClickDelete">
        <i class="fa-regular fa-trash icon"></i>
      </base-button>
    </section>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BatchDetailsModal from '@/components/Batch/BatchDetailsModal';
import BatchDeleteModal from '@/components/Batch/BatchDeleteModal';

export default {
  name: 'batch-cell-render',

  components: {
    BaseButton,
    BatchDetailsModal,
  },

  computed: {
    batch() {
      return this.params.data;
    },

  },

  methods: {
    ...mapActions('modal', ['setModal']),
    ...mapActions('batch', ['setSelectedBatch']),

    onClickDetail() {
      this.setSelectedBatch({ selectedBatch: this.batch });
      this.setModal(BatchDetailsModal);
    },

    async onClickDelete() {
      this.setSelectedBatch({ selectedBatch: this.batch });
      await this.setModal(BatchDeleteModal);
    },

  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";
.batch-cell-render {
  @include flex("block", "column", "start", "start");

  .item {
    @include flex("block", "row", "center", "center");

    border-radius: 4px;
    color: clr('white');
    height: 2rem;
    margin: 0.2rem;

    .base-button {
      height: 2rem;
    }
  }
}
</style>
