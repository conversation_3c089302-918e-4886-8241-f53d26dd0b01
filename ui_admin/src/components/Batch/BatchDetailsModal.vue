<template>
  <section class="batch-details-modal">
    <pre>{{jobParams}}</pre>
  </section>
</template>

<script>
import { mapState } from 'vuex';
import batchRequest from '@/services/BatchRequest';

export default {
  name: 'batch-details-modal',

  beforeMount() {
    this.doCallApiFetchJobParams();
  },

  computed: {
    ...mapState('batch', ['selectedBatch']),

    jobParams() {
      return this.selectedBatch?.params;
    },
  },

  methods: {
    async doCallApiFetchJobParams() {
      await batchRequest.getJobParams(this.selectedBatch);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.batch-details-modal {
  @include modal;

  position: relative;
}
</style>
