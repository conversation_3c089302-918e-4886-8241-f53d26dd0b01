<template>
  <section class="batch-header">
    <section class="title">
      <i class="fa-solid fa-sitemap icon"></i>
      <span>Batches</span>
    </section>
    <base-button v-if="batches.length > 0" :size="'small'" :colour="'danger'" @click="onClickDelete">
      <i class="fa-regular fa-trash icon"></i>
      Clear Completed Batch
    </base-button>
  </section>
</template>

<script>
import BaseButton from '@/components/Base/BaseButton';
import CompletedBatchDeleteModal from '@/components/Batch/CompletedBatchDeleteModal';
import { mapActions, mapState } from 'vuex';

export default {
  name: 'batch-header',

  components: {
    BaseButton,
  },

  computed: {
    ...mapState('batch', ['batches']),

  },

  methods: {
    ...mapActions('modal', ['setModal']),

    onClickDelete() {
      this.setModal(CompletedBatchDeleteModal);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.batch-header {
  @include flex("block", "row", "start", "start");

  height: fit-content;
  padding: 1rem 0.6rem 0 0.6rem;
  width: 100%;

  .title {
    @include flex("block", "row", "center", "start");

    font-size: 1.5rem;
    font-weight: $font-weight-bold;
  }

  .base-button {
    margin-left: auto;
  }

  .icon {
    margin-right: 0.4rem;
  }
}
</style>
