<template>
  <section class="workspace-details-modal">
    <section class="header">
      <h2>{{label}}</h2>
      <span class="note">Only able to transfer the user existing in less than 1 workspace</span>
    </section>
    <section class="body">
      <section class="row field">
      <span>User</span>
      <section class="wrapper-select">
        <v-select v-model="localUser"
                  class="v-select"
                  :options="userOptions"
                  :searchable="true"
                  :selectable="isOptionDisabled"
                  :is-disabled="isOptionDisabled"
                  @search="fetchUsers"/>
      </section>
    </section>
      <section class="row field">
        <span>Workspace label</span>
        <section class="wrapper-select">
          <v-select v-model="localWorkspace"
                    class="v-select"
                    :options="workspaceOptions"
                    :searchable="true"
                    :is-disabled="isOptionDisabled"
                    :selectable="isOptionDisabled"
                    @search="fetchWorkspace"
          />
        </section>
      </section>
      <section class="row field">
        <span>Add as role</span>
        <section class="wrapper-select">
          <v-select v-model="localRole"
                    class="v-select"
                    :options="['ADMIN', 'EDITOR', 'VIEWER']"
          />
        </section>
      </section>

    </section>
    <section class="footer">
      <base-button class="cancel" colour="light" size="small" type="link"  @click="onCancel">Cancel</base-button>
      <loading-blocks-overlay v-if="processing" />
      <section class="confirm" v-else>
        <base-button colour="base" size="small" :disabled="createDisabled" @click="onTransfer">
          <span>Transfer User</span>
        </base-button>
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import BaseRadio from '@/components/Base/BaseRadio';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import VSelect from 'vue-select';
import WorkspaceRole from '@/enum/workspace-role';
import WorkspaceToast from '@/components/Toast/WorkspaceToast';
import userRequest from '@/services/UserRequest';
import workspaceRequest from '@/services/WorkspaceRequest';
import { debounce } from 'lodash-es';

export default {
  name: 'workspace-details-modal',

  components: {
    BaseButton,
    BaseRadio,
    BaseInput,
    LoadingBlocksOverlay,
    VSelect,
  },

  computed: {
    ...mapState('workspace', ['workspaces']),

    createDisabled() {
      return !this.localRole || !this.localWorkspace || !this.localUser;
    },

    label() {
      return 'Transfer Workspace';
    },

    isAdmin() {
      return this.localRole.name === WorkspaceRole.ADMIN.name;
    },

    isEditor() {
      return this.localRole.name === WorkspaceRole.EDITOR.name;
    },

    isViewer() {
      return this.localRole.name === WorkspaceRole.VIEWER.name;
    },
  },

  data() {
    return {
      WorkspaceRole,
      localRole: WorkspaceRole.ADMIN,
      localUser: null,
      localWorkspace: null,
      processing: false,
      userOptions: [],
      workspaceOptions: [],
      selectedWorkspaces: [],
    };
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('toast', ['addToast']),

    ...mapActions('workspace', ['setSelectedWorkspace']),

    ...mapActions('workspace', { setWorkspaceSearch: 'setSearch' }),

    ...mapActions('user', ['setSearch']),

    async doCallApiFetchUsers() {
      this.userOptions = (await userRequest.getUsers()).map(u => ({
        id: u.id,
        label: `${u.loginName} (Id: ${u.id} )`,
        selectable: u.workspaceIds?.length <= 1,
        organisationId: u.organisationId,
        workspaceIds: u.workspaceIds,
      }));
    },

    async doCallApiFetchWorkspace() {
      await workspaceRequest.fetchWorkspaces();
      this.workspaceOptions = this.workspaces.filter(w => {
        return w.organisationId === this.localUser?.organisationId;
      }).map(w => ({
        ...w,
        selectable: !this.localUser?.workspaceIds.includes(w.id),
      }));
    },

    fetchUsers: debounce(async function fetchUsers(search, loading) {
      this.setSearch(search);
      await this.doCallApiFetchUsers();
      await this.doCallApiFetchWorkspace();
      loading(false);
    }, 500),

    fetchWorkspace: debounce(async function fetchWorkspace(search, loading) {
      await this.setWorkspaceSearch(search);
      await this.doCallApiFetchWorkspace();
      loading(false);
    }, 500),

    async onTransfer() {
      if (this.createDisabled) {
        return;
      }
      await workspaceRequest.transferWorkspace(this.localUser.id, this.localWorkspace.id, this.localRole).then((workspace) => {
        if (workspace) {
          workspaceRequest.fetchWorkspaces();
          this.closeModal();
          this.addToast({
            toastComponent: {
              component: WorkspaceToast,
              id: 'workspace-transferred',
            },
            toastData: {
              label: this.localWorkspace.label,
            },
          });
        }
      });
    },

    onCancel() {
      this.closeModal();
    },

    isOptionDisabled(option) {
      return option.selectable;
    },

  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.workspace-details-modal {
  @include modal;

  position: relative;

  .header .note {
    color: red;
  }

  .field {
    margin-bottom: 0.6rem;
  }

  .row {
    display: grid;
    grid-template-columns: 40% 60%
  }

  .wrapper-select {
    height: 30px;
  }

  .v-select {
    background-color: clr('white');
    width: 400px;
  }

}
</style>
