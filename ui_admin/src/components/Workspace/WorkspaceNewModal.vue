<template>
  <section class="workspace-details-modal">
    <section class="header">
      <h2>Create New Workspace</h2>
    </section>
    <section class="body">
      <section class="field row">
        <span>Organisation</span>
      <section class="wrapper-select">
        <v-select v-model="localOrganisation"
                  class="v-select"
                  :options="organisationsMap"
        />
      </section>
      </section>
      <section class="field row">
        <span>Workspace label</span>
        <base-input placeholder="Enter workspace label" :value="localWorkspace.label" @input="onInputName"/>
      </section>
      <section class="field row">
        <span>User limit</span>
        <base-input placeholder="Set the workspace user limit" :type="'number'" :value="localWorkspace.userLimit" @input="onInputUserCount"/>
      </section>
      <section class="field row owner">
        <span>Owner</span>
        <section class="wrapper-select">
          <v-select v-model="localOwner"
                    class="v-select"
                    :options="userOptions"
                    :searchable="true"
                    :selectable="isOptionSelectable"
                    :is-disabled="isOptionSelectable"
                    @search="fetchUsers"/>
        </section>
      </section>
      <section class="field row administrators">
        <span>Administrators</span>
        <section class="wrapper-select">
          <v-select v-model="localAdministrators"
                    class="v-select"
                    multiple
                    :options="userOptions"
                    :searchable="true"
                    :selectable="isOptionSelectable"
                    :is-disabled="isOptionSelectable"
                    @search="fetchUsers"/>
        </section>
      </section>
      <section class="field row editors">
        <span>Editors</span>
        <section class="wrapper-select">
          <v-select v-model="localEditors"
                    class="v-select"
                    multiple
                    :options="userOptions"
                    :searchable="true"
                    :selectable="isOptionSelectable"
                    :is-disabled="isOptionSelectable"
                    @search="fetchUsers"/>
        </section>
      </section>
      <section class="field row viewers">
        <span>Viewers</span>
        <section class="wrapper-select">
          <v-select v-model="localViewers"
                    class="v-select"
                    multiple
                    :options="userOptions"
                    :searchable="true"
                    :selectable="isOptionSelectable"
                    :is-disabled="isOptionSelectable"
                    @search="fetchUsers"/>
        </section>
      </section>
    </section>
    <section class="footer">
      <base-button class="cancel" colour="light" size="small" type="link"  @click="onCancel">Cancel</base-button>
      <loading-blocks-overlay v-if="creating" />
      <section class="confirm" v-else>
        <base-button colour="base" size="small" :disabled="createDisabled" @click="onCreate">
          <span>Create Workspace</span>
        </base-button>
      </section>

    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import BaseCheckbox from '@/components/Base/BaseCheckbox';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import VSelect from 'vue-select';
import WorkspaceToast from '@/components/Toast/WorkspaceToast';
import workspaceRequest from '@/services/WorkspaceRequest';
import organisationRequest from '@/services/OrganisationRequest';
import { debounce } from 'lodash-es';
import userRequest from '@/services/UserRequest';

export default {
  name: 'workspace-details-modal',

  components: {
    BaseButton,
    BaseCheckbox,
    BaseInput,
    LoadingBlocksOverlay,
    VSelect,
  },

  computed: {
    ...mapState('workspace', ['selectedWorkspace']),
    ...mapState('organisation', ['organisations']),

    organisationsMap() {
      return this.organisations.map(u => ({
        id: u.id,
        label: `${u.name} (Id: ${u.id} )`,
      }));
    },

    createDisabled() {
      return this.localWorkspace.name || this.localWorkspace?.userLimit <= 0;
    },
  },

  beforeMount() {
    organisationRequest.fetchOrganisations();
  },

  beforeDestroy() {
    this.setSelectedWorkspace({ selectedWorkspace: null });
  },

  data() {
    return {
      creating: false,
      localWorkspace: {
        label: '',
        userLimit: 0,
        organisationId: '',
      },
      localOrganisation: {
        id: null,
      },
      localAdministrators: [],
      localEditors: [],
      localViewers: [],
      userOptions: [],
      localOwner: {
        id: '',
      },
    };
  },

  methods: {
    ...mapActions('modal', ['closeModal']),
    ...mapActions('toast', ['addToast']),
    ...mapActions('workspace', ['setSelectedWorkspace']),
    ...mapActions('user', ['setSearch']),

    fetchUsers: debounce(async function fetchUsers(search, loading) {
      this.setSearch(search);
      await this.doCallApiFetchUsers();
      loading(false);
    }, 500),

    onInputName(e) {
      this.localWorkspace.label = e.target.value;
    },

    onInputUserCount(e) {
      this.localWorkspace.userLimit = e.target.value;
    },

    async onCreate() {
      if (this.createDisabled) {
        return;
      }
      this.localWorkspace.organisationId = this.localOrganisation.id;
      this.localWorkspace.ownerId = this.localOwner.id;
      this.localWorkspace.administratorIds = this.localAdministrators.map(u => { return u.id; });
      this.localWorkspace.editorIds = this.localEditors.map(u => { return u.id; });
      this.localWorkspace.viewerIds = this.localViewers.map(u => { return u.id; });
      // await this.setSelectedWorkspace({ selectedWorkspace: this.localWorkspace });
      await workspaceRequest.createWorkspace(this.localWorkspace).then((workspace) => {
        if (workspace) {
          workspaceRequest.fetchWorkspaces();
          this.closeModal();
          this.addToast({
            toastComponent: {
              component: WorkspaceToast,
              id: 'workspace-created',
            },
            toastData: {
              label: this.localWorkspace.label,
            },
          });
        }
      });
    },

    async doCallApiFetchUsers() {
      this.userOptions = (await userRequest.getUsers()).map(u => ({
        id: u.id,
        label: `${u.loginName} (Id: ${u.id} ) (Org Id: ${u.organisationId})`,
        selectable: u.organisationId === this.localOrganisation.id,
      }));
    },

    onCancel() {
      this.closeModal();
    },

    isOptionSelectable(u) {
      return u.selectable
      && !this.localEditors.find(local => local.id === u.id)
      && !this.localAdministrators.includes(local => local.id === u.id)
      && !this.localViewers.includes(local => local.id === u.id)
      && this.localOwner.id !== u.id;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.workspace-details-modal {
  @include modal;

  position: relative;

  .field {
    margin-bottom: 0.6rem;
  }

  .row {
    display: grid;
    grid-template-columns: 40% 60%
  }

  .wrapper-select {
    min-height: 30px;
  }

  .v-select {
    background-color: clr('white');
    width: 400px;
  }
}
</style>
