<template>
  <section class="workspace-table">
    <section class="workspace-toolbar">
      <section class="workspace-actions">
        <base-button :size="'small'" :colour="'danger'" @click="onClickTransfer">
          <i class="fa-regular fa-right-left icon"></i>
          Transfer User
        </base-button>
        <base-button :size="'small'" :colour="'base'" @click="onClickNewWorkspace">
          <i class="fa-regular fa-add icon"></i> New Workspace
        </base-button>
      </section>
      <section class="workspace-pagination">
        <pagination :current-page="page" :last="last" @onBtPrevious="onBtPrevious" @onBtNext="onBtNext" @onSetPage="onSetPage"></pagination>
      </section>
    </section>
    <section class="workspace-table-gird">
      <ag-grid-vue
          style="width: 100%; height: 100%"
          class="ag-theme-alpine"
          :columnDefs="columnDefs"
          :rowData="workspacesMap"
          rowSelection='multiple'
      />
    </section>
  </section>
</template>

<script>
import { AgGridVue } from 'ag-grid-vue3';
import { mapActions, mapState } from 'vuex';
import BaseButton from '@/components/Base/BaseButton';
import GridListCell from '@/components/Base/GridListCell';
import Pagination from '@/components/Pagination/pagination';
import WorkspaceActionCellRender from '@/components/Workspace/WorkspaceCellRender';
import WorkspaceNewModal from '@/components/Workspace/WorkspaceNewModal';
import WorkspaceTransferModal from '@/components/Workspace/WorkspaceTransferModal';
import workspaceRequest from '@/services/WorkspaceRequest';

export default {
  name: 'workspace-table',

  components: {
    BaseButton,
    Pagination,
    AgGridVue,
    WorkspaceActionCellRender,
  },

  data() {
    return {
      gridApi: null,
    };
  },

  beforeMount() {
    this.doCallApiFetchWorkspaces();
  },
  computed: {
    ...mapState('workspace', [
      'workspaces',
      'rows',
      'page',
    ]),

    columnDefs() {
      return [
        {
          id: 'Id',
          field: 'id',
          width: 100,
          wrapText: true,
          autoHeight: true,
          headerCheckboxSelection: true,
          checkboxSelection: true,
        },
        {
          headerName: 'Workspace label',
          field: 'label',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: ['contains'],
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
        },
        {
          headerName: 'Owner [ID]',
          field: 'owner',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: ['contains'],
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
        },
        {
          headerName: 'Administrators',
          field: 'administratorNames',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: GridListCell };
          },
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: ['contains'],
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
        },
        {
          headerName: 'Editors',
          field: 'editorNames',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: GridListCell };
          },
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: ['contains'],
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
        },
        {
          headerName: 'Viewers',
          field: 'viewerNames',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: GridListCell };
          },
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: ['contains'],
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
        },
        {
          headerName: 'User Limit',
          field: 'userLimit',
          flex: 1,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Organisation',
          field: 'organisationName',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: ['contains'],
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
        },
        {
          headerName: 'Actions',
          field: '',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: WorkspaceActionCellRender };
          },
        },
      ];
    },

    workspacesMap() {
      return this.workspaces.map(o => ({
        ...o,
        administratorLength: o.administratorIds.length,
        owner: `${o.ownerLabel} [${o.ownerId}]`,
        administratorNames: o.administrators.map(user => user.loginName),
        editorNames: o.editors.map(user => user.loginName),
        viewerNames: o.viewers.map(user => user.loginName),
      }));
    },

    last() {
      return this.workspaces.length < this.rows;
    },
  },

  methods: {
    ...mapActions('modal', ['setModal']),

    ...mapActions('workspace', ['setPage']),

    onClickTransfer() {
      this.setModal(WorkspaceTransferModal);
    },

    onClickNewWorkspace() {
      this.setModal(WorkspaceNewModal);
    },

    onBtPrevious() {
      this.setPage({ page: this.page - 1 });
      this.doCallApiFetchWorkspaces();
    },

    onBtNext() {
      this.setPage({ page: this.page + 1 });
      this.doCallApiFetchWorkspaces();
    },

    onSetPage(page) {
      this.setPage({ page });
      this.doCallApiFetchWorkspaces();
    },

    async doCallApiFetchWorkspaces() {
      await workspaceRequest.fetchWorkspaces();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.workspace-table {
  @include flex("block", "column", "start", "start");
  height: 100%;
  width: 100%;

  .workspace-toolbar {
    @include flex("block", "row", "start", "start");

    width: 100%;
    margin-top: 2rem;

    .workspace-actions {
      @include flex("block", "row", "start", "start");
      padding-left: 1rem;
      column-gap: 1em;
      margin: 0.2em;
      min-width: max-content;

      .icon {
        margin-right: 0.2em;
      }
    }

    .workspace-pagination {
      @include flex("block", "row", "end", "start");

      padding-right: 2rem;
      width: 100%;
    }
  }

  .workspace-table-gird {
    @include flex("block", "row", "start", "start");

    height: 100%;
    padding: 1rem 0.6rem 0 0.6rem;
    width: 100%;

    .title {
      @include flex("block", "row", "center", "start");

      font-size: 1.5rem;
      font-weight: $font-weight-bold;
    }

    .base-button {
      margin-left: auto;
    }
  }
}

</style>
