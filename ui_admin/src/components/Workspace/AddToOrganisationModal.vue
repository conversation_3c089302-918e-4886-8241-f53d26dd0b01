<template>
  <section class="add-to-organisation-modal">
    <section class="header">
      Add to organisation
    </section>

    <section class="body">
      <section class="wrapper-select">
        <v-select v-model="localOrganisation"
                  class="v-select"
                  :options="organisationsMap"
        />
      </section>
    </section>

    <section class="footer">
      <base-button class="cancel" colour="light" type="link" @click="closeModal">Cancel</base-button>
      <base-button class="delete" colour="danger" @click="onAddWorkspaceToOrganisation">Add</base-button>
    </section>
  </section>
</template>
<script>
import BaseButton from '@/components/Base/BaseButton';
import BaseRadio from '@/components/Base/BaseRadio';
import VSelect from 'vue-select';
import organisationRequest from '@/services/OrganisationRequest';
import { mapActions, mapState } from 'vuex';
import workspaceRequest from '@/services/WorkspaceRequest';

export default {
  name: 'add-to-organisation-modal',
  components: { BaseRadio, BaseButton, VSelect },

  data() {
    return {
      localOrganisation: {},
    };
  },

  beforeMount() {
    organisationRequest.fetchOrganisations();
  },

  computed: {
    ...mapState('organisation', ['organisations']),

    organisationsMap() {
      return this.organisations.map(u => ({
        id: u.id,
        label: `${u.name} (Id: ${u.id} )`,
      }));
    },

    isAdmin() {
      return this.localOrganisation.role ? this.localOrganisation.role === 'ADMIN' : false;
    },

  },

  methods: {
    ...mapActions('modal', ['setModal', 'closeModal']),

    onAddWorkspaceToOrganisation() {

    },

  },
};
</script>
<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";
.add-to-organisation-modal {
  @include modal

}
</style>
