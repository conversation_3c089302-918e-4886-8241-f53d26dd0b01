<template>
  <section class="workspace-cell-render">
    <section class="item">
      <base-button :size="'small'" :colour="'dark'" @click="onClickDetail">
        <i class="fa-regular fa-save icon"></i> Edit
      </base-button>
    </section>
    <section class="item">
      <base-button :size="'small'" :colour="'danger'" @click="onClickDelete" v-if="deletable">
        <i class="fa-regular fa-trash icon"></i> Delete
      </base-button>
    </section>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import WorkspaceDetailsModal from '@/components/Workspace/WorkspaceDetailsModal';
import WorkspaceDeleteModal from '@/components/Workspace/WorkspaceDeleteModal';

export default {
  name: 'workspace-cell-render',

  components: {
    BaseButton,
  },

  computed: {
    workspace() {
      return this.params.data;
    },

    deletable() {
      return this.workspace.editorIds.length === 0
          && this.workspace.administratorIds.length === 0
          && this.workspace.viewerIds.length === 0;
    },
  },

  methods: {
    ...mapActions('modal', ['setModal']),
    ...mapActions('workspace', ['setSelectedWorkspace']),

    onClickDetail() {
      this.setSelectedWorkspace({ selectedWorkspace: this.workspace });
      this.setModal(WorkspaceDetailsModal);
    },

    onClickDelete() {
      this.setSelectedWorkspace({ selectedWorkspace: this.workspace });
      this.setModal(WorkspaceDeleteModal);
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";
.workspace-cell-render {
  @include flex("block", "column", "start", "start");

  .item {
    @include flex("block", "row", "center", "center");

    border-radius: 4px;
    color: clr('white');
    height: 2rem;
    margin: 0.2rem;

    .base-button {
      height: 2rem;

      .icon {
        margin-right: 0.2rem;
      }
    }
  }
}
</style>
