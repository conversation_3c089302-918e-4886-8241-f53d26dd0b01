<template>
  <section class="workspace-details-modal">
    <section class="header">
      <h2>Update Workspace</h2>
    </section>
    <section class="body">
      <section class="field row">
        <span>Workspace label</span>
        <base-input placeholder="Enter workspace label" :value="localWorkspace.label" @input="onInputName"/>
      </section>
      <section class="field row">
        <span>User limit</span>
        <base-input placeholder="Set the workspace user limit" :type="'number'" :value="localWorkspace.userLimit" @input="onInputUserCount"/>
      </section>
      <section class="field row">
        <span>Topic Model</span>
        <v-select v-model="localWorkspace.topicModelType"
          class="v-select"
          :options="topicModelOptions"
          :searchable="true"
          />
      </section>
    </section>
    <section class="footer">
      <base-button class="cancel" colour="light" size="small" type="link"  @click="onCancel">Cancel</base-button>
      <loading-blocks-overlay v-if="creating" />
      <section class="confirm" v-else>
        <base-button v-if="isEditMode" colour="base" size="small" :disabled="createDisabled" @click="onUpdate">
          <span>Update Workspace</span>
        </base-button>
      </section>

    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import BaseCheckbox from '@/components/Base/BaseCheckbox';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import TopicModelType from '@/enum/topic-model-type';
import VSelect from 'vue-select';
import WorkspaceToast from '@/components/Toast/WorkspaceToast';
import workspaceRequest from '@/services/WorkspaceRequest';

export default {
  name: 'workspace-details-modal',

  components: {
    BaseButton,
    BaseCheckbox,
    BaseInput,
    LoadingBlocksOverlay,
    VSelect,
  },

  computed: {
    ...mapState('workspace', ['selectedWorkspace']),

    createDisabled() {
      return this.localWorkspace.name || this.localWorkspace?.userLimit <= 0;
    },

    isEditMode() {
      return this.selectedWorkspace != null;
    },

    topicModelOptions() {
      return [...TopicModelType].map(item => item.name);
    },
  },

  beforeMount() {
    this.localWorkspace = this.selectedWorkspace;
  },

  beforeDestroy() {
    this.setSelectedWorkspace({ selectedWorkspace: null });
  },

  data() {
    return {
      creating: false,
      localWorkspace: {
        label: '',
        topicModelType: 'NONE',
        userLimit: 0,
      },
    };
  },

  methods: {
    ...mapActions('modal', ['closeModal']),
    ...mapActions('toast', ['addToast']),
    ...mapActions('workspace', ['setSelectedWorkspace']),

    onInputName(e) {
      this.localWorkspace.label = e.target.value;
    },

    onInputUserCount(e) {
      this.localWorkspace.userLimit = e.target.value;
    },

    async onUpdate() {
      if (this.createDisabled) return;

      await this.setSelectedWorkspace({ selectedWorkspace: this.localWorkspace });

      const workspace = await workspaceRequest.updateWorkspace();

      if (workspace == null) return;

      workspaceRequest.fetchWorkspaces();
      this.closeModal();

      this.addToast({
        toastComponent: {
          component: WorkspaceToast,
          id: 'workspace-updated',
        },
        toastData: {
          label: this.localWorkspace.label,
        },
      });
    },

    onCancel() {
      this.closeModal();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.workspace-details-modal {
  @include modal;

  position: relative;

  .field {
    margin-bottom: 0.6rem;

    .features {
      @include flex('block', 'row', 'start', 'center');

      margin-top: 0.6rem;
      margin-left: 0.6rem;
    }
  }

  .row {
    align-items: center;
    display: grid;
    grid-template-columns: 40% 60%
  }

  .wrapper-select {
    height: 30px;
  }

  .v-select {
    background-color: clr('white');
  }
}
</style>
