<template>
  <section class="workspace-header">
    <section class="title">
      <i class="fa-solid fa-sitemap icon"></i>
      <span>Workspaces</span>
    </section>
  </section>
</template>

<script>
import BaseButton from '@/components/Base/BaseButton';
import { mapState } from 'vuex';

export default {
  name: 'workspace-header',

  components: {
    BaseButton,
  },

  computed: {
    ...mapState('workspace', ['workspaces']),

  },

  methods: {

  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.workspace-header {
  @include flex("block", "row", "start", "start");

  height: fit-content;
  padding: 1rem 0.6rem 0 0.6rem;
  width: 100%;

  .title {
    @include flex("block", "row", "center", "start");

    font-size: 1.5rem;
    font-weight: $font-weight-bold;
  }

  .base-button {
    margin-left: auto;
  }

  .icon {
    margin-right: 0.4rem;
  }
}
</style>
