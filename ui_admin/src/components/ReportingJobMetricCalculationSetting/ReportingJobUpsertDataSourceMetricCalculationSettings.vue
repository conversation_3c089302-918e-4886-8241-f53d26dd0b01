<template>
  <section class="reporting-job-upsert-metric-calculation-settings">
    <span class="title">Metric Calculation Settings: </span>

    <!-- List of metric calculation settings -->
    <reporting-job-upsert-metric-calculation-setting
        v-for="(setting, index) in localMetricCalculationSettings"
        :key="index"
        :index="index"
        :metadataColumns="metadataColumns"
        :metadataHeaders="metadataHeaders"
        :datasetNames="datasetNames"
        :metricCalculationSetting="setting"
        @removeMetricCalculationSetting="removeMetricCalculationSetting"
        @updateMetricCalculationSetting="updateMetricCalculationSetting"
        @addBoxValue="addBoxValue"
        @updateBoxValue="updateBoxValue"
        @removeBoxValue="removeBoxValue"
        @addCustomTarget="addCustomTarget"
        @updateTargetDatasetName="updateTargetDatasetName"
        @updateTargetDatasetValue="updateTargetDatasetValue"
        @removeCustomTarget="removeCustomTarget"
        class="metric-setting-container"
    />

    <!-- Add a new metric calculation setting button -->
    <div class="add-setting-btn">
      <base-button @click="addMetricCalculationSetting" size="small" class="btn btn-sm btn-primary">
        Add Metric Setting
      </base-button>
    </div>
  </section>
</template>

<script>
import BaseInput from '@/components/Base/BaseInput';
import BaseButton from '@/components/Base/BaseButton';
import ReportingJobUpsertMetricCalculationSetting from '@/components/ReportingJobMetricCalculationSetting/ReportingJobUpsertDataSourceMetricCalculationSetting';

export default {
  name: 'reporting-job-upsert-metric-calculation-settings',

  components: {
    BaseButton,
    BaseInput,
    ReportingJobUpsertMetricCalculationSetting,
  },

  props: {
    // The array of metric calculation settings
    metricCalculationSettings: {
      type: Array,
      default: () => [],
    },
    // Metadata headers for dropdown selection
    metadataHeaders: {
      type: Array,
      default: () => [],
    },
    // Metadata columns for mapping to the headers
    metadataColumns: {
      type: Array,
      default: () => [],
    },
    // Dataset names for custom targets dropdown
    datasetNames: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      // Local copy of the metricCalculationSettings to avoid modifying props directly
      localMetricCalculationSettings: [],
    };
  },

  watch: {
    // Watch for changes in the prop and update local copy
    metricCalculationSettings: {
      immediate: true,
      handler(newValue) {
        this.localMetricCalculationSettings = JSON.parse(JSON.stringify(newValue || []));
      },
    },
  },

  methods: {
    // Add a new empty metric calculation setting
    addMetricCalculationSetting() {
      const newSetting = {
        metadataColumn: this.metadataColumns?.[0] || 0,
        name: '',
        calculationType: '',
        boxValues: [],
        target: null,
        customTargets: {},
      };

      const updatedSettings = [...this.localMetricCalculationSettings, newSetting];
      this.updateMetricCalculationSettings(updatedSettings);
    },

    // Remove a metric calculation setting
    removeMetricCalculationSetting(index) {
      const updatedSettings = [...this.localMetricCalculationSettings];
      updatedSettings.splice(index, 1);
      this.updateMetricCalculationSettings(updatedSettings);
    },

    // Update a specific field in a setting
    updateMetricCalculationSetting({ index, field, value }) {
      const updatedSettings = [...this.localMetricCalculationSettings];

      updatedSettings[index] = {
        ...updatedSettings[index],
        [field]: value,
      };

      this.updateMetricCalculationSettings(updatedSettings);
    },

    // Add a new box value
    addBoxValue(settingIndex) {
      const updatedSettings = [...this.localMetricCalculationSettings];

      if (!updatedSettings[settingIndex].boxValues) {
        updatedSettings[settingIndex].boxValues = [];
      }

      updatedSettings[settingIndex].boxValues.push('');
      this.updateMetricCalculationSettings(updatedSettings);
    },

    // Update a specific box value
    updateBoxValue({ settingIndex, boxIndex, value }) {
      const updatedSettings = [...this.localMetricCalculationSettings];

      if (!updatedSettings[settingIndex].boxValues) {
        updatedSettings[settingIndex].boxValues = [];
      }

      updatedSettings[settingIndex].boxValues[boxIndex] = value;
      this.updateMetricCalculationSettings(updatedSettings);
    },

    // Remove a box value
    removeBoxValue({ settingIndex, boxIndex }) {
      const updatedSettings = [...this.localMetricCalculationSettings];

      if (updatedSettings[settingIndex].boxValues) {
        updatedSettings[settingIndex].boxValues.splice(boxIndex, 1);
      }

      this.updateMetricCalculationSettings(updatedSettings);
    },

    // Add a new custom target
    addCustomTarget(settingIndex) {
      const updatedSettings = [...this.localMetricCalculationSettings];

      if (!updatedSettings[settingIndex].customTargets) {
        updatedSettings[settingIndex].customTargets = {};
      }

      // Find the first available dataset name that's not already used
      const availableDatasetName = this.datasetNames.find(
        name => !Object.prototype.hasOwnProperty.call(updatedSettings[settingIndex].customTargets, name),
      );

      if (availableDatasetName) {
        updatedSettings[settingIndex].customTargets[availableDatasetName] = 0;
        this.updateMetricCalculationSettings(updatedSettings);
      }
    },

    // Update dataset name (rename key in the map)
    updateTargetDatasetName({ settingIndex, oldDatasetName, newDatasetName }) {
      const updatedSettings = [...this.localMetricCalculationSettings];

      if (!updatedSettings[settingIndex].customTargets) {
        updatedSettings[settingIndex].customTargets = {};
      }

      const { customTargets } = updatedSettings[settingIndex];

      // Check if the new dataset name already exists
      if (Object.prototype.hasOwnProperty.call(customTargets, newDatasetName)) {
        // Handle collision - could show error or merge values
        return;
      }

      // Get the old value and delete the old key
      const oldValue = customTargets[oldDatasetName] || 0;
      delete customTargets[oldDatasetName];

      // Set the new key with the old value
      customTargets[newDatasetName] = oldValue;

      this.updateMetricCalculationSettings(updatedSettings);
    },

    // Update target value for a specific dataset
    updateTargetDatasetValue({ settingIndex, datasetName, value }) {
      const updatedSettings = [...this.localMetricCalculationSettings];

      if (!updatedSettings[settingIndex].customTargets) {
        updatedSettings[settingIndex].customTargets = {};
      }

      updatedSettings[settingIndex].customTargets[datasetName] = value;
      this.updateMetricCalculationSettings(updatedSettings);
    },

    // Remove a custom target
    removeCustomTarget({ settingIndex, datasetName }) {
      const updatedSettings = [...this.localMetricCalculationSettings];

      if (updatedSettings[settingIndex].customTargets) {
        delete updatedSettings[settingIndex].customTargets[datasetName];
      }

      this.updateMetricCalculationSettings(updatedSettings);
    },

    // Common method to emit the updated settings
    updateMetricCalculationSettings(updatedSettings) {
      this.localMetricCalculationSettings = updatedSettings;
      this.$emit('update:metricCalculationSettings', updatedSettings);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-upsert-metric-calculation-settings {
  width: 35rem;

  .title {
    font-size: $font-size-sm;
  }
}
</style>
