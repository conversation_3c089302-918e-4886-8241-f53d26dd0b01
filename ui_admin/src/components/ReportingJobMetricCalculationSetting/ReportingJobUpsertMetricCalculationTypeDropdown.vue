<template>
  <section class="reporting-job-upsert-metric-calculation-type-dropdown">
    <section class="item avg" @click="onClickAvg">
      <i class="fa-solid fa-face-laugh-beam icon" />
      <section class="text">
        <span class="title">AVG</span>
      </section>
    </section>
    <section class="item nps" @click="onClickNps">
      <i class="fa-regular fa-comment icon" />
      <section class="text">
        <span class="title">NPS</span>
      </section>
    </section>
    <section class="item top-box" @click="onClickTopBox">
      <i class="fa-solid fa-chart-simple icon" />
      <section class="text">
        <span class="title">TOP BOX</span>
      </section>
    </section>
  </section>
</template>

<script>
import BaseButton from '@/components/Base/BaseButton';
import MetricCalculationType from '@/enum/metric-calculation-type';

export default {
  name: 'reporting-job-upsert-metric-calculation-type-dropdown',

  components: {
    BaseButton,
  },

  methods: {
    onClickAvg() {
      this.$emit('select', MetricCalculationType.AVG.name);
    },

    onClickNps() {
      this.$emit('select', MetricCalculationType.NPS.name);
    },

    onClickTopBox() {
      this.$emit('select', MetricCalculationType.TOP_BOX.name);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-upsert-metric-calculation-type-dropdown {
  @include flex("block", "column", "start", "start");
  @include panel-dropdown;

  font-weight: $font-weight-normal;
  left: 0;
  top: 28px;
  width: 14rem;

  .item {
    @include flex("block", "row", "start", "center");
    @include hover-element-dropdown;

    padding: 0.6rem;
    width: 100%;

    &:hover{
      font-weight: unset;
    }

    .icon {
      font-size: $font-size-sm;
      margin-right: 0.6rem;
    }

    .text {
      @include flex("block", "column", "start", "start");

      font-size: $font-size-xs;

      .title {
        font-weight: $font-weight-bold;
      }
    }
  }
}
</style>
