<template>
  <section class="reporting-job-upsert-metric-calculation-setting">
    <!-- List of metric calculation settings -->
    <div class="metric-setting-container">
      <div class="metric-setting-header">
        <h5>Metric Setting {{ index + 1 }}</h5>
        <base-button size="small" colour="danger" @click="removeMetricCalculationSetting" class="btn btn-sm btn-danger">
          Remove
        </base-button>
      </div>

      <div class="metric-setting-form">
        <!-- Metadata Column -->
        <div class="form-group">
          <label>Metadata Column:</label>
          <v-select
            class="value"
            :options="metadataHeaders"
            v-model="selectedHeader"
          />
        </div>
        <!-- Name -->
        <div class="form-group">
          <label>Name:</label>
          <base-input
            type="text"
            placeholder="Metric name"
            :value="metricCalculationSetting.name"
            @input="updateSetting('name', $event.target.value)"
          />
        </div>

        <!-- Calculation Type -->
        <div class="form-group">
          <label>Calculation Type:</label>
          <section
            class="btn-select"
            :class="{ isSelected }"
            @click="openDropdownMetricCalculationType = !openDropdownMetricCalculationType"
            v-click-outside="handleClickOutsideMetricCalculationType"
          >
            <span v-if="isSelected && metricCalculationSetting.calculationType">{{ metricCalculationSetting.calculationType }}</span>
            <span v-else>Select type</span>
            <i class="fa-solid fa-chevron-down icon-down" />
            <reporting-job-upsert-metric-calculation-type-dropdown
              v-if="openDropdownMetricCalculationType"
              class="form-control"
              :value="metricCalculationSetting.calculationType"
              @close="openDropdownMetricCalculationType = false"
              @select="updateSetting('calculationType', $event)"
            />
          </section>
        </div>

        <!-- Box Values -->
        <div class="form-group" v-if="MetricCalculationType.TOP_BOX.name === metricCalculationSetting.calculationType">
          <label>Box Values:</label>
          <div class="box-values-container">
            <div v-for="(boxValue, boxIndex) in metricCalculationSetting.boxValues || []" :key="boxIndex" class="box-value-item">
              <base-input
                type="text"
                :value="boxValue"
                @input="updateBoxValue(boxIndex, $event.target.value)"
                placeholder="Box value"
              />
              <base-button @click="removeBoxValue(boxIndex)" size="small" colour="danger" class="btn btn-sm btn-danger">
                &times;
              </base-button>
            </div>
            <base-button @click="addBoxValue" size="small" class="btn btn-sm btn-primary">
              Add Box Value
            </base-button>
          </div>
        </div>
        <div class="form-group">
          <label>Target:</label>
          <base-input
              type="text"
              :value="metricCalculationSetting.target"
              @input="updateSetting('target', $event.target.value)"
              placeholder="Global target value"
          />
        </div>
        <div class="form-group" v-if="datasetNames.length > 1">
          <label>Custom Targets:</label>
          <div class="targets-container">
            <div v-for="(targetValue, datasetName, targetIndex) in metricCalculationSetting.customTargets || {}" :key="datasetName" class="target-item">
              <v-select
                  class="dataset-select"
                  :options="datasetNames"
                  :modelValue="datasetName"
                  @input="updateTargetDatasetName(datasetName, $event)"
                  placeholder="Select dataset"
              />
              <base-input
                  type="number"
                  :value="targetValue"
                  @input="updateTargetDatasetValue(datasetName, $event.target.value)"
                  placeholder="Target value"
                  step="0.01"
              />
              <base-button @click="removeTarget(datasetName)" size="small" colour="danger" class="btn btn-sm btn-danger">
                &times;
              </base-button>
            </div>
            <base-button @click="addCustomTarget" size="small" class="btn btn-sm btn-primary">
              Add Custom Target
            </base-button>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import VSelect from 'vue-select';
import MetricCalculationType from '@/enum/metric-calculation-type';
import ReportingJobUpsertMetricCalculationTypeDropdown from '@/components/ReportingJobMetricCalculationSetting/ReportingJobUpsertMetricCalculationTypeDropdown';

export default {
  name: 'reporting-job-upsert-metric-calculation-setting',

  components: {
    ReportingJobUpsertMetricCalculationTypeDropdown,
    BaseButton,
    VSelect,
    BaseInput,
  },

  data() {
    return {
      openDropdownMetricCalculationType: false,
      localCustomTargets: {},
    };
  },

  props: {
    metricCalculationSetting: {
      type: Object,
      required: true,
    },
    index: {
      type: Number,
      required: true,
    },
    metadataHeaders: {
      type: Array,
      default: () => [],
    },
    metadataColumns: {
      type: Array,
      default: () => [],
    },
    datasetNames: {
      type: Array,
      default: () => [],
    },
  },

  computed: {
    MetricCalculationType() {
      return MetricCalculationType;
    },
    isSelected() {
      return this.metricCalculationSetting.calculationType !== null &&
          this.metricCalculationSetting.calculationType !== '';
    },
    selectedHeader: {
      get() {
        return this.getHeaderFromColumn(this.metricCalculationSetting.metadataColumn);
      },
      set(value) {
        // Handle in the updateSetting method
        this.updateSetting('metadataColumn', value);
      },
    },
  },

  methods: {
    // Remove this metric calculation setting
    removeMetricCalculationSetting() {
      this.$emit('removeMetricCalculationSetting', this.index);
    },

    // Get the header name from the column index
    getHeaderFromColumn(metadataColumn) {
      if (metadataColumn === null || metadataColumn === undefined) return null;
      const headerIndex = this.metadataColumns.indexOf(metadataColumn);
      return headerIndex !== -1 ? this.metadataHeaders[headerIndex] : null;
    },

    // Update a specific field in a setting
    updateSetting(field, value) {
      let processedValue = value;

      // Handle mapping from header name to column index for metadataColumn
      if (field === 'metadataColumn') {
        const headerIndex = this.metadataHeaders.indexOf(value);
        processedValue = headerIndex !== -1 ? this.metadataColumns[headerIndex] : 0;
      }

      this.$emit('updateMetricCalculationSetting', {
        index: this.index,
        field,
        value: processedValue,
      });
    },

    // Add a new box value
    addBoxValue() {
      this.$emit('addBoxValue', this.index);
    },

    // Update a specific box value
    updateBoxValue(boxIndex, value) {
      this.$emit('updateBoxValue', {
        settingIndex: this.index,
        boxIndex,
        value,
      });
    },

    // Remove a box value
    removeBoxValue(boxIndex) {
      this.$emit('removeBoxValue', {
        settingIndex: this.index,
        boxIndex,
      });
    },

    handleClickOutsideMetricCalculationType() {
      this.openDropdownMetricCalculationType = false;
    },

    // Add a new custom target
    addCustomTarget() {
      this.$emit('addCustomTarget', this.index);
    },

    // Update dataset name (rename key in the map)
    updateTargetDatasetName(oldDatasetName, newDatasetName) {
      if (oldDatasetName !== newDatasetName && newDatasetName) {
        this.$emit('updateTargetDatasetName', {
          settingIndex: this.index,
          oldDatasetName,
          newDatasetName,
        });
      }
    },

    // Update target value for a specific dataset
    updateTargetDatasetValue(datasetName, value) {
      this.$emit('updateTargetDatasetValue', {
        settingIndex: this.index,
        datasetName,
        value: parseFloat(value) || 0,
      });
    },

    // Remove a custom target
    removeTarget(datasetName) {
      this.$emit('removeCustomTarget', {
        settingIndex: this.index,
        datasetName,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";
.reporting-job-upsert-metric-calculation-setting {
  font-size: $font-size-sm;
  width: 100%;

  .metric-setting-container {
    border-radius: 4px;
    border: 1px solid;
    margin-bottom: 0.5rem;
    padding: 0.5rem;
  }

  .metric-setting-header {
    @include flex("block", "row", "space-between", "center");
  }

  .metric-setting-form {
    .form-group {
      margin-bottom: 0.75rem;

      label {
        display: block;
        margin-bottom: 0.3rem;
        font-weight: 500;
      }

      .btn-select {
        @include flex("block", "row", "space-between", "start");

        background-color: clr('white');
        border-radius: 4px;
        border: $border-standard;
        cursor: pointer;
        font-size: $font-size-xxs;
        font-weight: $font-weight-bold;
        margin-top: 0.4rem;
        min-width: 10rem;
        outline: none;
        padding: 0.3rem 0.35rem;
        position: relative;

        &.isSelected {
          border: $border-purple;
          color: $border-color-purple;
        }

        &:hover,
        &:focus {
          border: $border-purple;
          color: $border-color-purple;
          outline: none;
        }
      }
    }
  }

  .box-values-container {
    border-left: 2px solid;
    padding-left: 1rem;

    .box-value-item {
      @include flex("block", "row", "start", "center");
      margin-bottom: 0.5rem;

      .base-input {
        flex-grow: 1;
        margin-right: 0.5rem;
      }
    }
  }

  .targets-container {
    border-left: 2px solid;
    padding-left: 1rem;

    .target-item {
      @include flex("block", "row", "start", "center");
      margin-bottom: 0.5rem;
      gap: 0.5rem;

      .dataset-select {
        flex: 1;
        min-width: 150px;
      }

      .base-input {
        flex: 1;
        min-width: 120px;
      }
    }
  }
}
</style>
