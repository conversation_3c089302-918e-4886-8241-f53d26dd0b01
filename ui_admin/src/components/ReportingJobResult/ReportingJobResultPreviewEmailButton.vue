<template>
  <base-button :size="'small'" :colour="'dark'" @click="onClickPreviewResult">
    <i class="fa-solid fa-inbox icon" />
    Preview Result
  </base-button>
</template>

<script>
import { mapActions } from 'vuex';
import BaseButton from '@/components/Base/BaseButton';
import ReportingJobResultPreviewEmailModal from '@/components/ReportingJobResult/ReportingJobResultPreviewEmailModal';

export default {
  name: 'ReportingJobResultPreviewEmailButton',

  components: {
    BaseButton,
  },

  props: {
    reportingJobResult: {
      type: Object,
      required: true,
    },
  },

  methods: {
    ...mapActions('modal', ['setModalAndProps']),

    onClickPreviewResult() {
      this.setModalAndProps({
        component: ReportingJobResultPreviewEmailModal,
        props: {
          reportingJobResult: this.reportingJobResult,
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.icon {
  margin-right: 0.2em;
}
</style>
