<template>
  <section class="reporting-job-result-table">
    <loading-blocks-overlay v-if="loading" />
    <section v-else v-for="item in reportingJobResults" class="result-item">
      <section class="title">
        <span>Execution Number: {{item.executionNumber}}</span>
        <base-button :size="'small'" :colour="'dark'" @click="onClickDispatchEmail(item)">
          <i class="fa-solid fa-paper-plane-top icon" />
          Send Results
        </base-button>
        <base-button :size="'small'" :colour="'danger'" @click="onClickDelete(item)">
          <i class="fa-regular fa-trash icon" />
          Delete
        </base-button>
      </section>
      <ag-grid-vue
        style="width: 100%; height: 400px; margin-top: 0.3rem;"
        class="ag-theme-alpine"
        :columnDefs="columnDefs"
        :rowData="item.results"
      />
    </section>
  </section>
</template>

<script>
import { AgGridVue } from 'ag-grid-vue3';
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import DateTimeRenderer from '@/components/BaseAgGrid/DateTimeRenderer';
import GridListCell from '@/components/Base/GridListCell';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import Pagination from '@/components/Pagination/pagination';
import ReportingJobRequest from '@/services/ReportingJobRequest';
import ReportingJobResultActionRenderer from '@/components/ReportingJobResult/ReportingJobResultActionRenderer';
import ReportingJobResultDeleteModal from '@/components/ReportingJobResult/ReportingJobResultDeleteModal';
import ReportingJobResultDispatchEmailModal from '@/components/ReportingJobResult/ReportingJobResultDispatchEmailModal';

export default {
  name: 'reporting-job-result-table',

  components: {
    AgGridVue,
    BaseButton,
    GridListCell,
    LoadingBlocksOverlay,
    Pagination,
  },

  data() {
    return {
      loading: true,
    };
  },

  computed: {
    ...mapState('reportingJob', ['reportingJobResults', 'selectedId']),

    columnDefs() {
      return [
        {
          headerName: 'Dataset Id',
          field: 'datasetId',
          width: 100,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Dataset Original Name',
          field: 'datasetOriginalName',
          flex: 1,
          minWidth: 200,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Volume',
          field: 'volume',
          width: 100,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Volume Change',
          field: 'volumeChange',
          width: 150,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Score',
          field: 'adorescore',
          width: 80,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Score Change',
          field: 'adorescoreChange',
          width: 150,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Start Date',
          field: 'startDate',
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: DateTimeRenderer };
          },
        },
        {
          headerName: 'End Date',
          field: 'endDate',
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: DateTimeRenderer };
          },
        },
        {
          headerName: 'Created At',
          field: 'createdAt',
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: DateTimeRenderer };
          },
        },
        {
          headerName: 'Actions',
          field: '',
          width: 150,
          wrapText: true,
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: ReportingJobResultActionRenderer };
          },
        },
      ];
    },
  },

  async created() {
    this.loading = true;
    await ReportingJobRequest.fetchResults(this.selectedId);
    this.loading = false;
  },

  methods: {
    ...mapActions('modal', ['setModalAndProps']),

    onClickDelete(item) {
      this.setModalAndProps({
        component: ReportingJobResultDeleteModal,
        props: {
          executionNumber: item.executionNumber,
        },
      });
    },

    onClickDispatchEmail(item) {
      this.setModalAndProps({
        component: ReportingJobResultDispatchEmailModal,
        props: {
          executionNumber: item.executionNumber,
        },
      });
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-result-table {
  @include flex("block", "column", "start", "start");

  height: 100%;
  margin-top: 0.6rem;
  width: 100%;

  .result-item {
    height: 100%;
    margin-top: 0.6rem;
    margin-bottom: 1.2rem;
    width: 100%;

    .title {
      .base-button {
        margin-left: 0.5rem;
      }
    }
  }
}
</style>
