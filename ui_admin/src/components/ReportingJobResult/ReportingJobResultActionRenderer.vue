<template>
  <section class="reporting-job-result-action-renderer">
    <section class="item">
      <base-button :size="'small'" :colour="'dark'" @click="onClickEdit">
        <i class="fa-solid fa-pen-to-square icon" />
        Edit
      </base-button>
    </section>
    <section class="item">
      <reporting-job-result-preview-email-button :reporting-job-result="reportingJobResult" />
    </section>
    <section class="item">
      <reporting-job-result-send-result-button :reporting-job-result="reportingJobResult" />
    </section>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import ReportingJobResultPreviewEmailButton from '@/components/ReportingJobResult/ReportingJobResultPreviewEmailButton';
import ReportingJobResultSendResultButton from '@/components/ReportingJobResult/ReportingJobResultSendResultButton';
import Route from '@/enum/route';

export default {
  name: 'reporting-job-result-action-renderer',

  components: {
    BaseButton,
    ReportingJobResultPreviewEmailButton,
    ReportingJobResultSendResultButton,
  },

  computed: {
    reportingJobResult() {
      return this.params.data;
    },
  },

  methods: {
    ...mapActions('modal', ['setModalAndProps']),

    ...mapActions('reportingJob', ['setSelectedId', 'setSelectedResultId']),

    onClickEdit() {
      this.setSelectedResultId(this.reportingJobResult.id);
      this.$router.push({
        name: Route.REPORTING_JOB_RESULT_EDIT,
        query: {
          reportingJobId: this.reportingJobResult.reportingJobId,
          resultId: this.reportingJobResult.id,
        },
      });
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-result-action-renderer {
  @include flex("block", "column", "start", "start");

  .item {
    @include flex("block", "row", "center", "center");

    border-radius: 4px;
    color: clr('white');
    height: 2rem;
    margin: 0.2rem;

    .base-button {
      height: 2rem;
    }

    .icon {
      margin-right: 0.2em;
    }
  }
}
</style>
