<template>
  <section class="reporting-job-result-dispatch-email-modal">
    <section class="header">
      <h2>Send emails for Reporting Job Result</h2>
    </section>

    <section class="body">
      <section class="text">
        <span>
          Are you sure you want to send emails for Execution Number <b>{{executionNumber}}</b>
          of the reporting job <b>{{selectedReportingJob.name}}</b>?
        </span>
      </section>
      <section class="recipient-emails">
        <span class="title">Recipient Emails: </span>
        <base-input class="value" :value="recipientEmails" @input="onSetEmails($event)"/>
        <div class="warning-text">
          <span>Leave empty to use the default recipients from the job configuration.</span>
        </div>
      </section>
    </section>

    <section class="footer">
      <base-button class="cancel" colour="light" type="link" @click="closeModal">Cancel</base-button>
      <loading-blocks-overlay v-if="sending" />
      <base-button v-else class="send-emails" @click="onClickSend">Send Emails</base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import ReportingJobRequest from '@/services/ReportingJobRequest';

export default {
  name: 'reporting-job-result-dispatch-email-modal',

  components: {
    BaseInput,
    BaseButton,
    LoadingBlocksOverlay,
  },

  props: {
    componentProps: {
      executionNumber: Number,
    },
  },

  data() {
    return {
      sending: false,
      recipientEmails: '',
    };
  },

  computed: {
    ...mapState('reportingJob', ['selectedReportingJob']),

    executionNumber() {
      return this.componentProps.executionNumber;
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('toast', ['addToast']),

    async onClickSend() {
      this.sending = true;

      const emails = this.recipientEmails?.trim();
      const recipientEmails = emails ? emails.split(',').map(email => email.trim()) : null;

      const params = {
        executionNumber: this.executionNumber,
        recipientEmails,
      };

      await ReportingJobRequest.dispatchReportEmail(this.selectedReportingJob.id, params);
      await this.closeModal();

      this.sending = false;
    },

    onSetEmails(e) {
      this.recipientEmails = e.target.value;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-result-dispatch-email-modal {
  @include modal;

  .header {
    @include flex("block", "row", "between", "center");

    padding: $search-modal-padding;

    h2 {
      @include flex("block", "row", "start", "center");

      font-weight: $font-weight-bold;
    }
  }

  .body {
    padding: 1.5rem;

    .text {
      font-size: $font-size-xs;
      margin: 0.5rem 0;
    }

    .recipient-emails  {
      @include flex("block", "column", "start", "start");

      .warning-text {
        font-size: $font-size-xs;
        margin: 0.5rem 0;
        color: indianred;
      }
    }
  }

  .footer {
    font-size: $font-size-md;
    padding: $search-modal-padding;

    .cancel {
      font-weight: $font-weight-normal;
      padding-left: 0;
    }

    .send-emails {
      font-weight: $font-weight-medium;
    }
  }
}
</style>
