<template>
  <section class="reporting-job-result-send-email-modal">
    <section class="header">
      <h2>Send Report Result</h2>
    </section>

    <section class="body">
      <section class="recipient-emails">
        <span class="title">Recipient Emails: </span>
        <base-input class="value" :value="recipientEmails" @input="onSetEmails($event)"/>
        <div class="warning-text">
          <span>Leave empty to use the default recipients from the job configuration.</span>
        </div>
      </section>
    </section>

    <section class="footer">
      <base-button class="cancel" colour="light" type="link" @click="closeModal">Cancel</base-button>
      <loading-blocks-overlay v-if="sending" />
      <base-button v-else class="send" @click="onClickSend">Send Emails</base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import ReportingJobRequest from '@/services/ReportingJobRequest';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';

export default {
  name: 'reporting-job-result-send-email-modal',

  components: {
    LoadingBlocksOverlay,
    BaseInput,
    BaseButton,
  },

  props: {
    componentProps: {
      reportingJobResult: Object,
    },
  },

  data() {
    return {
      sending: false,
      recipientEmails: '',
    };
  },

  computed: {
    ...mapState('reportingJob', ['selectedId']),

    reportingJobResult() {
      return this.componentProps.reportingJobResult;
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('toast', ['addToast']),

    async onClickSend() {
      this.sending = true;

      const emails = this.recipientEmails?.trim();
      const recipientEmails = emails ? emails.split(',').map(email => email.trim()) : null;

      const params = {
        resultId: this.reportingJobResult.id,
        recipientEmails,
      };

      await ReportingJobRequest.dispatchReportEmail(this.selectedId, params);
      await this.closeModal();

      this.sending = false;
    },

    onSetEmails(e) {
      this.recipientEmails = e.target.value;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-result-send-email-modal {
  @include modal;

  .body {
    padding: 1.5rem;

    .text {
      font-size: $font-size-xs;
      margin: 0.5rem 0;

      .label {
        font-weight: $font-weight-bold;
      }
    }

    .recipient-emails  {
      @include flex("block", "column", "start", "start");

      .warning-text {
        font-size: $font-size-xs;
        margin: 0.5rem 0;
        color: indianred;
      }
    }

  }

  .footer {
    font-size: $font-size-md;
    padding: $search-modal-padding;

    .cancel {
      font-weight: $font-weight-normal;
      padding-left: 0;
    }

    .send {
      font-weight: $font-weight-medium;
    }
  }

  .header {
    @include flex("block", "row", "between", "center");

    padding: $search-modal-padding;

    h2 {
      @include flex("block", "row", "start", "center");

      font-weight: $font-weight-bold;
    }

    .icon {
      @include flex("block", "row", "end", "center");

      cursor: pointer;
      height: 1.25rem;
      opacity: 0.3;
      &:hover {
        opacity: 1;
      }

      &:active,
      &:focus {
        border: none;
        outline: none;
      }
    }
  }
}
</style>
