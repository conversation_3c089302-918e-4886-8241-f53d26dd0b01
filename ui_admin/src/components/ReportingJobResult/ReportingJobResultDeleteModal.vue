<template>
  <section class="reporting-job-result-delete-modal">
    <section class="header">
      <h2>Delete Reporting Job Result</h2>
    </section>

    <section class="body">
      <section class="text">
        <span>Execution number <b>{{executionNumber}}</b> of reporting job <b>{{selectedReportingJob.name}}</b> will be delete. Are you sure you wish to continue? </span>
      </section>
    </section>

    <section class="footer">
      <base-button class="cancel" colour="light" type="link" @click="closeModal">Cancel</base-button>
      <loading-blocks-overlay v-if="deleting" />
      <base-button v-else class="delete" colour="danger" @click="onClickDelete">Delete</base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import ReportingJobRequest from '@/services/ReportingJobRequest';
// import ReportingJobToast from '@/components/ReportingJob/ReportingJobToast';

export default {
  name: 'reporting-job-result-delete-modal',

  components: {
    BaseButton,
    LoadingBlocksOverlay,
  },

  props: {
    componentProps: {
      executionNumber: Number,
    },
  },

  data() {
    return {
      deleting: false,
    };
  },

  computed: {
    ...mapState('reportingJob', ['selectedReportingJob']),

    executionNumber() {
      return this.componentProps.executionNumber;
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('toast', ['addToast']),

    async onClickDelete() {
      this.deleting = true;
      await ReportingJobRequest.deleteResult(this.selectedReportingJob.id, this.executionNumber);
      await ReportingJobRequest.fetchResults(this.selectedReportingJob.id);
      await this.closeModal();
      // await this.addToast({
      //   toastComponent: {
      //     component: ReportingJobToast,
      //     id: 'reporting-job-deleted',
      //   },
      //   toastData: {
      //     label: this.reportingJob.name,
      //   },
      // });
      this.deleting = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-result-delete-modal {
  @include modal;

  .header {
    @include flex("block", "row", "between", "center");

    padding: $search-modal-padding;

    h2 {
      @include flex("block", "row", "start", "center");

      font-weight: $font-weight-bold;
    }

    .icon {
      @include flex("block", "row", "end", "center");

      cursor: pointer;
      height: 1.25rem;
      opacity: 0.3;
      &:hover {
        opacity: 1;
      }

      &:active,
      &:focus {
        border: none;
        outline: none;
      }
    }
  }

  .body {
    padding: 1.5rem;

    .text {
      font-size: $font-size-xs;
      margin: 0.5rem 0;
    }
  }

  .footer {
    font-size: $font-size-md;
    padding: $search-modal-padding;

    .cancel {
      font-weight: $font-weight-normal;
      padding-left: 0;
    }

    .delete {
      font-weight: $font-weight-medium;
    }
  }
}
</style>
