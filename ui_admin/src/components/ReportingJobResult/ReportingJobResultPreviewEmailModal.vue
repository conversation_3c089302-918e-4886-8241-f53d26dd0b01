<template>
  <section class="reporting-job-result-preview-email-modal">
    <section class="header">
      <h2>Preview Email</h2>
    </section>

    <section class="body">
      <loading-blocks-overlay v-if="loading" />

      <div v-if="!loading && html" class="email-preview">
        <div class="email-info">
          <div class="text">
            <span class="label">Dataset:</span> {{ reportingJobResult.datasetOriginalName }}
          </div>
          <div class="text">
            <span class="label">Job ID:</span> {{ reportingJobResult.id }}
          </div>
        </div>

        <div class="email-content">
          <iframe
              ref="emailFrame"
              class="email-iframe"
              :srcdoc="html"
              frameborder="0"
              sandbox="allow-same-origin"
          ></iframe>
        </div>
      </div>

      <div v-if="!loading && !html" class="error-message">
        <p>Failed to load email preview. Please try again.</p>
      </div>
    </section>

    <section class="footer">
      <base-button class="cancel" colour="light" type="link" @click="closeModal">
        Close
      </base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import ReportingJobRequest from '@/services/ReportingJobRequest';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';

export default {
  name: 'reporting-job-result-preview-email-modal',

  components: {
    LoadingBlocksOverlay,
    BaseInput,
    BaseButton,
  },

  props: {
    componentProps: {
      type: Object,
      default: () => ({
        reportingJobResult: {},
      }),
    },
  },

  data() {
    return {
      html: '',
      loading: true,
      sending: false,
      error: null,
    };
  },

  async created() {
    this.loading = true;
    this.html = await ReportingJobRequest.previewEmail(this.selectedId, this.reportingJobResult.id);
    this.loading = false;
  },

  computed: {
    ...mapState('reportingJob', ['selectedId']),

    reportingJobResult() {
      return this.componentProps.reportingJobResult || {};
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),
    ...mapActions('toast', ['addToast']),
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-result-preview-email-modal {
  @include modal;
  max-width: 800px;
  max-height: 90vh;

  .body {
    padding: 1.5rem;
    overflow-y: auto;
    max-height: 70vh;
    position: relative;

    .email-preview {
      .email-info {
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #e0e0e0;

        .text {
          font-size: $font-size-sm;
          margin: 0.5rem 0;

          .label {
            font-weight: $font-weight-bold;
            margin-right: 0.5rem;
          }
        }
      }

      .email-content {
        h3 {
          font-size: $font-size-md;
          font-weight: $font-weight-bold;
          margin-bottom: 1rem;
          color: #333;
        }

        .email-iframe {
          width: 100%;
          min-height: 400px;
          height: 60vh;
          border: 1px solid #e0e0e0;
          border-radius: 4px;
          background-color: #fff;
        }
      }
    }

    .error-message {
      text-align: center;
      padding: 2rem;
      color: #666;
    }
  }

  .footer {
    font-size: $font-size-md;
    padding: $search-modal-padding;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .cancel {
      font-weight: $font-weight-normal;
      padding-left: 0;
    }

    .send {
      font-weight: $font-weight-medium;
    }
  }

  .header {
    @include flex("block", "row", "between", "center");
    padding: $search-modal-padding;
    border-bottom: 1px solid #e0e0e0;

    h2 {
      @include flex("block", "row", "start", "center");
      font-weight: $font-weight-bold;
      margin: 0;
    }

    .icon {
      @include flex("block", "row", "end", "center");
      cursor: pointer;
      height: 1.25rem;
      width: 1.25rem;
      opacity: 0.6;
      transition: opacity 0.2s;

      &:hover {
        opacity: 1;
      }

      &:active,
      &:focus {
        border: none;
        outline: none;
      }

      svg {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
