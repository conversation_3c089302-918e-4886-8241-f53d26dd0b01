<template>
  <base-button :size="'small'" :colour="'dark'" @click="onClickSendResult">
    <i class="fa-solid fa-paper-plane-top icon" />
    Send Result
  </base-button>
</template>

<script>
import { mapActions } from 'vuex';
import BaseButton from '@/components/Base/BaseButton';
import ReportingJobResultSendEmailModal from '@/components/ReportingJobResult/ReportingJobResultSendEmailModal';

export default {
  name: 'ReportingJobResultSendResultButton',

  components: {
    BaseButton,
  },

  props: {
    reportingJobResult: {
      type: Object,
      required: true,
    },
  },

  methods: {
    ...mapActions('modal', ['setModalAndProps']),

    onClickSendResult() {
      this.setModalAndProps({
        component: ReportingJobResultSendEmailModal,
        props: {
          reportingJobResult: this.reportingJobResult,
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.icon {
  margin-right: 0.2em;
}
</style>
