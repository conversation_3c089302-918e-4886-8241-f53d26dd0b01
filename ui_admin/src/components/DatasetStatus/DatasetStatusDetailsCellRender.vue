<template>
  <section class="dataset-status-details-cell-render">
    <section v-for="s in sortedStatuses" :key="s.status" class="status-item">
      <span class="status-text">{{ s.status }}</span>
      <span class="time-text">{{ formatDuration(s.startedAt, s.updatedAt) }}</span>
    </section>
  </section>
</template>

<script>
import { DateTime } from 'luxon';

import BaseButton from '@/components/Base/BaseButton';
import DatasetStatus from '@/enum/dataset-status';

export default {
  name: 'dataset-status-details-cell-render',

  components: {
    BaseButton,
  },

  computed: {
    datasetStatus() {
      return this.params.data;
    },

    sortedStatuses() {
      return this.datasetStatus.statuses.sort((a, b) => {
        const statusA = DatasetStatus[a.status.toUpperCase()];
        const statusB = DatasetStatus[b.status.toUpperCase()];
        return statusA.order - statusB.order;
      });
    },
  },

  methods: {
    formatDuration(startedAt, updatedAt) {
      const start = DateTime.fromISO(startedAt);
      const end = DateTime.fromISO(updatedAt);
      const diff = end.diff(start, [
        'hours',
        'minutes',
        'seconds',
      ]);

      const formattedDuration = [];

      if (diff.hours) formattedDuration.push(`${diff.hours}h`);
      if (diff.minutes) formattedDuration.push(`${diff.minutes}m`);
      if (diff.seconds || (!diff.hours && !diff.minutes)) {
        formattedDuration.push(`${Math.round(diff.seconds)}s`);
      }

      return formattedDuration.join(' ');
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.dataset-status-details-cell-render {
  @include flex("block", "column", "start", "start");

  .status-item {
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 1rem;
    width: 100%;
  }
}
</style>
