<template>
  <section class="dataset-status-toolbar">
    <section class="dataset-status-actions">
    </section>
    <section class="dataset-status-pagination">
      <pagination
        :current-page="page"
        :last="datasetStatuses.length < this.rows"
        @onBtPrevious="onBtPrevious"
        @onBtNext="onBtNext"
        @onSetPage="onSetPage"
      />
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import DatasetStatusRequest from '@/services/DatasetStatusRequest';
import Pagination from '@/components/Pagination/pagination';

export default {
  name: 'dataset-status-toolbar',

  components: {
    BaseButton,
    Pagination,
  },

  computed: {
    ...mapState('datasetStatus', [
      'datasetStatuses',
      'page',
      'rows',
    ]),
  },

  methods: {
    ...mapActions('datasetStatus', ['setPage']),

    onBtPrevious() {
      this.setPage(this.page - 1);
      DatasetStatusRequest.fetchStatuses();
    },

    onBtNext() {
      this.setPage(this.page + 1);
      DatasetStatusRequest.fetchStatuses();
    },

    onSetPage(page) {
      this.setPage(page);
      DatasetStatusRequest.fetchStatuses();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.dataset-status-toolbar {
  @include flex("block", "row", "start", "start");

  margin-top: 0.6rem;
  width: 100%;

  .dataset-status-actions {
    @include flex("block", "row", "start", "start");
  }

  .dataset-status-pagination {
    @include flex("block", "row", "end", "start");

    padding-right: 2rem;
    width: 100%;
  }
}
</style>
