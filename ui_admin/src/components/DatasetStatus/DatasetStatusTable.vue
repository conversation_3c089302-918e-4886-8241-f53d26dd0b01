<template>
  <section class="dataset-status-table">
    <ag-grid-vue
      style="width: 100%; height: 100%"
      class="ag-theme-alpine"
      :columnDefs="columnDefs"
      :rowData="rowData"
      :rowClassRules="rowClassRules"
      rowSelection='multiple'
      @filter-changed="onFilterChanged"
      @grid-ready="onGridReady"
    />
  </section>
</template>

<script>
import { AgGridVue } from 'ag-grid-vue3';
import { DateTime } from 'luxon';
import { mapActions, mapState } from 'vuex';

import DatasetStatusDetailsCellRender from '@/components/DatasetStatus/DatasetStatusDetailsCellRender';
import DatasetStatusRequest from '@/services/DatasetStatusRequest';
import DateTimeRenderer from '@/components/BaseAgGrid/DateTimeRenderer';

export default {
  name: 'dataset-status-table',

  components: {
    AgGridVue,
  },

  data() {
    return {
      gridApi: null,
      rowClassRules: null,
    };
  },

  computed: {
    ...mapState('datasetStatus', ['datasetStatuses']),

    columnDefs() {
      return [
        {
          id: 'Dataset ID',
          field: 'datasetId',
          width: 115,
          wrapText: true,
          autoHeight: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
            filterOptions: [
              {
                displayName: 'Separate ids by ,',
                displayKey: 'contains',
                predicate() {
                  return true;
                },
                numberOfInputs: 1,
              },
            ],
          },
        },
        {
          headerName: 'Label',
          field: 'label',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: ['contains'],
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
        },
        {
          headerName: 'Status',
          field: 'status',
          width: 100,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Status Details',
          field: 'status',
          width: 260,
          wrapText: true,
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: DatasetStatusDetailsCellRender };
          },
        },
        {
          headerName: 'Total Time',
          field: 'totalTime',
          width: 100,
          wrapText: true,
          autoHeight: true,
          cellStyle: { 'font-weight': '700' },
        },
        {
          headerName: 'Documents',
          field: 'documentCount',
          width: 120,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Upload Start',
          field: 'uploadStart',
          width: 120,
          wrapText: true,
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: DateTimeRenderer };
          },
        },
      ];
    },

    rowData() {
      return this.datasetStatuses.map(o => {
        const totalDuration = o.statuses.reduce((sum, status) => {
          const start = DateTime.fromISO(status.startedAt);
          const end = DateTime.fromISO(status.updatedAt);
          return sum + end.diff(start, 'seconds').seconds;
        }, 0);

        const hours = Math.floor(totalDuration / 3600);
        const minutes = Math.floor((totalDuration % 3600) / 60);
        const seconds = Math.round(totalDuration % 60);

        const formattedDuration = [];
        if (hours > 0) formattedDuration.push(`${hours}h`);
        if (minutes > 0) formattedDuration.push(`${minutes}m`);
        if (seconds > 0 || (hours === 0 && minutes === 0)) formattedDuration.push(`${seconds}s`);

        return {
          ...o,
          totalTime: formattedDuration.join(' '),
        };
      });
    },
  },

  beforeMount() {
    DatasetStatusRequest.fetchStatuses();
  },

  beforeUnmount() {
    this.reset();
  },

  methods: {
    ...mapActions('datasetStatus', [
      'reset',
      'setFilterDatasetIds',
      'setFilterLabel',
    ]),

    onFilterChanged(e) {
      const model = e.api.getFilterModel();
      this.setFilterLabel(model.label?.filter || '');
      this.setFilterDatasetIds(model.datasetId?.filter || '');

      DatasetStatusRequest.fetchStatuses();
    },

    onGridReady(params) {
      this.gridApi = params.api;
      this.rowClassRules = {
        'table-danger': 'data.error',
        'table-primary': 'data?.status !== \'finished\'',
        'table-filter': 'data?.status === \'filtered\'',
      };
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.dataset-status-table {
  @include flex("block", "column", "start", "start");

  height: 100%;
  margin-top: 0.6rem;
  width: 100%;

  .table-primary {
    background-color:#b8daff;
  }

  .table-danger {
    background-color:#f5c6cb;
  }

  .table-filter {
    background-color: #98e1be;
  }
}
</style>
