<template>
  <section class="user-tier-details-modal">
    <section class="header">
      <h2>{{label}}</h2>
    </section>
    <section class="body">
      <section class="field row">
        <span>Name</span>
        <base-input placeholder="Enter a tier name" :value="localTier.name" @input="onInputName"/>
      </section>
      <section class="field row">
        <span>User Count</span>
        <base-input placeholder="Enter a user count" :type="'number'" :value="localTier.userCount" @input="onInputUserCount"/>
      </section>
      <section class="field row">
        <span>Cost</span>
        <base-input placeholder="Enter cost" :type="'number'" :value="localTier.cost" @input="onInputCost"/>
      </section>
      <section class="field row">
        <span>Max Uploads</span>
        <base-input placeholder="Enter Max Uploads" :type="'number'" :value="localTier.maxUploads" @input="onInputMaxUploads"/>
      </section>
      <section class="field row">
        <span>Max Characters</span>
        <base-input placeholder="Enter Max Characters" :type="'number'" :value="localTier.maxChars" @input="onInputMaxChars"/>
      </section>
      <section class="field row">
        <span>Max Duration</span>
        <base-input placeholder="Enter Max Uploads" :type="'number'" :value="localTier.duration" @input="onInputDuration"/>
      </section>
      <section class="field row">
        <span>Max Comparable Datasets</span>
        <base-input placeholder="Enter Max Comparable Datasets" :type="'number'" :value="localTier.maxComparableData" @input="onInputMaxComparableData"/>
      </section>
      <section class="field row">
        <span>Max Translated Characters</span>
        <base-input placeholder="Enter Max Translated Characters" :type="'number'" :value="localTier.maxTranslatedChars" @input="onInputTranslatedChars"/>
      </section>
      <section class="field">
        <span>Product Features</span>
        <section class="features" v-for="(item, index) in productFeatures" :key="index" @input="onSelectFeature(item)">
          <base-checkbox  :value="item.checked"/>
          <section class="label">{{ item.feature }}</section>
        </section>
      </section>
    </section>
    <section class="footer">
      <base-button class="cancel" colour="light" size="small" type="link"  @click="onCancel">Cancel</base-button>
      <loading-blocks-overlay v-if="creating" />
      <section class="confirm" v-else>
        <base-button v-if="isEditMode" colour="base" size="small" :disabled="createDisabled" @click="onUpdate">
          <span>Update Tier</span>
        </base-button>
        <base-button v-else colour="base" size="small" :disabled="createDisabled" @click="onCreate">
          <span>Create Tier</span>
        </base-button>
      </section>

    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import BaseCheckbox from '@/components/Base/BaseCheckbox';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import VSelect from 'vue-select';
import userTierRequest from '@/services/UserTierRequest';
import UserTierToast from '@/components/Toast/UserTierToast';

export default {
  name: 'user-tier-details-modal',

  components: {
    BaseButton,
    BaseCheckbox,
    BaseInput,
    LoadingBlocksOverlay,
    VSelect,
  },

  computed: {
    ...mapState('userTier', ['selectedUserTier', 'features']),

    isEditMode() {
      return this.selectedUserTier != null;
    },

    createDisabled() {
      if (this.localTier.name.trim() === '') return true;
      return this.localTier.count <= 0;
    },

    label() {
      return this.isEditMode ? 'Update User Tier' : 'Create New User Tier';
    },

    productFeatures() {
      if (this.features && this.localTier.productFeatures) {
        return this.features.map(value => {
          return {
            feature: value.titleCase(),
            checked: this.localTier.productFeatures.includes(value.name),
            value,
          };
        });
      }
      return [];
    },

  },

  async created() {
    await this.fetchLocalTier();
  },

  beforeDestroy() {
    this.setSelectedUserTier({ tier: null });
  },

  data() {
    return {
      creating: false,
      localTier: {
        name: '',
        userCount: 0,
        cost: 0,
        maxUploads: 0,
        maxChars: 0,
        duration: 0,
        maxTranslatedChars: 0,
        maxComparableData: 0,
        productFeatures: [],
      },
    };
  },

  methods: {
    ...mapActions('modal', ['closeModal']),
    ...mapActions('toast', ['addToast']),
    ...mapActions('userTier', ['setSelectedUserTier']),

    async fetchLocalTier() {
      await userTierRequest.getProductFeatures();

      if (this.isEditMode) {
        this.localTier = this.selectedUserTier;
      }
    },

    onSelectFeature(e) {
      if (this.localTier.productFeatures.includes(e.value.name)) {
        this.localTier.productFeatures = this.localTier.productFeatures.filter(t => e.value.name !== t);
      } else {
        this.localTier.productFeatures.push(e.value.name);
      }
    },

    onInputName(e) {
      this.localTier.name = e.target.value;
    },

    onInputUserCount(e) {
      this.localTier.userCount = e.target.value;
    },

    onInputCost(e) {
      this.localTier.cost = e.target.value;
    },

    onInputMaxUploads(e) {
      this.localTier.maxUploads = e.target.value;
    },

    onInputMaxChars(e) {
      this.localTier.maxChars = e.target.value;
    },

    onInputDuration(e) {
      this.localTier.duration = e.target.value;
    },

    onInputMaxComparableData(e) {
      this.localTier.maxComparableData = e.target.value;
    },

    onInputTranslatedChars(e) {
      this.localTier.maxTranslatedChars = e.target.value;
    },

    async onUpdate() {
      if (this.createDisabled) {
        return;
      }
      await this.setSelectedUserTier({ tier: this.localTier });
      await userTierRequest.updateUserTier(this.localTier.id, this.localTier).then((tier) => {
        if (tier) {
          userTierRequest.getUserTiers();
          this.closeModal();
          this.addToast({
            toastComponent: {
              component: UserTierToast,
              id: 'user-tier-updated',
            },
            toastData: {
              label: this.localTier.name,
            },
          });
        }
      });
    },

    async onCreate() {
      if (this.createDisabled) {
        return;
      }

      await this.setSelectedUserTier({ tier: this.localTier });
      await userTierRequest.createUserTier(this.localTier).then((tier) => {
        if (tier) {
          userTierRequest.getUserTiers();
          this.closeModal();
          this.addToast({
            toastComponent: {
              component: UserTierToast,
              id: 'user-tier-created',
            },
            toastData: {
              label: this.localTier.name,
            },
          });
        }
      });
    },

    onCancel() {
      this.closeModal();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.user-tier-details-modal {
  @include modal;

  position: relative;

  .field {
    margin-bottom: 0.6rem;

    .features {
      @include flex('block', 'row', 'start', 'center');

      margin-top: 0.6rem;
      margin-left: 0.6rem;
    }
  }

  .row {
    display: grid;
    grid-template-columns: 40% 60%
  }

  .wrapper-select {
    height: 30px;
  }

  .v-select {
    background-color: clr('white');
    position: absolute;
    width: 500px;
  }
}
</style>
