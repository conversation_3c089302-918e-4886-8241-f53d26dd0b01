<template>
  <section class="user-tier-table">
    <section class="user-tier-toolbar">
      <section class="user-tier-actions">
        <base-button :size="'small'" @click="onClickCreate">
          <i class="fa-regular fa-plus icon"></i>
          Create Tier
        </base-button>
      </section>
      <section class="user-tier-pagination">
        <pagination :current-page="page" :last="last" @onBtPrevious="onBtPrevious" @onBtNext="onBtNext" @onSetPage="onSetPage"></pagination>
      </section>
    </section>
    <section class="user-tier-table-gird">
      <ag-grid-vue
        class="ag-theme-alpine"
        rowSelection='single'
        style="width: 100%; height: 100%"
        :columnDefs="columnDefs"
        :rowData="userTiersMap"
      />
    </section>
  </section>
</template>

<script>
import { AgGridVue } from 'ag-grid-vue3';
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import GridListCell from '@/components/Base/GridListCell';
import Pagination from '@/components/Pagination/pagination';
import ProductFeatures from '@/enum/product-features';
import UserTierActionCellRender from '@/components/UserTier/UserTierActionCellRender';
import UserTierDetailsModal from '@/components/UserTier/UserTierDetailsModal';
import userTierRequest from '@/services/UserTierRequest';

export default {
  name: 'user-tier-table',

  components: {
    BaseButton,
    Pagination,
    AgGridVue,
    GridListCell,
    UserTierActionCellRender,
  },

  data() {
    return {
      gridApi: null,
    };
  },

  computed: {
    ...mapState('userTier', [
      'userTiers',
      'rows',
      'page',
    ]),

    columnDefs() {
      return [
        {
          id: 'Id',
          field: 'id',
          width: 100,
          wrapText: true,
          autoHeight: true,
          headerCheckboxSelection: true,
          checkboxSelection: true,
        },
        {
          headerName: 'Tier Name',
          field: 'name',
          flex: 1,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'User Count',
          field: 'userCount',
          flex: 1,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Cost',
          field: 'cost',
          flex: 1,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Max Uploads',
          field: 'maxUploads',
          flex: 1,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Max Characters',
          field: 'maxChars',
          flex: 1,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Duration',
          field: 'duration',
          flex: 1,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Max Comparable Datasets',
          field: 'maxComparableData',
          flex: 1,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Max Translated Characters',
          field: 'maxTranslatedChars',
          flex: 1,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Product Features',
          field: 'productFeaturesMap',
          flex: 1.5,
          wrapText: true,
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: GridListCell };
          },
        },
        {
          headerName: 'Actions',
          field: '',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: UserTierActionCellRender };
          }
          ,
        },
      ];
    },

    userTiersMap() {
      return this.userTiers.map(o => ({
        ...o,
        productFeaturesMap: this.parseProductFeatures(o.productFeatures),
      }));
    },

    last() {
      return this.userTiers.length < this.rows;
    },
  },

  beforeMount() {
    this.doCallApiFetchUserTiers();
  },

  methods: {
    ...mapActions('userTier', [
      'setSelectedUserTier',
      'setUserTiers',
      'setPage',
    ]),

    ...mapActions('modal', ['setModal']),

    onClickCreate() {
      this.setSelectedUserTier({ tier: null });
      this.setModal(UserTierDetailsModal);
    },

    async doCallApiFetchUserTiers() {
      await userTierRequest.getUserTiers();
    },

    parseProductFeatures(productFeatures) {
      if (productFeatures?.length > 0) {
        return productFeatures.map(feature => {
          return ProductFeatures.enumValueOf(feature) != null ? ProductFeatures.enumValueOf(feature).titleCase() : null;
        });
      }
      return null;
    },

    onBtPrevious() {
      this.setPage({ page: this.page - 1 });
      this.doCallApiFetchUserTiers();
    },

    onBtNext() {
      this.setPage({ page: this.page + 1 });
      this.doCallApiFetchUserTiers();
    },

    onSetPage(page) {
      this.setPage({ page });
      this.doCallApiFetchUserTiers();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.user-tier-table {
  @include flex("block", "column", "start", "start");

  height: 100%;
  width: 100%;

  .user-tier-toolbar {
    @include flex("block", "row", "start", "start");

    margin-top: 0.6rem;
    width: 100%;

    .user-tier-actions {
      @include flex("block", "row", "start", "start");

      column-gap: 1rem;
      min-width: max-content;

      .icon {
        margin-right: 0.2em;
      }
    }

    .user-tier-pagination {
      @include flex("block", "row", "end", "start");

      width: 100%;
    }
  }

  .user-tier-table-gird {
    @include flex("block", "row", "start", "start");

    height: 100%;
    margin-top: 0.6rem;
    width: 100%;

    .title {
      @include flex("block", "row", "center", "start");

      font-size: 1.5rem;
      font-weight: $font-weight-bold;
    }

    .base-button {
      margin-left: auto;
    }
  }
}
</style>
