<template>
  <section class="user-tier-action-cell-render">
    <section class="item">
      <base-button :size="'small'" :colour="'dark'" @click="onClickUpdate">
        <i class="fa-regular fa-save icon"></i> Edit
      </base-button>
    </section>
    <section class="item">
      <base-button :size="'small'" :colour="'danger'" @click="onClickDelete">
        <i class="fa-regular fa-trash icon"></i> Delete
      </base-button>
    </section>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import UserTierDeleteModal from '@/components/UserTier/UserTierDeleteModal';
import UserTierDetailsModal from '@/components/UserTier/UserTierDetailsModal';

export default {
  name: 'user-tier-action-cell-render',

  components: {
    BaseButton,
    UserTierDeleteModal,
    UserTierDetailsModal,
  },

  computed: {
    userTier() {
      return this.params.data;
    },

  },

  methods: {
    ...mapActions('modal', ['setModal']),
    ...mapActions('userTier', ['setSelectedUserTier']),

    onClickUpdate() {
      this.setSelectedUserTier({ tier: this.userTier });
      this.setModal(UserTierDetailsModal);
    },

    async onClickDelete() {
      this.setSelectedUserTier({ tier: this.userTier });
      this.setModal(UserTierDeleteModal);
    },

  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";
.user-tier-action-cell-render {
  @include flex("block", "column", "start", "start");

  .item {
    @include flex("block", "row", "center", "center");

    border-radius: 4px;
    color: clr('white');
    height: 2rem;
    margin: 0.2rem;

    .base-button {
      height: 2rem;
    }

    .icon {
      margin-right: 0.2rem;
    }
  }
}
</style>
