<template>
  <section class="saved-action-list-table">
    <section class="saved-action-list-toolbar">
      <section class="saved-action-list-pagination">
        <pagination :current-page="page" :last="last" @onBtPrevious="onBtPrevious" @onBtNext="onBtNext" @onSetPage="onSetPage"></pagination>
      </section>
    </section>
    <section class="saved-action-list-gird">
      <ag-grid-vue
          style="width: 100%; height: 100%"
          class="ag-theme-alpine"
          :columnDefs="columnDefs"
          :rowData="savedActionListMap"
          rowSelection='single'
          @filter-changed="onFilterChanged"
      />
    </section>
  </section>
</template>

<script>
import { AgGridVue } from 'ag-grid-vue3';
import { mapActions, mapState } from 'vuex';

import Pagination from '@/components/Pagination/pagination';
import SavedActionListActionModal from '@/components/SavedActionList/SavedActionListCellRender';
import savedActionListRequest from '@/services/SavedActionListRequest';

export default {
  name: 'saved-action-list-table',

  components: {
    AgGridVue,
    Pagination,
  },

  data() {
    return {
      gridApi: null,
    };
  },

  computed: {
    ...mapState('savedActionList', [
      'savedActionLists',
      'rows',
      'page',
    ]),

    columnDefs() {
      return [
        {
          id: 'Id',
          field: 'id',
          width: 100,
          wrapText: true,
          autoHeight: true,
          checkboxSelection: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
            filterOptions: [
              {
                displayName: 'Separate ids by ,',
                displayKey: 'contains',
                predicate() {
                  return true;
                },
                numberOfInputs: 1,
              },
            ],
          },
        },
        {
          headerName: 'Workspace label',
          field: 'workspace',
          flex: 0.5,
          wrapText: true,
          autoHeight: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: ['contains'],
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
        },
        {
          headerName: 'User Name',
          field: 'user',
          flex: 0.5,
          wrapText: true,
          autoHeight: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: ['contains'],
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
        },
        {
          headerName: 'Collection Label',
          field: 'collection',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            filterOptions: ['contains'],
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
          },
        },
        {
          headerName: 'Action items',
          field: 'list',
          flex: 1,
          wrapText: true,
          autoHeight: true,
          filter: 'agTextColumnFilter',
          filterParams: {
            defaultOption: 'contains',
            maxNumConditions: 1,
            buttons: ['reset'],
            filterOptions: [
              {
                displayName: 'Separate ids by ,',
                displayKey: 'contains',
                predicate() {
                  return true;
                },
                numberOfInputs: 1,
              },
            ],
          },
        },
        {
          headerName: 'Is Default',
          field: 'default',
          flex: 1,
          wrapText: true,
          autoHeight: true,
        },
        {
          headerName: 'Actions',
          field: '',
          width: 200,
          wrapText: true,
          autoHeight: true,
          cellRendererSelector: () => {
            return { component: SavedActionListActionModal };
          },
        },
      ];
    },

    savedActionListMap() {
      return this.savedActionLists.map(o => ({
        ...o,
        user: o.user.loginName,
        workspace: o.workspace.label,
        collection: o.label,
      }));
    },

    last() {
      return this.savedActionLists.length < this.rows;
    },
  },

  beforeMount() {
    this.doCallApiFetchSavedActionLists();
  },

  methods: {
    ...mapActions('savedActionList', [
      'setFilterActionIds',
      'setFilterCollectionLabel',
      'setFilterIds',
      'setFilterName',
      'setFilterWorkspaceLabel',
      'setPage',
    ]),

    async doCallApiFetchSavedActionLists() {
      await savedActionListRequest.fetchSavedActionLists();
    },

    onGridReady(params) {
      this.gridApi = params.api;
    },

    onFilterChanged(e) {
      const model = e.api.getFilterModel();

      this.setFilterCollectionLabel(model.collection?.filter || '');
      this.setFilterName(model.user?.filter || '');
      this.setFilterWorkspaceLabel(model.workspace?.filter || '');
      this.setFilterIds('id' in model ? [model.id.filter.replaceAll(/[^0-9,]+/g, '')] : []);
      this.setFilterActionIds('list' in model ? [model.list.filter.replaceAll(/[^0-9,]+/g, '')] : []);

      this.doCallApiFetchSavedActionLists();
    },

    onBtPrevious() {
      this.setPage({ page: this.page - 1 });
      this.doCallApiFetchSavedActionLists();
    },

    onBtNext() {
      this.setPage({ page: this.page + 1 });
      this.doCallApiFetchSavedActionLists();
    },

    onSetPage(page) {
      this.setPage({ page });
      this.doCallApiFetchSavedActionLists();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.saved-action-list-table {
  @include flex("block", "column", "start", "start");

  height: 100%;
  width: 100%;

  .saved-action-list-toolbar {
    @include flex("block", "row", "start", "start");

    width: 100%;

    .saved-action-list-pagination {
      @include flex("block", "row", "end", "start");

      padding-right: 2rem;
      width: 100%;
    }
  }

  .saved-action-list-gird {
    @include flex("block", "row", "start", "start");

    height: 100%;
    padding: 1rem 0.6rem 0 0.6rem;
    width: 100%;

    .title {
      @include flex("block", "row", "center", "start");

      font-size: 1.5rem;
      font-weight: $font-weight-bold;
    }

    .base-button {
      margin-left: auto;
    }
  }
}
</style>
