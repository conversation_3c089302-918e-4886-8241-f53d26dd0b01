<template>
  <section class="saved-action-list-cell-render">
    <section class="item">
      <base-button :size="'small'" :colour="'dark'" @click="onClickCopy">
        <i class="fa-regular fa-copy icon"></i> Copy
      </base-button>
    </section>
    <section class="item">
      <base-button :size="'small'" :colour="'danger'" @click="onClickDelete">
        <i class="fa-regular fa-trash icon"></i> Delete
      </base-button>
    </section>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import SavedActionListCopyModal from '@/components/SavedActionList/SavedActionListCopyModal';
import SavedActionListDeleteModal from '@/components/SavedActionList/SavedActionListDeleteModal';

export default {
  name: 'saved-action-list-cell-render',

  components: {
    BaseButton,
  },

  computed: {
    savedActionList() {
      return this.params.data;
    },

  },

  methods: {
    ...mapActions('modal', ['setModal']),

    ...mapActions('toast', ['addToast']),

    ...mapActions('savedActionList', ['setSelectedSavedActionList']),

    onClickCopy() {
      this.setSelectedSavedActionList(this.savedActionList);
      this.setModal(SavedActionListCopyModal);
    },

    async onClickDelete() {
      await this.setSelectedSavedActionList(this.savedActionList);
      await this.setModal(SavedActionListDeleteModal);
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.saved-action-list-cell-render {
  @include flex("block", "column", "start", "start");

  .item {
    @include flex("block", "row", "center", "center");

    border-radius: 4px;
    color: clr('white');
    height: 2rem;
    margin: 0.2rem;

    .base-button {
      height: 2rem;
    }

    .icon {
      margin-right: 0.2rem;
    }
  }
}
</style>
