<template>
  <section class="copy-saved-action-list-modal">
    <section class="header">
      Copy Collection
    </section>
    <section class="body">
      <section class="row field">
        <span>Select User</span>
        <section class="wrapper-select">
          <v-select v-model="localUser"
                    class="v-select"
                    :options="userOptions"
                    :searchable="true"
                    @search="fetchUsers"
                    @option:selected="fetchWorkspaces"
                    @option:deselected="workspaceOptions = []"
          />
        </section>
      </section>
      <section class="row field">
          <span> Select workspace</span>
          <section class="wrapper-select">
            <v-select v-model="localWorkspace"
                      class="v-select"
                      :options="workspaceOptions"
                      :searchable="true"
            />
          </section>
      </section>
      <section class="row field">
        <span>Collection Label</span>
        <base-input placeholder="Enter a collection label" @input="onInputLabel" :value="localLabel"/>
      </section>
    </section>
    <section class="footer">
      <base-button class="cancel" colour="light" type="link" @click="closeModal">Cancel</base-button>
      <base-button class="delete" colour="danger" @click="onCopy">Copy</base-button>
    </section>
  </section>

</template>
<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import BaseRadio from '@/components/Base/BaseRadio';
import SavedActionListToast from '@/components/Toast/SavedActionListToast';
import VSelect from 'vue-select';
import savedActionListRequest from '@/services/SavedActionListRequest';
import userRequest from '@/services/UserRequest';
import { debounce } from 'lodash-es';

export default {
  name: 'saved-action-list-copy-modal',
  components: { BaseInput, VSelect, BaseRadio, BaseButton },

  computed: {
    ...mapState('organisation', ['organisations']),
    ...mapState('user', ['users']),
    ...mapState('savedActionList', ['selectedSavedActionList']),

  },

  beforeDestroy() {
    this.resetFilterWorkspace();
    this.resetFilterUser();
  },

  created() {
    this.localLabel = this.selectedSavedActionList.label;
  },

  data() {
    return {
      localUser: null,
      localWorkspace: null,
      localLabel: '',
      workspaceOptions: [],
      userOptions: [],
    };
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('toast', ['addToast']),

    ...mapActions('user', { setFilterUser: 'setSearch', resetFilterUser: 'reset' }),

    ...mapActions('workspace', { setFilterWorkspace: 'setSearch', setFilterOwner: 'setFilterOwner', resetFilterWorkspace: 'reset' }),

    fetchUsers: debounce(async function fetchWorkspaces(search, loading) {
      await this.setFilterUser(search);
      await this.doCallApiFetchUsers();
      loading(false);
    }, 500),

    fetchWorkspaces() {
      this.doCallApiFetchWorkspaces();
    },

    async doCallApiFetchWorkspaces() {
      this.workspaceOptions = (this.localUser.workspaces).map(w => ({
        id: w.id,
        label: `${w.label} (Id: ${w.id} )`,
      }));
    },

    async doCallApiFetchUsers() {
      this.userOptions = (await userRequest.getUsers()).map(u => ({
        ...u,
        id: u.id,
        label: `${u.loginName} (Id: ${u.id} )`,
        selectable: u.id !== this.selectedSavedActionList.user.id,
      }));
    },

    async onCopy() {
      await savedActionListRequest.copySavedActionList(this.selectedSavedActionList.id, this.localUser.id, this.localWorkspace.id, this.localLabel);
      await savedActionListRequest.fetchSavedActionLists();
      await this.closeModal();
      await this.addToast({
        toastComponent: {
          component: SavedActionListToast,
          id: 'collection-copied',
        },
        toastData: {
          label: this.localLabel,
        },
      });
    },

    onInputLabel(e) {
      this.localLabel = e.target.value;
    },

    isWorkspaceSelectable(option) {
      return this.localUser.workspaces.map(w => w.id).includes(option.id);
    },
  },
};
</script>
<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";
.copy-saved-action-list-modal {
  @include modal;
  position: relative;

  .field {
    margin-bottom: 0.6rem;
  }

  .row {
    display: grid;
    grid-template-columns: 40% 60%
  }

  .wrapper-select {
    height: 30px;
  }

  .v-select {
    background-color: clr('white');
    width: 400px;
  }
}
</style>
