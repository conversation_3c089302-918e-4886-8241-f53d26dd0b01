/*
 *  Utilities for constructing and accessing the app colour palette.
 *  Define new app colours here and access them with the "clr" function
 */

// Colour map

$emotics-colours: (
  "red": (
    "base": #e41a1a,
    "badText": darken(#ff4343, 20%),
    "veryPoor": #d16258
  ),
  "yellow": (
    "base": #ff9100,
    "neutralText": #ff9100
  ),
  "orange": (
    "base": #fb9137
  ),
  "green": (
    "base": #35d61a,
    "goodText": darken(#35d61a, 10%),
    "veryGood": #48923f,
    "light": #039b00,
    "dark": #186943,
    "mint": #77f7d8
  ),
  "blue": (
    "base": #0083ff,
    "deep": #4D52EF,
    "dark": #3c3f4f,
    "darkgray": #667180,
    "gray": #5f6771,
    "light": #dae1e9,
    "lighter": #eef2f8
  ),
  "purple": (
    "base": #a15aef,
    "rich": #6641ad,
    "dark": #281750,
    "darker": #291d3c,
    "gray": #020202,
    "light": #aba6b4,
    "lighter": #f9f8ff
  ),
  "white": (
    "base": #ffffff
  ),
  "grey": (
    "base": #cccccc,
    "10": #191919,
    "20": #333333,
    "30": #4c4c4c,
    "40": #666666,
    "50": #7f7f7f,
    "60": #999999,
    "70": #b2b2b2,
    "80": #cccccc,
    "90": #e5e5e5,
    "100": #f9f9f9
  ),
  "black": (
    "base": #000000
  )
);

// Sass function for retrieving colours

@function clr($name: "white", $variant: "base") {
  $color: null;

  // Get the color spectrum
  $color-spectrum: map-get($emotics-colours, $name);

  // Get the color variant
  @if $color-spectrum {
    $color: map-get($color-spectrum, $variant);
  }

  @return $color;
}

// Helper classes for quick colour setting - base colours don't need the "base" suffix

@each $spectrum-key, $spectrum-map in $emotics-colours {
  @each $variant-key, $variant-value in $spectrum-map {
    @if $spectrum-map {
      @if $variant-key == "base" {
        .text-#{$spectrum-key} {
          color: $variant-value;
        }
        .bg-#{$spectrum-key} {
          background-color: $variant-value;
        }
        .border-#{$spectrum-key} {
          border-color: $variant-value;
        }
        .border-top-#{$spectrum-key} {
          border-top-color: $variant-value;
        }
        .border-right-#{$spectrum-key} {
          border-right-color: $variant-value;
        }
        .border-bottom-#{$spectrum-key} {
          border-bottom-color: $variant-value;
        }
        .border-left-#{$spectrum-key} {
          border-left-color: $variant-value;
        }
      } @else {
        .text-#{$spectrum-key}-#{$variant-key} {
          color: $variant-value;
        }
        .bg-#{$spectrum-key}-#{$variant-key} {
          background-color: $variant-value;
        }
        .border-#{$spectrum-key}-#{$variant-key} {
          border-color: $variant-value;
        }
        .border-top-#{$spectrum-key}-#{$variant-key} {
          border-top-color: $variant-value;
        }
        .border-right-#{$spectrum-key}-#{$variant-key} {
          border-right-color: $variant-value;
        }
        .border-bottom-#{$spectrum-key}-#{$variant-key} {
          border-bottom-color: $variant-value;
        }
        .border-left-#{$spectrum-key}-#{$variant-key} {
          border-left-color: $variant-value;
        }
      }
    }
  }
}
