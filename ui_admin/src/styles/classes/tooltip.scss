@import "src/styles/variables";

.vue-tooltip {
  background-color: clr("white");
  box-shadow: $box-shadow-hover-lg;
  color: $body-copy;
  font-size: $font-size-sm;
  padding: 0;
  border-color: $cfp-main-select-box-bdr;
  z-index: 9999;

  &.tooltip-analysis-icon {
    top: 2rem;
  }

  &.tooltip-base {
    font-size: $font-size-xs;
    padding: 0.2rem 0.5rem;

    .tooltip-arrow {
      border-color: clr("white");
    }
  }

  &.tooltip-base-dark {
    background-color: clr("grey", "20");
    color: clr("white");
    font-size: $font-size-xs;
    padding: 0.2rem 0.5rem;

    .tooltip-arrow {
      border-color: clr("grey", "20");
    }
  }

  &.tooltip-base-drag {
    background-color: clr("grey", "20");
    transition: all 1.5s linear;
    color: clr("white");
    font-size: $font-size-xs;
    padding: 0.2rem 0.5rem;

    .tooltip-arrow {
      border-color: clr("grey", "20");
    }
  }

  &.tooltip-base-tag {
    background-color: clr("grey", "20");
    color: clr("white");
    font-size: $font-size-xs;
    padding: 0.2rem 0.5rem;
    word-wrap: break-word;

    .tooltip-arrow {
      border-color: clr("grey", "20");
    }
  }

  &.tooltip-base-dropdown {
    border: $border-standard;

    .tooltip-arrow {
      border-color: $border-color;
    }
  }

  &.tooltip-base-metadata-item {
    background-color: clr("grey", "50");
    border: $border-standard;
    color: clr("white");
    font-size: $font-size-xs;
    max-width: 400px;
    padding: 0.3rem 0.5rem;

    .tooltip-arrow {
      border-color: $border-color;
    }
  }

  &.tooltip-clear-search {
    background-color: clr("grey", "30");
    color: clr("white");
    font-size: $font-size-xs;
    padding: 0.5rem;

    .tooltip-arrow {
      border-bottom-color: clr("grey", "30") !important;
    }
  }

  &.tooltip-dataset-controls {
    background-color: clr("grey", "30");
    border-radius: $border-radius-medium;
    color: white;
    font-size: $font-size-xs;
    padding: 0.5rem;
    margin-top: 0.3rem;

    .tooltip-arrow {
      display: none;
    }
  }

  &.tooltip-dataset-item-upper-panel-info-list-tooltip {
    background-color: clr("white");
    border: $border-standard;
    padding: 0.6rem 1rem;

    .tooltip-arrow {
      display: none;
    }
  }

  &.tooltip-dataset-name {
    background-color: clr("grey", "20");
    border: $border-standard;
    color: clr("white");
    font-size: $font-size-xs;
    margin: 1rem 0;
    max-width: 250px;
    padding: 0.5rem 1rem;

    .tooltip-arrow {
      border-color: clr("grey", "20");
    }
  }

  &.tooltip-dataset-selector {
    background-color: clr("white");
    border: $border-standard;
    margin-top: 1rem;

    .tooltip-arrow {
      border-color: clr("white");
    }
  }

  &.tooltip-dataset-selector-tag {
    background-color: clr("white");
    border: $border-standard;
    padding: 0.6rem 0.7rem;
    margin-top: 0.5rem;

    .tooltip-arrow {
      display: none;
    }
  }

  &.tooltip-datepicker {
    @import '~vue-ctk-date-time-picker/dist/vue-ctk-date-time-picker.css';

    margin-top: 1rem;

    .tooltip-arrow {
      display: none;
    }

    .tooltip-content {
      margin-left: -0.5rem;
    }
  }

  &.tooltip-filter-button {
    background-color: $cfp-top-bar-bg;
    color: clr("white");
    font-size: $font-size-xs;
    padding: 0.15rem;

    .tooltip-arrow {
      border-color: $cfp-top-bar-bg;
    }
  }

  &.tooltip-insights-dropdown {
    border: $border-standard;

    .tooltip-arrow {
      display: none;
    }
  }

  &.tooltip-insights-label {
    padding: 0.25rem;
  }

  &.tooltip-insights-modal-save-changes {
    background-color: clr("grey", "30");
    border: $border-standard;
    color: clr("white");
    padding: 0.5em;
    font-size: $font-size-xs;
    max-width: 400px;

    .tooltip-arrow {
      border-top-color: clr("grey", "30") !important;
    }
  }

  &.tooltip-insights-top-indicators-dropdown {
    max-width: 500px;
  }

  &.tooltip-merge-marger {
    background-color: clr("blue", "dark");
    color: clr("white");
    font-size: $font-size-xxs;
    margin-left: 0;
    padding: 0.3rem;

    .tooltip-arrow {
      display: none;
    }
  }

  &.tooltip-padding-025 {
    padding: 0.25rem;
  }

  &.tooltip-paged-modal-node {
    background-color: clr("blue", "dark");
    color: clr('white');
    font-size: $font-size-xs;
    padding: 5px;
    margin-top: 1rem;

    .tooltip-arrow {
      border-color: transparent;
      border-width: 5px;
      margin-top: 5px;
      width: 10px;
    }
  }

  &.tooltip-radial-score {
    background-color: clr("blue", "dark");
    margin-top: 0.4rem;
    padding: 0.5rem;

    .tooltip-arrow {
      border-color: transparent;
      border-bottom-color: clr("blue", "dark");
      margin-top: -14px;
    }

    .text,
    .title {
      color: clr("white") !important;
    }

    .value {
      color: clr("white") !important;
      font-weight: $font-weight-bold !important;
      margin-left: 1rem;
    }
  }

  &.tooltip-reassign-action {
    background-color: clr("white");
    border: $border-standard;
    padding: 0.6rem 1rem;
    margin-top: 0.5rem;

    .tooltip-arrow {
      display: none;
    }
  }

  &.tooltip-results-actions {
    background-color: clr("white");
    border: $border-standard;
    box-shadow: rgba(0, 0, 0, 0.5) 0px 2px 4px;
    padding: 0.6rem 1rem;
    margin-top: 0.1rem;

    .tooltip-arrow {
      display: none;
    }
  }

  &.tooltip-results-actions-reassign {
    margin-top: 0.1625rem;
  }

  &.tooltip-saved-search-controls {
    background-color: clr("blue", "dark");
    color: clr("white");
    padding: 0.5rem;
    font-size: $font-size-xs;

    .tooltip-arrow {
      border-color: clr("blue", "dark");
    }
  }

  &.tooltip-search-bar-save {
    background-color: clr("blue", "dark");
    color: clr('white');
    padding: 5px;
    margin-top: 1rem;

    .tooltip-arrow {
      border-bottom-color: clr("blue", "dark") !important;
      border-color: transparent;
      border-width: 5px;
      margin-top: -15px;
      width: 10px;
    }
  }

  &.tooltip-search-datasets {
    background-color: clr("blue", "dark");

    .tooltip-arrow {
      border-bottom-color: clr("blue", "dark");
    }
  }

  &.tooltip-search-query-item {
    background-color: clr("blue", "dark");
    color: clr("white");
    padding: 0.5rem;
    font-size: $font-size-xs;

    .tooltip-arrow {
      border-left-color: clr("blue", "dark");
    }
  }

  &.tooltip-send-to-chart {
    background-color: clr("blue", "dark");
    color: clr("white");
    padding: 0.5rem;
    font-size: $font-size-xs;

    .tooltip-arrow {
      border-color: clr("blue", "dark");
    }
  }

  &.tooltip-stopwords-button {
    background-color: clr("grey", "30");
    border: $border-standard;
    color: clr("white");
    padding: 0.5em;
    font-size: $font-size-xs;

    .tooltip-arrow {
      border-top-color: clr("grey", "30") !important;
    }
  }

  &.tooltip-themes-sort {
    border: $border-standard;
    padding: 0.7em;

    .tooltip-arrow {
      border-color: $border-color;
    }

    h2 {
      font-size: $font-size-sm;
    }

    p {
      font-size: $font-size-xs;
    }
  }

  &.tooltip-upload-dataset-help {
    border: $border-standard;
    margin-top: 0.5rem;
    max-width: 400px;

    .tooltip-arrow {
      display: none;
    }
  }
}
