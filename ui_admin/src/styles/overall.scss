$imported-modules: () !default;

/**
 * Module export mixin
 * This mixin helps making sure a module is imported once and only once.
 *
 * @access public
 * @param {String} $name - Name of exported module
 * @param {Bool} $warn [true] - Warn when module has been already imported
 * @require $imported-modules
 */
@mixin exports($name, $warn: true) {
  @if (index($imported-modules, $name) == null) {
    $imported-modules: append($imported-modules, $name) !global;
    @content;
  } @else if $warn == true {
    @warn "Module `#{$name}` has already been imported.";
  }
}

@import "classes";
@import "mixins";
@import "rules";
@import "variables";

html,
body {
  color: $body-copy;
  box-sizing: border-box;
  font-family: $font-stack;
  font-weight: $font-weight-normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

input,
textarea,
select {
  font-family: inherit;
  font-weight: inherit;
  font-size: inherit;

  &::placeholder {
    color: $body-copy-light;
  }
  &::-webkit-input-placeholder {
    color: $body-copy-light;
  }
  &::-moz-placeholder {
    color: $body-copy-light;
  }
  &::-ms-input-placeholder {
    color: $body-copy-light;
  }
  &:-moz-placeholder {
    color: $body-copy-light;
  }
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.-left {
  float: left;
}

.-right {
  float: right;
}

.-clear:after {
  content: "";
  display: table;
  clear: both;
}

.form-intro {
  text-align: center;
  font-size: 0.875rem;
  opacity: 0.5;
  margin-bottom: 2em;
  display: block;
}

.logomark {
  display: block;
  margin: 3em auto;
  text-align: center;
}
