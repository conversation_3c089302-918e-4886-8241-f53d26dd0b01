// Border thickness

$border-light: 1px;
$border-medium: 3px;
$border-heavy: 4px;

// Border Radius rounding amount

$border-radius-small: 2px;
$border-radius-medium: 4px;
$border-radius-large: 8px;
$border-radius-xlarge: 14px;
$border-radius-rounded: 100px;

// Per-component sizing
$action-bar-height: 60px;

$app-min-width: 1000px;

$header-height: 55px;
$header-menu-width: 80px;
$header-menu-item-height: 40px;
$header-menu-item-width: 200px;

$content-height: calc(100vh - #{$header-height});

$dataset-insight-padding: 1rem;
$dataset-insight-width: 380px;

$operator-tag-base-width: 2.6rem;
$operator-tag-light-width: 1.8rem;

$panel-margin: 1em;

$results-chart-axis-buffer: 16px;
$results-chart-bar-margin: 2px;
$results-chart-bar-max-width: 25px;
$results-chart-bars-margin: 1em;
$results-chart-height: 200px;
$results-chart-margin: 1em;
$results-dataset-height: 120px;
$results-dataset-width: $results-dataset-height;
$results-index-score-height: 36px;
$results-index-score-width: $results-index-score-height;

$search-modal-padding: 1.5rem 1.5rem;
$search-bar-height: 40px;
$search-controls-height: 3.2rem;

$score-section-height: 350px;
$score-card-width: 250px;

$sidebar-padding: 0.5rem;
$sidebar-score-height: 32px;
$sidebar-score-width: $sidebar-score-height;
$sidebar-width: 300px;

$swot-card-width: 200px;
$swot-card-height: $swot-card-width;
$swot-card-margin: 1.5em;

$tabbed-panel-tab-height: 35px;

$theme-chart-height: 600px;

$theme-table-checkbox-padding: 0.7rem;
$theme-table-count-width: 80px;
$theme-table-score-width: 200px;

$themes-item-height: 38px;

$themes-width: 250px;
$themes-width-expanded: 500px;

$swot-sort-menu-height: 50px;
$swot-sort-menu-width: 100px;
$swot-sort-menu-item-height: 30px;
$swot-sort-menu-item-width: 100px;
$swot-dataset-score-height: 36px;
$swot-dataset-score-width: $swot-dataset-score-height;

$terms-height: calc(100vh - 200px);

$upload-dropzone-border-radius: 2em;

$font-size-xxxs: 0.525rem;
$font-size-xxs: 0.625rem; //10px
$font-size-xs: 0.75rem; //12px
$font-size-sm: 0.875rem; //14px
$font-size-base: 1rem; //16px
$font-size-md: 1.125rem; //18px
$font-size-lg: 1.25rem; //20px
$font-size-xl: 2rem; //32px

$letter-spacing-xs: 0.4px;
$letter-spacing-sm: 0.8px;
$letter-spacing-base: 1px;
