@import './colors';
@import './sizing';

// Font family

$font-stack: 'Inter','Gotham SSm A', 'Gotham SSm B', 'Helvetica', 'Arial', sans-serif;

$font-weight-light: 200;
$font-weight-semi-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-bold: 700;

$box-shadow-base: 0 1px 2px 0 rgba(0, 0, 0, 0.07);
$box-shadow-hover: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
$box-shadow-search: 0 0 0 2px rgba(0, 71, 255, 0.21);

$box-shadow-hover-lg: 0 3px 10px 0 rgba(0, 0, 0, 0.1);

$border-standard: $border-light solid $border-color;

$border-dark: $border-light solid $border-color-dark;

$border-purple: $border-light solid $border-color-purple;

$dataset-insight-box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
