@mixin panel {
  border: $border-light solid $border-color;
  border-radius: $border-radius-medium;
  background-color: clr('white');
  box-shadow: $box-shadow-base;

  &:hover {
    box-shadow: $box-shadow-hover;
  }
}

@mixin panel-dropdown {
  background-color: clr("white");
  border-radius: $border-radius-medium;
  border: $border-light solid $border-purple;
  box-shadow: 0 0 8px rgb(156 189 255 / 40%);
  color: clr('black');
  position: absolute;
  z-index: 999;

  &:hover {
    box-shadow: $box-shadow-hover;
  }
}
