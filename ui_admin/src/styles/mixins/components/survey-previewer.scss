@mixin survey-question-range-small-browser($btn-border,$font-color,$btn-color,$span-color) {
  
}

@mixin survey-modal($color: $base-btn-bg, $font-color: $base-btn-bg) {
  .body {
    @include flex('block', 'column', 'start', 'center');

    background: #FFFFFF;
    row-gap: 2rem;

    .button {
      .btn-submit {
        background-color: $color;

        .base-button {
          border-radius: 2px;

          span {
            color: #FFFFFF;
          }
        }

        &:hover,
        &:focus {
          background-color: lighten($color, 8%);
        }
      }
    }
  }

  .footer {
    @include flex('block', 'column', 'start', 'center');

    color: $base-btn-bg;
    font-size: $font-size-xxs;
    row-gap: 1rem;

    .copy-right-contact {
      .base-form-link {
        opacity: 1;
        text-decoration: underline;
      }
    }
  }

  .header {
    @include flex('block', 'column', 'space-between', 'start');

    border-bottom: none;
    color: $color;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 17px;

    .logos {
      margin-bottom: 2rem;

      img {
        max-width:150px;

        &:first-child {
          margin-right:1.3rem;
        }

        &.logo-emotics {
          width:90px;
        }
      }
    }

    .title-description {
      display: inline-block;
      margin-top: 1rem;
      text-align: justify;

      span {
        align-items: center;
        color:$font-color;
        font-weight: $font-weight-normal;
        font-size: $font-size-xs;
      }
    }

    .title-name {
      span {
        color: $font-color;
        font-weight: 700;
        font-size: $font-size-md;
      }
    }
  }

  &.is-mobile {
    .header .title-description {
      text-align: left;
    }
  }
}

@mixin survey-message($font-color) {
  .description {
    @include flex('block', 'column', 'center', 'center');

    color: $font-color;
    font-size: $font-size-lg;
    width: 100%;
  }
}
