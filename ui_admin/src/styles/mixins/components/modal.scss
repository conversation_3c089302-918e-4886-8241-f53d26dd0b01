@import './panel';
@import '../layout/flex';

@mixin modal {
  @include flex('block', 'column', 'center', 'stretch');
  @include panel;

  width: 800px;

  .header {
    @include flex('block', 'column', 'center', 'center');
    @include rigid;

    border-bottom: $border-light solid $border-color;
    padding: 1rem;
    text-align: center;
  }

  .body {
    background-color: clr('purple', 'lighter');
    max-height: 500px;
    overflow-y: auto;
    padding: 1.5em;
  }

  .footer {
    @include flex('block', 'row', 'between', 'center');
    @include rigid;

    border-top: $border-light solid $border-color;
    padding: 1rem 1.5em;

    .base-button {
      font-size: 0.65em;
      font-weight: 600;
      text-transform: uppercase;
    }
  }
}

@mixin modal-header {
  @include flex("block", "row", "between", "center");

  padding: 1rem 1.5rem;

  .left {
    @include flex("block", "row", "start", "center");

    .icon {
      margin-right: 0.5rem;
    }

    h2 {
      font-size: $font-size-base;
      font-weight: $font-weight-bold;
    }
  }

  .right {
    @include flex("block", "row", "end", "center");

    .icon {
      color: $body-copy-light;
      cursor: pointer;

      &:hover {
        color: $body-copy;
      }
    }
  }
}