@mixin input-tag {
  .vue-input-tag-wrapper {
    appearance: none;
    border: $border-standard;
    border-radius: $border-radius-medium;
    min-height: 33px;
    outline: none;
    width: 100%;

    &.read-only {
      background-color: clr("blue", "lighter");
    }

    .new-tag,
    .input-tag {
      color: $body-copy;
    }

    .input-tag {
      @include flex("block", "row", "center", "center");

      background-color: clr("purple", "lighter");
      border: $border-standard;
      border-radius: $border-radius-medium;
      margin: 0 0.5em 0.3em 0;

      span {
        margin: 0.2em 0.5em;
        position: relative;
      }

      .remove {
        position: relative;

        &::before {
          transition: $transition-base;
          color: $body-copy-light;
          content: "✕";
          margin-right: 0.2rem;
        }
        &:hover::before {
          color: $body-copy;
        }
      }
    }
  }
}
