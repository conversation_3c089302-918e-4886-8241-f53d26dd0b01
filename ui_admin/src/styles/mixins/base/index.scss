@mixin truncate() {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

@mixin small-header() {
  color: $body-copy-light;
  font-size: $font-size-xxs;
  font-weight: $font-weight-medium;
  letter-spacing: $letter-spacing-base;
  text-transform: uppercase;
}

@mixin hover-element-dropdown($isFirst: false, $isLast: false) {
  &:hover{
    background-color: rgba(156, 189, 255, 0.3);
    cursor: pointer;
    font-weight: $font-weight-bold;
  }

  &:first-child {
    border-top-left-radius: $border-radius-medium;
    border-top-right-radius: $border-radius-medium;
  }

  &:last-child {
    border-bottom-left-radius: $border-radius-medium;
    border-bottom-right-radius: $border-radius-medium;
  }

  @if $isFirst {
    border-top-left-radius: $border-radius-medium;
    border-top-right-radius: $border-radius-medium;
  }

  @if $isLast {
    border-bottom-left-radius: $border-radius-medium;
    border-bottom-right-radius: $border-radius-medium;
  }
}
