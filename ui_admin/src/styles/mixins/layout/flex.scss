@mixin flex(
  $display: "flex",
  $direction: "row",
  $justify: "flex-start",
  $align: "flex-start",
  $wrap: "nowrap",
  $aligncontent: "stretch"
) {
  @if $display == "flex" or $display == "block" {
    display: flex;
  }
  @if $display == "inline-flex" or $display == "inline" {
    display: inline-flex;
  }

  @if $direction == "row" {
    flex-direction: row;
  }
  @if $direction == "r-reverse" {
    flex-direction: row-reverse;
  }
  @if $direction == "row-reverse" {
    flex-direction: row-reverse;
  }
  @if $direction == "row-r" {
    flex-direction: row-reverse;
  }
  @if $direction == "column" {
    flex-direction: column;
  }
  @if $direction == "c-reverse" {
    flex-direction: column-reverse;
  }
  @if $direction == "column-reverse" {
    flex-direction: column-reverse;
  }
  @if $direction == "column-r" {
    flex-direction: column-reverse;
  }

  @if $justify == "flex-start" or $justify == "start" {
    justify-content: flex-start;
  }
  @if $justify == "flex-end" or $justify == "end" {
    justify-content: flex-end;
  }
  @if $justify == "center" {
    justify-content: center;
  }
  @if $justify == "space-between" or $justify == "between" {
    justify-content: space-between;
  }
  @if $justify == "space-around" or $justify == "around" {
    justify-content: space-around;
  }
  @if $justify == "space-evenly" or $justify == "evenly" or $justify == "even" {
    justify-content: space-evenly;
  }

  @if $align == "flex-start" or $align == "start" {
    align-items: flex-start;
  }
  @if $align == "flex-end" or $align == "end" {
    align-items: flex-end;
  }
  @if $align == "center" {
    align-items: center;
  }
  @if $align == "stretch" {
    align-items: stretch;
  }
  @if $align == "baseline" {
    align-items: baseline;
  }

  @if $wrap == "nowrap" {
    flex-wrap: nowrap;
  }
  @if $wrap == "wrap" {
    flex-wrap: wrap;
  }
  @if $wrap == "wrap-reverse" or $wrap == "reverse" {
    flex-wrap: wrap-reverse;
  }

  @if $aligncontent == "flex-start" or $aligncontent == "start" {
    align-content: flex-start;
  }
  @if $aligncontent == "flex-end" or $aligncontent == "end" {
    align-content: flex-end;
  }
  @if $aligncontent == "center" {
    align-content: center;
  }
  @if $aligncontent == "stretch" {
    align-content: stretch;
  }
  @if $aligncontent == "space-between" or $aligncontent == "between" {
    align-content: space-between;
  }
  @if $aligncontent == "space-around" or $aligncontent == "around" {
    align-content: space-around;
  }
}

@mixin flex-self(
  $grow: 1,
  $shrink: 1,
  $basis: auto,
  $align: "flex-start",
  $order: 0
) {
  flex-grow: $grow;
  flex-shrink: $shrink;
  flex-basis: $basis;
  order: $order;

  @if $align == "flex-start" or $align == "start" or $align == flex-start {
    align-self: flex-start;
  }
  @if $align == "flex-end" or $align == "end" {
    align-self: flex-end;
  }
  @if $align == "center" {
    align-self: center;
  }
  @if $align == "stretch" {
    align-self: stretch;
  }
  @if $align == "baseline" {
    align-self: baseline;
  }
}

// Alias for flex-self
@mixin flex-child(
  $grow: 1,
  $shrink: 1,
  $basis: auto,
  $align: "flex-start",
  $order: 0
) {
  @include flex-self($grow, $shrink, $basis, $align, $order);
}

// Short-hands for common "flex-self" configurations

@mixin rigid {
  flex: 0 0 auto;
}
@mixin grow {
  flex: 1 0 auto;
}
@mixin shrink {
  flex: 0 1 auto;
}
@mixin stretch {
  flex: 1 1 auto;
}
@mixin size-evenly {
  flex: 1 1 0;
}
@mixin size-two-thirds {
  flex: 2 1 0;
}
