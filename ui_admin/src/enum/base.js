import { Enum } from 'enumify';
import { capitalize, camelCase, kebabCase, startCase } from 'lodash-es';

export default class Base extends Enum {
  camelCase() {
    return camelCase(this.name);
  }

  capitalize() {
    return capitalize(this.name);
  }

  dashify() {
    return this.titleCase().replace(' ', ' - ');
  }

  kebabCase() {
    return kebabCase(this.name);
  }

  lowerCase() {
    return this.name.toLowerCase();
  }

  titleCase() {
    return startCase(camelCase(this.name));
  }

  upperCase() {
    return this.name.toUpperCase();
  }
}
