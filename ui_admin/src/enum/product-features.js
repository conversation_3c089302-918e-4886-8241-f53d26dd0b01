import { Enum } from 'enumify';
import { startCase } from 'lodash-es';

export default class ProductFeatures extends Enum {
  lowerCase() {
    return this.name.toLowerCase();
  }

  titleCase() {
    return startCase(this.lowerCase());
  }
}

ProductFeatures.initEnum([
  'bigUpload',
  'breakdown',
  'circle',
  'dataImport',
  'eventDetection',
  'intensity',
  'multilingual',
  'metis',
  'staticCaerus',
  'storyteller',
  'surveys',
  'swot',
  'var',
]);
