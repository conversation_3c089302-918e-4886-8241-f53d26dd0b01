import Base from './base';

const textMap = {
  BATCH: '/batch',
  DATASET: '/dataset',
  DATASET_STATUS: '/dataset-status',
  HEARTBEAT: '/heartbeat',
  LOGIN: '/login',
  ORGANISATION: '/organisation',
  REGISTRATION: '/registration',
  REPORTING_JOB: '/reporting-job',
  REPORTING_JOB_EDIT: '/reporting-job/edit',
  REPORTING_JOB_EXECUTION_STATUS: '/reporting-job/execution-status',
  REPORTING_JOB_NEW: '/reporting-job/new',
  REPORTING_JOB_RESULT: '/reporting-job/result',
  REPORTING_JOB_RESULT_EDIT: '/reporting-job/result/edit',
  SAVED_ACTION: '/saved-action',
  SAVED_ACTION_HISTORY: '/saved-action-history',
  SAVED_ACTION_LIST: '/saved-action-list',
  USER: '/user',
  USER_TIER: '/user-tier',
  USER_USAGE: '/user-usage',
  USER_USAGE_QUERY: '/user-usage/query',
  WORKSPACE: '/workspace',
};

export default class Route extends Base {
  url() {
    return textMap[this.name];
  }
}

Route.initEnum(Object.keys(textMap));
