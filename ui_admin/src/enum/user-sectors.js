import { Enum } from 'enumify';

const textMap = {
  BEAUTY: 'Beauty',
  FASHION_RETAIL: 'Fashion Retail',
  FINANCIAL_SERVICES: 'Financial Services',
  FOOD_AND_BEVERAGES: 'Food and Beverages',
  GENERAL: 'General',
  TELECOMMUNICATIONS: 'Telecommunications',
  TRAVEL_AND_TRANSPORT: 'Travel and Transport',
};
export default class UserSectors extends Enum {
  lowerCase() {
    return this.name.toLowerCase();
  }

  titleCase() {
    return textMap[this.name];
  }
}

UserSectors.initEnum(Object.keys(textMap));
