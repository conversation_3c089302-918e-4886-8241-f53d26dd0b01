import { Enum } from 'enumify';

const textMap = {
  UPLOADING: {
    order: 0,
    value: 'uploading',
  },
  ANALYSING: {
    order: 1,
    value: 'analysing',
  },
  AGGREGATING: {
    order: 2,
    value: 'aggregating',
  },
  TOPIC_ANALYSING: {
    order: 3,
    value: 'topic_analysing',
  },
  GENERATING: {
    order: 4,
    value: 'generating',
  },
  S3_UPLOADING: {
    order: 5,
    value: 's3_uploading',
  },
  APPLYING_ACTIONS: {
    order: 6,
    value: 'applying_actions',
  },
  CANCELLING: {
    order: 7,
    value: 'cancelling',
  },
  FINISHED: {
    order: 8,
    value: 'finished',
  },
  DELETED: {
    order: 9,
    value: 'DELETED',
  },
  FILTERED: {
    order: 10,
    value: 'deleted',
  },
};
export default class DatasetStatus extends Enum {
}

DatasetStatus.initEnum(Object.keys(textMap));
