import { Enum } from 'enumify';

/**
 * @enum
 */
class NetworkKeys extends Enum {}

NetworkKeys.initEnum(
  [
    'BATCHES',
    'COMMENTS',
    'DATASET_STATUSES',
    'DATASETS',
    'HEARTBEATS',
    'ME',
    '<PERSON><PERSON><PERSON><PERSON>_RATING',
    'ORGANISATIONS',
    'ORGANISATIONS_CREATE',
    'REGISTRATIONS',
    'REPORTING_JOB',
    'REPORTING_JOB_DATA_SOURCE',
    'REPORTING_JOB_DATA_SOURCE_OVERVIEWS',
    'REPORTING_JOB_DATA_SOURCE_PREVIEW_RESPONSES',
    'REPORTING_JOB_DATA_SOURCES',
    'REPORTING_JOB_DISPATCH_EMAILS',
    'REPORTING_JOB_EMAIL_CONFIG',
    'REPORTING_JOB_EXECUTION_STATUSES',
    'REPORTING_JOB_RESULT',
    'REPORTING_JOB_RESULTS',
    'REPORTING_JOB_SEARCH_THEME',
    'REPORTING_JOB_SEARCH_THEMES',
    'REPORTING_JOB_SPLIT_RULE',
    'REPORTING_JOB_SPLIT_RULES',
    'REPORTING_JOB_WORKSPACE_RULE',
    'REPORTING_JOB_WORKSPACE_RULES',
    'REPORTING_JOB_EXECUTION',
    'REPORTING_JOBS',
    'SAVED_ACTIONS',
    'USAGE',
    'USAGE_DOWNLOAD',
    'USER',
    'USER_LOGIN',
    'USER_LOGOUT',
    'USER_TIERS',
    'USER_USERS',
    'WORKSPACE',
  ],
);

export default NetworkKeys;
