<template>
  <section class="user-usage-query-view">
    <user-usage-query-header />
    <user-usage-query-builder />
    <user-usage-query-result />
  </section>
</template>

<script>
import UserUsageQueryBuilder from '@/components/UserUsageQuery/UserUsageQueryBuilder';
import UserUsageQueryHeader from '@/components/UserUsageQuery/UserUsageQueryHeader';
import UserUsageQueryResult from '@/components/UserUsageQuery/UserUsageQueryResult';

export default {
  name: 'user-usage-query-view',

  components: {
    UserUsageQueryBuilder,
    UserUsageQueryHeader,
    UserUsageQueryResult,
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.user-usage-query-view {
  @include flex("block", "column", "start", "stretch");

  background-color: #F3F6FF;
  height: 100%;
  padding: 0.6rem;
  width: 100%;
}
</style>
