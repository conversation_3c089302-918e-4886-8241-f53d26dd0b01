<template>
  <section class="reporting-job-edit-view">
    <reporting-job-upsert-header />
    <reporting-job-upsert />
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import ReportingJobUpsert from '@/components/ReportingJobUpsert/ReportingJobUpsert';
import ReportingJobUpsertHeader from '@/components/ReportingJobUpsert/ReportingJobUpsertHeader';

export default {
  name: 'reporting-job-edit-view',

  components: {
    ReportingJobUpsert,
    ReportingJobUpsertHeader,
  },

  computed: {
    ...mapState('reportingJob', ['selectedId']),
  },

  created() {
    if (!this.selectedId) {
      const { id } = this.$route.query;
      this.setSelectedId(id);
    }
  },

  methods: {
    ...mapActions('reportingJob', ['setSelectedId']),
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-edit-view {
  @include flex("block", "column", "start", "stretch");

  background-color: #F3F6FF;
  height: 100%;
  overflow-y: auto;
  padding: 0.6rem;
  width: 100%;
}
</style>
