<template>
  <section class="saved-action-view">
    <saved-action-header />
    <saved-action-table />
  </section>
</template>

<script>
import SavedActionHeader from '@/components/SavedAction/SavedActionHeader';
import SavedActionTable from '@/components/SavedAction/SavedActionTable';

export default {
  name: 'saved-action-view',

  components: {
    SavedActionHeader,
    SavedActionTable,
  },

  computed: {
  },

  methods: {
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.saved-action-view {
  @include flex("block", "column", "start", "stretch");

  background-color: #F3F6FF;
  height: 100%;
  width: 100%;
}
</style>
