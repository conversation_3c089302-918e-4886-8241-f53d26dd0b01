<template>
  <section class="dataset-view">
    <dataset-header />
    <dataset-toolbar />
    <dataset-table />
  </section>
</template>

<script>
import DatasetHeader from '@/components/Dataset/DatasetHeader';
import DatasetTable from '@/components/Dataset/DatasetTable';
import DatasetToolbar from '@/components/Dataset/DatasetToolbar';

export default {
  name: 'dataset-view',

  components: {
    DatasetHeader,
    DatasetTable,
    DatasetToolbar,
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.dataset-view {
  @include flex("block", "column", "start", "stretch");

  background-color: #F3F6FF;
  height: 100%;
  padding: 0.6rem;
  width: 100%;
}
</style>
