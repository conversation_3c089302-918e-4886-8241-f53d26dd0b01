<template>
  <section class="reporting-job-view">
    <reporting-job-header />
    <reporting-job-toolbar />
    <reporting-job-table />
  </section>
</template>

<script>
import ReportingJobHeader from '@/components/ReportingJob/ReportingJobHeader';
import ReportingJobTable from '@/components/ReportingJob/ReportingJobTable';
import ReportingJobToolbar from '@/components/ReportingJob/ReportingJobToolbar';

export default {
  name: 'reporting-job-view',

  components: {
    ReportingJobHeader,
    ReportingJobTable,
    ReportingJobToolbar,
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-view {
  @include flex("block", "column", "start", "stretch");

  background-color: #F3F6FF;
  height: 100%;
  overflow-y: auto;
  padding: 0.6rem;
  width: 100%;
}
</style>
