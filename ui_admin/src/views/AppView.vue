<template>
  <section class="app-view">
    <nav-bar />
    <section class="app-content">
      <app-header />
      <section class="body-container">
        <router-view :key="$route.fullPath" />
      </section>
    </section>
  </section>
</template>

<script>
import AppHeader from '@/components/AppHeader/AppHeader';
import NavBar from '@/components/NavBar/NavBar';

export default {
  name: 'app-view',

  components: {
    NavBar,
    AppHeader,
  },

  methods: {
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.app-view {
  @include flex("block", "row", "start", "stretch");
  @include stretch;

  max-height: 100%;
  height: 100vh;
  min-height: 0;
  max-width: 100%;
  position: relative;

  .app-content {
    @include flex("block", "column", "start", "stretch");

    height: 100%;
    width: calc(100vw - 16rem);
  }

  .body-container {
    @include flex("block", "row", "start", "stretch");
    @include stretch;

    height: calc(100vh - 50px);
    max-height: calc(100vh - 50px);
    overflow: hidden;
    width: 100%;
  }
}
</style>
