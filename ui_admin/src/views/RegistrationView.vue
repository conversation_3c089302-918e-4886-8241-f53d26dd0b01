<template>
  <section class="registration-view">
    <registration-header />
    <registration-table />
  </section>
</template>

<script>
import RegistrationHeader from '@/components/Registration/RegistrationHeader';
import RegistrationTable from '@/components/Registration/RegistrationTable';

export default {
  name: 'registration-view',

  components: {
    RegistrationHeader,
    RegistrationTable,
  },

  computed: {
  },

  methods: {
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.registration-view {
  @include flex("block", "column", "start", "stretch");

  background-color: #F3F6FF;
  height: 100%;
  width: 100%;
}
</style>
