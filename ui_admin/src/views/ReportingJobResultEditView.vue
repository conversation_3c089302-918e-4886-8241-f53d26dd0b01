<template>
  <section class="reporting-job-result-edit-view">
    <loading-blocks-overlay v-if="loading" />
    <reporting-job-result-edit-header v-if="!loading" />
    <reporting-job-result-edit-body v-if="!loading" />
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import ReportingJobRequest from '@/services/ReportingJobRequest';
import ReportingJobResultEditBody from '@/components/ReportingJobResultEdit/ReportingJobResultEditBody';
import ReportingJobResultEditHeader from '@/components/ReportingJobResultEdit/ReportingJobResultEditHeader';

export default {
  name: 'reporting-job-result-edit-view',

  components: {
    LoadingBlocksOverlay,
    ReportingJobResultEditBody,
    ReportingJobResultEditHeader,
  },

  data() {
    return {
      loading: true,
      localResult: null,
    };
  },

  computed: {
    ...mapState('reportingJob', ['selectedId', 'selectedResultId']),
  },

  async created() {
    this.loading = true;

    if (!this.selectedId) {
      const { reportingJobId } = this.$route.query;
      this.setSelectedId(reportingJobId);
    }

    if (!this.selectedResultId) {
      const { resultId } = this.$route.query;
      this.setSelectedResultId(resultId);
    }

    await ReportingJobRequest.getReportingJob(this.selectedId);
    await ReportingJobRequest.getResult(this.selectedId, this.selectedResultId);

    this.loading = false;
  },

  methods: {
    ...mapActions('reportingJob', ['setSelectedId', 'setSelectedResultId']),
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-result-edit-view {
  @include flex("block", "column", "start", "stretch");

  background-color: #F3F6FF;
  height: 100%;
  overflow-y: auto;
  padding: 0.6rem;
  width: 100%;
}
</style>
