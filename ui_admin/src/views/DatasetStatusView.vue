<template>
  <section class="dataset-status-view">
    <dataset-status-header />
    <dataset-status-toolbar />
    <dataset-status-table />
  </section>
</template>

<script>
import DatasetStatusHeader from '@/components/DatasetStatus/DatasetStatusHeader';
import DatasetStatusTable from '@/components/DatasetStatus/DatasetStatusTable';
import DatasetStatusToolbar from '@/components/DatasetStatus/DatasetStatusToolbar';

export default {
  name: 'dataset-status-view',

  components: {
    DatasetStatusHeader,
    DatasetStatusTable,
    DatasetStatusToolbar,
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.dataset-status-view {
  @include flex("block", "column", "start", "stretch");

  background-color: #F3F6FF;
  height: 100%;
  padding: 0.6rem;
  width: 100%;
}
</style>
