<template>
  <section class="user-view">
    <user-header />
    <loading-blocks-overlay v-if="loading" />
    <user-toolbar v-if="!loading" />
    <user-table v-if="!loading" />
  </section>
</template>

<script>
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import UserHeader from '@/components/User/UserHeader';
import UserRequest from '@/services/UserRequest';
import UserTable from '@/components/User/UserTable';
import UserToolbar from '@/components/User/UserToolbar';
import UserTierRequest from '@/services/UserTierRequest';

export default {
  name: 'user-view',

  components: {
    LoadingBlocksOverlay,
    UserHeader,
    UserTable,
    UserToolbar,
  },

  data() {
    return {
      loading: true,
    };
  },

  async created() {
    await Promise.all([UserRequest.fetchUser(), UserTierRequest.getUserTiers()]);
    this.loading = false;
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.user-view {
  @include flex("block", "column", "start", "stretch");

  background-color: #F3F6FF;
  height: 100%;
  padding: 0.6rem;
  width: 100%;
}
</style>
