<template>
  <section class="reporting-job-new-view">
    <reporting-job-upsert-header />
    <reporting-job-upsert />
  </section>
</template>

<script>
import ReportingJobUpsert from '@/components/ReportingJobUpsert/ReportingJobUpsert';
import ReportingJobUpsertHeader from '@/components/ReportingJobUpsert/ReportingJobUpsertHeader';

export default {
  name: 'reporting-job-new-view',

  components: {
    ReportingJobUpsert,
    ReportingJobUpsertHeader,
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-new-view {
  @include flex("block", "column", "start", "stretch");

  background-color: #F3F6FF;
  height: 100%;
  overflow-y: auto;
  padding: 0.6rem;
  width: 100%;
}
</style>
