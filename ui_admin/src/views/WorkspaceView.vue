<template>
  <section class="workspace-view">
    <workspace-header />
    <workspace-table />
  </section>
</template>

<script>
import WorkspaceHeader from '@/components/Workspace/WorkspaceHeader';
import WorkspaceTable from '@/components/Workspace/WorkspaceTable';
import workspaceRequest from '@/services/WorkspaceRequest';

export default {
  name: 'workspace-view',

  components: {
    WorkspaceHeader,
    WorkspaceTable,
  },

  computed: {
  },

  created() {
    this.doCallApiFetchWorkspaces();
  },

  methods: {
    async doCallApiFetchWorkspaces() {
      await workspaceRequest.fetchWorkspaces();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.workspace-view {
  @include flex("block", "column", "start", "stretch");

  background-color: #F3F6FF;
  height: 100%;
  width: 100%;
}
</style>
