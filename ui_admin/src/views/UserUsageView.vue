<template>
  <section class="user-usage-view">
    <user-usage-header />
    <user-usage-toolbar />
    <user-usage-table />
  </section>
</template>

<script>
import UserUsageHeader from '@/components/UserUsage/UserUsageHeader';
import UserUsageTable from '@/components/UserUsage/UserUsageTable';
import UserUsageToolbar from '@/components/UserUsage/UserUsageToolbar';

export default {
  name: 'user-usage-view',

  components: {
    UserUsageHeader,
    UserUsageTable,
    UserUsageToolbar,
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.user-usage-view {
  @include flex("block", "column", "start", "stretch");

  background-color: #F3F6FF;
  height: 100%;
  padding: 0.6rem;
  width: 100%;
}
</style>
