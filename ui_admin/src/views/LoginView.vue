<template>
  <section class="login-view">
    <div class="logos">
      <a href="http://www.adoreboard.com" target="_blank"><img :src="logoAdoreboard"/></a>
    </div>
    <section class="wrapper">
      <section class="login-form">
        <base-form @submit="submit">
          <section class="form-content">
            <div class="header">
              <h1 class="header-title">Sign in to Adoreboard Admin</h1>
            </div>
            <base-form-field
                id="email"
                v-model="email"
                ref="email"
                :focus="true"
                label="Email"
                type="text"
                placeholder="<EMAIL>"
                @keydown.native.enter="submit"
            />
            <base-form-field
                id="password"
                v-model="password"
                ref="password"
                label="Password"
                type="password"
                placeholder="••••••••"
                @keydown.native.enter="submit"
            />

            <base-button
                v-if="!submitting"
                id="submit"
                layout="block"
                @click="submit"
            >Sign In
            </base-button>
            <loading-blocks-overlay v-else/>

            <section v-if="error" class="error">{{ errorMessage }}</section>
          </section>
        </base-form>
      </section>
    </section>
  </section>
</template>

<script>
import { mapGetters, mapState } from 'vuex';

import authRequest from '@/services/AuthRequest';
import BaseButton from '@/components/Base/BaseButton';
import BaseForm from '@/components/Base/BaseForm';
import BaseFormField from '@/components/Base/BaseFormField';
import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import logoAdoreboard from '@/assets/logo/logo-adoreboard-pink.svg';
import Route from '@/enum/route';

export default {
  name: 'login-view',

  components: {
    BaseButton,
    BaseForm,
    BaseFormField,
    LoadingBlocksOverlay,
  },

  data() {
    return {
      attempts: 0,
      email: '',
      error: false,
      logoAdoreboard,
      password: '',
      statusInterval: null,
      submitting: false,
    };
  },

  computed: {
    ...mapState('user', ['user']),

    ...mapGetters('user', ['basicAuth']),

    errorMessage() {
      if (this.attempts <= 2) {
        return "Couldn't log in. Please check your credentials and try again.";
      }

      return 'Forgot your password? You can change your details easily using the link below. If you believe your credentials are correct, the problem may be on our end. Please contact us if the issue persists.';
    },
  },

  methods: {
    async submit() {
      this.submitting = true;
      this.error = false;

      this.attempts += 1;

      const response = await authRequest.login(this.email, this.password);

      if (response?.access_token != null) {
        await authRequest.me();
        await this.$router.push({ name: Route.USER });
      } else {
        this.error = true;
      }

      this.submitting = false;
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.login-view {
  @include flex("block", "column", "start", "start");
  @include login-page;

  font-family: 'Inter', serif;

  .logos {
    margin-left: 1.5rem;
    margin-top: 1rem;
    position: absolute;
  }

  .wrapper {
    @include flex("block", "column", "center", "center");
    @include stretch;

    margin-bottom: 10rem;
    width: 100%;
  }

  .header {
    font-style: normal;
    margin-bottom: 1.5rem;

    .header-title {
      color: $adoreboard-mauve;
      font-weight: 900;
      letter-spacing: 0.4px;
      text-align: left;
      text-transform: uppercase;
    }
  }

  .login-form {
    max-width: 445px;
    width: 100%;
  }

  .base-form {
    @include rigid;

    background: #1B2435;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.25);
    border-radius: 5px 5px 0 0;

    .base-button {
      background: $adoreboard-mauve;
      border-radius: 3px;
      color: $nps-blue;
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      height: 40px;
      letter-spacing: 0.5px;
      line-height: 15px;
      margin-top: 1.5rem;
      text-align: center;
      text-transform: uppercase;

      &:hover {
        background: $adoreboard-mauve-hover;
        color: #FFF;
      }
    }

    .form-content {
      padding: 2.5rem;
    }

    .base-form-field {
      font-size: 12px;
      font-style: normal;
      margin-bottom: 1rem;

      label {
        color: #FFF;
        font-size: 12px;
        line-height: 15px;
        padding-bottom: 0.2rem;
      }

      input {
        background: #FFFFFF;
        border-radius: 3px;
        border: 1px solid rgba(36, 18, 77, 0.18);
        color: #000000;
        font-size: 12px;
        margin: 0.1rem;
        padding: 0.75rem 1rem;
      }

      input:-webkit-autofill,
      input:-webkit-autofill:hover,
      input:-webkit-autofill:focus input:-webkit-autofill,
      textarea:-webkit-autofill,
      textarea:-webkit-autofill:hover textarea:-webkit-autofill:focus,
      select:-webkit-autofill,
      select:-webkit-autofill:hover,
      select:-webkit-autofill:focus {
        border: 1px solid rgba(36, 18, 77, 0.18);
        border-radius: 3px;
        -webkit-box-shadow: 0 0 0 2rem #fff inset;
        -webkit-text-fill-color: #000000;
        font-size: 12px;
      }

      input::-webkit-input-placeholder {
        color: rgba(0, 0, 0, 0.3);
      }
    }

    h1 {
      font-style: normal;
      font-weight: 600;
      font-size: 19px;
      line-height: 23px;
      color: #24124D;
    }

    .login-links {
      @include flex("block", "row", "center", "center");

      font-size: 11px;
      margin-top: 1.5rem;
      text-transform: uppercase;

      .base-form-link {
        color: #FFF;
        font-weight: $font-weight-normal;
        letter-spacing: 0;
        opacity: 1;
        text-decoration: underline;
        text-transform: none;

        &:hover, &:focus {
          color: #FFF6A3;
          opacity: 1;
          outline: none;
          text-decoration: none;
        }
      }
    }

    .error {
      color: #fc6464;
      font-size: $font-size-xs;
      padding-top: 1.8rem;
      text-align: center;
      text-decoration-color: clr("white");
    }
  }
}
</style>
