<template>
  <section class="reporting-job-execution-status-view">
    <loading-blocks-overlay v-if="loading" />
    <reporting-job-execution-status-header v-if="!loading" @close="onClose" />
    <reporting-job-execution-status-table v-if="!loading" />
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import ReportingJobExecutionStatusHeader from '@/components/ReportingJobExecutionStatus/ReportingJobExecutionStatusHeader';
import ReportingJobExecutionStatusTable from '@/components/ReportingJobExecutionStatus/ReportingJobExecutionStatusTable';
import ReportingJobRequest from '@/services/ReportingJobRequest';
import Route from '@/enum/route';

export default {
  name: 'reporting-job-execution-status-view',

  components: {
    LoadingBlocksOverlay,
    ReportingJobExecutionStatusHeader,
    ReportingJobExecutionStatusTable,
  },

  data() {
    return {
      loading: true,
    };
  },

  computed: {
    ...mapState('reportingJob', ['selectedId']),
  },

  async created() {
    this.loading = true;

    if (!this.selectedId) {
      const { id } = this.$route.query;
      await this.setSelectedId(id);
    }
    await ReportingJobRequest.getReportingJob(this.selectedId);

    this.loading = false;
  },

  methods: {
    ...mapActions('reportingJob', ['setSelectedId']),

    onClose() {
      this.$router.push({ name: Route.REPORTING_JOB });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-execution-status-view {
  @include flex("block", "column", "start", "stretch");

  background-color: #F3F6FF;
  height: 100%;
  overflow-y: auto;
  padding: 0.6rem;
  width: 100%;
}
</style>
