<template>
  <section class="batch-view">
    <batch-header />
    <batch-table />
  </section>
</template>

<script>

import BatchHeader from '@/components/Batch/BatchHeader';
import BatchTable from '@/components/Batch/BatchTable';
import batchRequest from '@/services/BatchRequest';

export default {
  name: 'batch-view',

  components: {
    BatchHeader,
    BatchTable,
  },

  computed: {
  },

  created() {
    this.doCallApiFetchBatches();
  },

  methods: {
    async doCallApiFetchBatches() {
      await batchRequest.fetchBatches();
    },
  },

};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.batch-view {
  @include flex("block", "column", "start", "stretch");

  background-color: #F3F6FF;
  height: 100%;
  width: 100%;
}
</style>
