<template>
  <section class="user-tier-view">
    <user-tier-header />
    <user-tier-table />
  </section>
</template>

<script>
import UserTierHeader from '@/components/UserTier/UserTierHeader';
import UserTierTable from '@/components/UserTier/UserTierTable';

export default {
  name: 'user-tier-view',

  components: {
    UserTierHeader,
    UserTierTable,
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.user-tier-view {
  @include flex("block", "column", "start", "stretch");

  background-color: #F3F6FF;
  height: 100%;
  padding: 0.6rem;
  width: 100%;
}
</style>
