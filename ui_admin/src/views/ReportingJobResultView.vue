<template>
  <section class="reporting-job-result-view">
    <loading-blocks-overlay v-if="loading" />
    <reporting-job-result-header v-if="!loading" />
    <reporting-job-result-table v-if="!loading" />
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import LoadingBlocksOverlay from '@/components/Base/LoadingBlocksOverlay';
import ReportingJobRequest from '@/services/ReportingJobRequest';
import ReportingJobResultHeader from '@/components/ReportingJobResult/ReportingJobResultHeader';
import ReportingJobResultTable from '@/components/ReportingJobResult/ReportingJobResultTable';

export default {
  name: 'reporting-job-result-view',

  components: {
    LoadingBlocksOverlay,
    ReportingJobResultHeader,
    ReportingJobResultTable,
  },

  data() {
    return {
      loading: true,
    };
  },

  computed: {
    ...mapState('reportingJob', ['selectedId']),
  },

  async created() {
    this.loading = true;

    if (!this.selectedId) {
      const { id } = this.$route.query;
      await this.setSelectedId(id);
    }
    await ReportingJobRequest.getReportingJob(this.selectedId);

    this.loading = false;
  },

  methods: {
    ...mapActions('reportingJob', ['setSelectedId']),
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reporting-job-result-view {
  @include flex("block", "column", "start", "stretch");

  background-color: #F3F6FF;
  height: 100%;
  overflow-y: auto;
  padding: 0.6rem;
  width: 100%;
}
</style>
