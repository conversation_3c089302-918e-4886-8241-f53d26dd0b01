<template>
  <section class="heartbeat-view">
    <heartbeat-header />
    <heartbeat-table />
  </section>
</template>

<script>
import HeartbeatHeader from '@/components/Heartbeat/HeartbeatHeader';
import HeartbeatTable from '@/components/Heartbeat/HeartbeatTable';

export default {
  name: 'heartbeat-view',

  components: {
    HeartbeatHeader,
    HeartbeatTable,
  },

  computed: {
  },

  methods: {
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.heartbeat-view {
  @include flex("block", "column", "start", "stretch");

  background-color: #F3F6FF;
  height: 100%;
  width: 100%;
}
</style>
