<template>
  <section class="saved-action-list-view">
    <saved-action-list-header />
    <saved-action-list-table />
  </section>
</template>

<script>
import SavedActionListHeader from '@/components/SavedActionList/SavedActionListHeader';
import SavedActionListTable from '@/components/SavedActionList/SavedActionListTable';

export default {
  name: 'saved-action-list-view',

  components: {
    SavedActionListHeader,
    SavedActionListTable,
  },

  computed: {
  },

  methods: {
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.saved-action-list-view {
  @include flex("block", "column", "start", "stretch");

  background-color: #F3F6FF;
  height: 100%;
  width: 100%;
}
</style>
