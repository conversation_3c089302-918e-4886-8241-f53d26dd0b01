<template>
  <section class="organisation-view">
    <organisation-header />
    <organisation-table />
  </section>
</template>

<script>
import OrganisationHeader from '@/components/Organisation/OrganisationHeader';
import OrganisationTable from '@/components/Organisation/OrganisationTable';

export default {
  name: 'organisation-view',

  components: {
    OrganisationHeader,
    OrganisationTable,
  },

  computed: {
  },

  methods: {
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.organisation-view {
  @include flex("block", "column", "start", "stretch");

  background-color: #F3F6FF;
  height: 100%;
  width: 100%;
}
</style>
