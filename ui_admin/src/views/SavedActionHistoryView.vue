<template>
  <section class="saved-action-history-view">
    <saved-action-history-header />
    <saved-action-history-table />
  </section>
</template>

<script>
import SavedActionHistoryHeader from '@/components/SavedActionHistory/SavedActionHistoryHeader';
import SavedActionHistoryTable from '@/components/SavedActionHistory/SavedActionHistoryTable';

export default {
  name: 'saved-action-history-view',

  components: {
    SavedActionHistoryHeader,
    SavedActionHistoryTable,
  },

  computed: {
  },

  methods: {
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.saved-action-history-view {
  @include flex("block", "column", "start", "stretch");

  background-color: #F3F6FF;
  height: 100%;
  width: 100%;
}
</style>
