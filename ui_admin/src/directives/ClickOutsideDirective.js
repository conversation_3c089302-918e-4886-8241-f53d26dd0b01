const ClickOutsideDirective = {
  mounted(el, binding) {
    const handleClickOutside = (event) => {
      if (!el.contains(event.target) && el !== event.target) {
        binding.value();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    el.clickOutside = handleClickOutside;
  },
  unmounted(el) {
    document.removeEventListener('mousedown', el.clickOutside);
  },
};

export default ClickOutsideDirective;
