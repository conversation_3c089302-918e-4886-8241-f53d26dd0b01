<template>
  <section id="app">
    <the-modal v-for="(item, index) in getComponent"
      :component="item"
      :componentProps="getComponentProps"
      :index="index"
      :key="index"
    />

    <the-toast />
    <router-view/>
  </section>
</template>

<script>
import 'normalize.css';
import '@/styles/overall.scss';

import { mapGetters, mapState } from 'vuex';

import authRequest from '@/services/AuthRequest';
import Route from '@/enum/route';
import TheModal from '@/components/Modal/TheModal';
import TheToast from '@/components/TheToast';

export default {
  name: 'app',

  components: {
    TheModal,
    TheToast,
  },

  computed: {
    ...mapGetters('modal', ['getComponent', 'getComponentProps']),

    ...mapState('network', ['authorized']),

    ...mapState('user', ['user']),

    isLoginPage() {
      return this.$route != null && this.$route.meta.isLoginPage != null;
    },
  },

  watch: {
    $route() {
      if (this.authorized && this.isLoginPage) this.$router.push({ name: Route.USER });
    },

    async authorized() {
      if (this.authorized && this.isLoginPage) this.$router.push({ name: Route.USER });

      if (!this.authorized && !this.isLoginPage) {
        this.$router.push({ name: Route.LOGIN });
      }
    },
  },

  created() {
    this.$router.isReady().then(() => {
      this.checkAuthorized();
    });
  },

  methods: {
    async checkAuthorized() {
      await authRequest.me();

      if (this.user == null && !this.isLoginPage) {
        this.$router.push({ name: Route.LOGIN });
      }
    },
  },
};
</script>

<style lang="scss">
@import url("https://cloud.typography.com/6009272/801484/css/fonts.css");

@import "src/styles/variables";
@import "src/styles/mixins";
@import "~@fortawesome/fontawesome-pro/css/all.min.css";
@import "~font-awesome/css/font-awesome.min.css";
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap");

html,
body {
  height: 100%;
  max-height: 100%;
  min-height: 0;
  max-width: 100%;
  min-width: 100%;
  overflow: hidden;
}

#app {
  @include flex("block", "column", "start", "stretch", "nowrap", "stretch");
  @include stretch;

  height: 100%;
  max-height: 100%;
  max-width: 100%;
  min-height: 0;
  overflow: auto;
  transition: all $interaction-transition-time;
  width: 100%;

  .the-toast {
    z-index: 9999;
  }
}
</style>
