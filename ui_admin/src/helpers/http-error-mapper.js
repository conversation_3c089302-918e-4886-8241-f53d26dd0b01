/**
 * @module HttpErrorMapper
 */

import { chunk, isNumber } from 'lodash-es';

// 400 error codes 400 -> 451
const clientErrorRange = [...[...Array(52).keys()].map(v => v + 400)];

// 500 error codes 500 -> 511
const serverErrorRange = [...[...Array(12).keys()].map(v => v + 500)];

/**
 * @param {...Number} params Pairs of begin and end points for generating error code ranges
 * @memberof module:HttpErrorMapper
 * @function
 */
const statusRange = (...params) => {
  if (params.length % 2 === 1) {
    throw new Error('Status Range: Enter an even number of begin and end points');
  }

  return chunk(params, 2)
    .reduce((arr, [begin, end]) => [...arr, ...[...Array(end - begin).keys()].map(v => v + begin)], []);
};

/**
 * @param {String} ident Special case for creating a range that encompasses all client or server
 * errors
 * @memberof module:HttpErrorMapper
 * @function
 */
const specialStatusRange = (ident) => {
  if (ident === '4xx') return clientErrorRange;
  if (ident === '5xx') return serverErrorRange;

  return [];
};

/**
 * Generate a map of error codes and associated responses, and select appropriate response given an
 * error object
 *
 * @param {Object} error Error object generated from axios response
 * @param {(Number[]|Number|String)} params Pairs of error codes/code ranges and associated
 * messages
 * @memberof module:HttpErrorMapper
 * @function
 */
const statusMapper = (err, ...params) => {
  if (params.length % 2 === 1) {
    throw new Error(
      'Status Mapper: Mapper params are uneven, enter a status value and error message per mapping',
    );
  }

  const errorCodes = [...clientErrorRange, ...serverErrorRange];

  const statusMap = errorCodes.reduce((map, key) => {
    map[key] = '';

    return map;
  }, {});

  const assignStatus = (code, message) => {
    if (
      isNumber(code) &&
      Number.isInteger(code) &&
      (clientErrorRange.includes(code) || serverErrorRange.includes(code))
    ) {
      statusMap[code] = message;
    }
  };

  chunk(params, 2).forEach(([codeOrRange, message]) => {
    if (codeOrRange === '4xx' || codeOrRange === '5xx') {
      specialStatusRange(codeOrRange).forEach(code => {
        if (statusMap[code] === '') statusMap[code] = message;
      });
    }

    if (isNumber(codeOrRange)) assignStatus(codeOrRange, message);

    if (Array.isArray(codeOrRange)) {
      codeOrRange.forEach(code => assignStatus(code, message));
    }
  });

  const returnedCode = err.response.status;

  return statusMap[returnedCode];
};

export { statusRange, specialStatusRange, statusMapper };
