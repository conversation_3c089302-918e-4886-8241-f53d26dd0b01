import NetworkKeys from '@/enum/network-keys';

const contactUs = '<a href="mailto:support@adoreboard">Contact Us</a>';
const tryAgain = `Please try again in a few moments or ${contactUs} if the problem persists.`;

const clientErrorMessage = `An unexpected error occurred. ${tryAgain}`;
const serverErrorMessage = `The server encountered an error while trying to process a request. ${tryAgain}`;

const defaults = NetworkKeys.enumValues.reduce((register, key) => {
  register[key] = {
    '4xx': clientErrorMessage,
    '5xx': serverErrorMessage,
  };

  return register;
}, {});

const entries = {
  'user/login': {
    '4xx': "We couldn't log you in. Please make sure your credentials are correct and try again.",
  },
};

export default Object.keys(defaults).reduce((register, key) => {
  register[key] = Object.assign({}, defaults[key], entries[key]);

  return register;
}, {});
