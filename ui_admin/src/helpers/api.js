import axios from 'axios';
import qs from 'qs';

let customAdapter;

const logRequest = ({ baseURL, data, method, params, url }) => {
  /* eslint-disable no-console */
  console.groupCollapsed(
    `\
%cRequest ${method.toUpperCase()} ${url.replace(baseURL, '')}\
%c${params != null ? decodeURIComponent(`?${qs.stringify(params, { arrayFormat: 'repeat' })}`) : ''}\
%c${data != null ? ' (+data)' : ''}\
`,
    'color:#00BFFF',
    'color:#8ABFE6',
    'color:#9354D1',
  );
  console.groupCollapsed('Data');
  console.log({ data, params });
  console.groupEnd();
  console.groupCollapsed('Trace');
  console.trace();
  console.groupEnd();
  console.groupEnd();
  /* eslint-enable no-console */
};

const logResponse = ({ config, data }) => {
  /* eslint-disable no-console */
  console.groupCollapsed(
    `\
%cResponse ${config.method.toUpperCase()} ${config.url.replace(config.baseURL, '')}\
%c${config.params != null ? decodeURIComponent(`?${qs.stringify(config.params, { arrayFormat: 'repeat' })}`) : ''}\
%c${config.data != null ? ' (+data)' : ''}\
`,
    'color:#9354D1',
    'color:#C5979D',
    'color:#9354D1',
  );
  console.log(data);
  console.groupEnd();
  /* eslint-enable no-console */
};

export default {
  instance() {
    const instance = axios.create({
      adapter: customAdapter,
      baseURL: process.env.API_BASE_URL,
      timeout: 60000,
      withCredentials: true,
    });

    const accessToken = localStorage.getItem('accessToken');
    if (accessToken) {
      instance.defaults.headers.common.Authorization = `Bearer ${accessToken}`;
    }

    instance.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded';
    instance.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

    instance.interceptors.request.use(this.interceptorRequest(instance));
    instance.interceptors.response.use(this.interceptorResponse(instance));

    return instance;
  },

  interceptorRequest() {
    return request => {
      request.paramsSerializer = params => qs.stringify(params, { arrayFormat: 'repeat' });

      if (process.env.NODE_ENV === 'development' && window.console) {
        logRequest(request);
      }

      return request;
    };
  },

  interceptorResponse() {
    return response => {
      if (process.env.NODE_ENV === 'development' && window.console) {
        logResponse(response);
      }

      return response;
    };
  },
};
