export default class UserLocalSettings {
  /**
   * @param key key
   * @param initVal (opt.) - default value
   * @param val (opt.) - expected value - designed for boolean value
   * @returns {boolean|string | *}
   */
  get(key, initVal, val) {
    const rs = localStorage.getItem(key);
    if (val) {
      initVal = typeof initVal !== 'undefined' ? initVal : false;
      return rs ? rs === val : initVal;
    }
    return rs || initVal;
  }

  remove(key) {
    localStorage.removeItem(key);
  }

  /**
   * @param key key
   * @param val - will be parsed to string by default
   * @param noParse (opt.)
   */
  set(key, val, noParse) {
    val = noParse ? val : (val?.toString().trim() || '');
    localStorage.setItem(key, val);
  }
}
