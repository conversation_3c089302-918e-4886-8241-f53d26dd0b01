import baseApi from './BaseApi';
import NetKeys from '@/enum/network-keys';

const domain = {
  user: 'user',
};

const api = {
  // me: `/api/${domain.user}/me`,
  me_admin: `/api/${domain.user}/admin/me`,
  login: '/api/oauth/login/admin',
  logout: '/api/oauth/logout',
};

export default {
  ...baseApi,

  async login(email, password) {
    const { response } = await this
      .post(NetKeys.USER_LOGIN, api.login, {
        client_id: 'emotics',
        email,
        password,
      });

    if (response?.status === 200 && response.data.access_token) {
      localStorage.setItem('accessToken', response.data.access_token);
      return response.data;
    }

    this.setError(NetKeys.USER_LOGIN, 'Invalid credentials.');

    return null;
  },

  async logout() {
    await this.post(NetKeys.USER_LOGOUT, api.logout);

    localStorage.removeItem('accessToken');
  },

  async me() {
    const { response } = await this.get(NetKeys.ME, api.me_admin);

    if (response && response.data !== '') {
      return response.data;
    }

    this.setError(NetKeys.ME, 'Invalid credentials.');

    return null;
  },
};
