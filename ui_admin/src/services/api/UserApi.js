import baseApi from './BaseApi';
import NetKeys from '@/enum/network-keys';

/* eslint-disable */
const api = {
  getUsers:                       '/admin/api/users',
  loginData:          (userId) => `/admin/api/users/${userId}`,
  update:             (userId) => `/admin/api/users/${userId}`,
  addActiveUsage:     (userId) => `/admin/api/users/${userId}/usage-record`,
  renewUsage:         (userId) => `/admin/api/users/${userId}/usage-record/renew`,
  extendUsage:        (userId) => `/admin/api/users/${userId}/usage-record/extend`,
  updateOrganisation: (userId) => `/admin/api/users/${userId}/organisation`,
  updateWorkspaces:   (userId) => `/admin/api/users/${userId}/workspaces`,
  updateActive:       (userId) => `/admin/api/users/${userId}/active`,
  updatePassword:     (userId) => `/admin/api/users/${userId}/password`,
  resetPassword:                  '/api/password/reset-request',
  updateTier:         (userId) => `/admin/api/users/${userId}/tier`,
};
/* eslint-enable */

export default {
  ...baseApi,

  async createUser(newUser) {
    const { error, response } = await this.post(
      NetKeys.USER,
      api.getUsers,
      newUser,
    );

    return error ? error.response : response.data;
  },

  async updateUser(userId, user) {
    const { error, response } = await this.post(
      NetKeys.USER,
      api.update(userId),
      user,
    );

    return error ? error.response : response.data;
  },

  async getUsers(params) {
    const { response } = await this.get(
      NetKeys.USER_USERS,
      api.getUsers,
      { params },
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async getLoginData(userId) {
    const { response } = await this.get(
      NetKeys.USER_USERS,
      api.loginData(userId),
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async updateActive(userId, checked) {
    const { error, response } = await this.post(
      NetKeys.USER,
      api.updateActive(userId),
      null,
      { params: {
        checked,
      },
      },
    );

    return error ? error.response : response.data;
  },

  async updatePassword(userId, newPassword) {
    const { error, response } = await this.post(
      NetKeys.USER,
      api.updatePassword(userId),
      null,
      { params: {
        newPassword,
      },
      },
    );

    return error ? error.response : response.data;
  },

  async resetPassword(email) {
    const { error, response } = await this.post(
      NetKeys.USER,
      api.resetPassword,
      null,
      { params: {
        email,
      },
      },
    );

    return error ? error.response : response.data;
  },

  async updateTier(userId, userTierId) {
    const { error, response } = await this.post(
      NetKeys.USER,
      api.updateTier(userId),
      null,
      { params: {
        userTierId,
      },
      },
    );

    return error ? error.response : response.data;
  },

  async addUserToOrganisation(userId, request) {
    const { error, response } = await this.post(
      NetKeys.USER,
      api.updateOrganisation(userId),
      request,
    );

    return error ? error.response : response.data;
  },

  async addUserToWorkspaces(userId, request) {
    const { error, response } = await this.post(
      NetKeys.USER,
      api.updateWorkspaces(userId),
      request,
    );

    return error ? error.response : response.data;
  },

  async deleteUser(userId) {
    const { error, response } = await this.delete(
      NetKeys.USER,
      api.update(userId),
    );

    return error ? error.response : response.data;
  },

  async deleteUsers(ids) {
    const { error, response } = await this.delete(
      NetKeys.USER,
      api.getUsers,
      { params: { ids } },
    );

    return error ? error.response : response.data;
  },

  async addActiveUsageForUser(userId) {
    const { error, response } = await this.post(
      NetKeys.USER,
      api.addActiveUsage(userId),
    );

    return error ? error.response : response.data;
  },

  async renewUsageForUser(userId) {
    const { error, response } = await this.post(
      NetKeys.USER,
      api.renewUsage(userId),
    );

    return error ? error.response : response.data;
  },

  async extendUsageForUser(userId, usageToAdd) {
    const { error, response } = await this.post(
      NetKeys.USER,
      api.extendUsage(userId),
      null,
      { params: { usageToAdd } },
    );

    return error ? error.response : response.data;
  },

};
