import baseApi from './BaseApi';
import NetKeys from '@/enum/network-keys';

/* eslint-disable */
const api = {
  dataSourceOverview:         (reportingId, externalId)           => `/admin/api/reporting-jobs/${reportingId}/data-sources/overviews/${externalId}`,
  dataSourceOverviews:        (reportingId)           => `/admin/api/reporting-jobs/${reportingId}/data-sources/overviews`,
  dataSourcePreviewResponses: (reportingId)           => `/admin/api/reporting-jobs/${reportingId}/data-sources/preview-responses`,
  dataSources:                (reportingId)           => `/admin/api/reporting-jobs/${reportingId}/data-sources`,
  emailConfigs:               (reportingId)           => `/admin/api/reporting-jobs/${reportingId}/email-configs`,
  executionStatuses:          (reportingId)           => `/admin/api/reporting-jobs/${reportingId}/execution-statuses`,
  reportingJob:               (reportingId)           => `/admin/api/reporting-jobs/${reportingId}`,
  reportingJobs:                                                '/admin/api/reporting-jobs',
  dispatchReportEmail:        (reportingId)           => `/admin/api/reporting-jobs/${reportingId}/results/dispatch-email`,
  result:                     (reportingId, resultId) => `/admin/api/reporting-jobs/${reportingId}/results/${resultId}`,
  previewEmail:               (reportingId, resultId) => `/admin/api/reporting-jobs/${reportingId}/results/${resultId}/preview-email`,
  results:                    (reportingId)           => `/admin/api/reporting-jobs/${reportingId}/results`,
  searchThemes:               (reportingId)           => `/admin/api/reporting-jobs/${reportingId}/search-themes`,
  splitRules:                 (reportingId)           => `/admin/api/reporting-jobs/${reportingId}/split-rules`,
  workspaceRules:             (reportingId)           => `/admin/api/reporting-jobs/${reportingId}/workspace-rules`,
  executeJob:                 (reportingId)           => `/admin/api/reporting-jobs/${reportingId}/execute`,
  emailTemplates:                                               '/admin/api/reporting-jobs/email-templates',
  reloadEmailTemplate:         (templateId)           => `/admin/api/reporting-jobs/email-templates/clear-cache/${templateId}`,
};
/* eslint-enable */

export default {
  ...baseApi,

  // ===================================================================
  // Reporting Jobs
  // ===================================================================

  async getReportingJobs(params) {
    const { response } = await this.get(
      NetKeys.REPORTING_JOBS,
      api.reportingJobs,
      { params },
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async createReportingJob(params) {
    const { response } = await this.post(
      NetKeys.REPORTING_JOB,
      api.reportingJobs,
      params,
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async getReportingJob(id) {
    const { response } = await this.get(
      NetKeys.REPORTING_JOB,
      api.reportingJob(id),
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async updateReportingJob(id, params) {
    const { response } = await this.put(
      NetKeys.REPORTING_JOB,
      api.reportingJob(id),
      params,
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async deleteReportingJob(id) {
    const { response } = await this.delete(
      NetKeys.REPORTING_JOB,
      api.reportingJob(id),
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  // ===================================================================
  // Reporting Job Data Sources
  // ===================================================================

  async getDataSources(reportingId) {
    const { response } = await this.get(
      NetKeys.REPORTING_JOB_DATA_SOURCES,
      api.dataSources(reportingId),
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async updateDataSources(reportingJobId, params) {
    const { response } = await this.put(
      NetKeys.REPORTING_JOB_DATA_SOURCES,
      api.dataSources(reportingJobId),
      params,
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async getDataSourceOverviews(reportingId, params) {
    const { response } = await this.get(
      NetKeys.REPORTING_JOB_DATA_SOURCE_OVERVIEWS,
      api.dataSourceOverviews(reportingId),
      {
        params,
        timeout: 180000, // 3 minutes timeout for survey overviews
      },
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async getDataSourceOverview(reportingId, externalId, params) {
    const { response } = await this.get(
      NetKeys.REPORTING_JOB_DATA_SOURCE_OVERVIEWS,
      api.dataSourceOverview(reportingId, externalId),
      { params,
        timeout: 180000, // 3 minutes timeout for survey overviews
      },
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async previewDataSourcesResponses(reportingId, params) {
    const { response } = await this.get(
      NetKeys.REPORTING_JOB_DATA_SOURCE_PREVIEW_RESPONSES,
      api.dataSourcePreviewResponses(reportingId),
      { params },
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  // ===================================================================
  // Reporting Job Split Rules
  // ===================================================================

  async getSplitRules(reportingId) {
    const { response } = await this.get(
      NetKeys.REPORTING_JOB_SPLIT_RULES,
      api.splitRules(reportingId),
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async updateSplitRules(reportingJobId, params) {
    const { response } = await this.put(
      NetKeys.REPORTING_JOB_SPLIT_RULES,
      api.splitRules(reportingJobId),
      params,
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  // ===================================================================
  // Reporting Job Workspace Rules
  // ===================================================================

  async getWorkspaceRules(reportingId) {
    const { response } = await this.get(
      NetKeys.REPORTING_JOB_WORKSPACE_RULES,
      api.workspaceRules(reportingId),
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async updateWorkspaceRules(reportingJobId, params) {
    const { response } = await this.put(
      NetKeys.REPORTING_JOB_WORKSPACE_RULES,
      api.workspaceRules(reportingJobId),
      params,
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  // ===================================================================
  // Reporting Job Search Themes
  // ===================================================================

  async getSearchThemes(reportingId) {
    const { response } = await this.get(
      NetKeys.REPORTING_JOB_SEARCH_THEMES,
      api.searchThemes(reportingId),
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async updateSearchThemes(reportingJobId, params) {
    const { response } = await this.put(
      NetKeys.REPORTING_JOB_SEARCH_THEMES,
      api.searchThemes(reportingJobId),
      params,
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  // ===================================================================
  // Reporting Job Email Configs
  // ===================================================================

  async getEmailConfigs(reportingId) {
    const { response } = await this.get(
      NetKeys.REPORTING_JOB_EMAIL_CONFIG,
      api.emailConfigs(reportingId),
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async updateEmailConfigs(reportingJobId, params) {
    const { response } = await this.put(
      NetKeys.REPORTING_JOB_EMAIL_CONFIGS,
      api.emailConfigs(reportingJobId),
      params,
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  // ===================================================================
  // Reporting Job Execution Statuses
  // ===================================================================

  async getExecutionStatuses(reportingId) {
    const { response } = await this.get(
      NetKeys.REPORTING_JOB_EXECUTION_STATUSES,
      api.executionStatuses(reportingId),
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  // ===================================================================
  // Reporting Job Results
  // ===================================================================

  async getResults(reportingId) {
    const { response } = await this.get(
      NetKeys.REPORTING_JOB_RESULTS,
      api.results(reportingId),
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async getResult(reportingId, resultId) {
    const { response } = await this.get(
      NetKeys.REPORTING_JOB_RESULTS,
      api.result(reportingId, resultId),
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async previewEmail(reportingId, resultId) {
    const { response } = await this.get(
      NetKeys.REPORTING_JOB_RESULT,
      api.previewEmail(reportingId, resultId),
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async deleteResult(reportingJobId, executionNumber) {
    const { response } = await this.delete(
      NetKeys.REPORTING_JOB_RESULT,
      api.results(reportingJobId),
      { params: { executionNumber } },
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async updateResult(reportingJobId, resultId, params) {
    const { response } = await this.put(
      NetKeys.REPORTING_JOB_RESULTS,
      api.result(reportingJobId, resultId),
      params,
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async dispatchReportEmail(reportingJobId, params) {
    const { response } = await this.post(
      NetKeys.REPORTING_JOB_DISPATCH_EMAILS,
      api.dispatchReportEmail(reportingJobId),
      params,
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  // ===================================================================
  // Reporting Job Manual Actions
  // ===================================================================

  async executeReportingJob(reportingId) {
    const { response } = await this.post(
      NetKeys.REPORTING_JOB_EXECUTION,
      api.executeJob(reportingId),
      null,
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  // ===================================================================
  // Reporting Job Email Templates
  // ===================================================================
  async getEmailTemplates() {
    const { response } = await this.get(
      NetKeys.REPORTING_JOB_EMAIL_TEMPLATES,
      api.emailTemplates,
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }
    return null;
  },

  async reloadEmailTemplate(templateId) {
    const { response } = await this.post(
      NetKeys.REPORTING_JOB_EMAIL_TEMPLATE_RELOAD,
      api.reloadEmailTemplate(templateId),
      null,
      null,
    );
    if (response && response.data !== '') {
      return response.data;
    }
    return null;
  },
};
