import baseApi from './BaseApi';
import NetKeys from '@/enum/network-keys';

/* eslint-disable */
const api = {
  downloadUsage: '/admin/api/usages/download',
  queryUsage:    '/admin/api/usages/view',
};
/* eslint-enable */

export default {
  ...baseApi,

  async downloadUsage(type, query) {
    await this.post(
      NetKeys.USAGE_DOWNLOAD,
      api.downloadUsage,
      query,
      { params: { type }, responseType: 'blob' },
    ).then(rs => {
      const url = URL.createObjectURL(new Blob([rs.response.data], {
        type: 'application/vnd.ms-excel',
      }));
      const link = document.createElement('a');
      link.href = url;
      link.download = 'usage.xlsx';
      document.body.appendChild(link);
      link.click();
    });
  },

  async queryUsage(type, query) {
    const { error, response } = await this.post(
      NetKeys.USAGE,
      api.queryUsage,
      query,
      { params: { type } },
    );

    return error ? error.response : response.data;
  },
};
