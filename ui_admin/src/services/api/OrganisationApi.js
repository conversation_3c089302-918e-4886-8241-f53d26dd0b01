import baseApi from './BaseApi';
import NetKeys from '@/enum/network-keys';

const domain = {
  organisation: 'organisation',
};

const api = {
  createOrganisation: `/admin/api/${domain.organisation}`,
  getOrganisations: `/admin/api/${domain.organisation}`,
  updateOrganisation: (id) => `/admin/api/${domain.organisation}/${id}`,
  addWorkspacesToOrganisation: (id) => `/admin/api/${domain.organisation}/${id}/workspaces`,
};

export default {
  ...baseApi,

  async getOrganisations(params) {
    const { response } = await this.get(
      NetKeys.ORGANISATIONS,
      api.getOrganisations,
      { params },
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async createOrganisation(params) {
    const { response } = await this.post(
      NetKeys.ORGANISATIONS,
      api.createOrganisation,
      params,
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async updateOrganisation(params) {
    const { response } = await this.put(
      NetKeys.ORGANISATIONS,
      api.updateOrganisation(params.id),
      params,
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async addWorkspacesToOrganisation(id, workspaceIds) {
    const { error, response } = await this.put(
      NetKeys.ORGANISATIONS,
      api.addWorkspacesToOrganisation(id),
      workspaceIds,
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return error ? error.response : response.data;
  },

  async removeOrganisation(id) {
    const { response } = await this.delete(
      NetKeys.ORGANISATIONS,
      api.updateOrganisation(id),
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async updateOrganisationLogo(id, file) {
    const formData = new FormData();

    formData.append('file', file);

    const { response } = await this.post(
      NetKeys.ORGANISATIONS,
      `${api.updateOrganisation(id)}/logo`,
      formData,
      { headers: { 'Content-Type': 'multipart/form-data' } },
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },
};
