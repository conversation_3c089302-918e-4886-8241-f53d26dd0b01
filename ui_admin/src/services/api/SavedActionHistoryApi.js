import baseApi from './BaseApi';
import NetKeys from '@/enum/network-keys';

const domain = {
  historySavedAction: 'history-saved-actions',
};

const api = {
  historySavedActions: `/admin/api/${domain.historySavedAction}`,
  restoreHistorySavedActions: `/admin/api/${domain.historySavedAction}/restore`,
};

export default {
  ...baseApi,

  async getHistorySavedActions(params) {
    const { response } = await this.get(
      NetKeys.SAVED_ACTIONS,
      api.historySavedActions,
      { params },
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async deleteHistorySavedActions(ids) {
    const { response } = await this.delete(
      NetKeys.SAVED_ACTIONS,
      api.historySavedActions,
      { params: { ids } },
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async restoreHistorySavedActions(ids) {
    const { response } = await this.post(
      NetKeys.SAVED_ACTIONS,
      api.restoreHistorySavedActions,
      null,
      { params: { ids } },
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

};
