import api from '@/helpers/api';
import errorMessages from '@/helpers/http-error-messages';
import NetworkStatus from '@/enum/network-status';
import store from '@/store/index';

import { statusMapper } from '@/helpers/http-error-mapper';

const offlineMessage =
    'You appear to be offline. Please check your internet connection and try again.';

const errorHandlers = {
  401: () => {
    store.dispatch(
      'network/setAuthorized',
      false,
      true,
    );
  },
};

const defaultErrorHandler = error => {
  const handler = errorHandlers[error.response.status];

  if (handler != null) handler(error);
};

export default {
  store,

  delete(key, ...args) {
    return this.request('delete', key, ...args);
  },

  get(key, ...args) {
    return this.request('get', key, ...args);
  },

  post(key, ...args) {
    return this.request('post', key, ...args);
  },

  put(key, ...args) {
    return this.request('put', key, ...args);
  },

  async request(method, key, ...args) {
    const result = {};

    try {
      this.setStatus(key, NetworkStatus.LOADING);

      result.response = await api.instance()[method](...args);

      this.setStatus(key, NetworkStatus.SUCCESS);
    } catch (error) {
      if (error.response == null) {
        this.setError(key, offlineMessage);
      } else {
        defaultErrorHandler(error);
        const message = statusMapper(
          error,
          ...Object.entries(errorMessages[key]).flat(),
        );

        this.setError(key, message);
      }

      this.setStatus(key, NetworkStatus.ERROR);
      result.error = error;
    }

    return result;
  },

  dispatch(key, payload) {
    this.store.dispatch(key, payload, { root: true });
  },

  handleResponse(response, dispatches) {
    if (response != null && dispatches != null) {
      Object.entries(dispatches).forEach(([key, payload]) =>
        this.dispatch(key, payload));
    }

    if (response != null) {
      return response.data;
    }

    return null;
  },

  setError(key, message) {
    this.dispatch('network/setError', { key, message });
  },

  setStatus(key, status) {
    this.dispatch('network/setStatus', { key, status });
  },
};
