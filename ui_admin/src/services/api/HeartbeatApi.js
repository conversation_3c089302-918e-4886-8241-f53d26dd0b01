import baseApi from './BaseApi';
import NetKeys from '@/enum/network-keys';

const domain = {
  heartbeats: 'heartbeats',
};

const api = {
  heartbeats: `/admin/api/${domain.heartbeats}`,
  namespace: (id) => `/admin/api/${domain.heartbeats}/${id}/namespace`,
};

export default {
  ...baseApi,

  async deleteHeartbeats(ids) {
    const { response } = await this.delete(
      NetKeys.HEARTBEATS,
      api.heartbeats,
      { params: { ids } },
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async getHeartbeats(params) {
    const { response } = await this.get(
      NetKeys.HEARTBEATS,
      api.heartbeats,
      { params },
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async updateHeartbeatNamespace(id, namespace) {
    const { response } = await this.post(
      NetKeys.HEARTBEATS,
      api.namespace(id),
      null,
      { params: { namespace } },
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

};
