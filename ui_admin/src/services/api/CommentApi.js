import baseApi from './BaseApi';
import NetKeys from '@/enum/network-keys';

/* eslint-disable */
const api = {
  comments: '/admin/api/comments',
};
/* eslint-enable */

export default {
  ...baseApi,

  async getComments(params) {
    const { response } = await this.get(
      NetKeys.COMMENTS,
      api.comments,
      { params },
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },
};
