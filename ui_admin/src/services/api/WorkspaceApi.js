import baseApi from './BaseApi';
import NetKeys from '@/enum/network-keys';

const domain = {
  workspace: 'workspace',
};

const api = {
  workspaces: `/admin/api/${domain.workspace}`,
  transfer: `/admin/api/${domain.workspace}/transfer`,
  workspace: (id) => `/admin/api/${domain.workspace}/${id}`,

};

export default {
  ...baseApi,

  async getWorkspaces(params) {
    const { response } = await this.get(
      NetKeys.WORKSPACE,
      api.workspaces,
      { params },
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async updateWorkspace(params) {
    const { response } = await this.post(
      NetKeys.WORKSPACE,
      api.workspace(params.id),
      null,
      { params },
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async removeWorkspace(workspaceId) {
    const { response } = await this.delete(
      NetKeys.WORKSPACE,
      api.workspace(workspaceId),
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async transferUser(params) {
    const { response } = await this.post(
      NetKeys.WORKSPACE,
      api.transfer,
      null,
      { params },
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async createWorkspace(workspace) {
    const { response } = await this.post(
      NetKeys.WORKSPACE,
      api.workspaces,
      workspace,
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },
};
