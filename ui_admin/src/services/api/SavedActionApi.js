import baseApi from './BaseApi';
import NetKeys from '@/enum/network-keys';

const domain = {
  savedActions: 'saved-actions',
};

const api = {
  getSavedActions: `/admin/api/${domain.savedActions}`,
  getSavedActionLists: `/admin/api/${domain.savedActions}/lists`,
  copySavedActionList: (id) => `/admin/api/${domain.savedActions}/lists/${id}/copy`,
};

export default {
  ...baseApi,

  async deleteSavedActions(ids) {
    const { response } = await this.delete(
      NetKeys.SAVED_ACTIONS,
      api.getSavedActions,
      { params: { ids } },
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async deleteSavedActionLists(ids) {
    const { response } = await this.delete(
      NetKeys.SAVED_ACTIONS,
      api.getSavedActionLists,
      { params: { ids } },
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async getSavedActions(params) {
    const { response } = await this.get(
      NetKeys.SAVED_ACTIONS,
      api.getSavedActions,
      { params },
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async getSavedActionLists(params) {
    const { response } = await this.get(
      NetKeys.SAVED_ACTIONS,
      api.getSavedActionLists,
      { params },
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async copySavedActionList(id, request) {
    const { response } = await this.post(
      NetKeys.SAVED_ACTIONS,
      api.copySavedActionList(id),
      request,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

};
