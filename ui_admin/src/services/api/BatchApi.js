import baseApi from './BaseApi';
import NetKeys from '@/enum/network-keys';

const domain = {
  batches: 'batches',
};

const api = {
  batches: `/admin/api/${domain.batches}`,
  jobParams: (jobExecutionId) => `/admin/api/${domain.batches}/${jobExecutionId}/job-params`,
  completedBatches: `/admin/api/${domain.batches}/completed`,
};

export default {
  ...baseApi,

  async getBatches(params) {
    const { response } = await this.get(
      NetKeys.BATCHES,
      api.batches,
      { params },
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async deleteBatches(ids) {
    const { error, response } = await this.delete(
      NetKeys.BATCHES,
      api.batches,
      { params: { ids } },
    );

    return error ? error.response : response.data;
  },

  async deleteAllBatches() {
    const { error, response } = await this.delete(
      NetKeys.BATCHES,
      api.completedBatches,
    );

    return error ? error.response : response.data;
  },

  async getJobParams(jobExecutionId) {
    const { error, response } = await this.get(
      NetKeys.BATCHES,
      api.jobParams(jobExecutionId),
    );

    return error ? error.response : response.data;
  },
};
