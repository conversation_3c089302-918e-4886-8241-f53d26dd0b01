import baseApi from './BaseApi';
import NetKeys from '@/enum/network-keys';

const domain = {
  userTiers: 'user-tiers',
  product_features: 'product-features',
};

const api = {
  getUserTiers: `/admin/api/${domain.userTiers}`,
  createUserTier: `/admin/api/${domain.userTiers}`,
  deleteUserTier: (tierId) => `/admin/api/${domain.userTiers}/${tierId}`,
  updateUserTier: (tierId) => `/admin/api/${domain.userTiers}/${tierId}`,
  getProductFeatures: `/admin/api/${domain.userTiers}/${domain.product_features}`,
};

export default {
  ...baseApi,

  async getUserTiers(params) {
    const { response } = await this.get(
      NetKeys.USER_TIERS,
      api.getUserTiers,
      { params },
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async getProductFeatures() {
    const { response } = await this.get(
      NetKeys.USER_TIERS,
      api.getProductFeatures,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async deleteUserTiers(tierId) {
    const { error, response } = await this.delete(
      NetKeys.USER_TIERS,
      api.deleteUserTier(tierId),
    );

    return error ? error.response : response.data;
  },

  async updateUserTier(tierId, tier) {
    const { error, response } = await this.post(
      NetKeys.USER_TIERS,
      api.updateUserTier(tierId),
      tier,
    );

    return error ? error.response : response.data;
  },

  async createUserTier(tier) {
    const { error, response } = await this.post(
      NetKeys.USER_TIERS,
      api.createUserTier,
      tier,
    );

    return error ? error.response : response.data;
  },
};
