import baseApi from './BaseApi';
import NetKeys from '@/enum/network-keys';

const api = {
  fetch: '/admin/api/dataset-statuses',
};

export default {
  ...baseApi,

  async fetchDatasetStatuses(params) {
    const { response } = await this.get(
      NetKeys.DATASET_STATUSES,
      api.fetch,
      { params },
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },
};
