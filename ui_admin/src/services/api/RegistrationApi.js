import baseApi from './BaseApi';
import NetKeys from '@/enum/network-keys';

const domain = {
  registrations: 'registrations',
};

const api = {
  getRegistrations: `/admin/api/${domain.registrations}`,
};

export default {
  ...baseApi,

  async getRegistrations(params) {
    const { response } = await this.get(
      NetKeys.REGISTRATIONS,
      api.getRegistrations,
      { params },
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },
};
