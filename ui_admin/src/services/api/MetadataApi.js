import baseApi from './BaseApi';
import NetKeys from '@/enum/network-keys';

/* eslint-disable */
const api = {
  ratingScales: '/admin/api/metadata/rating-scales'
};
/* eslint-enable */

export default {
  ...baseApi,

  async fetchRatingScales(userId) {
    const { response } = await this.get(
      NetKeys.RATING,
      api.ratingScales,
      { params: { userId } },
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async createRatingScale(params) {
    const { response } = await this.post(
      NetKeys.RATING,
      api.ratingScales,
      params,
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },
};
