import baseApi from './BaseApi';
import NetKeys from '@/enum/network-keys';

const domain = {
  dataset: 'datasets',
};

const api = {
  fetch: `/admin/api/${domain.dataset}`,
  cancel: `/admin/api/${domain.dataset}/cancel`,
  regenerate: `/admin/api/${domain.dataset}/regenerate`,
  reaggregate: `/admin/api/${domain.dataset}/reaggregate`,
  remodel: `/admin/api/${domain.dataset}/remodel`,
  reanalyse: `/admin/api/${domain.dataset}/reanalyse`,
  intensity: `/admin/api/${domain.dataset}/intensity`,
  delete: `/admin/api/${domain.dataset}`,
};

export default {
  ...baseApi,

  async fetchDatasets(params) {
    const { response } = await this.get(
      NetKeys.DATASETS,
      api.fetch,
      { params },
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async cancelDataset(ids) {
    const { response } = await this.post(
      NetKeys.DATASETS,
      api.cancel,
      ids,
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async regenerateDataset(ids) {
    const { response } = await this.post(
      NetKeys.DATASETS,
      api.regenerate,
      ids,
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async reaggregateDataset(ids) {
    const { response } = await this.post(
      NetKeys.DATASETS,
      api.reaggregate,
      ids,
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async remodelDataset(ids) {
    const { response } = await this.post(
      NetKeys.DATASETS,
      api.remodel,
      ids,
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async reanalyseDataset(ids) {
    const { response } = await this.post(
      NetKeys.DATASETS,
      api.reanalyse,
      ids,
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async intensityDataset(ids) {
    const { response } = await this.post(
      NetKeys.DATASETS,
      api.intensity,
      ids,
      null,
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return null;
  },

  async deleteDataset(ids) {
    const { response, error } = await this.delete(
      NetKeys.DATASETS,
      api.delete,
      { params: { ids } },
    );

    if (response && response.data !== '') {
      return response.data;
    }

    return error;
  },

};
