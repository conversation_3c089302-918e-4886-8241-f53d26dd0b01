import { mapState, mapActions } from '@/services/BaseRequest';

import UserTierApi from '@/services/api/UserTierApi';
import ProductFeatures from '@/enum/product-features';

const userTierRequest = {

  get tierParameters() {
    return {
      page: this.page,
      pageSize: this.rows,
      sorting: this.sorting,
    };
  },

  async getProductFeatures() {
    const productFeatures = await UserTierApi.getProductFeatures();
    const features = productFeatures.map(feature => {
      return ProductFeatures.enumValueOf(feature);
    });
    this.setProductFeatures({ features });
  },

  async getUserTiers() {
    const tiers = await UserTierApi.getUserTiers();
    this.setUserTiers({ tiers });
  },

  async deleteUserTiers(tierId) {
    return UserTierApi.deleteUserTiers(tierId);
  },

  async updateUserTier(tierId, tier) {
    return UserTierApi.updateUserTier(tierId, tier);
  },

  async createUserTier(tier) {
    return UserTierApi.createUserTier(tier);
  },

};

mapState(userTierRequest, 'userTier', [
  'selectedUserTier',
  'page',
  'pages',
  'rows',
]);
mapActions(userTierRequest, 'userTier', ['setUserTiers', 'setProductFeatures']);

export default userTierRequest;
