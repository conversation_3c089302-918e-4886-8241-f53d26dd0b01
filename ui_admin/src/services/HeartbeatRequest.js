import { mapState, mapActions } from '@/services/BaseRequest';

import Heartbeat<PERSON><PERSON> from '@/services/api/HeartbeatApi';

const heartbeatRequest = {

  get heartbeatParameters() {
    return {
      filterLabel: this.search,
      filterOwner: this.filterOwner,
      filterStatus: this.filterStatus,
      filterUuid: this.filterUuid,
      filterWorkspaceLabel: this.filterWorkspaceLabel,
      filterIds: this.filterIds,
      page: this.page,
      pageSize: this.rows,
      sorting: this.sorting,
    };
  },

  async fetchHeartbeats() {
    const heartbeats = await HeartbeatApi.getHeartbeats(this.heartbeatParameters);
    this.setHeartbeats({ heartbeats });
  },

  async deleteHeartbeats(ids) {
    await HeartbeatApi.deleteHeartbeats(ids);
  },

  async updateHeartbeatNamespace(id, namespace) {
    await HeartbeatApi.updateHeartbeatNamespace(id, namespace);
  },
};

mapState(heartbeatRequest, 'heartbeat', [
  'search',
  'filterStatus',
  'filterUuid',
  'filterWorkspaceLabel',
  'page',
  'pages',
  'rows',
  'selectedHeartbeat',
  'sorting',
]);
mapActions(heartbeatRequest, 'heartbeat', [
  'setHeartbeats',
  'setPages',
  'setPage',
  'setRows',
]);

export default heartbeatRequest;
