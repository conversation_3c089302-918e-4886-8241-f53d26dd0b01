import { mapActions, mapGetters, mapState } from '@/services/BaseRequest';

import UserApi from '@/services/api/UserApi';

const userRequest = {

  get usersParameters() {
    return {
      filterLoginName: this.search,
      filterIds: this.filterIds,
      filterTierId: this.filterTierId,
      filterActive: this.filterActive,
      page: this.page,
      pages: this.pages,
      rows: this.rows,
    };
  },

  async addActiveUsage(userId) {
    return UserApi.addActiveUsageForUser(userId);
  },

  async addUserToOrganisation(userId, request) {
    return UserApi.addUserToOrganisation(userId, request);
  },

  async addUserToWorkspaces(userId, request) {
    return UserApi.addUserToWorkspaces(userId, request);
  },

  async createUser(user) {
    return UserApi.createUser(user);
  },

  async deleteUsers(userIds) {
    return UserApi.deleteUsers(userIds);
  },

  async extendUsage(userId, usageToAdd) {
    return UserApi.extendUsageForUser(userId, usageToAdd);
  },

  async fetchUser() {
    const users = await UserApi.getUsers(this.usersParameters);
    this.setUsers({ u: users });
    if (this.selectedUser) {
      this.setSelectedUser(users.find(t => { return t.id === this.selectedUser.id; }));
    }
  },

  async getLoginData(userId) {
    return UserApi.getLoginData(userId);
  },

  async getUsers() {
    return UserApi.getUsers(this.usersParameters);
  },

  async updateUser(userId, user) {
    return UserApi.updateUser(userId, user);
  },

  async updateActive(userId, checked) {
    return UserApi.updateActive(userId, checked);
  },

  async updatePassword(userId, password) {
    return UserApi.updatePassword(userId, password);
  },

  async updateTier(userId, userTierId) {
    return UserApi.updateTier(userId, userTierId);
  },

  async resetPassword(email) {
    return UserApi.resetPassword(email);
  },

  async renewUsage(userId) {
    return UserApi.renewUsageForUser(userId);
  },
};

mapState(userRequest, 'user', [
  'filterActive',
  'filterIds',
  'filterTierId',
  'page',
  'pages',
  'rows',
  'search',
  'selectedUser',
  'selectedUsers',
]);

mapGetters(userRequest, 'user', ['getSelectedUserIds']);

mapActions(userRequest, 'user', [
  'setUsers',
  'setPages',
  'setPage',
  'setRows',
  'setSelectedUser',
]);

export default userRequest;
