import { mapState, mapActions } from '@/services/BaseRequest';

import ReportingJobApi from '@/services/api/ReportingJobApi';

const reportingJobRequest = {

  // ===================================================================
  // Reporting Jobs
  // ===================================================================

  get parameters() {
    return {
      filterId: this.filterId,
      filterUserIds: this.filterUserIds,
      page: this.page,
      pageSize: this.rows,
      sorting: this.sorting,
    };
  },

  async fetchReportingJobs() {
    const reportingJobs = await ReportingJobApi.getReportingJobs(this.parameters);
    this.setReportingJobs(reportingJobs);
  },

  async createReportingJob(reportingJob) {
    const newReportingJob = await ReportingJobApi.createReportingJob(reportingJob);
    this.setSelectedReportingJob(newReportingJob);
    return newReportingJob;
  },

  async getReportingJob(id) {
    const reportingJob = await ReportingJobApi.getReportingJob(id);
    this.setSelectedReportingJob(reportingJob);
    return reportingJob;
  },

  async updateReportingJob(id, reportingJob) {
    const updated = await ReportingJobApi.updateReportingJob(id, reportingJob);
    this.setSelectedReportingJob(updated);
    return updated;
  },

  async deleteReportingJob(id) {
    await ReportingJobApi.deleteReportingJob(id);
  },

  // ===================================================================
  // Reporting Job Data Sources
  // ===================================================================

  async getDataSources(reportingJobId) {
    return ReportingJobApi.getDataSources(reportingJobId);
  },

  async updateDataSources(reportingJobId, dataSources) {
    return ReportingJobApi.updateDataSources(reportingJobId, dataSources);
  },

  async getDataSourceOverviews(reportingJobId) {
    return ReportingJobApi.getDataSourceOverviews(reportingJobId, { source: 'qualtrics' });
  },

  async getDataSourceOverview(reportingJobId, externalId) {
    return ReportingJobApi.getDataSourceOverview(reportingJobId, externalId, { source: 'qualtrics' });
  },

  async previewDataSourceResponses(reportingJobId, externalId) {
    return ReportingJobApi.previewDataSourcesResponses(reportingJobId, { source: 'qualtrics', externalId });
  },

  // ===================================================================
  // Reporting Job Split Rules
  // ===================================================================

  async getSplitRules(reportingJobId) {
    return ReportingJobApi.getSplitRules(reportingJobId);
  },

  async updateSplitRules(reportingJobId, splitRules) {
    return ReportingJobApi.updateSplitRules(reportingJobId, splitRules);
  },

  // ===================================================================
  // Reporting Job Workspace Rules
  // ===================================================================

  async getWorkspaceRules(reportingJobId) {
    return ReportingJobApi.getWorkspaceRules(reportingJobId);
  },

  async updateWorkspaceRules(reportingJobId, workspaceRules) {
    return ReportingJobApi.updateWorkspaceRules(reportingJobId, workspaceRules);
  },

  // ===================================================================
  // Reporting Job Search Themes
  // ===================================================================

  getSearchThemes(reportingJobId) {
    return ReportingJobApi.getSearchThemes(reportingJobId);
  },

  async updateSearchThemes(reportingJobId, searchThemes) {
    return ReportingJobApi.updateSearchThemes(reportingJobId, searchThemes);
  },

  // ===================================================================
  // Reporting Job Email Configs
  // ===================================================================

  async getEmailConfigs(reportingJobId) {
    return ReportingJobApi.getEmailConfigs(reportingJobId);
  },

  async updateEmailConfigs(reportingJobId, emailConfig) {
    return ReportingJobApi.updateEmailConfigs(reportingJobId, emailConfig);
  },

  // ===================================================================
  // Reporting Job Execution Statuses
  // ===================================================================

  async getExecutionStatuses(reportingJobId) {
    return ReportingJobApi.getExecutionStatuses(reportingJobId);
  },

  // ===================================================================
  // Reporting Job Results
  // ===================================================================

  async getResult(reportingJobId, resultId) {
    const result = await ReportingJobApi.getResult(reportingJobId, resultId);
    this.setSelectedResult(result);
    return result;
  },

  async fetchResults(reportingJobId) {
    this.setReportingJobResults([]);
    const results = await ReportingJobApi.getResults(reportingJobId);
    this.setReportingJobResults(results);
  },

  async deleteResult(reportingJobId, executionNumber) {
    await ReportingJobApi.deleteResult(reportingJobId, executionNumber);
  },

  async updateResult(reportingJobId, resultId, result) {
    return ReportingJobApi.updateResult(reportingJobId, resultId, result);
  },

  async previewEmail(reportingJobId, resultId) {
    return ReportingJobApi.previewEmail(reportingJobId, resultId);
  },

  async dispatchReportEmail(reportingJobId, params) {
    return ReportingJobApi.dispatchReportEmail(reportingJobId, params);
  },

  // ===================================================================
  // Reporting Job Manual Actions
  // ===================================================================
  async executeReportingJob(reportingJobId) {
    return ReportingJobApi.executeReportingJob(reportingJobId);
  },

  // ===================================================================
  // Reporting Job Email Templates
  // ===================================================================
  async getEmailTemplates() {
    return ReportingJobApi.getEmailTemplates();
  },

  async reloadEmailTemplate(templateId) {
    return ReportingJobApi.reloadEmailTemplate(templateId);
  },

};

mapState(reportingJobRequest, 'reportingJob', [
  'filterId',
  'filterUserIds',
  'page',
  'rows',
  'sorting',
]);

mapActions(reportingJobRequest, 'reportingJob', [
  'setReportingJobResults',
  'setReportingJobs',
  'setSelectedReportingJob',
  'setSelectedResult',
]);

export default reportingJobRequest;
