import { mapActions } from '@/services/BaseRequest';

import AuthApi from '@/services/api/AuthApi';

const authRequest = {
  async login(email, password) {
    return AuthApi.login(email, password);
  },

  async logout() {
    await AuthApi.logout();
    await this.reset();
    this.setAuthorized(false);
  },

  async me() {
    const user = await AuthApi.me();
    if (user) {
      this.setUser(user);
      this.setAuthorized(true);
    }
  },
};

mapActions(authRequest, 'user', ['reset', 'setUser']);

mapActions(authRequest, 'network', ['setAuthorized']);

export default authRequest;
