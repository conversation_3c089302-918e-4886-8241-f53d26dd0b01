import { mapState, mapActions } from '@/services/BaseRequest';

import SavedAction<PERSON><PERSON> from '@/services/api/SavedActionApi';

const savedActionRequest = {

  get savedActionParameters() {
    return {
      filterIds: this.filterIds,
      filterName: this.filterName,
      filterThemeLabel: this.filterThemeLabel,
      filterType: this.filterType,
      filterWorkspaceLabel: this.filterWorkspaceLabel,
      page: this.page,
      pageSize: this.rows,
      sorting: this.sorting,
    };
  },

  async fetchSavedActions() {
    const actions = await SavedActionApi.getSavedActions(this.savedActionParameters);
    this.setSavedActions({ savedActions: actions });
  },

  async deleteSavedActions(ids) {
    await SavedActionApi.deleteSavedActions(ids);
  },

};

mapState(savedActionRequest, 'savedAction', [
  'filterIds',
  'filterName',
  'filterThemeLabel',
  'filterType',
  'filterWorkspaceLabel',
  'page',
  'pages',
  'rows',
  'selectedSavedAction',
  'sorting',
]);
mapActions(savedActionRequest, 'savedAction', [
  'setSavedActions',
  'setPages',
  'setPage',
  'setRows',
]);

export default savedActionRequest;
