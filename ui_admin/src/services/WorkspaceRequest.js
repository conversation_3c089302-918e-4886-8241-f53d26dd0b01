import { mapState, mapActions } from '@/services/BaseRequest';

import Workspace<PERSON>pi from '@/services/api/WorkspaceApi';

const workspaceRequest = {

  get workspaceParameters() {
    return {
      filterLabel: this.search,
      filterOwner: this.filterOwner,
      page: this.page,
      pages: this.pages,
      pageSize: this.rows,
    };
  },

  async fetchWorkspaces() {
    const workspaces = await WorkspaceApi.getWorkspaces(this.workspaceParameters);
    this.setWorkspaces({ workspaces });
  },

  async getWorkspaces() {
    return WorkspaceApi.getWorkspaces(this.workspaceParameters);
  },

  async updateWorkspace() {
    const workspace = this.selectedWorkspace;
    return WorkspaceApi.updateWorkspace({ id: workspace.id, label: workspace.label, userLimit: workspace.userLimit, topicModelType: workspace.topicModelType });
  },

  async transferWorkspace(userId, workspaceId, workspaceRole) {
    return WorkspaceApi.transferUser({ userId, workspaceId, workspaceRole });
  },

  async createWorkspace(workspace) {
    return WorkspaceApi.createWorkspace(workspace);
  },

  async removeWorkspace() {
    const workspaceId = this.selectedWorkspace.id;
    return WorkspaceApi.removeWorkspace(workspaceId);
  },
};

mapState(workspaceRequest, 'workspace', [
  'filterOwner',
  'page',
  'pages',
  'rows',
  'search',
  'selectedWorkspace',
  'workspaces',
]);

mapActions(workspaceRequest, 'workspace', ['setWorkspaces', 'setSelectedWorkspace']);

export default workspaceRequest;
