import { mapState, mapActions } from '@/services/BaseRequest';

import OrganisationApi from '@/services/api/OrganisationApi';

const organisationRequest = {

  get organisationParameters() {
    return {
      filterLabel: this.filterLabel,
      filterName: this.filterName,
      filterOwner: this.filterOwner,
      page: this.page,
      pageSize: this.rows,
      sorting: this.sorting,
    };
  },

  async getOrganisations() {
    return OrganisationApi.getOrganisations(this.organisationParameters);
  },

  async fetchOrganisations() {
    const organisations = await OrganisationApi.getOrganisations(this.organisationParameters);
    this.setOrganisations({ organisations });
  },

  async createOrganisation() {
    return OrganisationApi.createOrganisation(this.selectedOrganisation);
  },

  async updateOrganisation() {
    return OrganisationApi.updateOrganisation(this.selectedOrganisation);
  },

  async removeOrganisation() {
    return OrganisationApi.removeOrganisation(this.selectedOrganisation.id);
  },

  async addWorkspacesToOrganisation(workspaceIds) {
    return OrganisationApi.addWorkspacesToOrganisation(this.selectedOrganisation.id, workspaceIds);
  },

  async uploadLogoOrganisation(file) {
    return OrganisationApi.updateOrganisationLogo(this.selectedOrganisation.id, file);
  },
};

mapState(organisationRequest, 'organisation', [
  'selectedOrganisation',
  'rows',
  'page',
  'sorting',
  'filterLabel',
  'filterName',
  'filterOwner',
]);
mapActions(organisationRequest, 'organisation', ['setOrganisations']);

export default organisationRequest;
