import { mapState, mapActions } from '@/services/BaseRequest';

import SavedActionHistory<PERSON><PERSON> from '@/services/api/SavedActionHistoryApi';

const savedActionHistoryRequest = {

  get savedActionParameters() {
    return {
      dateFrom: this.filterDateFrom,
      dateTo: this.filterDateTo,
      deletedBy: this.filterDeletedBy,
      ids: this.filterIds,
      outsideRange: this.filterOutsideRange,
      type: this.filterType,
      userId: this.filterUserId,
      workspaceId: this.filterWorkspaceId,
      page: this.page,
      pageSize: this.rows,
      sorting: this.sorting,
    };
  },

  async fetchSavedActionsHistory() {
    const actions = await SavedActionHistoryApi.getHistorySavedActions(this.savedActionParameters);
    this.setSavedActionsHistory({ savedActionsHistory: actions });
  },

  async deleteSavedActionHistory(ids) {
    await SavedActionHistoryApi.deleteHistorySavedActions(ids);
  },

  async restoreSavedActionHistory(ids) {
    await SavedActionHistoryApi.restoreHistorySavedActions(ids);
  },
};

mapState(savedActionHistoryRequest, 'savedActionHistory', [
  'filterDateFrom',
  'filterDateTo',
  'filterDeletedBy',
  'filterIds',
  'filterOutsideRange',
  'filterType',
  'filterUserId',
  'filterWorkspaceId',
  'page',
  'pages',
  'rows',
  'sorting',
]);
mapActions(savedActionHistoryRequest, 'savedActionHistory', [
  'setPages',
  'setPage',
  'setRows',
  'setSavedActionsHistory',
]);

export default savedActionHistoryRequest;
