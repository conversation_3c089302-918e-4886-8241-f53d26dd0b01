import { mapState, mapActions } from '@/services/BaseRequest';

import SavedAction<PERSON><PERSON> from '@/services/api/SavedActionApi';

const savedActionListRequest = {

  get savedActionParameters() {
    return {
      page: this.page,
      pages: this.pages,
      pageSize: this.rows,
      filterIds: this.filterIds,
      filterActionIds: this.filterActionIds,
      filterName: this.filterName,
      filterCollectionLabel: this.filterCollectionLabel,
      filterWorkspaceLabel: this.filterWorkspaceLabel,
    };
  },

  async fetchSavedActionLists() {
    const savedActionList = await SavedActionApi.getSavedActionLists(this.savedActionParameters);
    this.setSavedActionLists({ lists: savedActionList });
    return savedActionList;
  },

  async copySavedActionList(id, destUserId, destWorkspaceId, newLabel) {
    await SavedActionApi.copySavedActionList(id, { destUserId, destWorkspaceId, newLabel });
  },

  async deleteSavedActionLists(ids) {
    await SavedActionApi.deleteSavedActionLists(ids);
  },
};

mapState(savedActionListRequest, 'savedActionList', [
  'search',
  'page',
  'pages',
  'rows',
  'savedActionLists',
  'filterIds',
  'filterActionIds',
  'filterName',
  'filterCollectionLabel',
  'filterWorkspaceLabel',
]);
mapActions(savedActionListRequest, 'savedActionList', ['setSavedActionLists', 'setPages']);

export default savedActionListRequest;
