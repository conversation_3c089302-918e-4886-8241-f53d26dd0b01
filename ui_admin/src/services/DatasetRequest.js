import DatasetApi from '@/services/api/DatasetApi';
import { mapState, mapActions } from '@/services/BaseRequest';

const datasetRequest = {

  get datasetParameters() {
    return {
      filterArchived: this.filterArchived,
      filterHavingError: this.filterHavingError,
      filterId: this.filterId,
      filterIds: this.filterIds,
      filterLabel: this.search,
      filterStatus: this.filterStatus,
      filterUserIds: this.filterUserIds,
      fromDate: this.filterFromDate,
      page: this.page,
      pageSize: this.rows,
      sorting: this.sorting,
      toDate: this.filterToDate,
    };
  },

  async getDatasets() {
    return DatasetApi.fetchDatasets(this.datasetParameters);
  },

  async fetchDatasets() {
    const datasets = await DatasetApi.fetchDatasets(this.datasetParameters);
    this.setDatasets({ datasets });
  },

  cancelDataset() {
    return DatasetApi.cancelDataset(this.selectedDatasets.map(ds => ds.id));
  },

  regenerateDataset() {
    return DatasetApi.regenerateDataset(this.selectedDatasets.map(ds => ds.id));
  },

  reaggregateDataset() {
    return DatasetApi.reaggregateDataset(this.selectedDatasets.map(ds => ds.id));
  },

  remodelDataset() {
    return DatasetApi.remodelDataset(this.selectedDatasets.map(ds => ds.id));
  },

  reanalyseDataset() {
    return DatasetApi.reanalyseDataset(this.selectedDatasets.map(ds => ds.id));
  },

  intensityDataset() {
    return DatasetApi.intensityDataset(this.selectedDatasets.map(ds => ds.id));
  },

  deleteDataset() {
    return DatasetApi.deleteDataset(this.selectedDatasets.map(ds => ds.id));
  },

};

mapState(datasetRequest, 'dataset', [
  'filterArchived',
  'filterFromDate',
  'filterHavingError',
  'filterId',
  'filterIds',
  'filterStatus',
  'filterToDate',
  'filterUserIds',
  'page',
  'rows',
  'search',
  'selectedDatasets',
  'sorting',
]);

mapActions(datasetRequest, 'dataset', ['setDatasets']);

export default datasetRequest;
