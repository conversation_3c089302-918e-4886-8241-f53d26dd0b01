import { mapState, mapActions } from '@/services/BaseRequest';
import Batch<PERSON>pi from '@/services/api/BatchApi';

const batchRequest = {

  get batchParameters() {
    return {
      filterJobName: this.search,
      filterStatus: this.filterStatus,
      page: this.page,
      pageSize: this.rows,
      sorting: this.sorting,
    };
  },

  async getJobParams(selectedBatch) {
    selectedBatch.params = await BatchApi.getJobParams(selectedBatch.jobExecutionId);
    this.setSelectedBatch({ selectedBatch });
  },

  async fetchBatches() {
    const batches = await BatchApi.getBatches(this.batchParameters);
    this.setBatches({ batches });
  },

  async deleteBatches(ids) {
    return BatchApi.deleteBatches(ids);
  },

  async deleteAllBatches() {
    return BatchApi.deleteAllBatches();
  },
};

mapState(batchRequest, 'batch', [
  'selectedBatch',
  'search',
  'filterStatus',
  'page',
  'pages',
  'rows',
]);
mapActions(batchRequest, 'batch', ['setBatches', 'setSelectedBatch']);

export default batchRequest;
