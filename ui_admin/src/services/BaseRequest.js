import { isPlainObject } from 'lodash-es';

import store from '@/store';

/**
 * Access getter from store using key and optional module.
 *
 * @param {Object} param
 * @param {String} param.module Name of module to access getter from
 * @param {String} param.key Name of getter to access
 */
const getGetter = ({ module, key }) => {
  const qualifiedKey = module == null ? key : `${module}/${key}`;

  return store.getters[qualifiedKey];
};

/**
 * Access state from store using key and optional module.
 *
 * @param {Object} param
 * @param {String} param.module Name of module to access state from
 * @param {String} param.key Name of state property to access
 */
const getState = ({ module, key }) => {
  return module == null
    ? store.state[key]
    : store.state[module][key];
};

/**
 * Return a module of dynamic getters given an optional module and list of getters.
 *
 * @param {Object} param
 * @param {String} param.module Name of module to access getters from
 * @param {String[]} param.getters List of getter keys to access
 */
const processGettersArray = (target, { module, getters }) => {
  return getters.reduce((result, key) => {
    Object.defineProperty(result, key, {
      enumerable: true,

      get() {
        return getGetter({ module, key });
      },
    });

    return result;
  }, target);
};

/**
 * Return a module of dynamic getters given an optional module and a map of the store key, and the
 * key to assign it to.
 *
 * @param {Object} param
 * @param {String} param.module Name of module to access getters from
 * @param {Object} param.getters Map of getter keys to access in the form `{ assignKey: getterKey }`
 */
const processGettersObject = (target, { module, getters }) => {
  return Object.entries(getters).reduce((result, [assignKey, getterKey]) => {
    Object.defineProperty(result, assignKey, {
      enumerable: true,

      get() {
        return getGetter({ module, key: getterKey });
      },
    });

    return result;
  }, target);
};

/**
 * Return a module of dynamic state getters given an optional module and list of state keys.
 *
 * @param {Object} param
 * @param {String} param.module Name of module to access getters from
 * @param {String[]} param.state List of state keys to access
 */
const processStateArray = (target, { module, state }) => {
  return state.reduce((result, key) => {
    Object.defineProperty(result, key, {
      enumerable: true,

      get() {
        return getState({ module, key });
      },
    });

    return result;
  }, target);
};

/**
 * Return a module of dynamic state getters given an optional module and a map of the store key,
 * and the key to assign it to.
 *
 * @param {Object} param
 * @param {String} param.module Name of module to access getters from
 * @param {Object} param.state Map of state keys to access in the form `{ assignKey: stateKey }`
 */
const processStateObject = (target, { module, state }) => {
  return Object.entries(state).reduce((result, [assignKey, stateKey]) => {
    Object.defineProperty(result, assignKey, {
      enumerable: true,

      get() {
        return getState({ module, key: stateKey });
      },
    });

    return result;
  }, target);
};

/**
 * Mimics the Vuex mapGetters helper to facilitate usage in service subclasses.
 *
 * @param {Object} target The object to bind the getters to
 * @param {String} module Name of module to map getters from
 * @param {Object|String[]} getters Array of getter names or object with mapping from store name to local name
 */
export const mapGetters = (target, module, getters) => {
  const checkGetters = g => Array.isArray(g) || isPlainObject(g);
  const checkModule = m => typeof m === 'string' || m instanceof String;

  const hasModule = checkModule(module) && checkGetters(getters);
  const hasOnlyGetters = !hasModule && checkGetters(getters);

  // If only "getters" has been passed as a parameter, "module" must be the getters object
  // - assign it to a local variable.
  const gettersLocal = hasOnlyGetters ? module : getters;

  if (Array.isArray(gettersLocal)) {
    processGettersArray(target, { getters: gettersLocal, module });
  }

  if (isPlainObject(gettersLocal)) {
    processGettersObject(target, { getters: gettersLocal, module });
  }
};

/**
 * Mimics the Vuex mapState helper to facilitate usage in service subclasses.
 *
 * @param {Object} target The object to bind the state to
 * @param {String} module Name of module to map getters from
 * @param {Object|String[]} state Array of state names or object with mapping from store name to local name
 */
export const mapState = (target, module, state) => {
  const checkState = s => Array.isArray(s) || isPlainObject(s);
  const checkModule = m => typeof m === 'string' || m instanceof String;

  const hasModule = checkModule(module) && checkState(state);
  const hasOnlyState = !hasModule && checkState(state);

  // If only "state" has been passed as a parameter, "module" must be the state object - assign it
  // to a local variable.
  const stateLocal = hasOnlyState ? module : state;

  if (Array.isArray(stateLocal)) {
    processStateArray(target, { module, state: stateLocal });
  }

  if (isPlainObject(stateLocal)) {
    processStateObject(target, { module, state: stateLocal });
  }

  return {};
};

const dispatchAction = ({ module, action, args }) => {
  const qualifiedAction = module == null ? action : `${module}/${action}`;
  return store.dispatch(qualifiedAction, ...args);
};

const processActionsArray = (target, { actions, module }) => {
  actions.forEach(action => {
    target[action] = function actionHandler(...args) {
      return dispatchAction({ module, action, args });
    };
  });
};

const processActionsObject = (target, { actions, module }) => {
  Object.entries(actions).forEach(([assignKey, actionKey]) => {
    target[assignKey] = function actionHandler(...args) {
      return dispatchAction({ module, action: actionKey, args });
    };
  });
};

export const mapActions = (target, module, actions) => {
  const checkActions = a => Array.isArray(a) || isPlainObject(a);
  const checkModule = m => typeof m === 'string' || m instanceof String;

  const hasModule = checkModule(module) && checkActions(actions);
  const hasOnlyActions = !hasModule && checkActions(actions);

  // If only "actions" has been passed as a parameter, "module" must be the actions object
  // - assign it to a local variable.
  const actionsLocal = hasOnlyActions ? module : actions;

  if (Array.isArray(actionsLocal)) {
    processActionsArray(target, { actions: actionsLocal, module });
  }

  if (isPlainObject(actionsLocal)) {
    processActionsObject(target, { actions: actionsLocal, module });
  }
};
