import { mapActions } from '@/services/BaseRequest';

import UsageApi from '@/services/api/UsageApi';

const usageRequest = {
  async downloadUsage(type, query) {
    return UsageApi.downloadUsage(type, query);
  },

  async queryUsage(type, query) {
    const usages = await UsageApi.queryUsage(type, query);
    this.setUsages(usages);
  },
};

mapActions(usageRequest, 'usage', ['setUsages']);

export default usageRequest;
