import { mapState, mapActions } from '@/services/BaseRequest';

import DatasetStatusApi from '@/services/api/DatasetStatusApi';

const datasetStatusRequest = {

  get parameters() {
    return {
      filterDatasetIds: this.filterDatasetIds,
      filterLabel: this.search,
      page: this.page,
      pageSize: this.rows,
      sorting: this.sorting,
    };
  },

  async fetchStatuses() {
    const statuses = await DatasetStatusApi.fetchDatasetStatuses(this.parameters);
    this.setDatasetStatuses(statuses);
  },
};

mapState(datasetStatusRequest, 'datasetStatus', [
  'filterDatasetIds',
  'filterLabel',
  'page',
  'rows',
  'sorting',
]);

mapActions(datasetStatusRequest, 'datasetStatus', ['setDatasetStatuses']);

export default datasetStatusRequest;
