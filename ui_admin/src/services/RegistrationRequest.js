import { mapState, mapActions } from '@/services/BaseRequest';

import registrationApi from '@/services/api/RegistrationApi';

const registrationRequest = {

  get registrationParameters() {
    return {
      filterEmail: this.search,
      filterStatus: this.filterStatus,
      page: this.page,
      pages: this.pages,
      pageSize: this.rows,
    };
  },

  async fetchRegistrations() {
    const actions = await registrationApi.getRegistrations(this.registrationParameters);
    this.setRegistrations({ registrations: actions });
  },

};

mapState(registrationRequest, 'registration', [
  'selectedRegistration',
  'pages',
  'page',
  'rows',
  'filterStatus',
]);
mapActions(registrationRequest, 'registration', [
  'setRegistrations',
  'setPages',
  'setPage',
  'setRows',
]);

export default registrationRequest;
