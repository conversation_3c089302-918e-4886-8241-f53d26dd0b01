import { createRouter, createWebHistory } from 'vue-router';

import Route from '@/enum/route';

const AppView = () => { return import('@/views/AppView'); };

const BatchView = () => { return import('@/views/BatchView'); };
const DatasetStatusView = () => { return import('@/views/DatasetStatusView'); };
const DatasetView = () => { return import('@/views/DatasetView'); };
const HeartbeatView = () => { return import('@/views/HeartbeatView'); };
const LoginView = () => { return import('@/views/LoginView'); };
const OrganisationView = () => { return import('@/views/OrganisationView'); };
const RegistrationView = () => { return import('@/views/RegistrationView'); };
const ReportingJobEditView = () => { return import('@/views/ReportingJobEditView'); };
const ReportingJobExecutionStatusView = () => { return import('@/views/ReportingJobExecutionStatusView'); };
const ReportingJobNewView = () => { return import('@/views/ReportingJobNewView'); };
const ReportingJobResultEditView = () => { return import('@/views/ReportingJobResultEditView'); };
const ReportingJobResultView = () => { return import('@/views/ReportingJobResultView'); };
const ReportingJobView = () => { return import('@/views/ReportingJobView'); };
const SavedActionHistoryView = () => { return import('@/views/SavedActionHistoryView'); };
const SavedActionListView = () => { return import('@/views/SavedActionListView'); };
const SavedActionView = () => { return import('@/views/SavedActionView'); };
const UserTierView = () => { return import('@/views/UserTierView'); };
const UserUsageQueryView = () => { return import('@/views/UserUsageQueryView'); };
const UserUsageView = () => { return import('@/views/UserUsageView'); };
const UserView = () => { return import('@/views/UserView'); };
const WorkspaceView = () => { return import('@/views/WorkspaceView'); };

const routes = [
  {
    path: '/',
    component: AppView,
    redirect: Route.USER.url(),
    children: [
      {
        path: Route.USER.url(),
        name: Route.USER,
        component: UserView,
        meta: {
          title: 'Adoreboard Admin - Users',
        },
      },
      {
        path: Route.USER_USAGE.url(),
        name: Route.USER_USAGE,
        component: UserUsageView,
        meta: {
          title: 'Adoreboard Admin - User Usage',
        },
      },
      {
        path: Route.USER_USAGE_QUERY.url(),
        name: Route.USER_USAGE_QUERY,
        component: UserUsageQueryView,
        meta: {
          title: 'Adoreboard Admin - User Usage Query',
        },
      },
      {
        path: Route.USER_TIER.url(),
        name: Route.USER_TIER,
        component: UserTierView,
        meta: {
          title: 'Adoreboard Admin - User Tiers',
        },
      },
      {
        path: Route.REGISTRATION.url(),
        name: Route.REGISTRATION,
        component: RegistrationView,
        meta: {
          title: 'Adoreboard Admin - Registration',
        },
      },
      {
        path: Route.DATASET.url(),
        name: Route.DATASET,
        component: DatasetView,
        meta: {
          title: 'Adoreboard Admin - Datasets',
        },
      },
      {
        path: Route.DATASET_STATUS.url(),
        name: Route.DATASET_STATUS,
        component: DatasetStatusView,
        meta: {
          title: 'Adoreboard Admin - Dataset Statuses',
        },
      },
      {
        path: Route.ORGANISATION.url(),
        name: Route.ORGANISATION,
        component: OrganisationView,
        meta: {
          title: 'Adoreboard Admin - Organisations',
        },
      },
      {
        path: Route.WORKSPACE.url(),
        name: Route.WORKSPACE,
        component: WorkspaceView,
        meta: {
          title: 'Adoreboard Admin - Workspaces',
        },
      },
      {
        path: Route.SAVED_ACTION.url(),
        name: Route.SAVED_ACTION,
        component: SavedActionView,
        meta: {
          title: 'Adoreboard Admin - Saved Actions',
        },
      },
      {
        path: Route.SAVED_ACTION_HISTORY.url(),
        name: Route.SAVED_ACTION_HISTORY,
        component: SavedActionHistoryView,
        meta: {
          title: 'Adoreboard Admin - Saved Actions History',
        },
      },
      {
        path: Route.SAVED_ACTION_LIST.url(),
        name: Route.SAVED_ACTION_LIST,
        component: SavedActionListView,
        meta: {
          title: 'Adoreboard Admin - Saved Action List',
        },
      },
      {
        path: Route.REPORTING_JOB.url(),
        name: Route.REPORTING_JOB,
        component: ReportingJobView,
        meta: {
          title: 'Adoreboard Admin - Reporting Jobs',
        },
      },
      {
        path: Route.REPORTING_JOB_NEW.url(),
        name: Route.REPORTING_JOB_NEW,
        component: ReportingJobNewView,
        meta: {
          title: 'Adoreboard Admin - Reporting Job New',
        },
      },
      {
        path: Route.REPORTING_JOB_EDIT.url(),
        name: Route.REPORTING_JOB_EDIT,
        component: ReportingJobEditView,
        meta: {
          title: 'Adoreboard Admin - Reporting Job Edit',
        },
      },
      {
        path: Route.REPORTING_JOB_EXECUTION_STATUS.url(),
        name: Route.REPORTING_JOB_EXECUTION_STATUS,
        component: ReportingJobExecutionStatusView,
        meta: {
          title: 'Adoreboard Admin - Reporting Job Execution Status',
        },
      },
      {
        path: Route.REPORTING_JOB_RESULT.url(),
        name: Route.REPORTING_JOB_RESULT,
        component: ReportingJobResultView,
        meta: {
          title: 'Adoreboard Admin - Reporting Job Result',
        },
      },
      {
        path: Route.REPORTING_JOB_RESULT_EDIT.url(),
        name: Route.REPORTING_JOB_RESULT_EDIT,
        component: ReportingJobResultEditView,
        meta: {
          title: 'Adoreboard Admin - Reporting Job Result Edit',
        },
      },
      {
        path: Route.BATCH.url(),
        name: Route.BATCH,
        component: BatchView,
        meta: {
          title: 'Adoreboard Admin - Batches',
        },
      },
      {
        path: Route.HEARTBEAT.url(),
        name: Route.HEARTBEAT,
        component: HeartbeatView,
        meta: {
          title: 'Adoreboard Admin - Heartbeats',
        },
      },
    ],
  }, {
    path: Route.LOGIN.url(),
    name: Route.LOGIN,
    component: LoginView,
    meta: {
      isLoginPage: true,
      title: 'Adoreboard Admin - Login',
    },
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

export default router;
