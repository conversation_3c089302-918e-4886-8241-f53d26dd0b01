import { createApp } from 'vue';

import App from './App';
import ClickOutsideDirective from '@/directives/ClickOutsideDirective';
import router from './router';
import Vuex from 'vuex';
import store from './store';

import '@vuepic/vue-datepicker/dist/main.css';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';
import 'vue-select/dist/vue-select.css';

const app = createApp(App);

app.use(router);
app.use(Vuex);
app.use(store);
app.directive('click-outside', ClickOutsideDirective);
app.mount('#app');
