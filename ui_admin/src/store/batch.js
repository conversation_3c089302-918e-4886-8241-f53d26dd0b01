const initialState = {
  batches: [],
  selectedBatch: null,
  search: null,
  filterStatus: null,
  page: 0,
  pages: null,
  rows: 50,
  sortDirection: 'DESC',
  sorting: 'ID',
};

// eslint-disable-next-line import/prefer-default-export
export const batch = {
  namespaced: true,

  state: { ...initialState },

  actions: {
    reset({ commit }) {
      commit('reset');
    },

    setBatches({ commit }, { batches }) {
      commit('setBatches', { batches });
    },

    setJobParams({ commit }, { params }) {
      commit('setJobParams', { params });
    },

    setSelectedBatch({ commit }, { selectedBatch }) {
      commit('setSelectedBatch', { selectedBatch });
    },

    setPages({ commit }, { pages }) {
      commit('setPages', { pages });
    },

    setPage({ commit }, { page }) {
      commit('setPage', { page });
    },

    setRows({ commit }, { rows }) {
      commit('setRows', { rows });
    },

    setSort({ commit }, { sorting }) {
      commit('setSort', { sorting });
    },

    setSearch({ commit }, search) {
      commit('setSearch', search);
    },

    setFilterStatus({ commit }, filterStatus) {
      commit('setFilterStatus', filterStatus);
    },

  },

  mutations: {
    reset(state) {
      Object.assign(state, initialState);
    },

    setBatches(state, { batches }) {
      state.batches = batches;
    },

    setSelectedBatch(state, { selectedBatch }) {
      state.selectedBatch = selectedBatch;
    },

    setPages(state, { pages }) {
      state.pages = pages;
    },

    setPage(state, { page }) {
      state.page = page;
    },

    setRows(state, { rows }) {
      state.rows = rows;
    },

    setSort(state, { sorting }) {
      state.sorting = sorting;
    },

    setSearch(state, search) {
      state.search = search;
    },

    setFilterStatus(state, filterStatus) {
      state.filterStatus = filterStatus;
    },
  },

  getters: {
  },
};
