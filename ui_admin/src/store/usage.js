const initialState = {
  usages: null,
};

// eslint-disable-next-line import/prefer-default-export
export const usage = {
  namespaced: true,

  state: { ...initialState },

  actions: {
    reset({ commit }) {
      commit('reset');
    },

    setUsages({ commit }, u) {
      commit('setUsages', u);
    },
  },

  mutations: {
    reset(state) {
      Object.assign(state, initialState);
    },

    setUsages(state, u) {
      state.usages = u;
    },
  },

  getters: {
  },
};
