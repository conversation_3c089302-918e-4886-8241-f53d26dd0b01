const initialState = {
  workspaces: [],
  selectedWorkspace: null,
  search: '',
  filterLabel: null,
  filterOwner: null,
  page: 0,
  pages: null,
  rows: 50,
  sorting: 'CREATED_AT',
  sortDirection: 'DESC',
};

// eslint-disable-next-line import/prefer-default-export
export const workspace = {
  namespaced: true,

  state: { ...initialState },

  actions: {
    reset({ commit }) {
      commit('reset');
    },

    resetFilter({ commit }) {
      commit('resetFilter');
    },

    setWorkspaces({ commit }, { workspaces }) {
      commit('setWorkspaces', { workspaces });
    },

    setSelectedWorkspace({ commit }, { selectedWorkspace }) {
      commit('setSelectedWorkspace', { selectedWorkspace });
    },

    setSearch({ commit }, search) {
      commit('setSearch', search);
    },

    setFilterOwner({ commit }, filterOwner) {
      commit('setFilterOwner', filterOwner);
    },

    setPages({ commit }, { pages }) {
      commit('setPages', { pages });
    },

    setPage({ commit }, { page }) {
      commit('setPage', { page });
    },

    setRows({ commit }, { rows }) {
      commit('setRows', { rows });
    },
  },

  mutations: {
    reset(state) {
      Object.assign(state, initialState);
    },

    resetFilter(state) {
      state.filterLabel = null;
      state.filterOwner = null;
      state.page = 0;
      state.pages = null;
      state.rows = 50;
      state.search = '';
    },

    setWorkspaces(state, { workspaces }) {
      state.workspaces = workspaces;
    },

    setSelectedWorkspace(state, { selectedWorkspace }) {
      state.selectedWorkspace = selectedWorkspace;
    },

    setSearch(state, search) {
      state.search = search;
    },

    setFilterOwner(state, filterOwner) {
      state.filterOwner = filterOwner;
    },

    setPages(state, { pages }) {
      state.pages = pages;
    },

    setPage(state, { page }) {
      state.page = page;
    },

    setRows(state, { rows }) {
      state.rows = rows;
    },
  },

  getters: {
  },
};
