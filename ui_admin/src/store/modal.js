const initialState = {
  component: [],
  componentProps: {},
};

// eslint-disable-next-line import/prefer-default-export
export const modal = {
  namespaced: true,

  state: initialState,

  actions: {
    closeAllModal({ commit }) {
      commit('closeAllModal');
    },

    closeModal({ commit }) {
      commit('closeModal');
    },

    closeModalByName({ commit }, { name }) {
      commit('closeModalByName', { name });
    },

    setModal({ commit }, component) {
      commit('setModal', component);
    },

    setModalAndProps({ commit }, modalAndProps) {
      commit('setModal', modalAndProps.component);
      commit('setModalProps', modalAndProps.props);
    },
  },

  mutations: {
    closeAllModal(state) {
      state.component = [];
    },

    closeModal(state) {
      state.component.pop();
    },

    closeModalByName(state, { name }) {
      state.component = state.component.filter(c => c.name !== name);
    },

    setModal(state, component) {
      state.component.push(component);
    },

    setModalProps(state, props) {
      state.componentProps = props;
    },
  },

  getters: {
    getComponent: (state) => {
      return Object.assign([], state.component);
    },

    getComponentProps: (state) => {
      return state.componentProps;
    },
  },
};
