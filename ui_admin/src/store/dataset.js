const initialState = {
  datasets: [],
  filterArchived: null,
  filterFromDate: null,
  filterHavingError: null,
  filterId: null,
  filterIds: null,
  filterLabel: null,
  filterStatus: null,
  filterToDate: null,
  filterUserIds: [],
  page: 0,
  rows: 50,
  search: '',
  selectedDataset: null,
  selectedDatasets: [],
  sortDirection: 'DESC',
  sorting: 'ID',
};

// eslint-disable-next-line import/prefer-default-export
export const dataset = {
  namespaced: true,

  state: { ...initialState },

  actions: {
    reset({ commit }) {
      commit('reset');
    },

    setDatasets({ commit }, { datasets }) {
      commit('setDatasets', { datasets });
    },

    setSelectedDatasets({ commit }, { selectedDatasets }) {
      commit('setSelectedDatasets', { selectedDatasets });
    },

    setSelectedDataset({ commit }, { selectedDataset }) {
      commit('setSelectedDataset', { selectedDataset });
    },

    setPage({ commit }, { page }) {
      commit('setPage', { page });
    },

    setRows({ commit }, { rows }) {
      commit('setRows', { rows });
    },

    setSort({ commit }, { sorting }) {
      commit('setSort', { sorting });
    },

    setFilterUserIds({ commit }, filterUserIds) {
      commit('setFilterUserIds', filterUserIds);
    },

    setFilterArchived({ commit }, filterArchived) {
      commit('setFilterArchived', filterArchived);
    },

    setFilterId({ commit }, filterId) {
      commit('setFilterId', filterId);
    },

    setFilterIds({ commit }, filterIds) {
      commit('setFilterIds', filterIds);
    },

    setFilterHavingError({ commit }, filterHavingError) {
      commit('setFilterHavingError', filterHavingError);
    },

    setFilterStatus({ commit }, filterStatus) {
      commit('setFilterStatus', filterStatus);
    },

    setFilterFromDate({ commit }, filterFromDate) {
      commit('setFilterFromDate', filterFromDate);
    },

    setFilterToDate({ commit }, filterToDate) {
      commit('setFilterToDate', filterToDate);
    },

    setFilterLabel({ commit }, filterLabel) {
      commit('setFilterLabel', filterLabel);
    },
  },

  mutations: {
    reset(state) {
      Object.assign(state, initialState);
    },

    setDatasets(state, { datasets }) {
      state.datasets = datasets;
    },

    setSelectedDatasets(state, { selectedDatasets }) {
      state.selectedDatasets = selectedDatasets;
    },

    setSelectedDataset(state, { selectedDataset }) {
      state.selectedDataset = selectedDataset;
    },

    setPage(state, { page }) {
      state.page = page;
    },

    setRows(state, { rows }) {
      state.rows = rows;
    },

    setSort(state, { sorting }) {
      state.sorting = sorting;
    },

    setFilterUserIds(state, { filterUserIds }) {
      state.filterUserIds = filterUserIds;
    },

    setSearch(state, search) {
      state.search = search;
    },

    setFilterArchived(state, filterArchived) {
      state.filterArchived = filterArchived;
    },

    setFilterId(state, filterId) {
      state.filterId = filterId;
    },

    setFilterIds(state, filterIds) {
      state.filterIds = filterIds;
    },

    setFilterHavingError(state, filterHavingError) {
      state.filterHavingError = filterHavingError;
    },

    setFilterStatus(state, filterStatus) {
      state.filterStatus = filterStatus;
    },

    setFilterFromDate(state, filterFromDate) {
      state.fromDate = filterFromDate;
    },

    setFilterToDate(state, filterToDate) {
      state.toDate = filterToDate;
    },

    setFilterLabel(state, filterLabel) {
      state.filterLabel = filterLabel;
    },
  },

  getters: {
  },
};
