const initialState = {
  page: 0,
  pages: null,
  rows: 50,
  savedActionLists: [],
  search: '',
  selectedSavedActionList: null,
  sorting: 'CREATED_AT',
  sortDirection: 'DESC',
  filterIds: null,
  filterActionIds: null,
  filterName: null,
  filterCollectionLabel: null,
  filterWorkspaceLabel: null,
};

// eslint-disable-next-line import/prefer-default-export
export const savedActionList = {
  namespaced: true,

  state: { ...initialState },

  actions: {
    reset({ commit }) {
      commit('reset');
    },

    setSavedActionLists({ commit }, { lists }) {
      commit('setSavedActionLists', { lists });
    },

    setFilterIds({ commit }, filterIds) {
      commit('setFilterIds', filterIds);
    },

    setFilterActionIds({ commit }, filterActionIds) {
      commit('setFilterActionIds', filterActionIds);
    },

    setFilterName({ commit }, filterName) {
      commit('setFilterName', filterName);
    },

    setFilterCollectionLabel({ commit }, filterCollectionLabel) {
      commit('setFilterCollectionLabel', filterCollectionLabel);
    },

    setFilterWorkspaceLabel({ commit }, filterWorkspaceLabel) {
      commit('setFilterWorkspaceLabel', filterWorkspaceLabel);
    },

    setSelectedSavedActionList({ commit }, lst) {
      commit('setSelectedSavedActionList', lst);
    },

    setPages({ commit }, { pages }) {
      commit('setPages', { pages });
    },

    setPage({ commit }, { page }) {
      commit('setPage', { page });
    },

    setRows({ commit }, { rows }) {
      commit('setRows', { rows });
    },
  },

  mutations: {
    reset(state) {
      Object.assign(state, initialState);
    },

    setSavedActionLists(state, { lists }) {
      state.savedActionLists = lists;
    },

    setSelectedSavedActionList(state, lst) {
      state.selectedSavedActionList = lst;
    },

    setPages(state, { pages }) {
      state.pages = pages;
    },

    setPage(state, { page }) {
      state.page = page;
    },

    setRows(state, { rows }) {
      state.rows = rows;
    },

    setFilterIds(state, filterIds) {
      state.filterIds = filterIds;
    },

    setFilterActionIds(state, filterActionIds) {
      state.filterActionIds = filterActionIds;
    },

    setFilterName(state, filterName) {
      state.filterName = filterName;
    },

    setFilterCollectionLabel(state, filterCollectionLabel) {
      state.filterCollectionLabel = filterCollectionLabel;
    },

    setFilterWorkspaceLabel(state, filterWorkspaceLabel) {
      state.filterWorkspaceLabel = filterWorkspaceLabel;
    },

  },

  getters: {
  },
};
