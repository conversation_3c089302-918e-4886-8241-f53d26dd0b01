import { batch } from '@/store/batch';
import { createStore } from 'vuex';
import { dataset } from './dataset';
import { datasetStatus } from './dataset-status';
import { modal } from './modal';
import { network } from './network';
import { heartbeat } from '@/store/heartbeat';
import { organisation } from './organisation';
import { registration } from './registration';
import { savedAction } from './saved-action';
import { savedActionHistory } from '@/store/saved-action-history';
import { savedActionList } from './saved-action-list';
import { toast } from './toast';
import { reportingJob } from './reporting-job';
import { usage } from './usage';
import { user } from './user';
import { userTier } from './user-tier';
import { workspace } from './workspace';

const store = createStore({
  modules: {
    batch,
    dataset,
    datasetStatus,
    heartbeat,
    modal,
    network,
    organisation,
    registration,
    reportingJob,
    savedAction,
    savedActionHistory,
    savedActionList,
    toast,
    usage,
    user,
    userTier,
    workspace,
  },
});

export default store;
