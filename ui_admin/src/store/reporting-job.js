export default class State {
  constructor() {
    Object.assign(this, {
      reportingJobs: [],
      filterId: null,
      filterUserIds: [],
      page: 0,
      reportingJobResults: [],
      rows: 20,
      selectedId: null,
      selectedReportingJob: null, // TODO: should change this to activeReporting<PERSON>ob for clear understanding
      selectedResult: null,
      selectedResultId: null,
      sortDirection: 'DESC',
      sorting: 'ID',
    });
  }
}

export const reportingJob = {
  namespaced: true,

  state: new State(),

  actions: {
    reset({ commit }) {
      commit('reset');
    },

    setReportingJobs({ commit }, jobs) {
      commit('setReportingJobs', jobs);
    },

    setReportingJobResults({ commit }, results) {
      commit('setReportingJobResults', results);
    },

    setSelectedId({ commit }, id) {
      commit('setSelectedId', id);
    },

    setSelectedReportingJob({ commit }, job) {
      commit('setSelectedReportingJob', job);
      commit('setSelectedId', job.id);
    },

    setSelectedResult({ commit }, result) {
      commit('setSelectedResult', result);
      commit('setSelectedResultId', result.id);
    },

    setSelectedResultId({ commit }, id) {
      commit('setSelectedResultId', id);
    },

    setPage({ commit }, page) {
      commit('setPage', page);
    },

    setRows({ commit }, rows) {
      commit('setRows', rows);
    },

    setSort({ commit }, sorting) {
      commit('setSort', sorting);
    },

    setFilterUserIds({ commit }, filterUserIds) {
      commit('setFilterUserIds', filterUserIds);
    },

    setFilterId({ commit }, filterId) {
      commit('setFilterId', filterId);
    },
  },

  mutations: {
    reset(state) {
      Object.assign(state, new State());
    },

    setReportingJobs(state, jobs) {
      state.reportingJobs = jobs;
    },

    setReportingJobResults(state, results) {
      state.reportingJobResults = results;
    },

    setSelectedId(state, id) {
      state.selectedId = id;
    },

    setSelectedReportingJob(state, job) {
      state.selectedReportingJob = job;
    },

    setSelectedResult(state, result) {
      state.selectedResult = result;
    },

    setSelectedResultId(state, id) {
      state.selectedResultId = id;
    },

    setPage(state, page) {
      state.page = page;
    },

    setRows(state, rows) {
      state.rows = rows;
    },

    setSort(state, sorting) {
      state.sorting = sorting;
    },

    setFilterUserIds(state, filterUserIds) {
      state.filterUserIds = filterUserIds;
    },

    setFilterId(state, filterId) {
      state.filterId = filterId;
    },
  },

  getters: {
  },
};
