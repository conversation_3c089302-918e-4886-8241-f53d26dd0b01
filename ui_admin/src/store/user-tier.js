const initialState = {
  userTiers: [],
  selectedUserTier: null,
  features: [],
  page: 0,
  pages: null,
  rows: 50,
  search: '',
  sortDirection: 'DESC',
  sorting: 'CREATED_AT',
};

// eslint-disable-next-line import/prefer-default-export
export const userTier = {
  namespaced: true,

  state: { ...initialState },

  actions: {
    reset({ commit }) {
      commit('reset');
    },

    setUserTiers({ commit }, { tiers }) {
      commit('setUserTiers', { tiers });
    },

    setSelectedUserTier({ commit }, { tier }) {
      commit('setSelectedUserTier', { tier });
    },

    setProductFeatures({ commit }, { features }) {
      commit('setProductFeatures', { features });
    },

    setPages({ commit }, { pages }) {
      commit('setPages', { pages });
    },

    setPage({ commit }, { page }) {
      commit('setPage', { page });
    },

    setRows({ commit }, { rows }) {
      commit('setRows', { rows });
    },

    setSort({ commit }, { sorting }) {
      commit('setSort', { sorting });
    },
  },

  mutations: {
    reset(state) {
      Object.assign(state, initialState);
    },

    setUserTiers(state, { tiers }) {
      state.userTiers = tiers;
    },

    setSelectedUserTier(state, { tier }) {
      state.selectedUserTier = tier;
    },

    setProductFeatures(state, { features }) {
      state.features = [...features];
    },

    setPages(state, { pages }) {
      state.pages = pages;
    },

    setPage(state, { page }) {
      state.page = page;
    },

    setRows(state, { rows }) {
      state.rows = rows;
    },

    setSort(state, { sorting }) {
      state.sorting = sorting;
    },
  },

  getters: {
  },
};
