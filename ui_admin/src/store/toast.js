import { uniqBy } from 'lodash-es';

const initialState = {
  queue: [],
  toastData: null,
};

// eslint-disable-next-line import/prefer-default-export
export const toast = {
  namespaced: true,

  state: { ...initialState },

  actions: {
    reset({ commit }) {
      commit('reset');
    },

    addToast({ commit }, { toastComponent, toastData }) {
      commit('addToast', { toastComponent, toastData });
    },

    removeToastData({ commit }) {
      commit('removeToastData');
    },

    removeToast({ commit }, id) {
      commit('removeToast', id);
    },
  },

  mutations: {
    reset(state) {
      Object.assign(state, initialState);
    },

    addToast(state, { toastComponent, toastData }) {
      state.queue = uniqBy([...state.queue, toastComponent], toastComponent.id);
      toastData.id = toastComponent.id;
      state.toastData = toastData;
    },

    removeToast(state, { id }) {
      state.queue = state.queue.filter(t => t.id !== id);
      state.toastData = {};
    },

    removeToastData(state) {
      state.toastData = {};
    },
  },

  getters: {
  },
};
