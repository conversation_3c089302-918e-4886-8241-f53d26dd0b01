const initialState = {
  organisations: [],
  selectedOrganisation: null,
  filterLabel: null,
  filterName: null,
  filterOwner: null,
  page: 0,
  pages: null,
  rows: 50,
  sortDirection: 'DESC',
  sorting: 'ID',
};

// eslint-disable-next-line import/prefer-default-export
export const organisation = {
  namespaced: true,

  state: { ...initialState },

  actions: {
    reset({ commit }) {
      commit('reset');
    },

    resetFilter({ commit }) {
      commit('resetFilter');
    },

    setOrganisations({ commit }, { organisations }) {
      commit('setOrganisations', { organisations });
    },

    setSelectedOrganisation({ commit }, org) {
      commit('setSelectedOrganisation', org);
    },

    setFilterLabel({ commit }, filterLabel) {
      commit('setFilterLabel', filterLabel);
    },

    setFilterName({ commit }, filterName) {
      commit('setFilterName', filterName);
    },

    setFilterOwner({ commit }, filterOwner) {
      commit('setFilterOwner', filterOwner);
    },

    setPages({ commit }, { pages }) {
      commit('setPages', { pages });
    },

    setPage({ commit }, { page }) {
      commit('setPage', { page });
    },

    setRows({ commit }, { rows }) {
      commit('setRows', { rows });
    },

    setSort({ commit }, { sorting }) {
      commit('setSort', { sorting });
    },

  },

  mutations: {
    reset(state) {
      Object.assign(state, initialState);
    },

    resetFilter(state) {
      state.filterLabel = null;
      state.filterOwner = null;
      state.filterName = null;
      state.page = 0;
      state.pages = null;
      state.rows = 50;
      state.search = '';
    },

    setOrganisations(state, { organisations }) {
      state.organisations = organisations;
    },

    setSelectedOrganisation(state, org) {
      state.selectedOrganisation = org;
    },

    setFilterLabel(state, filterLabel) {
      state.filterLabel = filterLabel;
    },

    setFilterName(state, filterName) {
      state.filterName = filterName;
    },

    setFilterOwner(state, filterOwner) {
      state.filterOwner = filterOwner;
    },

    setPages(state, { pages }) {
      state.pages = pages;
    },

    setPage(state, { page }) {
      state.page = page;
    },

    setRows(state, { rows }) {
      state.rows = rows;
    },

    setSort(state, { sorting }) {
      state.sorting = sorting;
    },
  },

  getters: {
  },
};
