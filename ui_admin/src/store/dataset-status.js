const initialState = {
  datasetStatuses: [],
  filterDatasetIds: null,
  filterLabel: null,
  page: 0,
  rows: 50,
  sortDirection: 'DESC',
  sorting: 'ID',
};

// eslint-disable-next-line import/prefer-default-export
export const datasetStatus = {
  namespaced: true,

  state: { ...initialState },

  actions: {
    reset({ commit }) {
      commit('reset');
    },

    setDatasetStatuses({ commit }, statuses) {
      commit('setDatasetStatuses', statuses);
    },

    setPage({ commit }, page) {
      commit('setPage', page);
    },

    setRows({ commit }, rows) {
      commit('setRows', rows);
    },

    setSort({ commit }, sorting) {
      commit('setSort', sorting);
    },

    setFilterDatasetIds({ commit }, filterIds) {
      commit('setFilterDatasetIds', filterIds);
    },

    setFilterLabel({ commit }, filterLabel) {
      commit('setFilterLabel', filterLabel);
    },
  },

  mutations: {
    reset(state) {
      Object.assign(state, initialState);
    },

    setDatasetStatuses(state, statuses) {
      state.datasetStatuses = statuses;
    },

    setPage(state, page) {
      state.page = page;
    },

    setRows(state, rows) {
      state.rows = rows;
    },

    setSort(state, sorting) {
      state.sorting = sorting;
    },

    setFilterDatasetIds(state, filterIds) {
      state.filterDatasetIds = filterIds;
    },

    setFilterLabel(state, filterLabel) {
      state.filterLabel = filterLabel;
    },
  },

  getters: {
  },
};
