const initialState = {
  filterDateFrom: null,
  filterDateTo: null,
  filterDeletedBy: null,
  filterIds: null,
  filterOutsideRange: null,
  filterType: null,
  filterUserId: null,
  filterWorkspaceId: null,
  page: 0,
  pages: null,
  rows: 50,
  savedActionsHistory: [],
  search: '',
  selectedSavedActionHistory: null,
  selectedSavedActionsHistory: [],
  sortDirection: 'DESC',
  sorting: 'ID',
};

// eslint-disable-next-line import/prefer-default-export
export const savedActionHistory = {
  namespaced: true,

  state: { ...initialState },

  actions: {
    reset({ commit }) {
      commit('reset');
    },

    setSavedActionsHistory({ commit }, { savedActionsHistory }) {
      commit('setSavedActionsHistory', { savedActionsHistory });
    },

    setFilterName({ commit }, filterName) {
      commit('setFilterName', filterName);
    },

    setFilterType({ commit }, filterType) {
      commit('setFilterType', filterType);
    },

    setFilterWorkspaceLabel({ commit }, filterWorkspaceLabel) {
      commit('setFilterWorkspaceLabel', filterWorkspaceLabel);
    },

    setFilterThemeLabel({ commit }, filterThemeLabel) {
      commit('setFilterThemeLabel', filterThemeLabel);
    },

    setSelectedSavedActionHistory({ commit }, action) {
      commit('setSelectedSavedActionHistory', action);
    },

    setSelectedSavedActionsHistory({ commit }, actions) {
      commit('setSelectedSavedActionsHistory', actions);
    },

    setPages({ commit }, { pages }) {
      commit('setPages', { pages });
    },

    setPage({ commit }, { page }) {
      commit('setPage', { page });
    },

    setRows({ commit }, { rows }) {
      commit('setRows', { rows });
    },

    setFilterDateFrom({ commit }, filterDateFrom ) {
      commit('setFilterDateFrom', filterDateFrom)
    },

    setFilterDateTo({ commit }, filterDateTo ) {
      commit('setFilterDateTo',filterDateTo)
    },

    setFilterDeletedBy({ commit }, filterDeletedBy) {
      commit('setFilterDeletedBy', filterDeletedBy)
    },

    setFilterIds({ commit }, filterIds) {
      commit('setFilterIds', filterIds)
    },

    setFilterOutsideRange({ commit }, filterOutsideRange) {
      commit('setFilterOutsideRange', filterOutsideRange)
    },

    setFilterUserId({ commit }, filterUserId ) {
      commit('setFilterUserId', filterUserId)
    },

    setFilterWorkspaceId({ commit }, filterWorkspaceId) {
      commit('setFilterWorkspaceId', filterWorkspaceId)
    },

  },

  mutations: {
    reset(state) {
      Object.assign(state, initialState);
    },

    setSavedActionsHistory(state, { savedActionsHistory }) {
      state.savedActionsHistory = savedActionsHistory;
    },

    setSelectedSavedActionHistory(state, action) {
      state.selectedSavedActionHistory = action;
    },

    setSelectedSavedActionsHistory(state, actions) {
      state.selectedSavedActionsHistory = actions;
    },

    setPages(state, { pages }) {
      state.pages = pages;
    },

    setPage(state, { page }) {
      state.page = page;
    },

    setRows(state, { rows }) {
      state.rows = rows;
    },

    setFilterName(state, filterName) {
      state.filterName = filterName;
    },

    setFilterType(state, filterType) {
      state.filterType = filterType;
    },

    setFilterWorkspaceLabel(state, filterWorkspaceLabel) {
      state.filterWorkspaceLabel = filterWorkspaceLabel;
    },

    setFilterThemeLabel(state, filterThemeLabel) {
      state.filterThemeLabel = filterThemeLabel;
    },

    setFilterDateFrom(state, filterDateFrom ) {
      state.filterDateFrom = filterDateFrom;
    },

    setFilterDateTo(state, filterDateTo ) {
      state.filterDateTo = filterDateTo;
    },

    setFilterDeletedBy(state, filterDeletedBy) {
      state.filterDeletedBy = filterDeletedBy;
    },

    setFilterIds(state, filterIds) {
      state.filterIds = filterIds;
    },

    setFilterOutsideRange(state, filterOutsideRange) {
      state.filterOutsideRange = filterOutsideRange;
    },

    setFilterUserId(state, filterUserId ) {
      state.filterUserId = filterUserId;
    },

    setFilterWorkspaceId(state, filterWorkspaceId) {
      state.filterWorkspaceId = filterWorkspaceId;
    },

  },

  getters: {
  },
};
