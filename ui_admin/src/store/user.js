const initialState = {
  filterActive: null,
  filterTierId: null,
  filterIds: null,
  page: 0,
  pages: null,
  rows: 50,
  search: '',
  selectedUser: null,
  selectedUsers: [],
  sort: 'CREATED_AT',
  sortDirection: 'DESC',
  user: null,
  users: [],
};

// eslint-disable-next-line import/prefer-default-export
export const user = {
  namespaced: true,

  state: { ...initialState },

  actions: {
    reset({ commit }) {
      commit('reset');
    },

    resetFilter({ commit }) {
      commit('resetFilter');
    },

    setUser({ commit }, u) {
      commit('setUser', u);
    },

    setUsers({ commit }, { u }) {
      commit('setUsers', { u });
    },

    setSearch({ commit }, search) {
      commit('setSearch', search);
    },

    setFilterIds({ commit }, filterIds) {
      commit('setFilterIds', filterIds);
    },

    setFilterTierId({ commit }, tierId) {
      commit('setFilterTierId', tierId);
    },

    setSelectedUser({ commit }, u) {
      commit('setSelectedUser', u);
    },

    setSelectedUsers({ commit }, selectedUsers) {
      commit('setSelectedUsers', selectedUsers);
    },

    setPages({ commit }, { pages }) {
      commit('setPages', { pages });
    },

    setPage({ commit }, { page }) {
      commit('setPage', { page });
    },

    setRows({ commit }, { rows }) {
      commit('setRows', { rows });
    },
  },

  mutations: {
    reset(state) {
      Object.assign(state, initialState);
    },

    resetFilter(state) {
      state.filterActive = null;
      state.filterTierId = null;
      state.filterIds = null;
      state.page = 0;
      state.pages = null;
      state.rows = 50;
      state.search = '';
    },

    setUser(state, u) {
      state.user = u;
    },

    setUsers(state, { u }) {
      state.users = u;
    },

    setSearch(state, search) {
      state.search = search;
    },

    setFilterIds(state, filterIds) {
      state.filterIds = filterIds;
    },

    setFilterTierId(state, tierId) {
      state.filterTierId = tierId;
    },

    setSelectedUser(state, u) {
      state.selectedUser = u;
    },

    setSelectedUsers(state, selectedUsers) {
      state.selectedUsers = selectedUsers;
    },

    setPages(state, { pages }) {
      state.pages = pages;
    },

    setPage(state, { page }) {
      state.page = page;
    },

    setRows(state, { rows }) {
      state.rows = rows;
    },
  },

  getters: {
    getSelectedUserIds: (state) => {
      return state.selectedUsers.map(u => { return u.id; });
    },
  },
};
