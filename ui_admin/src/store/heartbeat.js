const initialState = {
  heartbeats: [],
  selectedHeartbeat: null,
  selectedHeartbeats: [],
  search: '',
  sorting: 'ID',
  sortDirection: 'DESC',
  page: 0,
  pages: null,
  rows: 50,
  filterIds: null,
  filterStatus: null,
  filterUuid: null,
  filterWorkspaceLabel: null,
  filterOwner: null,
};

// eslint-disable-next-line import/prefer-default-export
export const heartbeat = {
  namespaced: true,

  state: { ...initialState },

  actions: {
    reset({ commit }) {
      commit('reset');
    },

    setHeartbeats({ commit }, { heartbeats }) {
      commit('setHeartbeats', { heartbeats });
    },

    setFilterLabel({ commit }, filterLabel) {
      commit('setFilterLabel', filterLabel);
    },

    setFilterIds({ commit }, filterIds) {
      commit('setFilterIds', filterIds);
    },

    setFilterStatus({ commit }, filterStatus) {
      commit('setFilterStatus', filterStatus);
    },

    setFilterOwner({ commit }, filterOwner) {
      commit('setFilterOwner', filterOwner);
    },

    setFilterWorkspaceLabel({ commit }, filterWorkspaceLabel) {
      commit('setFilterWorkspaceLabel', filterWorkspaceLabel);
    },

    setFilterSurveyUuid({ commit }, filterUuid) {
      commit('setFilterSurveyUuid', filterUuid);
    },

    setSelectedHeartbeat({ commit }, item) {
      commit('setSelectedHeartbeat', item);
    },

    setSelectedHeartbeats({ commit }, items) {
      commit('setSelectedHeartbeats', items);
    },

    setPages({ commit }, { pages }) {
      commit('setPages', { pages });
    },

    setPage({ commit }, { page }) {
      commit('setPage', { page });
    },

    setRows({ commit }, { rows }) {
      commit('setRows', { rows });
    },
  },

  mutations: {
    reset(state) {
      Object.assign(state, initialState);
    },

    setHeartbeats(state, { heartbeats }) {
      state.heartbeats = heartbeats;
    },

    setSelectedHeartbeat(state, item) {
      state.selectedHeartbeat = item;
    },

    setSelectedHeartbeats(state, items) {
      state.selectedHeartbeats = items;
    },

    setPages(state, { pages }) {
      state.pages = pages;
    },

    setPage(state, { page }) {
      state.page = page;
    },

    setRows(state, { rows }) {
      state.rows = rows;
    },

    setFilterIds(state, filterIds) {
      state.filterIds = filterIds;
    },

    setFilterLabel(state, filterLabel) {
      state.search = filterLabel;
    },

    setFilterStatus(state, filterStatus) {
      state.filterStatus = filterStatus;
    },

    setFilterOwner(state, filterOwner) {
      state.filterOwner = filterOwner;
    },

    setFilterWorkspaceLabel(state, filterWorkspaceLabel) {
      state.filterWorkspaceLabel = filterWorkspaceLabel;
    },

    setFilterSurveyUuid(state, filterUuid) {
      state.filterUuid = filterUuid;
    },
  },

  getters: {
  },
};
