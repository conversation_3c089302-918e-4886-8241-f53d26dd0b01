const initialState = {
  savedActions: [],
  selectedSavedAction: null,
  selectedSavedActions: [],
  search: '',
  sorting: 'ID',
  sortDirection: 'DESC',
  page: 0,
  pages: null,
  rows: 50,
  filterIds: null,
  filterName: null,
  filterType: null,
  filterThemeLabel: null,
  filterWorkspaceLabel: null,
};

// eslint-disable-next-line import/prefer-default-export
export const savedAction = {
  namespaced: true,

  state: { ...initialState },

  actions: {
    reset({ commit }) {
      commit('reset');
    },

    setSavedActions({ commit }, { savedActions }) {
      commit('setSavedActions', { savedActions });
    },

    setFilterIds({ commit }, filterIds) {
      commit('setFilterIds', filterIds);
    },

    setFilterName({ commit }, filterName) {
      commit('setFilterName', filterName);
    },

    setFilterType({ commit }, filterType) {
      commit('setFilterType', filterType);
    },

    setFilterWorkspaceLabel({ commit }, filterWorkspaceLabel) {
      commit('setFilterWorkspaceLabel', filterWorkspaceLabel);
    },

    setFilterThemeLabel({ commit }, filterThemeLabel) {
      commit('setFilterThemeLabel', filterThemeLabel);
    },

    setSelectedSavedAction({ commit }, action) {
      commit('setSelectedSavedAction', action);
    },

    setSelectedSavedActions({ commit }, actions) {
      commit('setSelectedSavedActions', actions);
    },

    setPages({ commit }, { pages }) {
      commit('setPages', { pages });
    },

    setPage({ commit }, { page }) {
      commit('setPage', { page });
    },

    setRows({ commit }, { rows }) {
      commit('setRows', { rows });
    },
  },

  mutations: {
    reset(state) {
      Object.assign(state, initialState);
    },

    setSavedActions(state, { savedActions }) {
      state.savedActions = savedActions;
    },

    setSelectedSavedAction(state, action) {
      state.selectedSavedAction = action;
    },

    setSelectedSavedActions(state, actions) {
      state.selectedSavedActions = actions;
    },

    setPages(state, { pages }) {
      state.pages = pages;
    },

    setPage(state, { page }) {
      state.page = page;
    },

    setRows(state, { rows }) {
      state.rows = rows;
    },

    setFilterName(state, filterName) {
      state.filterName = filterName;
    },

    setFilterType(state, filterType) {
      state.filterType = filterType;
    },

    setFilterWorkspaceLabel(state, filterWorkspaceLabel) {
      state.filterWorkspaceLabel = filterWorkspaceLabel;
    },

    setFilterThemeLabel(state, filterThemeLabel) {
      state.filterThemeLabel = filterThemeLabel;
    },

    setFilterIds(state, filterIds) {
      state.filterIds = filterIds;
    },
  },

  getters: {
  },
};
