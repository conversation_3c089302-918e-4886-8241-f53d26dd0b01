const initialState = {
  registrations: [],
  selectedRegistration: null,
  search: '',
  filterStatus: '',
  sort: 'CREATED_AT',
  sortDirection: 'DESC',
  page: 0,
  pages: null,
  rows: 50,
};

// eslint-disable-next-line import/prefer-default-export
export const registration = {
  namespaced: true,

  state: { ...initialState },

  actions: {
    reset({ commit }) {
      commit('reset');
    },

    setRegistrations({ commit }, { registrations }) {
      commit('setRegistrations', { registrations });
    },

    setFilterStatus({ commit }, status) {
      commit('setFilterStatus', status);
    },

    setSearch({ commit }, search) {
      commit('setSearch', search);
    },

    setPages({ commit }, { pages }) {
      commit('setPages', { pages });
    },

    setPage({ commit }, { page }) {
      commit('setPage', { page });
    },

    setRows({ commit }, { rows }) {
      commit('setRows', { rows });
    },
  },

  mutations: {
    reset(state) {
      Object.assign(state, initialState);
    },

    setRegistrations(state, { registrations }) {
      state.registrations = registrations;
    },

    setFilterStatus(state, status) {
      state.filterStatus = status;
    },

    setSearch(state, search) {
      state.search = search;
    },

    setPages(state, { pages }) {
      state.pages = pages;
    },

    setPage(state, { page }) {
      state.page = page;
    },

    setRows(state, { rows }) {
      state.rows = rows;
    },
  },

  getters: {
  },
};
