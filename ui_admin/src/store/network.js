import NetworkStatus from '@/enum/network-status';

const initialState = {
  authorized: false,
  error: {},
  status: {},
};

// eslint-disable-next-line import/prefer-default-export
export const network = {
  namespaced: true,

  state: initialState,

  actions: {
    setAuthorized({ commit }, authorized) {
      commit('setAuthorized', authorized);
    },

    setError({ commit }, key, message) {
      commit('setError', message);
    },

    setStatus({ commit }, { key, status }) {
      commit('setStatus', { key, status });
    },
  },

  mutations: {
    setAuthorized(state, authorized) {
      state.authorized = authorized;
    },

    setError(state, key, message) {
      state.error = {
        ...state.error,
        [key]: message,
      };
    },

    setStatus(state, { key, status }) {
      state.status = {
        ...state.status,
        [key]: status,
      };
    },
  },

  getters: {
    isError: state => key => {
      return state.status[key]?.name === NetworkStatus.ERROR.name;
    },

    isLoading: state => key => {
      return state.status[key]?.name === NetworkStatus.LOADING.name;
    },

    status: state => key => {
      const status = state.status[key];
      return status == null ? NetworkStatus.IDLE : status;
    },
  },
};
