{"name": "ui_admin", "version": "1.0.0", "private": true, "scripts": {"start": "webpack serve --config build/webpack.dev.conf.js --port 3002 --hot", "build": "node build/build.js", "unit": "cross-env BABEL_ENV=test karma start test/unit/karma.conf.js --single-run", "unit-watch": "cross-env BABEL_ENV=test karma start test/unit/karma.conf.js", "e2e": "node test/e2e/runner.js", "test": "npm run unit && npm run e2e", "lint": "eslint --fix --ext .js,.vue src", "docs": "rm -rf ./docs && node_modules/.bin/jsdoc --configure .jsdoc.json --verbose"}, "dependencies": {"@fortawesome/fontawesome-pro": "^6.2.1", "@fortawesome/fontawesome-svg-core": "^6.1.1", "@fortawesome/pro-light-svg-icons": "^6.1.1", "@fortawesome/vue-fontawesome": "^3.0.0", "@vuepic/vue-datepicker": "^11.0.2", "ag-grid-community": "^30.2.1", "ag-grid-vue3": "^30.2.1", "axios": "^0.21.1", "connect-history-api-fallback": "^2.0.0", "core-js": "^3.8.3", "css-minimizer-webpack-plugin": "^4.2.2", "enumify": "^1.0.4", "eslint-webpack-plugin": "^3.2.0", "express": "^4.18.2", "http-proxy-middleware": "^2.0.6", "lodash-es": "^4.17.21", "luxon": "^3.3.0", "moment": "^2.30.1", "normalize.css": "^8.0.1", "opn": "^6.0.0", "qs": "^6.10.1", "semver": "^7.3.8", "terser-webpack-plugin": "^5.3.6", "vm-browserify": "^1.1.2", "vue": "^3.2.36", "vue-directive-tooltip": "^1.5.1", "vue-feather-icons": "^5.1.0", "vue-multiselect": "^3.0.0-beta.3", "vue-plotly": "^1.1.0", "vue-router": "^4.1.6", "vue-select": "^4.0.0-beta.6", "vue3-plotly": "^0.0.7", "vuex": "^4.1.0"}, "devDependencies": {"@babel/cli": "^7.13.16", "@babel/core": "^7.14.0", "@babel/eslint-parser": "^7.5.4", "@babel/plugin-proposal-decorators": "^7.13.15", "@babel/plugin-proposal-export-namespace-from": "^7.12.13", "@babel/plugin-proposal-function-sent": "^7.12.13", "@babel/plugin-proposal-json-strings": "^7.13.8", "@babel/plugin-proposal-numeric-separator": "^7.12.13", "@babel/plugin-proposal-throw-expressions": "^7.12.13", "@babel/plugin-syntax-decorators": "^7.12.13", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-transform-runtime": "^7.13.15", "@babel/polyfill": "^7.8.3", "@babel/preset-env": "^7.14.1", "@babel/register": "^7.13.16", "@babel/runtime": "^7.14.0", "@soda/friendly-errors-webpack-plugin": "^1.8.0", "autoprefixer": "^10.2.5", "babel-loader": "^8.2.2", "babel-plugin-istanbul": "^6.0.0", "chalk": "^4.1.2", "clean-webpack-plugin": "^3.0.0", "compression-webpack-plugin": "^7.1.2", "copy-webpack-plugin": "^8.1.1", "crypto-browserify": "^3.12.0", "crypto-js": "^4.0.0", "css-loader": "^6.7.2", "enumify": "^1.0.4", "eslint": "8.22.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.3.0", "eslint-import-resolver-webpack": "^0.13.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-react": "^7.31.11", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-vue": "^8.0.3", "eventsource-polyfill": "^0.9.6", "file-loader": "^6.2.0", "font-awesome": "^4.7.0", "html-webpack-plugin": "^5.3.1", "mini-css-extract-plugin": "^2.7.1", "ora": "^5.4.0", "rimraf": "^3.0.2", "sass": "1.56.1", "sass-loader": "^11.1.1", "semver": "^7.3.5", "stream-browserify": "^3.0.0", "url-loader": "^4.1.1", "vue-eslint-parser": "^7.6.0", "vue-feather-icons": "^5.1.0", "vue-loader": "^17.0.1", "vue-style-loader": "^4.1.3", "webpack": "^5.75.0", "webpack-bundle-analyzer": "^4.4.1", "webpack-cli": "^4.7.0", "webpack-dev-middleware": "^4.2.0", "webpack-dev-server": "^4.5.0", "webpack-hot-middleware": "^2.25.0", "webpack-merge": "^5.7.3"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}