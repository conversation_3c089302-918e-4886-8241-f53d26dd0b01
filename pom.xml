<?xml version="1.0" encoding="UTF-8"?>
<!-- ~ Copyright 2016 Adoreboard Ltd. All rights reserved. ~ ADOREBOARD PROPRIETARY/CONFIDENTIAL.
	Use is subject to license terms. -->


<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.adoreboard</groupId>
    <artifactId>farfisa</artifactId>
    <version>19.3.0</version>
    <packaging>pom</packaging>

    <modules>
        <module>farfisa-common</module>
        <module>farfisa-api</module>
        <module>farfisa-processor</module>
        <module>farfisa-data</module>
        <module>farfisa-calculator</module>
    </modules>

    <properties>

        <!-- Internal -->

        <caerus.version>1.14.0</caerus.version>

        <!-- Version properties -->

        <!-- Spring dependencies -->

        <frontend.version>1.0</frontend.version>
        <spring.amqp.version>2.3.10</spring.amqp.version>
        <spring.batch.version>4.3.3</spring.batch.version>
        <spring.boot.version>2.1.0.RELEASE</spring.boot.version>
        <spring.data.redis.version>2.3.8.RELEASE</spring.data.redis.version>
        <spring.session.data.redis.version>2.3.3.RELEASE</spring.session.data.redis.version>
        <spring.integration.version>5.5.4</spring.integration.version>
        <spring.security.version>5.5.2</spring.security.version>
        <spring.security.oauth.version>2.5.2.RELEASE</spring.security.oauth.version>
        <spring.security.oauth2.version>5.8.6</spring.security.oauth2.version>
        <spring.security.jwt>1.1.1.RELEASE</spring.security.jwt>
        <spring.version>5.3.10</spring.version>
        <spring.websocket.version>5.3.10</spring.websocket.version>
        <swagger-springmvc.version>0.6.6</swagger-springmvc.version>

        <!-- External libary version properites -->

        <aerogear.version>1.0.0</aerogear.version>
        <apache.commons-dbcp2.version>2.9.0</apache.commons-dbcp2.version>
        <apache.commons-math3.version>3.6.1</apache.commons-math3.version>
        <apache.commons-pool.version>2.11.1</apache.commons-pool.version>
        <apache.csv.version>1.9.0</apache.csv.version>
        <apache.httpcomponents.version>4.5.13</apache.httpcomponents.version>
        <apache.log4j.version>2.16.0</apache.log4j.version>
        <apache.mahout.version>0.9</apache.mahout.version>
        <apache.opennlp.version>1.9.3</apache.opennlp.version>
        <apache.poi.version>4.1.2</apache.poi.version>
        <assertj.version>3.21.0</assertj.version>
        <bucket4j.version>6.3.0</bucket4j.version>
        <com.google.zxing.version>3.4.1</com.google.zxing.version>
        <commons-codec.version>1.15</commons-codec.version>
        <commons-dbcp-jmx-jdbc4.version>0.2.4</commons-dbcp-jmx-jdbc4.version>
        <commons-fileupload.version>1.4</commons-fileupload.version>
        <commons-io.version>2.4</commons-io.version>
        <commons-lang.version>3.12.0</commons-lang.version>
        <commons-logging.version>1.2</commons-logging.version>
        <dom4j.version>2.1.3</dom4j.version>
        <ehcache.version>2.10.9.2</ehcache.version>
        <fasterxml.jackson.core.version>2.12.5</fasterxml.jackson.core.version>
        <fasterxml.jackson.datatype.version>2.13.2</fasterxml.jackson.datatype.version>
        <fasttext.version>0.4</fasttext.version>
        <freemarker.version>2.3.31</freemarker.version>
        <google.api.client.version>2.2.0</google.api.client.version>
        <gson.version>2.8.8</gson.version>
        <guava.version>31.0.1-jre</guava.version>
        <hamcrest.version>2.2</hamcrest.version>
        <io.lettuce.version>6.3.2.RELEASE</io.lettuce.version>
        <javax.annotation.version>1.3.2</javax.annotation.version>
        <javax.inject.version>1</javax.inject.version>
        <javax.mail.version>1.6.2</javax.mail.version>
        <javax.servlet.version>4.0.1</javax.servlet.version>
        <javax.websocket.version>1.1</javax.websocket.version>
        <jayway.jsonpath.version>2.6.0</jayway.jsonpath.version>
        <jedis.version>3.6.3</jedis.version>
        <joda-time.version>2.10.12</joda-time.version>
        <jolokia.version>1.7.1</jolokia.version>
        <json.smart.version>2.4.11</json.smart.version>
        <jsoup.version>1.14.3</jsoup.version>
        <junit.version>4.13.2</junit.version>
        <jwt.version>3.18.2</jwt.version>
        <liblinear.version>2.43</liblinear.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <lombok.version>1.18.36</lombok.version>
        <mandrill.lutung.version>0.0.8</mandrill.lutung.version>
        <mapstruct.version>1.6.3</mapstruct.version>
        <metrics-cloudwatch.version>0.4.0</metrics-cloudwatch.version>
        <metrics-jvm.version>4.2.4</metrics-jvm.version>
        <metrics-spring.version>3.1.3</metrics-spring.version>
        <mockito.version>1.9.5</mockito.version>
        <mybatis-spring.version>1.3.3</mybatis-spring.version>
        <mybatis.caches.version>1.2.3</mybatis.caches.version>
        <mybatis.version>3.5.7</mybatis.version>
        <nd4j.version>1.0.0-beta3</nd4j.version>
        <opencsv.version>5.5.2</opencsv.version>
        <opennlp-tools.version>1.9.3</opennlp-tools.version>
        <optimaize.version>0.6</optimaize.version>
        <pdfbox.version>2.0.16</pdfbox.version>
        <postgresql.version>42.3.4</postgresql.version>
        <rabbitmq.amqp.version>5.13.1</rabbitmq.amqp.version>
        <scala-lang.version>2.13.6</scala-lang.version>
        <skyscreamer.version>1.5.0</skyscreamer.version>
        <slf4j.version>1.7.5</slf4j.version>
        <software.amazon.awssdk.version>2.21.7</software.amazon.awssdk.version>
        <sourceforce.argo.version>3.40</sourceforce.argo.version>
        <springfox-swagger2.version>2.7.0</springfox-swagger2.version>
        <stripe.version>7.63.1</stripe.version>
        <thymeleaf.version>3.0.11.RELEASE</thymeleaf.version>
        <xercesImpl.version>2.12.1</xercesImpl.version>
        <xlsx-streamer.version>2.2.0</xlsx-streamer.version>
        <zxcvbn.version>1.5.2</zxcvbn.version>
        <!-- Database properties -->

        <postgresql.test.db.server>*****************************************</postgresql.test.db.server>
        <postgresql.test.db.database>*********************************************</postgresql.test.db.database>
        <postgres.username>postgres</postgres.username>
        <postgres.password>manager</postgres.password>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

    </properties>

    <dependencyManagement>
        <dependencies>

            <!-- Internal Dependencies -->
            <dependency>
                <groupId>com.adoreboard</groupId>
                <artifactId>farfisa-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.adoreboard</groupId>
                <artifactId>farfisa-calculator-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.adoreboard</groupId>
                <artifactId>farfisa-common</artifactId>
                <version>${project.version}</version>
                <type>test-jar</type>
                <scope>test</scope>
            </dependency>

            <!-- Caerus -->

            <dependency>
                <groupId>com.adoreboard</groupId>
                <artifactId>caerus-common</artifactId>
                <version>${caerus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.adoreboard</groupId>
                <artifactId>caerus-api</artifactId>
                <version>${caerus.version}</version>
            </dependency>

            <!-- Spring dependencies -->

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-core</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-beans</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-webmvc</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-aop</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-jdbc</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-expression</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.session</groupId>
                <artifactId>spring-session-data-redis</artifactId>
                <version>${spring.session.data.redis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-redis</artifactId>
                <version>${spring.data.redis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.amqp</groupId>
                <artifactId>spring-rabbit</artifactId>
                <version>${spring.amqp.version}</version> <!--1.6.7.RELEASE-->
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-core</artifactId>
                <version>${spring.security.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-web</artifactId>
                <version>${spring.security.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-config</artifactId>
                <version>${spring.security.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.integration</groupId>
                <artifactId>spring-integration-core</artifactId>
                <version>${spring.integration.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.integration</groupId>
                <artifactId>spring-integration-amqp</artifactId>
                <version>${spring.integration.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.amqp</groupId>
                <artifactId>spring-amqp</artifactId>
                <version>${spring.amqp.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.batch</groupId>
                <artifactId>spring-batch-core</artifactId>
                <version>${spring.batch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.batch</groupId>
                <artifactId>spring-batch-infrastructure</artifactId>
                <version>${spring.batch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.batch</groupId>
                <artifactId>spring-batch-integration</artifactId>
                <version>${spring.batch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-websocket</artifactId>
                <version>${spring.websocket.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <!-- Metrics -->
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-bom</artifactId>
                <version>1.15.1</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Other dependencies -->

            <dependency>
                <groupId>com.rabbitmq</groupId>
                <artifactId>amqp-client</artifactId>
                <version>${rabbitmq.amqp.version}</version>
            </dependency>
            <dependency>
                <groupId>net.sourceforge.argo</groupId>
                <artifactId>argo</artifactId>
                <version>${sourceforce.argo.version}</version>
            </dependency>
            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>${freemarker.version}</version>
            </dependency>
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${joda-time.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-joda</artifactId>
                <version>${fasterxml.jackson.core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${fasterxml.jackson.core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${fasterxml.jackson.core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${fasterxml.jackson.core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>${fasterxml.jackson.datatype.version}</version>
            </dependency>
            <dependency>
                <groupId>org.dom4j</groupId>
                <artifactId>dom4j</artifactId>
                <version>${dom4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>${mybatis-spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.caches</groupId>
                <artifactId>mybatis-ehcache</artifactId>
                <version>${mybatis.caches.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-dbcp2</artifactId>
                <version>${apache.commons-dbcp2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>${apache.commons-pool.version}</version>
            </dependency>
            <dependency>
                <groupId>net.ju-n.commons-dbcp-jmx</groupId>
                <artifactId>commons-dbcp-jmx-jdbc4</artifactId>
                <version>${commons-dbcp-jmx-jdbc4.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mangofactory</groupId>
                <artifactId>swagger-springmvc</artifactId>
                <version>${swagger-springmvc.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${apache.httpcomponents.version}</version>
            </dependency>
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${commons-fileupload.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>cloudwatch</artifactId>
                <version>${software.amazon.awssdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>bedrock</artifactId>
                <version>${software.amazon.awssdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>bedrockruntime</artifactId>
                <version>${software.amazon.awssdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>s3</artifactId>
                <version>${software.amazon.awssdk.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${apache.poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${apache.poi.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>${commons-codec.version}</version>
            </dependency>
            <dependency>
                <groupId>org.scala-lang</groupId>
                <artifactId>scala-library</artifactId>
                <version>${scala-lang.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ryantenney.metrics</groupId>
                <artifactId>metrics-spring</artifactId>
                <version>${metrics-spring.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-jvm</artifactId>
                <version>${metrics-jvm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.blacklocus</groupId>
                <artifactId>metrics-cloudwatch</artifactId>
                <version>${metrics-cloudwatch.version}</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${springfox-swagger2.version}</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>${springfox-swagger2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.monitorjbl</groupId>
                <artifactId>xlsx-streamer</artifactId>
                <version>${xlsx-streamer.version}</version>
            </dependency>
            <dependency>
                <groupId>com.opencsv</groupId>
                <artifactId>opencsv</artifactId>
                <version>${opencsv.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.nulab-inc</groupId>
                <artifactId>zxcvbn</artifactId>
                <version>${zxcvbn.version}</version>
            </dependency>
            <dependency>
                <groupId>io.lettuce</groupId>
                <artifactId>lettuce</artifactId>
                <version>${io.lettuce.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.aerogear</groupId>
                <artifactId>aerogear-otp-java</artifactId>
                <version>${aerogear.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mandrillapp.wrapper.lutung</groupId>
                <artifactId>lutung</artifactId>
                <version>${mandrill.lutung.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>${com.google.zxing.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>javase</artifactId>
                <version>3.4.0</version>
            </dependency>
            <dependency>
                <groupId>xerces</groupId>
                <artifactId>xercesImpl</artifactId>
                <version>${xercesImpl.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-csv</artifactId>
                <version>${apache.csv.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-math3</artifactId>
                <version>${apache.commons-math3.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>${javax.servlet.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.mahout</groupId>
                <artifactId>mahout-core</artifactId>
                <version>${apache.mahout.version}</version>
            </dependency>
            <dependency>
                <groupId>com.optimaize.languagedetector</groupId>
                <artifactId>language-detector</artifactId>
                <version>${optimaize.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.mail</groupId>
                <artifactId>javax.mail</artifactId>
                <version>${javax.mail.version}</version>
            </dependency>
            <dependency>
                <groupId>com.stripe</groupId>
                <artifactId>stripe-java</artifactId>
                <version>${stripe.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.vinhkhuc</groupId>
                <artifactId>jfasttext</artifactId>
                <version>${fasttext.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.opennlp</groupId>
                <artifactId>opennlp-tools</artifactId>
                <version>${opennlp-tools.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.vladimir-bukhtoyarov</groupId>
                <artifactId>bucket4j-core</artifactId>
                <version>${bucket4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jolokia</groupId>
                <artifactId>jolokia-core</artifactId>
                <version>${jolokia.version}</version>
            </dependency>
            <dependency>
                <groupId>net.sf.ehcache</groupId>
                <artifactId>ehcache</artifactId>
                <version>${ehcache.version}</version>
            </dependency>
            <dependency>
                <groupId>io.lettuce</groupId>
                <artifactId>lettuce-core</artifactId>
                <version>${io.lettuce.version}</version>
            </dependency>
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${jedis.version}</version>
            </dependency>
            <!-- Compile -->

            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <type>jar</type>
                <scope>compile</scope>
            </dependency>
            <!-- Unit testing -->

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-test</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>hamcrest-core</artifactId> <!-- mockito-core's dependency is on hamcrest-core 1.1, junit 4.11 includes
						a dependency on hamcrest-core 1.3 -->
                        <groupId>org.hamcrest</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.jayway.jsonpath</groupId>
                <artifactId>json-path</artifactId>
                <version>${jayway.jsonpath.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.hamcrest</groupId>
                <artifactId>hamcrest-library</artifactId>
                <version>${hamcrest.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.skyscreamer</groupId>
                <artifactId>jsonassert</artifactId>
                <version>${skyscreamer.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>net.sourceforge.htmlunit</groupId>
                <artifactId>htmlunit</artifactId>
                <version>${net.sourceforge.htmlunit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.assertj</groupId>
                <artifactId>assertj-core</artifactId>
                <version>${assertj.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-test</artifactId>
                <version>${spring.security.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.security.oauth</groupId>
                <artifactId>spring-security-oauth2</artifactId>
                <version>${spring.security.oauth.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-oauth2-core</artifactId>
                <version>${spring.security.oauth2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-jwt</artifactId>
                <version>${spring.security.jwt}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-oauth2-jose</artifactId>
                <version>${spring.security.oauth2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-oauth2-client</artifactId>
                <version>${spring.security.oauth2.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.inject</groupId>
                <artifactId>javax.inject</artifactId>
                <version>${javax.inject.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.api-client</groupId>
                <artifactId>google-api-client</artifactId>
                <version>${google.api.client.version}</version>
            </dependency>
            <dependency>
                <groupId>de.bwaldvogel</groupId>
                <artifactId>liblinear</artifactId>
                <version>${liblinear.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.annotation</groupId>
                <artifactId>javax.annotation-api</artifactId>
                <version>${javax.annotation.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.websocket</groupId>
                <artifactId>javax.websocket-client-api</artifactId>
                <version>${javax.websocket.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>javax.websocket</groupId>
                <artifactId>javax.websocket-api</artifactId>
                <version>${javax.websocket.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>net.minidev</groupId>
                <artifactId>json-smart</artifactId>
                <version>${json.smart.version}</version>
            </dependency>
            <dependency>
                <groupId>org.thymeleaf</groupId>
                <artifactId>thymeleaf</artifactId>
                <version>${thymeleaf.version}</version>
            </dependency>
            <dependency>
                <groupId>org.thymeleaf</groupId>
                <artifactId>thymeleaf-spring5</artifactId>
                <version>${thymeleaf.version}</version>
            </dependency>
            <dependency>
                <groupId>org.nd4j</groupId>
                <artifactId>nd4j-native</artifactId>
                <version>${nd4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>${pdfbox.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>fontbox</artifactId>
                <version>${pdfbox.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>${commons-logging.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!-- Logging Dependencies -->
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <version>${apache.log4j.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <version>${apache.log4j.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
            <version>${apache.log4j.version}</version>
        </dependency>
    </dependencies>

    <!-- <repositories>
        <repository>
            <id>ab-repo</id>
            <name>ab-repo-releases</name>
            <url>https://repo.adoreboard.com/artifactory/libs-release-local</url>
        </repository>
    </repositories> -->

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>3.1.0</version>
            </plugin>

            <plugin>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.4.0</version>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.13.0</version>
                <configuration>
                    <source>11</source>
                    <target>11</target>
                    <annotationProcessorPaths>
                        <!-- Note: mapstruct must place before lombok -->
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok-mapstruct-binding.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>

        <pluginManagement>
            <plugins>

                <!-- Set up unit test phase to ignore tests marked with the annotation @Category(IntegrationTest.class) -->

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>3.3.0</version>
                    <dependencies>
                        <dependency>
                            <groupId>org.apache.maven.surefire</groupId>
                            <artifactId>surefire-junit47</artifactId>
                            <version>3.3.0</version>
                        </dependency>
                    </dependencies>
                    <configuration>
                        <forkCount>1</forkCount>
                        <reuseForks>true</reuseForks>
                        <argLine>-Xms4g -Xmx4g --add-opens java.base/java.lang=ALL-UNNAMED --add-modules java.sql
                        </argLine>
                        <includes>
                            <include>**/*.class</include>
                        </includes>
                        <excludedGroups>com.adoreboard.emotics.common.test.IntegrationTest</excludedGroups>
                    </configuration>
                </plugin>

                <!-- Set up integration test phase to only include tests marked with the annotation @Category(IntegrationTest.class) -->

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-failsafe-plugin</artifactId>
                    <version>3.3.0</version>
                    <dependencies>
                        <dependency>
                            <groupId>org.apache.maven.surefire</groupId>
                            <artifactId>surefire-junit47</artifactId>
                            <version>3.3.0</version>
                        </dependency>
                    </dependencies>
                    <configuration>
                        <forkCount>1</forkCount>
                        <reuseForks>true</reuseForks>
                        <argLine>-Xms4g -Xmx4g</argLine>
                        <groups>com.adoreboard.emotics.common.test.IntegrationTest</groups>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>integration-test</goal>
                                <goal>verify</goal>
                            </goals>
                            <configuration>
                                <includes>
                                    <include>**/*.class</include>
                                </includes>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>

                <plugin>
                    <groupId>org.whitesource</groupId>
                    <artifactId>whitesource-maven-plugin</artifactId>
                    <version>3.1.7</version>
                    <configuration>
                        <orgToken>1be428f4-756c-41f2-a381-22cf516325c2</orgToken>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

</project>