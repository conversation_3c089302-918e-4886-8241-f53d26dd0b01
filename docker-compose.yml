version: '3.8'
services:
  db:
    image: postgres:14.1-alpine
    restart: always
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=manager
    ports:
      - '5432:5432'
  cache:
    image: redis:6.2-alpine
    restart: always
    ports:
      - '6379:6379'
  rabbitmq:
      image: rabbitmq:management
      container_name: rabbitmq
      environment:
        - RABBITMQ_DEFAULT_USER=guest
        - RABBITMQ_DEFAULT_PASS=guest
      ports:
        - "5672:5672"
        - "15672:15672"
volumes:
  cache:
    driver: local