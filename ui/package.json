{"name": "ui", "version": "1.0.0", "description": "A Vue.js project", "author": "<PERSON> <<EMAIL>>", "private": true, "scripts": {"start": "webpack serve --config build/webpack.dev.conf.js --port 3000 --hot", "build": "node build/build.js", "unit": "cross-env BABEL_ENV=test karma start test/unit/karma.conf.js --single-run", "unit-watch": "cross-env BABEL_ENV=test karma start test/unit/karma.conf.js", "e2e": "node test/e2e/runner.js", "test": "npm run unit && npm run e2e", "lint": "eslint --fix --ext .js,.vue src", "docs": "rm -rf ./docs && node_modules/.bin/jsdoc --configure .jsdoc.json --verbose"}, "dependencies": {"@adapttive/vue-markdown": "^4.0.1", "@fortawesome/fontawesome-pro": "^6.7.1", "@fortawesome/fontawesome-svg-core": "^6.7.1", "@fortawesome/pro-light-svg-icons": "^6.7.1", "@fortawesome/vue-fontawesome": "^2.0.8", "@ladjs/country-language": "^0.2.1", "@shopify/draggable": "^1.0.0-beta.9", "@tiptap/extension-color": "^2.4.0", "@tiptap/extension-text-style": "^2.4.0", "@tiptap/pm": "^2.3.0", "@tiptap/starter-kit": "^2.3.0", "@tiptap/vue-2": "^2.3.0", "axios": "^0.21.1", "chroma-js": "^2.1.1", "crypto-browserify": "^3.12.0", "crypto-js": "^4.0.0", "d3": "^7.8.5", "date-fns": "^2.21.3", "deep-freeze": "0.0.1", "dom-to-image": "^2.6.0", "dragselect": "^1.13.1", "dropzone": "^5.9.2", "enumify": "^1.0.4", "file-saver": "^2.0.5", "font-awesome": "^4.7.0", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.2", "jszip": "^3.10.1", "lodash-es": "^4.17.21", "logrocket": "^2.1.3", "markdown-it": "^12.2.0", "multi-range-slider-vue": "^1.1.4", "net": "^1.0.2", "normalize.css": "^8.0.1", "numeral": "^2.0.6", "papaparse": "^5.4.0", "pdf-lib": "^1.17.1", "pptxgenjs": "^3.12.0", "qs": "^6.10.1", "resize-observer-polyfill": "^1.5.1", "sass": "^1.53.0", "save-svg-as-png": "^1.4.17", "sockjs-client": "^1.5.1", "stompjs": "^2.3.3", "stream-browserify": "^3.0.0", "svgsaver": "^0.9.0", "underscore": "^1.13.1", "uuid": "^8.3.2", "vm-browserify": "^1.1.2", "vue": "^2.6.12", "vue-color": "^2.8.1", "vue-ctk-date-time-picker": "^2.5.0", "vue-custom-scrollbar": "^1.4.0", "vue-directive-tooltip": "1.5.1", "vue-feather-icons": "^5.1.0", "vue-html2pdf": "^1.8.0", "vue-input-tag": "^2.0.7", "vue-paginate": "^3.6.0", "vue-router": "^3.5.1", "vue-slider-component": "^3.2.11", "vue-stripe-elements-plus": "^0.2.10", "vue-textarea-autosize": "^1.1.1", "vue-virtual-scroll-list": "^1.4.4", "vue-virtual-scroller": "^1.0.10", "vue-youtube": "^1.4.0", "vuedraggable": "^2.24.3", "vuejs-datepicker": "^1.6.2", "vuex": "^3.6.2", "zxcvbn": "^4.4.2"}, "devDependencies": {"@babel/cli": "^7.13.16", "@babel/core": "^7.14.0", "@babel/plugin-proposal-decorators": "^7.13.15", "@babel/plugin-proposal-export-namespace-from": "^7.12.13", "@babel/plugin-proposal-function-sent": "^7.12.13", "@babel/plugin-proposal-json-strings": "^7.13.8", "@babel/plugin-proposal-numeric-separator": "^7.12.13", "@babel/plugin-proposal-throw-expressions": "^7.12.13", "@babel/plugin-syntax-decorators": "^7.12.13", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-transform-runtime": "^7.13.15", "@babel/polyfill": "^7.8.3", "@babel/preset-env": "^7.14.1", "@babel/register": "^7.13.16", "@babel/runtime": "^7.14.0", "@soda/friendly-errors-webpack-plugin": "^1.8.0", "autoprefixer": "^10.2.5", "babel-eslint": "^10.0.3", "babel-loader": "^8.2.2", "babel-plugin-istanbul": "^6.0.0", "braintree-jsdoc-template": "^3.3.0", "chai": "^4.3.4", "chalk": "^4.1.2", "chromedriver": "^90.0.0", "clean-webpack-plugin": "^3.0.0", "compression-webpack-plugin": "^7.1.2", "connect-history-api-fallback": "^1.6.0", "copy-webpack-plugin": "^8.1.1", "cross-env": "^7.0.3", "cross-spawn": "^7.0.3", "css-loader": "^5.2.4", "css-minimizer-webpack-plugin": "^3.0.0", "eslint": "^7.26.0", "eslint-config-airbnb": "^18.2.1", "eslint-config-airbnb-base": "^14.2.1", "eslint-config-prettier": "^8.3.0", "eslint-import-resolver-webpack": "^0.13.0", "eslint-loader": "^4.0.2", "eslint-plugin-html": "^6.1.2", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-react": "^7.23.2", "eslint-plugin-vue": "^7.9.0", "eslint-webpack-plugin": "^3.1.1", "eventsource-polyfill": "^0.9.6", "express": "^4.17.1", "file-loader": "^6.2.0", "function-bind": "^1.1.1", "html-webpack-plugin": "^5.3.1", "http-proxy-middleware": "^2.0.0", "jsdoc": "^4.0.2", "jsdoc-vue": "^1.0.0", "karma-coverage": "^2.0.3", "karma-mocha": "^2.0.1", "karma-sinon-chai": "^2.0.2", "karma-sourcemap-loader": "^0.3.8", "lolex": "^5.1.2", "markdown-loader": "^8.0.0", "mini-css-extract-plugin": "^1.6.0", "mocha": "^10.2.0", "opn": "^6.0.0", "ora": "^5.4.0", "phantomjs-prebuilt": "^2.1.16", "rimraf": "^3.0.2", "sass-loader": "^11.1.0", "selenium-server": "^3.141.59", "semver": "^7.3.5", "sinon": "^10.0.0", "sinon-chai": "^3.6.0", "string-loader": "0.0.1", "terser-webpack-plugin": "^5.1.1", "url-loader": "^4.1.1", "vue-eslint-parser": "^7.6.0", "vue-loader": "^15.9.7", "vue-style-loader": "^4.1.3", "vue-template-compiler": "^2.6.12", "webpack": "^5.37.0", "webpack-bundle-analyzer": "^4.4.1", "webpack-cli": "^4.7.0", "webpack-dev-middleware": "^4.2.0", "webpack-dev-server": "^4.5.0", "webpack-hot-middleware": "^2.25.0", "webpack-merge": "^5.7.3"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}