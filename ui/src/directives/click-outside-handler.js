export default {
  bind(el, binding, vnode) {
    el.clickOutsideHandler = (e) => {
      // check if click was outside the el & any specific excluded components
      const { handler, excluded, excludedParentClasses } = binding.value;
      let clickedOnExcludedEl = false;
      // excluded ref
      if (excluded?.length) {
        excluded.forEach(refName => {
          if (!clickedOnExcludedEl) {
            const excludedEl = vnode.context.$refs[refName];
            clickedOnExcludedEl = excludedEl.$el.contains(e.target);
          }
        });
      }
      // excluded parents' class
      if (!clickedOnExcludedEl && excludedParentClasses?.length) {
        const targetParentClasses = e.target?.parentElement?.getAttribute('class')?.split(' ') || [];
        for (let i = 0; i < targetParentClasses.length; i += 1) {
          clickedOnExcludedEl = excludedParentClasses.includes(targetParentClasses[i]);
          if (clickedOnExcludedEl) {
            break;
          }
        }
      }
      // callback handler function
      if (!el.contains(e.target) && !clickedOnExcludedEl) {
        vnode.context[handler](e);
      }
    };

    document.body.addEventListener('click', el.clickOutsideHandler);
  },

  unbind(el) {
    document.body.removeEventListener('click', el.clickOutsideHandler);
  },
};
