export default {
  bind(el, binding, vnode) {
    el.clickOutsideEvent = event => {
      // here I check that click was outside the el and his childrens
      if (
        el !== event.target
        && !el.contains(event.target)
        && event.target.className !== 'interface-controls-operators-tag'
        && event.target.className !== 'interface-bar-input-text-input'
      ) {
        // and if it did, call method provided in attribute value
        vnode.context[binding.expression](event);
      }
    };

    document.body.addEventListener('click', el.clickOutsideEvent);
  },
  unbind(el) {
    document.body.removeEventListener('click', el.clickOutsideEvent);
  },
};
