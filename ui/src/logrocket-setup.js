import LogRocket from 'logrocket';

if (window.location.host === 'app.emotics.io') {
  let c;

  try {
    c = console;
  } catch (e) {
    // do nothing, just fail if console doesn't exist
  }

  if (c) c.log('LogRocket initialised');

  LogRocket.init('btef2h/production-u2y7q', {
    dom: {
      textSanitizer: true,
      inputSanitizer: true,
    },
    network: {
      requestSanitizer: () => null,
      responseSanitizer: () => null,
    },
    browser: {
      urlSanitizer: url => url.replace(/\?.*/gim, ''),
    },
  });
}
