<template>
  <section class="storyteller-regenerate-action-modal-custom">
    <section class="wrapper" :class="{ editing: !disable || focusing }">
      <textarea v-model="localInput"
        class="text-area"
        :placeholder="placeHolder"
        @blur="focusing = false"
        @focus="focusing = true"
        @keydown="onKeydown"
      />
    </section>
    <section class="footer" :class="{disable}">
      <base-button class="submit-btn" size="small" @click="onClickSubmit">
        <span>Submit</span>
      </base-button>
    </section>
  </section>
</template>

<script>
import BaseButton from '@/components/Base/BaseButton';

export default {
  name: 'storyteller-regenerate-action-modal-custom',

  components: {
    BaseButton,
  },

  data() {
    return {
      focusing: false,
      localInput: '',
    };
  },

  computed: {
    placeHolder() {
      return 'Type here to tell me what you need. e.g. ‘could you please focus on the issue of work-life balance?’';
    },

    disable() {
      return this.localInput.trim() === '';
    },
  },

  methods: {
    onClickSubmit() {
      if (this.disable) return;
      this.$emit('submitCustom', this.localInput.trim());
    },

    onKeydown(e) {
      if (!e.shiftKey && e.which === 13) {
        this.onClickSubmit();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-regenerate-action-modal-custom {
  margin-top: 1rem;
  width: 100%;

  .wrapper {
    background-color: #FFF;
    border-radius: 4px;
    padding: 2.5px;

    .text-area {
      border-radius: 4px;
      border: 1px solid rgba(19, 28, 41, 0.3);
      display: -webkit-box;
      height: 100px;
      line-height: 1.225rem;
      outline: none;
      overflow-y: auto;
      padding: 0.6rem;
      resize: none;
      width: 100%;

      &::placeholder {
        opacity: 0.5;
      }
    }

    &.editing {
      background-color: rgba(53, 86, 255, 0.4);

      .text-area {
        border: 1px solid rgba(19, 28, 41, 1);
      }
    }
  }

  .footer {
    @include flex("block", "row", "start", "start");

    margin-bottom: 0.5rem;
    margin-top: 1rem;
    width: 100%;

    .submit-btn {
      background-color: #253CB3;
      margin-left: auto;
      padding: 0.5rem 0.8rem;
      text-transform: uppercase;

      &:hover {
        background-color: #3556FF;
      }
    }

    &.disable {
      .submit-btn {
        cursor: not-allowed;
        opacity: 0.3;
      }
    }
  }
}
</style>
