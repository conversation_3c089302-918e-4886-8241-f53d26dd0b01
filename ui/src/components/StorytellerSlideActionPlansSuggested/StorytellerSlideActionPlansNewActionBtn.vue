<template>
  <section class="storyteller-slide-action-plans-new-action-btn">
    <button class="btn" @click.stop="onClickBtn">
      <i class="fa-regular fa-wand-magic-sparkles magic-icon" />
      <span>New Action</span>
    </button>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import StorytellerRegenerateActionModal from '@/components/StorytellerSlideActionPlansSuggested/StorytellerRegenerateActionModal';

export default {
  name: 'storyteller-slide-action-plans-new-action-btn',

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    onClickBtn() {
      this.setModalComponent({ component: StorytellerRegenerateActionModal });
    },
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-action-plans-new-action-btn {
  @include flex("block", "row", "start", "center");

  margin-left: 0.4rem;
  height: 1.5rem;

  .btn {
    @include flex("block", "row", "center", "center");

    background: linear-gradient(180deg, #A739CE 0%, #4E75FF 150%) repeat-y;
    border-radius: 3px;
    border: none;
    color: clr('white');
    cursor: pointer;
    font-size: $font-size-xxs;
    font-weight: $font-weight-bold;
    height: 100%;
    padding: 0.4rem 0.6rem;
    text-transform: uppercase;

    .magic-icon {
      margin-right: 0.2rem;
    }

    &:hover, &:focus {
      background: linear-gradient(180deg, #792BA1 0%, #B35CE0 150%) repeat-y;
    }
  }
}
</style>
