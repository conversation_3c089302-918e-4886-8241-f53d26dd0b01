<template>
  <section class="storyteller-slide-action-plans-text" :class="{editable}">
    <section v-if="!editing" class="text">
      <section v-html="suggestedAction.actionPoint" class="text-content" @dblclick="startEditing" />
      <section class="icons" v-if="editable">
        <section class="icon-wrapper edit" @click="startEditing"
          v-tooltip.top="{
            content: 'Edit',
            class: 'tooltip-base-dark',
            delay: 0,
          }"
        >
          <i class="fa-light fa-pen icon" />
        </section>
      </section>
    </section>
    <storyteller-text-tiptap v-else
      :additional-buttons="additionalButtons"
      :init-content="suggestedAction.actionPoint"
      :show-text-size-control="false"
      max-height="7.26em"
      @blur="stopEditing"
    />
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import StorytellerSlideActionPlansDeleteBtn from '@/components/StorytellerSlideActionPlansSuggested/StorytellerSlideActionPlansDeleteBtn';
import StorytellerSlideActionPlansNewActionBtn from '@/components/StorytellerSlideActionPlansSuggested/StorytellerSlideActionPlansNewActionBtn';
import StorytellerTextTiptap from '@/components/StorytellerSlideText/StorytellerTextTiptap';

export default {
  name: 'storyteller-slide-action-plans-text',

  components: {
    StorytellerTextTiptap,
  },

  props: {
    suggestedAction: {
      type: Object,
      required: true,
    },
    editable: {
      type: Boolean,
      required: true,
    },
  },

  data() {
    return {
      editing: false,
      additionalButtons: [StorytellerSlideActionPlansNewActionBtn, StorytellerSlideActionPlansDeleteBtn],
    };
  },

  methods: {
    ...mapActions('storytellerActionPlans', ['selectAction']),

    startEditing() {
      this.editing = true;
      this.selectAction({ action: this.suggestedAction });
    },

    stopEditing(content) {
      this.editing = false;
      this.$emit('stopEditing', content, this.suggestedAction);
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-action-plans-text {
  font-size: 0.48em;
  height: 100%;
  line-height: 1.21em;
  padding-right: 1.2em;
  position: relative;
  width: 100%;

  &:hover {
    &.editable {
      .icons {
        visibility: visible;
      }
    }
  }

  .text {
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 6;
    display: -webkit-box;
    overflow-y: hidden;
    text-overflow: ellipsis;

    .icons {
      @include flex("block", "row", "center", "center");

      position: absolute;
      right: 0;
      top: 1.4em;
      visibility: hidden;
    }

    .icon-wrapper {
      @include flex("block", "row", "center", "center");

      border-radius: 50%;
      border: 1px solid #3981F7;
      color: #3981F7;
      cursor: pointer;
      font-size: 1.6em;
      height: 1em;
      width: 1em;

      .icon {
        font-size: 0.5em;
      }

      &:hover {
        background-color: rgba(57, 129, 247, 0.3);
      }
    }
  }
}
</style>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-action-plans-text {
  color: #373e49;
  p {
    margin: 0;
  }

  strong {
    font-weight: $font-weight-semi-bold;
  }
}
</style>
