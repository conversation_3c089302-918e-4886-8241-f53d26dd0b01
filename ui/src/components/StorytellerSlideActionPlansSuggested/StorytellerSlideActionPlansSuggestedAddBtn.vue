<template>
  <section class="storyteller-slide-action-plans-suggested-add-btn">
    <loading-blocks-overlay size="small" v-if="adding" />
    <section v-else class="icon-wrapper" @click="onClick">
      <i class="fa-regular fa-plus icon-add" />
    </section>
  </section>
</template>

<script>
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import StorytellerActionPlansRequest from '@/services/request/StorytellerActionPlansRequest';

export default {
  name: 'storyteller-slide-action-plans-suggested-add-btn',

  components: {
    LoadingBlocksOverlay,
  },

  props: {
    action: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      adding: false,
    };
  },

  methods: {
    async onClick() {
      this.adding = true;
      await StorytellerActionPlansRequest.addSuggestedAction(this.action.themeId);
      this.adding = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-slide-action-plans-suggested-add-btn {
  font-size:  0.68em;

  .icon-wrapper {
    @include flex("block", "row", "center", "center");

    border-radius: 50%;
    border: 1px solid #1F2734;
    cursor: pointer;
    height: 1em;
    opacity: 0.4;
    position: relative;
    width: 1em;
    min-width: 1em;

    .icon-add {
      color: #1F2734;
      font-size: 0.5em;
    }

    &:hover {
      opacity: 1;
    }
  }
}
</style>
