<template>
  <section class="storyteller-slide-action-plans-suggested" :class="{editable}">
    <span class="header">Suggested Actions</span>
    <section v-for="(action, actionIndex) in actions" class="action" :class="{middle: actionIndex === 1}" :key="actionIndex">
      <section v-for="suggested in action.suggestedActions" class="text" :key="suggested.uuid">
        <section class="icon-wrapper" @click="onClickComplete(suggested)">
          <i class="fa-regular fa-check icon" v-if="suggested.completed" />
        </section>
        <storyteller-slide-action-plans-text
          :suggested-action="suggested"
          :editable="editable"
          @stopEditing="stopEditing"
        />
      </section>
      <section v-if="action.suggestedActions.length < 2" v-for="addBtn in 2 - action.suggestedActions.length" class="add">
        <storyteller-slide-action-plans-suggested-add-btn :action="action" class="add-btn" />
      </section>
    </section>
  </section>
</template>

<script>
import StorytellerActionPlansRequest from '@/services/request/StorytellerActionPlansRequest';
import StorytellerSlideActionPlansSuggestedAddBtn from '@/components/StorytellerSlideActionPlansSuggested/StorytellerSlideActionPlansSuggestedAddBtn';
import StorytellerSlideActionPlansText from '@/components/StorytellerSlideActionPlansSuggested/StorytellerSlideActionPlansText';

export default {
  name: 'storyteller-slide-action-plans-suggested',

  components: {
    StorytellerSlideActionPlansSuggestedAddBtn,
    StorytellerSlideActionPlansText,
  },

  props: {
    actions: {
      type: Array,
      required: true,
    },
    editable: {
      type: Boolean,
      default: false,
    },
  },

  methods: {
    onClickComplete(suggested) {
      suggested.completed = !suggested.completed;
      StorytellerActionPlansRequest.updateSlide();
    },

    stopEditing(content, suggested) {
      suggested.actionPoint = content;
      StorytellerActionPlansRequest.updateSlide();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-slide-action-plans-suggested {
  &.editable {
    .action {
      &:hover {
        .add .add-btn {
          visibility: visible;
        }
      }
    }
  }

  .header {
    @include flex("block", "row", "start", "start");

    color: rgba(19, 28, 41, 0.3);
    font-size: 0.38em;
    font-weight: $font-weight-extra-bold;
    text-transform: uppercase;
  }

  .action {
    display: grid;
    grid-template-rows: 50% 50%;
    width: 100%;

    &.middle {
      background-color: rgba(241, 241, 241, 0.5);
    }

    .text {
      @include flex("block", "row", "start", "start");

      height: 100%;
      position: relative;
      padding-left: 1.2em;
      width: 100%;

      .icon-wrapper {
        @include flex("block", "row", "center", "center");

        border-radius: 2px;
        border: 1px solid;
        color: rgba(19, 28, 41, 0.4);
        cursor: pointer;
        font-size: 0.5em;
        height: 1em;
        left: 1em;
        min-height: 1em;
        min-width: 1em;
        position: absolute;
        width: 1em;

        .icon {
          color: rgba(19, 28, 41, 1);
          font-size: 0.75em;
          line-height: normal;
        }

        &:hover {
          color: rgba(19, 28, 41, 1);
        }
      }
    }

    .add {
      @include flex("block", "row", "center", "center");

      height: 100%;
      width: 100%;

      .add-btn {
        visibility: hidden;
      }
    }
  }
}
</style>
