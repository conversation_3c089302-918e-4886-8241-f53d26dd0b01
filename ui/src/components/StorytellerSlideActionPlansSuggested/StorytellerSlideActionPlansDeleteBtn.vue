<template>
  <section class="storyteller-slide-action-plans-delete-btn">
    <loading-blocks-overlay size="small" v-if="deleting" />
    <section v-else class="icon-wrapper" @click="onClick">
      <i class="fa-solid fa-trash-can icon-delete" />
    </section>
  </section>
</template>

<script>
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import StorytellerActionPlansRequest from '@/services/request/StorytellerActionPlansRequest';

export default {
  name: 'storyteller-slide-action-plans-delete-btn',

  components: {
    LoadingBlocksOverlay,
  },

  data() {
    return {
      deleting: false,
    };
  },

  methods: {
    async onClick() {
      this.deleting = true;
      await StorytellerActionPlansRequest.deleteSuggestedAction();
      this.deleting = false;
    },
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-action-plans-delete-btn {
  margin-left: 0.4rem;

  .icon-wrapper {
    @include flex("block", "row", "center", "center");

    background-color: clr('white');
    border-radius: 50%;
    border: 1px solid #FF5454;
    cursor: pointer;
    font-size: 1.5rem;
    height: 1.5rem;
    position: relative;
    width: 1.5rem;

    .icon-delete {
      color: #FF5454;
      font-size: 0.75rem;
    }

    &:hover {
      background-color: #FFE7E7;
    }
  }
}
</style>
