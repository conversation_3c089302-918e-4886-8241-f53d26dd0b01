<template>
  <section class="storyteller-regenerate-action-modal">
    <section class="header">
      <section class="header-title">
        <i class="fa-solid fa-wand-magic-sparkles icon-magic" />
        <span class="text-title">Regenerate Suggested Action</span>
      </section>
      <i class="fa-light fa-xmark x-icon" @click="closeModal" />
    </section>
    <section class="body">
      <section class="ai-chat">
        <span class="bot-message" v-html="botMessage" />
        <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M0 10L10 0V10H0Z" fill="#F0F0F0"/>
        </svg>
      </section>
      <storyteller-regenerate-action-modal-options
        v-if="viewing === 'OPTIONS_VIEW'"
        @selectCustom="selectCustom"
        @selectGenerate="selectGenerate"
        @selectRevert="selectRevert"
      />
      <loading-dots class="loading" v-else-if="viewing === 'LOADING'" />
      <storyteller-regenerate-action-modal-results
        v-else-if="viewing === 'RESULTS_VIEW'"
        :text-list="textList"
        @selectCustom="selectCustom"
      />
      <storyteller-regenerate-action-modal-custom
        v-else-if="viewing === 'CUSTOM_VIEW'"
        @submitCustom="submitCustom"
      />
    </section>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import LoadingDots from '@/components/LoadingDots';
import StorytellerRegenerateActionModalCustom from '@/components/StorytellerSlideActionPlansSuggested/StorytellerRegenerateActionModalCustom';
import StorytellerRegenerateActionModalOptions from '@/components/StorytellerSlideActionPlansSuggested/StorytellerRegenerateActionModalOptions';
import StorytellerRegenerateActionModalResults from '@/components/StorytellerSlideActionPlansSuggested/StorytellerRegenerateActionModalResults';
import StorytellerActionPlansRequest from '@/services/request/StorytellerActionPlansRequest';

export default {
  name: 'storyteller-regenerate-action-modal',

  components: {
    LoadingDots,
    StorytellerRegenerateActionModalCustom,
    StorytellerRegenerateActionModalOptions,
    StorytellerRegenerateActionModalResults,
  },

  data() {
    return {
      botMessage: 'Hey there! 👋 What would you like to do?',
      textList: [],
      viewing: 'OPTIONS_VIEW', // viewing should be OPTIONS_VIEW, LOADING, RESULTS_VIEW, CUSTOM_VIEW
    };
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    selectCustom() {
      this.botMessage = 'You Selected: <b>Customise Suggested Action.</b> Can you describe what you want from the suggested action in more detail?';
      this.viewing = 'CUSTOM_VIEW';
    },

    async selectGenerate() {
      this.viewing = 'LOADING';
      this.botMessage = 'You Selected: <b>Generate New Suggested Action.</b> <br>Give me a moment... ⏰';
      this.textList = await StorytellerActionPlansRequest.generateAiText();
      this.botMessage = 'Here are 3 suggested actions based on comments. Select one to add it to your presentation <i class="fa-regular fa-arrow-down" />';
      this.viewing = 'RESULTS_VIEW';
    },

    async selectRevert() {
      this.viewing = 'LOADING';
      await StorytellerActionPlansRequest.revertSuggestedAction();
      this.closeModal();
    },

    async submitCustom(input) {
      this.viewing = 'LOADING';
      this.botMessage = 'Thanks! Please give me a moment ⏰ I’m coming up with some new suggestions.';
      this.textList = await StorytellerActionPlansRequest.generateAiText(input);
      this.botMessage = 'Here are 3 suggested actions based on comments. Select one to add it to your presentation <i class="fa-regular fa-arrow-down" />';
      this.viewing = 'RESULTS_VIEW';
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-regenerate-action-modal {
  @include panel;

  color: $nps-blue;
  font-family: Inter, serif;
  min-height: 290px;
  padding: 1.5rem 2rem 2rem 2rem;
  position: relative;
  width: 450px;

  .header {
    @include flex("block", "row", "space-between", "center");

    margin-bottom: 1.5rem;
    width: 100%;

    .header-title {
      @include flex("block", "row", "start", "center");

      background: linear-gradient(180deg, #A43CD1, #705FED) repeat-y;
      font-size: 19px;
      font-weight: $font-weight-bold;
      width: 100%;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;

      .icon-magic {
        margin-right: 0.4rem;
      }
    }

    .x-icon {
      cursor: pointer;
      font-size: $font-size-lg;
    }
  }

  .body {
    @include flex("block", "column", "start", "start");

    font-size: $font-size-sm;
    width: 100%;

    .ai-chat {
      background-color: #F0F0F0;
      border-radius: $border-radius-medium;
      padding: 0.6rem 1rem;
      position: relative;
      width: 100%;

      .bot-message {
        line-height: 15px;
        width: 100%;
      }

      svg {
        bottom: 10px;
        left: -10px;
        position: absolute;
      }
    }

    .loading {
      margin: 3rem auto;
      width: 100%;
    }
  }
}
</style>
