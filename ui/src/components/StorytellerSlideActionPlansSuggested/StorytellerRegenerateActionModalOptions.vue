<template>
  <section class="storyteller-regenerate-action-modal-options">
    <section class="btn-action" @click="onClickGenerate">
      Generate New Suggested Action
    </section>
    <section class="btn-action" @click="onClickCustomise">
      Customise Suggested Action
    </section>
    <section class="btn-revert" @click="onClickRevert">
      <span>Revert to default suggested action</span>
      <i class="fa-regular fa-arrows-rotate-reverse" />
    </section>
  </section>
</template>

<script>
export default {
  name: 'storyteller-regenerate-action-modal-options',

  methods: {
    onClickCustomise() {
      this.$emit('selectCustom');
    },

    onClickGenerate() {
      this.$emit('selectGenerate');
    },

    onClickRevert() {
      this.$emit('selectRevert');
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-regenerate-action-modal-options {
  width: 100%;

  .btn-action {
    @include flex("block", "row", "center", "center");

    border-radius: $border-radius-medium;
    border: 1px solid rgba(115, 80, 255, 0.3);
    color: #472EE4;
    cursor: pointer;
    font-weight: $font-weight-bold;
    margin-top: 1.2rem;
    padding: 0.6rem 1rem;
    width: 100%;

    &:hover, &:focus {
      border: 1px solid rgba(115, 80, 255, 1);
      background-color: rgba(115, 80, 255, 0.1);
    }
  }

  .btn-revert {
    @include flex("block", "row", "center", "center");

    margin-top: 1.4rem;
    width: 100%;

    span {
      cursor: pointer;
      margin-right: 0.2rem;
      text-decoration: underline;
    }

    &:hover {
      color: rgba(115, 80, 255, 1);
    }
  }
}
</style>
