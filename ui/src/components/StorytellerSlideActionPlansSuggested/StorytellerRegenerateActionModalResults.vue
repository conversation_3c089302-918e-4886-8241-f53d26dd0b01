<template>
  <section class="storyteller-regenerate-action-modal-results">
    <section v-for="(text, index) in textList"
      class="item-action"
      v-html="text"
      :key="index"
      @click="onClickText(text)"
    />
    <section class="btn-custom" @click="onClickCustom">
      <span>I want to customise my suggested action</span>
      <i class="fa-light fa-sparkles" />
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import StorytellerActionPlansRequest from '@/services/request/StorytellerActionPlansRequest';

export default {
  name: 'storyteller-regenerate-action-modal-results',

  props: {
    textList: {
      type: Array,
      required: true,
    },
  },

  computed: {
    ...mapState('storytellerActionPlans', ['selectedAction']),
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    onClickText(text) {
      this.selectedAction.actionPoint = text;
      StorytellerActionPlansRequest.updateSuggestedAction();
      this.closeModal();
    },

    onClickCustom() {
      this.$emit('selectCustom');
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-regenerate-action-modal-results {
  width: 100%;

  .item-action {
    border-radius: $border-radius-medium;
    border: 1px solid rgba(115, 80, 255, 0.4);
    cursor: pointer;
    font-size: $font-size-sm;
    line-height: 15px;
    margin-top: 1rem;
    padding: 0.6rem 1rem;
    position: relative;
    width: 100%;
    word-break: break-word;

    &:hover, &:focus {
      border: 1px solid rgba(115, 80, 255, 1);
      background-color: rgba(115, 80, 255, 0.1);
    }
  }

  .btn-custom {
    @include flex("block", "row", "center", "center");

    margin-top: 1.4rem;
    width: 100%;

    span {
      cursor: pointer;
      margin-right: 0.2rem;
      text-decoration: underline;
    }

    &:hover {
      color: rgba(115, 80, 255, 1);
    }
  }
}
</style>
