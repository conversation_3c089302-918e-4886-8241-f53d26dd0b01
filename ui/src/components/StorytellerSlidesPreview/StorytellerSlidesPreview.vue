<template>
  <section class="storyteller-slides-preview" :style="style">
    <section class="header">
      <span class="header-text">Insight Storyteller Preview</span>
      <i class="fa-light fa-circle-xmark close-icon" @click="onClickClose" />
    </section>

    <section class="content" ref="content">
      <component :is="slideComponent"
        :slide-data="selectedSlide.slideData"
        :slide-width="slideWidth"
      />
    </section>

    <section v-if="!isFirstSlide" class="scroll scroll-left" @click="selectPrevSlide">
      <i class="fa-thin fa-circle-arrow-left arrow-icon" />
    </section>

    <section v-if="!isLastSlide" class="scroll scroll-right" @click="selectNextSlide">
      <i class="fa-thin fa-circle-arrow-right arrow-icon" />
    </section>

    <section class="controls">
      <storyteller-slides-download-btn :top-position="true" />
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import getSlideComponent from '@/helpers/storyteller-utils';
import StorytellerSlidesDownloadBtn from '@/components/StorytellerSlidesHeader/StorytellerSlidesDownloadBtn';

export default {
  name: 'storyteller-slides-preview',

  components: {
    StorytellerSlidesDownloadBtn,
  },

  data() {
    return {
      slideWidth: 0,
    };
  },

  computed: {
    ...mapGetters('storyteller', ['activeSlides']),

    ...mapState('storyteller', ['selectedSlide']),

    isFirstSlide() {
      return this.selectedSlide.id === this.activeSlides[0].id;
    },

    isLastSlide() {
      return this.selectedSlide.id === this.activeSlides[this.activeSlides.length - 1].id;
    },

    slideComponent() {
      return getSlideComponent(this.selectedSlide.slideType);
    },

    style() {
      return {
        '--slideWith': `${this.slideWidth}px`,
        '--slideHeight': `${this.slideWidth * 9 / 16}px`,
      };
    },
  },

  created() {
    const slideWrapperEle = document.getElementById('slide-wrapper');
    this.slideWidth = Math.min(slideWrapperEle.offsetWidth, 1200);
  },

  async mounted() {
    window.addEventListener('keydown', this.onKeydown);
  },

  beforeDestroy() {
    window.removeEventListener('keydown', this.onKeydown);
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('storyteller', ['selectNextSlide', 'selectPrevSlide']),

    changePage(amount) {
      this.page += amount;
    },

    onClickClose() {
      this.closeModal();
    },

    onKeydown(e) {
      if (e.key === 'ArrowRight') {
        this.selectNextSlide();
      } else if (e.key === 'ArrowLeft') {
        this.selectPrevSlide();
      } else if (e.key === 'Escape' || e.key === 'Esc') {
        this.closeModal();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slides-preview {
  display: grid;
  font-family: Inter, serif;
  grid-template-columns: 2.6rem var(--slideWith) 2.6rem;
  grid-template-rows: 2rem var(--slideHeight) 2rem;
  row-gap: 1rem;

  .header {
    @include flex("block", "row", "between", "center");

    align-self: end;
    color: clr('white');
    font-size: $font-size-base;
    font-weight: $font-weight-bold;
    grid-area: 1 / 2 / 2 / 3;

    .close-icon {
      color: #FFA4A4;
      cursor: pointer;
      font-size: $font-size-md;
      transition: opacity $interaction-transition-time;

      &:hover {
        opacity: 0.5;
      }
    }
  }

  .scroll {
    align-self: center;
    color: clr('white');

    &.scroll-left {
      grid-area: 2 / 1 / 3 / 2;
    }

    &.scroll-right {
      grid-area: 2 / 3 / 3 / 4;
      margin-left: 1.4rem;
    }

    .arrow-icon {
      cursor: pointer;
      font-size: 1.2rem;
      transition: opacity $interaction-transition-time;

      &:hover {
        opacity: 0.5;
      }
    }
  }

  .content {
    @include flex("block", "row", "stretch", "stretch");

    background-color: clr('white');
    border-radius: $border-radius-medium;
    grid-area: 2 / 2 / 3 / 3;
    overflow: hidden;
    height: 100%;
  }

  .controls {
    @include flex("block", "row", "end", "center");

    justify-self: end;
    grid-area: 3 / 2 / 4 / 3;
  }
}
</style>
