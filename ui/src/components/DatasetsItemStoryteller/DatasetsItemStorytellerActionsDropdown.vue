<template>
  <section class="datasets-item-storyteller-actions-dropdown" v-click-outside-handler="{ handler: 'onClickOutside' }">
<!--    <span class="item rename" @click.stop="onClickRename">Rename</span>-->
    <span class="item delete" @click.stop="onClickDelete">Delete</span>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import clickOutsideHandler from '@/directives/click-outside-handler';
import DeleteStorytellerReportModal from '@/components/DatasetsItemStoryteller/DeleteStorytellerReportModal';

export default {
  name: 'datasets-item-storyteller-actions-dropdown',

  props: {
    report: {
      type: Object,
      required: true,
    },
  },

  directives: {
    clickOutsideHandler,
  },

  methods: {
    ...mapActions('modal', ['closeModal', 'setModalComponentAndProps']),

    onClickRename() {
      this.closeModal();
      this.setModalComponentAndProps({
        component: DeleteStorytellerReportModal,
        props: {
          report: this.report,
        },
      });
    },

    onClickDelete() {
      this.setModalComponentAndProps({
        component: DeleteStorytellerReportModal,
        props: {
          datasetId: this.report.datasetId,
          report: this.report,
        },
      });
    },

    onClickOutside() {
      this.$emit('close');
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.datasets-item-storyteller-actions-dropdown {
  @include flex("block", "column", "start", "start");
  @include panel;

  right: 2rem;
  width: 100px;
  position: absolute;

  .item {
    color: #5f6771;
    padding: 0.3rem;
    width: 100%;

    &:first-child {
      border-top-left-radius: $border-radius-medium;
      border-top-right-radius: $border-radius-medium;
    }

    &:last-child {
      border-bottom-left-radius: $border-radius-medium;
      border-bottom-right-radius: $border-radius-medium;
    }

    &:hover, &:focus {
      background-color: #5f6771;
      color: #FFF;
    }

    &.delete {
      color: #e41a1a;

      &:hover, &:focus {
        background-color: #e41a1a;
        color: #FFF;
      }
    }
  }
}
</style>
