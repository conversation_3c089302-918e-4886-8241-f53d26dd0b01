<template>
  <section class="datasets-item-storyteller-new-report-modal" @keydown.enter="onClickDone">
    <section class="header">
      <i class="fa-light fa-file-lines icon-report"></i>
      <span class="text">Create New Report:&nbsp;</span>
      <span class="label">{{dataset.label}}</span>
      <adorescore-box-mini :bucket="bucket" :score="score" />
    </section>
    <section class="body">
      <storyteller-trial-badge />
      <section class="report-name">
        <span class="report-name-title">Report Name</span>
        <base-input v-model="reportName" />
        <span v-if="isDuplicateName" class="duplicate-name-error">Report name is duplicated.</span>
      </section>
      <section class="report-category">
        <span class="report-category-label">Select Report Category</span>
        <section class="report-category-type">
          <section class="report-type" :class="{selected: isReportCx}" @click="selectReportCx">
            <section class="radio"><i class="fa-solid fa-check icon-check" /></section>
            <span>Customer Experience (CX)</span>
          </section>
          <section class="report-type" :class="{selected: isReportEx}" @click="selectReportEx">
            <section class="radio"><i class="fa-solid fa-check icon-check" /></section>
            <span>Employee Experience (EX)</span>
          </section>
          <section class="report-type" :class="{selected: isReportOther}" @click="selectReportOther">
            <section class="radio"><i class="fa-solid fa-check icon-check" /></section>
            <span>Other</span>
          </section>
        </section>
      </section>
      <section class="report-other" v-if="isReportOther">
        <span class="report-other-label">Please Specify ‘Other’:</span>
        <base-input
            v-model="reportCategoryExtraInfo"
            :placeholder="'e.g. Patient Experience'"
            :class="{ 'input-error': isOtherFieldError }"
        />
        <span v-if="isOtherFieldError" class="validation-error">This field is required.</span>

      </section>
      <section class="report-preview">
        <section class="report-preview-info">
            <i class="fa-solid fa-eye icon-eye" />
            <span class="title">Report Preview</span>
            <storyteller-report-tag />
          </section>
        <img :src="require('@/assets/storyteller/storyteller-image.svg')" class="storyteller-image" alt="storyteller-image"/>
      </section>
    </section>
    <section class="footer">
      <base-button class="cancel-btn" colour="light" size="small" type="link" @click="onClickCancel">Cancel</base-button>
      <loading-blocks-overlay v-if="creating" size="small" />
      <base-button v-else class="done-btn" size="small" @click="onClickDone" :disabled="disable">
        <i class="fa-solid fa-check icon-check"></i>
        <span>Done</span>
      </base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';
import { some } from 'lodash-es';

import AdorescoreBoxMini from '@/components/AdorescoreBox/AdorescoreBoxMini';
import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import Route from '@/enum/route';
import StorytellerReportTag from '@/components/Storyteller/StorytellerReportTag';
import StorytellerReportType from '@/enum/storyteller-report-type';
import StorytellerRequest from '@/services/request/StorytellerRequest';
import StorytellerTrialBadge from '@/components/Storyteller/StorytellerTrialBadge';
import ThemesRequest from '@/services/request/ThemesRequest';

export default {
  name: 'datasets-item-storyteller-new-report-modal',

  components: {
    AdorescoreBoxMini,
    BaseButton,
    BaseInput,
    LoadingBlocksOverlay,
    StorytellerReportTag,
    StorytellerTrialBadge,
  },

  props: {
    componentProps: {
      datasetId: {
        type: Object,
        required: true,
      },
    },
  },

  data() {
    return {
      creating: false,
      isDuplicateName: false,
      isOtherFieldError: false, // New data property for validation
      reportCategoryExtraInfo: '',
      reportName: '',
      selectedType: 'customer_experience', // customer_experience, employee_experience or other
    };
  },

  computed: {
    ...mapGetters('benchmark', ['classifyAdorescore']),

    ...mapGetters('datasets', {
      getDataset: 'get',
    }),

    ...mapState('datasets', ['selected']),

    ...mapState('storyteller', ['reports', 'activeReport']),

    bucket() {
      return this.classifyAdorescore(this.score);
    },

    dataset() {
      return this.getDataset(this.componentProps.datasetId);
    },

    disable() {
      return this.isDuplicateName;
    },

    isReportCx() {
      return this.selectedType === StorytellerReportType.CX.value();
    },

    isReportEx() {
      return this.selectedType === StorytellerReportType.EX.value();
    },

    isReportOther() {
      return this.selectedType === StorytellerReportType.OTHER.value();
    },

    score() {
      return this.dataset.adoreScore;
    },
  },

  mounted() {
    this.reportName = this.generateUniqueReportName();
  },

  watch: {
    reportName(newName) {
      this.isDuplicateName = this.reports.some(report => report.settings.reportName === newName.trim());
    },
  },

  methods: {
    ...mapActions('datasets', ['select', 'setActive']),

    ...mapActions('modal', ['closeModal']),

    ...mapActions('storyteller', ['setActiveReport']),

    onClickCancel() {
      this.closeModal();
    },

    onClickDone() {
      if (this.creating) return;

      // Validation logic for "Other" field
      if (this.isReportOther && !this.reportCategoryExtraInfo) {
        this.isOtherFieldError = true;
        return;
      }
      this.isOtherFieldError = false;

      this.creating = true;
      StorytellerRequest.generateReport(this.dataset.id, this.reportName, this.selectedType, this.reportCategoryExtraInfo);
      this.select({ ids: [this.dataset.id] });
      this.setActive({ id: this.dataset.id });
      ThemesRequest.fetchAndSetThemes();
      this.$router.push({
        name: Route.STORYTELLER,
        query: {
          ids: this.dataset.id,
        },
      }).catch((e) => {
        if (e?.name !== 'NavigationDuplicated') {
          throw e;
        }
      });
      this.creating = false;
    },

    generateUniqueReportName() {
      const baseName = `${this.dataset.label} Storyteller`;
      let newName = baseName;
      let counter = 1;

      while (some(this.reports, { settings: { reportName: newName } })) {
        newName = `${baseName} (${counter})`;
        counter += 1;
      }

      return newName;
    },

    selectReportCx() {
      this.selectedType = StorytellerReportType.CX.value();
    },

    selectReportEx() {
      this.selectedType = StorytellerReportType.EX.value();
    },

    selectReportOther() {
      this.selectedType = StorytellerReportType.OTHER.value();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

$padding: 1.5rem 1.5rem;

.datasets-item-storyteller-new-report-modal {
  @include panel;

  color: $nps-blue;
  font-family: 'Inter', serif;
  position: relative;
  width: 480px;

  .header {
    @include flex("block", "row", "start", "center");

    padding: $padding;
    font-size: $font-size-sm;
    border-bottom: 1px solid rgba(19, 28, 41, 0.2);

    .icon-report {
      margin-right: 0.3rem;
    }

    .text {
      font-weight: $font-weight-bold;
      white-space: nowrap;
    }

    .label {
      @include truncate;
    }

    .adorescore-box-mini {
      margin-left: 0.4rem;
      width: 2rem;

      .score {
        font-size: 12px;
      }
    }
  }

  .body {
    font-size: $font-size-xs;
    padding: $padding;
    width: 100%;

    .report-name {
      .report-name-title {
        font-weight: $font-weight-bold;
      }

      .base-input {
        margin-top: 0.5rem;
      }

      .duplicate-name-error {
        color: red;
        font-size: 0.8em;
        margin-top: 0.5em;
      }
    }

    .report-category {
      margin-top: 1.5rem;
      width: 100%;

      .report-category-label {
        font-weight: $font-weight-bold;
      }

      .report-category-type {
        display: grid;
        font-weight: $font-weight-bold;
        gap: 0.5rem;
        grid-template-columns: repeat(3, 1fr);
        margin-top: 0.5rem;

        .report-type {
          @include flex("block", "row", "start", "center");

          border-radius: $border-radius-medium;
          border: 1px solid #D9D9D9;
          cursor: pointer;
          height: 44px;
          padding: 0.5rem 0 0.5rem 0.5rem;

          .radio {
            @include flex("block", "row", "center", "center");

            border-radius: 50%;
            border: 1px solid #D9D9D9;
            height: 1rem;
            margin-right: 0.4rem;
            min-width: 1rem;
            width: 1rem;

            .icon-check {
              font-size: 8px;
              visibility: hidden;
            }
          }

          &:hover {
            border-color: rgba(19, 28,49, 0.8);
          }

          &.selected {
            background-color: #3981F7;
            border-color: #1A5AC3;
            color: #FFF;

            .radio {
              background-color: #FFF;
              border-color: #FFF;
              color: #3981F7;

              .icon-check {
                visibility: visible;
              }
            }
          }
        }
      }
    }

    .report-other {
      margin-top: 1rem;
      width: 100%;

      .base-input {
        margin-top: 0.5rem;

        &::placeholder {
          color: #BDBDBD;
        }

        &.input-error {
          border: 1px solid red;
        }
      }

      .validation-error {
        color: red;
        font-size: 0.8em;
        font-weight: $font-weight-bold;
        margin-top: 0.5em;
      }
    }

    .report-preview {
      background-color: #F2F4F8;
      border-radius: 3px;
      margin-top: 1.5rem;
      padding: $padding;
      width: 100%;

      .report-preview-info {
        @include flex("block", "row", "start", "center");

        margin-bottom: 0.6rem;

        .icon-eye {
          margin-right: 0.2rem;
        }

        .title {
          font-weight: $font-weight-bold;
          margin-right: 0.6rem;
        }
      }

      .storyteller-image {
        width: 100%;
      }
    }

    .storyteller-trial-badge {
      margin-bottom: 1rem;
      font-size: $font-size-xxs;
    }
  }

  .footer {
    @include flex("block", "row", "space-between", "center");

    border-top: 1px solid rgba(19, 28, 41, 0.2);
    padding: $padding;
    width: 100%;

    .base-button {
      font-size: $font-size-xxs;
      font-weight: 600;
      text-transform: uppercase;

      &.cancel-btn {
        padding-left: 0;
        font-weight: $font-weight-bold;
      }

      &.done-btn {
        background-color: rgba(19, 28, 41, 0.8);
        padding: 0.5rem 0.8rem;
        font-weight: $font-weight-bold;

        .icon-check {
          margin-right: 0.2rem;
        }
      }

      &:hover, &:focus {
        &.done-btn {
          background-color: rgba(19, 28, 41);
        }
      }
    }
  }
}
</style>
