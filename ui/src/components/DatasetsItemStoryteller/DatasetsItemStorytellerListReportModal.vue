<template>
  <section class="datasets-item-storyteller-list-report-modal">
    <section class="header">
      <i class="fa-light fa-file-lines icon-report"></i>
      <span class="text">Reports:</span>
      <section class="dataset-info">
        <adorescore-box-mini class="score" :bucket="bucket" :score="dataset.adoreScore"/>
        <span class="label">{{dataset.label}}</span>
      </section>
    </section>
    <datasets-item-storyteller-list-report-modal-body :dataset="dataset" />
    <section class="footer">
      <base-button class="cancel-btn" colour="light" size="small" type="link" @click="onClickCancel">Cancel</base-button>
<!--      <base-button class="done-btn" size="small" @click="onClickDone">-->
<!--        <i class="fa-solid fa-check icon-check"></i>-->
<!--        <span>Done</span>-->
<!--      </base-button>-->
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';

import AdorescoreBoxMini from '@/components/AdorescoreBox/AdorescoreBoxMini';
import BaseButton from '@/components/Base/BaseButton';
import DatasetsItemStorytellerListReportModalBody from '@/components/DatasetsItemStoryteller/DatasetsItemStorytellerListReportModalBody';
import StorytellerRequest from '@/services/request/StorytellerRequest';

export default {
  name: 'datasets-item-storyteller-list-report-modal',

  components: {
    AdorescoreBoxMini,
    BaseButton,
    DatasetsItemStorytellerListReportModalBody,
  },

  props: {
    componentProps: {
      datasetId: {
        type: Number,
        required: true,
      },
    },
  },

  computed: {
    ...mapGetters('benchmark', ['classifyAdorescore']),

    ...mapGetters('datasets', {
      getDataset: 'get',
    }),

    bucket() {
      return this.classifyAdorescore(this.dataset.adorescore);
    },

    dataset() {
      return this.getDataset(this.componentProps.datasetId);
    },
  },

  async created() {
    await StorytellerRequest.getReports(this.dataset.id);
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    onClickCancel() {
      this.closeModal();
    },

    onClickDone() {
      // TODO: ask Aaron about the meaning of this button
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

$padding: 1.5rem;

.datasets-item-storyteller-list-report-modal {
  @include panel;

  color: $nps-blue;
  position: relative;
  width: 700px;

  .header {
    @include flex("block", "row", "start", "center");
    @include truncate;

    border-bottom: 1px solid rgba(19, 28, 41, 0.2);
    font-size: $font-size-sm;
    font-weight: $font-weight-bold;
    padding: $padding;
    width: 100%;

    .icon-report {
      margin-right: 0.3rem;
    }

    .dataset-info {
      @include flex("block", "row", "start", "center");
      @include truncate;

      margin-left: 0.6rem;

      .label {
        @include truncate;

        margin-left: 0.6rem;
      }
    }
  }

  .footer {
    @include flex("block", "row", "space-between", "center");

    border-top: 1px solid rgba(19, 28, 41, 0.2);
    padding: $padding;
    width: 100%;

    .base-button {
      font-size: $font-size-xxs;
      font-weight: 600;
      text-transform: uppercase;

      &.cancel-btn {
        padding-left: 0;
      }

      &.done-btn {
        background-color: rgba(19, 28, 41, 0.8);
        padding: 0.5rem 0.8rem;

        .icon-check {
          margin-right: 0.2rem;
        }
      }

      &:hover, &:focus {
        &.done-btn {
          background-color: rgba(19, 28, 41);
        }
      }
    }
  }
}
</style>
