<template>
  <section class="datasets-item-storyteller-btn">
    <base-button v-if="dataset.reportCount > 0" class="btn btn-list" size="small" @click="onClickListBtn">
      <i class="fa-light fa-file-lines icon-file" />
      <span>{{dataset.reportCount}}</span>
    </base-button>
    <base-button v-else-if="isEditor" class="btn btn-new" size="small" @click="onClickNewBtn">
      <i class="fa-regular fa-plus icon-plus"></i>
      <span>New</span>
    </base-button>
  </section>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import DatasetsItemStorytellerListReportModal from '@/components/DatasetsItemStoryteller/DatasetsItemStorytellerListReportModal';
import DatasetsItemStorytellerNewReportModal from '@/components/DatasetsItemStoryteller/DatasetsItemStorytellerNewReportModal';

export default {
  name: 'datasets-item-storyteller-btn',

  components: {
    BaseButton,
    DatasetsItemStorytellerNewReportModal,
  },

  props: {
    id: {
      type: Number,
      required: true,
    },
  },

  computed: {
    ...mapGetters('datasets', {
      getDataset: 'get',
      isEditable: 'isEditable',
    }),

    dataset() {
      return this.getDataset(this.id);
    },

    isEditor() {
      return this.isEditable(this.id);
    },
  },

  methods: {
    ...mapActions('modal', ['setModalComponentAndProps']),

    onClickListBtn() {
      this.setModalComponentAndProps({
        component: DatasetsItemStorytellerListReportModal,
        props: {
          datasetId: this.id,
        },
      });
    },

    onClickNewBtn() {
      this.setModalComponentAndProps({
        component: DatasetsItemStorytellerNewReportModal,
        props: {
          datasetId: this.id,
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.datasets-item-storyteller-btn {
  @include flex("block", "row", "space-between", "center");

  width: 100%;

  .btn {
    font-size: $font-size-xxs;
    font-weight: $font-weight-bold;
    height: 1.5rem;
    padding: 0.3rem;
    width: 44px;

    &.btn-list {
      background-color: #FFF;
      border: 1px solid rgba(57, 129, 247, 0.6);
      color: #1C56B4;

      .icon-file {
        margin-right: 0.3rem;
      }

      &:hover, &:focus {
        background-color: rgba(57, 129, 247, 0.05);
        border: 1px solid rgba(57, 129, 247, 1);
      }
    }

    &.btn-new {
      background-color: #3981F7;
      border: 1px solid #2269DC;
      color: #FFF;

      .icon-plus {
        margin-right: 0.2rem;
      }

      &:hover, &:focus {
        background-color: rgba(57, 129, 247, 0.7);
      }
    }
  }
}
</style>
