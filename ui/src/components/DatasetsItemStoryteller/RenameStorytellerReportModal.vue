<template>
  <section class="rename-storyteller-report-modal">
    <section class="header">
      <h2>Delete Report</h2>
    </section>
    <section class="body">
      <span>The following report will be deleted:</span>

      <section class="report-name">
        {{report.settings.reportName}}
      </section>

      <span>Deleting a report cannot be undone. Are you sure you want to proceed?</span>
    </section>
    <section class="footer">
      <base-button class="cancel" colour="light" type="link" @click="onCancel">Cancel</base-button>
      <loading-blocks-overlay v-if="deleting" size="small" />
      <base-button v-else class="delete" colour="danger" @click="onDelete">
        <span>Delete</span>
      </base-button>
    </section>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import StorytellerRequest from '@/services/request/StorytellerRequest';
import DatasetsRequestV0 from '@/services/request/DatasetsRequestV0';

export default {
  name: 'delete-storyteller-report-modal',

  components: {
    LoadingBlocksOverlay,
    BaseButton,
  },

  props: {
    componentProps: {
      datasetId: {
        type: Number,
        required: true,
      },
      report: {
        type: Object,
        required: true,
      },
    },
  },

  data() {
    return {
      deleting: false,
    };
  },

  computed: {
    report() {
      return this.componentProps.report;
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    onCancel() {
      this.closeModal();
    },

    async onDelete() {
      this.deleting = true;
      await StorytellerRequest.deleteReport(this.componentProps.datasetId, this.componentProps.report.id);
      await DatasetsRequestV0.getDatasets();
      this.deleting = false;
      this.closeModal();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.delete-storyteller-report-modal {
  @include modal;

  .body {
    @include flex("block", "column", "start", "stretch");

    span {
      font-size: $font-size-sm;
      margin: 0.5rem 0;
    }

    .report-name {
      font-weight: $font-weight-bold;
    }
  }
}
</style>
