<template>
  <section class="datasets-item-storyteller-list-report-modal-body">
    <section class="body-content body-header">
      <section>Report Name</section>
      <section />
      <section class="owner">Owner</section>
      <section />
    </section>
    <section v-if="loading" class="loading">
      <loading-blocks-overlay>Loading report...</loading-blocks-overlay>
    </section>
    <section v-else class="body-content-item-wrapper">
      <section class="body-content body-item" v-for="(report, index) in reports" :key="index" @click="onClickReport(report)">
        <section class="report-name">{{report.settings.reportName}}</section>
        <storyteller-report-tag />
        <section class="owner">{{report.user.firstName}}</section>
        <section class="actions" v-if="isEditor">
          <i class="fa-solid fa-ellipsis icon-actions" @click.stop="toggleDropdown(report.id)" />
        </section>
        <datasets-item-storyteller-actions-dropdown :report="report" v-if="isOpenDropdown(report.id)" @close="closeDropdown(report.id)" />
      </section>
    </section>
    <base-button v-if="!loading && isEditor" class="new-report-btn" size="small" @click="onClickNewReport">
      <i class="fa-regular fa-plus icon-plus"></i>
      New Report
    </base-button>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import DatasetsItemStorytellerActionsDropdown from '@/components/DatasetsItemStoryteller/DatasetsItemStorytellerActionsDropdown';
import DatasetsItemStorytellerNewReportModal from '@/components/DatasetsItemStoryteller/DatasetsItemStorytellerNewReportModal';
import Route from '@/enum/route';
import StorytellerReportTag from '@/components/Storyteller/StorytellerReportTag';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import NetworkStatus from '@/enum/network-status';
import NetworkKeys from '@/enum/network-keys';

export default {
  name: 'datasets-item-storyteller-list-report-modal-body',

  components: {
    LoadingBlocksOverlay,
    BaseButton,
    DatasetsItemStorytellerActionsDropdown,
    StorytellerReportTag,
  },

  props: {
    dataset: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      openDropdown: [],
    };
  },

  computed: {
    ...mapGetters('datasets', ['isEditable']),

    ...mapGetters('network', ['status']),

    ...mapState('datasets', ['selected']),

    ...mapState('storyteller', ['reports']),

    isEditor() {
      return this.isEditable(this.dataset.id);
    },

    loading() {
      return this.reportsStatus === NetworkStatus.LOADING;
    },

    reportsStatus() {
      return this.status(NetworkKeys.STORYTELLER_GET_REPORTS);
    },
  },

  methods: {
    ...mapActions('datasets', ['select', 'setActive']),

    ...mapActions('modal', ['closeModal', 'setModalComponentAndProps']),

    ...mapActions('storyteller', ['setActiveReport']),

    closeDropdown(reportId) {
      const indexOfId = this.openDropdown.indexOf(reportId);
      if (indexOfId !== -1) {
        this.openDropdown.splice(indexOfId, 1);
      }
    },

    isOpenDropdown(reportId) {
      return this.openDropdown.includes(reportId);
    },

    onClickNewReport() {
      this.closeModal();
      this.setModalComponentAndProps({
        component: DatasetsItemStorytellerNewReportModal,
        props: {
          datasetId: this.dataset.id,
        },
      });
    },

    onClickReport(report) {
      this.setActiveReport({ report });
      this.select({ ids: [this.dataset.id] });
      this.setActive({ id: this.dataset.id });
      this.$router.push({
        name: Route.STORYTELLER,
        query: {
          ids: this.dataset.id,
          reportId: report.id,
        },
      }).catch((e) => {
        if (e?.name !== 'NavigationDuplicated') {
          throw e;
        }
      });
    },

    toggleDropdown(reportId) {
      const indexOfId = this.openDropdown.indexOf(reportId);
      if (indexOfId !== -1) {
        this.openDropdown.splice(indexOfId, 1);
      } else {
        this.openDropdown.push(reportId);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.datasets-item-storyteller-list-report-modal-body {
  padding: 1.5rem 1rem;
  width: 100%;

  .body-content {
    align-items: center;
    display: grid;
    grid-template-columns: 50% 20% 20% 10%;

    .owner {
      justify-self: center;
    }

    .actions {
      justify-self: end;

      .icon-actions {
        border-radius: 3px;
        border: 1px solid transparent;
        padding: 0 5px;

        &:hover {
          border: 1px solid rgba(191, 191, 191, 0.8);
        }
      }
    }

    &.body-header {
      color: rgba(31, 39, 52, 0.5);
      font-size: $font-size-xxs;
      font-weight: $font-weight-extra-bold;
      letter-spacing: 0.3px;
      text-transform: uppercase;
      padding-left: 0.5rem;
      padding-right: 0.5rem;
      padding-bottom: 0.5rem;
    }

    &.body-item {
      border-bottom: 1px solid rgba(19, 28, 41, 0.2);
      border-radius: 3px;
      cursor: pointer;
      font-size: $font-size-xs;
      overflow: visible;
      padding: 1rem 0.5rem;
      position: relative;

      &:hover, &:focus {
        background-color: #E6EAFF;
      }

      .report-name {
        @include truncate;

        font-weight: $font-weight-bold;
      }

      &:last-child {
        border-bottom: none;
      }
    }
  }

  .body-content-item-wrapper {
    @include scrollbar-thin;

    max-height: 300px;
    overflow-y: auto;
    width: 100%;
    padding-bottom: 1rem;
  }

  .new-report-btn {
    background-color: #3981F7;
    border: 1px solid #2269DC;
    font-size: $font-size-xxs;
    font-weight: $font-weight-bold;
    height: 24px;
    margin-left: 0.5rem;
    margin-right: 0.5rem;
    text-transform: uppercase;

    .icon-plus {
      margin-right: 0.2rem;
    }

    &:hover, &:focus {
      background-color: rgba(57, 129, 247, 0.7);
    }
  }

  .loading {
    @include flex("block", "row", "center", "center");
    @include stretch;

    height: 100%;
  }
}
</style>
