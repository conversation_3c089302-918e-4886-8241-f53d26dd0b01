<template>
  <section class="storyteller-report-created-toast">
    <section class="toast">
      <section class="left">
        <i class="fa-regular fa-plus icon"></i>
        <span class="text">Report {{reportName}} was created.</span>
      </section>
      <section class="right">
        <base-button class="dismiss" colour="light" @click="onDismiss">Dismiss</base-button>
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import Toast from '@/components/Mixins/Toast';

export default {
  name: 'storyteller-report-created-toast',

  components: {
    BaseButton,
  },

  mixins: [Toast],

  beforeDestroy() {
    this.remove({ id: 'storyteller-report-created-toast' });
  },

  computed: {
    ...mapState('toast', ['toastData']),

    reportName() {
      return this.toastData?.activeReport.settings.reportName;
    },
  },

  created() {
    this.countWatcher = setInterval(() => {
      this.count -= 1;
      if (this.count === 0) {
        clearInterval(this.countWatcher);
        this.close();
      }
    }, 1000);
  },

  data() {
    return {
      count: 3,
      countWatcher: null,
    };
  },

  methods: {
    ...mapActions('toast', ['remove']),

    onDismiss() {
      this.remove({ id: 'surveys-deleted-success' });
      this.close();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../../../styles/variables/index";
@import "../../../styles/mixins/index";

.storyteller-report-created-toast {
  @include flex("block", "row", "end", "start");

  .toast {
    @include toast;

    border-radius: $border-radius-medium;
    padding: 1rem 1.5rem;

    .left {
      @include flex("block", "row", "start", "center");

      margin-right: 4rem;

      span {
        font-weight: $font-weight-normal;
        margin-left: 0.3rem;
      }
    }

    .right {
      @include flex("block", "row", "end", "center");

      .base-button {
        padding: 0.6rem 1.6rem;
        margin-left: 1rem;
      }
    }
  }
}
</style>
