<template>
  <section class="bubble-chart-panel">
    <bubble-chart-panel-header />

    <loading-switch :status="themesStatus">
      <template #default>
        <bubble-chart bcId="bubble-chart" />
      </template>
      <template #loading>
        <loading-blocks-overlay>Loading Themes</loading-blocks-overlay>
      </template>
    </loading-switch>
  </section>
</template>

<script>
import { mapGetters } from 'vuex';

import BubbleChartPanelHeader from '@/components/BubbleChartPanel/BubbleChartPanelHeader';
import BubbleChart from '@/components/BubbleChart/BubbleChart';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import LoadingSwitch from '@/components/LoadingSwitch';
import NetworkKeys from '@/enum/network-keys';

export default {
  name: 'bubble-chart-panel',

  components: {
    Bubble<PERSON>hartPanelHeader,
    Bubble<PERSON>hart,
    LoadingBlocksOverlay,
    LoadingSwitch,
  },

  computed: {
    ...mapGetters('network', ['status']),

    themesStatus() {
      return this.status(NetworkKeys.THEMES);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.bubble-chart-panel {
  @include flex("block", "column", "start", "stretch");
  @include stretch;

  .loading-blocks-overlay {
    height: 100%;
  }
}
</style>
