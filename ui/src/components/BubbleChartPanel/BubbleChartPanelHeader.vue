<template>
  <section class="bubble-chart-panel-header">
    <section class="title">
      <h3>Theme Analysis</h3>
      <p>Select a theme bubble in the chart to view relevant themes and comments below</p>
    </section>

    <section class="configuration" @click.stop>
      <base-button
          v-if="!customFlag"
          @click="onAddingToggle"
          icon="plus"
          size="small"
          type="outline"
      >Add plots to custom chart</base-button>

      <base-button
          v-else
          @click="onAddingToggle"
          colour="success"
          size="small"
      >Finished adding</base-button>

      <base-dropdown :data="items" :open="open" @close="open = false" @select="onSelect">
        <base-dropdown-button :active="open" @click="open = !open">{{ yPath.titleCase() }}</base-dropdown-button>
      </base-dropdown>
      <section class="options"></section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseDropdown from '@/components/Dropdown/BaseDropdown';
import BaseDropdownButton from '@/components/Dropdown/BaseDropdownButton';
import CustomChartType from '@/enum/custom-chart-type';
import ThemesChartPath from '@/enum/themes-chart-path';
import { datasetsRequestV0 } from '@/services/request';

export default {
  name: 'bubble-chart-panel-header',

  components: {
    BaseButton,
    BaseDropdown,
    BaseDropdownButton,
  },

  data() {
    return {
      open: false,
    };
  },

  computed: {
    ...mapState('datasets', ['active']),

    ...mapGetters('datasets', ['get']),

    ...mapState('themesChart', ['customFlag', 'yPath']),

    isSample() {
      return this.get(this.active).localSample;
    },

    items() {
      return ThemesChartPath.enumValues.map(e => {
        return {
          value: e,
          content: e.titleCase(),
        };
      });
    },
  },

  methods: {
    ...mapActions('themesChart', ['setCustomFlag', 'setYPath']),

    async onAddingToggle() {
      if (this.customFlag && !this.isSample) {
        await datasetsRequestV0.persistCustomChart(CustomChartType.THEME_ANALYSIS);
      }
      this.setCustomFlag({ customFlag: !this.customFlag });
    },

    onSelect(item) {
      this.setYPath({ path: item.value });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.bubble-chart-panel-header {
  @include flex("block", "row", "between", "stretch");

  padding: 1rem 1rem 0.5rem;

  .add-bubble-to-custom {
    margin-right: 0.5rem;
  }

  .title {
    @include panel-header-title;
  }

  .configuration {
    @include flex("block", "row");

    .base-button {
      margin-right: 1em;
    }
  }
}
</style>
