<template>
  <section class="themes-swot-dropdown"
           v-click-outside-handler="{
             handler: 'onClickOutside',
             excludedParentClasses: [
                 'item',
                 'item-status',
                 'list',
                 'list-item',
                 'themes-swot-dropdown-item',
             ],
           }"
  >
    <base-dropdown :close-on-select="false"
                   :component="ThemesSwotDropdownItem"
                   :data="dataList"
                   :open="open"
                   @select="onSelectItem">
      <section class="dropdown-label" @click.stop="open = !open">
        <span class="label">Swot Score</span>
        <i class="fa fa-caret-down icon" :class="{ open }"></i>
      </section>
    </base-dropdown>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import BaseDropdown from '@/components/Dropdown/BaseDropdown';
import clickOutsideHandler from '@/directives/click-outside-handler';
import Swot from '@/enum/swot';
import ThemesSwotDropdownItem from '@/components/ThemesControls/ThemesSwotDropdownItem';

export default {
  name: 'themes-swot-dropdown',

  components: {
    BaseDropdown,
  },

  directives: {
    clickOutsideHandler,
  },

  computed: {
    ...mapState('themes', ['swotFilterList']),

    dataList() {
      const rs = [{ header: true, content: 'Display SWOT' }];
      rs.push(...Swot.enumValues
        .filter(s => s !== Swot.NONE)
        .map(s => {
          return {
            content: s.name,
            selected: this.swotFilterList.includes(s),
            value: s,
          };
        }));
      return rs;
    },
  },

  data() {
    return {
      open: false,
      ThemesSwotDropdownItem,
    };
  },

  methods: {
    ...mapActions('themes', ['toggleSwotFilter']),

    onClickOutside() {
      this.open = false;
    },

    onSelectItem(i) {
      if (!i.header) {
        this.toggleSwotFilter({ value: i.value });
      }
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-swot-dropdown {
  .dropdown-label {
    @include flex("block", "row", "start", "center");
    border: 1px solid $border-color;
    border-radius: $border-radius-small;
    cursor: pointer;
    padding: 0.2rem 0.4rem;

    .label {
      font-size: $font-size-xxs;
      font-weight: $font-weight-bold;
    }

    .icon {
      color: clr("black");
      font-size: 10px;
      margin-left: 0.3rem;
      transition: all $interaction-transition-time;

      &.open{
        transform: rotate(180deg);
      }
    }
  }
}
</style>
