<template>
  <section class="themes-controls">

    <!-- left section -->
    <section class="left-section" :class="{ 'on-comment-page': onCommentPage, 'show-two-themes-column': themePanelLeft2Cols, timeSeries: isTimeSeries }" >
      <section class="selection" @click="toggle" v-if="!isTimeSeries && isEditor">
        <base-checkbox-solid :value="active" />
        <section class="selection-label">Select All</section>
      </section>
      <span class="slash-icon">/</span>
      <themes-selected-count />

      <themes-search v-show="showThemesSearch" />

      <section v-if="isThemeSwot" class="swot-toggle" @click="onClickToggleSwot">
        <span class="label">Swot ({{ numbOfSwots }})</span>
        <base-toggle-button :value="swotFilterShow"></base-toggle-button>
      </section>
    </section>

    <!-- right section -->
    <section class="right-section">
      <section v-if="showSwotScoreDropdown" class="swot-score-dropdown">
        <themes-swot-dropdown />
      </section>

      <section v-else class="sort-items">
        <section class="label" v-if="!isThemeSwot && !themePanelShowCommentList">SORT BY: </section>

        <common-sort-item v-for="header in sortList"
          :key="header.name"
          class="sort-item"
          :class="header.name"
          :current="currentSortItem"
          :font-size-xs="true"
          :header="header"
          @sort-event="sortEvent"
        />
      </section>
    </section>
  </section>
</template>

<script>
import { ChevronRightIcon } from 'vue-feather-icons';
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseCheckboxSolid from '@/components/Base/BaseCheckboxSolid';
import BaseToggleButton from '@/components/Base/BaseToggleButton';
import CommonSortItem from '@/components/CommonComponent/CommonSortItem';
import ResultsTabs from '@/enum/results-tabs';
import Route from '@/enum/route';
import router from '@/router';
import SortDirection from '@/enum/sort-direction';
import ThemesSearch from '@/components/ThemesSearch/ThemesSearch';
import ThemesSelectedCount from '@/components/NewThemePanel/ThemesSelectedCount';
import ThemesSort from '@/enum/themes-sort';
import ThemesSwotDropdown from '@/components/ThemesControls/ThemesSwotDropdown';
import ThemesType from '@/enum/themes-type';

export default {
  name: 'themes-controls',

  components: {
    BaseCheckboxSolid,
    BaseToggleButton,
    ChevronRightIcon,
    CommonSortItem,
    ThemesSearch,
    ThemesSelectedCount,
    ThemesSwotDropdown,
  },

  computed: {
    ...mapState('datasets', {
      activeDataset: state => state.active,
    }),

    ...mapGetters('datasets', ['isEditable']),

    ...mapGetters('themes', ['sortThemes']),

    ...mapState('themes', [
      'selectedTab',
      'selectedThemes',
      'sort',
      'sortDirection',
      'sortTypeAvailable',
      'swotFilterList',
      'swotFilterShow',
      'themePanelLeft2Cols',
      'themePanelShowCommentList',
      'themes',
      'type',
    ]),

    active() {
      return this.selectedThemes.length === this.sortedThemes.length;
    },

    currentSortItem() {
      return {
        sort: this.sort,
        direction: this.sortDirection,
      };
    },

    isEditor() {
      return this.isEditable(this.activeDataset);
    },

    isThemeAnalysis() {
      return this.$route.name === Route.THEME_ANALYSIS;
    },

    isThemeSwot() {
      return this.$route.name === Route.SWOT_ANALYSIS;
    },

    isTimeSeries() {
      return this.type === ThemesType.TIME_SERIES;
    },

    numbOfSwots() {
      return this.swotFilterList?.length || 0;
    },

    onCommentPage() {
      return router.currentRoute.name === Route.COMMENTS;
    },

    showSwotScoreDropdown() {
      return this.isThemeSwot && this.swotFilterShow;
    },

    showThemesSearch() {
      return (!this.themePanelShowCommentList || this.themePanelLeft2Cols) && this.onCommentPage;
    },

    sortedThemes() {
      return this.sortThemes(this.sort);
    },

    sortList() {
      if (this.selectedTab === ResultsTabs.TREND_ANALYSIS) {
        return this.sortTypeAvailable.filter(t => t !== ThemesSort.ADORESCORE)
          .sort((a, b) => b.displayOrder() - a.displayOrder());
      }

      return this.sortTypeAvailable
        .map(t => t)
        .sort((a, b) => b.displayOrder() - a.displayOrder());
    },
  },

  data() {
    return {
      ThemesSort,
      ThemesType,
    };
  },

  created() {
    if (this.isThemeSwot) {
      this.setSort({ by: ThemesSort.VOLUME });
      this.setSortDirection({ direction: SortDirection.DESC });
    }
  },

  methods: {
    ...mapActions('themes', [
      'addCustomThemes',
      'addSubtopics',
      'deselectThemes',
      'removeCustomThemes',
      'resetSwotFilterList',
      'selectThemes',
      'setSort',
      'setSortDirection',
      'setSwotFilterShow',
      'toggleExpandedTheme',
    ]),

    onClickToggleSwot() {
      this.setSwotFilterShow({ value: !this.swotFilterShow });
    },

    sortEvent(item) {
      this.setSort({ by: item.sort });
      this.setSortDirection({ direction: item.direction });
    },

    toggle() {
      if (this.isTimeSeries) return;

      if (this.active) {
        if (this.isThemeSwot || this.isThemeAnalysis) this.removeCustomThemes({ themes: this.themes });

        this.deselectThemes({ ids: this.sortedThemes.map(t => t.id) });
      } else {
        if (this.isThemeSwot || this.isThemeAnalysis) this.addCustomThemes({ themes: this.themes });

        this.selectThemes({ ids: this.sortedThemes.map(t => t.id) });
      }
    },
  },

  watch: {
    isThemeSwot() {
      if (this.isThemeSwot) {
        this.resetSwotFilterList();
      } else {
        this.setSwotFilterShow({ value: false });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/mixins";
@import "src/styles/variables";

.themes-controls {
  @include flex("block", "row", "start", "center");

  border-bottom: $border-standard;
  color: $body-copy;
  font-size: $font-size-xs;
  font-weight: $font-weight-medium;
  grid-area: themes-controls;
  height: 50px;
  padding-right: 1rem;
  text-transform: uppercase;
  width: 100%;

  .left-section {
    @include flex("block", "row", "start", "center");

    padding-left: 0.6rem;

    &.timeSeries {
      padding-left: 0.75rem;
    }

    .slash-icon {
      opacity: 0.1;
      margin-left: 0.5rem;
      margin-right: 0.5rem;
    }

    .selection {
      @include flex("block", "row", "start", "center");

      cursor: pointer;
      font-size: $font-size-xs;
      margin-left: 0.5rem;
      text-transform: uppercase;

      .selection-label {
        font-weight: $font-weight-bold;
        margin-left: 0.5rem;
      }
    }

    .swot-toggle {
      @include flex("block", "row", "start", "center");
      cursor: pointer;
      margin-left: 1rem;

      .label {
        font-size: $font-size-xs;
        font-weight: $font-weight-bold;
        text-transform: uppercase;
      }
    }
  }

  .right-section {
    @include stretch;

    .swot-score-dropdown {
      @include flex("block", "row", "end", "center");
    }

    .sort-items {
      @include flex("block", "row", "end", "center");
      font-weight: 600;

      .sort-item {
        margin-left: 0.8rem;
      }

      .label {
        font-weight: 400;
        margin-right: 0.3rem;
      }
    }
  }

  .themes-search {
    border-bottom: none;
    padding: 0 0.6rem 0 1rem;

    @media only screen and (min-width: 1280px) {
      padding-right: 0.8rem;
    }
  }
}
</style>
