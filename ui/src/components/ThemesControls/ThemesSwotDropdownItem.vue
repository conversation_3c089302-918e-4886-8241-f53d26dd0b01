<template>
  <section class="themes-swot-dropdown-item">
    <section v-if="data.header" class="header">
      {{ data.content }}
    </section>
    <section v-else class="item">
      <base-checkbox :value="data.selected"></base-checkbox>
      <section class="item-label">{{ data.content }}</section>
    </section>
  </section>
</template>

<script>
import BaseCheckbox from '@/components/Base/BaseCheckbox';

export default {
  name: 'themes-swot-dropdown-item',

  components: {
    BaseCheckbox,
  },

  props: {
    data: {
      type: Object,
      required: true,
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';
@import '~font-awesome/css/font-awesome.min.css';

.themes-swot-dropdown-item {
  .header {
    @include flex('block', 'row', 'start', 'center');
    font-size: $font-size-xs;
    font-weight: $font-weight-bold;
    min-height: 1.5rem;
    padding: 0.5rem 0.5rem 0.5rem 0.9rem;
  }

  .item {
    @include flex('block', 'row', 'start', 'center');
    cursor: pointer;
    font-size: $font-size-xs;
    font-weight: $font-weight-medium;
    min-height: 1.5rem;
    min-width: 11rem;
    padding: 0.5rem 0.5rem 0.5rem 0.9rem;

    &:hover {
      background-color: $cfp-btm-bar-apply-btn-bg;
      color: clr("white");
    }

    .item-label {
      margin-left: 0.3rem;
      width: 100%;
    }
  }
}
</style>
