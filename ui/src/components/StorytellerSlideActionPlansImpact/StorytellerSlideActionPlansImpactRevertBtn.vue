<template>
  <section class="storyteller-slide-action-plans-impact-revert-btn">
    <loading-blocks-overlay size="small" v-if="reverting" />
    <section v-else class="icon-wrapper" @click="onClick"
      v-tooltip.top="{
        content: 'Revert to Default',
        class: 'tooltip-base-dark',
        delay: 0,
      }"
    >
      <i class="fa-regular fa-arrow-rotate-left icon-revert" />
    </section>
  </section>
</template>

<script>
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import StorytellerActionPlansRequest from '@/services/request/StorytellerActionPlansRequest';

export default {
  name: 'storyteller-slide-action-plans-impact-revert-btn',

  components: {
    LoadingBlocksOverlay,
  },

  data() {
    return {
      reverting: false,
    };
  },

  methods: {
    async onClick() {
      this.reverting = true;
      await StorytellerActionPlansRequest.revertActionImpact();
      this.reverting = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-slide-action-plans-impact-revert-btn {
  margin-left: 0.4rem;

  .icon-wrapper {
    @include flex("block", "row", "center", "center");

    background-color: clr('white');
    border-radius: 50%;
    border: 1px solid #458D42;
    cursor: pointer;
    font-size: 1.5rem;
    height: 1.5rem;
    position: relative;
    width: 1.5rem;

    .icon-revert {
      color: #458D42;
      font-size: 0.75rem;
    }

    &:hover {
      background-color: #DAE8D9;
    }
  }
}
</style>
