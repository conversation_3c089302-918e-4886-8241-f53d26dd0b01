<template>
  <section class="storyteller-slide-action-plans-impact">
    <span class="column header">Potential Impact</span>
    <section v-for="(action, index) in actions" class="column content" :class="{middle: index === 1}">
      <storyteller-slide-action-plans-impact-text :action="action" :editable="editable" :key="index" @stopEditing="stopEditing" />
    </section>
  </section>
</template>

<script>
import StorytellerActionPlansRequest from '@/services/request/StorytellerActionPlansRequest';
import StorytellerSlideActionPlansImpactText from '@/components/StorytellerSlideActionPlansImpact/StorytellerSlideActionPlansImpactText';

export default {
  name: 'storyteller-slide-action-plans-impact',

  components: {
    StorytellerSlideActionPlansImpactText,
  },

  props: {
    actions: {
      type: Array,
      required: true,
    },
    editable: {
      type: Boolean,
      default: false,
    },
  },

  methods: {
    stopEditing(content, action) {
      action.impact = content;
      StorytellerActionPlansRequest.updateActionImpact(action.impact);
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-action-plans-impact {
  .column {
    border-top: $border-light solid rgba(19, 28, 41, 0.1);
  }

  .header {
    @include flex("block", "row", "start", "center");

    color: rgba(19, 28, 41, 0.3);
    font-size: 0.38em;
    font-weight: $font-weight-extra-bold;
    text-transform: uppercase;
  }

  .content {
    @include flex("block", "row", "start", "center");

    padding: 0 0.6em;

    &.middle {
      background-color: rgba(241, 241, 241, 0.5);
    }
  }
}
</style>
