<template>
  <section class="storyteller-slide-action-plans-impact-text">
    <section v-if="!editing" class="text">
      <section v-if="isEmptyImpact" class="empty" @dblclick="startEditing">-</section>
      <section v-else v-html="action.impact" class="text-content" @dblclick="startEditing" />
      <section class="icons" v-if="editable">
        <section class="icon-wrapper edit" @click="startEditing"
          v-tooltip.top="{
            content: 'Edit',
            class: 'tooltip-base-dark',
            delay: 0,
          }"
        >
          <i class="fa-light fa-pen icon" />
        </section>
      </section>
    </section>
    <storyteller-text-tiptap v-else
      :additional-buttons="additionalButtons"
      :init-content="action.impact"
      :show-text-size-control="false"
      max-height="1.2em"
      @blur="stopEditing"
    />
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import StorytellerSlideActionPlansImpactRevertBtn from '@/components/StorytellerSlideActionPlansImpact/StorytellerSlideActionPlansImpactRevertBtn';
import StorytellerTextTiptap from '@/components/StorytellerSlideText/StorytellerTextTiptap';

export default {
  name: 'storyteller-slide-action-plans-impact-text',

  components: {
    StorytellerTextTiptap,
  },

  props: {
    action: {
      type: Object,
      required: true,
    },
    editable: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      additionalButtons: [StorytellerSlideActionPlansImpactRevertBtn],
      editing: false,
    };
  },

  created() {
    this.localText = this.action.impact;
  },

  computed: {
    isEmptyImpact() {
      return this.action.impact === '<p></p>';
    },
  },

  methods: {
    ...mapActions('storytellerActionPlans', ['selectThemeId']),

    startEditing() {
      this.editing = true;
      this.selectThemeId({ themeId: this.action.themeId });
    },

    stopEditing(content) {
      this.editing = false;
      this.$emit('stopEditing', content, this.action);
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-action-plans-impact-text {
  font-size: 0.6em;
  width: 100%;

  &:hover {
    .text .icons {
      visibility: visible;
    }
  }

  .text {
    overflow: visible;
    position: relative;
    width: fit-content;

    .empty {
      color: #A4A4A4;
      font-weight: $font-weight-semi-bold;
    }

    .text-content {
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      display: -webkit-box;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .icons {
      @include flex("block", "row", "center", "center");

      position: absolute;
      right: -1.5em;
      top: 0.05em;
      visibility: hidden;

      .icon-wrapper {
        @include flex("block", "row", "center", "center");

        border-radius: 50%;
        border: 1px solid #3981F7;
        color: #3981F7;
        cursor: pointer;
        font-size: 1em;
        height: 1em;
        width: 1em;

        .icon {
          font-size: 0.5em;
        }

        &:hover {
          background-color: rgba(57, 129, 247, 0.3);
        }
      }
    }
  }

  .storyteller-text-tiptap {
    padding: 0.2em;
  }
}
</style>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-action-plans-impact {
  p {
    margin: 0;
  }

  strong {
    font-weight: $font-weight-semi-bold;
  }
}
</style>
