<template>
  <section class="value-at-risk-weight-button" :class="{ active, low: isLow, med: isMed, high: isHigh }"  @click="onClick">
    <section class="radio">
      <section class="icon-radio-wrapper">
        <i class="fa-solid fa-check icon-check icon-radio" />
      </section>
    </section>

    <section class="info">
      <slot name="header"></slot>
      <slot name="description"></slot>
      <section class="recommend" v-if="isMed">
        <label>Recommended</label>
      </section>
    </section>
  </section>
</template>

<script>
export default {
  name: 'value-at-risk-weight-button',

  props: {
    active: {
      type: Boolean,
      required: true,
    },

    weight: {
      type: String,
      required: true,
    },
  },

  computed: {
    isLow() {
      return this.weight === 'LOW';
    },

    isMed() {
      return this.weight === 'MEDIUM';
    },

    isHigh() {
      return this.weight === 'HIGH';
    },
  },

  methods: {
    onClick() {
      this.$emit('select', this.weight);
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.value-at-risk-weight-button {
  @include flex("block", "row", "start", "stretch");

  border: 1px solid $border-color;
  border-radius: 0.5rem;
  cursor: pointer;
  padding: 1rem;
  transition: all $interaction-transition-time;

  &.low .info .header {
    color: rgba(101, 192, 87, 1);
  }

  &.med .info .header {
    color: rgba(90, 127, 255, 1);
  }

  &.high .info .header {
    color: rgba(235, 83, 76, 1);
  }

  &:hover {
    &.low {
      background-color: rgba(101, 192, 87, 0.05);
      border-color: $swot-strength;
    }

    &.med {
      background-color: rgba(90, 127, 255, 0.1);
      border-color: rgba(90, 127, 255, 1);
    }

    &.high {
      background-color: rgba(235, 83, 76, 0.05);
      border-color: rgba(235, 83, 76, 1);
    }
  }

  &.active {
    .radio .icon-radio-wrapper {
      background-color: #5A7FFF;
      border-color: rgba(32, 63, 169, 0.8);
      color: white;

      .icon-radio {
        visibility: visible;
      }
    }

    &.low {
      background-color: rgba(101, 192, 87, 0.05);
      border-color: $swot-strength;
    }

    &.med {
      background-color: rgba(90, 127, 255, 0.1);
      border-color: rgba(90, 127, 255, 1);
    }

    &.high {
      background-color: rgba(235, 83, 76, 0.05);
      border-color: rgba(235, 83, 76, 1);
    }
  }

  .radio {
    @include flex("block", "row", "center", "start");

    color: #3B7DFF;
    height: 100%;
    margin-right: 0.6rem;

    .icon-radio-wrapper {
      @include flex("block", "row", "center", "center");

      background-color: white;
      border-radius: 50%;
      border: 1px solid #D9D9D9;
      height: 1rem;
      min-width: 1rem;
      width: 1rem;

      .icon-radio {
        font-size: 8px;
        visibility: hidden;
      }
    }
  }

  .info {
    @include flex("block", "column", "start", "stretch");

    margin-top: 0.1rem;

    .header {
      font-weight: $font-weight-bold;
      font-size: $font-size-xs;
      margin-bottom: 0.3rem;
    }

    .description {
      color: $nps-blue;
      font-size: 11px;
      line-height: 14px;
      opacity: 0.7;
    }

    .recommend {
      @include flex("block", "row", "start", "start");

      height: 14px;
      margin-top: 0.5rem;

      label {
        @include flex("block", "row", "center", "center");

        background-color: rgba(58, 70, 114, 0.9);
        border-radius: 14px;
        color: white;
        font-size: 8px;
        font-weight: $font-weight-bold;
        line-height: 14px;
        padding: 0 0.4rem;
        text-transform: uppercase;
      }
    }
  }
}
</style>
