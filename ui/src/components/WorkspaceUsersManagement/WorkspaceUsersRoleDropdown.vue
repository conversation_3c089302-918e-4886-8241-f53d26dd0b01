<template>
  <section class="workspace-users-role-dropdown">
    <section v-for="role in roles"
      class="item"
      :class="{active: active === role.titleCase()}"
      @click="onClick(role)"
    >
      <span class="text">{{ role.titleCase()}}</span>
    </section>
  </section>
</template>

<script>
import WorkspaceInviteUserRole from '@/enum/workspace-invite-user-role';

export default {
  name: 'workspace-users-role-dropdown',

  components: {
  },

  props: {
    active: {
      type: String,
      required: true,
    },
  },

  data() {
    return {
      roles: [
        WorkspaceInviteUserRole.ADMIN,
        WorkspaceInviteUserRole.EDITOR,
        WorkspaceInviteUserRole.VIEWER,
      ],
    };
  },

  computed: {
  },

  methods: {
    onClick(role) {
      if (this.active === role.titleCase()) return;
      this.$emit('select', role);
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.workspace-users-role-dropdown {
  @include flex("block", "column", "start", "stretch");

  background-color: clr("white");
  border: $border-standard;
  border-radius: $border-radius-medium;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
  position: absolute;
  margin-top: 0.2rem;
  right: 0;
  top: 1.7rem;
  width: 5rem;
  z-index: 1;

  .item {
    @include flex("block", "row", "start", "center");

    cursor: pointer;
    padding: 0.4rem 1rem;
    transition: all $interaction-transition-time;

    &:hover {
      background-color: rgba(clr('black'), 0.05);
    }

    &.active {
      color: clr("purple");
    }

    .text {
      color: inherit;
      font-size: $font-size-xs;
      font-weight: $font-weight-normal;
    }
  }
}
</style>
