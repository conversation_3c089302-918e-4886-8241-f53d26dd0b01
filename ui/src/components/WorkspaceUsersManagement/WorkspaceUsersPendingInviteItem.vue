<template>
  <section class="workspace-users-pending-invite-item">
    <section class="info">
      <i class="fa-solid fa-question icon-avatar"></i>
      <section class="labels">
        <span class="name" v-if="memberName">{{ memberName }}</span>
        <span class="email" v-if="memberEmail">{{ memberEmail }}</span>
      </section>
    </section>
    <section class="status">
      <section v-if="isAdmin" class="status-delete" @click="onClickDelete">
        <i class="fa-solid fa-trash-can" />
      </section>
      <section class="status-text">Pending</section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import WorkspaceInviteUserRole from '@/enum/workspace-invite-user-role';

export default {
  name: 'workspace-users-pending-invite-item',

  props: {
    memberItem: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapGetters('user', ['isAdmin']),

    memberEmail() {
      return this.memberItem.email?.trim() || '';
    },

    memberId() {
      return this.memberItem.id;
    },

    memberName() {
      const rs = [];
      if (this.memberItem.firstName?.trim().length) {
        rs.push(this.memberItem.firstName?.trim());
      }
      if (this.memberItem.lastName?.trim().length) {
        rs.push(this.memberItem.lastName?.trim());
      }

      return rs?.join(' ') || '';
    },

    memberRole() {
      if (this.memberItem.role) {
        return WorkspaceInviteUserRole[this.memberItem.role].titleCase();
      }

      return '';
    },
  },

  methods: {
    ...mapActions('workspaces', ['addEmailsToDisinvite']),

    onClickDelete() {
      this.addEmailsToDisinvite({ email: this.memberEmail });
      this.$emit('delete', this.memberItem);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.workspace-users-pending-invite-item {
  @include flex("block", "row", "between", "center");
  color: #2D1757;
  width: 100%;

  .info {
    @include flex("block", "row", "center", "center");

    .icon-avatar {
      @include flex("block", "row", "center", "center");

      background-color: rgba(255, 107, 0, 0.1);
      border-radius: 50%;
      color: #FF6B00;
      font-size: 12px;
      height: 26px;
      margin-right: 0.8rem;
      width: 26px;
    }

    .labels {
      @include flex("block", "column", "center", "start");

      .email {
        font-size: 11px;
        margin-top: 0.2rem;
      }

      .name {
        font-size: $font-size-xs;
        font-weight: $font-weight-bold;
      }
    }
  }

  .status {
    @include flex("block", "row", "center", "center");

    .status-delete {
      @include flex("block", "row", "center", "center");

      border: 1px solid rgba(203, 21, 9, 0.8);
      border-radius: 50%;
      color: rgba(203, 21, 9, 0.8);
      cursor: pointer;
      font-size: $font-size-xxs;
      height: 22px;
      margin-right: 1rem;
      width: 22px;

      &:hover {
        background: rgba(203, 21, 9, 1);
        color: #FFFFFF;
      }
    }

    .status-text {
      @include flex("block", "row", "end", "center");

      font-size: $font-size-xs;
      font-style: italic;
      font-weight: $font-weight-semi-light;
      width: 5rem;
    }
  }
}
</style>
