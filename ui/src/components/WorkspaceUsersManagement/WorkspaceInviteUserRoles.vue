<template>
  <section class="workspace-invite-user-roles">
    <span v-for="(item, i) in dataList"
          :key="i"
          class="role-item"
          :class="{ active: invitingRole === item}"
          @click="onSelect(item)"
    >
      {{ item.name }}
    </span>
  </section>
</template>

<script>
import WorkspaceInviteUserRole from '@/enum/workspace-invite-user-role';

export default {
  name: 'workspace-invite-user-roles',

  props: {
    invitingRole: {
      type: Object,
      required: true,
    },
  },

  computed: {
    dataList() {
      return WorkspaceInviteUserRole.enumValues
        .filter(t => t !== WorkspaceInviteUserRole.OWNER);
    },
  },

  methods: {
    onSelect(item) {
      this.$emit('onSelect', item);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.workspace-invite-user-roles {
  @include flex("block", "column", "start", "start");
  background: #ffffff;
  border: none;
  border-radius: $border-radius-medium;
  box-shadow: 0 0 3px 3px rgba(0, 0, 0, 0.25);

  .role-item {
    cursor: pointer;
    font-size: $font-size-xs;
    padding: 0.5rem 1rem;
    width: 100%;

    &.active {
      color: clr("purple");
    }

    &:hover {
      background-color: rgba(clr('black'), 0.05);
    }
  }
}
</style>
