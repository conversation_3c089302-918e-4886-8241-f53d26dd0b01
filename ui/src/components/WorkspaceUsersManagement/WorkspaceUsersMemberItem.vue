<template>
  <section class="workspace-users-member-item">
    <section class="info">
      <user-workspace-avatar :member-item=memberItem class="avatar" />
      <section class="labels">
        <span class="name" v-if="memberName">{{ memberName }}</span>
        <span class="email" v-if="memberEmail">{{ memberEmail }}</span>
      </section>
    </section>
    <section class="status">
      <section v-if="!disableAction" class="status-delete" @click="onClickDelete">
        <i class="fa-solid fa-trash-can" />
      </section>
      <section class="status-role"
        :class="{'normal-text': disableAction}"
        @click="onClickDropdown"
        v-click-outside-handler="{ handler: 'onClickOutsideRole' }"
      >
        <section class="text">{{ localRole }}</section>
        <i v-if="!disableAction" class="fa fa-caret-down icon" :class="{ 'show-dropdown': showDropdown }"></i>
      </section>
      <workspace-users-role-dropdown
          :active="localRole"
          v-if="showDropdown"
          @close="showDropdown = false"
          @select="selectRole"
      />
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import clickOutsideHandler from '@/directives/click-outside-handler';
import UserWorkspaceAvatar from '@/components/UserWorkspaceAvatar';
import WorkspaceInviteUserRole from '@/enum/workspace-invite-user-role';
import WorkspaceUsersRoleDropdown from '@/components/WorkspaceUsersManagement/WorkspaceUsersRoleDropdown';

export default {
  name: 'workspace-users-member-item',

  components: {
    UserWorkspaceAvatar,
    WorkspaceUsersRoleDropdown,
  },

  directives: {
    clickOutsideHandler,
  },

  props: {
    memberItem: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      localRole: '',
      showDropdown: false,
    };
  },

  computed: {
    ...mapState('user', ['user']),

    ...mapGetters('user', ['isAdmin']),

    ...mapState('workspaces', ['selectedWorkspace']),

    disableAction() {
      return this.memberRole === 'Owner'
        || !this.isAdmin
        || this.memberItem.id === this.user.id;
    },

    memberEmail() {
      return this.memberItem.email?.trim() || '';
    },

    memberId() {
      return this.memberItem.id;
    },

    memberName() {
      const rs = [];
      if (this.memberItem.firstName?.trim().length) {
        rs.push(this.memberItem.firstName?.trim());
      }
      if (this.memberItem.lastName?.trim().length) {
        rs.push(this.memberItem.lastName?.trim());
      }

      return rs?.join(' ') || '';
    },

    memberRole() {
      if (this.memberItem.role) {
        return WorkspaceInviteUserRole[this.memberItem.role].titleCase();
      }

      if (this.selectedWorkspace?.ownerId === this.memberId) {
        return WorkspaceInviteUserRole.OWNER.titleCase();
      }

      if (this.selectedWorkspace.administratorIds?.includes(this.memberId)) {
        return WorkspaceInviteUserRole.ADMIN.titleCase();
      }

      if (this.selectedWorkspace.editorIds?.includes(this.memberId)) {
        return WorkspaceInviteUserRole.EDITOR.titleCase();
      }

      if (this.selectedWorkspace.viewerIds?.includes(this.memberId)) {
        return WorkspaceInviteUserRole.VIEWER.titleCase();
      }

      return '';
    },
  },

  mounted() {
    this.localRole = this.memberRole;
  },

  methods: {
    ...mapActions('workspaces', ['addEditingWorkspaceMembersList']),

    onClickDelete() {
      const member = {
        id: this.memberItem.id,
        type: 'REMOVE',
      };
      this.showDropdown = false;
      this.addEditingWorkspaceMembersList({ member });
      this.$emit('delete', this.memberItem);
    },

    onClickDropdown() {
      if (this.disableAction) return;
      this.showDropdown = !this.showDropdown;
    },

    onClickOutsideRole() {
      this.showDropdown = false;
    },

    selectRole(role) {
      const member = {
        id: this.memberItem.id,
        role: role.name,
        type: 'UPDATE',
      };
      this.localRole = role.titleCase();
      this.showDropdown = false;
      this.addEditingWorkspaceMembersList({ member });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.workspace-users-member-item {
  @include flex("block", "row", "between", "center");

  color: #2D1757;
  width: 100%;

  &:last-child {
    .workspace-users-role-dropdown {
      top: -5.7rem;
    }
  }

  .info {
    @include flex("block", "row", "center", "center");

    .avatar {
      margin-right: 0.8rem;
    }

    .labels {
      @include flex("block", "column", "center", "start");

      .name {
        font-size: $font-size-xs;
        font-weight: $font-weight-bold;
      }

      .email {
        font-size: 11px;
        margin-top: 0.2rem;
      }
    }
  }

  .status {
    @include flex("block", "row", "center", "center");

    position: relative;

    .status-delete {
      @include flex("block", "row", "center", "center");

      border: 1px solid rgba(203, 21, 9, 0.8);
      border-radius: 50%;
      color: rgba(203, 21, 9, 0.8);
      cursor: pointer;
      font-size: $font-size-xxs;
      margin-right: 1rem;
      height: 22px;
      width: 22px;

      &:hover {
        background: rgba(203, 21, 9, 1);
        color: #FFFFFF;
      }
    }

    .status-role {
      @include flex("block", "row", "center", "center");

      border: 1px solid rgba(75, 114, 240, .15);
      border-radius: 2px;
      cursor: pointer;
      font-size: $font-size-xs;
      font-weight: $font-weight-bold;
      padding: 0.3rem 0;
      width: 5rem;

      &.normal-text {
        @include flex("block", "row", "end", "center");

        border: 0;
        cursor: unset;
        font-weight: $font-weight-normal;
      }

      .icon {
        transition: all $interaction-transition-time;

        &.show-dropdown {
          transform: rotate(180deg);
        }
      }

      .text {
        padding-right: 0.3rem;
      }
    }
  }
}
</style>
