<template>
  <section class="storyteller-slide-intro-gen-title">
    <button class="btn-generate-title" @click.stop="onClickGenerateTitle">
      <i class="fa-regular fa-arrows-rotate additional-icon" />
      <span>New Title</span>
    </button>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import StorytellerSlideRequest from '@/services/request/StorytellerSlideRequest';

export default {
  name: 'storyteller-slide-intro-gen-title',

  created() {
    StorytellerSlideRequest.generateSlideTitle();
  },

  methods: {
    ...mapActions('storyteller', ['setShouldUpdateIntroTitle']),

    async onClickGenerateTitle() {
      this.setShouldUpdateIntroTitle({ value: true });
      await StorytellerSlideRequest.generateSlideTitle();
      this.setShouldUpdateIntroTitle({ value: false });
    },
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-intro-gen-title {
  @include flex("block", "row", "start", "center");

  height: 100%;
  margin-left: 1rem;

  .btn-generate-title {
    @include flex("block", "row", "center", "center");

    background-color: #458F38;
    border-radius: 3px;
    border: none;
    color: clr('white');
    cursor: pointer;
    font-size: $font-size-xxs;
    font-weight: $font-weight-bold;
    height: 100%;
    padding: 0.4rem 0.6rem;
    text-transform: uppercase;

    .additional-icon {
      margin-right: 0.2rem;
    }
  }
}
</style>
