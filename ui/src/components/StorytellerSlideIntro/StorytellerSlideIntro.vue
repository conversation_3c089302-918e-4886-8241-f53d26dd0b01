<template>
  <section class="storyteller-slide-intro"
    :style="{fontSize}"
    @mouseenter="hovered = true"
    @mouseleave="hovered = false"
  >
    <section class="margin-top" />
    <section class="body">
      <img :src="localOrgLogo" v-if="orgLogo && orgLogoDisplay" class="organisation-logo" alt="organisation-logo"/>
      <storyteller-slide-intro-text
        :editable="editable"
        :font-size="fontSizeText"
        :hovered="hovered"
        :text-align="textAlign"
      />
      <span class="text-note">{{headerText}}</span>
    </section>
    <section class="footer" v-if="displayAdoreboardLogo">
      <img :src="require('@/assets/logo/logo-power-by-adoreboard.svg')" class="power-by-logo" alt="power-by-logo"/>
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import StorytellerSlideIntroText from '@/components/StorytellerSlideIntro/StorytellerSlideIntroText';

export default {
  name: 'storyteller-slide-intro',

  components: {
    StorytellerSlideIntroText,
  },

  props: {
    editable: {
      type: Boolean,
      default: false,
    },
    slideData: {
      type: Object,
      required: true,
    },
    slideWidth: {
      type: Number,
      required: true,
    },
  },

  data() {
    return {
      hovered: false,
    };
  },

  computed: {
    ...mapState('organisation', ['localOrgLogo', 'organisation']),

    ...mapState('storyteller', ['activeReport']),

    displayAdoreboardLogo() {
      return this.activeReport.settings.displaySettings.displayAdoreboardLogo;
    },

    fontSize() {
      return `${50 * this.ratio}px`;
    },

    fontSizeText() {
      return `${this.slideData.customCssStyle.fontSize * this.ratio}px`;
    },

    headerText() {
      return this.slideData.header;
    },

    orgLogo() {
      return this.organisation.settings.logoUrl;
    },

    orgLogoDisplay() {
      return this.activeReport.settings.displaySettings.displayOrgCoverLogo;
    },

    ratio() {
      return this.slideWidth / 1920;
    },

    textAlign() {
      return this.slideData.customCssStyle.align;
    },
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-intro {
  background-color: #FFF;
  color: $nps-blue;
  display: grid;
  font-family: Inter, serif;
  font-weight: $font-weight-semi-light;
  grid-template-rows: 13% 74% 13%;
  height: 100%;
  padding: 0 2em;
  width: 100%;

  strong {
    font-weight: 600;
  }

  .body {
    @include flex("block", "column", "center", "start");

    height: 100%;
    position: relative;
    width: 100%;

    .organisation-logo {
      height: 1.8em;
      left: 0;
      max-height: 1.8em;
      position: absolute;
      top: 0;
    }

    .text-note {
      font-size: 0.5em;
      font-weight: $font-weight-extra-bold;
      margin-top: 1.28em;
      opacity: 0.5;
    }
  }

  .footer {
    @include flex("block", "row", "end", "center");

    border-top: 1px solid rgba(19, 28, 41, 0.3);
    width: 100%;

    .power-by-logo {
      width: 6.6em;
    }
  }
}
</style>
