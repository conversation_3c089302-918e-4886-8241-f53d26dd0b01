<template>
  <section class="storyteller-slide-intro-title-dropdown">
    <section class="title">
      <i class="fa-light fa-arrow-up-arrow-down icon-arrow" />
      <span>Title Selection</span>
    </section>
    <section v-if="availableTitles.includes('var')" class="item" @click.stop="onClickItem('var')">
      <base-radio-with-tick-mark :value="titleType === 'var'" />
      <i class="fa-regular fa-calculator icon" />
      <span>Revenue at Risk</span>
    </section>
    <section v-if="availableTitles.includes('metric')" class="item" @click.stop="onClickItem('metric')">
      <base-radio-with-tick-mark :value="titleType === 'metric'" />
      <i class="fa-regular fa-chart-column icon" />
      <span>Metric</span>
    </section>
    <section v-if="availableTitles.includes('increase')" class="item" @click.stop="onClickItem('increase')">
      <base-radio-with-tick-mark :value="titleType === 'increase'" />
      <i class="fa-regular fa-arrow-up icon" />
      <span>Increase</span>
    </section>
    <section v-if="availableTitles.includes('address')"  class="item" @click.stop="onClickItem('address')">
      <base-radio-with-tick-mark :value="titleType === 'address'" />
      <i class="fa-regular fa-stamp icon" />
      <span>Address</span>
    </section>
    <section v-if="availableTitles.includes('theme_insight')" class="item" @click.stop="onClickItem('theme_insight')">
      <base-radio-with-tick-mark :value="titleType === 'theme_insight'" />
      <i class="fa-regular fa-tag icon" />
      <span>Themes, Insights...</span>
    </section>
    <section v-if="availableTitles.includes('number_of_themes')" class="item" @click.stop="onClickItem('number_of_themes')">
      <base-radio-with-tick-mark :value="titleType === 'number_of_themes'" />
      <i class="fa-regular fa-tag icon" />
      <span># of Themes...</span>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseRadioWithTickMark from '@/components/Base/BaseRadioWithTickMark';
import StorytellerSlideRequest from '@/services/request/StorytellerSlideRequest';

export default {
  name: 'storyteller-slide-intro-title-dropdown',

  components: {
    BaseRadioWithTickMark,
  },

  computed: {
    ...mapState('storyteller', ['selectedSlide']),

    availableTitles() {
      return this.selectedSlide.slideData.availableTitles;
    },

    titleType() {
      return this.selectedSlide.slideData.selectedTitle;
    },
  },

  methods: {
    ...mapActions('storyteller', ['setShouldUpdateIntroTitle']),

    onClickOutside() {
      this.$emit('close');
    },

    async onClickItem(item) {
      if (this.titleType === item) return;
      await StorytellerSlideRequest.generateSlideTitle(item);
      this.setShouldUpdateIntroTitle({ value: true });
      await StorytellerSlideRequest.getSlide();
      this.setShouldUpdateIntroTitle({ value: false });
    },

    onClose() {
      this.$emit('close');
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-intro-title-dropdown {
  @include flex("block", "column", "center", "start");
  @include panel;

  cursor: auto;
  color: $nps-blue;
  font-size: $font-size-sm;
  padding: 0.5rem 1rem;
  position: absolute;
  right: 0;
  top: 32px;
  width: 220px;

  .title {
    font-weight: $font-weight-bold;
    padding-bottom: 0.5rem;
    padding-top: 0.5rem;
  }

  .item {
    @include flex("block", "row", "start", "center");

    border-bottom: 1px solid rgba(208, 208, 208, 0.4);
    border-radius: 3px;
    cursor: pointer;
    padding-bottom: 0.5rem;
    padding-top: 0.5rem;
    width: 100%;

    &:last-child {
      border-bottom: none;
    }

    &:hover, &:focus {
      background-color: #E6EAFF;
    }

    .icon {
      margin-left: 0.5rem;
      margin-right: 0.5rem;
    }
  }
}
</style>
