<template>
  <section class="storyteller-slide-intro-text" :style="style" :class="{editable, hovered}">
    <transition mode="out-in" name="fade">
      <section v-if="!editing" class="text" @dblclick="onClickEdit">
        <section v-html="localText" class="text-content" />
        <section class="icons">
          <section class="icon-wrapper edit" @click.stop="onClickEdit"
            v-tooltip.top="{
             content: 'Edit',
             class: 'tooltip-base-dark',
             delay: 0,
            }"
          >
            <i class="fa-light fa-pen icon" />
          </section>
          <section class="icon-wrapper arrow" ref="btnArrow" @click.stop="onClickSelectTitle"
            v-tooltip.top="{
             content: 'Select title',
             class: 'tooltip-base-dark',
             delay: 0,
            }"
          >
            <i class="fa-light fa-arrow-up-arrow-down icon" />
            <storyteller-slide-intro-title-dropdown v-if="openTitleDropdown" v-click-outside-handler="{ handler: 'onCloseDropdown' }" />
          </section>
        </section>
      </section>
      <storyteller-text-tiptap v-else
        :key="editorKey"
        :additional-buttons="additionalButtons"
        :init-content="localText"
        :text-align="textAlign"
        :max-characters="80"
        @blur="stopEditing"
        @minusText="minusText"
        @plusText="plusText"
        @selectAlign="selectAlign"
        @updateText="updateText"
      />
    </transition>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import ClickOutsideHandler from '@/directives/click-outside-handler';
import StorytellerSlideIntroGenTitle from '@/components/StorytellerSlideIntro/StorytellerSlideIntroGenTitle';
import StorytellerSlideIntroTitleDropdown from '@/components/StorytellerSlideIntro/StorytellerSlideIntroTitleDropdown';
import StorytellerSlideRequest from '@/services/request/StorytellerSlideRequest';
import StorytellerTextTiptap from '@/components/StorytellerSlideText/StorytellerTextTiptap';

export default {
  name: 'storyteller-slide-intro-text',

  components: {
    StorytellerSlideIntroGenTitle,
    StorytellerSlideIntroTitleDropdown,
    StorytellerTextTiptap,
  },

  directives: {
    ClickOutsideHandler,
  },

  props: {
    editable: {
      type: Boolean,
      required: true,
    },
    hovered: {
      type: Boolean,
      required: true,
    },
    fontSize: {
      type: String,
      default: '16px',
    },
    textAlign: {
      type: String,
      default: 'start',
    },
  },

  data() {
    return {
      editing: false,
      editingText: '',
      editorKey: 0,
      localText: '',
      openTitleDropdown: false,
    };
  },

  computed: {
    ...mapGetters('network', ['status']),

    ...mapGetters('storyteller', ['slideIntro']),

    ...mapState('storyteller', [
      'generatedTitle',
      'selectedSlide',
      'shouldUpdateIntroTitle',
    ]),

    additionalButtons() {
      if (this.editingText !== this.generatedTitle) return [StorytellerSlideIntroGenTitle];
      return null;
    },

    introText() {
      return this.slideIntro.slideData.text;
    },

    style() {
      return {
        '--textFontSize': this.fontSize,
        '--justify-content': this.textAlign,
      };
    },
  },

  watch: {
    hovered() {
      if (!this.hovered) this.openTitleDropdown = false;
    },

    introText() {
      this.localText = this.introText;
    },

    shouldUpdateIntroTitle() {
      if (this.shouldUpdateIntroTitle) {
        this.localText = this.generatedTitle;
        this.editingText = this.generatedTitle;
        this.editorKey += 1;
      }
    },
  },

  created() {
    this.localText = this.introText;
    this.editingText = this.introText;
  },

  methods: {
    ...mapActions('storyteller', ['setShouldUpdateIntroTitle']),

    minusText() {
      this.selectedSlide.slideData.customCssStyle.fontSize -= 1;
    },

    plusText() {
      this.selectedSlide.slideData.customCssStyle.fontSize += 1;
    },

    onClickEdit() {
      this.editing = true;
    },

    onCloseDropdown(e) {
      if (!this.$refs.btnArrow.contains(e.target)) {
        this.openTitleDropdown = false;
      }
    },

    onClickSelectTitle() {
      this.openTitleDropdown = !this.openTitleDropdown;
    },

    stopEditing(content) {
      this.editing = false;
      if (content !== this.localText) {
        this.localText = content;
        this.selectedSlide.slideData.text = content;
        StorytellerSlideRequest.updateSlide();
      }
    },

    selectAlign(alignment) {
      this.selectedSlide.slideData.customCssStyle.align = alignment;
    },

    updateText(content) {
      this.editingText = content;
    },
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-intro-text {
  @include flex("block", "row", "start", "center");

  font-size: var(--textFontSize);
  justify-content: var(--justify-content);
  position: relative;
  text-align: var(--justify-content);
  width: 75%;

  .text {
    @include flex("block", "row", "start", "center");

    font-variant-ligatures: none;
    position: relative;
    width: fit-content;
    word-break: break-word;

    .text-content {
      p {
        margin: 0;
        text-wrap: balance;
      }
    }

    .icons {
      @include flex("block", "row", "center", "center");

      height: 100%;
      position: absolute;
      right: -100px;
      visibility: hidden;
    }

    .icon-wrapper {
      @include flex("block", "row", "center", "center");

      border-radius: 50%;
      cursor: pointer;
      font-size: 25px;
      height: 25px;
      position: relative;
      width: 25px;

      &.edit {
        border: 1px solid #3981F7;
        color: #3981F7;
        margin-right: 0.6rem;
      }

      &.arrow {
        border: 1px solid #458D42;
        color: #458D42;
      }

      .icon {
        font-size: 12px;
      }

      &:hover {
        &.edit {
          background-color: rgba(57, 129, 247, 0.3);
        }

        &.arrow {
          background-color: rgba(69, 141, 66, 0.3);
        }
      }
    }
  }

  &.editable {
    &.hovered {
      .icons {
        visibility: visible;
      }
    }
  }

  .storyteller-text-tiptap {
    .custom-editor {
      p {
        //margin: 0;
        text-wrap: balance;
      }
    }
  }
}
.fade-enter-active {
  transition: outline-width 0.1s ease;
}

.fade-enter, .fade-leave-to
  /* .component-fade-leave-active below version 2.1.8 */ {
  outline-width: 0;
}
</style>
