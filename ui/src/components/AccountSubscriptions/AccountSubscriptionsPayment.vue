<template>
  <section class="account-subscriptions-payment">
    <account-header>{{ isPaid ? 'Change Subscription' : 'Confirm Subscription' }}</account-header>

    <section class="body">
      <section class="left">
        <account-subscriptions-payment-plan/>
        <account-subscriptions-payment-contract/>
        <account-subscriptions-payment-method/>
        <account-subscriptions-payment-review/>
      </section>

      <section class="right">
        <account-subscriptions-payment-details/>
      </section>
    </section>
  </section>
</template>

<script>
import { CreditCardIcon, LockIcon } from 'vue-feather-icons';
import { mapActions, mapGetters, mapState } from 'vuex';

import AccountCard from '@/components/Account/AccountCard';
import AccountHeader from '@/components/Account/AccountHeader';
import AccountPaymentForm from '@/components/Account/AccountPaymentForm';
import BaseButton from '@/components/Base/BaseButton';
import AccountSubscriptionsPaymentContract from '@/components/AccountSubscriptions/AccountSubscriptionsPaymentContract';
import AccountSubscriptionsPaymentDetails from '@/components/AccountSubscriptions/AccountSubscriptionsPaymentDetails';
import AccountSubscriptionsPaymentMethod from '@/components/AccountSubscriptions/AccountSubscriptionsPaymentMethod';
import AccountSubscriptionsPaymentPlan from '@/components/AccountSubscriptions/AccountSubscriptionsPaymentPlan';
import AccountSubscriptionsPaymentReview from '@/components/AccountSubscriptions/AccountSubscriptionsPaymentReview';
import { paymentApi } from '@/services/api';

export default {
  name: 'account-subscriptions-payment',

  components: {
    AccountCard,
    AccountHeader,
    AccountPaymentForm,
    BaseButton,
    CreditCardIcon,
    LockIcon,
    AccountSubscriptionsPaymentContract,
    AccountSubscriptionsPaymentDetails,
    AccountSubscriptionsPaymentMethod,
    AccountSubscriptionsPaymentPlan,
    AccountSubscriptionsPaymentReview,
  },

  computed: {
    ...mapState('payment', ['tier']),

    ...mapGetters('user', ['extractTier', 'isPaid']),
  },

  async created() {
    if (this.isPaid) {
      await paymentApi.fetchCards();
    }

    if (this.tier == null && this.extractTier != null) {
      this.setTier({ tier: this.extractTier });
    }
  },

  methods: {
    ...mapActions('payment', ['setTier']),
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.account-subscriptions-payment {
  @include account-page;

  .body {
    @include flex("block", "row", "between", "stretch");

    .account-page-card {
      margin-bottom: 2rem;
    }

    .left {
      @include flex("block", "column", "start", "stretch");

      margin-right: 0.5rem;
      max-width: 700px;
      width: 60%;
    }

    .right {
      @include flex("block", "column", "start", "stretch");

      margin-left: 0.5rem;
      max-width: 400px;
      width: 40%;
    }
  }
}
</style>
