<template>
  <account-card
    class="account-subscriptions-payment-plan"
    :editable="true"
    :editing="editingTier"
    @edit="editingTier = true"
  >
    <span slot="header">1. Choose Your Plan</span>
    <span slot="link">Change</span>

    <section class="plan-details">
      <section class="plan-name">{{ tier ? tier.titleCase() : '...' }} Plan</section>
      <section
        class="plan-cost"
      >${{ selected ? selected.cost : '...' }} / {{ selected ? selected.duration : '...' }}</section>
    </section>

    <section class="plan-select" slot="editing">
      <section
        v-for="plan in plansLimited"
        :key="plan.id"
        class="plan"
        :class="{ active: isActive(plan) }"
        @click="selectPlan(plan)"
      >
        <section class="control">
          <base-radio :value="isActive(plan)"></base-radio>
        </section>

        <section class="text">
          <h3>{{ plan.tier.titleCase() }}</h3>
          <span>${{ plan.cost }} / {{ plan.duration }}</span>
        </section>
      </section>
    </section>
  </account-card>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import AccountCard from '@/components/Account/AccountCard';
import BaseRadio from '@/components/Base/BaseRadio';
import SubscriptionTier from '@/enum/subscription-tier';

export default {
  name: 'account-subscriptions-payment-plan',

  components: {
    AccountCard,
    BaseRadio,
  },

  data() {
    return {
      editingTier: false,
    };
  },

  computed: {
    ...mapState('payment', ['plans', 'tier']),

    plansLimited() {
      return Object.values(this.plans)
        .filter(plan => plan.tier !== SubscriptionTier.INSIGHTS_AS_A_SERVICE);
    },

    selected() {
      return this.plans[this.tier];
    },
  },

  methods: {
    ...mapActions('payment', ['setTier']),

    isActive(plan) {
      return plan.tier === this.selected.tier;
    },

    selectPlan(plan) {
      this.setTier({ tier: plan.tier });
      this.editingTier = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.account-subscriptions-payment-plan {
  .plan-details {
    margin: 0.5rem 0;

    .plan-name {
      font-weight: $font-weight-medium;
      font-size: $font-size-md;
    }

    .plan-cost {
      font-size: $font-size-sm;
      margin-top: 0.3rem;
    }
  }

  .plan {
    @include flex("block", "row", "start", "center");

    border-radius: $border-radius-medium;
    cursor: pointer;
    padding: 1.5rem 1rem;
    margin-bottom: 0.5rem;
    transition: all $interaction-transition-time;

    &:hover,
    &.active {
      background-color: clr("blue", "lighter");
    }

    .control {
      margin-right: 1rem;
    }

    .text {
      @include flex("block", "column", "start", "stretch");
      @include grow;

      h3 {
        margin: 0;
      }

      span {
        font-size: $font-size-sm;
      }
    }
  }
}
</style>
