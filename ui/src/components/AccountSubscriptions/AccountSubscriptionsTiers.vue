<template>
  <section class="account-subscriptions-tiers">
    <account-header>Subscriptions</account-header>
    <section class="tiers">
      <account-subscriptions-tiers-item v-for="item in availableTiers"
                                        :item="item"
                                        :key="item.tier.name">
      </account-subscriptions-tiers-item>
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import AccountHeader from '@/components/Account/AccountHeader';
import SubscriptionTier from '@/enum/subscription-tier';
import AccountSubscriptionsTiersItem from '@/components/AccountSubscriptions/AccountSubscriptionsTiersItem';

export default {
  name: 'account-page-account-subscriptions-tiers',

  components: {
    AccountHeader,
    AccountSubscriptionsTiersItem,
  },

  data() {
    return {
      tiers: [
        {
          details: [
            'Measure Emotion with 8 Indexes',
            'Benchmark Analysis',
            'SWOT Analysis',
            'Customer Journey Mapping',
            'Predictive Analysis (e.g. NPS)',
            'Presentation Friendly Reports',

          ],
          tier: SubscriptionTier.LITE,
          weight: 1,
        },
        {
          details: [
            'Trend Detection',
            'Benchmark up to 10 brands',
            'Easy data upload with TXT, CSV and XLSX',
            'Integrate with Qualtrics, Zendesk and SurveyMonkey',
            'Access 16 hours support for first data project',
            'Helpdesk for onboarding support',
          ],
          tier: SubscriptionTier.PROFESSIONAL,
          weight: 2,
        },
        {
          details: [
            'Scale insights with analysis in 76 languages',
            'Enable up to 5 users access',
            'API access',
            'Full historical Twitter and Reddit access',
            'Account manager with ongoing training',
          ],
          tier: SubscriptionTier.INSIGHTS_AS_A_SERVICE,
          weight: 3,
        },
      ],
      SubscriptionTier,
    };
  },

  computed: {
    ...mapState('payment', ['plans']),

    availableTiers() {
      return this.tiers.map(t => {
        t.duration = this.getDuration(t.tier);
        t.price = this.getPrice(t.tier);
        return t;
      });
    },
  },

  methods: {
    getDuration(tier) {
      return this.plans[tier].duration;
    },

    getPrice(tier) {
      return this.plans[tier].cost;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.account-subscriptions-tiers {
  @include account-page;

  .tiers {
    @include flex("block", "row", "center", "start");

    position: relative;
  }
}
</style>
