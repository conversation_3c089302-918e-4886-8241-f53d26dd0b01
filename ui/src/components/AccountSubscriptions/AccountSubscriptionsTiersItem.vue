<template>
  <section class="account-subscriptions-tiers-item">
    <section class="gradient"></section>

    <section class="foreground">
      <section class="top">
        <section class="name" :class="{ lite: isLite, pro: isPro, busi: isBusi }">{{ topName }}</section>
      </section>
      <section class="top-license">
        <section class="license" :class="{ lite: isLite, pro: isPro, busi: isBusi }">{{ topLicense }}</section>
      </section>
      <section class="body">
        <section class="pricing" v-if="item.price > -1">
          <section class="mark">$</section>
          <section class="price">{{ priceText }}/</section>
          <section class="duration">
            <span>{{ item.duration }}</span>
          </section>
        </section>
        <section class="details">
          <section class="access" :class="{ lite: isLite, pro: isPro, busi: isBusi }">
            {{ detailsAccess }}
          </section>
          <section class="amounts">
            <check-circle-icon class="icon" ></check-circle-icon>
            Import up to &nbsp;
            <section class="amount">{{ detailsAmount }}</section>
            &nbsp; characters
          </section>
          <section v-for="(detail, index) in item.details" :key="index" class="detail">
            <check-circle-icon class="icon" ></check-circle-icon>
            {{ detail }}
          </section>
        </section>
      </section>

      <section class="actions">
        <a v-if="item.tier === SubscriptionTier.INSIGHTS_AS_A_SERVICE" class="contact-link">
          <base-button link="#" linkId="intercom-chat-link" target="_self" @click="onClickChat" class="button">Talk to Sales</base-button>
        </a>
        <base-button v-else class="button" @click="onClickBuy">Buy Now</base-button>
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions } from 'vuex';
import { CheckCircleIcon } from 'vue-feather-icons';
import Logo from '@/../static/icons/favicon-196x196.png';

import AccountSubscriptionsTiersItemBackground from '@/components/AccountSubscriptions/AccountSubscriptionsTiersItemBackground';
import BaseButton from '@/components/Base/BaseButton';
import SubscriptionTier from '@/enum/subscription-tier';

export default {
  name: 'subscription-tiers-item',

  components: {
    AccountSubscriptionsTiersItemBackground,
    BaseButton,
    CheckCircleIcon,
  },

  props: {
    item: {
      type: Object,
      required: true,
    },
  },

  computed: {
    /* global StripeCheckout */
    handler() {
      return StripeCheckout.configure({
        key: process.env.STRIPE_PK,
        image: Logo,
        locale: 'auto',
        source: this.onSource,
      });
    },

    detailsAccess() {
      if (this.isLite) {
        return 'Access to:';
      }
      if (this.isPro) {
        return 'Access to The Starter plan +';
      }
      if (this.isBusi) {
        return 'Access to The Professional plan +';
      }
      return '';
    },

    detailsAmount() {
      if (this.isLite) {
        return '15 million';
      }
      if (this.isPro) {
        return '50 million';
      }
      if (this.isBusi) {
        return '500 million';
      }
      return '';
    },

    isBusi() {
      return this.item.tier === SubscriptionTier.INSIGHTS_AS_A_SERVICE;
    },

    isLite() {
      return this.item.tier === SubscriptionTier.LITE;
    },

    isPro() {
      return this.item.tier === SubscriptionTier.PROFESSIONAL;
    },

    priceText() {
      return new Intl.NumberFormat().format(this.item.price);
    },

    topName() {
      if (this.isLite) {
        return 'Starter';
      }
      if (this.isPro) {
        return 'Professional';
      }
      if (this.isBusi) {
        return 'Business Unit';
      }
      return '';
    },

    topLicense() {
      if (this.isLite) {
        return 'Monthly Software User Pass';
      }
      if (this.isPro) {
        return 'Annual License (Per User)';
      }
      if (this.isBusi) {
        return 'Annual license';
      }
      return '';
    },
  },

  data() {
    return {
      SubscriptionTier,
    };
  },

  methods: {
    ...mapActions('payment', ['setSource', 'setTier']),

    onClickBuy() {
      this.handler.open({
        amount: Number(this.item.price * 100),
        currency: 'usd',
        billingAddress: true,
        description: `Subscribe to tier: ${this.item.tier.titleCase()}`,
        name: 'Emotics',
        zipCode: true,
      });
    },

    onClickChat() {
      if (window.Intercom != null) {
        window.Intercom('show');
      }
    },

    onSource(source) {
      this.handler.close();

      this.setSource({ source });
      this.setTier({ tier: this.item.tier });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.account-subscriptions-tiers-item {
  @include flex("block", "column", "center", "center");
  @include panel;

  margin-right: 2%;
  min-height: 580px;
  width: 33%;
  position: relative;

  &:last-child {
    margin-right: 0;
  }

  .account-subscriptions-tiers-item-background {
    position: absolute;
    z-index: 0;
  }

  .gradient {
    /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#ffffff+0,ffffff+100&0+0,1+100 */
    background: -moz-linear-gradient(
      top,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 1) 70%
    ); /* FF3.6-15 */
    background: -webkit-linear-gradient(
      top,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 1) 70%
    ); /* Chrome10-25,Safari5.1-6 */
    background: linear-gradient(
      to bottom,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 1) 70%
    ); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00ffffff', endColorstr='#ffffff',GradientType=0 ); /* IE6-9 */

    height: 100%;
    position: absolute;
    width: 100%;
  }

  .foreground {
    @include flex("block", "column", "start", "start");
    @include grow;
    width: 100%;
    z-index: 99;

    .top {
      @include flex("block", "row", "start", "center");
      padding: 1.5rem 1.5rem 0.5rem 1.5rem;
      width: inherit;

      .name {
        font-size: $font-size-md;
        font-weight: $font-weight-bold;
        margin-right: 0.5rem;

        &.lite {
          color: darken(clr("red"), 5%);
        }

        &.pro {
          color: darken(clr("blue"), 5%);
        }

        &.busi {
          color: darken(clr("green"), 5%);
        }
      }
    }
    .top-license {
      @include flex("block", "row", "start", "center");
      border-bottom: $border-standard;
      padding: 0 1.5rem 1.5rem 1.5rem;
      width: 100%;
}

      .license {
        border-radius: 20px;
        color: clr("white");
        font-size: $font-size-xxs;
        font-weight: bold;
        padding: 0.1rem 0.3rem;
        text-transform: uppercase;

        &.lite {
          background-color: darken(clr("red"), 5%);
        }

        &.pro {
          background-color: darken(clr("blue"), 5%);
        }

        &.busi {
          background-color: darken(clr("green"), 5%);
        }
      }
    }

    .body {
      @include stretch;
      padding: 1.5rem 1.5rem;
      width:100%;

      .details {

        .access {
          font-size: 0.7rem;
          font-weight: $font-weight-bold;
          padding: 1.5rem 0;
          text-transform: uppercase;

          &.lite {
            color: lighten(clr("black"), 40%);
          }

          &.pro {
            color: darken(clr("red"), 5%);
          }

          &.busi {
            color: darken(clr("blue"), 5%);
          }
        }

        .amounts {
          @include flex("block", "row", "start", "start");
          font-size: $font-size-xs;
          padding-bottom: 1rem;

          .amount {
            font-weight: $font-weight-bold;
          }

          .icon {
            height: 0.8rem;
            margin-right: 0.5rem;
            width: 0.8rem;
          }
        }

        .detail {
          @include flex("block", "row", "start", "start");

          font-size: $font-size-xs;
          padding-bottom: 1rem;

          &:last-child {
            padding-bottom: 0;
          }

          .icon {
            height: 0.8rem;
            margin-right: 0.5rem;
            width: 0.8rem;
          }
        }
      }

      .pricing {
        @include flex("block", "row", "start", "end");

        .mark {
          font-size: 2rem;
        }

        .price {
          font-size: 2rem;
        }

        .duration {
          padding-bottom: 0.4rem;
          text-transform: lowercase;
          font-size: 0.8rem;
        }
      }
    }

    .actions {
      height: 4rem;
      padding: 0 2rem;
      width: inherit;

      .button {
        width: inherit;
      }

      .contact-link {
        text-decoration: none;
        width: inherit;

        .button {
          width: inherit;
        }
      }
    }
}
</style>
