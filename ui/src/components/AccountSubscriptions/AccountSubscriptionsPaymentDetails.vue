<template>
  <account-card class="subscription-payment-details">
    <span slot="header">Your New Subscription</span>
    <section class="details">
      <section class="name">{{ tier ? tier.titleCase() : '...' }} Plan</section>
      <section class="cost">${{ plan ? plan.cost : '...' }} / {{ plan ? plan.duration : '...' }}</section>
    </section>
  </account-card>
</template>

<script>
import { mapState } from 'vuex';

import AccountCard from '@/components/Account/AccountCard';

export default {
  name: 'subscription-payment-details',

  components: {
    AccountCard,
  },

  computed: {
    ...mapState('payment', ['plans', 'tier']),

    plan() {
      return this.plans[this.tier];
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.subscription-payment-details {
  .details {
    margin: 0.5rem 0;

    .name {
      font-weight: $font-weight-medium;
      font-size: $font-size-md;
    }

    .cost {
      font-size: $font-size-sm;
      margin-top: 0.3rem;
    }
  }
}
</style>
