<template>
  <account-card class="account-subscriptions-payment-review">
    <span slot="header">4. Review and Pay</span>
    <section class="payment">
      <section
        class="payment-text"
      >You have not yet been charged. Click below to confirm your payment. You will be billed the following amount immediately:</section>
      <section class="payment-total">
        <span>Total</span>
        <span>${{ plan ? plan.cost : '...' }}</span>
      </section>
      <section class="confirmation">
        <base-button @click="onClickPay" :disabled="!canPay || isPaying || hasPaid">
          <loader-icon v-if="isPaying" class="loader -spin"></loader-icon>
          <span v-else>Confirm &amp; Pay</span>
        </base-button>
        <alert-message v-if="!canPay" type="info">You are already subscribed to this tier</alert-message>
        <alert-message v-if="hasPaid" type="success">Payment confirmed. Thank you!</alert-message>
      </section>

      <section class="stripe-branding">
        <a href="https://www.stripe.com" target="_blank" rel="noopener noreferrer">
          <img class="stripe-logo" :src="stripeLogo" />
        </a>
      </section>
    </section>
  </account-card>
</template>

<script>
import { LoaderIcon } from 'vue-feather-icons';
import { mapGetters, mapState } from 'vuex';

import AccountCard from '@/components/Account/AccountCard';
import AlertMessage from '@/components/AlertMessage';
import BaseButton from '@/components/Base/BaseButton';
import stripeLogo from '@/assets/stripe-logo.svg';

import { paymentApi, userApi } from '@/services/api';

export default {
  name: 'account-subscriptions-payment-review',

  components: {
    AccountCard,
    AlertMessage,
    BaseButton,
    LoaderIcon,
  },

  data() {
    return {
      hasPaid: false,
      isPaying: false,
      stripeLogo,
    };
  },

  computed: {
    ...mapState('payment', ['plans', 'tier']),

    ...mapGetters('payment', ['sourceId']),

    ...mapGetters('user', ['extractTier']),

    canPay() {
      if (this.hasPaid) return true;

      return this.tier !== this.extractTier;
    },

    plan() {
      return this.plans[this.tier];
    },
  },

  methods: {
    async onClickPay() {
      this.isPaying = true;

      await paymentApi.confirmPayment(this.plans[this.tier].id, this.sourceId);

      this.isPaying = false;
      this.hasPaid = true;

      await userApi.getUser();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.account-subscriptions-payment-review {
  .payment {
    .payment-text {
      font-size: $font-size-sm;
      font-style: italic;
      margin: 0.5rem 0 1rem;
    }

    .payment-total {
      @include flex("block", "row", "between", "center");

      border-top: $border-standard;
      font-size: $font-size-md;
      font-weight: $font-weight-medium;
      padding: 1rem 0;
    }

    .confirmation {
      @include flex("block", "row", "start", "center");

      .base-button {
        @include rigid;

        margin: 1rem 0;
      }

      .loader {
        height: $font-size-lg;
        margin: -0.15rem 2rem;
      }

      .alert-message {
        @include rigid;

        margin-left: 1rem;
      }
    }

    .stripe-branding {
      @include flex("block", "row", "center", "center");

      width: 100%;

      .stripe-logo {
        height: 1.5rem;
      }
    }
  }
}
</style>
