<template>
  <account-card class="account-subscriptions-payment-contract">
    <span slot="header">2. Contract Term</span>
    <section class="contract-details">
      <h2>{{ plan ? plan.type : '...' }}</h2>
    </section>
  </account-card>
</template>

<script>
import { mapState } from 'vuex';

import AccountCard from '@/components/Account/AccountCard';

export default {
  name: 'account-subscriptions-payment-contract',

  components: {
    AccountCard,
  },

  computed: {
    ...mapState('payment', ['plans', 'tier']),

    plan() {
      return this.plans[this.tier];
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.account-subscriptions-payment-contract {
  .contract-details {
    margin: 1rem 0;
  }
}
</style>
