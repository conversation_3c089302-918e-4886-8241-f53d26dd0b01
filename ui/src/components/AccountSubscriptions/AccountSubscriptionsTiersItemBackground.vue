<template>
  <section class="account-subscriptions-tiers-item-background">
    <svg
      v-for="object in objects"
      :key="object"
      opacity="0.3"
      height="30"
      width="30"
      viewBox="0 0 30 30"
      :style="style()"
    >
      <polygon fill="none" :points="getPath()" :stroke="getColour()" stroke-width="3"></polygon>
    </svg>
  </section>
</template>

<script>
export default {
  name: 'account-subscriptions-tiers-item-background',

  props: {
    weight: {
      type: Number,
      default: 1,
    },
  },

  data() {
    return {
      colours: [
        '#F782FF',
        '#FCC419',
        '#5C7CFA',
        '#00E3AE',
        '#35D61A',
      ],
      height: 0,
      width: 0,
    };
  },

  computed: {
    objects() {
      return 5 * this.weight;
    },
  },

  mounted() {
    this.height = this.$el.getBoundingClientRect().height * 0.75;
    this.width = this.$el.getBoundingClientRect().width;
  },

  methods: {
    getColour() {
      return this.colours[Math.floor(Math.random() * 5)];
    },

    getPath() {
      const vertices = this.getVertices();
      const fac = Math.PI * 2 / vertices;
      const points = [];

      for (let i = 0; i < vertices; i += 1) {
        const x = 15 + 13 * Math.cos(i * fac + 180 * Math.PI);
        const y = 15 + 13 * Math.sin(i * fac + 180 * Math.PI);

        points.push(`${x},${y}`);
      }

      return points.join(' ');
    },

    style() {
      return {
        left: Math.floor(Math.random() * (this.width - 30)) + 15,
        top: Math.floor(Math.random() * (this.height - 30)) + 15,
      };
    },

    getVertices() {
      return Math.round(Math.random() * 3) + 3;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.account-subscriptions-tiers-item-background {
  height: 100%;
  overflow: hidden;
  width: 100%;

  svg {
    position: absolute;
  }
}
</style>
