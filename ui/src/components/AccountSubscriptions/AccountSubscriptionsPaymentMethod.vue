<template>
  <account-card
    class="account-subscriptions-payment-method"
    :editable="true"
    :editing="editingPayment"
    @edit="editingPayment = true"
  >
    <span slot="header">3. Payment Method</span>
    <span slot="link">Change</span>
    <section class="method-details">
      <credit-card-icon class="icon"/>
      <span class="card-number">•••• •••• •••• {{ card }}</span>
    </section>

    <section slot="editing" class="card-form">
      <account-payment-form @close="editingPayment = false"></account-payment-form>
    </section>
  </account-card>
</template>

<script>
import { CreditCardIcon } from 'vue-feather-icons';
import { mapState } from 'vuex';

import AccountCard from '@/components/Account/AccountCard';
import AccountPaymentForm from '@/components/Account/AccountPaymentForm';

export default {
  name: 'account-subscriptions-payment-method',

  components: {
    AccountCard,
    AccountPaymentForm,
    CreditCardIcon,
  },

  data() {
    return {
      editingPayment: false,
    };
  },

  computed: {
    ...mapState('payment', ['cards', 'source']),

    card() {
      if (this.source === null && this.cards === null) return '...';

      if (this.source === null) return this.cards.find(card => card.default).last4;

      return this.source.card.last4;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.account-subscriptions-payment-method {
  .method-details {
    @include flex("block", "row", "start", "center");

    .icon {
      margin-right: 1rem;
    }
  }

  .card-form {
    width: 100%;
  }
}
</style>
