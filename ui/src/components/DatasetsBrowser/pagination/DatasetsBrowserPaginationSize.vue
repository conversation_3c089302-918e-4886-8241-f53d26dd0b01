<template>
  <section class="datasets-browser-pagination-size" @click.stop="open = !open">
    <base-dropdown :data="limits" :open="open" @close="open = false" @select="onSelectLimit">
      <section class="dropdown-button">
        <i class="fa-solid fa-list icon-grid"></i>
        <span class="text">{{ showLimitText }}</span>
        <i class="fa fa-caret-down icon-caret" :class="{ open }"></i>
      </section>
    </base-dropdown>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import BaseDropdown from '@/components/Dropdown/BaseDropdown';

import { datasetsRequestV0 } from '@/services/request';

export default {
  name: 'datasets-browser-pagination-size',

  components: {
    BaseDropdown,
  },

  data() {
    return {
      open: false,
    };
  },

  computed: {
    ...mapState('datasets', ['limit', 'limits']),

    showLimitText() {
      return `${this.limit} Datasets`;
    },
  },

  methods: {
    ...mapActions('datasets', ['setLimit', 'setPage']),

    async onSelectLimit(item) {
      if (this.limit !== item.content) {
        this.setPage({ page: 1 });
        this.setLimit({ limit: item.content });
        await datasetsRequestV0.getDatasets();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';
@import '~font-awesome/css/font-awesome.min.css';

.datasets-browser-pagination-size {
  @include flex('block', 'row', 'start', 'center');
  border: 1px solid rgba(75, 114, 240, 0.15);
  border-radius: 3px;
  cursor: pointer;
  padding: 0.3rem 0.5rem;

  &:hover {
    border-color: rgba(75, 114, 240, 0.8);
  }

  .dropdown-button {
    @include flex("block", "row", "start", "center");
    color: $dataimport;

    &:hover {
      .text-value {
        color: lighten($dataimport, 20%);
      }
    }

    .icon-caret {
      font-weight: $font-weight-bold;
      margin-left: 0.5rem;
      transition: transform $interaction-transition-time;

      &.open {
        transform: rotateX(180deg);
      }
    }

    .icon-grid {
      margin-right: 0.5rem;
      height: $font-size-xs;
      width: $font-size-xs;
    }

    .text {
      font-size: 0.7rem;
      font-weight: $font-weight-bold;
      text-transform: uppercase;
      transition: all $interaction-transition-time;
    }
  }
}
</style>
