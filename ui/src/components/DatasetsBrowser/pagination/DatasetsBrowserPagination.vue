<template>
  <section class="datasets-browser-pagination">
    <span>Page:</span>
    <section
        v-for="(item, index) in pages"
        :key="index"
        class="paging-item"
        :class="item.class"
        @click="onClick(item.page)">
      <span>{{ item.page }}</span>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { datasetsRequestV0 } from '@/services/request';
import DatasetView from '@/enum/dataset-view';
import PaginationHelper from '@/helpers/pagination-helper';

const paginationHelper = new PaginationHelper();

export default {
  name: 'datasets-browser-pagination',

  data() {
    return {
      DatasetView,
    };
  },

  computed: {
    ...mapState('datasets', {
      currentPage: state => state.page,
      limit: state => state.limit,
      selected: state => state.selected,
      total: state => state.total,
      totalArchived: state => state.totalArchived,
      totalInProgress: state => state.totalInProgress,
      view: state => state.view,
    }),

    pages() {
      const { page, pages } = paginationHelper.getPages(
        this.currentPage,
        this.totalDatasets,
        this.limit,
      );
      if (page !== this.currentPage) {
        this.setPage({ page });
      }

      return pages.map(d => {
        return {
          page: d,
          class: this.getClass(d),
        };
      });
    },

    totalDatasets() {
      if (this.view === DatasetView.ARCHIVE) {
        return this.totalArchived;
      }
      if (this.view === DatasetView.IN_PROGRESS) {
        return this.totalInProgress;
      }
      if (this.view === DatasetView.SELECTED) {
        return this.selected.length;
      }
      return this.total;
    },
  },

  methods: {
    ...mapActions('datasets', ['setPage']),

    getClass(item) {
      if (typeof item === 'number') {
        if (item === this.currentPage) {
          return 'current-page';
        }
        return 'clickable';
      }
      return 'non-clickable';
    },

    async onClick(page) {
      if (typeof page === 'number' && page !== this.currentPage) {
        this.setPage({ page });
        await datasetsRequestV0.getDatasets();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';
@import '~font-awesome/css/font-awesome.min.css';

.datasets-browser-pagination {
  @include flex('block', 'row', 'start', 'center');
  color: $dataimport;
  font-size: $font-size-xs;
  font-weight: $font-weight-bold;
  margin-left: 1.5rem;
  text-transform: capitalize;

  .paging-item {
    margin-left: 0.5rem;

    &.clickable {
      cursor: pointer;
    }

    &.clickable:hover {
      color: clr("purple");
      font-size: $font-size-xs;
    }

    &.current-page {
      color: clr("purple");
      font-size: $font-size-xs;
      text-decoration: underline;
    }
  }
}
</style>
