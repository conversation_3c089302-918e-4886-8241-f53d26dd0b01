<template>
  <section class="datasets-browser-first-dataset">
    <section class="body">
      <img :src="require('@/assets/graphic-empty-state.png')"/>
      <h2 v-if="search">No Datasets Found</h2>
      <h2 v-else>Start a Data Project.</h2>

      <section v-if="search">
        <p>There are no datasets matching this search query.</p>
      </section>
      <section v-else>
        <p>
          Import a dataset for full comment emotion analysis and automatic insights.
        </p>
        <p>
          <a href="https://youtu.be/QQ1G5d25BOs" target="_blank">Watch this short how-to video</a>
        </p>
      </section>

      <base-button v-if="!search" class="import" icon="plus" @click="onClickImport">Add Dataset</base-button>
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import Route from '@/enum/route';

export default {
  name: 'datasets-browser-first-dataset',

  components: {
    BaseButton,
  },

  computed: {
    ...mapState('datasets', ['search']),
  },

  methods: {
    onClickImport() {
      this.$router.push({ name: Route.UPLOAD });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.datasets-browser-first-dataset {
  @include flex("block", "column", "start", "stretch");

  width: 100%;

  .body {
    @include flex("block", "column", "start", "center");
    @include stretch;

    img {
      width: 200px;
    }

    h2 {
      margin-top: 2rem;
      font-weight: $font-weight-bold;
    }

    p {
      font-size: $font-size-sm;
      margin-top: 1rem;
      text-align: center;

      a {
        color: clr('purple', 'rich');
        cursor: pointer;
        font-weight: $font-weight-medium;
        text-decoration: underline;
      }
    }

    .base-button {
      margin-top: 1rem;
      padding: 0.7rem;
      padding-right: 1.1rem;

      &.import {
        background-color: clr('purple', 'rich');
      }
    }
  }
}
</style>
