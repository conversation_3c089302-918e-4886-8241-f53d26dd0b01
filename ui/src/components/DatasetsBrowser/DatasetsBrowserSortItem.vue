<template>
  <section class="datasets-browser-sort-item" @click="onClick">
    <component :is="`${data.icon}-icon`" class="icon"></component>
    {{ data.value.titleCase() }}
  </section>
</template>

<script>
import { BarChartIcon, CalendarIcon, ClockIcon, Edit2Icon, HeartIcon } from 'vue-feather-icons';
import { mapActions, mapState } from 'vuex';

import intercomEvent from '@/services/IntercomEvent';
import intercomEvents from '@/enum/intercom-events';

import { datasetsRequestV0 } from '@/services/request';

export default {
  name: 'datasets-browser-sort-item',

  components: {
    BarChartIcon,
    CalendarIcon,
    ClockIcon,
    HeartIcon,
    Edit2Icon,
  },

  props: {
    data: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapState('datasets', ['sortedBy']),
  },

  methods: {
    ...mapActions('datasets', ['sort']),

    async onClick() {
      if (this.data.value !== this.sortedBy) {
        intercomEvent.send(intercomEvents.SORT_DATASETS);

        this.sort({ by: this.data.value });
        await datasetsRequestV0.getDatasets();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.datasets-browser-sort-item {
  @include flex("block", "row", "start", "center");

  color: $body-copy-light;
  cursor: pointer;
  font-size: $font-size-sm;
  padding: 0.5rem 0.7rem 0.5rem 0.5rem;
  transition: all $interaction-transition-time;

  &:hover {
    background-color: clr("blue", "lighter");
    color: $body-copy;
  }

  .icon {
    height: $font-size-sm;
    margin-right: 0.6rem;
    width: $font-size-sm;
  }
}
</style>
