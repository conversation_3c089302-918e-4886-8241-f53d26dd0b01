<template>
  <section class="datasets-browser-controls-tag-clear">
    <section class="clear-tag-btn" @click="clearAllTags">
      <tag-icon class="icon icon-tag" />
      <span class="text">Clear All Tags</span>
    </section>
  </section>
</template>

<script>
import { TagIcon } from 'vue-feather-icons';
import { mapActions, mapState } from 'vuex';

export default {
  name: 'datasets-browser-controls-tag-clear',

  components: {
    TagIcon,
  },

  computed: {
    ...mapState('datasetsTag', ['selectTagIds']),
  },

  methods: {
    ...mapActions('datasetsTag', ['removeSelectTagIds']),

    clearAllTags() {
      this.removeSelectTagIds({ tagIds: this.selectTagIds });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.datasets-browser-controls-tag-clear {
  @include flex("block", "column", "center", "start");
  margin-right: 0.5rem;

  .clear-tag-btn {
    @include flex("block", "row", "start", "center");
    border: 1px solid clr("purple");
    border-radius: 4px;
    color: clr("purple");
    cursor: pointer;
    padding: 0.4rem 0.6rem;

    &:hover {
      opacity: 0.8;

      .icon {
        color: clr("red");
      }
    }

    .icon {
      margin-right: 0.5rem;
      height: $font-size-xs;
      width: $font-size-xs;
    }

    .text {
      font-size: 0.65rem;
      font-weight: $font-weight-bold;
      text-transform: uppercase;
    }
  }
}
</style>
