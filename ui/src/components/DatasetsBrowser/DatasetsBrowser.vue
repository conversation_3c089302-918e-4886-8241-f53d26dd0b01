<template>
  <section class="datasets-browser">
    <loading-switch :status="datasetsStatus">
      <template #default>
        <datasets-browser-controls v-if="showDataset || hasFilters" />
        <section class="empty-result" v-if="!showDataset && hasFilters">
          No dataset(s) match the search query or contain the filter tags.
        </section>
        <datasets-browser-first-dataset v-else-if="!showDataset" />
        <datasets-browser-list v-if="showDataset" />
        <demo-datasets-browser-list v-if="showSample" />
      </template>
      <template #loading>
        <loading-blocks-overlay>Loading Datasets</loading-blocks-overlay>
      </template>
    </loading-switch>
  </section>
</template>

<script>
import { mapGetters, mapActions, mapState } from 'vuex';

import DatasetDisplay from '@/enum/dataset-display';
import DatasetsBrowserControls from '@/components/DatasetsBrowser/DatasetsBrowserControls';
import DatasetsBrowserFirstDataset from '@/components/DatasetsBrowser/DatasetsBrowserFirstDataset';
import DatasetsBrowserList from '@/components/DatasetsBrowser/DatasetsBrowserList';
import DemoDatasetsBrowserList from '@/components/DatasetsBrowser/Demo/DemoDatasetsBrowserList';
import dragSelect from '@/services/DragSelect';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import LoadingSwitch from '@/components/LoadingSwitch';
import NetworkKeys from '@/enum/network-keys';
import NetworkStatus from '@/enum/network-status';

export default {
  name: 'datasets-browser',

  components: {
    DatasetsBrowserControls,
    DatasetsBrowserFirstDataset,
    DatasetsBrowserList,
    DemoDatasetsBrowserList,
    LoadingBlocksOverlay,
    LoadingSwitch,
  },

  data() {
    return {
      ds: null,
      stopped: false,
    };
  },

  created() {
    this.setDatasetDisplay({ display: DatasetDisplay.LIST });
  },

  mounted() {
    // disable drag event to avoid click dropdown bugs, we can turn it on later
    // dragSelect.init({
    //   area: this.$el,
    //   callback: this.selectableCallback,
    //   onDragStart: this.onDragStart,
    // });
  },

  beforeDestroy() {
    dragSelect.reset();
  },

  computed: {
    ...mapGetters('network', ['status']),

    ...mapGetters('user', ['hasSampleDataset']),

    ...mapState('datasets', [
      'datasets',
      'sampleDatasets',
      'search',
    ]),

    ...mapState('datasetsTag', ['selectTagIds']),

    datasetsStatus() {
      if (this.datasets == null || this.datasets.length === 0) {
        return this.status(NetworkKeys.DATASETS);
      }

      return NetworkStatus.SUCCESS;
    },

    hasFilters() {
      return this.search?.trim().length > 0 || this.selectTagIds?.length > 0;
    },

    showDataset() {
      if (this.hasSampleDataset) {
        return this.datasets.length > this.sampleDatasets.length;
      }
      return this.datasets.length;
    },

    showSample() {
      return this.sampleDatasets.length && this.hasSampleDataset;
    },
  },

  methods: {
    ...mapActions('datasets', ['select', 'setSelecting']),

    ...mapActions(['setDatasetDisplay']),

    onDragStart() {
      this.setSelecting({ selecting: true });
    },

    selectableCallback(elements) {
      this.setSelecting({ selecting: false });
      this.select({ ids: [...elements.map(element => Number(element.dataset.id))] });

      window.getSelection().empty();
      dragSelect.clear();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.datasets-browser {
  @include flex("block", "column", "start", "stretch");
  @include stretch;

  min-height: 0;
  // min-width: fit-content;
  padding: 1rem 0.5rem;

  .empty-result {
    @include flex("block", "row", "center", "center");
    color: #352691;
    font-size: $font-size-lg;
    height: 5rem;
    opacity: 0.8;
    padding: 10rem 1rem;
  }

  .datasets-browser-first-dataset {
    margin: 1rem 0;
    height: 100%;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .datasets-browser-list {
    z-index: 1;
  }

  .demo-datasets-browser-list {
    z-index: 0;
  }

  .loading-blocks-overlay {
    height: 100%;
  }
}
</style>
