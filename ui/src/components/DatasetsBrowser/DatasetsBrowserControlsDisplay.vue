<template>
  <section class="datasets-browser-controls-display">
    View
    <component
      v-for="item in items"
      :is="`${item.icon}-icon`"
      :key="item.value.name"
      class="icon"
      :class="{ active: item.value === datasetDisplay, isList: item.icon === 'list' }"
      @click="setDisplay(item.value)"
    ></component>
  </section>
</template>

<script>
import { GridIcon, ListIcon } from 'vue-feather-icons';
import { mapActions, mapState } from 'vuex';

import DatasetDisplay from '@/enum/dataset-display';

export default {
  name: 'datasets-browser-controls-display',

  components: {
    GridIcon,
    ListIcon,
  },

  computed: {
    ...mapState(['datasetDisplay']),

    items() {
      return [{ value: DatasetDisplay.LIST, icon: 'list' }, { value: DatasetDisplay.GRID, icon: 'grid' }];
    },
  },

  methods: {
    ...mapActions(['setDatasetDisplay']),

    setDisplay(display) {
      this.setDatasetDisplay({ display });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.datasets-browser-controls-display {
  @include flex('block', 'row', 'center', 'center');
  color: $dataimport;
  font-size: $font-size-xs;
  font-weight: $font-weight-bold;
  margin-right: 1rem;

  .icon {
    color: $body-copy-light;
    cursor: pointer;
    height: $font-size-lg;
    margin-left: 0.5rem;
    transition: all $interaction-transition-time;

    &:hover,
    &.active {
      color: $dataimport;
    }

    &.isList {
      height: 1.5rem;
    }
  }
}
</style>
