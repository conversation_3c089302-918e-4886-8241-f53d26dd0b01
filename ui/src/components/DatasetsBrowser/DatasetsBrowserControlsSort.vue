<template>
  <section class="datasets-browser-controls-sort">
    <section class="controls-sort sort-direction">
      <base-dropdown :data="sortDirectionOpts"
                     :open="sortDirectionOpen"
                     @close="sortDirectionOpen = false"
                     @select="onSelectSortDirection">
        <section class="dropdown-button" @click.stop="sortDirectionOpen = !sortDirectionOpen">
          <arrow-up-icon class="icon icon-arrow" :class="{ open: isDescending }"/>
          <span class="text text-title">Order:</span>
          <span class="text text-value">{{ datasetSortDirection.fullName }}</span>
          <i class="fa fa-caret-down icon-caret" :class="{ open: sortDirectionOpen }"></i>
        </section>
      </base-dropdown>
    </section>
    <section class="controls-sort sort">
      <base-dropdown :component="dropdownComponent"
                     :data="sortByOpts"
                     :open="sortByOpen"
                     @close="sortByOpen = false">
        <section class="dropdown-button" @click.stop="sortByOpen = !sortByOpen">
          <component :is="`${icon}-icon`" class="icon"></component>
          <span class="text text-title">Sort:</span>
          <span class="text text-value">{{ sortedBy.titleCase() }}</span>
          <i class="fa fa-caret-down icon-caret" :class="{ open: sortByOpen }"></i>
        </section>
      </base-dropdown>
    </section>
  </section>
</template>

<script>
import { ArrowUpIcon, BarChartIcon, CalendarIcon, ClockIcon, Edit2Icon, HeartIcon } from 'vue-feather-icons';
import { mapActions, mapState } from 'vuex';

import BaseDropdown from '@/components/Dropdown/BaseDropdown';
import DatasetsBrowserSortItem from '@/components/DatasetsBrowser/DatasetsBrowserSortItem';
import DatasetSort from '@/enum/dataset-sort';
import { datasetsRequestV0 } from '@/services/request';
import SortDirection from '@/enum/sort-direction';

const iconMap = {
  [DatasetSort.CREATED]: 'calendar',
  [DatasetSort.NAME]: 'edit-2',
  [DatasetSort.SCORE]: 'heart',
  [DatasetSort.UPDATED]: 'clock',
  [DatasetSort.VOLUME]: 'bar-chart',
};

export default {
  name: 'datasets-browser-controls-sort',

  components: {
    ArrowUpIcon,
    BarChartIcon,
    BaseDropdown,
    CalendarIcon,
    ClockIcon,
    Edit2Icon,
    HeartIcon,
  },

  data() {
    return {
      dropdownComponent: DatasetsBrowserSortItem,
      sortByOpen: false,
      sortDirectionOpen: false,
    };
  },

  computed: {
    ...mapState('datasets', {
      datasetSortDirection: state => state.sortDirection,
      sortedBy: state => state.sortedBy,
    }),

    icon() {
      return iconMap[this.sortedBy];
    },

    isDescending() {
      return this.datasetSortDirection === SortDirection.DESC;
    },

    sortByOpts() {
      return DatasetSort.enumValues.map(value => {
        return {
          icon: iconMap[value],
          value,
        };
      });
    },

    sortDirectionOpts() {
      return SortDirection.enumValues.map(value => {
        return {
          content: value.fullName,
          value,
        };
      });
    },
  },

  methods: {
    ...mapActions('datasets', ['setSortDirection']),

    async onSelectSortDirection(item) {
      if (item.value !== this.datasetSortDirection) {
        this.setSortDirection({ sortDirection: this.datasetSortDirection.inverse });
        await datasetsRequestV0.getDatasets();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.datasets-browser-controls-sort {
  @include flex("block", "row", "start", "center");

  .controls-sort {
    @include flex("block", "row", "start", "center");
    border: 1px solid $pag-btn;
    border-radius: 3px;
    cursor: pointer;
    padding: 0.3rem 0.5rem;

    &:hover {
      border: 1px solid $pag-btn-hvr;
    }

    &.sort-direction {
      margin-right: 0.5rem;
    }

    .dropdown-button {
      @include flex("block", "row", "start", "center");
      color: $dataimport;

      &:hover {
        .text-value {
          color: lighten($dataimport, 20%);
        }
      }

      .icon {
        margin-right: 0.5rem;
        height: $font-size-xs;
        width: $font-size-xs;
      }

      .icon-caret {
        margin-left: 0.5rem;
        transition: transform $interaction-transition-time;

        &.open {
          transform: rotateX(180deg);
        }
      }

      .text {
        font-size: 0.65rem;
        text-transform: uppercase;
        transition: all $interaction-transition-time;

        &.text-title {
          font-weight: $font-weight-bold;
          margin-right: 0.2rem;
        }

        &.text-value {
          font-weight: $font-weight-normal;
        }
      }
    }
  }
}
</style>
