<template>
  <section class="datasets-browser-header">
    <section class="tab-btns">
      <section class="tab-btn tab-dataset"
               :class="{ inactive: !viewingDatasetManagementTab }"
               @click="viewDatasetTab()"
      >
        <i class="fa fa-database icon"></i>
        <span class="name">Datasets</span>
      </section>
      <section class="tab-btn tab-tag"
               :class="{ inactive: !viewingTagManagementTab }"
               @click="viewTagTab()"
      >
        <i class="fa fa-tags icon"></i>
        <span class="name">Tags</span>
      </section>
      <section class="tab-btn tab-memory"
               :class="{ inactive: !viewingMemoryPageTab }"
               @click="viewMemoryTab()"
      >
        <i class="fa fa-save icon"></i>
        <span class="name">Memory</span>
      </section>
               <section v-if="hasFeature('surveys')"
               class="tab-btn tab-survey"
               :class="{ inactive: !viewingSurveyManagementTab }"
               @click="viewHeartbeatTab()"
      >
        <img :src="viewingSurveyManagementTab ? require('@/assets/heartbeat/icon-heartbeat.svg') : require('@/assets/heartbeat/icon-heartbeat-grey.svg')" class="fa icon" />
        <span class="name">Heartbeat</span>
      </section>
    </section>
    <section class="divider" v-if="!showActionBuilderPanelLeft && !showSurveyBuilder" />
    <section class="search-bar" v-if="!showActionBuilderPanelLeft && !showSurveyBuilder">
      <datasets-search v-if="viewingDatasetManagementTab && (showDataset || hasFilters)" />
      <datasets-tags-search v-if="viewingTagManagementTab" />
      <datasets-surveys-search v-if="viewingSurveyManagementTab" />
      <datasets-memories-search v-if="viewingMemoryPageTab" />
      <section class="new-button">
        <!-- new dataset btn -->
        <base-button v-if="viewingDatasetManagementTab && isEditor && (showDataset || hasFilters)"
                     colour="data"
                     class="new-dataset-btn"
                     icon="plus"
                     size="base"
                     type="base"
                     @click="onClickNewDataset">
          Add Dataset
        </base-button>
        <!-- new tag btn -->
        <base-button v-if="viewingTagManagementTab && isEditor"
                     colour="data"
                     class="new-tag-btn"
                     icon="plus"
                     size="base"
                     type="base"
                     @click="onClickNewTag">
          Add Tag
        </base-button>
        <!-- new survey btn -->
        <base-button v-if="viewingSurveyManagementTab && isEditor"
                     colour="data"
                     class="new-survey-btn"
                     icon="plus"
                     size="base"
                     type="base"
                     @click="onClickNewSurvey">
          Create FeedBack Pulse
        </base-button>
        <base-button v-if="viewingMemoryPageTab"
            colour="dark"
            class="new-memory-btn"
            icon="plus"
            size="small"
            type="base"
            @click="onClickNewMemoryAction">
          Create Action
        </base-button>
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';
import { surveysRequest } from '@/services/request';
import { surveyApi } from '@/services/api';

import BaseButton from '@/components/Base/BaseButton';
import DatasetsMemoriesSearch from '@/components/MemoryPageBrowser/DatasetsMemoriesSearch';
import DatasetsSearch from '@/components/Datasets/DatasetsSearch';
import DatasetsSurveysSearch from '@/components/Survey/DatasetsSurveysSearch';
import DatasetsTagsModal from '@/components/DatasetsTags/DatasetsTagsModal';
import DatasetsTagsSearch from '@/components/DatasetsTags/DatasetsTagsSearch';
import NewSurveyModal from '@/components/Survey/Builder/NewSurveyModal';
import Route from '@/enum/route';

export default {
  name: 'datasets-browser-header',

  components: {
    BaseButton,
    DatasetsMemoriesSearch,
    DatasetsSearch,
    DatasetsSurveysSearch,
    DatasetsTagsSearch,
    NewSurveyModal,
  },

  created() {
  },

  computed: {
    ...mapGetters('datasets', ['count']),

    ...mapState('datasets', [
      'datasets',
      'sampleDatasets',
      'search',
    ]),

    ...mapState('datasetManagementTabs', [
      'viewingDatasetManagementTab',
      'viewingTagManagementTab',
      'viewingSurveyManagementTab',
      'viewingMemoryPageTab',
    ]),

    ...mapState('datasetsTag', ['selectTagIds']),

    ...mapState('savedActions', ['savedActions']),

    ...mapState('savedActionsBuilder', ['showActionBuilderPanelLeft']),

    ...mapState('surveys', ['showSurveyBuilder']),

    ...mapGetters('user', ['hasSampleDataset', 'isEditor']),

    ...mapGetters('user', ['hasFeature']),

    hasFilters() {
      return this.search?.trim().length > 0 || this.selectTagIds?.length > 0;
    },

    showDataset() {
      if (this.hasSampleDataset) {
        return this.datasets.length > this.sampleDatasets.length;
      }
      return this.datasets.length;
    },

    showDivider() {
      if (this.viewingDatasetManagementTab) {
        return (this.showDataset || this.hasFilters);
      }
      return !this.viewingMemoryPageTab;
    },
  },

  methods: {

    ...mapActions('datasetsTag', ['setEditTagId']),

    ...mapActions('datasetManagementTabs', ['setTab']),

    ...mapActions('modal', ['setModalComponent']),

    ...mapActions('savedActions', ['selectSavedAction']),

    ...mapActions('savedActionsBuilder', ['setShowActionBuilderPanelLeft']),

    ...mapActions('savedActionsBuilder', { resetBuilder: 'reset' }),

    ...mapActions('surveys', [
      'deselectAllSurveys',
      'setPage',
      'setSearch',
      'setSelectedSurvey',
      'setShowSurveyBuilder',
    ]),

    onClickNewDataset() {
      this.$router.push({ name: Route.UPLOAD });
    },

    onClickNewMemoryAction() {
      this.selectSavedAction({ id: null });
      this.resetBuilder();
      this.setShowActionBuilderPanelLeft({ value: true });
    },

    onClickNewTag() {
      this.setEditTagId({ id: null });
      this.setModalComponent({ component: DatasetsTagsModal });
    },

    onClickNewSurvey() {
      this.setSelectedSurvey({ survey: surveyApi.getDefaultSurvey() });
      this.setModalComponent({ component: NewSurveyModal });
    },

    viewTagTab() {
      if (this.$router.currentRoute?.query?.tab !== Route.TAGS.lowerCase()) {
        this.$router.push({ query: { tab: Route.TAGS.lowerCase() } });
      }
      this.setTab({ tab: Route.TAGS.lowerCase() });
    },

    async viewHeartbeatTab() {
      if (this.$router.currentRoute?.query?.tab !== Route.HEARTBEAT.lowerCase()) {
        await this.$router.push({ query: { tab: Route.HEARTBEAT.lowerCase() } });
      }
      await surveysRequest.getSurveys();
      this.setTab({ tab: Route.HEARTBEAT.lowerCase() });
    },

    viewDatasetTab() {
      if (this.$router.currentRoute?.query?.tab) {
        this.$router.push({ query: {} });
      }
      this.setTab({ tab: '' });
    },

    viewMemoryTab() {
      if (this.$router.currentRoute?.query?.tab !== Route.MEMORY.lowerCase()) {
        this.$router.push({ query: { tab: Route.MEMORY.lowerCase() } });
      }
      this.setTab({ tab: Route.MEMORY.lowerCase() });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.datasets-browser-header {
  @include flex('block', 'column', 'start', 'start');
  @include stretch;

  align-self: start;
  background-color: #FFFFFF;

  .tab-btns {
    @include flex('block', 'row', 'start', 'center');
    z-index: 999;

    .icon {
      margin-right: 0.5rem;
    }

    .tab-btn {
      @include flex('block', 'row', 'start', 'center');

      background-color: #2D1757;
      border: 1px solid #2D1757;
      border-radius: $border-radius-small;
      color: #FFFFFF;
      cursor: pointer;
      font-size: $font-size-xxs;
      font-weight: $font-weight-bold;
      letter-spacing: $letter-spacing-sm;
      min-width: fit-content;
      padding: 0.5rem 1rem;
      text-transform: uppercase;

      &.inactive {
        background-color: transparent;
        border: 1px solid rgba(45, 23, 87, 0.15);
        color: #9A9A9A;
        opacity: 0.7;

        &:hover {
          border-color: $border-color-hover;
          filter: brightness(100%);
          opacity: 1;
        }
      }

      &:hover {
        filter: brightness(125%);
      }

      &.tab-dataset {
        border-radius: 4px 0 0 4px;
        border-right-color: transparent;

        .inactive {
          border-right: none;
        }
      }

      &.tab-survey.inactive {
        border-left-color: transparent;
      }

      &.tab-memory.inactive {
        border-left-color: transparent;
      }

      &.tab-memory.inactive {
        border-left-color: transparent;
      }
    }
  }

  .browser-search {
    @include flex('block', 'row', 'start', 'center');
    width: 100%;
  }

  .divider {
    border-bottom: 1px solid #DFE1E4;
    margin: 1.5rem 0 0 0;
    width: 100%;
  }

  .search-bar {
    @include flex('block', 'row', 'start', 'stretch');

    column-gap: 1rem;
    height: 36px; // to avoid dancing height
    margin: 1.5rem 0 0 0;
    width: 100%;

    .new-button {
      .base-button {
        color: #FFFFFF;
        font-size: $font-size-xxs;
        font-weight: $font-weight-bold;
        padding: 0.7rem 2.2rem 0.7rem 1.5rem;
        text-align: center;
        text-transform: uppercase;
      }
    }
  }
}
</style>
