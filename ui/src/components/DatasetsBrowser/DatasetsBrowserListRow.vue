<script>
import Analysing from '@/components/DatasetsAnalysing/DatasetsAnalysing';
import Dataset from '@/components/DatasetsItem/DatasetsItem';

export default {
  name: 'datasets-browser-list-row',

  functional: true,

  props: {
    row: {
      type: Array,
      required: true,
    },
  },

  render(h, ctx) {
    const items = ctx.props.row.map(item => {
      return h(item.type === 'analysis' ? Analysing : Dataset, {
        props: {
          id: item.id,
        },
      });
    });

    return h('section', { class: 'datasets-browser-list-row' }, [...items]);
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.datasets-browser-list-row {
  @include flex("block", "row", "start", "stretch");
  @include rigid;

  position: relative;
}
</style>
