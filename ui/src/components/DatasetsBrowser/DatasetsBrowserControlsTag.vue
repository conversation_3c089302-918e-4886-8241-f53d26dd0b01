<template>
  <section class="datasets-browser-controls-tag"
           v-click-outside-handler="{ handler: 'onClickOutside' }">
    <section class="view-tag-btn" @click="hideDropdown = !hideDropdown">
      <i class="fa-solid fa-tag icon-tag"></i>
      <span class="text text-label">Tags:</span>
      <span class="text text-count">{{ textTagCount }}</span>
      <i class="fa fa-caret-down icon-caret" :class="{ open: !hideDropdown }"></i>
    </section>
    <section class="view-tag-list" :class="{ hide: hideDropdown }">
      <section class="list-search">
        <search-icon class="icon"/>
        <base-input placeholder="Type to search Tags..." v-model="localSearchTag"/>
      </section>
      <section class="empty-list" v-if="tags.length === 0">
        Nothing here yet. Try to create a new tag.
      </section>
      <section class="empty-list" v-else-if="dataList.length === 0">
        None tag matches the search query.
      </section>
      <section class="list-body" v-else>
        <section class="item"
                 v-for="(tag, i) in dataList"
                 :key="i"
                 @click="doSelectTag(tag.id)"
        >
          <base-checkbox :value="isSelected(tag.id)"/>
          <base-tag :colour="tag.colour" :max-width="'15rem'" :selected="isSelected(tag.id)" :text="tag.name" />
        </section>
      </section>
      <section class="list-footer">
        <base-button colour="danger"
                     size="base"
                     type="outline"
                     @click="onClickClearAll">
          Clear All
        </base-button>
      </section>
    </section>
  </section>
</template>

<script>
import { debounce } from 'lodash-es';
import { SearchIcon } from 'vue-feather-icons';
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseCheckbox from '@/components/Base/BaseCheckbox';
import BaseInput from '@/components/Base/BaseInput';
import BaseTag from '@/components/Base/BaseTag';
import clickOutsideHandler from '@/directives/click-outside-handler';

import { datasetsRequestV0 } from '@/services/request';

export default {
  name: 'datasets-browser-controls-tag',

  components: {
    BaseButton,
    BaseCheckbox,
    BaseInput,
    BaseTag,
    SearchIcon,
  },

  directives: {
    clickOutsideHandler,
  },

  computed: {
    ...mapState('datasetsTag', ['selectTagIds', 'tags']),

    dataList() {
      return this.tags.filter(t => {
        return this.localSearchTag?.trim().length
          ? t.name.indexOf(this.localSearchTag.trim().toLowerCase().replace(/\s\s+/g, ' ')) !== -1
          : true;
      });
    },

    textTagCount() {
      if (!this.selectTagIds?.length) {
        return 'All';
      }

      return this.selectTagIds
        .map(id => this.tags.find(t => t.id === id).name)
        .join(', ');
    },
  },

  data() {
    return {
      hideDropdown: true,
      localSearchTag: '',
    };
  },

  methods: {
    ...mapActions('datasetsTag', ['addSelectTagIds', 'removeSelectTagIds']),

    debounceReloadDatasets: debounce(async () => {
      await datasetsRequestV0.getDatasets();
    }, 500),

    doSelectTag(id) {
      if (this.isSelected(id)) {
        this.removeSelectTagIds({ tagIds: [id] });
      } else {
        this.addSelectTagIds({ tagIds: [id] });
      }
    },

    isSelected(id) {
      return this.selectTagIds?.includes(id) || false;
    },

    onClickClearAll() {
      this.removeSelectTagIds({ tagIds: this.selectTagIds });
    },

    onClickOutside() {
      this.hideDropdown = true;
    },
  },

  watch: {
    async selectTagIds() {
      await this.debounceReloadDatasets();
    },

    hideDropdown() {
      this.localSearchTag = '';
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.datasets-browser-controls-tag {
  @include flex("block", "column", "center", "start");
  position: relative;

  .view-tag-btn {
    @include flex("block", "row", "start", "center");
    color: #5F52C5;
    cursor: pointer;
    padding: 0.4rem 0.6rem;

    .icon-tag {
      margin-right: 0.3rem;
      font-size: $font-size-xs;
    }

    .text {
      font-size: $font-size-xs;
      text-transform: uppercase;

      &.text-label {
        font-weight: $font-weight-bold;
      }

      &.text-count {
        @include truncate;
        margin-left: 0.2rem;
        max-width: 15rem;
      }
    }

    .icon-caret {
      font-size: $font-size-xs;
      font-weight: $font-weight-bold;
      padding-left: 0.2rem;
      transition: transform $interaction-transition-time;

      &.open {
        transform: rotateX(180deg);
      }
    }
  }

  .view-tag-list {
    @include flex("block", "column", "start", "start");
    background-color: clr("white");
    border: 1px solid $border-color;
    border-radius: $border-radius-medium;
    box-shadow: 0 0 3px 3px rgba(0, 0, 0, 0.06);
    position: absolute;
    top: 2rem;
    z-index: 2;

    &.hide {
      display: none;
    }

    .list-search {
      @include flex("block", "row", "start", "center");

      background-color: clr('white');
      border: $border-purple;
      border-radius: $border-radius-medium;
      margin: 1rem;
      padding: 0 0.5rem;
      width: 18rem;

      .icon {
        color: $border-color-purple;
        height: $font-size-xs;
        width: $font-size-xs;
      }

      .base-input {
        border: none;
        font-size: $font-size-xs;

        &::placeholder {
          color: $border-color-purple;
          font-style: italic;
          opacity: 0.5;
          pointer-events: none;
        }
      }
    }

    .empty-list {
      border-top: 1px solid lighten($border-color, 5%);
      color: #352691;
      font-size: $font-size-xs;
      height: 150px;
      opacity: 0.8;
      padding: 1rem;
      width: 100%;
    }

    .list-body {
      @include flex("block", "column", "start", "start");
      @include scrollbar-thin;
      border-top: 1px solid lighten($border-color, 5%);
      max-height: 350px;
      min-height: 150px;
      overflow-y: auto;
      width: 100%;

      .item {
        @include flex("block", "row", "start", "center");
        cursor: pointer;
        padding: 0.5rem 1rem;
        width: 100%;

        &:hover {
          background-color: clr("grey", "100");
        }

        .base-checkbox {
          margin-right: 0.2rem;
        }
      }
    }

    .list-footer {
      @include flex("block", "row", "end", "center");
      border-top: 1px solid lighten($border-color, 5%);
      padding: 1rem;
      text-transform: uppercase;
      width: 100%;

      .base-button {
        font-size: $font-size-xs;
        padding: 0.3rem 1rem;
      }
    }
  }
}
</style>
