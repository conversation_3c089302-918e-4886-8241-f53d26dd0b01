<template>
  <section class="datasets-browser-list"
           :class="{ 'demo-exists': hasSampleDataset }"
           :style="{ 'padding-bottom': isActionBarVisible ? '4rem' : '0' }"
  >
    <section class="list" ref="list" :remain="rows">
      <datasets-browser-list-row v-for="(row, index) in items" :key="index" :row="row"></datasets-browser-list-row>
    </section>
    <datasets-browser-pagination />
  </section>
</template>

<script>
import { chunk, debounce } from 'lodash-es';
import { mapGetters, mapState } from 'vuex';

import DatasetDisplay from '@/enum/dataset-display';
import DatasetsBrowserListRow from '@/components/DatasetsBrowser/DatasetsBrowserListRow';
import DatasetsBrowserPagination from '@/components/DatasetsBrowser/pagination/DatasetsBrowserPagination';
import Route from '@/enum/route';

export default {
  name: 'datasets-browser-list',

  components: {
    DatasetsBrowserListRow,
    DatasetsBrowserPagination,
  },

  data() {
    return {
      height: 0,
      itemHeight: 85,
      minItemWidth: 350,
      width: 0,
    };
  },

  computed: {
    ...mapState(['datasetDisplay']),

    ...mapState('datasets', ['datasets', 'selected']),

    ...mapGetters('user', ['hasSampleDataset']),

    items() {
      return chunk(
        this.datasets
          .filter(d => !d.localSample && d.status !== 'filtered')
          .map(item => {
            return {
              id: item.id,
              type: item.status === 'finished' ? 'dataset' : 'analysis',
            };
          }),
        this.rowSize,
      );
    },

    rows() {
      return Math.floor(this.height / this.itemHeight) + 10;
    },

    rowSize() {
      if (this.datasetDisplay === DatasetDisplay.LIST) return 1;

      return Math.floor(this.width / this.minItemWidth);
    },

    isActionBarVisible() {
      return this.selected.length > 1 && this.$route.name === Route.DATASETS;
    },
  },

  watch: {
    datasetDisplay() {
      this.onResize();
      // this.$refs.list.forceRender();
    },

    items() {
      this.onResize();
      // this.$refs.list.forceRender();
    },
  },

  mounted() {
    if (this.$el) {
      this.width = this.$el.getBoundingClientRect().width;
      this.height = this.$el.getBoundingClientRect().height;

      window.addEventListener('resize', this.onResize);
    }
  },

  destroy() {
    window.removeEventListener('resize', this.onResize);
  },

  methods: {
    onResize: debounce(function onResize() {
      this.height = this.$el.getBoundingClientRect().height;
      this.width = this.$el.getBoundingClientRect().width;
    }, 50),
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.datasets-browser-list {
  @include flex("block", "column", "start", "stretch");
  animation: $load-up;
  margin: 1rem 0 3.5rem;
  z-index: 0;

  &.demo-exists {
    padding-bottom:1.5rem;
  }

  & > div {
    padding-top: 1px;
  }

  .list {
    .datasets-browser-list-row {

      .datasets-analysing {
        border-bottom: 1px solid $border-color;
        border-radius: 0;
      }
    }
  }

  .datasets-browser-pagination {
    margin-top: 1rem;
  }
}
</style>
