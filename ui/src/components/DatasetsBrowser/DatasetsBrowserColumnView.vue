<template>
  <section class="datasets-browser-column-view" @click.stop="open = !open">
    <section class="dropdown-button">
      <i class="fa-solid fa-eye icon-eye"></i>
      <span class="text">Cols</span>
      <i class="fa fa-caret-down icon-caret" :class="{ open }"></i>
    </section>
    <datasets-browser-column-view-drop-down
        v-if="open"
        @close="open = false"
    />
  </section>
</template>

<script>
import DatasetsBrowserColumnViewDropDown from '@/components/DatasetsBrowser/DatasetsBrowserColumnViewDropDown';

export default {
  name: 'datasets-browser-column-view',

  components: {
    DatasetsBrowserColumnViewDropDown,
  },

  data() {
    return {
      open: false,
    };
  },

  computed: {
  },

  methods: {
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.datasets-browser-column-view {
  @include flex('block', 'row', 'start', 'center');

  border: 1px solid rgba(75, 114, 240, 0.15);
  border-radius: 3px;
  cursor: pointer;
  padding: 0.3rem 0.5rem;

  &:hover {
    border-color: rgba(75, 114, 240, 0.8);
  }

  .dropdown-button {
    @include flex("block", "row", "start", "center");

    color: #2D1757;

    .icon-caret {
      font-weight: $font-weight-bold;
      margin-left: 0.5rem;
      transition: transform $interaction-transition-time;

      &.open {
        transform: rotateX(180deg);
      }
    }

    .icon-eye {
      margin-right: 0.5rem;
      height: $font-size-xs;
      width: $font-size-xs;
    }

    .text {
      font-size: 0.7rem;
      text-transform: uppercase;
      font-weight: $font-weight-bold;
      transition: all $interaction-transition-time;
    }
  }
}
</style>
