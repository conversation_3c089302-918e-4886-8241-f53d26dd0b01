<template>
  <section class="demo-datasets-browser-list">
    <section class="title">
      <i class="fa fa-window-maximize icon"></i>
      <h3>{{ titleText }}</h3>
    </section>
    <section class="select-all" @click="onSelectAll">
      <base-checkbox :value="areAllDemoSelected" :size1x="true"></base-checkbox>
      Select all demo datasets
    </section>
    <section class="list" ref="list" :remain="rows">
      <demo-datasets-browser-list-row v-for="(row, index) in items" :key="index" :row="row"></demo-datasets-browser-list-row>
    </section>
  </section>
</template>

<script>
import { chunk, debounce } from 'lodash-es';
import { mapActions, mapGetters, mapState } from 'vuex';
import BaseCheckbox from '@/components/Base/BaseCheckbox';
import DatasetDisplay from '@/enum/dataset-display';
import DemoDatasetsBrowserListRow from '@/components/DatasetsBrowser/Demo/DemoDatasetsBrowserListRow';

export default {
  name: 'demo-datasets-browser-list',

  components: {
    BaseCheckbox,
    DemoDatasetsBrowserListRow,
  },

  data() {
    return {
      height: 0,
      itemHeight: 85,
      minItemWidth: 350,
      width: 0,
    };
  },

  computed: {
    ...mapGetters('datasets', ['areAllDemoSelected']),

    ...mapState(['datasetDisplay']),

    ...mapState('datasets', ['datasets', 'sampleDatasets']),

    items() {
      return chunk(this.datasets.filter(d => d.localSample).map(item => {
        return {
          id: item.id,
          type: item.status === 'finished' ? 'dataset' : 'analysis',
        };
      }), this.rowSize);
    },

    rows() {
      return Math.floor(this.height / this.itemHeight) + 10;
    },

    rowSize() {
      if (this.datasetDisplay === DatasetDisplay.LIST) return 1;

      return Math.floor(this.width / this.minItemWidth);
    },

    titleText() {
      if (!this.sampleDatasets?.length) {
        return '0 Demo Dataset';
      }
      if (this.sampleDatasets.length === 1) {
        return '1 Demo Dataset';
      }
      return `${this.sampleDatasets.length} Demo Datasets`;
    },
  },

  watch: {
    datasetDisplay() {
      this.onResize();
      // this.$refs.list.forceRender();
    },

    items() {
      this.onResize();
      // this.$refs.list.forceRender();
    },
  },

  mounted() {
    if (this.$el) {
      this.width = this.$el.getBoundingClientRect().width;
      this.height = this.$el.getBoundingClientRect().height;

      window.addEventListener('resize', this.onResize);
    }
  },

  destroy() {
    window.removeEventListener('resize', this.onResize);
  },

  methods: {
    ...mapActions('datasets', ['deselect', 'select']),

    onResize: debounce(function onResize() {
      this.height = this.$el.getBoundingClientRect().height;
      this.width = this.$el.getBoundingClientRect().width;
    }, 50),

    onSelectAll() {
      if (this.areAllDemoSelected) {
        this.deselect({ ids: this.sampleDatasets.map(s => s.id) });
      } else {
        this.select({ ids: this.sampleDatasets.map(s => s.id) });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.demo-datasets-browser-list {
  @include flex("block", "column", "start", "stretch");
  animation: $load-up;
  margin: 1rem 0;
  padding: 0.5rem 0 4.5rem 0;
  z-index: 0;

  & > div {
    padding-top: 1px;
  }

  input {
    &:hover {
      border-bottom:none;
    }
  }

  .title {
    @include flex("block", "row", "start", "center");
    color: darken(clr("blue"), 13%);
    margin-bottom: 1rem;
    padding: 0 0.5rem;

    .icon {
      margin-right: 0.5rem;
    }
  }

  .select-all {
    @include flex('block', 'row', 'start', 'center');
    color: darken(clr("blue"), 13%);
    cursor: pointer;
    font-size: $font-size-xs;
    font-weight: 400;
    margin-bottom:1.5rem;
    padding: 0 0.5rem;
    text-transform: uppercase;

    .base-checkbox {
      margin-right: 0.5rem;
    }
  }

  .list {
    .demo-datasets-browser-list-row {
      .datasets-item {
        border: 1px solid transparent;
        border-bottom: 1px solid $border-color;
        border-radius: 0;

        &:hover {
          border: 1px solid rgba(165, 159, 217, 1);
          border-radius: 3px;
        }
      }
    }
  }
}
</style>
