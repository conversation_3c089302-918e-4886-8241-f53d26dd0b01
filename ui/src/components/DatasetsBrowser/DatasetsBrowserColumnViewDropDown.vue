<template>
  <section class="datasets-browser-column-view-drop-down">
    <span>Theme name</span>
    <span>Adoresocre</span>
    <span>etc</span>
  </section>
</template>

<script>

export default {
  name: 'datasets-browser-column-view-drop-down',

  components: {
  },

  data() {
    return {
    };
  },

  computed: {
  },

  methods: {
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.datasets-browser-column-view-drop-down {
  @include flex('block', 'column', 'start', 'start');

  position: absolute;
  text-transform: none;
  top: 50px;
}
</style>
