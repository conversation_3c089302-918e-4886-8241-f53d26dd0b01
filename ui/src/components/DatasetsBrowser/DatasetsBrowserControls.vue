<template>
  <section class="datasets-browser-controls">
    <base-checkbox :value="allSelected" :size1x="true" @input="onSelectAll" />
    <section class="header dataset-name header-gap-large">
      <span class="total">{{ titleText }}</span>
      <i class="fa-thin fa-circle-small icon-circle"></i>
      <base-sort-control text="Dataset Name"
                         :active="sortedByName"
                         :asc="asc"
                         @click="onClickSortByName()"
      />
    </section>
    <base-sort-control class="header adorescore header-gap-large"
                       text="Adorescore"
                       :active="sortedByScore"
                       :asc="asc"
                       @click="onClickSortByScore()"
    />
    <section class="header insight header-gap-large">Insights</section>
    <section class="header report header-gap-large" v-if="hasFeature('storyteller')">Reports</section>
    <section class="header report header-gap-large" v-if="hasFeature('var')">Revenue at Risk</section>
    <base-sort-control class="header permission header-gap-large"
       v-if="!isLimitedWorkspace"
       text="Access"
       :active="sortedByPermission"
       :asc="asc"
       @click="onClickSortByPermission()"
    />
    <base-sort-control class="header uploaded header-gap-large"
       text="Updated"
       :active="sortedByUpdated"
       :asc="asc"
       @click="onClickSortByUpdated()"
    />
    <datasets-browser-controls-tag v-if="showControlTag"/>
    <section class="header cols" >
      <datasets-browser-pagination-size />
      <!-- todo: later - datasets-browser-column-view / -->
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseCheckbox from '@/components/Base/BaseCheckbox';
import BaseSortControl from '@/components/Base/BaseSortControl';
import DatasetsBrowserControlsTag from '@/components/DatasetsBrowser/DatasetsBrowserControlsTag';
import DatasetsBrowserPaginationSize from '@/components/DatasetsBrowser/pagination/DatasetsBrowserPaginationSize';
import DatasetSort from '@/enum/dataset-sort';
import DatasetView from '@/enum/dataset-view';
import intercomEvent from '@/services/IntercomEvent';
import intercomEvents from '@/enum/intercom-events';
import SortDirection from '@/enum/sort-direction';

import { datasetsRequestV0 } from '@/services/request';

export default {
  name: 'datasets-browser-controls',

  components: {
    BaseCheckbox,
    BaseSortControl,
    DatasetsBrowserControlsTag,
    DatasetsBrowserPaginationSize,
  },

  computed: {
    ...mapGetters('datasets', [
      'count',
      'isSelected',
      'viewPipe',
    ]),

    ...mapGetters('user', ['isLimitedWorkspace', 'hasFeature']),

    ...mapState('datasets', {
      selected: state => state.selected,
      sortDirection: state => state.sortDirection,
      sortedBy: state => state.sortedBy,
      totalDataset: state => state.total,
      totalArchived: state => state.totalArchived,
      totalInProgress: state => state.totalInProgress,
      view: state => state.view,
    }),

    ...mapState('datasetsTag', ['tags']),

    allSelected() {
      return this.viewPipe.every(id => this.isSelected(id));
    },

    asc() {
      return this.sortDirection === SortDirection.ASC;
    },

    showControlTag() {
      return this.tags?.length > 0;
    },

    sortedByName() {
      return this.sortedBy === DatasetSort.NAME;
    },

    sortedByPermission() {
      return this.sortedBy === DatasetSort.PERMISSION;
    },

    sortedByScore() {
      return this.sortedBy === DatasetSort.SCORE;
    },

    sortedByUpdated() {
      return this.sortedBy === DatasetSort.UPDATED;
    },

    titleText() {
      if (this.total === 0) {
        return 'No Datasets';
      }

      if (this.total === 1) {
        return `${this.total} Dataset`;
      }
      return `${this.total} Datasets`;
    },

    total() {
      if (this.view === DatasetView.ARCHIVE) {
        return this.totalArchived;
      }
      if (this.view === DatasetView.IN_PROGRESS) {
        return this.totalInProgress;
      }
      if (this.view === DatasetView.SELECTED) {
        return this.selected.length;
      }
      return this.totalDataset;
    },
  },

  methods: {
    ...mapActions('datasets', [
      'deselect',
      'select',
      'setSortDirection',
      'sort',
    ]),

    async onClickSort(data) {
      intercomEvent.send(intercomEvents.SORT_DATASETS);

      if (this.sortedBy === data) {
        this.setSortDirection({ sortDirection: this.sortDirection.inverse });
      } else {
        this.setSortDirection({ sortDirection: SortDirection.DESC });
      }

      this.sort({ by: data });
      await datasetsRequestV0.getDatasets();
    },

    onClickSortByName() {
      this.onClickSort(DatasetSort.NAME);
    },

    onClickSortByPermission() {
      this.onClickSort(DatasetSort.PERMISSION);
    },

    onClickSortByScore() {
      this.onClickSort(DatasetSort.SCORE);
    },

    onClickSortByUpdated() {
      this.onClickSort(DatasetSort.UPDATED);
    },

    onSelectAll() {
      if (this.allSelected) {
        this.deselect({ ids: this.viewPipe });
      } else {
        this.select({ ids: this.viewPipe });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.datasets-browser-controls {
  @include flex('block', 'row', 'start', 'center');
  padding: 0.5rem 1rem 0 1rem;

  .base-checkbox {
    @include flex('block', 'row', 'center', 'center');

    margin-right: 0.8rem;
    min-width: 1rem;
  }

  .header {
    @include flex('block', 'row', 'start', 'center');

    color: #5F52C5;
    font-size: $font-size-xs;
    margin-right: 1rem;
    text-transform: uppercase;

    &.adorescore, &.insight, &.report, &.permission, &.uploaded {
      min-width: 100px;
      width: 100px;
    }

    &.cols {
      margin-left: auto;
      margin-right: 0;
      min-width: fit-content;
    }

    &.dataset-name {
      min-width: 300px;
      width: 300px;

      .icon-circle {
        font-size: 6px;
        padding: 0 0.6rem;
      }
    }

    &.header-gap-large {
      margin-right: 35px;
    }
  }

  .insight, .report {
    font-weight: $font-weight-bold;
  }

  .datasets-browser-controls-tag {
    margin-right: 1rem;
    min-width: 230px;
  }
}
</style>
