<template>
  <section class="generated-chart-panel-options">
    <section class="text" @click.stop="open = !open">
      <span>CHART OPTIONS</span>
      <menu-icon class="icon" />
    </section>

    <generated-chart-panel-options-dropdown v-if="open" @close="open = false" />
  </section>
</template>

<script>
import { MenuIcon } from 'vue-feather-icons';

import GeneratedChartPanelOptionsDropdown from '@/components/GeneratedChartPanel/GeneratedChartPanelOptionsDropdown';

export default {
  name: 'generated-chart-panel-options',

  components: {
    GeneratedChartPanelOptionsDropdown,
    MenuIcon,
  },

  data() {
    return {
      open: false,
    };
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.generated-chart-panel-options {
  position: relative;

  .text {
    @include flex("block", "row", "center", "center");

    color: $body-copy-light;
    cursor: pointer;
    transition: all $interaction-transition-time;

    &:hover {
      color: $body-copy;
    }

    span {
      font-size: $font-size-xxs;
      font-weight: $font-weight-medium;
      letter-spacing: $letter-spacing-sm;
      margin-right: 1em;
    }

    .icon {
      height: 1.3em;
      margin-top: -0.1rem;
    }
  }
}
</style>
