<template>
  <section class="generated-chart-panel">
    <section v-if="themes.length > 0" class="section">
      <generated-chart-panel-header />
      <bubble-chart bcId="custom-chart" />
    </section>
    <section v-else class="section">
      <h1>Chart Generator</h1>
      <p>Select themes from the table in the table tab to generate your own bubble chart</p>
      <img class="empty" :src="require('@/assets/themes-table-empty.svg')" />
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BubbleChart from '@/components/BubbleChart/BubbleChart';
import CustomChartType from '@/enum/custom-chart-type';
import GeneratedChartPanelHeader from '@/components/GeneratedChartPanel/GeneratedChartPanelHeader';
import { datasetsRequestV0 } from '@/services/request';

export default {
  name: 'generated-chart-panel',

  components: {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    GeneratedChartPanelHeader,
  },

  computed: {
    ...mapState('datasets', ['active']),

    ...mapGetters('datasets', ['get', 'getFromSelected']),

    ...mapState('themes', ['themes']),

    isSample() {
      return this.get(this.active).localSample;
    },
  },

  async created() {
    if ((!this.themes || this.themes.length === 0) && !this.isSample) {
      await datasetsRequestV0.retrieveCustomChart(CustomChartType.THEME_ANALYSIS);
    }
  },

  mounted() {
    this.setLabel({ label: `${this.getFromSelected(this.active).label} Themes` });
  },

  methods: {
    ...mapActions('themesGenerated', ['setLabel']),
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.generated-chart-panel {
  @include flex("block", "column", "start", "stretch");
  @include stretch;

  .section {
    @include flex("block", "column", "start", "stretch");
    @include stretch;

    text-align: center;

    h1 {
      margin: 1rem 1rem 0.5rem;
      padding-bottom: 0;
    }

    p {
      color: $body-copy-light;
      margin: 0;
    }

    .empty {
      height: 70%;
      margin-top: 1rem;
    }
  }
}
</style>
