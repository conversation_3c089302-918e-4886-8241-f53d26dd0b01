<template>
  <section class="generated-chart-panel-export-dropdown">
    <h4>Image Scale (for PNG)</h4>
    <section class="option">
      <vue-slider v-model="scale" v-bind="sliderScale" />
    </section>
    <span class="image-size">{{ Math.round(1523 * scale) }}px × {{ Math.round(471 * scale) }}px</span>

    <base-button :disabled="disabled" size="small" @click="onClickExport('png')">
      <span>Export as PNG</span>
    </base-button>

    <base-button :disabled="disabled" size="small" @click="onClickExport('svg')">
      <span>Export as SVG</span>
    </base-button>
  </section>
</template>

<script>
import VueSlider from 'vue-slider-component';

import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BlurCloseable from '@/components/Mixins/BlurCloseable';
import intercomEvent from '@/services/IntercomEvent';
import intercomEvents from '@/enum/intercom-events';
import sliderDefaultStyles from '@/helpers/slider-default-styles';

export default {
  name: 'generated-chart-panel-export-dropdown',

  components: {
    BaseButton,
    VueSlider,
  },

  mixins: [BlurCloseable],

  data() {
    return {
      creating: false,
    };
  },

  computed: {
    ...mapState('chartOptions', ['imageScale']),

    disabled() {
      return this.creating;
    },

    scale: {
      get() {
        return this.imageScale;
      },

      set(value) {
        this.setImageScale({ scale: value });
      },
    },

    sliderBase() {
      return {
        ...sliderDefaultStyles,
        width: '150px',
      };
    },

    sliderScale() {
      return {
        ...this.sliderBase,
        steps: [
          1,
          2,
          3,
          4,
          5,
        ],
        max: 5,
        min: 0.5,
        tooltipFormatter: '{value}x',
        interval: 0.5,
      };
    },
  },

  methods: {
    ...mapActions('chartOptions', ['createImage', 'setImageScale']),

    async onClickExport(type) {
      this.creating = true;

      await this.$nextTick(async () => {
        await this.createImage({ elementId: 'custom-chart-svg', name: 'themes', type });
        this.creating = false;
      });

      intercomEvent.send(intercomEvents.EXPORT_THEME_CHART);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.generated-chart-panel-export-dropdown {
  @include flex("block", "column", "start", "stretch");
  @include rigid;
  @include panel;

  font-size: $font-size-xs;
  padding: 0.3rem 0.8rem 0.5rem;
  position: absolute;
  right: 0;
  top: 1.5rem;
  z-index: 99;

  .image-size {
    font-size: $font-size-xxs;
    margin: 0.2rem 0 0.5rem;
    text-align: center;
  }

  h4 {
    color: $body-copy-light;
    font-size: $font-size-xxs;
    letter-spacing: $letter-spacing-sm;
    margin: 1em 0;
    text-transform: uppercase;
  }

  .option {
    @include flex("block", "row", "start", "center");

    cursor: pointer;
    margin: 0 0.5rem 0.5rem 0.5rem;
    width: 150px;
    position: relative;
  }

  .help {
    font-size: $font-size-xxs;
    margin-top: 0.5rem;
  }

  .base-button {
    margin-top: 1rem;

    .icon {
      height: $font-size-sm;
      width: $font-size-sm;
    }
  }
}
</style>
