<template>
  <section class="generated-chart-panel-export">
    <section class="text" @click.stop="open = !open">
      <span>EXPORT</span>
      <download-icon class="icon" />
    </section>

    <generated-chart-panel-export-dropdown v-if="open" @close="onClose" />
  </section>
</template>

<script>
import { DownloadIcon } from 'vue-feather-icons';

import GeneratedChartPanelExportDropdown from '@/components/GeneratedChartPanel/GeneratedChartPanelExportDropdown';

export default {
  name: 'generated-chart-panel-export',

  components: {
    DownloadIcon,
    GeneratedChartPanelExportDropdown,
  },

  data() {
    return {
      open: false,
    };
  },

  methods: {
    onClose() {
      this.open = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.generated-chart-panel-export {
  margin-right: 2rem;
  position: relative;

  .text {
    @include flex("block", "row", "center", "center");

    color: $body-copy-light;
    cursor: pointer;
    transition: all $interaction-transition-time;

    &:hover {
      color: $body-copy;
    }

    span {
      font-size: $font-size-xxs;
      font-weight: $font-weight-medium;
      letter-spacing: $letter-spacing-sm;
      margin-right: 1em;
    }

    .icon {
      height: 1.3em;
    }
  }
}
</style>
