<template>
  <section class="generated-chart-panel-header">
    <section class="controls">
      <generated-chart-panel-export />

      <section class="configuration">
        <base-dropdown :data="items" :open="open" @close="open = false" @select="onSelect">
          <base-dropdown-button :active="open" @click.stop="open = !open">{{ yPath.titleCase() }}</base-dropdown-button>
        </base-dropdown>
      </section>

      <generated-chart-panel-options />
    </section>

    <section class="title">
      <input v-model="localLabel" type="text" name="title" />
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseDropdown from '@/components/Dropdown/BaseDropdown';
import BaseDropdownButton from '@/components/Dropdown/BaseDropdownButton';
import GeneratedChartPanelExport from '@/components/GeneratedChartPanel/GeneratedChartPanelExport';
import GeneratedChartPanelOptions from '@/components/GeneratedChartPanel/GeneratedChartPanelOptions';
import ThemesChartPath from '@/enum/themes-chart-path';

export default {
  name: 'generated-chart-panel-header',

  components: {
    BaseDropdown,
    BaseDropdownButton,
    GeneratedChartPanelExport,
    GeneratedChartPanelOptions,
  },

  data() {
    return {
      open: false,
    };
  },

  computed: {
    ...mapState('themesChart', ['yPath']),

    ...mapState('themesGenerated', ['label']),

    items() {
      return ThemesChartPath.enumValues.map(e => {
        return {
          value: e,
          content: e.titleCase(),
        };
      });
    },

    localLabel: {
      get() {
        return this.label;
      },
      set(label) {
        this.setLabel({ label });
      },
    },
  },

  methods: {
    ...mapActions('themesChart', ['setYPath']),

    ...mapActions('themesGenerated', ['setLabel']),

    onSelect(item) {
      this.setYPath({ path: item.value });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.generated-chart-panel-header {
  @include flex("block", "column", "center", "stretch");

  padding: 1rem 0 0;
  position: relative;

  .controls {
    @include flex("block", "row", "end", "center");

    padding: 0 1rem 0.5rem;

    .configuration {
      margin-right: 1rem;
    }
  }

  .title {
    input {
      border: none;
      color: $body-copy;
      font-size: $font-size-lg;
      margin-bottom: 0;
      text-align: center;
      width: 100%;

      &:focus,
      &:active {
        outline: none;
      }
    }
  }
}
</style>
