<template>
  <section class="generated-chart-panel-options-dropdown">
    <h4>LABELS:</h4>
    <section class="options-section">
      <section class="options-column">
        <section class="options-item">
          <section class="options-item" @click="onClickLabels">
            <base-checkbox :value="showLabels"></base-checkbox>
            <span>Show Labels</span>
          </section>
        </section>
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseCheckbox from '@/components/Base/BaseCheckbox';
import BlurCloseable from '@/components/Mixins/BlurCloseable';

export default {
  name: 'generated-chart-panel-options-dropdown',

  mixins: [BlurCloseable],

  components: {
    BaseCheckbox,
  },

  computed: {
    ...mapState('themesGenerated', ['showLabels']),
  },

  methods: {
    ...mapActions('themesGenerated', ['setShowLabels']),

    onClickLabels() {
      this.setShowLabels({ show: !this.showLabels });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.generated-chart-panel-options-dropdown {
  @include flex("block", "column", "start", "stretch");
  @include rigid;

  background-color: white;
  border: $border-standard;
  border-radius: $border-radius-small;
  font-size: $font-size-xs;
  padding: 0 1em 0.5em;
  position: absolute;
  right: 0;
  z-index: 199;

  h4 {
    color: $body-copy-light;
    font-size: $font-size-xxs;
    letter-spacing: $letter-spacing-sm;
    margin: 1em 0;
    text-align: left;
  }

  .options-section {
    @include flex("block", "row", "start", "center");

    .options-column {
      @include flex("block", "column", "start", "stretch");

      margin-right: 0.5em;

      .options-item {
        @include flex("block", "row", "start", "center");
        @include rigid;

        cursor: pointer;
        margin: 0 0.5em 0.5em 0;

        .base-checkbox {
          margin-right: 0.5em;
          pointer-events: none;
        }
      }
    }
  }
}
</style>
