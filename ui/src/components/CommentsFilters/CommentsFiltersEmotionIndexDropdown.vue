<template>
  <section class="comments-filters-emotion-index-dropdown"
           v-click-outside-handler="{ handler: 'onClickOutside' }">
    <base-dropdown :closeOnSelect="true"
                   :component="CommentsFiltersEmotionIndexItem"
                   :data="dataList"
                   :height="400"
                   :open="open"
                   :search="false"
                   @select="onSelectItem">
      <section class="dropdown-text"
               :class="{ hasError }" @click="open = !open">
        <section class="selected">
          <span class="text">
            {{ textSelected }}
          </span>
        </section>
        <i class="fa fa-caret-down icon icon-right" :class="{ open }"></i>
      </section>
    </base-dropdown>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import BaseDropdown from '@/components/Dropdown/BaseDropdown';
import clickOutsideHandler from '@/directives/click-outside-handler';
import CommentsFiltersEmotionIndexItem from '@/components/CommentsFilters/CommentsFiltersEmotionIndexItem';
import Index from '@/enum/index';

export default {
  name: 'comments-filters-emotion-index-dropdown',

  components: {
    BaseDropdown,
  },

  directives: {
    clickOutsideHandler,
  },

  props: {
    index: {
      type: Number,
      required: true,
    },
    filterObj: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      CommentsFiltersEmotionIndexItem,
      Index,
      open: false,
    };
  },

  computed: {
    ...mapState('snippets', ['filtersAttempted']),

    dataList() {
      return Index.enumValues.map(o => {
        return {
          active: this.isSelected(o),
          text: o.titleCase(),
          val: o,
        };
      });
    },

    hasError() {
      return this.filtersAttempted && !this.filterObj.emotionIndex;
    },

    textSelected() {
      return this.filterObj.emotionIndex?.titleCase() || 'Select...';
    },
  },

  methods: {
    ...mapActions('snippets', ['editFilterEmotionIndex']),

    isSelected(val) {
      return this.filterObj.emotionIndex === val;
    },

    onClickOutside() {
      this.open = false;
    },

    onSelectItem(item) {
      if (!this.isSelected(item.val)) {
        this.editFilterEmotionIndex({ index: this.index, value: item.val });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-filters-emotion-index-dropdown {
  @include flex("block", "row", "start", "stretch");
  @include stretch;

  cursor: pointer;
  font-size: $font-size-xxs;
  font-weight: $font-weight-normal;

  .base-dropdown {
    @include stretch;

    .dropdown-text {
      @include flex("block", "row", "between", "center");
      background-color: $cfp-main-select-box-bg;
      border: 1px solid $cfp-btn-bdr-clr;
      border-radius: 3px;
      color: clr("white");
      height: 1.8rem;
      padding: 0.4rem 0.2rem 0.4rem 0.4rem;
      width: inherit;

      &.hasError {
        border: 2px solid clr("red");
      }

      &:focus, &:hover {
        border: 1px solid $cfp-main-select-box-bdr;

        .icon {
          opacity: 1;
        }
      }

      .selected {
        @include flex("block", "row", "start", "center");
        overflow: hidden;

        .text {
          @include truncate;
          max-width: 6rem;
        }
      }

      .icon {
        opacity: 0.5;
        stroke-width: 3px;
        transition: all $interaction-transition-time;
        width: 0.7rem;

        &.open{
          transform: rotate(180deg) translateX(5px);
        }
      }
    }
  }
}
</style>
