<template>
  <section class="comments-filters-column-dropdown"
           v-click-outside-handler="{ handler: 'onClickOutside' }">
    <base-dropdown :component="CommentsFiltersColumnItem"
                   :open="open"
                   :data="filterColumnList"
                   :height="400"
                   @select="onSelectFilterColumn"
                   tooltip-class="tooltip-comments-filters-column-dropdown"
    >
      <section class="dropdown-text" @click="open = !open">
        <section class="value">
          <span class="selected-icon" v-if="filterObj.column">
            <img :src="require(`@/assets/meta-column/${filterObj.column.icon}.svg`)" alt=""/>
          </span>
          <span class="text" :class="{ triCol: showEmotionIndexDropdown }">{{ textSelected }}</span>
        </section>
        <i class="fa fa-caret-down icon icon-right" :class="{ open }"></i>
      </section>
    </base-dropdown>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseDropdown from '@/components/Dropdown/BaseDropdown';
import clickOutsideHandler from '@/directives/click-outside-handler';
import CommentsFilterColumn from '@/enum/comments-filter-column';
import CommentsFiltersColumnItem from '@/components/CommentsFilters/CommentsFiltersColumnItem';

import { metadataApi } from '@/services/api';

export default {
  name: 'comments-filters-column-dropdown',

  components: {
    BaseDropdown,
  },

  directives: {
    clickOutsideHandler,
  },

  props: {
    index: {
      type: Number,
      required: true,
    },
    filterObj: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      CommentsFilterColumn,
      CommentsFiltersColumnItem,
      open: false,
      showCommentOptInUserDefinedGroup: true,
    };
  },

  computed: {
    ...mapGetters('snippets', ['getFilterMetadataList']),

    ...mapState('datasets', {
      datasetId: 'active',
    }),

    ...mapState('snippets', ['filters']),

    defaultGroup() {
      const rs = [];
      rs.push({
        groupLabel: true,
        icon: false,
        text: 'Defaults',
      });
      rs.push(...this.filterByGroupAndPos(CommentsFilterColumn.enumValues, 0)
        .map(o => this.mappingDropdownItem(o)));

      return rs;
    },

    filterColumnList() {
      const rs = [];
      // user defined group
      rs.push(...this.userDefinedGroup);

      // system default group
      rs.push(...this.defaultGroup);
      return rs;
    },

    showEmotionIndexDropdown() {
      return this.filterObj.column?.val === CommentsFilterColumn.EMOTION.name;
    },

    textSelected() {
      return this.filterObj.column?.text || 'Select Field';
    },

    userDefinedGroup() {
      const rs = [];
      const {
        metadataColumns,
        metadataHeaders,
        metadataTypes,
      } = this.getFilterMetadataList({ datasetId: this.datasetId });

      if (metadataColumns?.length && metadataHeaders?.length && metadataTypes?.length) {
        rs.push({
          groupLabel: true,
          icon: false,
          text: 'User Defined Fields',
        });

        let feObj;
        let beObj;
        for (let i = 0; i < metadataHeaders.length; i += 1) {
          // ignore comment content: -1
          // when dataset has multi comment cols but user didn't select them all as comment
          if (metadataColumns[i] !== -1 || this.showCommentOptInUserDefinedGroup) {
            beObj = {
              column: metadataColumns[i],
              header: metadataHeaders[i],
              index: Number(i),
              type: metadataTypes[i],
            };

            feObj = CommentsFilterColumn[beObj.type.toUpperCase()];
            // we have SCORE & NUMERIC to cover any specific numeric type like integer, float, double...
            // for now it's just a miscommunication between api vs survey, but more might happen
            if (!feObj
              && [
                'DOUBLE',
                'FLOAT',
                'INTEGER',
              ].includes(beObj.type.toUpperCase())
            ) {
              feObj = CommentsFilterColumn.SCORE;
            }

            rs.push(this.mappingDropdownItem(feObj, beObj));
          }
        }
      }

      // return [] if there's no other option but the groupLabel
      if (rs.length === 1 && rs[0].groupLabel) {
        return [];
      }

      return rs;
    },
  },

  methods: {
    ...mapActions('snippets', ['updateFilterUndefined', 'editFilterColumn']),

    isDisabled(columnName) {
      const rs = this.filters
        .filter(o => o.column && o !== this.filterObj)
        .map(o => o.column.val)
        .includes(columnName);
      return rs;
    },

    isSelected(columnName) {
      return this.filterObj.column?.val === columnName;
    },

    /**
     *
     * @param feObj - system default / generic column CommentsFilterColumn
     * @param beObj - metadata obj from back-end
     */
    mappingDropdownItem(feObj, beObj) {
      const text = beObj ? beObj.header : feObj.text();
      const val = beObj ? beObj.header : feObj.name;
      const active = this.isSelected(val);
      const disabled = this.isDisabled(val);

      return {
        // dropdown props
        active,
        disabled,
        groupLabel: false,
        // val
        defaultOperator: feObj.defaultOperator(),
        defaultLimit: feObj.defaultLimit(),
        group: feObj.group(),
        icon: feObj.icon(1),
        iconDark: feObj.icon(0),
        isDateTime: feObj.isDateTime(),
        isNumeric: feObj.isNumeric(),
        isText: feObj.isText(),
        metadataColumn: beObj?.column || null,
        metadataHeader: beObj?.header || null,
        metadataIndex: beObj?.index != null ? beObj.index : null,
        metadataType: beObj?.type || null,
        operators: feObj.operators(),
        position: feObj.position(),
        showSlider: feObj.showSlider(),
        text,
        val,
      };
    },

    onClickOutside() {
      this.open = false;
    },

    onSelectFilterColumn(item) {
      if (!item.groupLabel) {
        if (!this.isDisabled(item.val) && !this.isSelected(item.val)) {
          if (this.filterObj.column == null) {
            this.updateFilterUndefined({ value: false });
          }
          this.editFilterColumn({ index: this.index, value: item });
          this.$emit('select', item, this.index);
        }
      }
    },

    filterByGroupAndPos(list, group) {
      return list
        .filter(o => o.group() === group)
        .sort((a, b) => a.position() - b.position());
    },
  },

  async mounted() {
    const { metadataColumns } = this.getFilterMetadataList({ datasetId: this.datasetId });
    if (metadataColumns.includes(-1)) {
      const rs = await metadataApi.getDistinctValuesForTextData(this.datasetId, metadataColumns.indexOf(-1));

      if (!rs?.length) {
        this.showCommentOptInUserDefinedGroup = false;
        return;
      }

      let i = 0;
      while (this.showCommentOptInUserDefinedGroup && i < rs.length) {
        this.showCommentOptInUserDefinedGroup = rs[i].content?.length > 0;
        i += 1;
      }
    }
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-filters-column-dropdown {
  @include flex("block", "column", "start", "stretch");
  @include stretch;

  color: clr("white");
  cursor: pointer;
  font-size: $font-size-xxs;
  font-weight: 600;

  .base-dropdown {
    .dropdown-text {
      @include flex("block", "row", "between", "center");
      background-color: $cfp-main-select-box-bg;
      border: 1px solid $cfp-btn-bdr-clr;
      border-radius: 3px;
      height: 1.8rem;
      padding: 0.6em 0.2rem 0.3rem 0.4rem;
      width: inherit;

      &:focus, &:hover {
        border: 1px solid #9874c8;

        .icon {
          opacity: 1;
        }
      }

      .value {
        @include flex("block", "row", "start", "center");
        overflow: hidden;

        .selected-icon {
          padding-top: 0.2rem;
          margin-right: 0.2rem;
        }

        .text {
          @include truncate;
          max-width: 9rem;

          &.triCol {
            max-width: 5rem;
          }
        }
      }

      .icon {
        opacity: 0.5;
        stroke-width: 3px;
        transition: all $interaction-transition-time;
        width: 0.7rem;

        &.open{
          transform: rotate(180deg) translateX(5px);
        }
      }
    }
  }
}
</style>
