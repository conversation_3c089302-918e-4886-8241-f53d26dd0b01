<template>
  <section class="comments-filter-value" :class="{ noValue: !hasValue }">
    <comments-filter-value-datetime v-if="isDatetimeColumn"
                                         :index="index"
                                         :filter-obj="filterObj">
    </comments-filter-value-datetime>
    <comments-filter-value-numeric-range v-else-if="isNumericColumn"
                                         :index="index"
                                         :filter-obj="filterObj">
    </comments-filter-value-numeric-range>
    <comments-filter-value-text-distinct v-else-if="showTextDropdown"
                                         :index="index"
                                         :filter-obj="filterObj">
    </comments-filter-value-text-distinct>
    <comments-filter-value-text-input v-else-if="showTextInput"
                                         :index="index"
                                         :filter-obj="filterObj">
    </comments-filter-value-text-input>
  </section>
</template>

<script>
import CommentsFilterValueDatetime from '@/components/CommentsFilters/CommentsFilterValueDatetime';
import CommentsFilterValueNumericRange from '@/components/CommentsFilters/CommentsFilterValueNumericRange';
import CommentsFilterValueTextDistinct from '@/components/CommentsFilters/CommentsFilterValueTextDistinct';
import CommentsFilterValueTextInput from '@/components/CommentsFilters/CommentsFilterValueTextInput';

export default {
  name: 'comments-filter-value',

  components: {
    CommentsFilterValueDatetime,
    CommentsFilterValueNumericRange,
    CommentsFilterValueTextDistinct,
    CommentsFilterValueTextInput,
  },

  props: {
    index: {
      type: Number,
      required: true,
    },
    filterObj: {
      type: Object,
      required: true,
    },
  },

  computed: {
    hasValue() {
      return this.isDatetimeColumn
        || this.isNumericColumn
        || this.showTextDropdown
        || this.showTextInput;
    },

    isDatetimeColumn() {
      return this.filterObj.column.isDateTime;
    },

    isNumericColumn() {
      return this.filterObj.column.isNumeric;
    },

    showTextDropdown() {
      return this.filterObj.column.isText
        && !this.filterObj.operator.hasNoValues()
        && !this.filterObj.operator.hasInputText();
    },

    showTextInput() {
      return this.filterObj.column.isText
        && this.filterObj.operator.hasInputText();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-filter-value {

  &.noValue {
    margin-top: 0;
  }
}
</style>
