<template>
  <section class="comments-filters-save-dropdown-list"
           v-click-outside-handler="{ handler: 'onClickOutside' }">
    <section class="selected"
             :class="{open: open}"
             @click="onClickSelected">
      <span class="text">{{ selected }}</span>
      <chevron-down-icon class="icon" />
    </section>
    <section class="dropdown-list" :class="{hideList: !open}">
      <section class="body">
        <section v-for="(option, i) of options"
                 :key="i"
                 class="item"
                 :class="{ active: isSelected(option) }"
                 @click.stop="onSelectOption(option)">
          {{ option }}
        </section>
      </section>
      <section class="footer">
        <section class="btn btn-manage" @click="onClickManage">
          Manage Filters
        </section>
        <section class="btn btn-new" @click="onClickNewFilter">
          New Filters
        </section>
      </section>
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';
import { ChevronDownIcon } from 'vue-feather-icons';
import clickOutsideHandler from '@/directives/click-outside-handler';

export default {
  name: 'comments-filters-save-dropdown-list',

  components: {
    ChevronDownIcon,
  },

  directives: {
    clickOutsideHandler,
  },

  props: {
    options: {
      type: Array,
      required: true,
    },
  },

  data() {
    return {
      selected: 'Please choose one',
      open: false,
    };
  },

  computed: {
    ...mapState('snippets', ['filterShow']),
  },

  methods: {
    isSelected(o) {
      return this.selected === o;
    },

    onClickManage() {
      // console.log('Manage Filters');
    },

    onClickNewFilter() {
      // console.log('Create New Filters');
    },

    onClickOutside() {
      if (this.filterShow) {
        this.open = false;
      }
    },

    onClickSelected() {
      this.open = !this.open;
    },

    onSelectOption(o) {
      this.selected = o;
      this.open = false;
      this.$emit('select', o);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-filters-save-dropdown-list {
  @include flex("block", "column", "start", "start");
  background-color: $cfp-save-bar-select-box-bg;
  color: clr("white");
  cursor: pointer;
  font-size: $font-size-xxs;
  position: relative;
  width: inherit;

  .selected {
    @include flex("block", "row", "between", "center");
    border: 1px solid $cfp-btn-bdr-clr;
    border-radius: $border-radius-small;
    height: 1.4rem;
    padding: 0.2em;
    width: inherit;

    .text {
      @include truncate;
      max-width: 8rem;
    }

    .icon {
      stroke-width: 3px;
      transition: all $interaction-transition-time;
      width: 1rem;
    }

    &.open{
      .icon {
        transform: rotate(180deg);
      }
    }
  }

  .dropdown-list {
    background-color: $cfp-save-bar-select-box-bg;
    border: 1px solid $cfp-btn-bdr-clr;
    border-radius: $border-radius-small;
    padding: 0.2em;
    position: absolute;
    top: 1.3rem;
    width: inherit;
    z-index: 9;

    .body {
      max-height: 20rem;
      overflow: auto;

      .item {
        @include flex("block", "row", "start", "center");
        cursor: pointer;
        padding: 0.5rem 0.2rem;

        &:hover, &.active {
          background-color: $cfp-btm-bar-apply-btn-bg;
        }
      }
    }

    .footer {
      @include flex("block", "column", "center", "start");
      .btn {
        @include flex("block", "row", "center", "start");
        background-color: $cfp-save-bar-save-btn;
        border: 1px solid $cfp-btn-bdr-clr;
        border-radius: $border-radius-small;
        color: clr("white");
        cursor: pointer;
        font-size: $font-size-xxs;
        font-weight: $font-weight-medium;
        margin: 0.2rem 0;
        padding: 0.3rem;
        text-transform: uppercase;
        width: 100%;

        &:hover {
          background-color: $cfp-save-bar-save-btn-hover;
        }
      }
    }

    &.hideList {
      display: none;
    }
  }
}
</style>
