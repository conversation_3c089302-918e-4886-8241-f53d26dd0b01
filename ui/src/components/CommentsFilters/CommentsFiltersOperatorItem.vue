<template>
  <section class="comments-filters-operator-item"
           :class="{ active: data.active }">
    <span class="text">{{ textColumn }}</span>
  </section>
</template>

<script>
export default {
  name: 'comments-filters-operator-item',

  props: {
    data: {
      type: Object,
      required: true,
    },
  },

  computed: {
    textColumn() {
      return `${this.data.text}...`;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-filters-operator-item {
  @include flex("block", "row", "start", "center");
  @include stretch;

  background-color: $cfp-main-select-box-bg;
  color: clr("white");
  cursor: pointer;
  font-size: $font-size-xxs;
  font-weight: 400;
  min-height: 1.8rem;
  min-width: 10rem;
  padding: 0.5rem;

  &:hover, &.active {
    background-color: $cfp-btm-bar-apply-btn-bg;
  }
}
</style>
