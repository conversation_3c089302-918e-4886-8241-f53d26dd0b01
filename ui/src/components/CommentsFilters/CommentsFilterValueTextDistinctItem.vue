<template>
  <section class="comments-filter-value-text-distinct-item">
    <section class="comments-filter-value-text-distinct-item-status">
      <i class="fa fa-check-square-o icon-select" v-if="data.selected"></i>
      <i class="fa fa-square-o icon-unselect" v-else></i>
    </section>
    <section class="item-label">{{ data.content }}</section>
  </section>
</template>

<script>
export default {
  name: 'comments-filter-value-text-distinct-item',

  props: {
    data: {
      type: Object,
      required: true,
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';
@import '~font-awesome/css/font-awesome.css';

.comments-filter-value-text-distinct-item {
  @include flex('block', 'row', 'between', 'center');
  cursor: pointer;
  font-size: $font-size-xxs;
  font-weight: $font-weight-medium;
  min-height: 1.5rem;
  padding: 0.5rem 0.5rem 0.5rem 0.9rem;
  min-width: 11.8rem;

  &:hover {
    background-color: $cfp-btm-bar-apply-btn-bg;
    color: clr("white");
  }

  .comments-filter-value-text-distinct-item-status {
    width: 1rem;
  }

  .item-label {
    width: 100%;
  }
}
</style>
