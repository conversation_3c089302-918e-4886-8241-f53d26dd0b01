<template>
  <section class="comments-filters-column-item"
           :class="{
              active: data.active,
              disabled: data.disabled,
              groupLabel: data.groupLabel,
           }">
    <span class="icon" v-if="data.icon || this.data.iconDark">
      <img :src="require(`@/assets/meta-column/${textIcon}.svg`)"/>
    </span>
    <span class="text">{{ textColumn }}</span>
  </section>
</template>

<script>

export default {
  name: 'comments-filters-column-item',

  props: {
    data: {
      type: Object,
      required: true,
    },
  },

  computed: {
    textIcon() {
      return this.data.icon;
    },

    textColumn() {
      return this.data.text;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-filters-column-item {
  @include flex("block", "row", "start", "center");

  background-color: $cfp-main-select-box-bg;
  color: clr("white");
  cursor: pointer;
  font-size: $font-size-xxs;
  font-weight: $font-weight-medium;
  min-height: 1.8rem;
  padding: 0.5rem;
  width: 11.8rem;
  line-height: 1.1rem;

  .icon {
    @include flex("block", "row", "start", "center");

    width: 1rem;
  }

  &.groupLabel {
    color: $cp-filter-footer-bg;
    cursor: default;
    font-size: 0.7rem;
    font-weight:600;
    text-transform: uppercase;
    letter-spacing: 0rem;
  }

  &.disabled {
    cursor: default;

    .icon, .text {
      opacity: 0.3;
    }
  }

  &:hover, &.active {
    background-color: $cfp-btm-bar-apply-btn-bg;

    &.groupLabel, &.disabled {
      background-color: $cfp-main-select-box-bg;
    }
  }
}
</style>
