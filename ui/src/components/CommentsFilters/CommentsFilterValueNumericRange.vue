<template>
  <section class="comments-filter-value-numeric-range">
    <input class="value-input value-from" v-model.lazy="valueInputFrom" />
    <vue-slider v-if="showSlider"
                class="slider"
                v-model="filterSliderModel"
                v-bind="filterSliderProps"
                :lazy="true">
    </vue-slider>
    <section v-else class="no-slider">
      <span class="no-slider-label min" :class="{ hasRangeValues }">Min</span>
      <span class="no-slider-label max" :class="{ hasRangeValues }">Max</span>
    </section>
    <input class="value-input value-to" v-model.lazy="valueInputTo" v-if="hasRangeValues" />
  </section>
</template>

<script>
import { mapActions } from 'vuex';
import sliderMetadataFilterStyle from '@/helpers/slider-metadata-filter-styles';
import VueSlider from 'vue-slider-component';

export default {
  name: 'comments-filter-value-numeric-range',

  components: {
    VueSlider,
  },

  props: {
    index: {
      type: Number,
      required: true,
    },
    filterObj: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      maxVal: null,
      minVal: null,
    };
  },

  mounted() {
    const list = this.filterObj.valueLimit?.slice() || [];
    if (list.length > 1) {
      this.minVal = Math.min(...list);
      this.maxVal = Math.max(...list);
    }
  },

  computed: {
    filterSliderModel: {
      get() {
        return this.filterObj.values;
      },
      set(values) {
        this.editFilterValues({
          index: this.index,
          values: this.hasRangeValues ? values : [values],
        });
      },
    },

    filterSliderProps() {
      const rs = {
        ...sliderMetadataFilterStyle,
        interval: 1,
        min: this.minVal,
        max: this.maxVal,
        width: '100%',
      };
      if (this.minVal === null) delete rs.min;
      if (this.maxVal === null) delete rs.max;
      return rs;
    },

    hasRangeValues() {
      return this.filterObj.operator.hasRangeValues();
    },

    showSlider() {
      return this.filterObj.column.showSlider;
    },

    valueInputFrom: {
      get() {
        return this.filterObj.values[0];
      },
      set(value) {
        this.editFilterValues({
          index: this.index,
          values: this.validateInputNumber(value) ? value : null,
          valueIndex: 0,
        });
      },
    },

    valueInputTo: {
      get() {
        return this.filterObj.values[1];
      },
      set(value) {
        this.editFilterValues({
          index: this.index,
          values: this.validateInputNumber(value) ? value : null,
          valueIndex: 1,
        });
      },
    },
  },

  methods: {
    ...mapActions('snippets', ['editFilterValues']),

    validateInputNumber(val) {
      const rg = new RegExp(/^([+-]?\d+)(\.\d+)?$/, 'g');
      if (!val.match(rg)) {
        return false;
      }
      if (this.minVal !== null && val < this.minVal) {
        return false;
      }
      if (this.maxVal !== null && val > this.maxVal) {
        return false;
      }
      return true;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-filter-value-numeric-range {
  @include flex("block", "row", "start", "center");

  .value-input {
    border: 0;
    border-radius: 2px;
    padding: 0.2rem;
    font-size: $font-size-xxs;
    text-align: center;
    width: 2rem;

    &.value-from {
      margin-right: 0.5rem;
    }

    &.value-to {
      margin-left: 0.5rem;
    }
  }

  .no-slider {
    @include flex("block", "row", "between", "center");
    @include stretch;

    color: clr("white");

    .no-slider-label {
      font-size: $font-size-xs;
      visibility: hidden;

      &.hasRangeValues {
        visibility: visible;
      }
    }
  }
}
</style>
