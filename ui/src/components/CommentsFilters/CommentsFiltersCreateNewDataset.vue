<template>
  <section class="comments-filters-create-new-dataset">
    <section class="header">
      <edit2-icon class="icon" />
      <h2>Create New Dataset</h2>
    </section>
    <section class="body">
      <base-input v-model="label"
        class="dataset-label"
        :focus="true"
        ref="label"
        placeholder="Please Enter Dataset Label"
        @submit="onConfirm"
      />
    </section>
    <section class="footer">
      <base-button class="cancel" colour="light" size="small" type="link" @click="onCancel">Cancel</base-button>
      <base-button class="confirm" colour="base" size="small" @click="onConfirm" :disabled="hasError">Save</base-button>
    </section>
  </section>
</template>

<script>
import { Edit2Icon } from 'vue-feather-icons';
import { mapActions } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import DatasetCreatedToast from '@/components/Search/Toast/DatasetCreatedToast';

import { metadataRequest } from '@/services/request';

export default {
  name: 'comments-filters-create-new-dataset',

  components: {
    BaseButton,
    BaseInput,
    Edit2Icon,
  },

  data() {
    return {
      datasetLabel: null,
    };
  },

  computed: {
    hasError() {
      return !(this.datasetLabel?.length && this.datasetLabel.length < 200);
    },

    label: {
      get() {
        return this.datasetLabel;
      },

      set(value) {
        this.datasetLabel = value?.trim() || null;
      },
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('toast', ['add']),

    onCancel() {
      this.closeModal();
    },

    async onConfirm() {
      if (!this.hasError) {
        await metadataRequest.createNewDatasetOnMetadata(this.datasetLabel);

        this.add({ toast: { component: DatasetCreatedToast, id: 'dataset-created' } });
        this.closeModal();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-filters-create-new-dataset {
  @include modal;

  width: 450px;

  .header {
    @include flex("block", "row", "start", "center");

    padding: 1rem 2rem;

    h2 {
      font-size: $font-size-sm;
    }

    .icon {
      height: $font-size-sm;
      margin-right: 0.5rem;
      width: $font-size-sm;
    }
  }

  .body {
    @include flex("block", "column", "start", "start");

    padding: 2rem;

    .dataset-label {
      background-color: clr('white');
      border: $border-standard;
      border-radius: $border-radius-medium;
      font-size: $font-size-sm;
      padding: 0.5rem;
    }
  }

  .footer {
    padding: 1rem 2rem;

    .base-button {
      padding: 0.5rem 1rem;
    }

    .cancel {
      margin-left: -0.6rem;
    }

    .confirm {
      &.disabled-true {
        cursor: not-allowed;
      }
    }
  }
}
</style>
