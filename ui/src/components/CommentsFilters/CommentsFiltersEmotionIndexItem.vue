<template>
  <section class="comments-filters-emotion-index-item"
           :class="{ active: data.active }">
    <span class="text">{{ data.text }}</span>
  </section>
</template>

<script>
export default {
  name: 'comments-filters-emotion-index-item',

  props: {
    data: {
      type: Object,
      required: true,
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-filters-emotion-index-item {
  @include flex("block", "row", "start", "center");
  background-color: $cfp-main-select-box-bg;
  color: clr("white");
  cursor: pointer;
  font-size: $font-size-xxs;
  font-weight: 400;
  min-height: 1.8rem;
  padding: 0.5rem;
  width: 11.8rem;

  &:hover, &.active {
    background-color: $cfp-btm-bar-apply-btn-bg;
  }
}
</style>
