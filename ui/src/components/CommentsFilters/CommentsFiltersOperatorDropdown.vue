<template>
  <section class="comments-filters-operator-dropdown"
           v-click-outside-handler="{ handler: 'onClickOutside' }">
    <base-dropdown :component="CommentsFiltersOperatorItem"
                   :open="open"
                   :data="filterOperatorList"
                   :height="400"
                   @select="onSelectOperatorColumn">
      <section class="dropdown-text" @click="open = !open">
        <span class="text" :class="{ triCol: showEmotionIndexDropdown }">{{ textSelected }}</span>
        <i class="fa fa-caret-down icon icon-right" :class="{ open }"></i>
      </section>
    </base-dropdown>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import BaseDropdown from '@/components/Dropdown/BaseDropdown';
import clickOutsideHandler from '@/directives/click-outside-handler';
import CommentsFilterColumn from '@/enum/comments-filter-column';
import CommentsFilterOperator from '@/enum/comments-filter-operator';
import CommentsFiltersOperatorItem from '@/components/CommentsFilters/CommentsFiltersOperatorItem';

export default {
  name: 'comments-filters-operator-dropdown',

  components: {
    BaseDropdown,
  },

  directives: {
    clickOutsideHandler,
  },

  props: {
    index: {
      type: Number,
      required: true,
    },
    filterObj: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      CommentsFilterColumn,
      CommentsFilterOperator,
      CommentsFiltersOperatorItem,
      open: false,
    };
  },

  computed: {
    ...mapState('snippets', ['filterShow']),

    filterOperatorList() {
      if (this.filterObj.column) {
        return this.filterObj.column.operators
          .map(o => {
            return {
              active: this.isSelected(o),
              text: o.text(),
              val: o.name,
            };
          });
      }
      return [];
    },

    showEmotionIndexDropdown() {
      return this.filterObj.column?.val === CommentsFilterColumn.EMOTION.name;
    },

    textSelected() {
      return `${this.filterObj.operator?.text()}...` || 'Select Operator';
    },
  },

  methods: {
    ...mapActions('snippets', ['editFilterOperator']),

    isSelected(val) {
      return this.filterObj.operator === val;
    },

    onClickOutside() {
      this.open = false;
    },

    onSelectOperatorColumn(item) {
      item = CommentsFilterOperator[item.val];
      if (item && !this.isSelected(item)) {
        this.editFilterOperator({ index: this.index, value: item });
        this.$emit('select', item, this.index);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-filters-operator-dropdown {
  @include flex("block", "column", "start", "stretch");
  @include stretch;

  color: clr("white");
  cursor: pointer;
  font-size: $font-size-xxs;
  font-weight: $font-weight-normal;

  .base-dropdown {
    @include stretch;

    .dropdown-text {
      @include flex("block", "row", "between", "center");
      background-color: $cfp-main-select-box-bg;
      border: 1px solid $cfp-btn-bdr-clr;
      border-radius: 3px;
      height: 1.8rem;
      overflow: hidden;
      padding: 0.3em 0.2rem 0.3rem 0.4rem;

      &:focus, &:hover {
        border: 1px solid $cfp-main-select-box-bdr;

        .icon {
          opacity: 1;
        }
      }

      .text {
        @include truncate;
        max-width: 10rem;

        &.triCol {
          max-width: 6rem;
        }
      }

      .icon {
        opacity: 0.5;
        stroke-width: 3px;
        transition: all $interaction-transition-time;
        width: 0.7rem;

        &.open{
          transform: rotate(180deg) translateX(5px);
        }
      }
    }
  }
}
</style>
