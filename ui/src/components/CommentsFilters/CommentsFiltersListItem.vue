<template>
  <section class="comments-filters-list-item">
    <section class="left">
      <section class="filter-type">
        <section class="filter-column-select"
                 :class="{ unselected: !filterObj.column, triCol: showEmotionIndexDropdown }">
          <comments-filters-column-dropdown :filter-obj="filterObj"
                                            :index="index"
                                            @select="onSelectColumn">
          </comments-filters-column-dropdown>
        </section>
        <section class="filter-emotion-select" v-if="showEmotionIndexDropdown">
          <comments-filters-emotion-index-dropdown :filter-obj="filterObj"
                                                   :index="index">
          </comments-filters-emotion-index-dropdown>
        </section>
        <section class="filter-operator-select"
                 :class="{ triCol: showEmotionIndexDropdown }"
                 v-if="filterObj.column">
          <comments-filters-operator-dropdown :filter-obj="filterObj"
                                              :index="index"
                                              @select="onSelectOperator">
          </comments-filters-operator-dropdown>
        </section>
      </section>
      <section class="filter-value">
        <comments-filter-value :filter-obj="filterObj"
                               :index="index"
                               v-if="showCommentsFilterValue">
        </comments-filter-value>
      </section>
    </section>

    <section v-if="definedFilters.length" class="right">
      <section class="close" @click="onClickDelete">
        <img :src="require('@/assets/icon-close-white.svg')" class="icon"/>
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import CommentsFilterColumn from '@/enum/comments-filter-column';
import CommentsFiltersColumnDropdown from '@/components/CommentsFilters/CommentsFiltersColumnDropdown';
import CommentsFiltersEmotionIndexDropdown from '@/components/CommentsFilters/CommentsFiltersEmotionIndexDropdown';
import CommentsFiltersOperatorDropdown from '@/components/CommentsFilters/CommentsFiltersOperatorDropdown';
import CommentsFilterValue from '@/components/CommentsFilters/CommentsFilterValue';

import { metadataApi } from '@/services/api';

export default {
  name: 'comments-filters-list-item',

  components: {
    CommentsFiltersColumnDropdown,
    CommentsFiltersEmotionIndexDropdown,
    CommentsFiltersOperatorDropdown,
    CommentsFilterValue,
  },

  props: {
    index: {
      type: Number,
      required: true,
    },
    filterObj: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      CommentsFilterColumn,
    };
  },

  computed: {
    ...mapState('datasets', { datasetId: 'active' }),

    ...mapState('snippets', ['filters']),

    definedFilters() {
      return this.filters.filter(o => o.column);
    },

    showEmotionIndexDropdown() {
      return this.filterObj.column?.val === CommentsFilterColumn.EMOTION.name;
    },

    showCommentsFilterValue() {
      if (this.filterObj.column?.val === CommentsFilterColumn.EMOTION.name) {
        return this.filterObj.operator
          && this.filterObj.values
          && this.filterObj.emotionIndex != null;
      }
      return this.filterObj.operator && this.filterObj.values;
    },
  },

  methods: {
    ...mapActions('snippets', [
      'addNewFilter',
      'editFilterValueLimit',
      'editFilterValues',
      'removeFilter',
      'updateFilterUndefined',
    ]),

    onClickDelete() {
      this.removeFilter({ index: this.index });
      if (!this.filters.length) {
        this.addNewFilter();
      } else if (this.filterObj.column == null) {
        this.updateFilterUndefined({ value: false });
      }
    },

    async onSelectColumn(item, i) {
      if (item.group === 0) {
        this.setValueLimitSystemDefault(item, i);
      } else if (item.isDateTime) {
        this.setValueLimitEmpty(item, i);
      } else if (item.isText) {
        this.setValueLimitTextDistinct(item, i);
      } else if (item.isNumeric) {
        this.setValueLimitNumericRange(item, i);
      }
    },

    onSelectOperator(item, i) {
      let rs = [];

      if (this.filterObj.column.val === CommentsFilterColumn.COMMENT_LENGTH.name) {
        rs = item.hasRangeValues() ? [0, 500] : [0];
      } else if (this.filterObj.column.isNumeric) {
        if (item.hasRangeValues()) {
          rs = [...this.filterObj.valueLimit];
        } else {
          const val = this.filterObj.valueLimit?.slice().sort((a, b) => a - b)[0] || 0;
          rs = [val];
        }
      }

      this.editFilterValues({ index: i, values: rs });
    },

    setValueLimitEmpty(item, i) {
      this.editFilterValueLimit({ index: i, values: [] });
      this.editFilterValues({ index: i, values: [] });
    },

    async setValueLimitNumericRange(item, i) {
      const rs = await metadataApi.getRangeForNumericData(this.datasetId, item.metadataIndex);
      this.editFilterValueLimit({ index: i, values: rs || [] });
      this.editFilterValues({ index: i, values: rs || [0, 0] });
    },

    setValueLimitSystemDefault(item, i) {
      this.editFilterValueLimit({ index: i, values: item.defaultLimit || [] });
      let initVal;
      switch (item.val) {
        case CommentsFilterColumn.COMMENT_LENGTH.name:
          initVal = [0, 500];
          break;
        case CommentsFilterColumn.EMOTION_BREAKDOWN.name:
        case CommentsFilterColumn.EMOTION_INTENSITY.name:
          initVal = [];
          break;
        default:
          initVal = item.defaultLimit;
          break;
      }
      this.editFilterValues({ index: i, values: initVal });
    },

    async setValueLimitTextDistinct(item, i) {
      const rs = await metadataApi.getDistinctValuesForTextData(this.datasetId, item.metadataIndex);
      this.editFilterValueLimit({ index: i, values: rs || [] });
      this.editFilterValues({ index: i, values: [] });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

$cfli-tri-col-padding: 0.8rem;
$cfli-tri-col-width: 7.8rem;

.comments-filters-list-item {
  @include flex("block", "row", "start", "stretch");
  @include stretch;

  border-bottom: 1px solid $cfp-btn-bdr-clr;
  padding: 1.5rem 30px;

  .left {
    @include flex("block", "column", "start", "stretch");
    @include stretch;

    .filter-type {
      @include flex("block", "row", "start", "stretch");
      @include size-evenly;

      min-height: 1.8rem;

      .filter-column-select {
        @include size-evenly;

        margin-right: 0.5rem;

        &.unselected {
          margin-right: 0;
        }
      }

      .filter-emotion-select {
        @include size-evenly;

        margin-right: 0.5rem;
      }

      .filter-operator-select {
        @include flex("block", "row", "start", "start");
        @include size-evenly;
      }
    }

    .filter-value {
      margin-top: 0.7rem;
    }
  }

  .right {
    @include flex("block", "row", "center", "center");
    @include rigid;

    margin-left: 1rem;
    margin-right: -0.15rem;

    .close {
      @include flex("block", "row", "center", "center");

      cursor: pointer;
      height: 1rem;
      width: 1rem;

      &:hover .icon {
        opacity: 1;
        transform: scale(1.3);
      }

      .icon {
        height: 0.7rem;
        opacity: 0.5;
        transition: opacity $interaction-transition-time;
        width: 0.7rem;
      }
    }
  }
}
</style>
