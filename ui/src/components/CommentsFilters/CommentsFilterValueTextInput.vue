<template>
  <section class="comments-filter-value-text-input">
    <input class="text-input"
           :class="{ hasError }"
           v-model.lazy="textInput"
           placeholder="Please enter..." />
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

export default {
  name: 'comments-filter-value-text-input',

  props: {
    index: {
      type: Number,
      required: true,
    },
    filterObj: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapState('snippets', ['filtersAttempted']),

    hasError() {
      return this.filtersAttempted && !this.filterObj.values[0];
    },

    textInput: {
      get() {
        return this.filterObj.values[0];
      },
      set(value) {
        this.editFilterValues({ index: this.index, values: [value?.trim() || ''] });
      },
    },
  },

  methods: {
    ...mapActions('snippets', ['editFilterValues']),
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-filter-value-text-input {
  @include flex("block", "row", "start", "center");
  width: 100%;

  .text-input {
    border: 0;
    border-radius: 3px;
    padding: 0.5rem 0.3rem;
    font-size: $font-size-xxs;
    text-align: left;
    width: inherit;

    &.hasError {
      border: 2px solid clr("red");
    }
  }
}
</style>
