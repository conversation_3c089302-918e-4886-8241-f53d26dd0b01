<template>
  <section class="comments-filter-value-text-distinct"
           v-click-outside-handler="{ handler: 'onClickOutside' }">
    <base-dropdown :closeOnSelect="false"
                   :component="CommentsFilterValueTextDistinctItem"
                   :data="dataList"
                   :height="400"
                   :open="open"
                   :search="true"
                   @select="onSelectItem">
      <section class="dropdown-text" :class="{ hasError }" @click="open = !open">
        <section class="selected">
          <span class="none-selected" v-if="isEmpty">
            Please select...
          </span>
          <span v-else
                v-for="(item, index) in selectedList"
                :key="index"
                class="selected-item">
            {{ item }}
            <x-icon class="remove-icon" @click.stop="onClickRemoveItem(item)" />
          </span>
        </section>
        <i class="fa fa-caret-down icon icon-right" :class="{ open }"></i>
      </section>
    </base-dropdown>
  </section>
</template>

<script>
import { ChevronDownIcon, XIcon } from 'vue-feather-icons';
import { mapActions, mapState } from 'vuex';
import BaseDropdown from '@/components/Dropdown/BaseDropdown';
import clickOutsideHandler from '@/directives/click-outside-handler';
import CommentsFilterValueTextDistinctItem from '@/components/CommentsFilters/CommentsFilterValueTextDistinctItem';

export default {
  name: 'comments-filter-value-text-distinct',

  components: {
    BaseDropdown,
    ChevronDownIcon,
    XIcon,
  },

  directives: {
    clickOutsideHandler,
  },

  props: {
    index: {
      type: Number,
      required: true,
    },
    filterObj: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      CommentsFilterValueTextDistinctItem,
      open: false,
    };
  },

  computed: {
    ...mapState('snippets', ['filtersAttempted']),

    dataList() {
      return this.filterObj.valueLimit
        .filter(o => {
          if (this.filterObj.column.group !== 0) {
            return o?.content && o.count;
          }
          return o != null;
        })
        .map(o => {
          if (this.filterObj.column.group !== 0) {
            o = o.content;
          }
          return {
            content: o,
            selected: this.isSelected(o),
          };
        })
        .sort((a, b) => a.content.toLowerCase().localeCompare(b.content.toLowerCase()));
    },

    hasError() {
      return this.filtersAttempted && this.isEmpty;
    },

    isEmpty() {
      return !this.filterObj.values?.length;
    },

    selectedList() {
      return [
        ...this.filterObj.values.slice()
          .sort((a, b) => a.toLowerCase().localeCompare(b.toLowerCase())),
      ];
    },
  },

  methods: {
    ...mapActions('snippets', ['editFilterValues']),

    editAdd(val) {
      this.editFilterValues({
        index: this.index,
        values: [...this.filterObj.values, val],
      });
    },

    editRemove(val) {
      this.editFilterValues({
        index: this.index,
        values: [...this.filterObj.values.filter(o => o !== val)],
      });
    },

    isSelected(val) {
      return this.filterObj.values.includes(val);
    },

    onClickOutside(e) {
      if (!e.target?.parentElement) {
        return;
      }

      if (![
        'base-dropdown-search',
        'comments-filter-value-text-distinct-item list-item',
        'comments-filter-value-text-distinct-item-status',
        'list',
        'list-item',
      ].includes(e.target.parentElement.getAttribute('class'))) {
        this.open = false;
      }
    },

    onClickRemoveItem(val) {
      this.editRemove(val);
    },

    onSelectItem(item) {
      if (this.isSelected(item.content)) {
        this.editRemove(item.content);
      } else {
        this.editAdd(item.content);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-filter-value-text-distinct {
  @include flex("block", "row", "start", "center");

  font-size: $font-size-xxs;

  .base-dropdown {
    @include stretch;

    .dropdown-text {
      @include flex("block", "row", "between", "center");

      background-color: clr("white");
      border-radius: 2px;
      color: clr("black");
      padding: 0.1rem 0.2rem 0.2rem 0.4rem;

      &.hasError {
        border: 2px solid clr("red");
      }

      .selected {
        @include flex("block", "row", "start", "center");
        @include stretch;

        cursor: pointer;
        flex-wrap: wrap;
        min-height: 1.4rem;
        padding-bottom: 0.1rem;

        .none-selected {
          color: clr("black");
        }

        .selected-item {
          @include flex("block", "row", "start", "center");
          background-color: $cfp-btm-bar-apply-btn-bg;
          border-radius: $border-radius-medium;
          color: clr("white");
          margin: 0.2rem 0.2rem 0 0;
          padding: 0 0.3rem;
          width: fit-content;

          .remove-icon {
            cursor: pointer;
            margin-left: 0.5rem;
            stroke-width: 2px;
            width: 1rem;

            &:hover {
              color: clr("red");
            }
          }

          &:last-child {
            margin-right: 0;
          }
        }
      }

      .icon {
        color: clr("black");
        cursor: pointer;
        stroke-width: 3px;
        transition: all $interaction-transition-time;
        width: 0.7rem;

        &.open{
          transform: rotate(180deg) translateX(5px);
        }
      }
    }
  }
}
</style>
