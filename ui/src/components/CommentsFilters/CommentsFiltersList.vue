<template>
  <section class="comments-filters-list">
    <section class="filter-items">
      <comments-filters-list-item v-for="(obj, i) in filters"
                                  :filterObj="obj"
                                  :index="i"
                                  :key="getKey(obj)"
                                  class="filter-item">
      </comments-filters-list-item>
    </section>
    <section class="btn-wrapper">
      <base-button class="add-btn"
                   :disabled="filterUndefined"
                   @click="onClickAddFilter"
                   icon="plus"
                   size="small">
        Add Filter
      </base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import CommentsFiltersListItem from '@/components/CommentsFilters/CommentsFiltersListItem';

export default {
  name: 'comments-filters-list',

  components: {
    BaseButton,
    CommentsFiltersListItem,
  },

  computed: {
    ...mapState('snippets', ['filters', 'filterUndefined']),
  },

  methods: {
    ...mapActions('snippets', ['addNewFilter']),

    getKey(obj) {
      return obj.column?.val || -1;
    },

    onClickAddFilter() {
      if (!this.filterUndefined) {
        this.addNewFilter();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-filters-list {
  @include flex("block", "column", "start", "stretch");

  width: $themes-width-expanded;

  .btn-wrapper {
    border-bottom: 1px solid $cfp-divider-clr;

    .add-btn {
      background-color: transparent;
      border: 1px solid clr('white');
      border-radius: 2px;
      display: block;
      color: clr("white");
      font-size: $font-size-xxs;
      font-weight: $font-weight-bold;
      margin: 1.5rem 1.9rem;
      padding: 0.4rem 1.5rem;
      text-transform: uppercase;

      &.disabled-true {
        opacity: 0.2;
        cursor: not-allowed;
      }

      &:hover {
        background-color: rgba(255,255,255, 1);
        color: $cfp-save-bar-bg;

        &.disabled-true {
          background-color: transparent;
          color: clr("white");
        }
      }
    }
  }
}
</style>
