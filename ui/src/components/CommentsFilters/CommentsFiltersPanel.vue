<template>
  <section class="comments-filters-panel">
    <section class="top">
      <section class="left">
        <section class="title">
          <filter-icon class="icon-filter" />
          Filters
        </section>
        <section class="sub-title" v-if="filtersApplied.length">
          {{ filtersApplied.length }} Filter(s) Applied
        </section>
      </section>
      <section class="right">
        <img :src="require('@/assets/icon-close-white.svg')" class="icon" @click="onClickClosePanel"/>
      </section>
    </section>
    <section class="save-bar" v-if="false">
      <section class="save-list">
        <comments-filters-save-dropdown-list :options="savedFiltersList" />
      </section>
      <section class="btn save-btn" @click="onClickSaveFilters">
        Save
      </section>
      <section class="btn save-as-btn" @click="onClickSaveFiltersAs">
        Save As
      </section>
      <section class="btn edit-btn" @click="onClickEditFilters">
        Edit
      </section>
    </section>
    <section class="body" ref="body">
      <comments-filters-list />
    </section>
    <section class="bottom">
      <section class="clear-all" @click="onClickClearAll">
        <slash-icon class="icon"/>
        <span class="text">Clear All</span>
      </section>
      <section class="apply-filter" @click="onClickApplyFilters">
        <filter-icon class="icon-filter" />
        <span class="text">Apply</span>
        <i class="fa fa-arrow-right icon-arrow"></i>
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';
import { FilterIcon, SlashIcon } from 'vue-feather-icons';

import CommentsFiltersList from '@/components/CommentsFilters/CommentsFiltersList';
import CommentsFiltersSaveDropdownList from '@/components/CommentsFilters/CommentsFiltersSaveDropdownList';
import router from '@/router';
import Route from '@/enum/route';

import { metadataRequest, themesRequest } from '@/services/request';

export default {
  name: 'comments-filters-panel',

  components: {
    CommentsFiltersSaveDropdownList,
    CommentsFiltersList,
    FilterIcon,
    SlashIcon,
  },

  data() {
    return {
      clientHeight: 0,
      scrollHeight: 0,
    };
  },

  computed: {
    ...mapGetters('snippets', ['getFilterValidatedList']),

    ...mapState('snippets', ['filters', 'filtersApplied']),

    allowApplyBtn() {
      return this.getFilterValidatedList.length || this.filtersApplied.length;
    },

    hasErrors() {
      return this.filters.filter(o => o.column).length !== this.getFilterValidatedList.length;
    },

    savedFiltersList() {
      return [
        'List 1',
        'List 2',
        'List 1234567890 really extreme super ultra long name',
      ];
    },
  },

  created() {
    // using CommentsFiltersPopupList in Comments View
    if (router.currentRoute.name !== Route.COMMENTS) {
      this.resetSnippetsFilters();
    }
  },

  methods: {
    ...mapActions('snippets', [
      'addNewFilter',
      'applyFilters',
      'resetSnippets',
      'resetSnippetsFilters',
      'setFilters',
      'setFiltersAttempted',
      'setFilterShow',
    ]),

    ...mapActions('themes', { resetThemes: 'reset' }),

    async onClickApplyFilters() {
      if (this.hasErrors) {
        this.setFiltersAttempted({ value: true });
      } else if (this.allowApplyBtn) {
        this.setFiltersAttempted({ value: false });
        this.applyFilters();
        this.resetSnippets();

        await metadataRequest.applyFilterView();
        await this.reloadThemeListForFilterView();
        await metadataRequest.filterCommentsOnMetadata();
        await metadataRequest.filterCommentsCountOnMetadata();
      }
    },

    async onClickClearAll() {
      this.setFilters({ filters: [] });
      this.addNewFilter();
      this.applyFilters();
      this.resetSnippets();

      await this.reloadThemeListForFilterView();
      await metadataRequest.filterCommentsOnMetadata();
      await metadataRequest.filterCommentsCountOnMetadata();
    },

    onClickClosePanel() {
      this.setFilterShow({ value: false });
    },

    onClickSaveFilters() {
      // console.log('On click save filters');
    },

    onClickSaveFiltersAs() {
      // console.log('On click save filters As');
    },

    onClickEditFilters() {
      // console.log('On click edit filters');
    },

    async reloadThemeListForFilterView() {
      const themes = await themesRequest.fetchThemes();
      // todo
      // optional params - passing any custom val for initial-state
      // to avoid a circle:
      // >> apply filter
      // >> reset themes
      // >> reset theme-status (ThemeAnalysisView)
      // >> re-create Chart View
      // >> re-create CommentsFiltersPanel
      // >> reset filter onCreated
      // check this and themes/actions-reset method
      // todo - maybe replace loading-switch in ThemeAnalysisView with v-show?
      this.resetThemes({ themes });
      // this.setThemes({ themes });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

$cfp-panel-padding: 1rem 30px; // for the scroll-y overflow bar

.comments-filters-panel {
  @include flex("block", "column", "start", "stretch");
  @include panel;

  grid-area: themes-panel-left;
  background-color: $cfp-main-bg;
  border: none;
  border-radius: 4px 0 0 0;
  min-height: 600px;
  overflow: hidden;
  position: relative;
  width: $themes-width-expanded;

  .top {
    @include flex("block", "row", "between", "center");
    @include rigid;

    background-color: $cfp-top-bar-bg;
    border-bottom: 1px solid $cfp-divider-clr;
    padding: $cfp-panel-padding;

    .left {
      @include flex("block", "row", "start", "center");

      .title {
        @include flex("block", "row", "center", "center");

        color: clr("white");
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;

        .icon-filter {
          height: $font-size-sm;
          margin-right: 0.3rem;
          width: $font-size-sm;
        }
      }

      .sub-title {
        @include flex("block", "row", "center", "center");

        background-color: transparent;
        border: 1px solid $cfp-btn-bdr-clr;
        border-radius: $border-radius-rounded;
        color: clr("white");
        font-size: $font-size-xxs;
        font-weight: 400;
        height: 1.2rem;
        margin-left: 0.5rem;
        padding: 0 0.5rem;
        text-transform: uppercase;
      }
    }

    .right {
      @include flex("block", "row", "center", "center");

      .icon {
        cursor: pointer;
        height: 0.9rem;
        opacity: 0.5;
        transition: opacity $interaction-transition-time;
        width: 0.9rem;

        &:hover {
          opacity: 1;
          transform: scale(1.2);
        }
      }
    }
  }

  .save-bar {
    @include flex("block", "row", "start", "center");
    background-color: $cfp-save-bar-bg;
    border-bottom: 1px solid $cfp-divider-clr;
    padding: $cfp-panel-padding;
    width: inherit;

    .save-list {
      width: 10rem;
    }

    .btn {
      @include flex("block", "row", "center", "center");
      border: 1px solid $cfp-btn-bdr-clr;
      border-radius: $border-radius-small;
      color: clr("white");
      cursor: pointer;
      font-size: $font-size-xxs;
      margin-left: 0.5rem;
      padding: 0.2rem;
      text-transform: uppercase;

      &.save-btn {
        background-color: $cfp-save-bar-save-btn;
        width: 5rem;
      }

      &.save-as-btn {
        background-color: $cfp-save-bar-save-btn;
        width: 9rem;
      }

      &.edit-btn {
        background-color: $cfp-save-bar-edit-btn;
        width: 5rem;
      }

      &:hover {
        background-color: $cfp-save-bar-save-btn-hover;
      }
    }
  }

  .body {
    @include flex("block", "column", "start", "stretch");

    overflow-y: auto;
    overflow-x: hidden;
    width: inherit;
  }

  .bottom {
    @include flex("block", "row", "between", "center");

    min-height: fit-content;
    padding: $cfp-panel-padding;
    position: relative;
    top: 0;
    width: inherit;

    .clear-all {
      @include flex("block", "row", "start", "center");
      background-color: $cfp-btm-bar-clear-btn-bg;
      border: 1px solid $cfp-btm-bar-clear-btn-bdr;
      border-radius: 2px;
      color: clr("white");
      cursor: pointer;
      padding: 0.3rem 0.3rem 0.3rem 0.2rem;

      .icon {
        height: $font-size-sm;
      }

      .text {
        font-size: 0.7rem;
        font-weight: 600;
        text-transform: uppercase;
      }

      &:hover {
        background-color: $cfp-btm-bar-clear-btn-bdr;
      }
    }

    .apply-filter {
      @include flex("block", "row", "start", "center");
      background-color: $cfp-btm-bar-apply-btn-bg;
      border: 1px solid $cfp-btm-bar-apply-btn-bg;
      border-radius: 2px;
      color: clr("white");
      cursor: pointer;
      padding: 0.3rem;
      text-transform: uppercase;

      .icon-filter {
        height: $font-size-sm;
      }

      .text {
        font-size: 0.7rem;
        font-weight: 600;
        text-transform: uppercase;
      }

      .icon-arrow {
        margin-left: 1rem;
        font-size: $font-size-xxs;
      }

      &:hover {
        background-color: $cfp-btm-bar-apply-btn-hover;
      }
    }
  }
}
</style>
