<template>
  <section class="comments-filter-value-datetime">
    <section class="picker-section section-from" :class="{ hasRange }">
      <time-picker class="picker"
                   id="from"
                   ref="fpicker"
                   v-bind="pickerTimeFromProps"
                   v-model="pickerTimeFromModel"
                   @is-shown="pickerFromVisible = true"
                   @is-hidden="pickerFromVisible = false">
        <section class="selected-btn" :class="{ hasError: hasErrorTimeFrom }">
          <section class="btn-body">
            {{ formattedTimeFrom }}
          </section>
          <section class="btn-close">
            <x-icon class="icon icon-remove" v-if="timeFrom" @click="removeTimeFrom" />
            <calendar-icon class="icon" v-else />
          </section>
        </section>
      </time-picker>

      <section v-if="mounted" class="tooltip" v-tooltip.bottom.left.notrigger="{ html: 'from-picker-tooltip', class: 'tooltip-datepicker date-time-picker', visible: pickerFromVisible }"></section>
    </section>
    <section class="picker-section section-to" v-if="hasRange">
      <time-picker class="picker"
                   id="to"
                   ref="tpicker"
                   v-bind="pickerTimeToProps"
                   v-model="pickerTimeToModel"
                   @is-shown="pickerToVisible = true"
                   @is-hidden="pickerToVisible = false">
        <section class="selected-btn" :class="{ hasError: hasErrorTimeTo }">
          <section class="btn-body">
            {{ formattedTimeTo }}
          </section>
          <section class="btn-close">
            <x-icon class="icon icon-remove" v-if="timeTo" @click="removeTimeTo" />
            <calendar-icon class="icon" v-else />
          </section>
        </section>
      </time-picker>

      <section v-if="mounted" class="tooltip" v-tooltip.bottom.left.notrigger="{ html: 'to-picker-tooltip', class: 'tooltip-datepicker date-time-picker', visible: pickerToVisible }"></section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { CalendarIcon, ChevronDownIcon, XIcon } from 'vue-feather-icons';
import { format, parseISO } from 'date-fns';
import TimePicker from 'vue-ctk-date-time-picker';

export default {
  name: 'comments-filter-value-datetime',

  components: {
    CalendarIcon,
    ChevronDownIcon,
    TimePicker,
    XIcon,
  },

  props: {
    index: {
      type: Number,
      required: true,
    },
    filterObj: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      formatter: 'd/M/yy',
      mounted: false,
      pickerFormatter: 'yyyy-MM-dd',
      pickerFromVisible: false,
      pickerToVisible: false,
      timeFrom: null,
      timeTo: null,
    };
  },

  mounted() {
    this.timeFrom = this.filterObj.values[0] || null;
    this.timeTo = this.filterObj.values[1] || null;

    if (this.$refs?.fpicker?.$el) {
      this.$refs.fpicker.$el.querySelector('.datetimepicker').id = 'from-picker-tooltip';
    }

    if (this.$refs?.tpicker?.$el) {
      this.$refs.tpicker.$el.querySelector('.datetimepicker').id = 'to-picker-tooltip';
    }

    this.mounted = true;
  },

  computed: {
    ...mapState('snippets', ['filtersAttempted']),

    operator() {
      return this.filterObj.operator;
    },

    formattedTimeFrom() {
      if (this.timeFrom) {
        return format(this.timeFrom, this.formatter);
      }
      return this.hasRange ? 'Date From' : 'Please Select Date';
    },

    formattedTimeTo() {
      if (this.timeTo) {
        return format(this.timeTo, this.formatter);
      }
      return 'Date To';
    },

    hasErrorTimeFrom() {
      return this.filtersAttempted && !this.timeFrom;
    },

    hasErrorTimeTo() {
      return this.filtersAttempted && !this.timeTo;
    },

    hasRange() {
      return this.filterObj.operator.hasRangeValues();
    },

    pickerTimeFromModel: {
      get() {
        if (this.timeFrom) {
          return format(this.timeFrom, this.pickerFormatter);
        }
        return null;
      },
      set(time) {
        this.timeFrom = parseISO(time);
        this.updateObjValue();
      },
    },

    pickerTimeToModel: {
      get() {
        if (this.timeTo) {
          return format(this.timeTo, this.pickerFormatter);
        }
        return null;
      },
      set(time) {
        this.timeTo = parseISO(time);
        this.updateObjValue();
      },
    },

    /**
     * picker properties
     */
    pickerProps() {
      return {
        buttonColor: '#a15aef',
        color: '#2d1757',
        format: 'YYYY-MM-DD',
        noButtonNow: true,
        noHeader: true,
        noValueToCustomElem: true,
        onlyDate: true,
      };
    },

    pickerTimeFromProps() {
      return {
        ...this.pickerProps,
        maxDate: this.timeTo ? format(this.timeTo, this.pickerFormatter) : null,
      };
    },

    pickerTimeToProps() {
      return {
        ...this.pickerProps,
        minDate: this.timeFrom ? format(this.timeFrom, this.pickerFormatter) : null,
      };
    },
  },

  methods: {
    ...mapActions('snippets', ['editFilterValues']),

    updateObjValue() {
      const values = this.hasRange ? [this.timeFrom, this.timeTo] : [this.timeFrom];
      this.editFilterValues({ index: this.index, values });
    },

    removeTimeFrom() {
      this.timeFrom = null;
      this.updateObjValue();
    },

    removeTimeTo() {
      this.timeTo = null;
      this.updateObjValue();
    },
  },

  watch: {
    operator() {
      this.timeTo = this.hasRange ? this.timeTo : null;
      this.updateObjValue();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-filter-value-datetime {
  @include flex("block", "row", "between", "center");

  position: relative;

  .picker-section {
    @include flex("block", "column", "start", "start");

    margin-right: 0.5rem;

    &.section-from, &.section-to {
      @include size-evenly;
    }

    &:last-child {
      margin-right: 0;
    }

    .selected-btn {
      @include flex("block", "row", "between", "center");
      background-color: clr("white");
      border-radius: 3px;
      font-size: $font-size-xxs;
      font-weight: 300;
      height: 1.8rem;
      padding: 0 0.5rem;

      &.hasError {
        border: 2px solid clr("red");
      }

      .btn-body {
        @include flex("block", "row", "start", "center");
        cursor: pointer;
        margin-right: 0.2rem;
        width: 100%;
      }

      .btn-close {
        .icon {
          cursor: pointer;
          width: $font-size-xs;

          &.icon-remove {
            &:hover {
              color: clr("red");
            }
          }
        }
      }
    }
  }
}
</style>
