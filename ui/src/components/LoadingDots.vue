<template>
  <section class="loading-dots">
    <section class="container">
      <section></section>
      <section></section>
      <section></section>
    </section>
  </section>
</template>

<script>
export default {
  name: 'loading-dots',
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

@-webkit-keyframes color-change {
  0%, 100% {
    background-color: #7350FF;
  }
  50% {
    background-color: #CFC3FF;
  }
}

@keyframes color-change {
  0%, 100% {
    background-color: #7350FF;
  }
  50% {
    background-color: #CFC3FF;
  }
}

.loading-dots {
  @include flex("block", "column", "center", "center");

  .container {
    text-align: center;

    section {
      background-color: #7350FF;
      border-radius: 50%;
      display: inline-block;
      height: 8px;
      margin-right: 2px;
      width: 8px;

      -webkit-animation: color-change 1.2s infinite;
      animation: color-change 1.2s infinite;
    }

    section:nth-child(2) {
      -webkit-animation-delay: -1.1s;
      animation-delay: -1.1s;
    }

    section:nth-child(3) {
      -webkit-animation-delay: -1s;
      animation-delay: -1s;
    }
  }
}
</style>
