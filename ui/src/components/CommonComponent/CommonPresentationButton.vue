<template>
  <section class="common-presentation-button" @click="onClick">
    Presentation
    <i class="fa fa-desktop icon"></i>
  </section>
</template>

<script>
export default {
  name: 'common-presentation-button',

  methods: {
    onClick() {
      this.$emit('btnClick');
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.common-presentation-button {
  background-color: $presentation-btn-bg;
  border-radius: $border-radius-small;
  color: clr('white');
  cursor: pointer;
  font-size: 0.65rem;
  font-weight: $font-weight-bold;
  padding: 0.45rem 0.7rem;
  text-transform: uppercase;
  transition: opacity $interaction-transition-time;

  &:hover {
    opacity: 0.7;
  }

  .icon {
    margin-left: 0.3rem;
  }
}
</style>
