<template>
  <section class="common-index-number">
    {{ index }}
  </section>
</template>

<script>
export default {
  name: 'common-index-number',

  props: {
    index: {
      type: Number,
      required: true,
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.common-index-number {
  @include flex("block", "row", "center", "center");

  background-color: #7f7bb3;
  border-radius: $border-radius-medium;
  color: clr("white");
  font-size: $font-size-xxs;
  font-weight: 600;
  height: 1rem;
  width: 1rem;
}
</style>
