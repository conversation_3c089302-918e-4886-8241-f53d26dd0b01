<template>
  <section class="common-close-modal-button" @click="onClick">
    <i class="fa-light fa-xmark x-icon"></i>
  </section>
</template>

<script>
export default {
  name: 'common-close-modal-button',

  methods: {
    onClick() {
      this.$emit('click');
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.common-close-modal-button {
  .x-icon {
    @include flex("block", "row", "center", "center");

    background-color: #2D1757;
    border-radius: 50%;
    color: clr('white');
    cursor: pointer;
    height: 1.2rem;
    transition: opacity $interaction-transition-time;
    width: 1.2rem;

    &:hover {
      filter: brightness(125%);
    }
  }
}
</style>
