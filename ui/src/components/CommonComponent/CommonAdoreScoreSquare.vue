<template>
  <section class="common-adore-score-square"
           :class="[className]"
           :style="{
             height: `${size}rem`,
             width: `${size}rem`,
           }"
  >
    <div class="text">{{ textScore }}</div>
    <div :class="{ footer }"></div>
  </section>
</template>

<script>
import { kebabCase } from 'lodash-es';
import { mapGetters } from 'vuex';

export default {
  name: 'common-adore-score-square',

  props: {
    colorBorder: {
      type: Boolean,
      default: false,
    },
    footer: {
      type: Boolean,
      default: false,
    },
    percentage: {
      type: Boolean,
      default: false,
    },
    score: {
      type: [Number, String],
      required: true,
    },
    showZero: {
      type: Boolean,
      default: false,
    },
    signifier: {
      type: Boolean,
      default: false,
    },
    size: {
      type: Number,
      required: false,
      default: 2,
    },
  },

  computed: {
    ...mapGetters('benchmark', ['classifyAdorescore']),

    bucket() {
      return this.classifyAdorescore(this.score);
    },

    className() {
      if (!this.colorBorder) return '';
      return kebabCase(this.bucket.name);
    },

    textScore() {
      let rs = this.score;
      if (this.percentage) {
        rs = (this.score !== 0 || this.showZero) ? `${rs}%` : '<1%';
      }
      if (this.signifier) {
        rs = (this.score >= 0) ? `+${rs}` : rs;
      }
      return rs;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.common-adore-score-square {
  @include flex("block", "column", "start", "center");

  background-color: clr('white');
  border: 1px solid $border-color-dark;
  border-radius: $border-radius-medium;

  &.fair {
    border-color: $fair-bdr;
    color: $fair-score-txt;

    .footer {
      background-color: $fair-inner-bg;
    }
  }

  &.good {
    border-color: $good-bdr;
    color: $good-score-txt;

    .footer {
      background-color: $good-inner-bg;
    }
  }

  &.poor {
    border-color: $poor-bdr;
    color: $poor-score-txt;

    .footer {
      background-color: $poor-inner-bg;
    }
  }

  &.very-good {
    border-color: $very-good-bdr;
    color: $very-good-score-txt;

    .footer {
      background-color: $very-good-inner-bg;
    }
  }

  &.very-poor {
    border-color: $very-poor-bdr;
    color: $very-poor-score-txt;

    .footer {
      background-color: $very-poor-inner-bg;
    }
  }

  .text {
    @include flex("block", "row", "center", "center");
    @include stretch;

    font-size: $font-size-xs;
    font-weight: $font-weight-bold;
  }

  .footer {
    height: 0.3rem;
    width: 100%;
  }
}
</style>
