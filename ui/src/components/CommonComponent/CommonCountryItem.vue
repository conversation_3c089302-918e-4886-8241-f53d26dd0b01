<template>
  <section class="common-country-item">
    <section class="item-label">{{ data.content }}</section>
    <section class="item-status">
      <i class="fa fa-check icon" v-if="data.selected"></i>
    </section>
  </section>
</template>

<script>
export default {
  name: 'common-country-item',

  props: {
    data: {
      type: Object,
      required: true,
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';
@import '~font-awesome/css/font-awesome.css';

.common-country-item {
  @include flex('block', 'row', 'start', 'center');
  cursor: pointer;
  padding: 1em;

  &:hover {
    background-color: clr('purple', 'lighter');
  }

  .item-label {
    @include flex('block', 'row', 'start', 'center');
    width: 100%;
  }

  .item-status {
    @include flex('block', 'row', 'end', 'center');
    width: 3rem;

    .icon {
      color: clr("green", "dark");
    }
  }
}
</style>
