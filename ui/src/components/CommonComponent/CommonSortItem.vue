<template>
  <section class="common-sort-item" :class="{ visible }" @click="onClick">
    <section class="header" :class="{ active, fontSizeXs, fontSizeXxs}">
      {{ header.titleCase() }}
    </section>
    <section class="sort" :class="{ fontSizeXs, fontSizeXxs}">
      <i v-if="active && asc" class="fa-solid fa-sort-up icon" :class="{ active: active && asc }"/>
      <i v-if="active && !asc" class="fa-solid fa-sort-down icon" :class="{ active: active && !asc }"/>
      <i v-if="!active" class="fa-solid fa-sort icon"/>
    </section>
  </section>
</template>

<script>
import SortDirection from '@/enum/sort-direction';

export default {
  name: 'common-sort-item',

  components: {
  },

  props: {
    current: {
      type: Object,
      required: true,
    },
    fontSizeXs: {
      type: Boolean,
      default: false,
    },
    fontSizeXxs: {
      type: <PERSON>olean,
      default: false,
    },
    header: {
      type: Object,
      required: true,
    },
    visible: {
      type: <PERSON>olean,
      default: true,
    },
  },

  computed: {
    active() {
      return this.current.sort === this.header;
    },

    asc() {
      return this.current.direction === SortDirection.ASC;
    },
  },

  methods: {
    emitEvent(sort, direction) {
      this.$emit('sort-event', { sort, direction });
    },

    onClick() {
      if (this.active) {
        this.emitEvent(this.header, this.current.direction.inverse);
      } else {
        this.emitEvent(this.header, SortDirection.DESC);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.common-sort-item {
  @include flex("block", "row", "start", "center");
  cursor: pointer;
  display: none;

  &.visible {
    display: flex;
  }

  .header {
    font-size: $font-size-sm;

    &.active,
    &:hover {
      color: clr("purple");
    }

    &.fontSizeXs {
      font-size: $font-size-xs;
    }

    &.fontSizeXxs {
      font-size: $font-size-xxs;
    }
  }

  .sort {
    @include flex("block", "column", "start", "center");
    margin-left: 0.2rem;

    &.fontSizeXs {
      .icon {
        font-size: $font-size-xs;
      }
    }

    &.fontSizeXxs {
      .icon {
        font-size: $font-size-xxs;
      }
    }

    .icon {
      font-size: $font-size-sm;

      &.active,
      &:hover {
        color: clr("purple");
      }
    }
  }
}
</style>
