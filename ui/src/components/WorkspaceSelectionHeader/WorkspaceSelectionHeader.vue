<template>
  <section class="workspace-selection-header">
    <section class="header-content">
      <img v-if="isLogoSrcExist"
           class="logo"
           height="36"
           width="172"
           :src="require(`@/assets/${this.organisationName}/logo.svg`)"
      />
<!--      <img v-else alt="Your logo" class="missing-logo"/>-->
      <section class="user-info">
        <span class="text">Signed in as: <b>{{fullName}}</b></span>
        <user-organisation-role class="org-role" />
      </section>
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import UserOrganisationRole from '@/components/UserOrganisationRole';

export default {
  name: 'workspace-selection-header',

  components: {
    UserOrganisationRole,
  },

  computed: {
    ...mapState('user', ['user']),

    ...mapState('organisation', ['organisation']),

    fullName() {
      return `${this.user.firstName} ${this.user.lastName}`;
    },

    organisationName() {
      return this.organisation.name;
    },

    isLogoSrcExist() {
      try {
        // eslint-disable-next-line import/no-dynamic-require,global-require
        require(`@/assets/${this.organisationName}/logo.svg`);
        return true;
      } catch (e) {
        return false;
      }
    },
  },

  methods: {
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.workspace-selection-header {
  @include flex("block", "row", "center", "center");

  background-color: rgba(19, 28, 41, 0.02);
  height: 92px;
  position: relative;
  width: 100%;

  .header-content {
    @include flex("block", "row", "space-between", "start");

    height: 100%;
    width: 800px; // TODO: make this to be common variable

    .logo {
      @include flex("block", "row", "start", "center");

      height: 100%;
    }

    .missing-logo {
      margin-top: auto;
      margin-bottom: auto;
    }

    .user-info {
      @include flex("block", "row", "start", "center");

      height: 100%;

      .text {
        font-size: 11px;
      }
    }
  }

  .org-role {
    margin-left: 0.6rem;
  }
}
</style>
