<template>
  <section class="themes-builder-item-sub-topic"
           :class="{ focus: !localItem.brandNew || focusLabel || focusQuery || hoverLabel || hoverQuery }"
  >
    <section class="sub-topic-icon">
      <span class="icon"></span>
      <span class="gap"></span>
    </section>
    <section class="sub-topic-label">
      <span class="label">
        SubTheme Label
        <span v-show="localItem.invalidLabel || localItem.duplicatedLabel"> *</span>
      </span>
      <section class="input" :class="{ invalid: localItem.invalidLabel || localItem.duplicatedLabel }">
        <input ref="labelInput"
               v-model="inputLabel"
               @focusin="focusLabel = true"
               @focusout="focusLabel = false"
               @keydown.tab.prevent="onTabLabel"
               @mouseenter="hoverLabel = true"
               @mouseleave="hoverLabel = false"
        />
      </section>
    </section>
    <section class="query query-input-wrapper">
      <span class="label">
        Query
        <span v-show="localItem.invalidQuery"> *</span>
      </span>
      <section class="input query-input-wrapper"
               :class="{ 'has-dropdown': showQueryOption, invalid: localItem.invalidQuery }"
               @focusin="focusQuery = true"
               @focusout="focusQuery = false"
               @mouseenter="hoverQuery = true"
               @mouseleave="hoverQuery = false"
      >
        <i class="fa-regular fa-magnifying-glass icon" />
        <themes-builder-query-input ref="queryInput"
                                    :id="id"
                                    :text="localItem.query"
                                    @inputQuery="doSetQuery"
                                    @openDropdown="onOpenOptionDropdown"
                                    @onTab="onTabQuery"
                                    @previewQuery="onClickPreview"
        />
        <section class="preview-btn"
                 :class="{ 'is-previewing': isPreviewing }"
                 v-show="!isQueryEmpty"
                 @click="onClickPreview"
        >
          <span class="text">Preview</span>
          <i class="fa-regular fa-arrow-right-to-line icon"></i>
        </section>
      </section>
      <!-- Options Dropdown -->
      <themes-builder-query-option :open="showQueryOption"
                                   @clickOutside="onClickOutsideQueryOption"
                                   @selectOperator="onSelectOperator"
                                   @toggleExactSearch="onToggleExactSearch"
                                   @toggleRemoveDuplicates="onToggleRemoveDuplicates"
      />
    </section>
    <section class="close" :class="{ hide: !isRemovable }">
      <span class="label">&nbsp;</span>
      <i class="fa-regular fa-times icon" @click="onClickRemove" />
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';
import bus from '@/helpers/bus';
import searchQueryConverter from '@/helpers/search-query-converter';
import ThemesBuilderQueryInput from '@/components/ThemesBuilderItem/ThemesBuilderQueryInput';
import ThemesBuilderQueryOption from '@/components/ThemesBuilderItem/ThemesBuilderQueryOption';

export default {
  name: 'themes-builder-item-sub-topic',

  components: {
    ThemesBuilderQueryInput,
    ThemesBuilderQueryOption,
  },

  props: {
    focus: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [Number, String],
      required: true,
    },
    index: {
      type: Number,
      required: true,
    },
    parentId: {
      type: [Number, String],
      required: true,
    },
    validating: {
      type: Boolean,
      default: false,
    },
  },

  computed: {
    ...mapGetters('themesBuilder', [
      'getBuildingSubtopic',
      'isLabelValid',
      'isQueryValid',
      'validateBuildingItem',
    ]),

    ...mapState('themesBuilder', ['previewId', 'showQueryOptionDropdown']),

    inputLabel: {
      get() {
        return this.localItem.label || '';
      },
      set(val) {
        this.doSetLabel(val);
      },
    },

    isPreviewing() {
      return this.previewId === this.id;
    },

    isQueryEmpty() {
      return !this.localItem.query?.trim().length;
    },

    isRemovable() {
      return !this.localItem.brandNew;
    },

    showQueryOption() {
      return this.showQueryOptionDropdown === this.id;
    },
  },

  beforeDestroy() {
    bus.$off('theme-builder-remove-invalid-subtopic', this.doRemoveInvalid);
    bus.$off('theme-builder-update-excluding-list', this.doUpdateExcludingList);
  },

  created() {
    bus.$on('theme-builder-remove-invalid-subtopic', this.doRemoveInvalid);
    bus.$on('theme-builder-update-excluding-list', this.doUpdateExcludingList);

    const subtopic = this.getBuildingSubtopic(this.id, this.parentId);
    this.localItem = {
      ...subtopic,
      query: subtopic.query || '',
    };
  },

  data() {
    return {
      focusLabel: false,
      focusQuery: false,
      hoverLabel: false,
      hoverQuery: false,
      localItem: null,
    };
  },

  methods: {
    ...mapActions('themesBuilder', [
      'setPressedTab',
      'setPreviewId',
      'setPreviewIdParent',
      'setPreviewQuery',
      'setShowPreviewPanel',
      'setShowQueryOptionDropdown',
    ]),

    doRemoveInvalid(val) {
      if (this.id === val) {
        this.$emit('removeSubTopic', { index: this.index });
      }
    },

    doSetLabel(val) {
      const { invalidLabel, duplicatedLabel } = this.isLabelValid(val, this.id, this.parentId);
      this.localItem = {
        ...this.localItem,
        brandNew: false,
        duplicatedLabel,
        invalidLabel,
        label: val,
      };
      this.$emit('editSubTopic', {
        index: this.index,
        sub: this.localItem,
      });
    },

    doSetQuery({ val }) {
      if (this.isPreviewing) {
        this.setPreviewId({ id: null });
        this.setPreviewIdParent({ parentId: null });
      }
      this.localItem = {
        ...this.localItem,
        brandNew: false,
        excludeContentIds: [],
        invalidQuery: !this.isQueryValid(val),
        query: val,
      };
      this.$emit('editSubTopic', {
        index: this.index,
        sub: this.localItem,
      });
    },

    doUpdateExcludingList({ id, excludeContentIds }) {
      if (id === this.id) {
        this.localItem = {
          ...this.localItem,
          excludeContentIds,
        };
        this.$emit('editSubTopic', {
          index: this.index,
          sub: this.localItem,
        });
      }
    },

    onClickOutsideQueryOption() {
      if (this.showQueryOption) {
        this.setShowQueryOptionDropdown({ id: null });
      }
    },

    onClickPreview() {
      if (this.localItem.invalidQuery) {
        return;
      }

      const query = searchQueryConverter(this.localItem.query.trim());
      this.setPreviewQuery({ query });
      this.setPreviewId({ id: this.id });
      this.setPreviewIdParent({ parentId: this.parentId });
      this.setShowPreviewPanel({ show: true });
    },

    onClickRemove() {
      if (this.isRemovable) {
        this.$emit('removeSubTopic', { index: this.index });
      }
    },

    onOpenOptionDropdown() {
      this.setShowQueryOptionDropdown({ id: this.id });
    },

    onSelectOperator(val) {
      bus.$emit(`select-operator-for-theme-builder-${this.id}`, val);
    },

    onToggleExactSearch(val) {
      this.localItem = {
        ...this.localItem,
        brandNew: false,
        exact: val,
      };
      this.$emit('editSubTopic', {
        index: this.index,
        sub: this.localItem,
      });
    },

    onTabLabel(e) {
      e.preventDefault();
      this.$refs.queryInput.onFocusEditor();
    },

    onTabQuery() {
      if (this.showQueryOption) {
        this.setShowQueryOptionDropdown({ id: null });
      }
    },

    onToggleRemoveDuplicates(val) {
      this.localItem = {
        ...this.localItem,
        brandNew: false,
        distinctContent: val,
      };
      this.$emit('editSubTopic', {
        index: this.index,
        sub: this.localItem,
      });
    },

    validateSubTopic() {
      const validatedItem = this.validateBuildingItem(this.id, this.parentId);
      if (validatedItem !== null) {
        this.localItem = { ...validatedItem };
        this.$emit('editSubTopic', {
          index: this.index,
          sub: this.localItem,
        });
      }
      this.$emit('finishValidating');
    },
  },

  watch: {
    focus() {
      this.$refs.labelInput.focus();
      this.setPressedTab({ tab: null });
    },

    validating() {
      if (this.validating) {
        this.validateSubTopic();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-builder-item-sub-topic {
  @include flex("block", "row", "start", "center");
  @include stretch;
  line-height: 1.5rem;
  opacity: 0.5;

  &.focus {
    opacity: 1;
  }

  &:hover {
    .close {
      &:not(.hide) .icon {
        cursor: pointer;
        opacity: 0.5;
      }
    }
  }

  .sub-topic-icon {
    @include flex("block", "column", "center", "start");
    margin-right: 7px;
    width: 8px;

    .icon {
      border-bottom: 1px solid rgba(97, 87, 192, 1);
      border-left: 1px solid rgba(97, 87, 192, 1);
      height: 8px;
      margin-top: -32px;
      width: 8px;
    }
  }

  .sub-topic-label {
    @include flex("block", "column", "center", "start");
    width: 145px;

    .label {
      color: rgba(97, 87, 192, 1);
      font-size: 10px;
      font-weight: 800;
      text-transform: uppercase;
    }

    .input {
      @include flex("block", "row", "start", "center");
      background: #FFFFFF;
      border: 1px solid rgba(97, 87, 192, 1);
      border-radius: 2px;
      height: 30px;
      padding: 0.2rem 0.5rem;
      width: inherit;

      &.invalid {
        border: 1px solid clr("red");
      }

      input {
        border: none;
        color: rgba(20, 20, 20, 1);
        font-size: 12px;
        width: 100%;

        &:focus, &:active {
          outline: none;
        }
      }
    }
  }

  .query {
    @include flex("block", "column", "center", "start");
    @include stretch;
    margin-left: 0.5rem;
    position: relative;
    width: 100%;

    .label {
      color: rgba(97, 87, 192, 1);
      font-size: 10px;
      font-weight: 800;
      text-transform: uppercase;
    }

    .input {
      @include flex("block", "row", "start", "center");
      background: #FFFFFF;
      border: 1px solid rgba(97, 87, 192, 1);
      border-radius: 2px;
      padding-left: 0.5rem;
      width: inherit;

      &.has-dropdown {
        border-radius: 2px 2px 0 0;
      }

      &.invalid {
        border: 1px solid clr("red");
      }

      .icon {
        color: #2D1757;
        font-size: 14px;
        margin-right: 0.5rem;
      }

      .themes-builder-query-input {
        height: 28px;
      }

      .preview-btn {
        @include flex("block", "row", "start", "center");
        color: rgba(45, 23, 87, 1);
        cursor: pointer;
        font-size: 10px;
        font-weight: $font-weight-medium;
        height: 28px;
        padding-left: 0.7rem;
        padding-right: 0.4rem;
        text-transform: uppercase;

        .icon {
          margin-left: 0.2rem;
        }

        &.is-previewing {
          background-color: #2D1757;
          color: clr('white');

          .icon {
            color: clr('white');
          }
        }
      }
    }

    .themes-builder-query-option {
      border-radius: 0 0 2px 2px;
      border-top: none;
      top: 54px;
    }
  }

  .close {
    @include flex("block", "column", "center", "start");
    margin-left: 0.5rem;

    .label {
      opacity: 0;
    }

    .icon {
      opacity: 0;
    }
  }
}
</style>
