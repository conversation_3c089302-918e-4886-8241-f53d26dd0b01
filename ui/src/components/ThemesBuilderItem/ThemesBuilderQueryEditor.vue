<template>
  <section
      class="themes-builder-query-editor"
      :contenteditable="!readonly"
      ref="editor"
      tabindex="-1"
      @blur="onBlur"
      @copy="onCopy"
      @focus="onFocus"
      @input="onInput"
      @paste="onPaste"
      @keydown.delete.exact="onBackspace"
      @keydown.enter.exact="onEnter"
      @keydown.enter.shift.exact="onEnter"
      @keydown.ctrl.90.stop.prevent
      @keydown.meta.90.stop.prevent
      @keydown.tab.exact="onTab"
  ></section>
</template>

<script>
import SearchOperator from '@/enum/search-operator';

const classMap = {
  '(': 'light',
  ')': 'light',
  AND: 'blue',
  NOT: 'red',
  OR: 'yellow',
};

const conversionMap = {
  '&': 'AND',
  '!': 'NOT',
  '|': 'OR',
};

export default {
  name: 'themes-builder-query-editor',

  props: {
    clear: {
      type: Boolean,
      default: false,
    },

    focused: {
      type: <PERSON>olean,
      default: false,
    },

    query: {
      type: String,
      default: '',
    },

    readonly: {
      type: <PERSON>olean,
      default: false,
    },

    tag: {
      type: [Object, null],
      validator: (value) => {
        return value == null || value instanceof SearchOperator;
      },
    },
  },

  data() {
    return {
      lastAnchor: null,
      lastAnchorOffset: null,
      lastFocus: null,
      lastFocusOffset: null,
      text: '',
    };
  },

  computed: {
    tagList() {
      return Object.keys(classMap);
    },
  },

  watch: {
    clear() {
      if (this.clear) this.onClear();
    },

    focused() {
      if (this.focused) this.$refs.editor.focus();
    },

    query() {
      if (this.query == null) return;

      this.setText(this.query);

      this.$emit('query-updated');
    },

    resetSelection() {
      if (this.resetSelection) this.onResetSelection();

      this.$emit('selection-reset');
    },

    tag() {
      if (this.tag == null) return;

      const { editor } = this.$refs;

      if ((this.isText(this.lastAnchor) || this.isSpacer(this.lastAnchor))
          && (this.isText(this.lastAnchor) || this.isSpacer(this.lastFocus))
          || (editor.isSameNode(this.lastAnchor) && editor.isSameNode(this.lastFocus))) {
        this.insertTag(this.tag);
      }

      if (this.isSpacer(this.lastAnchor)
          && this.isSpacer(this.lastFocus)
          && this.lastAnchor.isSameNode(this.lastFocus)) {
        const node = document.createTextNode(this.tag.name);

        editor.replaceChild(node, this.lastAnchor);

        this.onInput();
      }

      this.$emit('tag-inserted');
    },

    text() {
      this.setLastSelection();

      this.$emit('input', this.text);
    },
  },

  mounted() {
    const { editor } = this.$refs;
    this.lastAnchor = editor;
    this.lastAnchorOffset = 0;
    this.lastFocus = editor;
    this.lastFocusOffset = 0;

    document.addEventListener('selectionchange', this.onSelectionChange);

    this.setText(this.query || '');
  },

  beforeDestroy() {
    document.removeEventListener('selectionchange', this.onSelectionChange);
  },

  methods: {
    convertNodesToFrag(nodes) {
      const frag = document.createDocumentFragment();

      nodes.forEach(node => frag.appendChild(node));

      return frag;
    },

    convertSpacerTags() {
      const { editor } = this.$refs;
      const nodes = editor.childNodes;

      if (nodes.length === 0) return;

      nodes.forEach(node => {
        if (this.isSpacer(node) && !/\n/gm.test(node.innerText)) {
          const textNode = document.createTextNode(node.innerText);
          const textLength = node.innerText.length;

          editor.replaceChild(textNode, node);

          this.setSelection(textNode, textLength);
        }
      });
    },

    convertTextToFrag(input) {
      return this.convertNodesToFrag(this.convertTextToNodes(input));
    },

    convertTextToNodes(input) {
      if (input === '') return [];

      const split = input.split(/(\(|\)|&|\||!|AND|OR|NOT)/gm).map(text => text.trim());

      const nodes = split.map(text => {
        const converted = conversionMap[text] != null ? conversionMap[text] : text;

        if (text === '') return this.createSpacerNode();
        if (this.tagList.includes(converted)) return this.createTagNode(converted);

        return document.createTextNode(converted);
      });

      return nodes;
    },

    convertTextNodesToTagNodes() {
      const { editor } = this.$refs;
      const nodes = editor.childNodes;

      nodes.forEach(node => {
        if (this.isText(node)) {
          if (/\(|\)|&|\||!|AND|OR|NOT/gm.test(node.nodeValue)) {
            const frag = this.convertTextToFrag(node.nodeValue);
            const { lastChild } = frag;
            const textLength = lastChild.textContent.length;

            editor.replaceChild(frag, node);

            this.setSelection(lastChild, textLength);
          }
        }
      });
    },

    createSpacerNode() {
      const node = document.createElement('span');
      node.className = 'spacer';

      if (navigator.userAgent.toLowerCase().indexOf('firefox') > -1) {
        node.innerHTML = '&nbsp;';
      } else {
        node.innerHTML = '<br>';
      }

      return node;
    },

    createTagNode(text) {
      const textNode = document.createTextNode(text);
      const node = document.createElement('span');
      node.className = `tag ${classMap[text]}`;
      node.setAttribute('contenteditable', 'false');
      node.appendChild(textNode);
      node.addEventListener('click', this.onClickTag);

      return node;
    },

    getNodeList() {
      return this.$refs.editor.childNodes;
    },

    getRange() {
      return window.getSelection().getRangeAt(0);
    },

    getSelection() {
      return window.getSelection();
    },

    insertTag(tag) {
      const node = document.createTextNode(tag.name);
      const range = document.createRange();

      range.setStart(this.lastAnchor, this.lastAnchorOffset);
      range.setEnd(this.lastFocus, this.lastFocusOffset);
      range.deleteContents();
      range.insertNode(node);

      this.onInput();
    },

    isSpacer(node) {
      if (node.classList == null) return false;

      return node.classList.contains('spacer');
    },

    isTag(node) {
      if (node.classList == null) return false;

      return node.classList.contains('tag');
    },

    isText(node) {
      return node.nodeName === '#text' && node.parentNode === this.$refs.editor;
    },

    onBackspace(e) {
      const selection = this.getSelection();

      if (selection.isCollapsed
          && this.isText(selection.anchorNode)
          && selection.anchorNode.textContent.length === 1
          && selection.anchorOffset === 1) {
        e.preventDefault();

        const { editor } = this.$refs;
        const spacer = this.createSpacerNode();

        editor.replaceChild(spacer, selection.anchorNode);

        this.setSelection(spacer);
      }

      if (this.$refs.editor.textContent === '') this.onClear();
    },

    onBlur() {
      this.$emit('blur');
    },

    onClear() {
      this.setText('');
      this.$emit('clear');
    },

    onClickTag(e) {
      const { editor } = this.$refs;

      const node = e.target;
      const index = [...editor.childNodes.values()].indexOf(node);

      this.setSelection(editor.childNodes[index + 1], 0);
    },

    onCopy(e) {
      const text = window.getSelection().toString().replace(/\n|\r/gm, ' ');
      e.clipboardData.setData('text', text);
      e.preventDefault();
    },

    onFocus() {
      this.$emit('focus');
    },

    onInput() {
      // Work around for FireFox, the issue happens when the input is a child of a "spacer" instead of the editor, so, whether it happened, removed the spacer
      if (navigator.userAgent.toLowerCase().indexOf('firefox') > -1) {
        if (this.isSpacer(this.lastAnchor.parentNode) && this.lastAnchor.nodeName === '#text') {
          this.$refs.editor.appendChild(this.lastAnchor);
          this.$refs.editor.removeChild(this.lastAnchor.parentNode);
        }
      }

      this.text = this.$refs.editor.textContent.replace(/\s+/gm, ' ');

      this.$refs.editor.normalize();
      this.convertTextNodesToTagNodes();
      this.resolveTagSpacing();
      this.convertSpacerTags();
    },

    onEnter(e) {
      e.preventDefault();
      this.$emit('enter');
    },

    onPaste(e) {
      e.preventDefault();

      const text = e.clipboardData.getData('text').replace(/\s+/gm, ' ');
      const sanitised = this.sanitiseText(text);
      const frag = this.convertTextToFrag(sanitised);

      const range = this.getRange();
      range.deleteContents();

      if (this.isSpacer(range.startContainer)
          && this.isSpacer(range.endContainer)
          && range.startContainer === range.endContainer) {
        this.$refs.editor.replaceChild(frag, range.startContainer);
      } else {
        range.insertNode(frag);
      }

      this.getSelection().collapseToEnd();
      this.onInput();
    },

    onSelectionChange() {
      this.setLastSelection();
    },

    onTab(e) {
      e.preventDefault();
      this.$emit('tab');
    },

    resolveTagSpacing() {
      const { editor } = this.$refs;
      const nodes = editor.childNodes;

      if (nodes.length === 0) return;

      if (this.isTag(nodes[0])) editor.insertBefore(this.createSpacerNode(), nodes[0]);

      let limit = nodes.length;

      for (let i = 1; i < limit; i += 1) {
        if (this.isTag(nodes[i - 1]) && this.isTag(nodes[i])) {
          editor.insertBefore(this.createSpacerNode(), nodes[i]);
          limit += 1;
        }

        if (this.isSpacer(nodes[i - 1]) && this.isSpacer(nodes[i])) {
          editor.removeChild(nodes[i]);
          limit -= 1;
        }

        if (i === limit - 1 && this.isTag(nodes[i])) {
          editor.appendChild(this.createSpacerNode());
        }
      }
    },

    sanitiseText(text) {
      const replaced = text.replace(/&|\||!/gm, match => conversionMap[match]).replace(/AND\s?NOT/gm, 'NOT');
      return replaced;
    },

    setLastSelection() {
      const range = this.getRange();

      this.lastAnchor = range.startContainer;
      this.lastAnchorOffset = range.startOffset;
      this.lastFocus = range.endContainer;
      this.lastFocusOffset = range.endOffset;
    },

    setSelection(node, offset, node2 = node, offset2 = offset) {
      const range = document.createRange();

      range.setStart(node, offset);
      range.setEnd(node2, offset2);

      this.getSelection().removeAllRanges();
      this.getSelection().addRange(range);
    },

    setText(text) {
      this.$refs.editor.innerHTML = '';

      const sanitised = this.sanitiseText(text);
      const frag = this.convertTextToFrag(sanitised);

      this.$refs.editor.appendChild(frag);

      this.onInput();
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-builder-query-editor {
  @include flex("block", "row", "start", "center");
  @include rigid;
  @include scrollbar-thin;

  color: $search-dropdown-txt;
  height: auto;
  min-height: 30%; // fix Firefox cursor issue
  overflow-x: hidden;
  overflow-y: hidden;
  position: absolute;
  white-space: nowrap;
  width: 100%;

  &:active,
  &:focus {
    outline: none;
    border: none;
  }

  .tag {
    @include flex("block", "row", "center", "center");
    @include button-gradient;

    border: $border-standard;
    border-radius: 3px;
    font-family: "Courier New", Courier, monospace;
    font-size: 12px;
    font-weight: $font-weight-bold;
    height: 20px;
    margin: 0 0.4rem;
    padding: 0.25rem 0.3rem 0.125rem 0.3rem;
    position: relative;
    white-space: nowrap;
    width: fit-content;

    &.light {
      border-color: $body-copy-light;
      color: $body-copy-light;
      width: $operator-tag-light-width;
    }

    &.red {
      border-color: clr("red");
      color: clr("red");
    }

    &.yellow {
      border-color: clr("yellow");
      color: clr("yellow");
    }

    &.blue {
      border-color: clr("blue");
      color: clr("blue");
    }
  }

  .spacer {
    display: inline-block;
    min-width: 0.5em;
  }
}
</style>
