<template>
  <section class="themes-builder-query-option-operators">
    <span class="label">Add</span>
    <i class="fa-regular fa-arrow-right-long icon"></i>
    <themes-builder-query-option-operator-item
        class="operator-item"
        v-for="operator in operators"
        :key="operator.ordinal"
        :operator="operator"
        @selectOperator="onSelectOperator"
    />
    <section class="vertical-line" />
  </section>
</template>

<script>
import SearchOperator from '@/enum/search-operator';
import ThemesBuilderQueryOptionOperatorItem from '@/components/ThemesBuilderItem/ThemesBuilderQueryOptionOperatorItem';

export default {
  name: 'themes-builder-query-option-operators',

  components: {
    ThemesBuilderQueryOptionOperatorItem,
  },

  computed: {
    operators() {
      return SearchOperator.enumValues;
    },
  },

  methods: {
    onSelectOperator(val) {
      this.$emit('selectOperator', val);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-builder-query-option-operators {
  @include flex("block", "row", "center", "center");
  padding: 0 6px;

  .label {
    color: rgba(97, 87, 192, 1);
    font-size: 10px;
    font-weight: $font-weight-bold;
    text-transform: uppercase;
  }

  .icon {
    color: rgba(97, 87, 192, 1);
    font-size: 12px;
    margin: 0 3px;
  }

  .operator-item {
    margin-right: 6px;
  }

  .vertical-line {
    border-left: 1px solid rgba(64, 51, 168, 0.2);
    height: 30px;
  }
}
</style>
