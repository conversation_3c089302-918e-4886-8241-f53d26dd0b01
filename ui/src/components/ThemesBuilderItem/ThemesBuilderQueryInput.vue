<template>
  <section class="themes-builder-query-input" @click="onClick">
    <themes-builder-query-editor
        :clear="!text.length"
        :focused="focused"
        :query="query"
        :tag="tag"
        @blur="onBlurEditor"
        @enter="onEnterEditor"
        @focus="onFocusEditor"
        @input="onInputEditor"
        @query-updated="query = null"
        @tag-inserted="tag = null"
        @tab="onTab"
    />
  </section>
</template>

<script>
import { mapActions } from 'vuex';
import bus from '@/helpers/bus';
import ThemesBuilderQueryEditor from '@/components/ThemesBuilderItem/ThemesBuilderQueryEditor';

export default {
  name: 'themes-builder-query-input',

  components: {
    ThemesBuilderQueryEditor,
  },

  props: {
    id: {
      type: [Number, String],
      default: -1,
    },
    text: {
      type: String,
      default: '',
    },
  },

  beforeDestroy() {
    bus.$off(`select-operator-for-theme-builder-${this.id}`, this.onSelectOperator);
    bus.$off('theme-builder-via-search-bar', this.createdViaSearchBar);
  },

  created() {
    bus.$on(`select-operator-for-theme-builder-${this.id}`, this.onSelectOperator);
    bus.$on('theme-builder-via-search-bar', this.createdViaSearchBar);
  },

  data() {
    return {
      focused: false,
      query: null,
      tag: null,
    };
  },

  methods: {
    ...mapActions('themesBuilder', ['setPressedTab']),

    createdViaSearchBar({ id, query }) {
      if (id === this.id) {
        this.query = query;
      }
    },

    onBlurEditor() {
      this.focused = false;
    },

    onClick() {
      this.focused = true;
    },

    onEnterEditor() {
      this.$emit('previewQuery');
    },

    onFocusEditor() {
      this.focused = true;
    },

    onInputEditor(text) {
      this.$emit('inputQuery', { val: text });
    },

    onSelectOperator(val) {
      this.tag = val;
    },

    onTab() {
      this.$emit('onTab');
      this.setPressedTab({ tab: this.id });
    },
  },

  watch: {
    focused() {
      if (this.focused) {
        this.$emit('openDropdown');
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-builder-query-input {
  @include flex("inline", "row", "start", "center");
  @include stretch;

  background:clr("white");
  cursor: text;
  height: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  position: relative;
  transition: all $interaction-transition-time;

  .themes-builder-query-editor {
    color: rgba(20, 20, 20, 1);
    font-size: 12px;
  }
}
</style>
