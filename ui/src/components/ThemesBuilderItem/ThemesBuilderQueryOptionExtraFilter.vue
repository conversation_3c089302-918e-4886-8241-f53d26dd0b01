<template>
  <section class="themes-builder-query-option-extra-filter">
    <section class="filter-item exact-search" @click="onClickExactSearch">
      <base-checkbox :value="exactSearch" @click.prevent.stop />
      <span class="label">Exact Search</span>
    </section>
    <section class="filter-item remove-duplicates" @click="onClickRemoveDuplicates">
      <base-checkbox :value="removeDuplicates" @click.prevent.stop />
      <span class="label">Remove Duplicates</span>
    </section>
  </section>
</template>

<script>
import BaseCheckbox from '@/components/Base/BaseCheckbox';

export default {
  name: 'themes-builder-query-option-extra-filter',

  components: {
    BaseCheckbox,
  },

  props: {
    distinct: {
      type: Boolean,
      default: false,
    },
    exact: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      exactSearch: false,
      removeDuplicates: false,
    };
  },

  created() {
    this.exactSearch = this.exact;
    this.removeDuplicates = this.distinct;
  },

  methods: {
    onClickExactSearch() {
      this.exactSearch = !this.exactSearch;
      this.$emit('toggleExactSearch', this.exactSearch);
    },

    onClickRemoveDuplicates() {
      this.removeDuplicates = !this.removeDuplicates;
      this.$emit('toggleRemoveDuplicates', this.removeDuplicates);
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.themes-builder-query-option-extra-filter {
  @include flex('block', 'row', 'start', 'center');
  overflow-x: auto;
  overflow-y: hidden;

  .filter-item {
    @include flex('block', 'row', 'start', 'center');
    cursor: pointer;

    &:not(:first-child) {
      margin-left: 6px;
    }

    .label {
      font-size: 10px;
      margin-left: 3px;
    }
  }
}
</style>
