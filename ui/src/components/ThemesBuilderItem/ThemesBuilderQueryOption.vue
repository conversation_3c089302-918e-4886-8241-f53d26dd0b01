<template>
  <section
    class="themes-builder-query-option"
    v-click-outside-handler="{
      handler: 'onClickOutside',
      excludedParentClasses: [
        'filter-item',
        'query-input-wrapper',
        'themes-builder-query-input',
        'themes-builder-query-option',
        'themes-builder-query-option-operators',
      ],
    }"
    v-show="open"
  >
    <themes-builder-query-option-operators @selectOperator="onSelectOperator" />
    <themes-builder-query-option-extra-filter
      :distinct="distinct"
      :exact="exact"
      @toggleExactSearch="onToggleExactSearch"
      @toggleRemoveDuplicates="onToggleRemoveDuplicates"
    />
  </section>
</template>

<script>
import clickOutsideHandler from '@/directives/click-outside-handler';
import ThemesBuilderQueryOptionExtraFilter from '@/components/ThemesBuilderItem/ThemesBuilderQueryOptionExtraFilter';
import ThemesBuilderQueryOptionOperators from '@/components/ThemesBuilderItem/ThemesBuilderQueryOptionOperators';

export default {
  name: 'themes-builder-query-option',
  components: { ThemesBuilderQueryOptionOperators, ThemesBuilderQueryOptionExtraFilter },
  directives: {
    clickOutsideHandler,
    ThemesBuilderQueryOptionExtraFilter,
    ThemesBuilderQueryOptionOperators,
  },

  props: {
    distinct: {
      type: Boolean,
      default: false,
    },
    exact: {
      type: Boolean,
      default: false,
    },
    open: {
      type: Boolean,
      default: false,
    },
  },

  methods: {
    onClickOutside() {
      this.$emit('clickOutside');
    },

    onSelectOperator(val) {
      this.$emit('selectOperator', val);
    },

    onToggleExactSearch(val) {
      this.$emit('toggleExactSearch', val);
    },

    onToggleRemoveDuplicates(val) {
      this.$emit('toggleRemoveDuplicates', val);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-builder-query-option {
  @include flex("block", "row", "start", "center");
  @include panel;

  background: #FFFFFF;
  border: 1px solid rgba(97, 87, 192, 1);
  height: 30px;
  position: absolute;
  width: 100%;
  z-index: 2;
}
</style>
