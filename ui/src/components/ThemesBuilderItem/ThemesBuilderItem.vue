<template>
  <section class="themes-builder-item">
    <themes-builder-item-theme-parent :focus="focusedInput === id"
                                      :id="id"
                                      @editParentTheme="onEditParentTheme"
                                      @removeParentTheme="onRemoveParentTheme"
                                      @finishValidating="onFinishValidatingParentTheme"
    />
    <themes-builder-item-sub-topic v-for="(subId, subIndex) in dataList"
                                   :focus="focusedInput === subId"
                                   :id="subId"
                                   :index="subIndex"
                                   :key="subId"
                                   :parentId="id"
                                   :validating="validatingSubtopic"
                                   @editSubTopic="onEditSubTopic"
                                   @removeSubTopic="onRemoveSubTopic"
                                   @finishValidating="onFinishValidatingSubtopic"
    />
  </section>
</template>

<script>
import { v4 as uuid } from 'uuid';
import { mapActions, mapGetters, mapState } from 'vuex';
import ThemesBuilderItemSubTopic from '@/components/ThemesBuilderItem/ThemesBuilderItemSubTopic';
import ThemesBuilderItemThemeParent from '@/components/ThemesBuilderItem/ThemesBuilderItemThemeParent';

export default {
  name: 'themes-builder-item',

  components: {
    ThemesBuilderItemSubTopic,
    ThemesBuilderItemThemeParent,
  },

  props: {
    id: {
      type: [Number, String],
      required: true,
    },
    index: {
      type: Number,
      required: true,
    },
  },

  computed: {
    ...mapGetters('themesBuilder', ['getBuildingTheme']),

    ...mapState('themesBuilder', [
      'buildingThemeList',
      'pressedTab',
      'validatingCount',
    ]),

    dataList() {
      return this.localList.map(s => s.id);
    },

    hasNewSubTopic() {
      return this.localList.filter(s => s.brandNew).length > 0;
    },
  },

  created() {
    const subtopics = this.getBuildingTheme(this.id)?.subtopics || [];
    this.localList = [
      ...subtopics.map(s => {
        return { ...s, id: s.id || uuid() };
      }),
    ];

    if (!this.hasNewSubTopic) {
      this.addNewSubTopic();
      this.updateSubtopicList();
    }
  },

  data() {
    return {
      focusedInput: null,
      localList: [],
      validatingSubtopic: false,
      validatingSubtopicCount: 0,
    };
  },

  methods: {
    ...mapActions('themesBuilder', [
      'addNewTheme',
      'removeTheme',
      'setPressedTab',
      'setValidatingCount',
      'updateSubtopics',
      'updateTheme',
    ]),

    addNewSubTopic() {
      this.localList.push({
        brandNew: true,
        id: uuid(),
      });
    },

    onEditParentTheme({ item }) {
      this.updateTheme({
        index: this.index,
        theme: { ...item, subtopics: this.localList },
      });
      this.$emit('updateItem');
    },

    onEditSubTopic({ index, sub }) {
      this.localList.splice(index, 1, sub);
      if (!this.hasNewSubTopic) {
        this.addNewSubTopic();
      }
      this.updateSubtopicList();
    },

    onFinishValidatingParentTheme() {
      this.validatingSubtopicCount = 0;
      this.validatingSubtopic = true;
    },

    onFinishValidatingSubtopic() {
      this.validatingSubtopicCount += 1;
    },

    onRemoveParentTheme() {
      this.removeTheme({ index: this.index });

      if (!this.buildingThemeList.length) {
        this.addNewTheme({
          theme: {
            brandNew: true,
            id: uuid(),
          },
        });
      }
    },

    onRemoveSubTopic({ index }) {
      this.localList.splice(index, 1);
      if (!this.hasNewSubTopic) {
        this.addNewSubTopic();
      }
      this.updateSubtopicList();
    },

    updateSubtopicList() {
      this.updateSubtopics({
        parentIndex: this.index,
        list: [...this.localList],
      });
      this.$emit('updateItem');
    },
  },

  watch: {
    pressedTab() {
      if (this.pressedTab) {
        for (let i = 0; i < this.buildingThemeList.length; i += 1) {
          const theme = this.buildingThemeList[i];
          const nextTheme = this.buildingThemeList[i + 1];
          if (this.pressedTab === theme.id) {
            this.focusedInput = theme.subtopics[0].id;
            break;
          } else {
            for (let j = 0; j < theme.subtopics.length; j += 1) {
              const subtopic = theme.subtopics[j];
              const nextSubtopic = theme.subtopics[j + 1];
              if (this.pressedTab === subtopic.id) {
                if (nextSubtopic) {
                  this.focusedInput = nextSubtopic.id;
                  break;
                } else if (nextTheme) {
                  this.focusedInput = nextTheme.id;
                  break;
                } else {
                  this.focusedInput = null;
                  this.setPressedTab({ tab: null });
                }
              }
            }
          }
        }
      } else {
        this.focusedInput = null;
      }
    },

    validatingSubtopicCount() {
      if (this.validatingSubtopicCount === this.localList.length) {
        this.validatingSubtopicCount = 0;
        this.validatingSubtopic = false;
        this.setValidatingCount({ value: Number(this.validatingCount + 1) });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-builder-item {
  @include stretch;
  background: rgba(97, 87, 192, 0.1);
  border-radius: 3px;
  padding: 12px 1rem 20px 1rem;

  .themes-builder-item-sub-topic {
    margin-top: 0.5rem;
  }
}
</style>
