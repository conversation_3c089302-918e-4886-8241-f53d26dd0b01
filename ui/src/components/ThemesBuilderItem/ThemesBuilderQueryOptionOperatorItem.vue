<template>
  <section
      class="themes-builder-query-option-operator-item"
      :class="{ 'operator-open': operator.name === '(', 'operator-close': operator.name === ')' }"
      :style="style"
      @click="onClick"
      @mouseenter="hover = true"
      @mouseleave="hover = false"
  >{{ operator.name }}</section>
</template>

<script>
import clickOutsideTag from '@/directives/click-outside-tag';

export default {
  name: 'themes-builder-query-option-operator-item',

  directives: {
    clickOutsideTag,
  },

  props: {
    operator: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      hover: false,
      style: {
        '--colour': this.operator.color(),
        '--hoverBgColour': this.operator.hoverBgColor(),
        '--hoverColour': this.operator.hoverColor(),
      },
    };
  },

  methods: {
    onClick() {
      this.$emit('selectOperator', this.operator);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-builder-query-option-operator-item {
  @include button-gradient;
  @include flex("block", "row", "center", "center");
  @include panel;

  border-color: var(--colour);
  border-radius: 3px;
  color: var(--colour);
  cursor: pointer;
  font-family: "Courier New", Courier, monospace;
  font-size: 10px;
  font-weight: bold;
  height: 20px;
  padding: 0 5px;
  transition: all $interaction-transition-time;
  user-select: none;
  width: fit-content;

  &:hover,
  &:focus {
    background: var(--hoverBgColour);
    border-color: var(--hoverColour);
    color: var(--hoverColour);
  }

  &.operator-close {
    padding: 0 4px 0 6px;
  }

  &.operator-open {
    padding: 0 6px 0 4px;
  }
}
</style>
