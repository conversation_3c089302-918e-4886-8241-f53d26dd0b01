<template>
  <section class="swot-chart-panel-header">
    <section class="swot-chart-configurations-left">
      <section class="chart-mode-toggle"
               @click="onClickShowChartMode"
               v-tooltip.top="{
                class: 'tooltip-padding-025',
                content: 'Expand / Collapse the Chart',
                delay: 0,
              }"
      >
        <i class="fa fa-indent" v-if="showChartMaxWidth"></i>
        <i class="fa fa-outdent" v-else></i>
      </section>
      <section class="title"
         v-tooltip.top="{
              class: 'tooltip-padding-025',
              content: selectedTitle,
              delay: 0,
            }"
         @click.stop
      >
        <base-input
            class="input"
            ref="localTitle"
            v-model="localTitle"
            @blur="onLabelBlur"
            @submit="onLabelSubmit"
        />
      </section>
      <section class="option">
        <span>LABELS</span>
        <base-toggle-button @click="onClickLabels" :value="showSwotLabel"></base-toggle-button>
      </section>
      <section class="option">
        <span>CUSTOM MARKERS</span>
        <base-toggle-button @click="onClickCustomMarkers" :value="showSwotCustomMarker"></base-toggle-button>
      </section>
    </section>
    <section class="swot-chart-configurations-right">
      <swot-chart-edit v-if="isEditor" />
      <swot-chart-options />
      <common-presentation-button class="presentation-btn" @btnClick="onClickPresent"></common-presentation-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';
import { BarChart2Icon, CheckIcon, Edit2Icon, SlashIcon } from 'vue-feather-icons';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import BaseToggleButton from '@/components/Base/BaseToggleButton';
import CommonPresentationButton from '@/components/CommonComponent/CommonPresentationButton';
import CustomChartType from '@/enum/custom-chart-type';
import intercomEvent from '@/services/IntercomEvent';
import intercomEvents from '@/enum/intercom-events';
import PreventSampleAction from '@/components/Mixins/PreventSampleAction';
import SwotChartEdit from '@/components/SwotChartEdit/SwotChartEdit';
import SwotChartOptions from '@/components/SwotChartOption/SwotChartOptions';
import SwotPresentation from '@/components/SwotPresentation/SwotPresentation';

import { datasetsRequestV0 } from '@/services/request';

export default {
  name: 'swot-chart-panel-header',

  components: {
    BarChart2Icon,
    BaseButton,
    BaseInput,
    BaseToggleButton,
    CheckIcon,
    CommonPresentationButton,
    Edit2Icon,
    SlashIcon,
    SwotChartEdit,
    SwotChartOptions,
  },

  mixins: [PreventSampleAction],

  created() {
    this.localTitle = this.customChartTitles[CustomChartType.SWOT.name];
    this.setShowChartMaxWidth({ value: false });
  },

  data() {
    return {
      localTitle: '',
    };
  },

  watch: {
    selectedTitle() {
      this.localTitle = this.selectedTitle;
    },
  },

  computed: {
    ...mapState('datasets', ['active', 'selected']),

    ...mapState('themes', ['customChartTitles']),

    ...mapGetters('datasets', ['get', 'isEditable']),

    ...mapState('swotChart', {
      customFlag: state => state.customFlag,
      showChartMaxWidth: state => state.showChartMaxWidth,
      showSwotLabel: state => state.chartOptions.showLabel,
      showSwotCustomMarker: state => state.chartOptions.showCustomMarker,
    }),

    isEditor() {
      return this.isEditable(this.active);
    },

    isSample() {
      return this.get(this.active).localSample;
    },

    selectedTitle() {
      return this.customChartTitles[CustomChartType.SWOT.name];
    },
  },

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    ...mapActions('swotChart', [
      'setShowChartMaxWidth',
      'setShowLabel',
      'setShowCustomMarker',
    ]),

    ...mapActions('themes', ['setCustomChartTitles']),

    onLabelBlur() {
      if (this.$refs.localTitle.value !== this.selectedTitle) {
        datasetsRequestV0.persistCustomChart(CustomChartType.SWOT, this.localTitle);
        this.setCustomChartTitles({ key: CustomChartType.SWOT.name, title: this.localTitle });
      }
    },

    onLabelSubmit(_, element) {
      element.blur();
    },

    onClickLabels() {
      this.setShowLabel({ show: !this.showSwotLabel });
    },

    onClickCustomMarkers() {
      this.setShowCustomMarker({ show: !this.showSwotCustomMarker });
    },

    onClickPresent() {
      intercomEvent.send(intercomEvents.VIEW_SWOT_PRESENTATION);
      this.setModalComponent({ component: SwotPresentation });
    },

    onClickShowChartMode() {
      this.setShowChartMaxWidth({ value: !this.showChartMaxWidth });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.swot-chart-panel-header {
  @include flex("block", "row", "between", "stretch");

  border-bottom: 1px solid #807DBA;
  height: 50px;
  padding: 0.7rem 1rem;

  .chart-mode-toggle {
    @include flex("block", "row", "start", "center");
    border: 1px solid $border-color;
    border-radius: $border-radius-small;
    cursor: pointer;
    font-size: $font-size-sm;
    margin-right: 0.5rem;
    padding: 0.3rem;
  }

  .option {
    @include flex("block", "row", "start", "center");

    margin-right: 1em;

    .base-checkbox {
      margin-left: 0.3em;
      pointer-events: none;
    }

    span {
      color: $insights-tab-highlight;
      font-size: $font-size-xxs;
      font-weight: $font-weight-bold;
    }
  }

  .swot-chart-configurations-left {
    @include flex("block", "row", "center", "center");
  }

  .swot-chart-configurations-right {
    @include flex("block", "row", "center", "center");
    padding-right: 1rem;

    .base-button {
      margin-right: 1em;
      padding: 0.4rem;
    }

    .presentation-btn {
      margin-left: 0.5rem;
    }
  }

  .title {
    @include flex("block", "row", "start", "center");

    margin-right: 1em;

    .input {
      background-color: transparent;
      border: none;
      border-bottom: $border-light solid transparent;
      border-radius: 0;
      color: clr("purple", "rich");
      font-size: $font-size-sm;
      font-weight: $font-weight-medium;
      padding: 0;
      transition: all $interaction-transition-time;
      width: 100%;

      &:hover:not(:disabled),
      &:focus {
        border-bottom: $border-light solid clr("purple");
      }

      &:focus,
      &:active {
        outline: none;
      }
      &:disabled {
        background-color: transparent;
      }
    }
  }
}
</style>
