<template>
  <section class="swot-chart-panel" :class="{editQuadrants}">
    <swot-chart-panel-header/>
    <swot-chart-edit-mode v-if="editQuadrants" @close="editQuadrants = false"/>
    <loading-switch :status="themesStatus">
      <template #default>
        <swot-chart scId="swot-chart" />
      </template>
      <template #loading>
        <loading-blocks-overlay>Loading Themes</loading-blocks-overlay>
      </template>
    </loading-switch>
  </section>
</template>

<script>
import { mapGetters, mapState } from 'vuex';

import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import LoadingSwitch from '@/components/LoadingSwitch';
import NetworkKeys from '@/enum/network-keys';
import SwotChart from '@/components/SwotChart/SwotChart';
import SwotChartEditMode from '@/components/SwotChartEdit/SwotChartEditMode';
import SwotChartPanelHeader from '@/components/SwotChartPanel/SwotChartPanelHeader';

export default {
  name: 'swot-chart-panel',

  components: {
    LoadingBlocksOverlay,
    LoadingSwitch,
    SwotChart,
    SwotChartEditMode,
    SwotChartPanelHeader,
  },

  data() {
    return {
      openEdit: false,
    };
  },

  computed: {
    ...mapState('datasets', ['active']),

    ...mapState('swotChart', ['editQuadrants']),

    ...mapGetters('datasets', ['get']),

    ...mapGetters('network', ['status']),

    isSample() {
      return this.get(this.active).localSample;
    },

    themesStatus() {
      return this.status(NetworkKeys.THEMES);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.swot-chart-panel {
  @include flex("block", "column", "start", "stretch");
  @include stretch;
  grid-area: swot-chart-panel;

  height: $theme-chart-height;
  margin-bottom: -2em;

  .loading-blocks-overlay {
    height: 100%;
  }

  &.editQuadrants {
    height: $theme-chart-height * 1.05;
  }
}
</style>
