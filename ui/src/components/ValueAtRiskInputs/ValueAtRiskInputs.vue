<template>
  <section class="value-at-risk-inputs">
    <value-at-risk-section-header :position="2">
      <span slot="header">
        Enter Details
      </span>
    </value-at-risk-section-header>

    <value-at-risk-inputs-cx v-if="varInfo.valueAtRiskType === 'CUSTOMER'" />
    <value-at-risk-inputs-ex v-if="varInfo.valueAtRiskType === 'EMPLOYEE'" />

<!--    <section class="preview">-->
<!--      <section class="description">Total Cost Per Comment Preview:</section>-->
<!--      <section class="preview-amount">${{ previewCost }}</section>-->
<!--    </section>-->
  </section>
</template>

<script>
import { mapState } from 'vuex';

import ValueAtRiskInputsCx from '@/components/ValueAtRiskInputs/ValueAtRiskInputsCx';
import ValueAtRiskInputsEx from '@/components/ValueAtRiskInputs/ValueAtRiskInputsEx';
import ValueAtRiskSectionHeader from '@/components/ValueAtRisk/ValueAtRiskSectionHeader';

export default {
  name: 'value-at-risk-inputs',

  components: {
    ValueAtRiskInputsCx,
    ValueAtRiskInputsEx,
    ValueAtRiskSectionHeader,
  },

  computed: {
    ...mapState('valueAtRisk', ['varInfo']),
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.value-at-risk-inputs {
  @include flex("block", "row", "start", "start");

  border-bottom: 1px solid $border-color;
  padding: 1.5rem 0;
}
</style>
