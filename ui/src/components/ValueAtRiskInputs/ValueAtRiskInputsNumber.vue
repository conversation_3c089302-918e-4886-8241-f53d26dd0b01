<template>
  <section class="value-at-risk-inputs-number" >
    <section class="value-at-risk-input-value" :class="{focus,'input-error': hasError }">
      <section class="symbol">
        <i v-if="icon" :class="icon" />
        <img v-if="iconSvg" :src="require(`@/assets/${iconSvg}.svg`)" alt="iconSvg"/>
      </section>
      <input class="input" :value="modelValue" @blur="onBlur" @focus="focus = true" @input="onInput" />
      <section class="stepper">
        <i class="fa-solid fa-sort-up icon-stepper icon-up" @click.stop="onClickUp" />
        <i class="fa-solid fa-sort-down icon-stepper icon-down" @click.stop="onClickDown" />
      </section>
    </section>
    <!-- Error message -->
    <p v-if="hasError" class="error-message">{{ errorMessage }}</p>
  </section>
</template>

<script>
import { parseInt } from 'lodash-es';

export default {
  name: 'value-at-risk-inputs-number',

  props: {
    icon: {
      type: String,
      required: false,
    },
    iconSvg: {
      type: String,
      required: false,
    },
    value: {
      value: [Number],
      default: '',
    },
    errorMessage: {
      type: String,
      default: '',
    },
    hasError: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      focus: false,
      modelValue: this.value,
    };
  },

  watch: {
    value() {
      this.modelValue = this.value;
    },
  },

  methods: {
    onBlur() {
      this.focus = false;
      this.$emit('blur');
    },

    onClickDown() {
      if (this.modelValue > 0) {
        this.modelValue -= 1;
        this.$emit('input', this.modelValue);
      }
    },

    onClickUp() {
      this.modelValue += 1;
      this.$emit('input', this.modelValue);
    },

    onInput(e) {
      const value = e.target.value.replace(/[^0-9]/g, '');
      if (value) {
        e.target.value = parseInt(value);
        this.modelValue = e.target.value;
        this.$emit('input', this.modelValue);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.value-at-risk-inputs-number {
  @include flex("block", "column", "start", "start");

  .value-at-risk-input-value {
    @include flex("block", "row", "start", "center");

    background-color: white;
    border-radius: 3px;
    border: $border-standard;
    height: 2rem;
    width: 280px;

    &:hover {
      border: $border-light solid mix($border-color, clr("purple"), 75%);
    }

    &.focus {
      border: $border-light solid clr("purple");
      outline: none;
    }

    .symbol {
      @include flex("block", "row", "center", "center");

      background-color: rgba(244, 244, 244, 0.7);
      border-bottom-left-radius: 3px;
      border-top-left-radius: 3px;
      font-size: 11px;
      height: 100%;
      min-width: 2rem;
      width: 2rem;

      img {
        width: 10px;
      }
    }

    .input {
      background-color: clr('white');
      border-radius: 0;
      border: none;
      color: $body-copy;
      font-size: $font-size-xs;
      height: 100%;
      padding: 0.5rem;
      transition: all $interaction-transition-time;
      width: 100%;

      &:hover {
        border: none;
      }

      &:focus {
        border: none;
        outline: none;
      }
    }

    .stepper {
      @include flex("block", "column", "center", "center");

      color: #A3A3A3;
      font-size: $font-size-xs;
      height: 100%;
      margin-right: 0.5rem;

      .icon-stepper {
        cursor: pointer;
        position: relative;

        &:hover {
          color: $nps-blue;
        }

        &.icon-up {
          bottom: -3px;
        }

        &.icon-down {
          top: -3px;
        }
      }
    }
  }

  .error-message {
    color: red;
    font-size: $font-size-xxs;
    margin-top: 0.5rem;
  }

  .input-error {
    border-color: red;
  }
}
</style>
