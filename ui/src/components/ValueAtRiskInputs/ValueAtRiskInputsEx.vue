<template>
  <section class="value-at-risk-inputs-ex">
    <label class="label">Average Employee Salary</label>
    <value-at-risk-inputs-number v-model.number="localEmployeeSalary"
      class="input"
      :icon="currency.fontawesomeIcon"
      @input="onInputSalary"
      error-message="Please enter a value for 'Average Employee Salary' to continue."
      :has-error="varInfoError.avgEmployeeSalary"
    />

    <label class="label">Additional Costs (e.g. recruitment costs)</label>
    <value-at-risk-inputs-number v-model.number="localEmployeeAdditionalCost"
      class="input"
      :icon="currency.fontawesomeIcon"
      @input="onInputAdditionalCost"
    />

    <section class="scale-people" @click="onToggleSelection">
      <base-checkbox-solid :value="localScaleToTotalPeople" />
      <label class="label">Scale the calculation to your total number of employees?</label>
    </section>

    <section class="number-of-people" v-if="localScaleToTotalPeople" >
      <label class="label">Total Number of Employees</label>
      <value-at-risk-inputs-number v-model.number="localNumberOfEmployees"
        icon="fa-regular fa-users"
        error-message="Please enter a value for 'Total Number of Employees' to continue."
        :has-error="varInfoError.numberOfEmployees"
        @input="onInputNumberOfEmployees"
      />
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseCheckboxSolid from '@/components/Base/BaseCheckboxSolid';
import CurrencyType from '@/enum/currency-type';
import ValueAtRiskInputsNumber from '@/components/ValueAtRiskInputs/ValueAtRiskInputsNumber';
import ValueAtRiskRequest from '@/services/request/ValueAtRiskRequest';

export default {
  name: 'value-at-risk-inputs-ex',

  components: {
    BaseCheckboxSolid,
    ValueAtRiskInputsNumber,
  },

  data() {
    return {
      localEmployeeAdditionalCost: 0,
      localEmployeeSalary: 0,
      localNumberOfEmployees: 0,
      localScaleToTotalPeople: false,
      timeout: null,
      error: {
        avgEmployeeSalary: false,
        numberOfEmployees: false,
      },
    };
  },

  computed: {
    ...mapState('valueAtRisk', [
      'clear',
      'varInfo',
      'varInfoError',
    ]),

    currency() {
      return CurrencyType[this.varInfo.currency];
    },
  },

  mounted() {
    this.localEmployeeAdditionalCost = this.varInfo.employeeAdditionalCost;
    this.localEmployeeSalary = this.varInfo.employeeSalary;
    this.localNumberOfEmployees = this.varInfo.numberOfEmployees;
    this.localScaleToTotalPeople = this.varInfo.scaleToTotalPeople;
    this.setVarInfoError({ error: this.error });
  },

  watch: {
    clear() {
      if (this.clear) {
        this.localEmployeeAdditionalCost = 0;
        this.localEmployeeSalary = 0;
        this.localNumberOfEmployees = 1;
        this.localScaleToTotalPeople = false;
        this.error = {
          avgEmployeeSalary: false,
          numberOfEmployees: false,
        };
        this.onUpdateModel();
        this.setClear({ clear: false });
      }
    },
  },

  methods: {
    ...mapActions('valueAtRisk', [
      'setClear',
      'setVarInfo',
      'setVarInfoError',
    ]),

    debounceUpdateModel() {
      clearTimeout(this.timeout);

      this.timeout = setTimeout(() => {
        this.onUpdateModel();
      }, 1000);
    },

    async onUpdateModel() {
      const newInfo = {
        ...this.varInfo,
        employeeAdditionalCost: this.localEmployeeAdditionalCost,
        employeeSalary: this.localEmployeeSalary,
        numberOfEmployees: this.localNumberOfEmployees,
        scaleToTotalPeople: this.localScaleToTotalPeople,
      };

      this.setVarInfo({ info: newInfo });
      this.setVarInfoError({ error: this.error });
      await ValueAtRiskRequest.previewVarInfo();
    },

    onInputSalary(value) {
      this.localEmployeeSalary = Number(value);
      this.error.avgEmployeeSalary = !this.localEmployeeSalary;
      this.debounceUpdateModel();
    },

    onInputAdditionalCost(value) {
      this.localEmployeeAdditionalCost = Number(value);
      this.debounceUpdateModel();
    },

    onInputNumberOfEmployees(value) {
      this.localNumberOfEmployees = Number(value);
      this.error.numberOfEmployees = !this.localNumberOfEmployees;
      this.debounceUpdateModel();
    },

    onToggleSelection() {
      this.localScaleToTotalPeople = !this.localScaleToTotalPeople;
      this.onUpdateModel();
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.value-at-risk-inputs-ex {
  @include flex("block", "column", "start", "stretch");

  margin-right: 2rem;

  .label {
    font-size: $font-size-xs;
    margin-bottom: 0.5rem;
  }

  .input {
    margin-bottom: 1rem;
  }

  .scale-people {
    @include flex("block", "row", "start", "center");

    margin-bottom: 1rem;

    .base-checkbox-solid {
      margin-right: 0.5rem;
    }

    .label {
      cursor: pointer;
      margin-bottom: 0;
    }
  }

  .number-of-people {
    @include flex("block", "row", "start", "start");

    background-color: #F2F4F8;
    border-radius: $border-radius-medium;
    padding: 1.5rem;

    .label {
      margin-bottom: 0;
      margin-right: 1rem;
    }
  }
}
</style>
