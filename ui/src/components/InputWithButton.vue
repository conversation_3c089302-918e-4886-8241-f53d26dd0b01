<template>
  <section class="input-with-button">
    <input :placeholder="placeholder" @input="$emit('input', $event.target.value)"/>
    <base-button :icon="icon" size="small">
      <slot></slot>
    </base-button>
  </section>
</template>

<script>
import BaseButton from '@/components/Base/BaseButton';

export default {
  name: 'input-with-button',

  components: {
    BaseButton,
  },

  props: {
    icon: {
      type: String,
      required: false,
    },

    placeholder: {
      type: String,
      required: false,
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.input-with-button {
  @include flex("block", "row", "start", "center");

  height: 2rem;

  input {
    border-top-left-radius: $border-radius-medium;
    border-bottom-left-radius: $border-radius-medium;
    border: $border-standard;
    border-right: none;
    color: $body-copy;
    font-size: $font-size-xs;
    padding-left: 0.5rem;

    &:active, &:focus {
      outline: none;
    }
  }

  input, .base-button {
    height: 100%;
  }

  .base-button {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}
</style>
