<template>
  <section class="value-at-risk-header-left">
    <section class="icon-arrow-wrapper" @click.stop="onClickBack">
      <i class="fa-regular fa-arrow-left" />
    </section>
    <i class="fa-solid fa-calculator icon-calculator" />
    <span class="title">Revenue at Risk Calculator: </span>
    <adorescore-box-mini :bucket="bucket" :score="score" />
    <span class="label">{{dataset.label}}</span>
  </section>
</template>

<script>
import { mapGetters, mapState } from 'vuex';

import AdorescoreBoxMini from '@/components/AdorescoreBox/AdorescoreBoxMini';
import Route from '@/enum/route';

export default {
  name: 'value-at-risk-header-left',

  components: {
    AdorescoreBoxMini,
  },

  computed: {
    ...mapGetters('benchmark', ['classifyAdorescore']),

    ...mapGetters('datasets', {
      getDataset: 'get',
    }),

    ...mapState('storyteller', ['activeReport']),

    ...mapState('valueAtRisk', ['datasetId', 'previousRoute']),

    bucket() {
      return this.classifyAdorescore(this.score);
    },

    dataset() {
      return this.getDataset(this.datasetId);
    },

    score() {
      return this.dataset.adoreScore;
    },
  },

  methods: {
    onClickBack() {
      if (this.previousRoute === Route.STORYTELLER) {
        this.$router.push({
          name: this.previousRoute,
          query: {
            ids: this.datasetId,
            reportId: this.activeReport.id,
          },
        });
      } else {
        this.$router.push({ name: this.previousRoute });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.value-at-risk-header-left {
  @include flex("block", "row", "start", "center");

  .icon-arrow-wrapper {
    @include flex("block", "row", "center", "center");

    border-radius: 50%;
    border: 1px solid rgba(19, 28, 41, 0.2);
    cursor: pointer;
    font-size: 10px;
    height: 22px;
    margin-right: 1rem;
    width: 22px;

    &:hover {
      border-color: rgba(19, 28, 41, 1);
    }
  }

  .icon-calculator {
    margin-right: 0.4rem;
  }

  .title {
    font-weight: $font-weight-bold;
    margin-right: 0.4rem;
  }

  .adorescore-box-mini {
    margin-right: 0.4rem;
    width: 2rem;

    .score {
      font-size: 12px;
    }
  }
}
</style>
