<template>
  <section class="value-at-risk-header-info">
    <i class="fa-solid fa-circle-info icon-info" />
    <span class="text">Revenue At Risk?</span>
  </section>
</template>

<script>
export default {
  name: 'value-at-risk-header-info',
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.value-at-risk-header-info {
  @include flex('inline', 'row', 'start', 'center');

  background-color: #3981F7;
  border-radius: $border-radius-small;
  color: clr('white');
  cursor: pointer;
  font-size: $font-size-xxs;
  height: 1.5rem;
  padding: 0.5rem;

  &:hover {
    background-color: #1957BE;
  }

  .icon-info {
    margin-right: 0.3rem;
  }

  .text {
    font-weight: $font-weight-extra-bold;
    text-transform: uppercase;
    letter-spacing: 0.3px;
  }
}
</style>
