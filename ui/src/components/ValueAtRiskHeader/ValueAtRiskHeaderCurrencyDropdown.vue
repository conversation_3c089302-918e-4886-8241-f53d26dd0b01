<template>
  <section class="value-at-risk-header-currency-dropdown">
    <section v-for="([currency, details], index) in currencies" :key="index" class="currency" @click="onClick(currency)">
      <img :src="require(`@/assets/flag/${details.flag}.svg`)" class="flag" alt="flag"/>
      <span class="abbreviation">{{details.abbreviation}} -&nbsp;</span>
      <span class="description">{{details.description}}</span>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import CurrencyType from '@/enum/currency-type';

export default {
  name: 'value-at-risk-header-currency-dropdown',

  computed: {
    ...mapState('valueAtRisk', ['varInfo']),

    currencies() {
      return Object.entries(CurrencyType);
    },
  },

  methods: {
    ...mapActions('valueAtRisk', ['setVarInfo']),

    onClick(currency) {
      const newInfo = {
        ...this.varInfo,
        currency,
      };
      this.setVarInfo({ info: newInfo });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.value-at-risk-header-currency-dropdown {
  @include flex("block", "column", "start", "stretch");

  background-color: clr("white");
  border-radius: $border-radius-small;
  border: 1px solid #7362B7;
  font-size: $font-size-xs;
  position: absolute;
  right: 0;
  top: 27px;
  width: 100%;
  z-index: 99;

  .currency {
    @include flex("block", "row", "start", "center");

    padding: 0.3rem;

    .flag {
      width: 14px;
      margin-right: 0.2rem;
    }

    &:hover {
      background-color: #E6EAFF;
    }

    .description {
      opacity: 0.8;
    }
  }
}
</style>
