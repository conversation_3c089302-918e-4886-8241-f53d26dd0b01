<template>
  <section class="value-at-risk-header-currency" @click.stop="open = !open" v-click-outside-handler="{ handler: 'onClose' }">
    <section class="input">
      <span class="currency-label">Currency:&nbsp;</span>
      <img :src="require(`@/assets/flag/${currency.flag}.svg`)" class="flag" alt="flag"/>
      <span class="abbreviation">{{currency.abbreviation}} -&nbsp;</span>
      <span class="description">{{currency.description}}</span>
    </section>
    <section class="dropdown-icon" :class="{ open }" >
      <i class="fa fa-caret-down icon-dropdown" :class="{ open }" />
    </section>
    <value-at-risk-header-currency-dropdown v-if="open" />
  </section>
</template>

<script>
import { mapState } from 'vuex';

import clickOutsideHandler from '@/directives/click-outside-handler';
import ValueAtRiskHeaderCurrencyDropdown from '@/components/ValueAtRiskHeader/ValueAtRiskHeaderCurrencyDropdown';
import CurrencyType from '@/enum/currency-type';

export default {
  name: 'value-at-risk-header-currency',

  components: {
    ValueAtRiskHeaderCurrencyDropdown,
  },

  directives: {
    clickOutsideHandler,
  },

  data() {
    return {
      open: false,
    };
  },

  computed: {
    ...mapState('valueAtRisk', ['varInfo']),

    currency() {
      return CurrencyType[this.varInfo.currency];
    },
  },

  methods: {
    onClose() {
      this.open = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.value-at-risk-header-currency {
  @include flex('inline', 'row', 'start', 'center');

  cursor: pointer;
  font-size: 11px;
  height: 1.5rem;
  position: relative;
  width: 235px;

  &:hover {
    .input, .dropdown-icon {
      border-color: rgba(115, 98, 183, 1);
    }
  }

  .input {
    @include flex('inline', 'row', 'start', 'center');

    background-color: clr("white");
    border-bottom-left-radius: $border-radius-small;
    border-top-left-radius: $border-radius-small;
    border: 1px solid rgba(115, 98, 183, 0.7);
    height: 100%;
    padding: 0.5rem;
    position: relative;
    border-right: none;
    width: calc(100% - 1.5rem);

    .currency-label {
      font-weight: $font-weight-bold;
    }

    .flag {
      width: 14px;
      margin-right: 0.2rem;
    }

    .description {
      opacity: 0.8;
    }
  }

  .dropdown-icon {
    @include panel;
    @include flex('inline', 'row', 'center', 'center');

    border-radius: 0 $border-radius-small $border-radius-small 0;
    border: 1px solid rgba(115, 98, 183, 0.7);
    box-shadow: unset;
    cursor: pointer;
    height: 100%;
    width: 1.5rem;

    .icon-dropdown {
      transition: all $interaction-transition-time;

      &.open {
        transform: rotate(180deg);
      }
    }
  }
}
</style>
