<template>
  <section class="value-at-risk-header">
    <value-at-risk-header-left />
    <value-at-risk-header-right />
  </section>
</template>

<script>
import ValueAtRiskHeaderLeft from '@/components/ValueAtRiskHeader/ValueAtRiskHeaderLeft';
import ValueAtRiskHeaderRight from '@/components/ValueAtRiskHeader/ValueAtRiskHeaderRight';

export default {
  name: 'value-at-risk-header',

  components: {
    ValueAtRiskHeaderLeft,
    ValueAtRiskHeaderRight,
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.value-at-risk-header {
  @include flex("block", "row", "space-between", "center");

  border-bottom: 1px solid $border-color;
  font-size: $font-size-sm;
  height: 85px;
  padding-left: 2rem;
  padding-right: 2rem;
}
</style>
