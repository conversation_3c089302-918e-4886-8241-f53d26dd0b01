<template>
  <section class="value-at-risk-header-right">
    <value-at-risk-header-currency class="item" />
<!--    <value-at-risk-header-info class="item" />-->
  </section>
</template>

<script>
import ValueAtRiskHeaderCurrency from '@/components/ValueAtRiskHeader/ValueAtRiskHeaderCurrency';
import ValueAtRiskHeaderInfo from '@/components/ValueAtRiskHeader/ValueAtRiskHeaderInfo';

export default {
  name: 'value-at-risk-header-right',

  components: {
    ValueAtRiskHeaderCurrency,
    ValueAtRiskHeaderInfo,
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.value-at-risk-header-right {
  @include flex("block", "row", "start", "center");

  .item {
    margin-left: 0.5rem;
  }
}
</style>
