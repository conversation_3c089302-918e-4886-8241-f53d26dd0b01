<template>
  <section class="storyteller-select-comments-item" @click="toggleSelection">
    <base-checkbox-solid :value="active" />
    <span class="content">{{snippet.content}}</span>
  </section>
</template>

<script>
import BaseCheckboxSolid from '@/components/Base/BaseCheckboxSolid';
import { mapActions, mapState } from 'vuex';

export default {
  name: 'storyteller-select-comments-item',

  components: {
    BaseCheckboxSolid,
  },

  props: {
    snippet: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapState('snippets', ['selectedSnippets']),

    active() {
      return this.selectedSnippets.includes(this.id);
    },

    id() {
      return this.snippet.userDocId;
    },
  },

  methods: {
    ...mapActions('snippets', ['deselectSnippet', 'selectSnippet']),

    toggleSelection() {
      if (this.active) {
        this.deselectSnippet({ id: this.id });
      } else {
        this.selectSnippet({ id: this.id });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-select-comments-item {
  @include flex("block", "row", "start", "start");
  @include stretch;

  border-bottom: 1px solid #FAFAFA;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: $font-weight-normal;
  line-height: 1.2rem;
  padding: 1rem 0;
  width: 100%;

  &:first-child {
    padding-top: 0;
  }

  .base-checkbox-solid {
    margin-right: 1rem;
  }
}
</style>
