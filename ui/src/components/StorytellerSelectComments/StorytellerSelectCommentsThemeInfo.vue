<template>
  <section class="storyteller-select-comments-theme-info">
    <i class="fa-regular fa-xmark icon-x" @click="onClickClose" />
    <span class="description">Viewing All Comments of <b>{{selectedTheme.topicLabel}}</b> for</span>
    <adorescore-box-mini :bucket="bucket" :score="score" />
    <span class="dataset-label">{{dataset.label}}</span>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import AdorescoreBoxMini from '@/components/AdorescoreBox/AdorescoreBoxMini';

export default {
  name: 'storyteller-select-comments-theme-info',

  components: {
    AdorescoreBoxMini,
  },

  computed: {
    ...mapGetters('benchmark', ['classifyAdorescore']),

    ...mapGetters('datasets', {
      getDataset: 'get',
    }),

    ...mapState('datasets', {
      datasetId: 'active',
    }),

    ...mapState('themes', ['selectedTheme']),

    bucket() {
      return this.classifyAdorescore(this.score);
    },

    dataset() {
      return this.getDataset(this.datasetId);
    },

    score() {
      return this.dataset.adoreScore;
    },
  },

  methods: {
    ...mapActions('storyteller', ['setSelectingCommentsView']),

    onClickClose() {
      this.setSelectingCommentsView({ selectingCommentsView: false });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-select-comments-theme-info {
  @include flex("block", "row", "start", "center");

  color: #131C29;
  grid-area: storyteller-select-comments-theme-info;
  padding: 0 1rem;
  width: 100%;

  .icon-x {
    @include flex("block", "row", "center", "center");

    background-color: #EEEEEE;
    border-radius: 50%;
    cursor: pointer;
    font-size: 11px;
    height: 1rem;
    width: 1rem;

    &:hover {
      background-color: rgba(19, 28, 41, 0.2);
    }
  }

  .description {
    font-size: $font-size-sm;
    margin-left: 0.75rem;
  }

  .adorescore-box-mini {
    margin-left: 0.75rem;
    width: 2rem;

    .score {
      font-size: 12px;
    }
  }

  .dataset-label {
    font-size: $font-size-sm;
    font-weight: $font-weight-bold;
    margin-left: 0.3rem;
  }
}
</style>
