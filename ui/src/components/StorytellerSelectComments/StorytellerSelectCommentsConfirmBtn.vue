<template>
  <section class="storyteller-select-comments-confirm-btn" @click="onClickBtn" :class="{disabled}">
    <loading-blocks-overlay v-if="saving" size="small" />
    <base-button v-else size="small" :disabled="disabled">
      <i class="fa-regular fa-check icon-check" />
      <span>Confirm</span>
    </base-button>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import StorytellerSlideRequest from '@/services/request/StorytellerSlideRequest';

export default {
  name: 'storyteller-select-comments-confirm-btn',

  components: {
    BaseButton,
    LoadingBlocksOverlay,
  },

  data() {
    return {
      saving: false,
    };
  },

  computed: {
    ...mapState('snippets', ['selectedSnippets']),

    disabled() {
      return this.selectedSnippets.length < 1 || this.selectedSnippets.length > 3;
    },
  },

  methods: {
    ...mapActions('storyteller', ['setSelectingCommentsView']),

    async onClickBtn() {
      this.saving = true;

      await StorytellerSlideRequest.updateSelectedComments(this.selectedSnippets);
      this.setSelectingCommentsView({ selectingCommentsView: false });

      this.saving = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-select-comments-confirm-btn {
  @include flex("block", "row", "start", "center");

  .base-button {
    @include flex("block", "row", "start", "center");

    background-color: rgba(64, 45, 179, 1);
    padding: 0.4rem 0.6rem;

    .icon-check {
      margin-right: 0.4rem;
    }

    &:hover, &:focus {
      background-color: rgba(64, 45, 179, 0.7);
    }
  }

  &.disabled {
    .base-button {
      cursor: not-allowed;
    }
  }
}
</style>
