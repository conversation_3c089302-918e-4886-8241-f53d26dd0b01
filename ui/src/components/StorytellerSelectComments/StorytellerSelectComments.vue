<template>
  <section class="storyteller-select-comments">
    <loading-blocks-overlay v-if="loading" />
    <storyteller-select-comments-body v-else />
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import MetadataRequest from '@/services/request/MetadataRequest';
import SnippetsRequest from '@/services/request/SnippetsRequest';
import StorytellerSelectCommentsBody from '@/components/StorytellerSelectComments/StorytellerSelectCommentsBody';
import ThemesRequest from '@/services/request/ThemesRequest';

export default {
  name: 'storyteller-select-comments',

  components: {
    LoadingBlocksOverlay,
    StorytellerSelectCommentsBody,
  },

  data() {
    return {
      loading: false,
    };
  },

  computed: {
    ...mapGetters('themes', ['getThemeById']),

    ...mapState('storyteller', ['selectedSlide']),
  },

  async created() {
    this.loading = true;

    this.resetSnippets();
    this.resetThemes();

    await ThemesRequest.fetchAndSetThemes();
    const theme = this.getThemeById(this.selectedSlide.slideData.themeId);
    this.selectTheme({ theme });

    const response = await MetadataRequest.filterCommentsOnMetadata();
    // some slide doesn't have commentSlideData ??
    let selectedComments = this.selectedSlide?.slideData?.commentSlideData?.selectedComments?.length > 0
      ? this.selectedSlide.slideData.commentSlideData.selectedComments : [];
    if (selectedComments?.length > 0) {
      selectedComments = await SnippetsRequest.fetchSnippetByIds(selectedComments.map(s => s.id));
    }

    const filteredSnippets = response.snippets.filter(
      snippet => !selectedComments.some(c => c.userDocId === snippet.userDocId),
    );
    response.snippets = [...selectedComments, ...filteredSnippets];
    this.resetSnippets();
    this.setSnippets({ meta: response });
    selectedComments.forEach(c => this.selectSnippet({ id: c.userDocId }));

    this.loading = false;
  },

  methods: {
    ...mapActions('snippets', {
      resetSnippets: 'reset',
      selectSnippet: 'selectSnippet',
      setSnippets: 'setSnippets',
    }),

    ...mapActions('themes', {
      resetThemes: 'reset',
      selectTheme: 'selectTheme',
    }),
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-select-comments {
  @include flex("block", "column", "start", "stretch");

  height: calc(100vh - $header-height - $dataset-storyteller-selector-height);
  width: 100%;
}
</style>
