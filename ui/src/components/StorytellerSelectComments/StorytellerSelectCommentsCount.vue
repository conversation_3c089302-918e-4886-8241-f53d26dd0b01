<template>
  <section class="storyteller-select-comments-count">
    <span class="text all-comments">All Comments</span>
    <i class="fa-solid fa-chevron-right icon-arrow" />
    <span class="text label">{{ selectedTheme.topicLabel }}:</span>
    <section class="selected-comment-count" :class="{disabled}">
      {{ selectedCommentCount }} Selected
    </section>
  </section>
</template>

<script>

import { mapState } from 'vuex';

export default {
  name: 'storyteller-select-comments-count',

  computed: {
    ...mapState('snippets', ['selectedSnippets']),

    ...mapState('themes', ['selectedTheme']),

    disabled() {
      return !this.selectedCommentCount || this.selectedCommentCount > 3;
    },

    selectedCommentCount() {
      return this.selectedSnippets?.length || 0;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-select-comments-count {
  @include flex("block", "row", "start", "center");

  font-size: $font-size-sm;
  grid-area: storyteller-select-comments-count;
  padding: 0 210px;
  width: 100%;

  .text {
    font-weight: $font-weight-bold;
  }

  .icon-arrow {
    font-weight: 100;
    margin-left: 1rem;
    margin-right: 1rem;
  }

  .selected-comment-count {
    font-weight: $font-weight-normal;
    margin-left: 0.6rem;

    &.disabled {
      color: clr('red');
    }
  }
}
</style>
