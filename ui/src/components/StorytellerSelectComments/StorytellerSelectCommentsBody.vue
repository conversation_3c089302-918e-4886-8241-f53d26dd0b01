<template>
  <section class="storyteller-select-comments-body">
    <storyteller-select-comments-theme-info />
    <storyteller-select-comments-action />
    <storyteller-select-comments-search @search="onSearch" />
    <storyteller-select-comments-count />
    <storyteller-select-comments-list :search="search" />
  </section>
</template>

<script>
import StorytellerSelectCommentsAction from '@/components/StorytellerSelectComments/StorytellerSelectCommentsAction';
import StorytellerSelectCommentsCount from '@/components/StorytellerSelectComments/StorytellerSelectCommentsCount';
import StorytellerSelectCommentsList from '@/components/StorytellerSelectComments/StorytellerSelectCommentsList';
import StorytellerSelectCommentsSearch from '@/components/StorytellerSelectComments/StorytellerSelectCommentsSearch';
import StorytellerSelectCommentsThemeInfo from '@/components/StorytellerSelectComments/StorytellerSelectCommentsThemeInfo';

export default {
  name: 'storyteller-select-comments-body',

  components: {
    StorytellerSelectCommentsAction,
    StorytellerSelectCommentsCount,
    StorytellerSelectCommentsList,
    StorytellerSelectCommentsSearch,
    StorytellerSelectCommentsThemeInfo,
  },

  data() {
    return {
      search: '',
    };
  },

  methods: {
    onSearch(search) {
      this.search = search;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-select-comments-body {
  @include stretch;

  display: grid;
  grid-template-areas:
    'storyteller-select-comments-action'
    'storyteller-select-comments-theme-info'
    'storyteller-select-comments-search'
    'storyteller-select-comments-count'
    'storyteller-select-comments-list';
  grid-template-columns: 1fr;
  grid-template-rows: repeat(3, 50px) 50px 1fr;
  overflow: hidden;
  position: relative;
}
</style>
