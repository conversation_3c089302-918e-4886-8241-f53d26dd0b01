<template>
  <section class="storyteller-select-comments-cancel-btn" @click="onClickBtn">
    <base-button size="small" >
      <span>Cancel</span>
    </base-button>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';

export default {
  name: 'storyteller-select-comments-cancel-btn',

  components: {
    BaseButton,
  },

  methods: {
    ...mapActions('storyteller', ['setSelectingCommentsView']),

    onClickBtn() {
      this.setSelectingCommentsView({ selectingCommentsView: false });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-select-comments-cancel-btn {
  @include flex("block", "row", "start", "center");

  .base-button {
    @include flex("block", "row", "start", "center");

    background-color: transparent;
    color: $nps-blue;
    font-weight: $font-weight-bold;
    letter-spacing: 0.3px;

    &:hover, &:focus {
      background-color: transparent;
      color: rgba(19, 28, 41, 0.7);
    }
  }
}
</style>
