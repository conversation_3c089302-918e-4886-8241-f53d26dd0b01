<template>
  <section class="storyteller-select-comments-action">
    <i class="fa-regular fa-arrow-down icon-down" />
    <span class="text help">Select up to 3 comments</span>
    <storyteller-select-comments-confirm-btn class="btn" />
    <storyteller-select-comments-cancel-btn class="btn cancel" />
  </section>
</template>

<script>
import StorytellerSelectCommentsCancelBtn from '@/components/StorytellerSelectComments/StorytellerSelectCommentsCancelBtn';
import StorytellerSelectCommentsConfirmBtn from '@/components/StorytellerSelectComments/StorytellerSelectCommentsConfirmBtn';

export default {
  name: 'storyteller-select-comments-action',

  components: {
    StorytellerSelectCommentsCancelBtn,
    StorytellerSelectCommentsConfirmBtn,
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-select-comments-action {
  @include flex("block", "row", "start", "center");

  background-color: rgba(214, 184, 255, 0.3);
  grid-area: storyteller-select-comments-action;
  padding: 0 1rem;
  width: 100%;

  .icon-down {
    margin-right: 0.5rem;
  }

  .text {
    font-size: $font-size-sm;

    &.help {
      font-weight: $font-weight-bold;
    }
  }

  .icon-book {
    margin-left: 0.4rem;
  }

  .btn {
    margin-left: 1rem;
    text-transform: uppercase;

    &.cancel {
      margin-left: 0.55rem;
    }
  }
}
</style>
