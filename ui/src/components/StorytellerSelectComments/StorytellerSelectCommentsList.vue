<template>
  <section class="storyteller-select-comments-list" @scroll.passive="onScroll">
    <section v-if="!loading" class="list">
      <storyteller-select-comments-item
        v-for="snippet in localSnippets"
        :key="snippet.userDocId"
        :snippet="snippet"
      />
      <section class="loader" ref="loader">
        <loading-blocks-overlay v-if="!search && moreAvailable" />
      </section>
    </section>

    <section v-else class="loading">
      <loading-blocks-overlay>Loading Comments...</loading-blocks-overlay>
    </section>
  </section>
</template>

<script>
import { throttle } from 'lodash-es';
import { mapGetters, mapState } from 'vuex';

import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import NetworkKeys from '@/enum/network-keys';
import NetworkStatus from '@/enum/network-status';
import StorytellerSelectCommentsItem from '@/components/StorytellerSelectComments/StorytellerSelectCommentsItem';

import { metadataRequest } from '@/services/request';

export default {
  name: 'storyteller-select-comments-list',

  components: {
    LoadingBlocksOverlay,
    StorytellerSelectCommentsItem,
  },

  props: {
    search: {
      type: String,
      required: false,
    },
  },

  data() {
    return {
      areSnippetsLoaded: false,
      loaderVisible: false,
    };
  },

  computed: {
    ...mapGetters('network', ['status']),

    ...mapGetters('snippets', ['moreAvailable']),

    ...mapState('snippets', ['snippets']),

    loading() {
      return !this.areSnippetsLoaded && this.snippetsStatus === NetworkStatus.LOADING;
    },

    localSnippets() {
      if (this.search) {
        return this.snippets.filter(snippet => snippet.content.toLowerCase().includes(this.search.toLowerCase()));
      }
      return this.snippets;
    },

    snippetsStatus() {
      return this.status(NetworkKeys.METADATA_FILTER_COMMENT);
    },
  },

  watch: {
    async loaderVisible() {
      if (this.loaderVisible) {
        await this.fetchSnippets();
      }
    },

    snippets() {
      if (this.snippets.length > 0) this.areSnippetsLoaded = true;
      else this.areSnippetsLoaded = false;
    },
  },

  created() {
    if (this.snippets.length > 0) this.areSnippetsLoaded = true;
  },

  methods: {
    checkVisible() {
      return this.getLoaderRect().top - this.getRect().top < this.getRect().height;
    },

    async fetchSnippets() {
      await metadataRequest.filterCommentsOnMetadata();
      await metadataRequest.filterCommentsCountOnMetadata();
    },

    getLoaderRect() {
      return this.$refs.loader.getBoundingClientRect();
    },

    getRect() {
      return this.$el.getBoundingClientRect();
    },

    onScroll: throttle(function throttleOnScroll() {
      if (this.moreAvailable) this.setLoaderVisible();
    }, 1000),

    setLoaderVisible() {
      this.loaderVisible = this.checkVisible();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-select-comments-list {
  @include flex("block", "column", "start", "stretch");

  background: clr("white");
  grid-area: storyteller-select-comments-list;
  height: 100%;
  overflow: auto;
  padding: 0 200px;

  .loading {
    @include flex("block", "row", "center", "center");
    @include stretch;
  }
}
</style>
