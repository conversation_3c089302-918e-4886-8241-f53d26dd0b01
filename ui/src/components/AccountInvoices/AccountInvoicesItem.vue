<template>
  <section class="account-page-invoices-item" :class="{ striping }">
    <section class="date">{{ date }}</section>
    <section class="amount">${{ invoice.subtotal / 100 || '' }}</section>
    <a class="download" :href="invoice.pdf">Download PDF</a>
  </section>
</template>

<script>
import { format, parseISO } from 'date-fns';

export default {
  name: 'account-page-invoices-item',

  props: {
    invoice: {
      type: Object,
      required: true,
    },
    striping: {
      type: Boolean,
      required: true,
    },
  },

  computed: {
    date() {
      return format(parseISO(this.invoice.date), 'MMMM yyyy');
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.account-page-invoices-item {
  @include flex("block", "row", "start", "center");

  border-radius: $border-radius-medium;
  padding: 1rem;

  &.striping {
    background-color: clr("purple", "lighter");
  }

  .date,
  .amount {
    width: 40%;
  }

  .download {
    color: clr("blue");
    text-decoration: none;
    transition: all $interaction-transition-time;
    width: 20%;

    &:hover {
      color: darken(clr("blue"), 10%);
    }
  }
}
</style>
