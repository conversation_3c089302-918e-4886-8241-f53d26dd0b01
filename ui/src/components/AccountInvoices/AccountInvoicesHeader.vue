<template>
  <section class="account-page-invoices-header">
    <section class="header date" :class="{ active: isDateActive }" @click="onClickDate">
      <span>Date</span>
      <chevron-down-icon class="icon" :class="{ asc: isAsc }"/>
    </section>
    <section class="header amount" :class="{ active: isAmountActive }" @click="onClickAmount">
      <span>Amount</span>
      <chevron-down-icon class="icon" :class="{ asc: isAsc }"/>
    </section>
    <section class="header download"></section>
  </section>
</template>

<script>
import { ChevronDownIcon } from 'vue-feather-icons';
import { mapActions, mapState } from 'vuex';

import InvoiceSort from '@/enum/invoice-sort';
import SortDirection from '@/enum/sort-direction';

export default {
  name: 'account-page-invoices-header',

  components: {
    ChevronDownIcon,
  },

  computed: {
    ...mapState('invoices', ['sort', 'sortDirection']),

    isAmountActive() {
      return this.sort === InvoiceSort.SUBTOTAL;
    },

    isAsc() {
      return this.sortDirection === SortDirection.ASC;
    },

    isDateActive() {
      return this.sort === InvoiceSort.DATE;
    },
  },

  methods: {
    ...mapActions('invoices', ['setSortDirection', 'sortBy']),

    onClickAmount() {
      if (this.isAmountActive) {
        this.setSortDirection({ direction: this.sortDirection.inverse });
      } else {
        this.sortBy({ sort: InvoiceSort.SUBTOTAL });
        this.setSortDirection({ direction: SortDirection.DESC });
      }
    },

    onClickDate() {
      if (this.isDateActive) {
        this.setSortDirection({ direction: this.sortDirection.inverse });
      } else {
        this.sortBy({ sort: InvoiceSort.DATE });
        this.setSortDirection({ direction: SortDirection.DESC });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.account-page-invoices-header {
  @include flex("block", "row", "start", "center");

  color: $body-copy-light;
  font-size: $font-size-xs;
  font-weight: $font-weight-medium;
  padding: 1rem;
  text-transform: uppercase;

  .date,
  .amount {
    width: 40%;
  }

  .download {
    width: 20%;
  }

  .header {
    @include flex("block", "row", "start", "center");

    cursor: pointer;
    transition: all $interaction-transition-time;

    &:hover,
    &.active {
      color: $body-copy;
    }

    .icon {
      height: $font-size-md;
      transition: all $interaction-transition-time;

      &.asc {
        transform: rotate(180deg);
      }
    }
  }
}
</style>
