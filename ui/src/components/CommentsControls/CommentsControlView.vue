<template>
  <section class="comments-control-view"
           v-click-outside-handler="{
                                handler: 'onClickOutside',
                                excludedParentClasses: [
                                    'base-radio',
                                    'comments-control-view-item',
                                    'list',
                                    'list-item',
                                    'option-left',
                                    'option-right',
                                ],
           }">
    <base-dropdown :component="CommentsControlViewItem"
                   :closeOnSelect="false"
                   :data="dataList"
                   :open="open"
                   @select="onSelect">
      <section class="button" @click="open = !open" :class="{ active: viewValue != null, open }">
        <span class="text">{{ buttonText }}</span>
        <i class="fa fa-chevron-up icon"></i>
      </section>
    </base-dropdown>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import BaseDropdown from '@/components/Dropdown/BaseDropdown';
import clickOutsideHandler from '@/directives/click-outside-handler';
import CommentsControlViewItem from '@/components/CommentsControls/CommentsControlViewItem';
import intercomEvent from '@/services/IntercomEvent';
import intercomEvents from '@/enum/intercom-events';
import Route from '@/enum/route';
import SnippetType from '@/enum/snippet-type';

import { metadataRequest } from '@/services/request';

export default {
  name: 'comments-control-view',

  components: {
    BaseDropdown,
  },

  directives: {
    clickOutsideHandler,
  },

  computed: {
    ...mapState('datasets', { activeDataset: 'active' }),

    ...mapState('snippets', ['display', 'viewUnassigned']),

    ...mapState('themes', ['selectedTheme']),

    buttonText() {
      if (this.viewValue) {
        return this.viewValue.buttonText;
      }
      return 'Comments View';
    },

    dataList() {
      const rs = [];

      rs.push({
        action: () => this.toggleViewUnassigned(),
        active: this.isActive(1),
        available: this.isUnassignedAvailable,
        buttonText: 'Comments View - Unassigned',
        icon: 'fa fa-ban',
        name: 'View All Unassigned Comments',
        val: 1,
      });

      rs.push({
        action: () => this.toggleViewBookmark(),
        active: this.isActive(2),
        available: true,
        buttonText: 'Comments View - Bookmarks',
        icon: 'fa fa-bookmark-o icon',
        name: 'View All Bookmarks',
        val: 2,
      });

      return rs;
    },

    isUnassignedAvailable() {
      return this.$route.name === Route.COMMENTS && this.selectedTheme == null;
    },
  },

  data() {
    return {
      CommentsControlViewItem,
      viewValue: null,
      open: false,
    };
  },

  methods: {
    ...mapActions('snippets', [
      'resetSnippets',
      'setDisplay',
      'setViewUnassigned',
    ]),

    isActive(val) {
      return this.viewValue?.val === val;
    },

    onClickOutside() {
      this.open = false;
    },

    onSelect(item) {
      if (!item.available) {
        return;
      }

      if (this.viewValue?.val === item.val) {
        this.viewValue = null;
      } else {
        this.viewValue = item;
      }
      item.action();
    },

    async toggleViewBookmark() {
      if (this.display === SnippetType.BOOKMARK) {
        this.setDisplay({ display: SnippetType.SNIPPET });
      } else {
        this.setDisplay({ display: SnippetType.BOOKMARK });
        intercomEvent.send(intercomEvents.VIEW_BOOKMARKS);
      }

      this.setViewUnassigned({ view: false });
      this.resetSnippets();
      await metadataRequest.filterCommentsOnMetadata();
      await metadataRequest.filterCommentsCountOnMetadata();
    },

    async toggleViewUnassigned() {
      if (!this.viewUnassigned) {
        intercomEvent.send(intercomEvents.VIEW_UNASSIGNED);
      }

      this.setViewUnassigned({ view: !this.viewUnassigned });
      this.setDisplay({ display: SnippetType.SNIPPET });
      this.resetSnippets();
      await metadataRequest.filterCommentsOnMetadata();
      await metadataRequest.filterCommentsCountOnMetadata();
    },
  },

  watch: {
    activeDataset() {
      this.viewValue = null;
      this.setDisplay({ display: SnippetType.SNIPPET });
    },

    selectedTheme() {
      if (this.selectedTheme && this.viewValue?.val === 1) {
        this.viewValue = null;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-control-view {
  .button {
    @include flex("block", "row", "start", "center");
    cursor: pointer;

    &.active, &:hover {
      color: clr("purple");
    }

    &.open {
      color: clr("purple");

      .icon {
        transform: rotateX(180deg);
      }
    }

    .text {
      font-size: $font-size-xs;
      font-weight: $font-weight-medium;
      text-transform: uppercase;
    }

    .icon {
      font-size: 10px;
      margin-left: 0.5rem;
      padding-bottom: 2px;
      transition: all $interaction-transition-time;
    }
  }
}
</style>
