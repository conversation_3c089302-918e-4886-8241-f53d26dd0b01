<template>
    <section class="comments-control-select-all">
      <section class="inner" v-if="!selectedAll">
        All<b>&nbsp;{{selectedSnippetsCount}}&nbsp;</b>comments in this view are selected.&nbsp;
        <section class="select-all-comment" @click="onClickSelectAll">
           Select all {{snippetsCount}} comments in this theme.
        </section>
      </section>
      <section class="inner" v-else>
        All<b>&nbsp;{{snippetsCount}}&nbsp;</b>comments in this view are selected.&nbsp;
        <section class="select-all-comment" @click="onClickDeSelectAll">
            Clear Selection
        </section>
      </section>
    </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

export default {
  name: 'comments-control-select-all',

  computed: {
    ...mapGetters('snippets', ['snippetsCount']),

    ...mapState('snippets', ['selectedSnippets', 'selectedAll']),

    selectedSnippetsCount() {
      return `${this.selectedSnippets?.length || 0}`;
    },
  },

  methods: {
    ...mapActions('snippets', ['selectAllSnippets', 'deselectAllSnippets']),

    onClickSelectAll() {
      this.selectAllSnippets();
    },

    onClickDeSelectAll() {
      this.deselectAllSnippets({});
    },
  },
};

</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.comments-control-select-all {
  align-items: center;
  background: rgba(237, 236, 245, 0.62);
  border: 1px solid rgba(237, 236, 245, 0.62);
  border-radius: 3px;
  color: #2A003C;
  display: flex;
  justify-content: center;
  margin: 0.8rem 1rem 0.5rem;
  padding: 0 1rem;
  text-align: center;
  font-size: 0.75rem;

  .inner{
    @include flex("block", "row", "start", "center");
    padding: 0.5rem 0 0.5rem 0 ;
    .select-all-comment {
      color: #3858FF;
      cursor: pointer;
      font-weight: bold;
      text-decoration: underline;
    }
  }

}
</style>
