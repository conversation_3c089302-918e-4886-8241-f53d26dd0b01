<template>
  <section class="comments-controls">
    <section class="selection" @click="onClickSelectAll">
      <base-checkbox-solid :value="areAllSelected" />
      <section class="selection-label" >Select All</section>
    </section>

    <section class="view">
      <comments-control-view class="control-btn"/>
      <comments-metadata-control-view-and-order class="control-btn" v-if="showCommentsMetadataControl"/>
      <comments-control-sort class="control-btn" v-if="showShort"/>
      <comments-control-sort-order class="control-btn" v-if="showShortOrder"/>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseCheckboxSolid from '@/components/Base/BaseCheckboxSolid';
import CommentsControlSort from '@/components/CommentsControls/CommentsControlSort';
import CommentsControlSortOrder from '@/components/CommentsControls/CommentsControlSortOrder';
import CommentsControlView from '@/components/CommentsControls/CommentsControlView';
import CommentsMetadataControlViewAndOrder from '@/components/CommentsMetadata/CommentsMetadataControlViewAndOrder';
import intercomEvent from '@/services/IntercomEvent';
import intercomEvents from '@/enum/intercom-events';
import SnippetType from '@/enum/snippet-type';
import ThemesType from '@/enum/themes-type';

export default {
  name: 'comments-controls',

  components: {
    BaseCheckboxSolid,
    CommentsControlSort,
    CommentsControlSortOrder,
    CommentsControlView,
    CommentsMetadataControlViewAndOrder,
  },

  computed: {
    ...mapGetters('snippets', [
      'areAllSelected',
      'getFilterMetadataList',
      'snippetsCount',
    ]),

    ...mapState('datasets', ['active']),

    ...mapState('snippets', [
      'bookmarks',
      'display',
      'snippets',
      'viewMetadataOpts',
      'selectedSnippets',
      'state',
    ]),

    ...mapState('themes', {
      themeType: 'type',
    }),

    isTimeSeries() {
      return this.themeType === ThemesType.TIME_SERIES;
    },

    showCommentsMetadataControl() {
      const { metadataColumns, metadataHeaders, metadataTypes } = this.getFilterMetadataList({ datasetId: this.active });
      return !this.isTimeSeries
        && metadataColumns?.length
        && metadataHeaders?.length
        && metadataTypes?.length;
    },

    showShort() {
      const { metadataColumns } = this.getFilterMetadataList({ datasetId: this.active });
      return this.showCommentsMetadataControl
        && metadataColumns.some(i => !this.viewMetadataOpts.hide.includes(i));
    },

    showShortOrder() {
      return this.showShort && this.viewMetadataOpts.sortBy !== null;
    },

    selectedSnippetsCount() {
      return `${this.selectedSnippets?.length || 0}`;
    },

  },

  data() {
    return {
      ThemesType,
    };
  },

  methods: {
    ...mapActions('snippets', ['deselectAllSnippets', 'selectSnippet']),

    onClickSelectAll() {
      intercomEvent.send(intercomEvents.SELECT_ALL_COMMENTS);

      if (this.areAllSelected) {
        this.deselectAllSnippets({});
      } else if (this.display === SnippetType.SNIPPET) {
        this.snippets.forEach(s => {
          this.selectSnippet({ id: s.userDocId });
        });
      } else {
        this.bookmarks.forEach(s => {
          this.selectSnippet({ id: s.userDocId });
        });
      }
    },

  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.comments-controls {
  background: clr("white");
  border-bottom: $border-standard;
  display: grid;
  grid-area: comments-controls;
  grid-template-areas: 'comments-controls-selection comments-controls-view';
  grid-template-columns: auto 1fr;
  min-height: 50px;
  padding: 0 1rem;
  width: 100%;

  .selection {
    @include flex("block", "row", "start", "center");
    cursor: pointer;
    font-size: $font-size-xs;
    grid-area: comments-controls-selection;
    text-transform: uppercase;

    .selection-label {
      font-weight: $font-weight-bold;
      margin-left: 0.5rem;
    }
  }

  .view {
    @include flex("block", "row", "end", "center");

    grid-area: comments-controls-view;

    .control-btn {
      margin-left: 0.5rem;
      margin-right: 0;
    }
  }
}
</style>
