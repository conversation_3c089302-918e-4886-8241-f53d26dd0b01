<template>
  <section class="comments-control-view-item" :class="{ disabled: !data.available }">
    <section class="option-left">
      <base-checkbox :value="data.active" radio-size="small"/>
      <span class="text">{{ textName }}</span>
    </section>
    <section class="option-right">
      <i :class="[data.icon]" class="icon" v-if="data.icon"/>
    </section>
  </section>
</template>

<script>
import BaseCheckbox from '@/components/Base/BaseCheckbox';

export default {
  name: 'comments-control-view-item',

  components: {
    BaseCheckbox,
  },

  props: {
    data: {
      type: Object,
      required: true,
    },
  },

  computed: {
    textName() {
      return this.data.name;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-control-view-item {
  @include flex("block", "row", "between", "center");
  cursor: pointer;
  font-size: $font-size-xxs;
  padding: 0.5rem 1rem;
  text-transform: uppercase;

  &.disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  &:hover {
    background-color: $list-item-hover-color;
  }

  .option-left {
    @include flex("block", "row", "start", "center");
    @include stretch;
    margin-right: 1rem;

    .text {
      margin-left: 0.5rem;
    }
  }
}
</style>
