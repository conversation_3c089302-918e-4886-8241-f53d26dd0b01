<template>
  <section class="comments-control-sort"
           v-click-outside-handler="{
                            handler: 'onClickOutside',
                            excludedParentClasses: [
                                    'base-radio',
                                    'comments-control-sort-item',
                                    'list',
                                    'list-item',
                            ],
           }">
    <base-dropdown :component="CommentsControlSortItem"
                   :closeOnSelect="false"
                   :data="sortedList"
                   :open="open"
                   :height="400"
                   @select="onSelect">
      <section class="button" @click="open = !open" :class="{ active: viewMetadataOpts.sortBy != null, open }">
        <span class="text">Sort By</span>
        <i class="fa fa-chevron-up icon"></i>
      </section>
    </base-dropdown>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';
import BaseDropdown from '@/components/Dropdown/BaseDropdown';
import clickOutsideHandler from '@/directives/click-outside-handler';
import CommentsControlSortItem from '@/components/CommentsControls/CommentsControlSortItem';

import { metadataRequest } from '@/services/request';

export default {
  name: 'comments-control-sort',

  components: {
    BaseDropdown,
  },

  directives: {
    clickOutsideHandler,
  },

  computed: {
    ...mapGetters('snippets', ['getFilterMetadataList']),

    ...mapState('datasets', {
      datasetId: 'active',
    }),

    ...mapState('snippets', ['viewMetadataOpts']),

    dataList() {
      const rs = [];
      const { metadataColumns, metadataHeaders, metadataTypes } = this.getFilterMetadataList({ datasetId: this.datasetId });

      if (
        !metadataColumns.length
        || !metadataHeaders.length
        || !metadataTypes.length
      ) {
        return rs;
      }

      for (let i = 0; i < metadataColumns.length; i += 1) {
        // note: may user wanna name their metadata-column as 'Comments'?
        if (metadataColumns[i] !== -1) {
          rs.push({
            active: this.viewMetadataOpts.sortBy === metadataColumns[i],
            available: !this.viewMetadataOpts.hide.includes(metadataColumns[i]),
            column: metadataColumns[i],
            name: metadataHeaders[i],
            type: metadataTypes[i],
          });
        }
      }

      return rs;
    },

    sortedList() {
      const rs = [...this.dataList];
      if (this.viewingOrder?.length) {
        rs.sort((a, b) => this.viewingOrder.indexOf(a.column) - this.viewingOrder.indexOf(b.column));
      }
      return rs;
    },

    viewingOrder() {
      return this.viewMetadataOpts.viewingOrder;
    },
  },

  data() {
    return {
      CommentsControlSortItem,
      open: false,
    };
  },

  methods: {
    ...mapActions('snippets', ['resetSnippets', 'setViewMetadataSortBy']),

    onClickOutside() {
      this.open = false;
    },

    async onSelect(val) {
      if (val.available && val.column !== this.viewMetadataOpts.sortBy) {
        this.setViewMetadataSortBy({ columnNumber: val.column });
        this.resetSnippets();
        await metadataRequest.filterCommentsOnMetadata();
        await metadataRequest.filterCommentsCountOnMetadata();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-control-sort {
  .button {
    @include flex("block", "row", "start", "center");
    cursor: pointer;

    &.active, &:hover {
      color: clr("purple");
    }

    &.open {
      color: clr("purple");

      .icon {
        transform: rotateX(180deg);
      }
    }

    .text {
      font-size: $font-size-xs;
      font-weight: $font-weight-medium;
      text-transform: uppercase;
    }

    .icon {
      font-size: 10px;
      margin-left: 0.5rem;
      padding-bottom: 2px;
      transition: all $interaction-transition-time;
    }
  }
}
</style>
