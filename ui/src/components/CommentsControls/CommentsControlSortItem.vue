<template>
  <section class="comments-control-sort-item" :class="{ disabled: !data.available }">
    <base-radio :value="data.active" radio-size="small"/>
    <span class="text">{{ textName }}</span>
  </section>
</template>

<script>
import BaseRadio from '@/components/Base/BaseRadio';

export default {
  name: 'comments-control-sort-item',

  components: {
    BaseRadio,
  },

  props: {
    data: {
      type: Object,
      required: true,
    },
  },

  computed: {
    textName() {
      return this.data.name;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-control-sort-item {
  @include flex("block", "row", "start", "center");
  cursor: pointer;
  font-size: $font-size-xxs;
  padding: 0.5rem 1rem;
  text-transform: uppercase;

  &.disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  &:hover {
    background-color: $list-item-hover-color;
  }

  .text {
    margin-left: 0.5rem;
  }
}
</style>
