<template>
  <section class="comments-control-sort-order"
           v-click-outside-handler="{
                            handler: 'onClickOutside',
                            excludedParentClasses: [
                                    'base-radio',
                                    'comments-control-sort-order-item',
                                    'list',
                                    'list-item',
                                    'option-left',
                                    'option-right',
                            ],
           }">
    <base-dropdown :component="CommentsControlSortOrderItem"
                   :closeOnSelect="false"
                   :data="dataList"
                   :open="open"
                   @select="onSelect">
      <section class="button" @click="open = !open" :class="{ open }">
        <span class="text">
          {{ buttonText }}
        </span>
        <i class="fa fa-chevron-up icon"></i>
      </section>
    </base-dropdown>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import BaseDropdown from '@/components/Dropdown/BaseDropdown';
import clickOutsideHandler from '@/directives/click-outside-handler';
import CommentsControlSortOrderItem from '@/components/CommentsControls/CommentsControlSortOrderItem';

import { metadataRequest } from '@/services/request';

export default {
  name: 'comments-control-sort-order',

  components: {
    BaseDropdown,
  },

  directives: {
    clickOutsideHandler,
  },

  computed: {
    ...mapState('snippets', ['viewMetadataOpts']),

    buttonText() {
      if (this.viewMetadataOpts.sortAsc) {
        return 'Order - ASC';
      }
      return 'Order - DESC';
    },

    dataList() {
      const rs = [];

      rs.push({
        active: this.viewMetadataOpts.sortAsc,
        name: 'ASC',
        val: true,
      });
      rs.push({
        active: !this.viewMetadataOpts.sortAsc,
        name: 'DESC',
        val: false,
      });

      return rs;
    },
  },

  data() {
    return {
      CommentsControlSortOrderItem,
      open: false,
    };
  },

  methods: {
    ...mapActions('snippets', ['resetSnippets', 'setViewMetadataSortAsc']),

    onClickOutside() {
      this.open = false;
    },

    async onSelect(item) {
      if (item.val !== this.viewMetadataOpts.sortAsc) {
        this.setViewMetadataSortAsc({ val: item.val });
        this.resetSnippets();
        await metadataRequest.filterCommentsOnMetadata();
        await metadataRequest.filterCommentsCountOnMetadata();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-control-sort-order {
  .button {
    @include flex("block", "row", "start", "center");
    cursor: pointer;

    &:hover {
      color: clr("purple");
    }

    &.open {
      color: clr("purple");

      .icon {
        transform: rotateX(180deg);
      }
    }

    .text {
      font-size: $font-size-xs;
      font-weight: $font-weight-medium;
      text-transform: uppercase;
    }

    .icon {
      font-size: 10px;
      margin-left: 0.5rem;
      padding-bottom: 2px;
      transition: all $interaction-transition-time;
    }
  }
}
</style>
