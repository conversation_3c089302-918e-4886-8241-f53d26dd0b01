<template>
  <section class="comments-control-sort-order-item">
    <section class="option-left">
      <base-radio :value="data.active" radio-size="small"/>
      <span class="text">{{ textName }}</span>
    </section>
    <section class="option-right">
      <i class="fa fa-arrow-up" v-if="data.val"/>
      <i class="fa fa-arrow-down" v-else/>
    </section>
  </section>
</template>

<script>
import BaseRadio from '@/components/Base/BaseRadio';

export default {
  name: 'comments-control-sort-order-item',

  components: {
    BaseRadio,
  },

  props: {
    data: {
      type: Object,
      required: true,
    },
  },

  computed: {
    textName() {
      return this.data.name;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-control-sort-order-item {
  @include flex("block", "row", "start", "center");
  cursor: pointer;
  font-size: $font-size-xxs;
  padding: 0.5rem 1rem;
  text-transform: uppercase;

  &:hover {
    background-color: $list-item-hover-color;
  }

  .option-left {
    @include flex("block", "row", "start", "center");
    @include stretch;
    margin-right: 1rem;

    .text {
      margin-left: 0.5rem;
    }
  }
}
</style>
