<template>
  <section class="insights-overview-configuration-modal">
    <section class="header">
      Edit Insights Overview
    </section>
    <section class="body">

      <section class="items">
        <section class="item score-care-title">
          <p class="title">Insights Scorecard Title</p>
          <base-input v-model="localScorecardTitle" />
        </section>

        <section class="separator"></section>
        <section class="item display-emotion">
          <section class="img">
            <img src="~@/assets/edit-insights/edit-insights-1.png">
          </section>
          <section class="content" @click="onClickDisplayEmotion">
            <p class="title">Display Emotion</p>
            <section class="content-inner">
              <base-checkbox :value="localDisplayEmotion" />
              <span class="content-text">Display Emotion on Adorescore Dial</span>
            </section>
          </section>
        </section>
        <section class="separator"></section>
        <section class="item breakdown-name">
          <section class="img">
            <img src="~@/assets/edit-insights/edit-insights-2.png">
          </section>

          <section class = "content">
            <p class="title"> Rename Emotion Breakdown Metrics</p>

            <section class="breakdown-positive">
              <p class="title">Adored</p>
              <base-input v-model="localPositiveName" />
            </section>

            <section class="breakdown-neutral">
              <p class="title">Ignored</p>
              <base-input v-model="localNeutralName" />
            </section>

            <section class="breakdown-negative">
              <p class="title">Floored</p>
              <base-input v-model="localNegativeName" />
            </section>
          </section>

        </section>
        <section class="separator"></section>

        <section class="item display-emotion">
          <section class="img">
            <img src="~@/assets/edit-insights/edit-insights-3.png">
          </section>
          <section class="content" @click="onClickDisplayCorrelations">
            <p class="title">Show Correlations Chart?</p>
            <section class="content-inner">
              <base-checkbox :value="localDisplayCorrelations" />
              <span class="content-text">Show correlations chart on Insight Report Card</span>
            </section>
          </section>
        </section>

      </section>
    </section>
    <section class="footer">
      <base-button
        class="btn-cancel"
        colour="light"
        size="small"
        type="link"
        @click="onClickCancel"
        >Cancel</base-button
      >
      <base-button class="btn-update" size="small" @click="onClickUpdate">Update</base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseCheckbox from '@/components/Base/BaseCheckbox';
import BaseInput from '@/components/Base/BaseInput';
import intercomEvent from '@/services/IntercomEvent';
import intercomEvents from '@/enum/intercom-events';

import { datasetInsightApiV0 } from '@/services/api';

export default {
  name: 'insights-overview-configuration-modal',

  components: {
    BaseButton,
    BaseCheckbox,
    BaseInput,
  },

  data() {
    return {
      localDisplayEmotion: true,
      localDisplayCorrelations: true,
      localNegativeName: 'Floored',
      localNeutralName: 'Ignored',
      localPositiveName: 'Adored',
      localScorecardTitle: '',
    };
  },

  computed: {
    ...mapGetters('datasets', ['get']),

    ...mapState('datasetsInsights', ['datasetBenchmark', 'scorecards']),

    dataset() {
      return this.get(this.datasetBenchmark);
    },

    scorecard() {
      return this.scorecards.find(s => s.datasetId === this.datasetBenchmark);
    },
  },

  created() {
    const insightsScorecard = this.scorecard?.insightsScorecard || {};

    this.localScorecardTitle = insightsScorecard.title?.trim().length
      ? insightsScorecard.title.trim()
      : `Insights Scorecard for ${this.dataset.label}`;

    this.localDisplayCorrelations = insightsScorecard.displayCorrelations != null
      ? insightsScorecard.displayCorrelations
      : true;

    this.localDisplayEmotion = insightsScorecard.displayEmotion != null
      ? insightsScorecard.displayEmotion
      : true;

    const breakdownNames = insightsScorecard.breakdownNames || {};

    this.localPositiveName = breakdownNames.positive?.trim().length
      ? breakdownNames.positive.trim()
      : 'Adored';

    this.localNeutralName = breakdownNames.neutral?.trim().length
      ? breakdownNames.neutral.trim()
      : 'Ignored';

    this.localNegativeName = breakdownNames.negative?.trim().length
      ? breakdownNames.negative.trim()
      : 'Floored';
  },

  methods: {
    ...mapActions('datasetsInsights', ['updateScorecard']),

    ...mapActions('modal', ['closeModal']),

    onClickCancel() {
      this.closeModal();
    },

    onClickDisplayCorrelations() {
      this.localDisplayCorrelations = !this.localDisplayCorrelations;
    },

    onClickDisplayEmotion() {
      this.localDisplayEmotion = !this.localDisplayEmotion;
    },

    async onClickUpdate() {
      await datasetInsightApiV0.updateTitle(
        this.datasetBenchmark,
        this.localScorecardTitle,
        {
          positive: this.localPositiveName,
          neutral: this.localNeutralName,
          negative: this.localNegativeName,
        },
        this.localDisplayEmotion,
        this.localDisplayCorrelations,
      );

      this.closeModal();

      intercomEvent.send(intercomEvents.EDIT_INSIGHTS_TITLE);

      const scorecard = await datasetInsightApiV0.getScorecard(this.datasetBenchmark);

      this.updateScorecard({ scorecard });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-overview-configuration-modal {
  @include modal;
  width: 443px;

  .header {
    text-align: start;
    padding: 1em 1.5em 1em 1.5em;
    align-items: start;
    font-weight: $font-weight-bold;
    font-size: $font-size-sm;
  }

  .score-care-title {
    @include flex("block", "column", "start", "start");
  }

  .body {
    padding: 0;

    .items {
      @include flex("block", "column", "start", "start");
    }
    .separator {
      border: 1px solid #ECE9FB;
      width: 100%;
    }
    .item {
      @include flex("block", "row", "start", "start");

      font-size: $font-size-xs;
      margin-bottom: 1rem;
      padding: 0 1.5em 0 1.5em;
      margin-top: 1rem;

      .title {
        font-weight: $font-weight-bold;
      }

      .img {
        max-width: 20%;
      }

      img {
        border-style: none;
        width: 100%;
      }

      .content {
        @include flex("block", "column", "start", "start");
        width: 100%;
        margin-left: 1rem;

        .content-inner {
          @include flex("block", "row", "center", "center");
        }

        .content-text {
          margin-left: 0.5rem;
          font-size: $font-size-xxs;
        }

        .breakdown-positive, .breakdown-neutral, .breakdown-negative {
          @include flex("block", "row", "start", "center");
          width: 100%;
          margin-bottom: 0.5rem;
          gap: 1rem;

          .title {
            font-weight: $font-weight-medium;
            font-size: $font-size-xs;
          }
        }
      }

      &.score-care-title{
        @include flex("block", "column", "start", "start");
        width: 100%;
      }

      &.display-emotion {
        .content {
          @include flex("block", "column", "start", "start");
          max-width: 100%;
          cursor: pointer;
        }

        img {
          border-style: none;
          width: 100%;
        }
      }
    }
  }

  .footer {
    .base-button {
      padding: 0.5rem 1rem;
    }

    .btn-update {
      background: #2D1757;
    }

    .btn-cancel  {
      margin-left: -1rem;
      color: #2D1757;
      opacity: 0.5;
    }
  }
}
</style>
