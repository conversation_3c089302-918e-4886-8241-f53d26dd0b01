<template>
  <section class="insights-overview-dropdown">
    <h3>Edit Scorecard</h3>
    <section class="item" @click="onClickOverview">
      <edit-2-icon class="icon"/>
      <span class="text">Edit Insights Overview</span>
    </section>
    <section v-if="showMetrics" class="item" @click="onClickMetrics">
      <edit-2-icon class="icon"/>
      <span class="text">Select Metrics</span>
    </section>
  </section>
</template>

<script>
import { Edit2Icon } from 'vue-feather-icons';
import { mapActions, mapGetters, mapState } from 'vuex';

import BlurCloseable from '@/components/Mixins/BlurCloseable';
import InsightsOverviewMetricModal from '@/components/InsightsOverview/InsightsOverviewMetricModal';
import InsightsOverviewConfigurationModal from '@/components/InsightsOverview/InsightsOverviewConfigurationModal';

export default {
  name: 'insights-overview-dropdown',

  components: {
    Edit2Icon,
  },

  mixins: [BlurCloseable],

  computed: {
    ...mapState('datasetsInsights', ['datasetBenchmark']),

    ...mapGetters('datasetsInsights', ['correlations']),

    showMetrics() {
      return this.correlations(this.datasetBenchmark).adorescore != null;
    },
  },

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    onClickMetrics() {
      this.setModalComponent({ component: InsightsOverviewMetricModal });
      this.$emit('close');
    },

    onClickOverview() {
      this.setModalComponent({ component: InsightsOverviewConfigurationModal });
      this.$emit('close');
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-overview-dropdown {
  @include panel;

  padding: 0.8rem;

  h3 {
    font-size: 0.7rem;
    font-weight: $font-weight-bold;
    padding: 0 0.2rem;
    text-transform: uppercase;
  }

  .item {
    @include flex("block", "row", "start", "center");

    border-radius: $border-radius-medium;
    cursor: pointer;
    padding: 0.2rem 0.4rem;
    margin-top: 0.3rem;

    &:hover {
      background-color: lighten($body-copy, 75%);
    }

    .icon {
      height: $font-size-xs;
      width: $font-size-xs;
    }

    .text {
      font-size: 0.7rem;
      margin-left: 0.4rem;
    }
  }
}
</style>
