<template>
  <section class="insights-overview-meta">
    <section class="description">{{ metadataHeader(id) }} Correlations</section>

    <template v-for="cor in displayedCorrelations">
      <section v-if="cor.isNone" :key="`${cor.key}-text`" class="text">
        <span class="strong">{{ cor.label }}</span> and {{ metadataHeader(id) }} are not correlated.
      </section>
      <section v-else :key="`${cor.key}-text`" class="text">
        If <span class="strong">{{ cor.label }}</span>
        goes up, {{ metadataHeader(id) }} goes {{ cor.val > 0 ? 'up' : 'down' }}.
      </section>

      <section :key="`${cor.key}-cls`" class="info" >
        <section class="classification">
          {{ cor.classification }}
        </section>

        <section v-if="!cor.isNone" class="bar">
          <section class="fill" :style="{ width: cor.percent }"></section>
        </section>

        <section v-if="!cor.isNone" class="polarity">{{ cor.val > 0 ? 'Positive' : 'Negative' }}</section>
      </section>
    </template>
  </section>
</template>

<script>
import { startCase } from 'lodash-es';
import { mapGetters } from 'vuex';
import Index from '@/enum';

export default {
  name: 'insights-overview-meta',

  props: {
    id: {
      type: Number,
      required: true,
    },
  },

  computed: {
    ...mapGetters('datasets', ['metadataHeader', 'scoreColumns']),

    ...mapGetters('datasetsInsights', ['correlations', 'selectedCorrelations']),

    correlationsValues() {
      return this.correlations(this.id);
    },

    displayedCorrelations() {
      if (!this.hasCorrelations) return [];

      return this.selectedCorrelationsLocal.map(key => {
        const label = Index.enumValueOf(key.toUpperCase()) ? Index.enumValueOf(key.toUpperCase()).titleCase() : startCase(key);
        const val = this.correlationsValues[key];
        const classification = this.correlationClass(val);
        const color = this.correlationColor(val);
        const isNone = classification === 'NONE';
        const percent = `${Math.round(Math.abs(val) * 100)}%`;

        return { key, label, val, classification, color, isNone, percent };
      });
    },

    hasCorrelations() {
      return Object.keys(this.correlationsValues).length > 0 && this.selectedCorrelations.length > 0;
    },

    selectedCorrelationsLocal() {
      return this.selectedCorrelations(this.id);
    },
  },

  methods: {
    correlationColor(val) {
      if (val >= 0) return '#73E164';

      return '#ff4343';
    },

    correlationClass(val) {
      const aVal = Math.abs(val);

      if (aVal >= 1) return 'PERFECT';
      if (aVal >= 0.8) return 'V. STRONG';
      if (aVal >= 0.6) return 'STRONG';
      if (aVal >= 0.4) return 'MODERATE';
      if (aVal >= 0.2) return 'WEAK';
      if (aVal >= 0.05) return 'V. WEAK';

      return 'NONE';
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-overview-meta {
  align-self: center;
  background-color: #F9F8FE;
  border: $border-standard;
  border-radius: $border-radius-medium;
  column-gap: 0.5rem;
  display: grid;
  grid-template-columns: 70px auto;
  min-width: 300px;
  padding: 1rem;

  .description {
    align-self: center;
    color: $insights-blue;
    font-size: $font-size-xs;
    font-weight: $font-weight-bold;
    grid-area: 1 / 1 / 2 / 3;
    text-transform: capitalize;
  }

  .text {
    font-size: 0.7rem;
    grid-column: 1 / 3;
    line-height: 1rem;
    margin: 0.5rem 0 0.2rem;

    .strong {
      font-weight: $font-weight-bold;
    }
  }

  .info {
    align-items: start;
    align-self: start;
    column-gap: 0.3rem;
    display: grid;
    grid-auto-flow: row;
    grid-column: 1 / 3;
    grid-template-columns: auto 1fr auto;
    grid-template-rows: 16px;
    row-gap: 0.2rem;

    .classification {
      @include flex("block", "row", "center", "center");

      align-self: stretch;
      background-color: #665F99;
      border-radius: $border-radius-small;
      color: clr('white');
      font-size: 0.6rem;
      font-weight: $font-weight-bold;
      padding: 0 0.3rem;
      text-align: center;
      text-transform: uppercase;
    }

    .bar {
      @include flex("block", "row", "start", "stretch");

      align-self: stretch;
      background-color: rgba(#665F99, 0.25);
      border: 1px solid #665F99;
      border-radius: 2px;
      overflow: hidden;

      .fill {
        background-color: rgba(#665F99, 0.5);
        height: 100%;
      }
    }

    .polarity {
      @include flex("block", "row", "center", "center");

      align-self: stretch;
      border: $border-standard;
      font-size: $font-size-xxs;
      font-weight: $font-weight-bold;
      padding: 0 0.3rem;
      text-transform: uppercase;
    }
  }
}
</style>
