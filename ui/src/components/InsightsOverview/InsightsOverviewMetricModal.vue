<template>
  <section class="insights-overview-metric-modal">
    <section class="header">
      Edit Metric: "{{ metadataHeader(id) }}"
    </section>

    <section class="body">
      <section class="items">
       <section class="item enable-metric">
        <section class="img">
          <img src="~@/assets/edit-insights/edit-insights-4.png">
        </section>

        <section class="content" @click="onClickEnableMetric">
          <p class="title">Show metric on Infographic?</p>
          <section class="content-inner">
          <base-checkbox :value="localEnableMetric"/>
          <span class="content-text">Replace Adorescore with Metric score</span>
          </section>
        </section>
      </section>
       <section class="separator"></section>

      <section class="item edit-metric">
        <p class="title">Edit Metrics</p>

        <section class="metric-calculation-type">
          <p class="title">Metric Calculation Type</p>
          <base-dropdown :data="metricCalculationTypeList"
                         :open="openMetricCalculationType"
                         @close="openMetricCalculationType = false"
                         @select="onSelectMetricCalculationType">
            <base-dropdown-button :active="openMetricCalculationType"
                                  @click.prevent.stop="openMetricCalculationType = !openMetricCalculationType">
              {{ localMetricCalculationType.name === 'NPS' ? 'NPS' : localMetricCalculationType.titleCase() }}
            </base-dropdown-button>
          </base-dropdown>
        </section>

        <section class="metric-value">
          <section class=" min-value">
            <p class="title">Minimum Value of Metric</p>
            <base-input v-model="localMinValue"/>
          </section>

          <section class=" max-value">
            <p class="title">Maximum Value of Metric</p>
            <base-input v-model="localMaxValue"/>
          </section>

        </section>

        <section class=" correlation-name" :class="{ 'last-item': metadataScoreList.length <= 1 }">
          <p class="title">Metric Name</p>
          <base-input v-model="localName"/>
        </section>

        <section class=" select-correlation last-item" v-if="metadataScoreList.length > 1">
          <p class="title">Select Metric</p>
          <base-dropdown :data="metadataItemList"
                         :open="dropdownOpen"
                         @close="dropdownOpen = false"
                         @select="onSelectMetadata"
          >
            <base-dropdown-button :active="dropdownOpen" @click.stop.prevent="dropdownOpen = !dropdownOpen">
              <span class="text">{{ metadataText(localSelectedMetadata) }}</span>
            </base-dropdown-button>
          </base-dropdown>
        </section>

      </section>

        <section class="separator"></section>

        <section class="emotion-correlations">
        <p class="title">Select 3 Metrics for Correlation</p>
        <InsightsOverviewMetricCorrelationItem v-for="cor in displayedCorrelations"
                                               :key="cor.key"
                                               class="correlation-item"
                                               @click="onClickMetricCorrelationItem"
                                               :cor="cor"
                                               :localSelected="localSelected"
        />
      </section>

      </section>

    </section>
    <section class="footer">
      <base-button class="btn-cancel" colour="light" type="link" size="small" @click="onClickCancel">Cancel</base-button>
      <base-button v-if="!loading" :disabled="isSaveDisabled" size="small" @click="onClickSave" class="btn-update">Save</base-button>
      <loading-blocks-overlay v-else/>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';
import { EditIcon } from 'vue-feather-icons';

import BaseButton from '@/components/Base/BaseButton';
import BaseCheckbox from '@/components/Base/BaseCheckbox';
import BaseDropdown from '@/components/Dropdown/BaseDropdown';
import BaseDropdownButton from '@/components/Dropdown/BaseDropdownButton';
import BaseInput from '@/components/Base/BaseInput';
import InsightsMetricCalculationType from '@/enum/insights-metric-calculation-type';
import InsightsOverviewMetricCorrelationItem from '@/components/InsightsOverview/InsightsOverviewMetricCorrelationItem';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';

import { datasetApi, datasetInsightApiV0, genesisApi } from '@/services/api';

export default {
  name: 'insights-overview-metric-modal',

  components: {
    BaseButton,
    BaseCheckbox,
    BaseDropdown,
    BaseDropdownButton,
    BaseInput,
    EditIcon,
    InsightsOverviewMetricCorrelationItem,
    LoadingBlocksOverlay,
  },

  data() {
    return {
      dropdownOpen: false,
      localEnableMetric: false,
      localMaxValue: 10,
      localMetricCalculationType: null,
      localMinValue: 0,
      localName: '',
      localSelected: [],
      localSelectedMetadata: null,
      loading: false,
      openMetricCalculationType: false,
    };
  },

  computed: {
    ...mapGetters('datasets', [
      'get',
      'hasScoreMetadata',
      'metadataHeader',
      'metadataHeaderObj',
      'scoreColumns',
    ]),

    ...mapGetters('datasetsInsights', [
      'correlations',
      'metricConfiguration',
      'selectedCorrelations',
      'selectedMetadata',
    ]),

    ...mapState('datasetsInsights', ['datasetBenchmark', 'themeSets']),

    displayedCorrelations() {
      const correlationsValues = this.correlations(this.id);
      if (correlationsValues.adorescore == null) return [];

      return Object.keys(correlationsValues).map(key => {
        return { key, val: correlationsValues[key] };
      });
    },

    id() {
      return this.datasetBenchmark;
    },

    isSaveDisabled() {
      return this.localSelected.length < 3;
    },

    metadataHeaderList() {
      return this.get(this.id)?.metadataHeaders || [];
    },

    metadataItemList() {
      return this.metadataScoreList.map(index => {
        return {
          content: this.metadataHeaderList[index],
          value: index,
        };
      });
    },

    metadataScoreList() {
      return this.scoreColumns(this.id);
    },

    metricCalculationTypeList() {
      return InsightsMetricCalculationType.enumValues
        .map(t => {
          return {
            content: t.name === 'NPS' ? 'NPS' : t.titleCase(),
            value: t,
          };
        });
    },

    metricConfig() {
      return this.metricConfiguration(this.id);
    },

    persistedCorrelations() {
      return this.selectedCorrelations(this.id);
    },
  },

  created() {
    this.localSelected = this.displayedCorrelations.filter(cor => this.persistedCorrelations.includes(cor.key));

    this.localName = this.metadataHeaderObj(this.id).fullText;
    this.localSelectedMetadata = this.metadataHeaderObj(this.id).index;

    this.localEnableMetric = this.metricConfig?.enableMetric != null
      ? this.metricConfig.enableMetric
      : false;

    this.localMetricCalculationType = this.metricConfig?.calculationType
      ? InsightsMetricCalculationType[this.metricConfig.calculationType]
      : InsightsMetricCalculationType.AVERAGE;

    this.localMaxValue = this.metricConfig?.maxValue
      ? `${this.metricConfig.maxValue}`
      : '10';

    this.localMinValue = this.metricConfig?.minValue
      ? `${this.metricConfig.minValue}`
      : '0';
  },

  methods: {
    ...mapActions('datasets', ['updateMetadataHeader']),

    ...mapActions('datasetsInsights', ['setThemesMetadata', 'updateScorecard']),

    ...mapActions('modal', ['closeModal']),

    isSelected(cor) {
      return this.localSelected.find(sel => sel.key === cor.key) != null;
    },

    metadataText(index) {
      if (index == null || !this.metadataScoreList.includes(index)) {
        return this.metadataHeaderList[this.metadataScoreList[0]];
      }

      return this.metadataHeaderList[index];
    },

    onClickCancel() {
      this.closeModal();
    },

    onClickEnableMetric() {
      this.localEnableMetric = !this.localEnableMetric;
    },

    onClickMetricCorrelationItem(item) {
      if (this.isSelected(item)) {
        this.localSelected = this.localSelected.filter(sel => sel.key !== item.key);
      } else if (this.localSelected.length < 3) {
        this.localSelected.push(item);
      }
    },

    async onClickSave() {
      this.loading = true;

      const correlations = this.localSelected.map(sel => sel.key);
      await datasetInsightApiV0.updateCorrelations(
        this.id,
        correlations,
        this.localSelectedMetadata,
        this.localEnableMetric,
        this.localMetricCalculationType.name,
        this.localMinValue,
        this.localMaxValue,
      );

      const currentHeaderName = this.metadataText(this.localSelectedMetadata);
      if (this.localName !== currentHeaderName) {
        await datasetApi.updateMetadataHeader(this.id, this.localSelectedMetadata, this.localName);

        // Slightly hacky (let's say; less than ideal) because the interaction
        // between `datasets` and `summaries` is bizarre and it would take like
        // a week to sort out that mess
        this.updateMetadataHeader({ id: this.id, index: this.localSelectedMetadata, header: this.localName });
      }

      const scorecard = await datasetInsightApiV0.getScorecard(this.id);
      this.updateScorecard({ scorecard });

      await this.updateThemesMetadata();

      this.closeModal();

      this.loading = false;
    },

    onSelectMetadata(item) {
      this.localName = item.content;

      this.localSelectedMetadata = item.value;
    },

    onSelectMetricCalculationType(item) {
      this.localMetricCalculationType = item.value;
    },

    async updateThemesMetadata() {
      const themeSet = this.themeSets.find(t => t.id === this.id);

      if (themeSet == null || !this.hasScoreMetadata(this.id)) {
        this.setThemesMetadata({ values: [] });
      } else if (themeSet.themes.length > 0) {
        const themeIds = themeSet.themes.map(t => t.id);

        // this.selectedMetadata -> scorecard.insightsScorecard.correlations.metadataIndex should be updated already
        const column = this.selectedMetadata(this.id) == null
          ? this.metadataScoreList[0]
          : this.selectedMetadata(this.id);

        const metadataChanges = await genesisApi.getImprovements(this.id, themeIds, column);

        this.setThemesMetadata({ values: metadataChanges });
      }
    },
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-overview-metric-modal {
  @include modal;

  .header {
    text-align: center;
    padding: 1em 1.5em 1em 1.5em;
    align-items: center;
    font-weight: $font-weight-bold;
    font-size: $font-size-sm;
    flex-direction: row;
    justify-content: left;

    .icon {
      margin-right: 0.5rem;
    }
  }

  .body {
    @include flex("block", "column", "start", "stretch");

    padding: 0;

    .items {
      @include flex("block", "column", "start", "start");
    }

    .separator {
      border: 1px solid #ECE9FB;
      width: 100%;
    }

    .item {
      @include flex("block", "row", "start", "start");
      font-size: $font-size-xs;
      margin-bottom: 1rem;
      padding: 0 1.5em 0 1.5em;
      margin-top: 1rem;
      gap: 1rem;

      &.edit-metric {
        @include flex("block", "column", "start", "start");
        width: 100%;
      }

      .title {
        font-weight: $font-weight-bold;
        font-size: $font-size-sm;
      }

      .img {
        max-width: 20%;
      }

      img {
        border-style: none;
        width: 100%;
      }

      .content {
        @include flex("block", "column", "start", "start");
        width: 100%;
        margin-left: 1rem;

        .content-text {
          margin-left: 0.5rem;
          font-size: $font-size-xxs;

        }
        .content-inner {
          @include flex("block", "row", "center", "center");
        }
      }

      .base-input {
        padding: 0.3rem 0.5rem;
      }

      .base-dropdown-button {
        margin-top: 0.5rem;

        .base-button {
          padding: 0.3rem 0.5rem;
        }
      }

      &.enable-metric {
        .content {
          @include flex("block", "column", "start", "start");
          max-width: 100%;
          cursor: pointer;
        }

        img {
          border-style: none;
          width: 100%;
        }
      }

      .metric-value{
        @include flex("block", "row", "start", "start");
        width: 100%;
        gap: 1.5rem;
        .min-value,.max-value {
          width: 100%;
        }
        .title {
          font-weight: $font-weight-medium;
          font-size: $font-size-xs;
        }
      }

      .metric-calculation-type,.correlation-name {
        width: 100%;
        .title {
          font-weight: $font-weight-medium;
          font-size: $font-size-xs;
        }
      }

      &.last-item {
        border-bottom: $border-standard;
        padding-bottom: 1rem;
      }

      &.select-correlation {
        .base-button .text {
          @include truncate;
          display: inline-block;
          max-width: 500px;
        }
      }
    }

    .emotion-correlations {
      @include flex("block", "column", "start", "stretch");

      background-color: clr('white');
      font-size: $font-size-xs;
      padding: 0.5rem 0;
      width: 100%;
      .title {
        font-weight: $font-weight-bold;
        padding: 0 1.5rem 0.5rem;
      }
    }
  }

  .footer {
    .base-button {
      padding: 0.5rem 2rem;

      &.btn-cancel {
        padding-left: 0;
        color: #2D1757;
        opacity: 0.5;
      }
    }

    .btn-update {
      background: #2D1757;
    }

    .loading-blocks-overlay {
      height: 2rem;
    }
  }
}
</style>
