<template>
  <section class="insights-overview-metric-correlation-item"
           :class="{ disabled: isDisabled }"
           @click="onClickCorrelation"
  >
    <base-checkbox :value="isSelected"/>
    <section class="label">{{ corLabel }}</section>
    <section class="classification">{{ corClassification }}</section>
    <section class="bar" :style="{ borderColor: corColor }">
      <section class="fill" :style="{ backgroundColor: corColor, width: corPercent }"></section>
    </section>
    <section v-if="!isNoneClass" class="polarity">{{ cor.val > 0 ? 'Positive' : 'Negative' }}</section>
  </section>
</template>

<script>
import { startCase } from 'lodash-es';
import BaseCheckbox from '@/components/Base/BaseCheckbox';
import Index from '@/enum';

export default {
  name: 'insights-overview-metric-correlation-item',

  components: {
    BaseCheckbox,
  },

  props: {
    cor: {
      type: Object,
      required: true,
    },
    localSelected: {
      type: Array,
      required: true,
    },
  },

  computed: {
    corClassification() {
      const aVal = Math.abs(this.cor.val);

      if (aVal >= 1) return 'PERFECT';
      if (aVal >= 0.8) return 'VERY STRONG';
      if (aVal >= 0.6) return 'STRONG';
      if (aVal >= 0.4) return 'MODERATE';
      if (aVal >= 0.2) return 'WEAK';
      if (aVal >= 0.05) return 'VERY WEAK';

      return 'NONE';
    },

    corColor() {
      if (this.cor.val >= 0) return '#A5F19B';

      return '#F79090';
    },

    corLabel() {
      return Index.enumValueOf(this.cor.key.toUpperCase()) ? Index.enumValueOf(this.cor.key.toUpperCase()).titleCase() : startCase(this.cor.key);
    },

    corPercent() {
      return `${Math.round(Math.abs(this.cor.val) * 100)}%`;
    },

    isDisabled() {
      return this.localSelected.length === 3 && !this.isSelected;
    },

    isNoneClass() {
      return this.corClassification === 'NONE';
    },

    isSelected() {
      return this.localSelected.find(sel => sel.key === this.cor.key) != null;
    },
  },

  methods: {
    onClickCorrelation() {
      if (this.isDisabled) return;
      this.$emit('click', this.cor);
    },
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-overview-metric-correlation-item {
  @include flex("block", "row", "start", "center");
  @include stretch;

  cursor: pointer;
  border-radius: $border-radius-medium;
  padding: 0.5rem 1.5rem;

  &:hover:not(.disabled) {
    background-color: rgba($body-copy, 0.05);
  }

  &.disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  .base-checkbox {
    @include rigid;

    pointer-events: none;
    margin-right: 1rem;
  }

  .label {
    @include rigid;

    width: 18%;
  }

  .classification {
    @include flex("block", "row", "center", "center");
    @include rigid;

    background-color: #665F99;
    border: 1px solid;
    border-radius: 3px;
    color: clr('white');
    font-size: $font-size-xxs;
    font-weight: $font-weight-bold;
    margin-right: 0.8rem;
    padding: 0.2rem 0.3rem;
    text-transform: uppercase;
    white-space: nowrap;
    width: 17%;
  }

  .bar {
    @include flex("block", "row", "start", "stretch");
    @include rigid;

    background-color: clr('blue', 'lighter');
    border: 1px solid;
    border-radius: 3px;
    height: 18px;
    margin-right: 1rem;
    overflow: hidden;
    width: 45%;

    .fill {
      height: 100%;
    }
  }

  .polarity {
    @include flex("block", "row", "center", "center");
    @include rigid;

    border: 1px solid #dee1e4;
    border-radius: 3px;
    font-size: $font-size-xxs;
    font-weight: $font-weight-bold;
    padding: 0.2rem 0.3rem;
    text-transform: uppercase;
    width: 12%;
  }
}
</style>
