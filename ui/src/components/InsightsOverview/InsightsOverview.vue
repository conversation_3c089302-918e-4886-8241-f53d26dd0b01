<template>
  <section class="insights-overview"
           :class="{ presentation, structured: dialDisplayCorrelations, separate: saveAsSeparate }"
  >
    <section class="title">
      <section class="left">
        <i class="fa fa-file icon"></i>
        <h2 class="text">{{ title }}</h2>
      </section>

      <section v-if="isEditor" class="right">
        <section class="dropdown-btn"
          v-if="!presentation"
          v-tooltip.bottom.end.notrigger="{
            html: 'insights-overview-dropdown',
            class: 'tooltip-insights-dropdown',
            delay: 0,
            visible: dropdownOpen,
          }"
          @click.stop="dropdownOpen = !dropdownOpen">
          •••
        </section>
        <insights-overview-dropdown v-if="!presentation" id="insights-overview-dropdown" @close="dropdownOpen = false"/>
      </section>
    </section>

    <section class="overview-scorecard" :class="{ 'no-metadata': !dialDisplayCorrelations }">
      <section class="dial-and-stats">
        <section class="dial">
          <adorescore-dial :id="id"
                           :score="dialScore"
                           :score-title="dialScoreTitle"
                           :display-emotion="dialDisplayEmotion"
                           :calculation-type="dialCalculationType"
                           :min-value="dialMinValue"
                           :max-value="dialMaxValue"
          />
        </section>

        <section class="overview-stats">
          <section class="stats">
            <h4>Stats</h4>
            <section v-for="[key, value] in overviewCounts" :key="key" class="overview-count">
              <span class="key"></span>
              <span class="overview-value">{{ value }}</span>
              <span class="overview-label">{{ key }}</span>
            </section>
          </section>

          <section class="breakdown">
            <h4>Breakdown</h4>
            <section v-for="c in contributions" :key="c.key" class="overview-count">
              <span class="key" :class="[c.key]"></span>
              <span class="overview-value" :class="[c.key]">{{ c.value }}%</span>
              <span class="overview-label">{{ c.text }}</span>
            </section>
          </section>
        </section>
      </section>

      <section v-if="dialDisplayCorrelations" class="meta-container">
        <insights-overview-meta :id="id"/>
      </section>
    </section>
  </section>
</template>

<script>
import { CheckIcon } from 'vue-feather-icons';
import { mapGetters, mapState } from 'vuex';

import AdorescoreDial from '@/components/AdorescoreDial/AdorescoreDial';
import InsightsMetricCalculationType from '@/enum/insights-metric-calculation-type';
import InsightsOverviewDropdown from '@/components/InsightsOverview/InsightsOverviewDropdown';
import InsightsOverviewMeta from '@/components/InsightsOverview/InsightsOverviewMeta';
import ScoreRateHelper from '@/helpers/score-rate-helper';

const scoreRate = new ScoreRateHelper();

export default {
  name: 'insights-overview',

  components: {
    AdorescoreDial,
    CheckIcon,
    InsightsOverviewDropdown,
    InsightsOverviewMeta,
  },

  props: {
    presentation: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      dropdownOpen: false,
    };
  },

  computed: {
    ...mapGetters('datasets', ['isEditable']),

    ...mapGetters('datasets', [
      'get',
      'hasScoreMetadata',
      'metadataHeaderObj',
    ]),

    ...mapGetters('datasetsInsights', ['metricConfiguration']),

    ...mapState('datasetsInsights', [
      'datasetBenchmark',
      'overviews',
      'saveAsSeparate',
      'scorecards',
      'showCustomInsights',
    ]),

    contributions() {
      const breakdownNames = this.scorecard?.insightsScorecard?.breakdownNames || {};
      const adoredKey = 'adored';
      const ignoredKey = 'ignored';
      const flooredKey = 'floored';

      const { contribution } = this.overview;
      const roundedContribution = scoreRate.getRates(
        [
          contribution.positive,
          contribution.neutral,
          contribution.negative,
        ],
        0,
        100,
      );

      return [
        { key: adoredKey, text: breakdownNames.positive || adoredKey, value: roundedContribution[0] },
        { key: ignoredKey, text: breakdownNames.neutral || ignoredKey, value: roundedContribution[1] },
        { key: flooredKey, text: breakdownNames.negative || flooredKey, value: roundedContribution[2] },
      ];
    },

    dataset() {
      return this.get(this.id);
    },

    dialCalculationType() {
      if (this.metricConfig?.enableMetric) {
        return InsightsMetricCalculationType[this.metricConfig.calculationType].name;
      }

      return 'ADORESCORE';
    },

    dialDisplayEmotion() {
      if (!this.scorecard?.insightsScorecard || this.scorecard.insightsScorecard.displayEmotion == null) {
        return true;
      }
      return this.scorecard.insightsScorecard.displayEmotion;
    },

    dialDisplayCorrelations() {
      if (!this.scorecard?.insightsScorecard?.correlations) {
        return false;
      }
      if (this.scorecard.insightsScorecard.displayCorrelations == null) {
        return this.hasScoreMetadata(this.id);
      }
      return this.scorecard.insightsScorecard.displayCorrelations;
    },

    dialMaxValue() {
      if (this.metricConfig?.enableMetric && this.metricConfig?.maxValue != null) {
        return this.metricConfig.maxValue;
      }

      return 100;
    },

    dialMinValue() {
      if (this.metricConfig?.enableMetric && this.metricConfig?.maxValue != null) {
        return this.metricConfig.minValue;
      }

      return -100;
    },

    dialScore() {
      if (this.metricConfig?.enableMetric) {
        return Math.round(this.metricConfig.metricScore) || 0;
      }

      return this.dataset.adoreScore;
    },

    dialScoreTitle() {
      if (this.metricConfig?.enableMetric) {
        let title = this.metadataHeaderObj(this.id).fullText.trim();
        title = title.length > 14 ? `${title.slice(0, 14).trim()}...` : title;
        return title;
      }

      return 'Adorescore';
    },

    id() {
      return this.datasetBenchmark;
    },

    isEditor() {
      return this.isEditable(this.id);
    },

    metricConfig() {
      return this.metricConfiguration(this.id);
    },

    overview() {
      return this.overviews.find(o => o.id === this.datasetBenchmark);
    },

    overviewCounts() {
      const { documentCount, topicCount, subTopicCount } = this.overview;

      // Object.entries output format
      return [
        ['comments', documentCount],
        ['themes', topicCount],
        ['subtopics', subTopicCount],
      ];
    },

    scorecard() {
      return this.scorecards.find(s => s.datasetId === this.id);
    },

    title() {
      if (this.scorecard?.insightsScorecard != null) return this.scorecard.insightsScorecard.title;

      return `Insights Scorecard for ${this.dataset.label}`;
    },
  },

};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-overview {
  @include flex("block", "column", "stretch", "stretch");

  margin-bottom: 1rem;

  &:not(.structured) {
    .title {
      margin-bottom: 1rem;
    }

    .overview-scorecard {
      .dial-and-stats {
        .dial {
          margin-left: 1rem;
          padding-top: 0;
        }

        .overview-stats {
          display: grid;
          grid-gap: 0.5rem;

          .stats, .breakdown {
            align-content: center;
            align-self: center;
            column-gap: 2.5rem;
            font-size: $font-size-sm;
            grid-template-rows: repeat(2, min-content);
            grid-template-columns: repeat(3, 1fr);
            margin-left: 2rem;
            padding: 1rem;
            row-gap: 0.7rem;

            h4 {
              grid-column: 1 / -1;
            }

            .overview-count .key {
              border-radius: 0.3rem;
              height: 0.6rem;
              width: 0.6rem;
            }
          }
        }
      }
    }
  }

  &.separate {
    margin-bottom: 0;

    .title {
      padding-left: 1rem;
      margin-bottom: 0;
    }

    .overview-scorecard {

      &.no-metadata {
        @include flex("block", "row", "start", "stretch");

        .dial-and-stats {
          padding: 1rem 0.1rem;
        }
      }

      .dial-and-stats {
        border-color: darken($border-color, 50%);
        border-radius: $border-radius-medium;
        border-style: dashed;
        border-width: 2px;
        padding: 1rem 0.1rem;
      }

      .meta-container {
        border-color: darken($border-color, 50%);
        border-radius: $border-radius-medium;
        border-style: dashed;
        border-width: 2px;
        padding: 1rem 0.5rem;
      }
    }
  }

  .title {
    @include flex("block", "row", "between", "center");

    margin-bottom: 1rem;
    padding-top: 0.3rem;
    padding-bottom: 0.3rem;
    width: 100%;

    .left {
      @include flex("block", "row", "start", "center");

      h2 {
        color: $insights-blue;
        font-size: $font-size-base;
      }

      .icon {
        color: $insights-blue;
        height: $font-size-sm;
        margin-right: 0.5rem;
        width: $font-size-sm;
      }
    }

    .right {
      .dropdown-btn {
        border: 1px solid $insights-bdr-key;
        border-radius: $border-radius-medium;
        cursor: pointer;
        font-size: $font-size-xs;
        padding: 0.1rem 0.4rem;
        transition: all $interaction-transition-time;
        user-select: none;

        &:hover {
          border-color: $body-copy;
        }
      }
    }
  }

  .overview-scorecard {
    @include flex("block", "row", "space-between", "stretch");

    margin-top: 0.5rem;
    width: 100%;

    .dial-and-stats {
      @include flex("block", "row", "start", "inherit");

      .dial {
        height: 180px;
        justify-self: center;
        margin-bottom: -1rem;
        margin-left: 1.4rem;
        margin-right: 2rem;
        padding-top: 0.4rem;
        width: 180px;
      }

      .overview-stats {
        display: grid;
        grid-gap: 0.5rem;

        .stats, .breakdown {
          align-self: stretch;
          border: $border-standard;
          border-radius: 2px;
          color: $insights-blue;
          display: grid;
          font-size: $font-size-xs;
          margin-left: 1rem;
          padding: 0.5rem 1rem;
          row-gap: 0.2rem;

          h4 {
            color: $insights-blue;
            font-weight: $font-weight-bold;
            text-transform: uppercase;
          }

          .overview-count {
            @include flex("block", "row", "start", "center");

            .key {
              @include rigid;

              border: 1px solid $border-color-dark;
              border-radius: 0.2rem;
              display: inline-block;
              height: 0.4rem;
              margin-right: 0.5rem;
              width: 0.4rem;

              &.adored {
                background-color: lighten($adored, 20%);
                border-color: $adored;
              }

              &.ignored {
                background-color: lighten($ignored, 20%);
                border-color: $ignored;
              }

              &.floored {
                background-color: lighten($floored, 20%);
                border-color: $floored;
              }
            }

            .overview-value {
              font-weight: $font-weight-bold;
              margin-right: 0.3rem;
              text-transform: capitalize;
            }

            .overview-label {
              @include truncate;
              font-weight: 400;
              margin: 0;
              max-width: 8rem;
              text-transform: capitalize;
            }
          }
        }
      }
    }

    .meta-container {
      @include flex("block", "row", "end", "center");

      margin-left: 1rem;
    }
  }
}
</style>
