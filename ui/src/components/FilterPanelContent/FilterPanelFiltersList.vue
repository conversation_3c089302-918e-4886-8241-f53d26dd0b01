<template>
  <section class="filter-panel-filters-list">
    <filter-panel-filters-list-item v-for="(obj, i) in filters"
                                :filterObj="obj"
                                :index="i"
                                :key="getKey(obj)"
                                class="filter-item">
    </filter-panel-filters-list-item>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import FilterPanelFiltersListItem from '@/components/FilterPanelContent/FilterPanelFiltersListItem';

export default {
  name: 'filter-panel-filters-list',

  components: {
    FilterPanelFiltersListItem,
  },

  computed: {
    ...mapState('snippets', ['filters']),
  },

  methods: {
    getKey(obj) {
      return obj.column?.val || -1;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.filter-panel-filters-list {
  @include flex("block", "column", "start", "stretch");

  width: inherit;
}
</style>
