<template>
  <section class="filter-panel-filters-list-item">
    <section class="left">
      <section class="filter-type">
        <section class="filter-column-select"
                 :class="{ unselected: !filterObj.column }">
          <comments-filters-column-dropdown :filter-obj="filterObj"
                                            :index="index"
                                            @select="onSelectColumn">
          </comments-filters-column-dropdown>
        </section>
        <section class="filter-emotion-select" v-if="showEmotionIndexDropdown">
          <comments-filters-emotion-index-dropdown :filter-obj="filterObj"
                                                   :index="index">
          </comments-filters-emotion-index-dropdown>
        </section>
        <section class="filter-operator-select"
                 v-if="filterObj.column">
          <comments-filters-operator-dropdown :filter-obj="filterObj"
                                              :index="index"
                                              @select="onSelectOperator">
          </comments-filters-operator-dropdown>
        </section>
      </section>
      <section class="filter-value" v-if="showCommentsFilterValue">
        <comments-filter-value :filter-obj="filterObj"
                               :index="index">
        </comments-filter-value>
      </section>
    </section>

    <section v-if="definedFilters.length" class="right">
      <section class="close comments-filters-popup-list-item-close" @click="onClickDelete">
        <i class="icon fa-regular fa-xmark"/>
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import CommentsFilterColumn from '@/enum/comments-filter-column';
import CommentsFiltersColumnDropdown from '@/components/CommentsFilters/CommentsFiltersColumnDropdown';
import CommentsFiltersEmotionIndexDropdown from '@/components/CommentsFilters/CommentsFiltersEmotionIndexDropdown';
import CommentsFiltersOperatorDropdown from '@/components/CommentsFilters/CommentsFiltersOperatorDropdown';
import CommentsFilterValue from '@/components/CommentsFilters/CommentsFilterValue';
import { metadataApi } from '@/services/api';

export default {
  name: 'filter-panel-filters-list-item',

  components: {
    CommentsFiltersColumnDropdown,
    CommentsFiltersEmotionIndexDropdown,
    CommentsFiltersOperatorDropdown,
    CommentsFilterValue,
  },

  props: {
    index: {
      type: Number,
      required: true,
    },
    filterObj: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      CommentsFilterColumn,
    };
  },

  computed: {
    ...mapState('datasets', { datasetId: 'active' }),

    ...mapState('snippets', ['filters']),

    definedFilters() {
      return this.filters.filter(o => o.column);
    },

    showEmotionIndexDropdown() {
      return this.filterObj.column?.val === CommentsFilterColumn.EMOTION.name;
    },

    showCommentsFilterValue() {
      if (this.filterObj.column?.val === CommentsFilterColumn.EMOTION.name) {
        return this.filterObj.operator
          && this.filterObj.values
          && this.filterObj.emotionIndex != null;
      }
      return this.filterObj.operator && this.filterObj.values;
    },
  },

  methods: {
    ...mapActions('snippets', [
      'addNewFilter',
      'editFilterValueLimit',
      'editFilterValues',
      'removeFilter',
      'updateFilterUndefined',
    ]),

    onClickDelete() {
      this.removeFilter({ index: this.index });
      if (!this.filters.length) {
        this.addNewFilter();
      } else if (this.filterObj.column == null) {
        this.updateFilterUndefined({ value: false });
      }
    },

    async onSelectColumn(item, i) {
      if (item.group === 0) {
        this.setValueLimitSystemDefault(item, i);
      } else if (item.isDateTime) {
        this.setValueLimitEmpty(item, i);
      } else if (item.isText) {
        this.setValueLimitTextDistinct(item, i);
      } else if (item.isNumeric) {
        this.setValueLimitNumericRange(item, i);
      }
    },

    onSelectOperator(item, i) {
      let rs = [];

      if (this.filterObj.column.val === CommentsFilterColumn.COMMENT_LENGTH.name) {
        rs = item.hasRangeValues() ? [0, 500] : [0];
      } else if (this.filterObj.column.isNumeric) {
        if (item.hasRangeValues()) {
          rs = [...this.filterObj.valueLimit];
        } else {
          const val = this.filterObj.valueLimit?.slice().sort((a, b) => a - b)[0] || 0;
          rs = [val];
        }
      }

      this.editFilterValues({ index: i, values: rs });
    },

    setValueLimitEmpty(item, i) {
      this.editFilterValueLimit({ index: i, values: [] });
      this.editFilterValues({ index: i, values: [] });
    },

    async setValueLimitNumericRange(item, i) {
      const rs = await metadataApi.getRangeForNumericData(this.datasetId, item.metadataIndex);
      this.editFilterValueLimit({ index: i, values: rs || [] });
      this.editFilterValues({ index: i, values: rs || [0, 0] });
    },

    setValueLimitSystemDefault(item, i) {
      this.editFilterValueLimit({ index: i, values: item.defaultLimit || [] });
      let initVal;
      switch (item.val) {
        case CommentsFilterColumn.COMMENT_LENGTH.name:
          initVal = [0, 500];
          break;
        case CommentsFilterColumn.EMOTION_BREAKDOWN.name:
        case CommentsFilterColumn.EMOTION_INTENSITY.name:
          initVal = [];
          break;
        default:
          initVal = item.defaultLimit;
          break;
      }
      this.editFilterValues({ index: i, values: initVal });
    },

    async setValueLimitTextDistinct(item, i) {
      const rs = await metadataApi.getDistinctValuesForTextData(this.datasetId, item.metadataIndex);
      this.editFilterValueLimit({ index: i, values: rs || [] });
      this.editFilterValues({ index: i, values: [] });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.filter-panel-filters-list-item {
  @include flex("block", "row", "start", "stretch");
  @include stretch;

  border-bottom: $border-light solid $cfp-bottom-border-clr;
  margin-top: 0.5rem;
  padding: 0 1.5rem 1.5rem 1.5rem;
  position: relative;

  .left {
    @include flex("block", "column", "start", "stretch");
    @include stretch;

    .filter-type {
      @include flex("block", "row", "start", "center");
      @include size-two-thirds;

      column-gap: 1em;
      flex-flow: row wrap;

      .filter-column-select {
        @include size-evenly;
        margin-top: 15px;

        &.unselected {
          margin-right: 0;
        }
      }

      .filter-emotion-select {
        @include size-evenly;
        margin-top: 15px;
      }

      .filter-operator-select {
        @include size-evenly;
        margin-top: 15px;
      }
    }

    .filter-value {
      margin-top: 0.7rem;
    }
  }

  .right {
    @include flex("block", "row", "center", "center");

    position: absolute;
    right: 7px;
    top: 22px;

    .close {
      @include flex("block", "row", "center", "center");

      cursor: pointer;
      height: 0.7rem;
      width: 0.7rem;

      &:hover .icon {
        opacity: 1;
        transform: scale(1.2);
      }

      .icon {
        height: 0.7rem;
        opacity: 0.5;
        transition: opacity $interaction-transition-time;
        width: 0.7rem;
      }
    }
  }
}
</style>
