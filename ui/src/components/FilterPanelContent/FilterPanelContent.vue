<template>
  <section class="filter-panel-content">
    <filter-panel-filters-list />
    <filter-panel-add-filter />
  </section>
</template>

<script>
import FilterPanelAddFilter from '@/components/FilterPanelContent/FilterPanelAddFilter';
import FilterPanelFiltersList from '@/components/FilterPanelContent/FilterPanelFiltersList';

export default {
  name: 'filter-panel-content',

  components: {
    FilterPanelAddFilter,
    FilterPanelFiltersList,
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.filter-panel-content {
  @include flex("block", "column", "start", "start");

  width: 100%;
}
</style>
