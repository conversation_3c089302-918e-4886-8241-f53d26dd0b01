<template>
  <section class="filter-panel-add-filter">
    <section class="horizontal-line" />
    <base-button class="add-btn"
                 :disabled="filterUndefined"
                 @click="onClickAddFilter"
                 icon="plus"
                 size="small">
      Add Filter
    </base-button>
    <section class="horizontal-line" />
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';

export default {
  name: 'filter-panel-add-filter',

  components: {
    BaseButton,
  },

  computed: {
    ...mapState('snippets', ['filters', 'filterUndefined']),
  },

  methods: {
    ...mapActions('snippets', ['addNewFilter']),

    onClickAddFilter() {
      if (!this.filterUndefined) {
        this.addNewFilter();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.filter-panel-add-filter {
  @include flex("block", "row", "start", "center");

  padding: 1.5rem;
  width: 100%;

  .horizontal-line {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    width: 100%;
  }

  .add-btn {
    background-color: transparent;
    color: clr("white");
    display: block;
    font-size: 11px;
    font-weight: $font-weight-bold;
    padding: 0 0.8rem 0 0.4rem;
    text-transform: uppercase;
    white-space: nowrap;
    width: 100%;

    &.disabled-true {
      opacity: 0.2;
      cursor: not-allowed;
    }

    &:hover, &:focus {
      background-color: unset;
      color: rgba(clr("white"), 0.89);

      &.disabled-true {
        background-color: transparent;
        color: clr("white");
      }
    }
  }
}
</style>
