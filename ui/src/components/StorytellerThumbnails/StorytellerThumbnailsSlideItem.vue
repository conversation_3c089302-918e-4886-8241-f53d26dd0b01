<template>
  <section class="storyteller-thumbnails-slide-item" :class="{active, activeGroup, hover}">
    <i v-if="isIntro" class="fa-light fa-rectangle alias" />
    <i v-else-if="isThemesInsights" class="fa-light fa-list alias" />
    <i v-else-if="isPresentationFlow" class="fa-light fa-book-open-cover alias" />
    <span v-else class="alias text">{{alias}}</span>
    <section class="slide-wrapper" @mouseenter="hover = true" @mouseleave="hover = false" @click="selectThumbnail">
      <component
        ref="slide"
        class="slide"
        :is="slideComponent"
        :slide-data="slide.slideData"
        :slide-width="slideWidth"
      />
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import getSlideComponent from '@/helpers/storyteller-utils';
import StorytellerSlideType from '@/enum/storyteller-slide-type';

export default {
  name: 'storyteller-thumbnails-slide-item',

  props: {
    slide: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      hover: false,
      slideWidth: 0,
    };
  },

  computed: {
    ...mapState('storyteller', ['selectedSlide']),

    active() {
      return this.selectedSlide.id === this.slide.id;
    },

    activeGroup() {
      return this.selectedSlide.slideData.themeId && this.selectedSlide.slideData.themeId === this.slide.slideData.themeId;
    },

    alias() {
      return this.slide.slideData.alias;
    },

    isIntro() {
      return this.slide.slideType === StorytellerSlideType.INTRO.name;
    },

    isPresentationFlow() {
      return this.slide.slideType === StorytellerSlideType.PRESENTATION_FLOW.name;
    },

    isThemesInsights() {
      return this.slide.slideType === StorytellerSlideType.THEMES_INSIGHTS.name;
    },

    slideComponent() {
      return getSlideComponent(this.slide.slideType);
    },
  },

  mounted() {
    this.slideWidth = this.$refs.slide.$el.offsetWidth;
  },

  methods: {
    ...mapActions('storyteller', ['selectSlide']),

    selectThumbnail() {
      this.selectSlide({ slide: this.slide });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-thumbnails-slide-item {
  @include flex("block", "row", "start", "start");

  .alias {
    color: rgba(19, 28, 41, 0.5);
    font-size: 11px;
    margin-left: 0.6rem;
    margin-right: 0.6rem;
    width: 1rem;

    &.text {
      font-weight: $font-weight-medium;
    }
  }

  .slide-wrapper {
    background-color: clr('white');
    border-radius: 2px;
    cursor: pointer;
    margin-bottom: 1rem;
    outline: 1px solid rgba(154, 154, 154, 0.5);

    .slide {
      border-radius: 2px;
      height: 80px;
      width: 140px;
    }
  }

  &.activeGroup {
    .slide-wrapper {
      outline: 1px solid rgba(157, 83, 243, 0.3);
    }
  }

  &.hover {
    .slide-wrapper {
      outline: 1px solid #AD87D7;
    }

    .alias {
      color: #AD87D7;
    }
  }

  &.active {
    .alias {
      color: rgba(157, 83, 243, 1);

      &.text {
        font-weight: $font-weight-bold;
      }
    }

    .slide-wrapper {
      outline: 2px solid rgba(157, 83, 243, 1);
    }
  }
}
</style>
