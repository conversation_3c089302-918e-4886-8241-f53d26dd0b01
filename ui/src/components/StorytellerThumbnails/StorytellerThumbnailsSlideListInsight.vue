<template>
  <section class="storyteller-thumbnails-slide-list-insight">
    <storyteller-thumbnails-title text="Insight Storyteller" icon="fa-solid fa-book icon-book" :active="selectedSlideIsInsightSlide" />
    <storyteller-thumbnails-slide-item v-for="(slide) in loadingSlides" :key="slide.id" :slide="slide"/>
    <storyteller-thumbnails-slide-item v-if="slideIntro && displaySlides.intro" :slide="slideIntro"/>
    <storyteller-thumbnails-slide-item v-if="slideThemesInsights && displaySlides.themesInsights" :slide="slideThemesInsights"/>
    <storyteller-thumbnails-slide-item v-if="slideFlow && displaySlides.presentationFlow" :slide="slideFlow"/>
    <section v-for="(themeId) in activeReport.themeIds" :key="themeId">
      <section class="thumbnail-theme-group">
        <section class="horizontal-start" v-show="activeGroup(themeId)"/>
        <storyteller-thumbnails-slide-item v-for="(slide, index) in getGroupSlide(themeId)"
          class="thumbnail-theme"
          :key="slide.id"
          :slide="slide"
          :class="{last: index === getGroupSlide(themeId).length - 1, activeGroup: activeGroup(themeId)}"
        />
        <section class="vertical" v-show="activeGroup(themeId)"/>
        <section class="horizontal-end" v-show="activeGroup(themeId)"/>
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import StorytellerSlideType from '@/enum/storyteller-slide-type';
import StorytellerThumbnailsSlideItem from '@/components/StorytellerThumbnails/StorytellerThumbnailsSlideItem';
import StorytellerThumbnailsTitle from '@/components/StorytellerThumbnails/StorytellerThumbnailsTitle';

export default {
  name: 'storyteller-thumbnails-slide-list-insight',

  components: {
    StorytellerThumbnailsSlideItem,
    StorytellerThumbnailsTitle,
  },

  computed: {
    ...mapGetters('storyteller', [
      'displaySlides',
      'loadingSlides',
      'slideFlow',
      'slideIntro',
      'slideThemesInsights',
      'themeSlides',
    ]),

    ...mapState('storyteller', ['activeReport', 'selectedSlide']),

    selectedSlideIsInsightSlide() {
      return StorytellerSlideType.isInsightSlide(this.selectedSlide.slideType);
    },
  },

  methods: {
    ...mapActions('storyteller', ['selectNextSlide', 'selectPrevSlide']),

    activeGroup(themeId) {
      return this.selectedSlide.slideData.themeId === themeId;
    },

    getGroupSlide(themeId) {
      return this.themeSlides[themeId];
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-thumbnails-slide-list-insight {
  @include flex("block", "column", "start", "start");

  width: 100%;

  .thumbnail-theme-group {
    position: relative;

    .horizontal-start {
      background-color: rgba(157, 83, 243, 0.25);
      height: 1px;
      position: absolute;
      top: 0;
      width: 6px;
    }

    .thumbnail-theme {
      border-left: 1px solid rgba(157, 83, 243, 0);

      &.activeGroup {
        border-left: 1px solid rgba(157, 83, 243, 0.3);
      }

      &.last {
        border-left: 1px solid rgba(157, 83, 243, 0);
      }
    }

    .vertical {
      background-color: rgba(157, 83, 243, 0.25);
      bottom: 90px;
      height: 6px;
      position: absolute;
      width: 1px;
    }

    .horizontal-end {
      background-color: rgba(157, 83, 243, 0.25);
      bottom: 89px;
      height: 1px;
      position: absolute;
      width: 6px;
    }
  }
}
</style>
