<template>
  <section class="storyteller-thumbnails-header">
    <i class="fa-regular fa-presentation-screen icon-screen"></i>
    <span>{{slideCount}} Slide{{ slideCount === 1 ? '' : 's' }}</span>
  </section>
</template>

<script>
import { mapState } from 'vuex';

export default {
  name: 'storyteller-thumbnails-header',

  computed: {
    ...mapState('storyteller', ['slides']),

    slideCount() {
      return this.slides.length;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-thumbnails-header {
  @include flex("block", "row", "start", "center");

  border-bottom: 1px solid rgba(217, 217, 217, 0.5);
  font-size: $font-size-sm;
  font-weight: $font-weight-bold;
  height: $storyteller-header-height;
  min-height: $storyteller-header-height;
  padding-left: 1.5rem;
  width: 100%;

  .icon-screen {
    margin-right: 0.4rem;
  }
}
</style>
