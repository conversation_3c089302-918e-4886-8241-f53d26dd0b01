<template>
  <section class="storyteller-thumbnails-title" :class="{active}">
    <i :class="icon" />
    <span class="text">{{text}}</span>
  </section>
</template>

<script>

export default {
  name: 'storyteller-thumbnails-title',

  props: {
    active: {
      type: Boolean,
      default: false,
    },
    icon: {
      type: String,
      required: true,
    },
    text: {
      type: String,
      required: true,
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-thumbnails-title {
  @include flex("block", "row", "start", "center");

  color: rgba(19, 28, 41, 0.5);
  font-size: 11px;
  font-weight: $font-weight-extra-bold;
  margin-bottom: 1.2rem;
  text-transform: uppercase;
  width: 100%;

  i {
    margin-right: 0.4rem;
  }

  &.active {
    color: rgba(157, 83, 243, 1);
  }
}
</style>
