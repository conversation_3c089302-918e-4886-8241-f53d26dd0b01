<template>
  <section class="storyteller-thumbnails">
    <storyteller-thumbnails-header />
    <storyteller-thumbnails-slide-list />
  </section>
</template>

<script>
import StorytellerThumbnailsHeader from '@/components/StorytellerThumbnails/StorytellerThumbnailsHeader';
import StorytellerThumbnailsSlideList from '@/components/StorytellerThumbnails/StorytellerThumbnailsSlideList';

export default {
  name: 'storyteller-thumbnails',

  components: {
    StorytellerThumbnailsHeader,
    StorytellerThumbnailsSlideList,
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-thumbnails {
  @include flex("block", "column", "start", "start");

  border-right: 1px solid rgba(217, 217, 217, 0.5);
  overflow-y: auto;
}
</style>
