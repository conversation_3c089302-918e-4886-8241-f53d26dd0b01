<template>
  <section class="storyteller-thumbnails-slide-list-action-plans">
    <storyteller-thumbnails-title text="Suggested Actions" icon="fa-solid fa-square-check" :active="selectedSlideIsActionSlide" />
    <storyteller-thumbnails-slide-item v-for="(slide) in slideActionPlans" :key="slide.id" :slide="slide" />
  </section>
</template>

<script>
import { mapGetters, mapState } from 'vuex';

import StorytellerSlideType from '@/enum/storyteller-slide-type';
import StorytellerThumbnailsSlideItem from '@/components/StorytellerThumbnails/StorytellerThumbnailsSlideItem';
import StorytellerThumbnailsTitle from '@/components/StorytellerThumbnails/StorytellerThumbnailsTitle';

export default {
  name: 'storyteller-thumbnails-slide-list-action-plans',

  components: {
    StorytellerThumbnailsSlideItem,
    StorytellerThumbnailsTitle,
  },

  computed: {
    ...mapGetters('storyteller', ['activeSlides']),

    ...mapState('storyteller', ['selectedSlide']),

    selectedSlideIsActionSlide() {
      return StorytellerSlideType.isActionPlansSlide(this.selectedSlide.slideType);
    },

    slideActionPlans() {
      return this.activeSlides.filter(slide => slide.slideType === StorytellerSlideType.ACTION_PLANS.name);
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-thumbnails-slide-list-action-plans {
  @include flex("block", "column", "start", "start");

  margin-top: 0.4rem;
  width: 100%;
}
</style>
