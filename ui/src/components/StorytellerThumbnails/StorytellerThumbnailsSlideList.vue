<template>
  <section ref="slideList" class="storyteller-thumbnails-slide-list">
    <storyteller-thumbnails-slide-list-insight />
    <storyteller-thumbnails-slide-list-action-plans v-if="!noActionPlans" />
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import StorytellerSlideType from '@/enum/storyteller-slide-type';
import StorytellerThumbnailsSlideListActionPlans from '@/components/StorytellerThumbnails/StorytellerThumbnailsSlideListActionPlans';
import StorytellerThumbnailsSlideListInsight from '@/components/StorytellerThumbnails/StorytellerThumbnailsSlideListInsight';

export default {
  name: 'storyteller-thumbnails-slide-list',

  components: {
    StorytellerThumbnailsSlideListActionPlans,
    StorytellerThumbnailsSlideListInsight,
  },

  computed: {
    ...mapState('storyteller', ['activeReport', 'selectedSlide']),

    ...mapState('storytellerActionPlans', ['selectedThemeId']),

    noActionPlans() {
      return !(this.activeReport.actionPlanThemeIds?.length);
    },

    selectedSlideId() {
      return this.selectedSlide.id;
    },
  },

  async mounted() {
    window.addEventListener('keydown', this.onKeydown);
  },

  beforeDestroy() {
    window.removeEventListener('keydown', this.onKeydown);
  },

  watch: {
    selectedSlideId() {
      this.scrollToSelectedSlide();

      // For action plan slides: when selected slide change, if it already has the selected theme ID, do nothing.
      // Otherwise, select the first theme ID from the actions.
      if (StorytellerSlideType.isActionPlansSlide(this.selectedSlide.slideType)) {
        const actionThemeIds = this.selectedSlide.slideData.actions.map(action => action.themeId);

        if (actionThemeIds.includes(this.selectedThemeId)) {
          return;
        }

        const firstActionThemeId = this.selectedSlide.slideData.actions[0].themeId;
        this.selectThemeId({ themeId: firstActionThemeId });
      }
    },
  },

  methods: {
    ...mapActions('storyteller', ['selectNextSlide', 'selectPrevSlide']),

    ...mapActions('storytellerActionPlans', ['selectThemeId']),

    onKeydown(e) {
      if (e.key === 'ArrowDown') {
        e.preventDefault();
        this.selectNextSlide();
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        this.selectPrevSlide();
      }
    },

    scrollToSelectedSlide() {
      this.$nextTick(() => {
        const { slideList } = this.$refs;
        const selectedSlideElement = slideList.querySelector('.storyteller-thumbnails-slide-item.active');

        if (selectedSlideElement) {
          const slideRect = selectedSlideElement.getBoundingClientRect();
          const listRect = slideList.getBoundingClientRect();

          if (slideRect.bottom > listRect.bottom) {
            slideList.scrollTop += slideRect.bottom - listRect.bottom + 10;
          } else if (slideRect.top < listRect.top) {
            slideList.scrollTop -= listRect.top - slideRect.top + 10;
          }
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-thumbnails-slide-list {
  @include flex("block", "column", "start", "start");
  @include scrollbar-thin;

  overflow-x: hidden;
  overflow-y: auto;
  padding: 1.2rem 1.5rem;
  width: 100%;
}
</style>
