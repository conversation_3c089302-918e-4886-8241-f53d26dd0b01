<template>
  <section class="illustrative-comment-modal-results" @scroll.passive="onScroll">
    <section v-for="comment in snippets"
      :key="comment.docId"
      class="comment"
      @click="onSelect(comment)">
      <base-checkbox :value="isChecked(comment)"/>
      <section class="text">
        {{ comment.content }}
      </section>
    </section>

    <loading-blocks-overlay v-if="loading">Loading Comments...</loading-blocks-overlay>
    <loading-blocks-overlay v-if="!loading && hasMore" ref="loader">Loading Comments...</loading-blocks-overlay>

    <section v-if="hasNoComment" class="no-comments">No comments found for this search query.</section>
  </section>
</template>

<script>
import { throttle } from 'lodash-es';
import { mapActions, mapState } from 'vuex';

import BaseCheckbox from '@/components/Base/BaseCheckbox';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';

import { searchRequest } from '@/services/request';

export default {
  name: 'illustrative-comment-modal-results',

  components: {
    BaseCheckbox,
    LoadingBlocksOverlay,
  },

  data() {
    return {
      loaderVisible: false,
    };
  },

  computed: {
    ...mapState('datasetsInsights', ['commentSelection', 'commentTheme']),

    ...mapState('search', [
      'count',
      'loading',
      'snippets',
      'valid',
    ]),

    hasMore() {
      return this.snippets.length < this.count;
    },

    hasNoComment() {
      return !this.loading && this.valid && !this.count && !this.snippets?.length;
    },
  },

  watch: {
    async loaderVisible() {
      if (this.loaderVisible) {
        await searchRequest.loadMoreTopic({ datasetId: this.commentTheme.datasetId, topicId: this.commentTheme.id });
      }
    },
  },

  methods: {
    ...mapActions('datasetsInsights', ['setCommentSelection']),

    checkVisible() {
      return this.getLoaderRect().top - this.getRect().top < this.getRect().height;
    },

    getRect() {
      return this.$el.getBoundingClientRect();
    },

    getLoaderRect() {
      return this.$refs.loader.$el.getBoundingClientRect();
    },

    isChecked(comment) {
      return comment.docId === this.commentSelection?.id;
    },

    onScroll: throttle(function throttleOnScroll() {
      if (this.hasMore) this.setLoaderVisible();
    }, 1000),

    onSelect(comment) {
      this.setCommentSelection({
        comment: {
          id: comment.docId,
          content: comment.content,
        },
      });
    },

    setLoaderVisible() {
      this.loaderVisible = this.checkVisible();
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.illustrative-comment-modal-results {
  @include flex("block", "column", "start", "stretch");
  @include stretch;

  overflow-y: auto;

  .comment {
    @include flex("block", "row", "start", "center");

    border-bottom: $border-standard;
    cursor: pointer;
    margin: 0.5rem 0;
    padding: 0.5rem 0;

    &:hover {
      background-color: clr('white');
    }

    .base-checkbox {
      @include rigid;

      margin: 0 0.5rem;
      pointer-events: none;
    }

    .text {
      font-size: $font-size-sm;
    }
  }

  .loading-blocks-overlay {
    margin: 1rem 0;
  }

  .no-comments {
    @include flex("block", "row", "center", "center");
    @include stretch;

    color: $body-copy-light;
    margin: 1.5rem 1.5rem 0;
  }
}
</style>
