<template>
  <section class="illustrative-comment-modal-comments" @scroll.passive="onScroll">
    <section v-for="comment in selectableComments"
      :key="comment.userDocId"
      class="comment"
      @click="onSelect(comment)">
      <base-checkbox :value="isChecked(comment)"/>
      <section class="text">
        {{ comment.content }}
      </section>
    </section>

    <loading-blocks-overlay v-if="hasMore" ref="loader">Loading Comments...</loading-blocks-overlay>
  </section>
</template>

<script>
import { throttle } from 'lodash-es';
import { mapActions, mapState } from 'vuex';

import BaseCheckbox from '@/components/Base/BaseCheckbox';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';

export default {
  name: 'illustrative-comment-modal-comments',

  components: {
    BaseCheckbox,
    LoadingBlocksOverlay,
  },

  props: {
    hasMore: {
      type: Boolean,
      required: true,
    },
  },

  data() {
    return {
      loaderVisible: false,
    };
  },

  computed: {
    ...mapState('datasetsInsights', ['commentSelection', 'selectableComments']),
  },

  watch: {
    loaderVisible() {
      if (this.loaderVisible) {
        this.$emit('fetch');
      }
    },
  },

  methods: {
    ...mapActions('datasetsInsights', ['setCommentSelection']),

    checkVisible() {
      return this.getLoaderRect().top - this.getRect().top < this.getRect().height;
    },

    getRect() {
      return this.$el.getBoundingClientRect();
    },

    getLoaderRect() {
      return this.$refs.loader.$el.getBoundingClientRect();
    },

    isChecked(comment) {
      return comment.userDocId === this.commentSelection?.id;
    },

    onScroll: throttle(function throttleOnScroll() {
      if (this.hasMore) this.setLoaderVisible();
    }, 1000),

    onSelect(comment) {
      if (this.isChecked(comment)) {
        this.setCommentSelection({ comment: null });
      } else {
        this.setCommentSelection({
          comment: {
            id: comment.userDocId,
            content: comment.content,
          },
        });
      }
    },

    setLoaderVisible() {
      this.loaderVisible = this.checkVisible();
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.illustrative-comment-modal-comments {
  @include stretch;

  overflow-y: auto;

  .comment {
    @include flex("block", "row", "start", "center");

    border-bottom: $border-standard;
    cursor: pointer;
    margin: 0.5rem 0;
    padding: 0.5rem 0;

    &:hover {
      background-color: clr('white');
    }

    .base-checkbox {
      @include rigid;

      margin: 0 0.5rem;
      pointer-events: none;
    }

    .text {
      font-size: $font-size-sm;
    }
  }
}
</style>
