<template>
  <section class="illustrative-comment-modal-search-error">
    <section class="header">
      <x-circle-icon class="icon"/>
      <h3>There are errors in your query:</h3>
    </section>

    <section class="messages">
      <span v-for="(error, index) in queryErrors" :key="index">
        {{ error }}
      </span>

      <span
        v-if="queryErrors.length === 0"
      >We're not sure what's wrong with your query. Here are some general guidelines for creating search queries - if you're still not sure, please contact us and we'll try to help you out.</span>
      <span v-if="queryErrors.length === 0">Balance any Boolean operators (AND, OR, NOT) by making sure there is text on either side.</span>
      <span v-if="queryErrors.length === 0">Only use one NOT operator per group - use parentheses to create groups.</span>
      <span v-if="queryErrors.length === 0">Balance groups by making sure every opening parenthesis has a closing parenthesis.</span>
    </section>
  </section>
</template>

<script>
import { XCircleIcon } from 'vue-feather-icons';
import { mapGetters } from 'vuex';

export default {
  name: 'illustrative-comment-modal-search-error',

  components: {
    XCircleIcon,
  },

  computed: {
    ...mapGetters('search', ['queryErrors']),
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.illustrative-comment-modal-search-error {
  @include flex("block", "column", "start", "stretch");
  @include panel;

  margin-top: 0.5rem;
  padding: 1rem;

  .header {
    @include flex("block", "row", "start", "center");

    margin-bottom: 0.5rem;

    h3 {
      font-size: $font-size-sm;
      margin: 0 0.5rem;
    }

    .icon {
      color: clr('red');
      height: $font-size-base;
      width: $font-size-base;
    }
  }

  .messages {
    @include flex("block", "column", "start", "stretch");
    @include stretch;

    font-size: $font-size-sm;
    white-space: normal;

    span {
      margin-bottom: 0.3rem;
    }
  }
}
</style>
