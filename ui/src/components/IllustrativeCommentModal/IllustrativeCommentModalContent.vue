<template>
  <section class="illustrative-comment-modal-content">
    <h3 class="header">
      Illustrative Content
    </h3>
    <section class="comment-content">
      <textarea rows="5" ref="content"
                      class="text-area"
                      :class="{disabled}"
                      disabled
                      v-if="disabled"
      />
      <textarea rows="5" ref="content"
                v-model="localContent"
                class="text-area"
                v-else
      />

    </section>
    <section v-if="disabled" class="note">
      Please select a comment!
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

export default {
  name: 'illustrative-comment-modal-content',

  computed: {
    ...mapState('datasetsInsights', ['commentSelection']),

    localContent: {
      get() {
        return this.commentSelection ? this.commentSelection.content || '' : '';
      },
      set(newVal) {
        if (!newVal) {
          this.setCommentSelection({ comment: null });
        } else {
          this.setCommentSelection({ comment: { id: this.commentSelection.id, content: newVal } });
        }
      },
    },

    disabled() {
      return !this.localContent;
    },
  },

  methods: {
    ...mapActions('datasetsInsights', ['setCommentSelection']),
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.illustrative-comment-modal-content {
  @include flex("block", "column", "start", "stretch");
  margin-bottom: 1rem;

  .header {
    @include flex("block", "row", "start", "center");
    font-weight: $font-weight-medium;
    margin-bottom: 0.5rem;
  }

  .comment-content {
    @include flex("block", "row", "start", "stretch");
    @include stretch;

    .text-area {
      border: 1px solid $border-color;
      border-radius: $border-radius-medium;
      color: $body-copy;
      font-size: $font-size-xs;
      line-height: 1.125rem;
      outline: none;
      overflow-x: hidden;
      overflow-y: auto;
      padding: 0.5rem;
      resize: none;
      width: 100%;

      &.disabled {
        cursor: not-allowed;
        opacity: 0.5;
      }
    }
  }

  .note {
    color: red;
    font-size: $font-size-xxs;
    margin-top: 0.2rem;
  }
}
</style>
