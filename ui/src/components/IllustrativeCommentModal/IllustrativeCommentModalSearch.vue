<template>
  <section class="illustrative-comment-modal-search">
    <section class="editor-wrapper" @click="focused = true">
      <section v-show="!hasSearch && !focused" class="placeholder">
        <search-icon class="icon"/>
        Type to search for comment
      </section>
      <editor v-show="hasSearch || focused"
        :focused="focused"
        :query="localText"
        :tag="tag"
        @blur="focused = false"
        @input="onEditorInput"
        @tag-inserted="tag = null"
      />
    </section>
    <search-controls-operators/>
  </section>
</template>

<script>
import { debounce } from 'lodash-es';
import { SearchIcon } from 'vue-feather-icons';
import { mapActions, mapGetters, mapState } from 'vuex';

import bus from '@/helpers/bus';
import Editor from '@/components/Search/Editor/Editor';
import SearchControlsOperators from '@/components/Search/Controls/SearchControlsOperators';

import { searchRequest } from '@/services/request';

export default {
  name: 'illustrative-comment-modal-search',

  components: {
    Editor,
    SearchControlsOperators,
    SearchIcon,
  },

  data() {
    return {
      focused: false,
      localText: '',
      tag: null,
    };
  },

  computed: {
    ...mapState('datasetsInsights', ['commentTheme']),

    ...mapState('search', ['text', 'valid']),

    ...mapGetters('search', ['query']),

    hasSearch() {
      return this.text != null && this.text !== '';
    },
  },

  watch: {
    async query() {
      this.setLoading({ loading: true });

      await this.debounceValidate();
    },

    async valid() {
      this.setSnippets({ snippets: [] });

      if (this.valid) {
        await searchRequest.searchTopic({ datasetId: this.commentTheme.datasetId, topicId: this.commentTheme.id });
      }
    },
  },

  created() {
    this.reset();
    bus.$on('select-search-operator', this.onSelectTag);
  },

  methods: {
    ...mapActions('search', [
      'reset',
      'setLoading',
      'setSnippets',
      'setText',
      'setValid',
    ]),

    debounceValidate: debounce(async function debounceValidate() {
      await searchRequest.validateTopic({ datasetId: this.commentTheme.datasetId, topicId: this.commentTheme.id });
    }, 500),

    onEditorInput(text) {
      this.setValid({ valid: true });
      this.setText({ text });
    },

    onSelectTag(tag) {
      this.tag = tag;
    },
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.illustrative-comment-modal-search {
  @include flex("block", "column", "start", "start");

  .editor-wrapper {
    @include flex("block", "row", "start", "center");

    background-color: clr('white');
    border: $border-standard;
    border-radius: $border-radius-medium;
    height: 42px;
    padding: 0 0.3rem;
    position: relative;
    width: 100%;

    .placeholder {
      @include flex("block", "row", "start", "stretch");

      color: $body-copy-light;
      font-size: $font-size-xs;

      .icon {
        height: $font-size-sm;
        margin: 0 0.5rem;
        width: $font-size-sm;
      }
    }

    .editor {
      margin: 0;
      width: 100%;

      .tag {
        font-size: $font-size-xs;
      }
    }
  }

  .search-controls-operators {
    margin-top: 0.5rem;

    .search-controls-operators-tag {
      padding: 0.3rem 0.6rem;
    }
  }
}
</style>
