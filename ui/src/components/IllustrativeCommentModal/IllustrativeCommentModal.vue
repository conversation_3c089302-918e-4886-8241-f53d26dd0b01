<template>
  <section class="illustrative-comment-modal">
    <section class="header">
      <h2>Select Comments from <b>{{commentTheme.topicLabel}}</b></h2>
    </section>

    <section class="body">
      <illustrative-comment-modal-content ></illustrative-comment-modal-content>
      <illustrative-comment-modal-search/>

      <illustrative-comment-modal-results v-if="hasSearch"/>
      <illustrative-comment-modal-search-error v-if="!valid && !loading"/>

      <illustrative-comment-modal-comments v-if="!hasSearch" :has-more="hasMore" @fetch="fetchSnippets"/>

      <loading-blocks-overlay v-if="showLoading">
        Loading Comments...
      </loading-blocks-overlay>
    </section>

    <section class="footer">
      <base-button colour="light" size="small" type="link" @click="onClickCancel">Cancel</base-button>
      <base-button size="small" :disabled="hasError" @click="onClickSelect">Select</base-button>
    </section>
  </section>
</template>

<script>
import { throttle } from 'lodash-es';
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseCheckbox from '@/components/Base/BaseCheckbox';
import ******************************** from '@/components/IllustrativeCommentModal/********************************';
import IllustrativeCommentModalContent from '@/components/IllustrativeCommentModal/IllustrativeCommentModalContent';
import IllustrativeCommentModalResults from '@/components/IllustrativeCommentModal/IllustrativeCommentModalResults';
import IllustrativeCommentModalSearch from '@/components/IllustrativeCommentModal/IllustrativeCommentModalSearch';
import IllustrativeCommentModalSearchError from '@/components/IllustrativeCommentModal/IllustrativeCommentModalSearchError';
import intercomEvent from '@/services/IntercomEvent';
import intercomEvents from '@/enum/intercom-events';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';

import { datasetInsightApiV0, snippetApi } from '@/services/api';

export default {
  name: 'illustrative-comment-modal',

  components: {
    BaseButton,
    BaseCheckbox,
    ********************************,
    IllustrativeCommentModalContent,
    IllustrativeCommentModalResults,
    IllustrativeCommentModalSearch,
    IllustrativeCommentModalSearchError,
    LoadingBlocksOverlay,
  },

  data() {
    return {
      commentsMeta: {},
    };
  },

  computed: {
    ...mapState('datasetsInsights', [
      'commentSelection',
      'commentTheme',
      'datasetBenchmark',
      'scorecards',
      'selectableComments',
    ]),

    ...mapState('search', [
      'loading',
      'snippets',
      'text',
      'valid',
    ]),

    hasError() {
      return !this.commentSelection?.id
        || !this.commentSelection?.content;
    },

    hasMore() {
      if (this.commentsMeta.count == null) return true;

      return this.selectableComments.length !== this.commentsMeta.count;
    },

    hasSearch() {
      return this.text != null && this.text !== '';
    },

    persistedComments() {
      return this.scorecard.insightsScorecard.persistedComments;
    },

    scorecard() {
      return this.scorecards.find(s => s.datasetId === this.datasetBenchmark);
    },

    showLoading() {
      return !this.hasSearch && this.selectableComments.length === 0;
    },
  },

  async mounted() {
    const meta = await snippetApi.fetchSnippetsNoSet(this.datasetBenchmark, {
      topicId: this.commentTheme.id,
      page: 0,
      size: 50,
    });

    this.commentsMeta = meta;

    this.setSelectableComments({ comments: meta.snippets });
  },

  destroyed() {
    this.setCommentSelection({ comment: null });
    this.setCommentTheme({ theme: null });
    this.setSelectableComments({ comments: [] });
  },

  methods: {
    ...mapActions('datasetsInsights', [
      'setCommentSelection',
      'setCommentTheme',
      'setSelectableComments',
      'updateScorecard',
    ]),

    ...mapActions('modal', ['closeModal']),

    checkVisible() {
      return this.getLoaderRect().top - this.getRect().top < this.getRect().height;
    },

    getLoaderRect() {
      return this.$refs.loader.$el.getBoundingClientRect();
    },

    async fetchSnippets() {
      const meta = await snippetApi.fetchSnippetsNoSet(this.datasetBenchmark, {
        topicId: this.commentTheme.id,
        page: this.commentsMeta.page + 1,
        size: 50,
      });

      this.commentsMeta = meta;
      this.setSelectableComments({ comments: [...this.selectableComments, ...meta.snippets] });
    },

    getRect() {
      return this.$refs.list.getBoundingClientRect();
    },

    onClickCancel() {
      this.closeModal();
    },

    async onClickSelect() {
      let comments;

      if (this.persistedComments != null) {
        comments = this.persistedComments;

        const index = this.persistedComments.findIndex(p => p.themeId === this.commentTheme.id);

        if (index !== -1) {
          comments.splice(index, 1, {
            themeId: this.commentTheme.id,
            commentId: this.commentSelection.id,
            comment: this.commentSelection.content.trim(),
          });
        } else {
          comments.push({
            themeId: this.commentTheme.id,
            commentId: this.commentSelection.id,
            comment: this.commentSelection.content.trim(),
          });
        }
      } else {
        comments = [
          {
            themeId: this.commentTheme.id,
            commentId: this.commentSelection.id,
            comment: this.commentSelection.content.trim(),
          },
        ];
      }

      await datasetInsightApiV0.updateComments(
        this.datasetBenchmark,
        comments,
      );

      intercomEvent.send(intercomEvents.EDIT_INSIGHTS_THEME_COMMENT);

      const scorecard = await datasetInsightApiV0.getScorecard(this.datasetBenchmark);

      this.updateScorecard({ scorecard });

      this.closeModal();
    },

    onScroll: throttle(function throttleOnScroll() {
      if (this.hasMore) this.setLoaderVisible();
    }, 1000),

    setLoaderVisible() {
      this.loaderVisible = this.checkVisible();
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.illustrative-comment-modal {
  @include modal;

  .body {
    @include flex("block", "column", "start", "stretch");

    height: 600px;
    overflow: hidden;

    .loading-blocks-overlay {
      margin: 1rem 0;
    }
  }

  .footer {
    .base-button {
      padding: 0.5rem 1rem;
    }
  }
}
</style>
