<template>
  <section class="comments-summary">
    <comments-summary-header />
    <comments-summary-content />
    <comments-summary-collapse-btn class="collapse-btn" />
  </section>
</template>

<script>
import CommentsSummaryCollapseBtn from '@/components/CommentsSummary/CommentsSummaryCollapseBtn';
import CommentsSummaryContent from '@/components/CommentsSummary/CommentsSummaryContent';
import CommentsSummaryHeader from '@/components/CommentsSummary/CommentsSummaryHeader';

export default {
  name: 'comments-summary',

  components: {
    CommentsSummaryCollapseBtn,
    CommentsSummaryContent,
    CommentsSummaryHeader,
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.comments-summary {
  @include flex("block", "column", "start", "start");

  background: radial-gradient(36.01% 46.87% at 92.42% 5.77%, rgba(201, 153, 255, 0.4) 0%, rgba(255, 255, 255, 0.4) 100%);
  border-radius: 5px;
  border: 1.5px solid #8F4CDD;
  box-shadow: 0 0 7px 0 rgba(0, 0, 0, 0.35);
  grid-area: themes-summary;
  position: relative;
  width: 100%;

  .collapse-btn {
    bottom: -9px;
    left: calc(50% - 9px);
    position: absolute;
    z-index: 99;
  }
}
</style>
