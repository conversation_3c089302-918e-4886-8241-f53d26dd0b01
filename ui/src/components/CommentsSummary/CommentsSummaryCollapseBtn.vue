<template>
  <section class="comments-summary-collapse-btn" @click="onClick">
    <i class="fa-solid fa-chevrons-up icon" />
  </section>
</template>

<script>
import { mapActions } from 'vuex';

export default {
  name: 'comments-summary-collapse-btn',

  methods: {
    ...mapActions('layout', ['setShowThemeSummary']),

    onClick() {
      this.setShowThemeSummary({ value: false });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.comments-summary-collapse-btn {
  @include flex("block", "row", "center", "center");

  background-color: #281E51;
  border-radius: 50%;
  border: 1px solid #362C60;
  cursor: pointer;
  height: 18px;
  width: 18px;

  &:hover {
    background-color: #5B44B7;
  }

  .icon {
    color: #FFF;
    font-size: $font-size-xxs;
  }
}
</style>
