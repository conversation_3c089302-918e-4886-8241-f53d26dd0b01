<template>
  <section class="comments-summary-content">
    <loading-blocks-overlay v-if="loading" />
    <section v-show="!loading" class="content">
      <section class="title">Summary of ‘{{topicLabel}}’</section>
      <section class="text">{{summary}}</section>
      <section class="control-row">
        <section class="regenerate-btn" @click="onClickRegenerate">
          <section class="icon-wrapper">
            <i class="fa-regular fa-arrows-rotate icon-regenerate" />
          </section>
          <section class="btn-text-wrapper">
            <span class="btn-text">Regenerate</span>
            <span class="btn-text">Summary</span>
          </section>
        </section>
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import NetworkKeys from '@/enum/network-keys';
import ThemesRequest from '@/services/request/ThemesRequest';

export default {
  name: 'comments-summary-content',

  components: {
    LoadingBlocksOverlay,
  },

  data() {
    return {
      summary: null,
      topicLabel: null,
    };
  },

  computed: {
    ...mapGetters('network', ['isLoading']),

    ...mapState('themes', ['selectedSubtopic', 'selectedTheme']),

    loading() {
      return this.isLoading(NetworkKeys.THEMES_SUMMARY);
    },

    selectedId() {
      if (this.selected) return this.selected.id;
      return null;
    },

    selected() {
      if (this.selectedSubtopic) return this.selectedSubtopic;
      if (this.selectedTheme) return this.selectedTheme;
      return null;
    },
  },

  watch: {
    selectedId() {
      if (this.selectedId) {
        this.fetchSummary();
      } else {
        this.setShowThemeSummary({ value: false });
      }
    },
  },

  created() {
    this.fetchSummary();
  },

  methods: {
    ...mapActions('layout', ['setShowThemeSummary']),

    async fetchSummary() {
      const res = await ThemesRequest.fetchSummary(this.selected.id);
      this.topicLabel = this.selected.topicLabel;
      this.summary = res.summary;
    },

    async onClickRegenerate() {
      const res = await ThemesRequest.regenerateSummary(this.selected.id);
      this.summary = res.summary;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.comments-summary-content {
  @include flex("block", "column", "center", "center");

  width: 100%;

  .content {
    max-width: 480px;
    padding: 1.75rem 0 40px;
    width: 100%;
  }

  .title {
    font-size: $font-size-sm;
    font-weight: $font-weight-bold;
  }

  .text {
    font-size: $font-size-xs;
    font-weight: 350;
    line-height: 130%;
    margin-top: 0.4rem;
  }

  .control-row {
    @include flex("block", "row", "start", "center");
    margin-top: 30px;

    .regenerate-btn {
      @include flex("block", "row", "center", "center");
      border-radius: 15px;
      cursor: pointer;
      height: 25px;
      padding-right: 8px;

      &:hover {
        background-color: rgba(147, 74, 219, 0.1);

        .icon-wrapper {
          background-color: rgba(99, 67, 164, 1);

          .icon-regenerate {
            color: #FFFFFF;
          }
        }
      }

      .icon-wrapper {
        @include flex("block", "row", "center", "center");
        border: solid 1px rgba(62, 53, 98, 1);
        border-radius: 50%;
        height: 24px;
        width: 24px;

        .icon-regenerate {
          font-size: 12px;
          color: rgba(62, 53, 98, 1);
          padding-left: 1px;
        }
      }

      .btn-text-wrapper {
        @include flex("block", "column", "center", "start");
        margin-left: 5px;

        .btn-text {
          font-size: 10px;
          font-weight: 600;
        }
      }
    }
  }
}
</style>
