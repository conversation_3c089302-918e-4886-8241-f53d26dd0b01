<template>
  <section class="welcome-header" :class="{ expanded }">
    <section class="header-upper">
      <section v-if="isLimitedWorkspace" class="label">
        Signed in as
        <span class="label-email">{{ email }}</span>
      </section>
      <section v-else class="label">
        <span class="label-workspace">{{ workspaceLabel }}</span>
        / Signed in as
        <span class="label-email">{{ email }}</span>
      </section>
      <transition name="transition-opacity">
        <base-button v-if="!expanded && isAdmin && !isLimitedWorkspace" class="btn-invite" size="small" @click="onClickOpenModalInvite">
          <i class="fa-regular fa-users icon"></i> Invite Teammates
        </base-button>
      </transition>
      <transition name="transition-opacity">
        <!-- Hide this 'Get Started' btn for now: AD-604 -->
        <base-button v-if="showGuide && !expanded && false" class="btn-starter" size="small" @click="onClickChecklist">
          <i class="fa-solid fa-book icon"></i> Get Started
        </base-button>
      </transition>
      <transition name="transition-opacity">
        <base-button v-if="hasChangelog && !expanded" class="btn-changes" size="small" @click="onClickChanges">
          <i class="fa-solid fa-megaphone icon"></i> What's New?
          <section v-if="!hasViewedChangelog" class="ping">
            <section class="sonar"></section>
          </section>
        </base-button>
      </transition>
      <section class="btn-workspace-info-wrapper" :style="{visibility: isLimitedWorkspace ? 'hidden' : 'visible'}">
        <base-button class="btn-workspace-info" size="small" @click="onClickOpenModalInvite">
          <i class="fa-solid fa-users icon"></i> {{ numberWorkspaceUsers }} Workspace Users
          <i class="fa-solid fa-ellipsis-vertical icon-ellipsis"></i>
        </base-button>
      </section>
    </section>
    <p v-if="isTrial" class="trial-msg">
      You have
      <strong>{{ daysRemaining }}</strong>
      remaining of your trial -
      <router-link :to="'/account/subscriptions'">Upgrade Your Account Now!</router-link>
    </p>
    <transition name="transition-opacity">
      <section v-if="expanded" class="header-lower" :class="{ 'is-not-admin': !isAdmin || isLimitedWorkspace }">
        <section v-if="isAdmin && !isLimitedWorkspace" class="lower-btn lower-btn-invite">
          <i class="fa-regular fa-users icon"></i>
          <span>Invite teammates to this workspace</span>
          <section class="lower-btn-invite-action">
            <base-input :disabled="!isAdmin" v-model="inviteEmail" placeholder="Enter <EMAIL>" />
            <section class="lower-btn-invite-role"
                     :class="{ focus: inviteRoleFocus }"
                     @click="onClickInviteRole"
                     v-click-outside-handler="{
                       handler: 'onClickOutside',
                     }"
            >
              {{ textInvitingRole }}
              <i class="fa fa-caret-down dropdown-icon" />
              <workspace-invite-user-roles
                  v-if="inviteRoleFocus"
                  :invitingRole="invitingRole"
                  @onSelect="selectInvitingRole"
              />
            </section>
            <base-button
                class="btn-send-invite"
                :disabled="inviteEmail === '' || !emailValidator.constraintsPassed"
                @click="onClickInvite"
            >Invite</base-button>
          </section>
        </section>
        <section v-if="showGuide" class="lower-btn lower-btn-starter" @click="onClickChecklist">
          <i class="fa-regular fa-book icon"></i>
          <section class="lower-btn-starter-info">
            <span>Get Started</span>
            <span class="detail">A step-by-step guide on how get the most out of the platform</span>
          </section>
        </section>
        <section v-if="hasChangelog" class="lower-btn lower-btn-changes" @click="onClickChanges">
          <i class="fa-regular fa-megaphone icon"></i>
          <section class="lower-btn-changes-info">
            <span>What's new in the platform?</span>
            <span class="date">{{ currentChangelog.date }} - {{ currentChangelogText }}</span>
          </section>
          <section v-if="!hasViewedChangelog" class="ping">
            <section class="sonar"></section>
          </section>
        </section>
      </section>
    </transition>
    <section v-if="showGuide" class="expand-icon" @click="onClickExpand">
      <i class="fa-solid fa-angles-up expand-icon-img" :class="{ expanded }"></i>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import changelog from '@/changelog.json';
import ChangelogModal from '@/components/ChangelogModal/ChangelogModal';
import clickOutsideHandler from '@/directives/click-outside-handler';
import EmailValidator from '@/services/EmailValidator';
import StarterModal from '@/components/StarterModal/StarterModal';
import SubscriptionTier from '@/enum/subscription-tier';
import WorkspaceErrorToast from '@/components/Toasts/WorkspaceErrorToast';
import WorkspaceInviteUserRole from '@/enum/workspace-invite-user-role';
import WorkspaceInviteUserRoles from '@/components/WorkspaceUsersManagement/WorkspaceInviteUserRoles';
import WorkspaceInviteUserToast from '@/components/Toasts/WorkspaceInviteUserToast';
import WorkspaceUsersManagementModal from '@/components/WorkspaceUsersManagement/WorkspaceUsersManagementModal';

import { workspaceApi } from '@/services/api';

export default {
  name: 'welcome-header',

  components: {
    BaseButton,
    BaseInput,
    WorkspaceInviteUserRoles,
  },

  directives: {
    clickOutsideHandler,
  },

  props: {
    showGuide: {
      type: Boolean,
      default: true,
    },
  },

  data() {
    return {
      expanded: false,
      inviteEmail: '',
      inviteRoleFocus: false,
      invitingRole: WorkspaceInviteUserRole.VIEWER,
    };
  },

  computed: {
    ...mapState(['changelogViewed']),

    ...mapState('datasets', ['view']),

    ...mapState('user', ['user']),

    ...mapGetters('user', [
      'extractTier',
      'isAdmin',
      'isLimitedWorkspace',
    ]),

    ...mapState('user', ['user', 'activeWorkspace']),

    ...mapGetters('usage', ['cycleDaysRemaining']),

    currentChangelog() {
      if (!this.hasChangelog) return {};

      return changelog[0];
    },

    currentChangelogText() {
      if (!this.hasChangelog) return '';

      const text = changelog[0].changes.improved ? changelog[0].changes.improved[0] : changelog[0].changes.fixed[0];

      return `${text.substring(0, 20)}...`;
    },

    daysRemaining() {
      if (this.cycleDaysRemaining == null) return '...';

      const days = Math.floor(this.cycleDaysRemaining);

      if (Math.floor(this.cycleDaysRemaining) === 1) return '1 day';

      if (Math.floor(this.cycleDaysRemaining) <= 0) return '0 days';

      return `${days} days`;
    },

    email() {
      return this.user?.email || '';
    },

    hasChangelog() {
      return changelog.length > 0;
    },

    hasViewedChangelog() {
      return this.changelogViewed === this.currentChangelog.id.toString();
    },

    isTrial() {
      return this.extractTier === SubscriptionTier.TRIAL;
    },

    name() {
      return this.user?.firstName || '';
    },

    numberWorkspaceUsers() {
      return this.activeWorkspace?.numberUsers || 1;
    },

    emailValidator() {
      return new EmailValidator(this.inviteEmail);
    },

    textInvitingRole() {
      return this.invitingRole?.titleCase() || '';
    },

    workspaceLabel() {
      return this.activeWorkspace?.label || 'Your workspace';
    },
  },

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    ...mapActions('toast', ['add']),

    ...mapActions('user', ['setActiveWorkspace']),

    ...mapActions('workspaces', ['setEmailToInvite', 'setSelectedWorkspace']),

    onClickChanges() {
      this.setModalComponent({ component: ChangelogModal });
    },

    onClickChecklist() {
      this.setModalComponent({ component: StarterModal });
    },

    onClickExpand() {
      this.expanded = !this.expanded;
    },

    async onClickInvite() {
      this.setEmailToInvite({ email: this.inviteEmail });
      const response = await workspaceApi.inviteUser(this.activeWorkspace.id, this.inviteEmail, this.invitingRole.name);

      if (response.error) {
        this.add({
          toast: {
            component: WorkspaceErrorToast,
            id: 'workspace-error-toast',
          },
        });

        return;
      }

      this.add({
        toast: {
          component: WorkspaceInviteUserToast,
          id: 'workspace-invite-new-user',
        },
      });
      this.inviteEmail = '';
      this.invitingRole = WorkspaceInviteUserRole.VIEWER;
      const workspace = await workspaceApi.getWorkspace(this.activeWorkspace.id);
      this.setActiveWorkspace({ workspace });
    },

    onClickInviteRole() {
      this.inviteRoleFocus = !this.inviteRoleFocus;
    },

    onClickOpenModalInvite() {
      this.setSelectedWorkspace({ workspace: this.activeWorkspace });
      this.setModalComponent({ component: WorkspaceUsersManagementModal });
    },

    onClickOutside() {
      this.inviteRoleFocus = false;
    },

    selectInvitingRole(role) {
      this.invitingRole = role;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

@keyframes pulse {
  0% {
    opacity: 0.8;
    transform: scale(1);
  }

  100% {
    opacity: 0;
    transform: scale(3);
  }
}

.welcome-header {
  @include flex("block", "column", "start", "stretch");
  @include rigid;

  background-color: #EFF1FF;
  height: auto;
  padding: 1.4rem 2.25rem 1.4rem 2.25rem;
  position: relative;

  &.expanded {
    max-height: 11rem;
    transition: max-height $interaction-transition-time ease-in;
  }

  .base-button {
    border-radius: 2px;
    font-size: 0.65rem;
    font-weight: 600;
    line-height: 1rem;
    margin-left: 0.4rem;
    padding: 0.3rem 0.8rem;
    text-transform: uppercase;

    .icon {
      margin-right: 0.4rem;
    }

    &.btn-changes {
      background-color: transparent;
      border: 1px solid rgba(75, 114, 240, 0.15);
      color: clr('purple', 'rich');
      transition: all $interaction-transition-time;

      &:hover, &:active, &:focus {
        border-color: rgba(75, 114, 240, 0.8);
      }
    }

    &.btn-starter {
      background: #0CBB3D;
      border: 1px solid #0CBB3D;
      color: clr('white');

      &:hover, &:active, &:focus {
        background: #08952F;
        border-color: #058328;
      }
    }

    &.btn-invite {
      background-color: #5F52C5;
      border: 1px solid #5F52C5;

      &:hover, &:active, &:focus {
        background-color: #5143BC;
        border-color: #3D2FAA;
      }
    }

    &.btn-workspace-info {
      background-color: rgba(255, 255, 255, 0.5);
      border: 1px solid rgba(75, 114, 240, 0.15);
      color: clr('purple', 'rich');

      &:hover, &:active, &:focus {
        border-color: rgba(75, 114, 240, 0.8);
      }
    }

    &.btn-send-invite {
      background-color: clr('purple', 'rich');
      border: $border-light solid rgba(42, 0, 60, 0.17);
      border-left: none;
      border-radius: 0 $border-radius-small $border-radius-small 0;
      height: 100%;
      margin-left: 0;

      &:hover, &:active, &:focus {
        opacity: 0.9;
      }
    }

    &.disabled-true {
      cursor: not-allowed;
      opacity: 1;
    }
  }

  .base-input {
    border: $border-light solid rgba(42, 0, 60, 0.17);
    border-radius: $border-radius-small 0 0 $border-radius-small;
    border-right: none;
    height: 100%;
    font-weight: 400;

    &::placeholder {
      color: rgba(0, 0, 0, 0.29);
    }
  }

  .btn-workspace-info-wrapper {
    margin-left: auto;
  }

  .expand-icon {
    background-color: #5F52C5;
    border-radius: 50%;
    bottom: -0.5rem;
    cursor: pointer;
    height: 1.3rem;
    left: 50%;
    position: absolute;
    width: 1.3rem;

    .expand-icon-img {
      @include flex("block", "row", "center", "center");

      color: clr('white');
      font-size: $font-size-xxs;
      height: 100%;
      transform: rotate(180deg);
      width: 100%;

      &.expanded {
        transform: rotate(0deg);
      }
    }

    &:hover {
      background-color: clr('purple', 'rich');
    }
  }

  .fa-solid {
    &.icon-ellipsis {
      margin-left: 0.4rem;
    }
  }

  .header-upper {
    @include flex("block", "row", "start", "center");
  }

  .trial-msg {
    margin: 0.5rem 0 0.5rem 0;
  }

  .header-lower {
    @include flex("block", "row", "between", "center");

    margin-top: 0.6rem;

    &.is-not-admin {
      width: 66%;

      .lower-btn {
        &.lower-btn-changes, &.lower-btn-starter {
          width: 49%;
        }
      }
    }

    .lower-btn {
      border-radius: 2px;
      cursor: pointer;
      font-size: 0.65rem;
      font-weight: 600;
      height: 4.3rem;
      line-height: 1rem;
      padding: 0.3rem 0.8rem;

      &.lower-btn-changes {
        @include flex("block", "row", "start", "center");

        background-color: transparent;
        border: 1px solid rgba(75, 114, 240, 0.15);
        color: clr('purple', 'rich');
        transition: all $interaction-transition-time;
        width: 30%;

        &:hover, &:active, &:focus {
          border-color: rgba(75, 114, 240, 0.8);
        }

        .lower-btn-changes-info {
          @include flex("block", "column", "start", "start");

          .date {
            font-weight: 400;
            padding: 0.2rem 0;
          }
        }
      }

      &.lower-btn-invite {
        align-items: center;
        background-color: #5F52C5;
        border: 1px solid #5F52C5;
        color: clr('white');
        cursor: default;
        display: grid;
        grid-template-columns: auto 8rem 1fr;
        width: 38%;

        .lower-btn-invite-action {
          @include flex("block", "row", "start", "center");

          height: 1.6rem;
        }

        .lower-btn-invite-role {
          @include flex('block', 'row', 'center', 'center');

          border: 1px solid rgba(42, 0, 60, 0.17);
          border-left: 1px solid rgba(95, 82, 197, 0.2);
          background-color: clr('white');
          color: #2D1757;
          cursor: pointer;
          font-size: $font-size-xxs;
          font-weight: $font-weight-bold;
          height: 100%;
          padding: 0 0.6rem;
          position: relative;

          .dropdown-icon {
            font-size: $font-size-xs;
            margin-left: 0.4rem;
            transition: transform $interaction-transition-time;
          }

          &.focus {
            .dropdown-icon {
              transform: rotateX(180deg);
            }
          }
        }
      }

      &.lower-btn-starter {
        @include flex("block", "row", "start", "center");

        background: #0CBB3D;
        border: 1px solid #0CBB3D;
        color: clr('white');
        width: 30%;

        &:hover, &:active, &:focus {
          background: #08952F;
          border-color: #058328;
        }

        .lower-btn-starter-info {
          @include flex("block", "column", "start", "start");

          .detail {
            font-weight: 400;
            padding: 0.2rem 0;
          }
        }
      }

      .icon {
        font-size: $font-size-base;
        margin-left: 0.2rem;
        margin-right: 1rem;
      }
    }
  }

  .label {
    font-size: 0.7rem;
    margin-right: 0.6rem;

    .label-workspace {
      font-weight: $font-weight-bold;
    }

    .label-email {
      font-weight: $font-weight-bold;
      text-decoration: underline;
    }
  }

  .ping {
    background-color: lighten(clr('blue'), 20%);
    border-radius: 0.25rem;
    display: inline-block;
    height: 0.5rem;
    right: 0;
    position: relative;
    margin-left: 0.6rem;
    width: 0.5rem;

    .sonar {
      animation: pulse 1s ease-out infinite;
      background-color: lighten(clr('blue'), 20%);
      border-radius: 0.25rem;
      height: 100%;
      width: 100%;
    }
  }

  .transition-opacity-enter-active,
  .transition-opacity-leave-active {
    transition: opacity $interaction-transition-time;
  }

  .transition-opacity-enter,
  .transition-opacity-leave-to {
    opacity: 0;
  }

  p {
    font-size: 0.8rem;
    a {
      font-weight: bold;
      color: $helper-txt-hvr;;
    }
  }
}

</style>

<style lang="scss">
.welcome-header {
  .workspace-invite-user-roles {
    position: absolute;
    right: 0;
    top: 28px;

    .role-item {
      font-size: 10px;
      padding: 0.3rem 0.8rem;
    }
  }
}
</style>
