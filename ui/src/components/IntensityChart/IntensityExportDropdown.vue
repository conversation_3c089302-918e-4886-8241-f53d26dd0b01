<template>
  <section class="intensity-export-dropdown">
    <h4>Image Scale (for PNG)</h4>
    <section class="option">
      <vue-slider v-model="scale" v-bind="sliderScale" />
    </section>

    <span
      class="help"
    >Use the chart options menu to configure the look of your charts before exporting.</span>

    <base-button :disabled="disabled" size="small" @click="onClickExport('png')">
      <span>Export as PNG</span>
    </base-button>

    <base-button :disabled="disabled" size="small" @click="onClickExport('svg')">
      <span>Export as SVG</span>
    </base-button>
  </section>
</template>

<script>
import VueSlider from 'vue-slider-component';

import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BlurCloseable from '@/components/Mixins/BlurCloseable';
import sliderDefaultStyles from '@/helpers/slider-default-styles';

export default {
  name: 'intensity-export-dropdown',

  components: {
    BaseButton,
    VueSlider,
  },

  mixins: [BlurCloseable],

  data() {
    return {
      creating: false,
    };
  },

  computed: {
    ...mapState('chartOptions', ['imageScale']),

    disabled() {
      return this.creating;
    },

    scale: {
      get() {
        return this.imageScale;
      },

      set(value) {
        this.setImageScale({ scale: value });
      },
    },

    sliderBase() {
      return {
        ...sliderDefaultStyles,
        width: '150px',
      };
    },

    sliderScale() {
      return {
        ...this.sliderBase,
        steps: [
          1,
          2,
          3,
          4,
          5,
        ],
        max: 5,
        min: 0.5,
        tooltipFormatter: '{value}x',
        interval: 0.5,
      };
    },
  },

  methods: {
    ...mapActions('chartOptions', ['createImage', 'setImageScale']),

    async onClickExport(type) {
      this.creating = true;

      await this.$nextTick(async () => {
        await this.createImage({ elementId: 'intensity-chart', name: 'intensity', type });
        this.creating = false;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.intensity-export-dropdown {
  @include flex("block", "column", "start", "stretch");
  @include rigid;
  @include panel;

  font-size: $font-size-xs;
  padding: 0.3rem 0.8rem 0.5rem;
  position: absolute;
  right: 0;
  top: 1.5rem;
  z-index: 99;

  .image-size {
    font-size: $font-size-xxs;
    margin: 0.2rem 0 0.5rem;
  }

  h4 {
    color: $body-copy-light;
    font-size: $font-size-xxs;
    letter-spacing: $letter-spacing-sm;
    margin: 1em 0;
    text-transform: uppercase;
  }

  .option {
    @include flex("block", "row", "start", "center");

    cursor: pointer;
    margin: 0 0.5rem 0.5rem 0.5rem;
    width: 150px;
    position: relative;
  }

  .help {
    font-size: $font-size-xxs;
    margin-top: 0.5rem;
  }

  .base-button {
    margin-top: 1rem;

    .icon {
      height: $font-size-sm;
      width: $font-size-sm;
    }
  }
}
</style>
