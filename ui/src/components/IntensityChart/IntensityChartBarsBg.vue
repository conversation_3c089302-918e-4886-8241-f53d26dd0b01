<template>
  <rect
    class="intensity-chart-bars-bg"
    :x="xBandBg(dataset)"
    :y="0"
    :height="chartHeight"
    :width="xBandBg.bandwidth()"
    fill="transparent"
    @click="setRangeFromIndex(bucket)"
  ></rect>
</template>

<script>
import { scaleBand } from 'd3';
import { mapActions, mapGetters, mapState } from 'vuex';
import { themesRequest } from '@/services/request';

export default {
  name: 'intensity-chart-bars-bg',

  props: {
    bucket: {
      type: Number,
      required: true,
    },
    chartHeight: {
      type: Number,
      required: true,
    },
    dataset: {
      type: Number,
      required: true,
    },
    width: {
      type: Number,
      required: true,
    },
  },

  computed: {
    ...mapState('themes', ['bucketCount']),

    ...mapState('datasets', ['active']),

    ...mapGetters('datasets', ['selectedLimited']),

    xBandBg() {
      return scaleBand()
        .domain(this.selectedLimited)
        .padding(0.2)
        .rangeRound([0, this.width]);
    },
  },

  methods: {
    ...mapActions('datasets', ['setActive']),

    ...mapActions('themes', [
      'selectFirstTheme',
      'setRange',
      'setThemes',
    ]),

    getIndexRange(bucket) {
      const lower = -100 + 200 / this.bucketCount * (bucket - 1);
      const upper = -100 + 200 / this.bucketCount * bucket;

      return { lower, upper };
    },

    async setRangeFromIndex(bucket) {
      const range = this.getIndexRange(bucket);

      this.setRange({ range });

      if (this.active !== this.dataset) {
        this.setActive({ id: this.dataset });
      } else {
        const themes = await themesRequest.fetchThemes();

        this.setThemes({ themes });
        this.selectFirstTheme();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.intensity-chart-bars-bg {
  cursor: pointer;

  &:hover {
    fill: rgba(clr("purple"), 0.2);
  }
}
</style>
