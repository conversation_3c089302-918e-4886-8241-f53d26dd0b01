<template>
  <rect class="intensity-chart-bars-fg"
    :x="xBar(dataset)"
    :y="chartHeight - y(range(bucket))"
    :width="xBar.bandwidth()"
    :height="y(range(bucket))"
    :fill="colour(dataset)"
  ></rect>
</template>

<script>
import { scaleBand, scaleLinear } from 'd3';
import { mapGetters, mapState } from 'vuex';

export default {
  name: 'intensity-chart-bars-fg',

  props: {
    bucket: {
      type: Number,
      required: true,
    },
    chartHeight: {
      type: Number,
      required: true,
    },
    dataset: {
      type: Number,
      required: true,
    },
    width: {
      type: Number,
      required: true,
    },
  },

  computed: {
    ...mapState('themes', {
      bucketCount: state => state.bucketCount,
    }),

    ...mapGetters('datasets', ['colour', 'selectedLimited']),

    ...mapGetters('themes', ['getBucketThemesCount', 'maxBucketThemesCount']),

    xBar() {
      return scaleBand()
        .domain(this.selectedLimited)
        .padding(0.2)
        .rangeRound([0, this.width]);
    },

    y() {
      return scaleLinear()
        .range([0, this.chartHeight])
        .domain([0, this.maxBucketThemesCount]);
    },
  },

  methods: {
    range(bucket) {
      return this.getBucketThemesCount(this.dataset, bucket);
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.intensity-chart-bars-fg {
}
</style>
