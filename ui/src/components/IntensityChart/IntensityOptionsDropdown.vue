<template>
  <section class="intensity-options-dropdown">
    <h4>Score Buckets</h4>
    <section class="option">
      <vue-slider ref="slider"
        v-bind="sliderOptions"
        v-model="count"
        class="slider"
        @drag-end="onDragEnd"
      ></vue-slider>
    </section>

    <h4>Toggle Bar/Line</h4>
    <section class="option" @click="onClickBarToggle">
      <base-checkbox :value="intensityBarToggle"></base-checkbox>
      <span>Bar</span>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import VueSlider from 'vue-slider-component';

import BaseCheckbox from '@/components/Base/BaseCheckbox';
import BlurCloseable from '@/components/Mixins/BlurCloseable';
import sliderDefaultStyles from '@/helpers/slider-default-styles';
import { themeApi } from '@/services/api';

export default {
  name: 'intensity-options-dropdown',

  components: {
    BaseCheckbox,
    VueSlider,
  },

  mixins: [BlurCloseable],

  computed: {
    ...mapGetters('datasets', ['selectedLimited']),

    ...mapState('chartOptions', {
      intensityBarToggle: state => state.intensityBarToggle,
    }),

    ...mapState('themes', {
      bucketCount: state => state.bucketCount,
    }),

    count: {
      get() {
        return this.bucketCount == null ? 6 : this.bucketCount;
      },

      set(count) {
        this.setBucketCount({ count });
      },
    },

    sliderOptions() {
      return {
        interval: 2,
        min: 2,
        max: 20,
        piecewise: true,
        width: 200,
        ...sliderDefaultStyles,
      };
    },
  },

  methods: {
    ...mapActions('chartOptions', ['setIntensityBarToggle']),

    ...mapActions('themes', ['setBucketCount']),

    onClickBarToggle() {
      this.setIntensityBarToggle({ toggle: !this.intensityBarToggle });
    },

    async onDragEnd() {
      await themeApi.fetchIntensityCounts(this.selectedLimited, this.bucketCount);
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.intensity-options-dropdown {
  @include flex('block', 'column', 'start', 'stretch');
  @include rigid;
  @include panel;

  font-size: $font-size-xs;
  padding: 0.3rem 0.8rem 0.5rem;
  position: absolute;
  right: 0;
  top: 1.5rem;
  z-index: 99;

  h4 {
    color: $body-copy-light;
    font-size: $font-size-xxs;
    letter-spacing: $letter-spacing-sm;
    margin: 1em 0;
    text-transform: uppercase;
  }

  .option {
    @include flex('block', 'row', 'start', 'center');

    cursor: pointer;
    margin: 0 0.5rem 0.5rem 0.5rem;
    width: 200px;
    position: relative;

    .base-checkbox {
      margin-right: 0.5em;
      pointer-events: none;
    }

    span {
      font-size: $font-size-xs;
    }
  }
}
</style>
