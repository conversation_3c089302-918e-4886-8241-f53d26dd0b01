<template>
  <g class="intensity-chart-lines">
    <path v-for="dataset in selectedLimited" :key="`line-${dataset}`"
      :d="line(lineData(dataset))"
      pointer-events="none"
      :stroke="colour(dataset)"
      stroke-linecap="round"
      :stroke-width="3"
      fill="none"
    ></path>
  </g>
</template>

<script>
import {
  curveBundle, line, scaleLinear, scalePoint,
} from 'd3';
import { mapGetters, mapState } from 'vuex';

export default {
  name: 'intensity-chart-lines',

  props: {
    chartHeight: {
      type: Number,
      required: true,
    },
    width: {
      type: Number,
      required: true,
    },
  },

  computed: {
    ...mapState('themes', {
      bucketCount: state => state.bucketCount,
    }),

    ...mapGetters('datasets', ['colour', 'selectedLimited']),

    ...mapGetters('themes', ['getBucketThemesCount', 'maxBucketThemesCount']),

    line() {
      return line()
        .x((d, i) => this.xPoint(i))
        .y(d => this.chartHeight - this.y(d))
        .curve(curveBundle.beta(1));
    },

    xPoint() {
      return scalePoint()
        .domain([...Array(this.bucketCount).keys()])
        .padding(0.6)
        .rangeRound([0, this.width]);
    },

    y() {
      return scaleLinear()
        .range([0, this.chartHeight])
        .domain([0, this.maxBucketThemesCount]);
    },
  },

  methods: {
    lineData(dataset) {
      return [...Array(this.bucketCount).keys()].map(index => this.range(dataset, index + 1));
    },

    range(dataset, bucket) {
      return this.getBucketThemesCount(dataset, bucket);
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.intensity-chart-lines {
}
</style>
