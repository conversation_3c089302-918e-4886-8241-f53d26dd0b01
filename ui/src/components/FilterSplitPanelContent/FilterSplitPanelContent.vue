<template>
  <section class="filter-split-panel-content">
    <filter-split-panel-content-item-header class="element" />
    <filter-split-panel-content-item-select-all class="element"/>
    <filter-split-panel-content-list class="element" />
    <filter-split-panel-content-action v-if="splitSelectedViewIds.length > 0" class="element" />
  </section>
</template>

<script>
import { mapState } from 'vuex';

import FilterSplitPanelContentAction from '@/components/FilterSplitPanelContent/FilterSplitPanelContentAction';
import FilterSplitPanelContentItemHeader from '@/components/FilterSplitPanelContent/FilterSplitPanelContentItemHeader';
import FilterSplitPanelContentItemSelectAll from '@/components/FilterSplitPanelContent/FilterSplitPanelContentItemSelectAll';
import FilterSplitPanelContentList from '@/components/FilterSplitPanelContent/FilterSplitPanelContentList';

export default {
  name: 'filter-split-panel-content',

  components: {
    FilterSplitPanelContentAction,
    FilterSplitPanelContentItemHeader,
    FilterSplitPanelContentItemSelectAll,
    FilterSplitPanelContentList,
  },

  computed: {
    ...mapState('snippetsFilter', ['splitSelectedViewIds']),
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.filter-split-panel-content {
  @include flex("block", "column", "start", "start");

  font-size: $font-size-xxs;
  padding-top: 1.5rem;
  width: 100%;

  .element {
    margin-bottom: 0.8rem;
  }
}
</style>
