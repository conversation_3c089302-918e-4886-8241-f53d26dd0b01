<template>
  <section class="filter-split-panel-content-list">
    <filter-split-panel-content-item v-for="(item, index) in sortedFilterViews" :item="item" :index="index" :key="index" />
  </section>
</template>

<script>
import { mapState } from 'vuex';

import FilterSplitPanelContentItem from '@/components/FilterSplitPanelContent/FilterSplitPanelContentItem';

export default {
  name: 'filter-split-panel-content-list',

  components: {
    FilterSplitPanelContentItem,
  },

  computed: {
    ...mapState('snippetsFilter', ['splitFilterViews']),

    sortedFilterViews() {
      return this.splitFilterViews.sort((a, b) => a.metadataFilterList.label.localeCompare(b.metadataFilterList.label));
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.filter-split-panel-content-list {
  @include flex("block", "column", "start", "center");

  width: 100%;
}
</style>
