<template>
  <section class="filter-split-panel-content-item" @click="onClickItem">
    <section class="item-content-wrapper" :class="{isSelected, isClicked}">
      <section class="item-content" :class="{last: splitFilterViews.length === index + 1}">
        <section class="checkbox" @click.stop>
          <base-checkbox-solid :value="isSelected" @input="onSelectItem" />
        </section>
        <span class="name">{{splitLabel}}</span>
        <span class="vol">{{splitDataset.documentCount}}</span>
        <adorescore-box-mini v-if="splitDataset.documentCount" class="score" :bucket="bucket" :score="adorescore"/>
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import AdorescoreBoxMini from '@/components/AdorescoreBox/AdorescoreBoxMini';
import BaseCheckboxSolid from '@/components/Base/BaseCheckboxSolid';

import { metadataRequest, themesRequest } from '@/services/request';

export default {
  name: 'filter-split-panel-content-item',

  components: {
    AdorescoreBoxMini,
    BaseCheckboxSolid,
  },

  props: {
    index: {
      type: Number,
      required: true,
    },
    item: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapGetters('benchmark', ['classifyAdorescore']),

    ...mapState('snippetsFilter', [
      'splitClickedViewId',
      'splitFilterViews',
      'splitSelectedViewIds',
    ]),

    adorescore() {
      return Math.round(this.splitDataset.summary.polarity * 100);
    },

    bucket() {
      return this.classifyAdorescore(this.adorescore);
    },

    isClicked() {
      return this.splitClickedViewId === this.item.dataset.id;
    },

    isSelected() {
      return this.splitSelectedViewIds.includes(this.item.dataset.id);
    },

    splitDataset() {
      return this.item.dataset;
    },

    splitLabel() {
      return this.item.metadataFilterList.label;
    },
  },

  methods: {
    ...mapActions('datasets', ['setActiveViewing']),

    ...mapActions('snippets', ['deselectAllSnippets', 'resetSnippets']),

    ...mapActions('snippetsFilter', [
      'addSplitSelectedViewId',
      'removeSplitSelectedViewId',
      'setSplitClickedViewId',
    ]),

    async onClickItem() {
      this.deselectAllSnippets();

      if (this.isClicked) {
        this.setSplitClickedViewId({ datasetId: null });
      } else {
        this.setSplitClickedViewId({ datasetId: this.splitDataset.id });
        this.resetSnippets();
        this.setActiveViewing({ id: this.splitDataset.id });

        await themesRequest.fetchAndSetThemes();
        await metadataRequest.filterCommentsOnMetadata();
        await metadataRequest.filterCommentsCountOnMetadata();
      }
    },

    onSelectItem() {
      if (this.isSelected) this.removeSplitSelectedViewId({ datasetId: this.item.dataset.id });
      else this.addSplitSelectedViewId({ datasetId: this.item.dataset.id });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.filter-split-panel-content-item {
  @include flex("block", "row", "start", "center");

  padding-left: 1rem;
  padding-right: 1rem;
  width: 100%;

  .item-content-wrapper {
    border-radius: 3px;
    border: $border-light solid transparent;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    width: 100%;

    &:hover {
      background-color: rgba(57, 47, 101, 0.5);
      border: $border-light solid rgba(19, 28, 41,  0.5);

      .item-content {
        border-bottom: $border-light solid transparent;
      }
    }

    &.isClicked {
      background-color: rgba(57, 47, 101, 1);
      border: $border-light solid $nps-blue;

      .item-content {
        border-bottom: $border-light solid transparent;
      }
    }

    .item-content {
      @include flex("block", "row", "start", "center");

      border-bottom: $border-light solid rgba(255, 255, 255, 0.2);
      cursor: pointer;
      display: grid;
      grid-template-columns: 10% 50% 26% 14%;
      padding-bottom: 0.5rem;
      padding-top: 0.5rem;
      width: 100%;

      &.last {
        border-bottom: unset;
      }
    }
  }

  &:last-child {
    border-bottom: unset;
  }

  .name {
    @include truncate;
  }
}
</style>
