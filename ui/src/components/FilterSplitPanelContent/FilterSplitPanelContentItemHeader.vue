<template>
  <section class="filter-split-panel-content-item-header">
    <span class="header empty"></span>
    <span class="header name">Name</span>
    <span class="header vol">Vol</span>
    <span class="header score">Score</span>
  </section>
</template>

<script>
export default {
  name: 'filter-split-panel-content-item-header',
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.filter-split-panel-content-item-header {
  @include flex("block", "row", "start", "center");

  display: grid;
  grid-template-columns: 10% 50% 25% 15%;
  opacity: 0.7;
  padding: 0 1.5rem;
  text-transform: uppercase;
  width: 100%;
}
</style>
