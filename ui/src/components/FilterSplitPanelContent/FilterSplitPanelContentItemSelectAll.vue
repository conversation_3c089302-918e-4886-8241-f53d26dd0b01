<template>
  <section class="filter-split-panel-content-item-select-all">
    <base-checkbox-solid :value="isSelectedAll" @input="onClickSelectAll" />
    <span class="select-all" @click="onClickSelectAll">
      Select All - {{splitFilterViews.length}} '{{splitMetadataType.name}}' Splits
    </span>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseCheckboxSolid from '@/components/Base/BaseCheckboxSolid';

export default {
  name: 'filter-split-panel-content-item-select-all',

  components: {
    BaseCheckboxSolid,
  },

  computed: {
    ...mapState('snippetsFilter', [
      'splitFilterViews',
      'splitMetadataType',
      'splitSelectedViewIds',
    ]),

    isSelectedAll() {
      return this.splitFilterViews.length === this.splitSelectedViewIds.length;
    },
  },

  methods: {
    ...mapActions('snippetsFilter', ['addSplitSelectedViewId', 'removeSplitSelectedViewId']),

    onClickSelectAll() {
      if (this.isSelectedAll) {
        this.splitSelectedViewIds.forEach(datasetId => this.removeSplitSelectedViewId({ datasetId }));
      } else {
        this.splitFilterViews.forEach(filterView => {
          this.addSplitSelectedViewId({ datasetId: filterView.dataset.id });
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.filter-split-panel-content-item-select-all {
  @include flex("block", "row", "start", "center");

  display: grid;
  grid-template-columns: 10% 1fr;
  padding: 0 1.5rem;
  width: 100%;

  .select-all {
    cursor: pointer;
    font-weight: $font-weight-bold;
    width: fit-content;
  }
}
</style>
