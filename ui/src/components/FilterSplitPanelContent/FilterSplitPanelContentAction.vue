<template>
  <section class="filter-split-panel-content-action">
    <base-button class="btn btn-clear" colour="light" size="small" @click="onClear">Clear</base-button>
    <section class="create-wrapper">
      <section class="btn btn-create" @click="open = !open" :class="{open}" v-click-outside-handler="{ handler: 'onCloseDropdown' }">
        <span class="text">Create</span>
        <i class="fa fa-caret-down icon-down"></i>
      </section>

      <filter-split-panel-content-create-dropdown v-if="open" @close="onCloseDropdown" />
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import clickOutsideHandler from '@/directives/click-outside-handler';
import FilterSplitPanelContentCreateDropdown from '@/components/FilterSplitPanelContent/FilterSplitPanelContentCreateDropdown';

export default {
  name: 'filter-split-panel-content-action',

  components: {
    BaseButton,
    FilterSplitPanelContentCreateDropdown,
  },

  directives: {
    clickOutsideHandler,
  },

  data() {
    return {
      open: false,
    };
  },

  computed: {
    ...mapState('snippetsFilter', ['splitFilterViews', 'splitSelectedViewIds']),
  },

  methods: {
    ...mapActions('snippetsFilter', [
      'removeSplitFilterView',
      'resetSplitFilterViews',
      'setShowSplitPanel',
    ]),

    onClear() {
      this.splitSelectedViewIds.forEach(datasetId => {
        this.removeSplitFilterView({ datasetId });
      });
      if (this.splitFilterViews.length === 0) {
        this.resetSplitFilterViews();
        this.setShowSplitPanel({ value: false });
      }
    },

    onCloseDropdown() {
      this.open = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.filter-split-panel-content-action {
  @include flex("block", "row", "space-between", "center");

  padding: 0 1.5rem;
  width: 100%;

  .create-wrapper {
    position: relative;
  }

  .btn {
    height: 26px;
    padding: 0.4rem 0.8rem;
    text-transform: uppercase;
    font-size: 11px;
    font-weight: $font-weight-bold;

    &.btn-clear {
      background-color: transparent;
      border: $border-light solid #FF837B;
      color: #FF837B;

      &:hover {
        background-color: #392E6A;
      }
    }

    &.btn-create {
      @include flex("block", "row", "start", "center");

      border-radius: 2px;
      border: 1px solid clr('white');
      color: clr('white');
      cursor: pointer;
      padding: 0.4rem 0.6rem;

      .icon-down {
        margin-left: 0.2rem;
      }

      &:hover, &.open {
        background-color: #333451;
      }
    }
  }
}
</style>
