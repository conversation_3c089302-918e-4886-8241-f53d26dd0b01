<template>
  <section class="journey-timeline">
    <section class="timeline-stages">
      <journey-timeline-stage
        v-for="stage in stageWindow"
        :key="stage.id"
        :active="isActive(stage)"
        v-bind="getStageProps(stage)"
        @add="onAdd"
        @select="onSelect(stage)"
      ></journey-timeline-stage>
    </section>
    <section class="labels-stages">
      <journey-timeline-label
        v-for="stage in stageWindow"
        :key="stage.id"
        :active="isActive(stage)"
        :stage="stage"
        @add="onAdd"
        @select="onSelect(stage)"
      ></journey-timeline-label>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import JourneyTimelineLabel from '@/components/JourneyTimeline/JourneyTimelineLabel';
import JourneyTimelineStage from '@/components/JourneyTimeline/JourneyTimelineStage';
import JourneyStageModalFilters from '@/components/JourneyStageModal/JourneyStageModalFilters';
import JourneyStageModalLabel from '@/components/JourneyStageModal/JourneyStageModalLabel';
import JourneyStageModalQuery from '@/components/JourneyStageModal/JourneyStageModalQuery';
import JourneyStageModalReview from '@/components/JourneyStageModal/JourneyStageModalReview';
import PagedModal from '@/components/PagedModal/PagedModal';

import { journeyApi } from '@/services/api';
import { journeyRequest } from '@/services/request';

const notPaddedStage = stage => stage != null && stage.id != null;

export default {
  name: 'journey-timeline',

  components: {
    JourneyTimelineLabel,
    JourneyTimelineStage,
    PagedModal,
  },

  computed: {
    ...mapState('datasets', ['active']),

    ...mapState('journey', [
      'maxStages',
      'navPosition',
      'selectedJourney',
      'selectedStage',
      'stages',
      'visibleStages',
    ]),

    paddedStages() {
      return new Array(this.maxStages)
        .fill({})
        .map((v, i) => this.getStage(i));
    },

    stagePages() {
      return [
        { name: 'New Stage: Stage Name', component: JourneyStageModalLabel },
        { name: 'New Stage: Search Query', component: JourneyStageModalQuery },
        { name: 'New Stage: Search Filters', component: JourneyStageModalFilters },
        { name: 'New Stage: Review Stage', component: JourneyStageModalReview },
      ];
    },

    stageWindow() {
      return this.paddedStages.slice(this.navPosition, this.navPosition + this.visibleStages);
    },
  },

  methods: {
    ...mapActions('journey', [
      'resetNewStage',
      'setSelectedStage',
      'setStages',
    ]),

    ...mapActions('modal', ['setModalComponent']),

    ...mapActions('pagedModal', [
      'setCurrentPage',
      'setDisabled',
      'setPages',
      'setSubmitFn',
      'setSubmitText',
    ]),

    ...mapActions('snippets', {
      resetSnippet: 'reset',
    }),

    ...mapActions('themes', ['selectTheme']),

    async fetchStages() {
      const stages = await journeyApi.fetchStages(this.active, this.selectedJourney.id);

      this.setStages({ stages });
    },

    getStage(index) {
      const stage = this.stages[index];

      return stage != null ? stage : {};
    },

    getStageProps(stage) {
      const index = this.paddedStages.indexOf(stage);
      const visIndex = this.stageWindow.indexOf(stage);

      return {
        index,
        stage,
        visIndex,
        hasData: notPaddedStage(stage),
        hasNext: notPaddedStage(this.stages[index + 1]),
        isEnd: index === this.maxStages - 1,
        isMiddle: index > 0 && index < this.maxStages - 1,
        isStart: index === 0,
        isFirstVisible: visIndex === 0,
        isLastVisible: visIndex === this.visibleStages - 1,
      };
    },

    isActive(stage) {
      return stage.id != null && this.selectedStage != null && stage.id === this.selectedStage.id;
    },

    notComplete(stage) {
      return stage?.summary?.adoreScore == null
        || stage?.exemplarTopicId == null
        || stage?.exemplarSnippetId == null;
    },

    onAdd() {
      this.resetNewStage();
      this.setPages({ pages: this.stagePages });
      this.setCurrentPage({ page: 0 });
      this.setDisabled({ disabled: true });
      this.setSubmitFn({ fn: this.onCreateStage });
      this.setSubmitText(({ text: 'Create Stage' }));
      this.setModalComponent({ component: PagedModal });
    },

    onSelect(stage) {
      if (this.selectedStage?.id === stage.id) return;

      this.selectTheme({ theme: null });
      this.setSelectedStage({ stage });
      this.resetSnippet();
    },

    async onCreateStage() {
      await journeyRequest.createStage();

      const stages = await journeyApi.fetchStages(this.active, this.selectedJourney.id);

      this.setStages({ stages });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.journey-timeline {
  @include flex("block", "column", "start", "stretch");

  .timeline-stages, .labels-stages {
    @include flex("block", "row", "start", "center");
    @include stretch;
  }

  .labels-stages {
    margin-top: 1rem;
  }
}
</style>
