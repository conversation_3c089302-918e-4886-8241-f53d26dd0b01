<template>
  <section class="journey-timeline-stage"
    :class="{
      active,
      'dashed-head': isLastAndHasNext,
      'dashed-tail': isFirstAndHasPrev,
      'has-add': hasAdd,
      'has-data': hasData,
      'has-next': hasNext,
      'show-head': showHead,
      'show-tail': showTail,
    }">
    <section class="line start-line"></section>
    <section class="stage" @click="$emit('select')">{{ index + 1 }}</section>
    <section class="line end-line"></section>
    <section class="add" v-if="hasAdd" @click="$emit('add')">
      <plus-icon class="icon"/>
    </section>
  </section>
</template>

<script>
import { PlusIcon } from 'vue-feather-icons';

export default {
  name: 'journey-timeline-stage',

  components: {
    PlusIcon,
  },

  props: {
    active: {
      type: Boolean,
      default: false,
    },

    hasData: {
      type: Boolean,
      default: false,
    },

    hasNext: {
      type: Boolean,
      default: false,
    },

    index: {
      type: Number,
      required: true,
    },

    isStart: {
      type: Boolean,
      default: false,
    },

    isMiddle: {
      type: Boolean,
      default: false,
    },

    isEnd: {
      type: Boolean,
      default: false,
    },

    isFirstVisible: {
      type: Boolean,
      default: false,
    },

    isLastVisible: {
      type: Boolean,
      default: false,
    },

    stage: {
      type: Object,
      default: () => {},
    },

    visIndex: {
      type: Number,
      required: true,
    },
  },

  computed: {
    isFirstAndHasPrev() {
      return this.isFirstVisible && this.isMiddle;
    },

    isLastAndHasNext() {
      return this.isLastVisible && this.hasNext;
    },

    hasAdd() {
      return this.hasData
        && !this.hasNext
        && this.isLastVisible
        && !this.isEnd;
    },

    noTopics() {
      return this.stage.exemplarTopicId == null;
    },

    showTail() {
      return !this.isStart;
    },

    showHead() {
      return this.isStart
        || (this.isMiddle && !this.isLastVisible)
        || this.hasAdd
        || this.isLastAndHasNext;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

$light-gray: lighten($body-copy-light, 10%);
$circle-d: 2.5rem;
$circle-r: calc(#{$circle-d} / 2);

.journey-timeline-stage {
  @include flex("block", "row", "start", "center");
  @include size-evenly;

  .line {
    @include stretch;
  }

  .stage {
    @include flex("block", "row", "center", "center");
    @include rigid;

    background-color: clr('white');
    border: 2px solid $light-gray;
    border-radius: $circle-r;
    color: $body-copy-light;
    font-size: $circle-r;
    font-weight: $font-weight-medium;
    height: $circle-d;
    padding-top: 0.1rem;
    transition: all $interaction-transition-time;
    user-select: none;
    width: $circle-d;
  }

  .add {
    @include flex("block", "row", "center", "center");
    @include rigid;

    background-color: clr('white');
    border: 2px solid clr('purple');
    border-radius: $circle-r;
    border-style: dashed;
    cursor: pointer;
    height: $circle-d;
    transition: all $interaction-transition-time;
    width: $circle-d;

    &:hover {
      background-color: lighten(clr('purple'), 25%);
    }

    .icon {
      color: clr('purple');
    }
  }

  &.has-data {
    .stage {
      border-color: clr('purple');
      color: clr('purple');
      cursor: pointer;

      &:hover {
        background-color: lighten(clr('purple'), 25%);
      }
    }

    &.has-next .end-line, &.show-tail .start-line {
      border-bottom: 2px solid clr('purple');
    }

    &.has-add .start-line {
      padding-left: $circle-d;
    }
  }

  &.show-head .end-line, &.show-tail .start-line {
    border-bottom: 2px solid $light-gray;
  }

  &.dashed-head .end-line, &.dashed-tail .start-line {
    border-bottom-style: dashed !important;
  }

  &.active {
    .stage {
      background-color: clr('purple');
      border-color: darken(clr('purple'), 15%);
      color: clr('white');
    }
  }
}
</style>
