<template>
  <section class="journey-timeline-label">
    <section v-if="stage.id != null" class="label">
      <h3>{{ stage.label }}</h3>
    </section>
    <section v-else-if="isEditor" class="add">
      <base-button size="small" @click="$emit('add')">Add Stage</base-button>
    </section>
  </section>
</template>

<script>
import { mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';

export default {
  name: 'journey-timeline-label',

  components: {
    BaseButton,
  },

  props: {
    active: {
      type: Boolean,
      default: false,
    },

    stage: {
      type: Object,
      default: () => {},
    },
  },

  computed: {
    ...mapGetters('datasets', ['isEditable']),

    ...mapState('datasets', {
      activeDatasetId: state => state.active,
    }),

    isEditor() {
      return this.isEditable(this.activeDatasetId);
    },

    noTopics() {
      return this.stage.exemplarTopicId == null;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.journey-timeline-label {
  @include flex("block", "row", "center", "center");
  @include size-evenly;

  .label {
    @include flex("block", "column", "center", "center");

    h3 {
      font-size: $font-size-md;
    }
  }
}
</style>
