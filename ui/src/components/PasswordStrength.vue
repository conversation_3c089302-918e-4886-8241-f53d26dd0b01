<template>
  <section class="password-strength">
    <p class="text">
      <!-- <span class="material-icons md-18 -icon" @mouseenter="showInfo = true" @mouseleave="showInfo = false">info_outline</span> -->
      <span class="-info">Password strength:</span>
      <span class="-strength" :style="{ color }">{{ text }}</span>
    </p>
    <section class="strength-bar">
      <section
        class="bar"
        :style="{
        backgroundColor: color,
        width: (strength + 1) * 20 + '%'
      }"
      ></section>
    </section>

    <section class="information" v-if="showInfo">If your password isn't strong enough, try...
      <ul>
        <li>Making your password longer</li>
        <li>Removing common words</li>
        <li>Replacing some letters with capitals</li>
        <li>Adding or replacing letters with numbers</li>
        <li>Adding special characters or punctuation marks</li>
      </ul>
    </section>
  </section>
</template>

<script>
export default {
  props: {
    strength: {
      type: Number,
      required: true,
    },
  },
  computed: {
    color() {
      return this.colours[this.strength];
    },
    text() {
      return this.texts[this.strength];
    },
  },
  data() {
    return {
      colours: [
        '#ff4343',
        '#FF6A22',
        '#FF9100',
        '#9AB40D',
        '#35d61a',
      ],
      showInfo: false,
      texts: [
        'Very Weak',
        'Weak',
        'OK',
        'Strong',
        'Very Strong',
      ],
    };
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";

.password-strength {
  font-size: $font-size-sm;
  font-weight: $font-weight-bold;
  margin-bottom: 1em;

  .text {
    align-items: center;
    display: flex;
    margin-bottom: 0.5em;

    .-icon {
      margin-right: 0.2em;

      &:hover {
        color: clr("purple");
        cursor: pointer;
      }
    }

    .-info {
      margin-right: 0.4em;
    }
  }

  .strength-bar {
    height: 4px;
    border-radius: $border-radius-rounded;
    width: 100%;
    overflow: hidden;
    background-color: clr("purple", "darker");

    .bar {
      height: 100%;

      transition: all $interaction-transition-time;
    }
  }

  .information {
    background-color: clr("white");
    border: 1px solid clr("purple");
    border-radius: 0.3em;
    margin-left: 1.5em;
    margin-top: -2em;
    padding: 1em;
    padding-bottom: 0;
    position: absolute;
    font-weight: normal;
    width: 313px;
    z-index: 1000;

    ul {
      padding-left: 1.3em;
    }
  }
}
</style>
