<template>
  <section class="workspace-dataset-permissions-type-dropdown">
    <workspace-dataset-permissions-type-dropdown-item v-for="item in dataList"
                                                 :isActive="isActive(item)"
                                                 :key="item.id"
                                                 :item="item"
                                                 class="permission-type-dropdown-item"
                                                 @onSelect="onSelectItem"
    />
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import DatasetPermissionType from '@/enum/dataset-permission-type';
import WorkspaceDatasetPermissionsTypeDropdownItem from '@/components/WorkspaceDatasetPermissions/WorkspaceDatasetPermissionsTypeDropdownItem';

export default {
  name: 'workspace-dataset-permissions-type-dropdown',

  components: {
    WorkspaceDatasetPermissionsTypeDropdownItem,
  },

  computed: {
    ...mapState('workspaces', ['customGroups']),

    ...mapState('workspacesDatasetPermissions', ['editingCustomGroup', 'editingPermissionType']),

    dataList() {
      const rs = [];

      rs.push(...DatasetPermissionType.enumValues
        .filter(i => i !== DatasetPermissionType.GROUP)
        .map(i => {
          return {
            id: i.rank(),
            type: i,
          };
        }));

      if (this.customGroups?.length) {
        // === separator ===
        rs.push({
          id: 'Custom Groups',
          type: -1,
        });
        // === groups list ===
        rs.push(...this.customGroups.map(i => {
          return {
            ...i,
            type: DatasetPermissionType.GROUP,
          };
        }));
      }

      return rs;
    },
  },

  methods: {
    ...mapActions('workspacesDatasetPermissions', ['setEditingCustomGroup', 'setEditingPermissionType']),

    isActive(item) {
      if (item.type === DatasetPermissionType.GROUP) {
        return item.id === this.editingCustomGroup?.id;
      }
      return item.type === this.editingPermissionType;
    },

    onSelectItem(item) {
      // type -1 - Separator
      if (item.type !== -1) {
        this.setEditingPermissionType({ value: item.type });
        if (item.type === DatasetPermissionType.GROUP) {
          this.setEditingCustomGroup({ value: item });
        } else {
          this.setEditingCustomGroup({ value: null });
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.workspace-dataset-permissions-type-dropdown {
  @include flex("block", "column", "start", "start");
  @include scrollbar-thin;
  background: #ffffff;
  border: none;
  border-radius: $border-radius-medium;
  box-shadow: 0 0 3px 3px rgba(0, 0, 0, 0.25);
  max-height: 400px;
  overflow: hidden auto;
  width: 500px;

  .permission-type-dropdown-item {
    padding: 0.5rem 1rem;

    &.isSeparator {
      padding-top: 1rem;
    }
  }
}
</style>
