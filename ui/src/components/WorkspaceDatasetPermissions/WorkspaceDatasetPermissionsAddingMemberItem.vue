<template>
  <section class="workspace-dataset-permissions-adding-member-item">
    <section class="info">
      <user-workspace-avatar :member-item=memberItem class="avatar" />
      <section class="labels">
        <span class="name" v-if="textMemberName">{{ textMemberName }}</span>
        <span class="email" v-if="textMemberEmail">{{ textMemberEmail }}</span>
      </section>
    </section>
    <section v-if="textMemberRole"
             @click="onClickMemberRole"
             v-click-outside-handler="{
               handler: 'onClickOutsideRole',
               excludedParentClasses: ['group-role-item', 'workspace-custom-groups-user-roles']
             }"
             class="adding-member-role"
             :class="{ disabled: !editable, isDatasetOwner }"
    >
      {{ textMemberRole }}
      <i class="fa-solid fa-caret-down icon" :class="{ hide: !editable, showDropdown }" />
    </section>
    <!-- invite user-role dropdown -->
    <workspace-custom-groups-user-roles :class="{ hide: !showDropdown }"
                                        :currentWorkspaceRole="currentWorkspaceRole"
                                        :invitingGroupRole="localInvitingRole"
                                        @onSelect="onSelectInvitingRole"
    />
  </section>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import clickOutsideHandler from '@/directives/click-outside-handler';
import UserWorkspaceAvatar from '@/components/UserWorkspaceAvatar';
import WorkspaceCustomGroupUpdateType from '@/enum/workspace-custom-group-update-type';
import WorkspaceCustomGroupsUserRoles from '@/components/WorkspaceCustomGroups/WorkspaceCustomGroupsUserRoles';
import WorkspaceInviteUserRole from '@/enum/workspace-invite-user-role';

export default {
  name: 'workspace-dataset-permissions-adding-member-item',

  components: {
    UserWorkspaceAvatar,
    WorkspaceCustomGroupsUserRoles,
  },

  props: {
    editable: {
      type: Boolean,
      default: false,
    },
    memberItem: {
      type: Object,
      required: true,
    },
  },

  directives: {
    clickOutsideHandler,
  },

  computed: {
    ...mapGetters('workspaces', ['getWorkspaceRoleByMemberId']),

    ...mapGetters('workspacesDatasetPermissions', ['checkDatasetMemberStatus']),

    currentDatasetRole() {
      if (this.datasetMemberStatus.isDatasetOwner) {
        return WorkspaceInviteUserRole.OWNER;
      }
      if (this.datasetMemberStatus.isDatasetEditor) {
        return WorkspaceInviteUserRole.EDITOR;
      }
      if (this.datasetMemberStatus.isDatasetViewer) {
        return WorkspaceInviteUserRole.VIEWER;
      }
      return null;
    },

    currentWorkspaceRole() {
      return this.getWorkspaceRoleByMemberId(this.memberId);
    },

    datasetMemberStatus() {
      return this.checkDatasetMemberStatus(this.memberId);
    },

    isDatasetOwner() {
      return this.datasetMemberStatus.isDatasetOwner;
    },

    memberId() {
      return this.memberItem.id;
    },

    textMemberEmail() {
      return this.memberItem.email?.trim() || '';
    },

    textMemberName() {
      const rs = [];
      if (this.memberItem.firstName?.trim().length) {
        rs.push(this.memberItem.firstName?.trim());
      }
      if (this.memberItem.lastName?.trim().length) {
        rs.push(this.memberItem.lastName?.trim());
      }

      return rs?.join(' ') || '';
    },

    textMemberRole() {
      return this.localInvitingRole?.titleCase() || WorkspaceInviteUserRole.VIEWER.titleCase();
    },
  },

  data() {
    return {
      localInvitingRole: WorkspaceInviteUserRole.VIEWER,
      showDropdown: false,
    };
  },

  mounted() {
    // memberItem.role: new role for new member - being edited atm
    if (this.memberItem.role) {
      if (typeof this.memberItem.role === 'object') {
        this.localInvitingRole = this.memberItem.role;
      } else if (typeof this.memberItem.role === 'string') {
        this.localInvitingRole = WorkspaceInviteUserRole[this.memberItem.role] || WorkspaceInviteUserRole.VIEWER;
      }
    } else if (this.datasetMemberStatus.isDatasetOwner) {
      this.localInvitingRole = WorkspaceInviteUserRole.OWNER;
    } else if (this.datasetMemberStatus.isDatasetEditor) {
      this.localInvitingRole = WorkspaceInviteUserRole.EDITOR;
    }
  },

  methods: {
    ...mapActions('workspacesDatasetPermissions', ['addEditingMembersList', 'removeEditingMembersList']),

    onClickMemberRole() {
      if (!this.isDatasetOwner && this.editable) {
        this.showDropdown = !this.showDropdown;
      }
    },

    onClickOutsideRole() {
      this.showDropdown = false;
    },

    onSelectInvitingRole(item) {
      this.showDropdown = false;
      if (this.isDatasetOwner || !this.editable) {
        return;
      }

      this.localInvitingRole = item;
      if (this.localInvitingRole === this.currentDatasetRole) {
        this.removeEditingMembersList({ value: { id: this.memberId } });
      } else {
        const value = {
          id: this.memberId,
          role: this.localInvitingRole,
          type: this.currentDatasetRole ? WorkspaceCustomGroupUpdateType.UPDATE : WorkspaceCustomGroupUpdateType.ADD,
        };
        this.addEditingMembersList({ value });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.workspace-dataset-permissions-adding-member-item {
  @include flex("block", "row", "between", "center");
  color: #2D1757;
  position: relative;
  width: 100%;

  &:last-child {
    .workspace-custom-groups-user-roles {
      top: -52px;
    }
  }

  .adding-member-role {
    @include flex("block", "row", "between", "center");
    border: 1px solid rgba(75, 114, 240, 0.15);
    border-radius: 2px;
    cursor: pointer;
    font-size: $font-size-xs;
    padding: 0.3rem 0.5rem;
    width: 80px;

    &.isDatasetOwner, &.disabled {
      border: none;
      cursor: default;

      &:hover {
        border: none;
      }

      .icon {
        display: none;
      }
    }

    &:hover {
      border: 1px solid rgba(75, 114, 240, 0.7);
    }

    .icon {
      margin-left: 0.2rem;
      transition: all $interaction-transition-time;

      &.hide {
        display: none;
      }

      &.showDropdown {
        transform: rotate(180deg);
      }
    }
  }

  .info {
    @include flex("block", "row", "center", "center");

    .avatar {
      margin-right: 0.8rem;
    }

    .labels {
      @include flex("block", "column", "center", "start");

      .name {
        font-size: $font-size-xs;
        font-weight: $font-weight-bold;
      }

      .email {
        font-size: 11px;
        margin-top: 0.2rem;
      }
    }
  }

  .workspace-custom-groups-user-roles {
    position: absolute;
    right: 2px;
    top: 52px;
    z-index: 2;

    &.hide {
      display: none;
    }
  }
}
</style>
