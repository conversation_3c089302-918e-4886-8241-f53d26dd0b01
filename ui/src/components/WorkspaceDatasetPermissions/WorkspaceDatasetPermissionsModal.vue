<template>
  <section class="workspace-dataset-permissions-modal">
    <section class="header">
      <span class="text">
        Permissions for {{ textDatasetLabel }}
      </span>
      <section class="permission-type">
        <workspace-dataset-permissions-type />
      </section>
    </section>
    <section class="body">
      <section class="body-item select-user"
               :class="{ hide: !isSearchingAvailable || !isEditable }"
               v-click-outside-handler="{
                                           handler: 'onClickOutsideUserSearch',
                                           excludedParentClasses: [
                                               'dropdown-item-name',
                                               'dropdown-item-role',
                                               'dropdown-item-status',
                                               'dropdown-item',
                                               'workspace-dataset-permissions-dropdown-item',
                                               'workspace-dataset-permissions-dropdown-list',
                                           ],
                                        }"
      >
        <input v-model="searchUser"
               @click="onClickSearch"
               class="text-search"
               placeholder="Select users" />
        <span class="dropdown" @click="onClickDropdown">
          <i class="fa-solid fa-caret-down icon" :class="{ open: showDropdownList }" />
        </span>
      </section>
      <section class="body-item users-list">
        <span v-if="!dataList.length" class="empty-list">{{ textEmptyMessage }}</span>
        <workspace-dataset-permissions-adding-member-item v-else v-for="item in dataList"
                                                          :key="item.id"
                                                          :memberItem="item"
                                                          :editable="isSearchingAvailable && isEditable"
                                                          class="adding-member-item"
        />
      </section>
    </section>
    <section class="footer">
      <base-button class="cancel-btn" colour="light" size="small" type="link" @click="onClickCancel">Cancel</base-button>
      <base-button v-if="!proceeding"
                   class="done-btn"
                   size="small"
                   @click="onClickSave"
      >
        Done
      </base-button>
      <loading-blocks-overlay v-if="proceeding" />
    </section>
    <!-- Available members dropdown-list -->
    <workspace-dataset-permissions-dropdown-list :class="{ hide: !showDropdownList }"
                                                 :searchStr="searchUser"/>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';
import BaseButton from '@/components/Base/BaseButton';
import clickOutsideHandler from '@/directives/click-outside-handler';
import DatasetPermissionType from '@/enum/dataset-permission-type';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import WorkspaceCustomGroupUpdateType from '@/enum/workspace-custom-group-update-type';
import WorkspaceDatasetPermissionsAddingMemberItem from '@/components/WorkspaceDatasetPermissions/WorkspaceDatasetPermissionsAddingMemberItem';
import WorkspaceDatasetPermissionsDropdownList from '@/components/WorkspaceDatasetPermissions/WorkspaceDatasetPermissionsDropdownList';
import WorkspaceDatasetPermissionsType from '@/components/WorkspaceDatasetPermissions/WorkspaceDatasetPermissionsType';
import WorkspaceInviteUserRole from '@/enum/workspace-invite-user-role';

import { datasetPermissionApi, workspaceGroupApi } from '@/services/api';
import { datasetsRequestV0 } from '@/services/request';

export default {
  name: 'workspace-dataset-permissions-modal',

  components: {
    BaseButton,
    LoadingBlocksOverlay,
    WorkspaceDatasetPermissionsAddingMemberItem,
    WorkspaceDatasetPermissionsDropdownList,
    WorkspaceDatasetPermissionsType,
  },

  directives: {
    clickOutsideHandler,
  },

  data() {
    return {
      proceeding: false,
      searchUser: '',
      showDropdownList: false,
    };
  },

  computed: {
    ...mapGetters('datasets', ['get']),

    ...mapState('user', ['activeWorkspace', 'user']),

    ...mapGetters('user', ['isAdmin']),

    ...mapState('workspaces', ['customGroups']),

    ...mapGetters('workspaces', ['getMembersFromWorkspaceByIds']),

    ...mapState('workspacesDatasetPermissions', [
      'datasetPermission',
      'editingCustomGroup',
      'editingDatasetId',
      'editingMembersList',
      'editingPermissionType',
    ]),

    ...mapGetters('workspacesDatasetPermissions', ['isDatasetOwner']),

    dataList() {
      const rs = [];

      const owner = this.getMembersFromWorkspaceByIds([this.datasetOwnerId])[0] || null;
      if (owner) {
        rs.push({
          ...owner,
          role: WorkspaceInviteUserRole.OWNER,
        });
      }

      if (this.isPrivate) {
        return rs;
      }

      if (this.isRestricted) {
        rs.push(...this.datasetAddingMembersList);
      } else if (this.isGroup) {
        rs.push(...this.membersListOfGroup);
      } else if (this.isPublic) {
        rs.push(...this.membersListOfWorkspace);
      }

      return rs;
    },

    dataset() {
      return this.get(this.editingDatasetId);
    },

    datasetAddingMembersList() {
      const adding = [];
      if (this.editingMembersList?.length) {
        adding.push(...this.editingMembersList.filter(i => i.type !== WorkspaceCustomGroupUpdateType.REMOVE));
      }

      let rs = adding.map(i => i.id);
      rs = this.getMembersFromWorkspaceByIds(rs);
      // map new group-role for memberObj
      rs = rs.map(i => {
        const addingObj = adding.find(o => o.id === i.id);
        let role = addingObj?.role || WorkspaceInviteUserRole.VIEWER;
        role = (typeof role === 'string') ? WorkspaceInviteUserRole[role] : role;

        return {
          ...i,
          role,
        };
      });

      return rs.filter(i => i.id !== this.datasetOwnerId);
    },

    datasetOwnerId() {
      return this.dataset.userId;
    },

    isEditable() {
      return this.isAdmin || this.isDatasetOwner(this.user.id);
    },

    isGroup() {
      return this.editingPermissionType === DatasetPermissionType.GROUP;
    },

    isPrivate() {
      return this.editingPermissionType === DatasetPermissionType.PRIVATE;
    },

    isPublic() {
      return this.editingPermissionType === DatasetPermissionType.PUBLIC;
    },

    isRestricted() {
      return this.editingPermissionType === DatasetPermissionType.RESTRICTED;
    },

    isSearchingAvailable() {
      return this.isRestricted;
    },

    membersListOfGroup() {
      const rs = [];
      if (this.editingCustomGroup?.editors?.length) {
        rs.push(...this.editingCustomGroup.editors);
      }
      if (this.editingCustomGroup?.viewers?.length) {
        rs.push(...this.editingCustomGroup.viewers);
      }
      return rs.filter(i => i.id !== this.datasetOwnerId);
    },

    membersListOfWorkspace() {
      const rs = [];
      if (this.activeWorkspace.editors?.length) {
        rs.push(...this.activeWorkspace.editors);
      }
      if (this.activeWorkspace.viewers?.length) {
        rs.push(...this.activeWorkspace.viewers);
      }
      return rs.filter(i => i.id !== this.datasetOwnerId);
    },

    textDatasetLabel() {
      return this.dataset?.label?.trim() || 'Dataset';
    },

    textEmptyMessage() {
      if (this.isPrivate) {
        return 'Only you and Workspace Owner / Admin can see this dataset.';
      }
      if (this.isRestricted) {
        return 'Only invited members can see this dataset.';
      }
      if (this.isGroup) {
        return 'All members in this group can see this dataset.';
      }
      if (this.isPublic) {
        return 'All members in workspace can see this dataset.';
      }
      return 'This dataset is shared with none yet.';
    },
  },

  beforeDestroy() {
    this.setEditingDatasetId({ value: null });
    this.setDatasetPermission({ value: null });
    this.setEditingCustomGroup({ value: null });
    this.setEditingMembersList({ values: [] });
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('workspaces', ['setCustomGroups']),

    ...mapActions('workspacesDatasetPermissions', [
      'setDatasetPermission',
      'setEditingCustomGroup',
      'setEditingDatasetId',
      'setEditingMembersList',
      'setEditingPermissionType',
    ]),

    async loadAllCustomGroups() {
      if (this.activeWorkspace) {
        const groups = await workspaceGroupApi.getAllCustomGroups(this.activeWorkspace.id);
        this.setCustomGroups({ values: groups });
      }
    },

    onClickCancel() {
      this.closeModal();
    },

    onClickDropdown() {
      if (this.isSearchingAvailable) {
        this.showDropdownList = !this.showDropdownList;
      }
    },

    onClickOutsideUserSearch() {
      this.showDropdownList = false;
    },

    async onClickSave() {
      if (!this.isEditable) {
        this.closeModal();
        return;
      }

      if (this.proceeding || !this.activeWorkspace) {
        return;
      }
      this.proceeding = true;

      // setup request-body & params
      const groupId = (this.isGroup && this.editingCustomGroup)
        ? this.editingCustomGroup.id
        : null;
      const editorIds = [];
      const viewerIds = [];
      if (this.isRestricted && this.editingMembersList?.length) {
        editorIds.push(...this.editingMembersList
          .filter(i => {
            return i.type !== WorkspaceCustomGroupUpdateType.REMOVE
              && i.role === WorkspaceInviteUserRole.EDITOR;
          })
          .map(i => i.id));
        viewerIds.push(...this.editingMembersList
          .filter(i => {
            return i.type !== WorkspaceCustomGroupUpdateType.REMOVE
              && i.role === WorkspaceInviteUserRole.VIEWER;
          })
          .map(i => i.id));
      }
      const request = {
        permissionType: this.editingPermissionType?.name || DatasetPermissionType.PRIVATE.name,
        editorIds,
        viewerIds,
        groupId,
      };
      await datasetPermissionApi.updatePermission(this.editingDatasetId, request);
      await datasetsRequestV0.getDatasets();
      // await datasetsRequestV0.reloadSelected();

      this.proceeding = false;
      this.closeModal();
    },

    onClickSearch() {
      if (this.isSearchingAvailable) {
        this.showDropdownList = true;
      }
    },
  },

  async mounted() {
    await this.loadAllCustomGroups();
    const permission = await datasetPermissionApi.retrievePermission(this.editingDatasetId);
    this.setDatasetPermission({ value: permission });

    if (permission?.permissionType) {
      const permissionType = DatasetPermissionType[permission.permissionType] || DatasetPermissionType.PRIVATE;
      this.setEditingPermissionType({ value: permissionType });

      // restricted-list
      if (permission.permissionType === DatasetPermissionType.RESTRICTED.name) {
        const members = [];
        if (permission.editorIds?.length) {
          members.push(...permission.editorIds.map(i => {
            return {
              id: i,
              role: WorkspaceInviteUserRole.EDITOR,
              type: WorkspaceCustomGroupUpdateType.UPDATE,
            };
          }));
        }
        if (permission.viewerIds?.length) {
          members.push(...permission.viewerIds.map(i => {
            return {
              id: i,
              role: WorkspaceInviteUserRole.VIEWER,
              type: WorkspaceCustomGroupUpdateType.UPDATE,
            };
          }));
        }
        this.setEditingMembersList({ values: members });
      }
      if (permission.permissionType === DatasetPermissionType.GROUP.name
        && permission?.groupId && this.customGroups?.length) {
        const group = this.customGroups.find(i => i.id === permission.groupId) || null;
        this.setEditingCustomGroup({ value: group });
      } else {
        this.setEditingCustomGroup({ value: null });
      }
    }
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.workspace-dataset-permissions-modal {
  @include panel;
  position: relative;
  width: 600px;

  .header {
    @include flex('block', 'column', 'center', 'start');
    border-bottom: $border-light solid $border-color;

    .text {
      @include truncate;
      color: #2D1757;
      font-size: $font-size-sm;
      font-weight: $font-weight-bold;
      padding: 1.5rem 1.5rem 1rem;
    }

    .permission-type {
      padding: 0 1.5rem 1.25rem;
      width: 100%;
    }
  }

  .body {
    @include flex('block', 'column', 'start', 'start');
    @include scrollbar-thin;
    max-height: 450px;
    overflow-y: auto;
    padding: 1.25rem 1.5rem;

    .body-item {
      margin-bottom: 1rem;
      width: 100%;

      &.select-user {
        @include flex('block', 'row', 'start', 'center');
        border: 1px solid rgba(115, 98, 183, 1);
        border-radius: 2px;

        &.hide {
          display: none;
        }

        .text-search {
          background: none;
          border: none;
          font-size: $font-size-xs;
          padding-left: 0.5rem;
          width: 100%;

          &:active, &:focus {
            outline: none;
          }
        }

        .dropdown {
          @include flex('block', 'row', 'center', 'center');
          border-left: 1px solid rgba(115, 98, 183, 1);
          cursor: pointer;
          height: 32px;
          margin-left: 1rem;
          padding: 0.4rem;
          width: 32px;

          .icon {
            transition: transform $interaction-transition-time;

            &.open {
              transform: rotate(180deg);
            }
          }
        }
      }

      &.users-list {
        @include scrollbar-thin;
        height: 100%;
        width: 100%;

        .empty-list {
          align-items: center;
          display: grid;
          height: inherit;
          justify-content: center;
          opacity: 0.7;
        }

        .adding-member-item {
          border-bottom: 1px solid rgba(242, 241, 251, 1);
          padding: 1rem 0;

          &:last-child {
            border-bottom: none;
            padding-bottom: 0;
          }
        }
      }
    }
  }

  .footer {
    @include flex('block', 'row', 'between', 'center');
    @include rigid;
    border-top: $border-light solid $border-color;
    padding: 1.5rem 1.5rem;

    .base-button {
      font-size: 0.65em;
      font-weight: 600;
      text-transform: uppercase;

      &.cancel-btn {
        padding: 0.5rem 1rem 0.5rem 0;
      }

      &.done-btn {
        background: #2D1757;
        padding: 0.5rem 1.5rem;
      }
    }

    .loading-blocks-overlay {
      height: 28px;
    }
  }

  .workspace-dataset-permissions-dropdown-list {
    position: absolute;
    left: 25px;
    top: 175px;
    z-index: 2;

    &.hide {
      display: none;
    }
  }
}
</style>
