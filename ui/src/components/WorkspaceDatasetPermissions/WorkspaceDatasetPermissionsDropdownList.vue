<template>
  <section class="workspace-dataset-permissions-dropdown-list">
    <section v-if="!dataList.length && !isSearching" class="empty-list">
      No available member in this workspace.
    </section>
    <section v-else-if="!dataList.length && isSearching" class="empty-list">
      Found none member that matches your search.
    </section>
    <workspace-dataset-permissions-dropdown-item v-else
                                           v-for="item in dataList"
                                           :isActive="isActive(item)"
                                           :key="item.id"
                                           :memberItem="item"
                                           class="dropdown-item"
                                           @onSelect="onSelectItem"
    />
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';
import WorkspaceCustomGroupUpdateType from '@/enum/workspace-custom-group-update-type';
import WorkspaceDatasetPermissionsDropdownItem from '@/components/WorkspaceDatasetPermissions/WorkspaceDatasetPermissionsDropdownItem';

export default {
  name: 'workspace-dataset-permissions-dropdown-list',

  components: {
    WorkspaceDatasetPermissionsDropdownItem,
  },

  props: {
    searchStr: {
      type: String,
      default: '',
    },
  },

  computed: {
    ...mapGetters('workspacesDatasetPermissions', ['checkDatasetMemberStatus']),

    ...mapState('user', ['activeWorkspace']),

    dataList() {
      const rs = [];
      rs.push(...this.workspaceEditorsList);
      rs.push(...this.workspaceViewersList);

      return rs.filter(i => {
        // remove Dataset owner from the list
        const status = this.checkDatasetMemberStatus(i.id);
        if (status.isDatasetOwner) {
          return false;
        }

        const searchStr = this.searchStr?.replace(/\s+/g, ' ').trim() || '';
        if (!searchStr.length) {
          return true;
        }

        const name = `${i.firstName || ''} ${i.lastName || ''}`.replace(/\s+/g, ' ').trim();
        const email = i.email?.replace(/\s+/g, ' ').trim() || '';
        return name.indexOf(searchStr) !== -1
            || email.indexOf(searchStr) !== -1;
      });
    },

    isSearching() {
      return this.searchStr?.trim().length > 0;
    },

    workspaceEditorsList() {
      return this.activeWorkspace?.editors || [];
    },

    workspaceViewersList() {
      return this.activeWorkspace?.viewers || [];
    },
  },

  methods: {
    ...mapActions('workspacesDatasetPermissions', ['addEditingMembersList', 'removeEditingMembersList']),

    isActive(item) {
      const status = this.checkDatasetMemberStatus(item.id);
      return status.adding || status.updating;
    },

    onSelectItem(item) {
      const status = this.checkDatasetMemberStatus(item.id);
      let value;

      /**
       * if adding new -> remove from editingList
       * if updating -> remove from editingList
       * if removing -> remove from editingList
       * others -> add new
       */
      if (status.adding || status.updating || status.removing) {
        value = { id: item.id };
        this.removeEditingMembersList({ value });
      } else {
        value = {
          id: item.id,
          role: status.workspaceRole,
          type: WorkspaceCustomGroupUpdateType.ADD,
        };
        this.addEditingMembersList({ value });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.workspace-dataset-permissions-dropdown-list {
  @include flex("block", "column", "start", "start");
  @include scrollbar-thin;
  background: #ffffff;
  border: none;
  border-radius: $border-radius-medium;
  box-shadow: 0 0 3px 3px rgba(0, 0, 0, 0.25);
  max-height: 400px;
  overflow: hidden auto;
  width: 515px;

  .empty-list {
    align-items: center;
    display: grid;
    height: 60px;
    justify-content: center;
    opacity: 0.7;
    width: inherit;
  }

  .dropdown-item {
    cursor: pointer;
    padding: 0.5rem 1rem;

    &:hover {
      background-color: rgba(clr('black'), 0.05);
    }
  }
}
</style>
