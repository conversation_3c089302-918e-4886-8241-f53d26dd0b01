<template>
  <section class="workspace-dataset-permissions-type"
           :class="{ disabled: !isEditable, isGroup }"
           @click="onClick"
           v-click-outside-handler="{ handler: 'onClickOutside' }"
  >
    <section class="left-icon">
      <i :class="textIconType" class="icon" />
    </section>
    <section class="label">
      <section class="name">{{ textName }}</section>
      <section class="description">{{ textDescription }}</section>
    </section>
    <section class="dropdown-icon">
      <i class="fa-solid fa-caret-down icon" :class="{ open: showDropdown }" />
    </section>
    <!-- Available Dataset Permissions dropdown-list -->
    <workspace-dataset-permissions-type-dropdown :class="{ hide: !showDropdown }" />
  </section>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import clickOutsideHandler from '@/directives/click-outside-handler';
import DatasetPermissionType from '@/enum/dataset-permission-type';
import WorkspaceDatasetPermissionsTypeDropdown from '@/components/WorkspaceDatasetPermissions/WorkspaceDatasetPermissionsTypeDropdown';

export default {
  name: 'workspace-dataset-permissions-type',

  components: {
    WorkspaceDatasetPermissionsTypeDropdown,
  },

  directives: {
    clickOutsideHandler,
  },

  computed: {
    ...mapState('user', ['user']),

    ...mapGetters('user', ['isAdmin']),

    ...mapState('workspacesDatasetPermissions', [
      'datasetPermission',
      'editingCustomGroup',
      'editingPermissionType',
    ]),

    ...mapGetters('workspacesDatasetPermissions', ['isDatasetOwner']),

    isEditable() {
      return this.isAdmin || this.isDatasetOwner(this.user.id);
    },

    isGroup() {
      return this.localPermissionType === DatasetPermissionType.GROUP;
    },

    isPublic() {
      return this.localPermissionType === DatasetPermissionType.PUBLIC;
    },

    isRestricted() {
      return this.localPermissionType === DatasetPermissionType.RESTRICTED;
    },

    localPermissionType() {
      if (this.editingPermissionType) {
        return this.editingPermissionType;
      }
      if (this.datasetPermission) {
        return typeof this.datasetPermission.permissionType === 'string'
          ? DatasetPermissionType[this.datasetPermission.permissionType]
          : this.datasetPermission.permissionType;
      }
      return DatasetPermissionType.PRIVATE;
    },

    textDescription() {
      if (this.isPublic) {
        return 'All workspace users';
      }
      if (this.isGroup) {
        return this.editingCustomGroup?.description?.trim() || 'All users in a group';
      }
      if (this.isRestricted) {
        return 'Only invited users';
      }
      return 'Only you (and admins)';
    },

    textIconType() {
      if (this.isPublic) {
        return 'fa-solid fa-globe';
      }
      if (this.isGroup) {
        return 'fa-solid fa-users';
      }
      if (this.isRestricted) {
        return 'fa-solid fa-users';
      }
      return 'fa-solid fa-user';
    },

    textName() {
      if (this.isPublic) {
        return 'Everyone';
      }
      if (this.isGroup) {
        return this.editingCustomGroup?.label?.trim() || 'Custom Group';
      }
      if (this.isRestricted) {
        return 'Restricted';
      }
      return 'Private';
    },
  },

  data() {
    return {
      showDropdown: false,
    };
  },

  methods: {
    onClick() {
      if (!this.isEditable) return;
      this.showDropdown = !this.showDropdown;
    },

    onClickOutside() {
      this.showDropdown = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.workspace-dataset-permissions-type {
  @include flex("block", "row", "start", "center");
  background-color: rgba(95, 82, 197, 0.25);
  border-radius: 80px;
  color: rgba(45, 23, 87, 1);
  cursor: pointer;
  height: 45px;
  padding: 0.4rem 1rem;
  position: relative;
  width: inherit;

  &.disabled {
    cursor: default;

    .dropdown-icon {
      display: none;
    }
  }

  &.isGroup {
    background-color: rgba(0, 99, 255, 0.25);
    color: rgba(0, 99, 255, 1);
  }

  .left-icon {
    height: 20px;
    margin-right: 1rem;
    width: 20px;
  }

  .label {
    width: 100%;

    .name {
      font-size: $font-size-xs;
      font-weight: $font-weight-bold;
    }

    .description {
      font-size: 11px;
      margin-top: 0.2rem;
    }
  }

  .dropdown-icon {
    .icon {
      transition: transform $interaction-transition-time;

      &.open {
        transform: rotate(180deg);
      }
    }
  }

  .workspace-dataset-permissions-type-dropdown {
    position: absolute;
    left: 10px;
    top: 52px;
    z-index: 2;

    &.hide {
      display: none;
    }
  }
}
</style>
