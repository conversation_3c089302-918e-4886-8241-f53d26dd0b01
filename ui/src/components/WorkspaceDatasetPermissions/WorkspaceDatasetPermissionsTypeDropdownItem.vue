<template>
  <section class="workspace-dataset-permissions-type-dropdown-item"
           :class="{ isActive, isGroup, isSeparator }"
           @click="onSelect"
  >
    <section class="item-left-icon" v-if="!isSeparator">
      <i :class="textIconType" class="icon" />
    </section>
    <section class="item-label">
      <section class="name">{{ textName }}</section>
      <section class="description" v-if="textDescription">{{ textDescription }}</section>
    </section>
    <section class="item-status" v-if="!isSeparator">
      <i class="fa-solid fa-circle-check icon" v-show="isActive" />
    </section>
  </section>
</template>

<script>
import DatasetPermissionType from '@/enum/dataset-permission-type';

export default {
  name: 'workspace-dataset-permissions-type-dropdown-item',

  props: {
    isActive: {
      type: Boolean,
      required: true,
    },
    item: {
      type: Object,
      required: true,
    },
  },

  computed: {
    isGroup() {
      return this.item.type === DatasetPermissionType.GROUP;
    },

    isPublic() {
      return this.item.type === DatasetPermissionType.PUBLIC;
    },

    isRestricted() {
      return this.item.type === DatasetPermissionType.RESTRICTED;
    },

    isSeparator() {
      return typeof this.item.type === 'number';
    },

    textDescription() {
      if (this.isSeparator) {
        return '';
      }
      if (this.isPublic) {
        return 'All workspace users';
      }
      if (this.isGroup) {
        return this.item?.description?.trim() || 'All users in a group';
      }
      if (this.isRestricted) {
        return 'Only invited users';
      }
      return 'Only you (and admins)';
    },

    textIconType() {
      if (this.isSeparator) {
        return '';
      }
      if (this.isPublic) {
        return 'fa-solid fa-globe';
      }
      if (this.isGroup) {
        return 'fa-solid fa-users';
      }
      if (this.isRestricted) {
        return 'fa-solid fa-users';
      }
      return 'fa-solid fa-user';
    },

    textName() {
      if (this.isSeparator) {
        return this.item.id;
      }
      if (this.isPublic) {
        return 'Everyone';
      }
      if (this.isGroup) {
        return this.item?.label?.trim() || 'Custom Group';
      }
      if (this.isRestricted) {
        return 'Restricted';
      }
      return 'Private';
    },
  },

  methods: {
    onSelect() {
      this.$emit('onSelect', this.item);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.workspace-dataset-permissions-type-dropdown-item {
  align-items: center;
  border-bottom: 1px solid rgba(95, 82, 197, 0.25);
  color: rgba(45, 23, 87, 1);
  cursor: pointer;
  display: grid;
  font-size: $font-size-xs;
  grid-template-columns: 16px 1fr 16px;
  grid-column-gap: 1rem;
  width: inherit;

  &.isActive:not(.isSeparator), &:hover:not(.isSeparator) {
    background-color: rgba(95, 82, 197, 0.25);

    &.isGroup {
      background-color: rgba(0, 99, 255, 0.25);
    }
  }

  &.isGroup {
    color: rgba(0, 99, 255, 1);
  }

  &.isSeparator {
    border-bottom: none;
    border-top: 1px solid rgba(165, 159, 217, 1);
    color: rgba(0, 99, 255, 1);
    cursor: default;
    font-size: $font-size-xxs;
    grid-template-columns: 1fr;
    text-transform: uppercase;
  }

  .item-label {
     max-width: 370px;

    .name {
      @include truncate;
      font-size: $font-size-xs;
      font-weight: $font-weight-bold;
    }

    .description {
      @include truncate;
      font-size: 11px;
      margin-top: 0.2rem;
    }
  }
}
</style>
