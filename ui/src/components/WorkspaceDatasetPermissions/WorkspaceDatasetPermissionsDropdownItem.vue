<template>
  <section class="workspace-dataset-permissions-dropdown-item" @click="onSelect">
    <section class="dropdown-item-status">
      <base-checkbox :value="isActive" />
    </section>
    <section class="dropdown-item-name">
      <span class="text name" v-if="textName">{{ textName }}</span>
      <span class="text email" v-if="textEmail">{{ textEmail }}</span>
    </section>
    <section class="dropdown-item-role" v-if="textRole">{{ textRole }}</section>
  </section>
</template>

<script>
import { mapState } from 'vuex';
import BaseCheckbox from '@/components/Base/BaseCheckbox';
import WorkspaceInviteUserRole from '@/enum/workspace-invite-user-role';

export default {
  name: 'workspace-dataset-permissions-dropdown-item',

  components: { BaseCheckbox },

  props: {
    isActive: {
      type: Boolean,
      required: true,
    },
    memberItem: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapState('user', ['activeWorkspace']),

    memberId() {
      return this.memberItem.id;
    },

    textEmail() {
      return this.memberItem.email?.trim() || '';
    },

    textName() {
      const rs = [];
      if (this.memberItem.firstName?.trim().length) {
        rs.push(this.memberItem.firstName?.trim());
      }
      if (this.memberItem.lastName?.trim().length) {
        rs.push(this.memberItem.lastName?.trim());
      }

      return rs?.join(' ') || '';
    },

    textRole() {
      if (this.memberItem.role) {
        if (typeof this.memberItem.role === 'object') {
          return this.memberItem.role.titleCase();
        }
        return WorkspaceInviteUserRole[this.memberItem.role].titleCase();
      }

      if (this.activeWorkspace.owner?.id === this.memberId) {
        return WorkspaceInviteUserRole.OWNER.titleCase();
      }

      if (this.activeWorkspace.administratorIds?.includes(this.memberId)) {
        return WorkspaceInviteUserRole.ADMIN.titleCase();
      }

      if (this.activeWorkspace.editorIds?.includes(this.memberId)) {
        return WorkspaceInviteUserRole.EDITOR.titleCase();
      }

      if (this.activeWorkspace.viewerIds?.includes(this.memberId)) {
        return WorkspaceInviteUserRole.VIEWER.titleCase();
      }

      return '';
    },
  },

  methods: {
    onSelect() {
      this.$emit('onSelect', this.memberItem);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.workspace-dataset-permissions-dropdown-item {
  align-items: center;
  display: grid;
  grid-template-columns: 16px 1fr auto;
  grid-column-gap: 1rem;
  font-size: $font-size-xs;
  width: inherit;

  .dropdown-item-status {
    @include flex("block", "row", "center", "center");
  }

  .dropdown-item-name {
    @include flex("block", "column", "center", "start");

    .text {
      @include truncate;
      max-width: 370px;

      &.name {
        font-size: $font-size-xs;
        font-weight: $font-weight-bold;
      }

      &.email {
        font-size: 11px;
        margin-top: 0.2rem;
      }
    }
  }

  .dropdown-item-role {
    font-size: $font-size-xs;
  }
}
</style>
