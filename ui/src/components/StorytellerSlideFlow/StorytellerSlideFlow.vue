<template>
  <section class="storyteller-slide-flow" :style="{fontSize}">
    <section class="margin-top" />
    <section class="body" @mouseenter="hovered = true" @mouseleave="hovered = false">
      <span class="header">Presentation Flow</span>
      <section class="image-container">
        <img v-if="isReportCx"
             class="flow-image"
             :src="flowCxImage"
             alt="flow-image"/>
        <img v-else-if="isReportEx"
             class="flow-image"
             :src="flowExImage"
             alt="flow-image"/>
        <img v-else-if="isReportOther"
             class="flow-image"
             :src="flowOtherImage"
             alt="flow-image"/>
      </section>
    </section>
    <storyteller-slide-footer :slide-width="slideWidth" />
  </section>
</template>

<script>
import { mapState } from 'vuex';

import flowCxImage from '@/assets/storyteller/storyteller-slide-flow-cx.png';
import flowExImage from '@/assets/storyteller/storyteller-slide-flow-ex.png';
import flowOtherImage from '@/assets/storyteller/storyteller-slide-flow-generic.png';
import flowPxImage from '@/assets/storyteller/storyteller-slide-flow-px.png';
import StorytellerReportType from '@/enum/storyteller-report-type';
import StorytellerSlideFooter from '@/components/StorytellerSlides/StorytellerSlideFooter';
import UpdateSlide from '@/components/Mixins/UpdateSlide';

export default {
  name: 'storyteller-slide-flow',

  components: {
    StorytellerSlideFooter,
  },

  mixins: [UpdateSlide],

  data() {
    return {
      flowCxImage,
      flowExImage,
      flowOtherImage,
      flowPxImage,
    };
  },

  computed: {
    ...mapState('storyteller', ['activeReport']),

    isReportCx() {
      return this.activeReport.settings.reportCategory === StorytellerReportType.CX.value();
    },

    isReportEx() {
      return this.activeReport.settings.reportCategory === StorytellerReportType.EX.value();
    },

    isReportOther() {
      return this.activeReport.settings.reportCategory === StorytellerReportType.OTHER.value();
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-flow {
  background-color: #FFF;
  color: $nps-blue;
  display: grid;
  font-family: Inter, serif;
  font-weight: $font-weight-semi-light;
  grid-template-rows: 13% 74% 13%;
  height: 100%;
  padding: 0 2em;
  width: 100%;

  .body {
    @include flex("block", "column", "start", "center");
    height: 100%;
    position: relative;
    width: 100%;

    .header {
      font-size: 0.5em;
      font-weight: 800;
      left: 0;
      letter-spacing: 0.04em;
      opacity: 0.5;
      position: absolute;
      text-transform: uppercase;
      top: 0;
    }

    .image-container {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .flow-image {
        height: 74%;
        max-width: 100%;
        object-fit: contain;
        width: auto;
      }
    }
  }
}
</style>
