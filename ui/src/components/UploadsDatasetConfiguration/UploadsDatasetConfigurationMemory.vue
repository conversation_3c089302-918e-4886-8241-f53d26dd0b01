<template>
  <section class="uploads-dataset-configuration-memory">
    <section class="header">
      <section class="left">
        <i class="fa fa-save icon icon-memory"></i>
        Memory
      </section>
      <section class="right">
        <section class="checkbox" @click="clickSelectAll">
          <base-toggle-button colour="green" size="large" :value="allSelected" @click.prevent/>
          Apply all actions in Memory
        </section>
        <help-circle-icon class="icon icon-help"
                          v-tooltip.bottom="{
            content: 'Apply actions you have saved to memory to automatically rename, merge or create themes during the upload process.',
            class: 'tooltip-base',
            delay: 0,
          }"
        />
      </section>
    </section>
    <section class="body">
      <section class="no-content">
        <section class="count text">
          <section v-if="!hasSelected" class="applied applied-zero">
            No collections will be applied.
          </section>

          <section v-else class="applied">
            <span v-if="allSelected">
              All Memory actions will be applied.
            </span>
            <span v-else>
              {{ selectedLists.length }} Collection{{ selectedLists.length === 1 ? '' : 's' }} will be applied.
            </span>
          </section>
        </section>

        <base-button icon="edit" type="outline" @click="onClickApplyMemory">
          {{ selectedLists.length > 0 ? 'Edit Selected Memory' : 'Apply Memory' }}
        </base-button>

        <section class="ex-adore-themes">
          <span class="checkbox"
                @click="onClickExcludeAdoreThemes"
          >
            <base-checkbox :value="excludeAdoreThemes" />
            <span>Exclude Adoreboard-generated themes</span>
          </span>
          <help-circle-icon class="icon icon-help"
                            v-tooltip.bottom="{
            content: 'Selecting this option will prevent Adoreboard from automatically generating themes, and only generate themes based on selected saved-actions.',
            class: 'tooltip-base',
            delay: 0,
          }"
          />
        </section>
      </section>
    </section>
  </section>
</template>

<script>
import { HelpCircleIcon } from 'vue-feather-icons';
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseCheckbox from '@/components/Base/BaseCheckbox';
import BaseToggleButton from '@/components/Base/BaseToggleButton';
import WorkspaceMemoryCollectionsModal from '@/components/WorkspaceMemoryCollections/WorkspaceMemoryCollectionsModal';

export default {
  name: 'uploads-dataset-configuration-memory',

  components: {
    BaseButton,
    BaseCheckbox,
    BaseToggleButton,
    HelpCircleIcon,
  },

  props: {
    id: {
      type: String,
      required: true,
    },
  },

  computed: {
    ...mapState('savedActions', [
      'excludeAdoreThemes',
      'savedActionLists',
      'selectedLists',
    ]),

    allMemoryListId() {
      return this.savedActionLists.find(list => list.default)?.id || -1;
    },

    allSelected() {
      return this.selectedLists.includes(this.allMemoryListId);
    },

    hasSelected() {
      return this.selectedLists.length > 0;
    },
  },

  created() {
    this.resetSelectedLists();
  },

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    ...mapActions('savedActions', [
      'addToSelectedLists',
      'resetSelectedLists',
      'setExcludeAdoreThemes',
      'setIsApplying',
    ]),

    clickSelectAll() {
      if (this.allSelected) {
        this.resetSelectedLists();
      } else {
        this.addToSelectedLists({ id: this.allMemoryListId });
      }
    },

    getTheCountWord(numb) {
      return numb > 1 ? 'actions' : 'action';
    },

    async onClickApplyMemory() {
      this.setIsApplying({ isApplying: true });
      this.setModalComponent({ component: WorkspaceMemoryCollectionsModal });
    },

    onClickExcludeAdoreThemes() {
      this.setExcludeAdoreThemes({ value: !this.excludeAdoreThemes });
    },
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.uploads-dataset-configuration-memory {
  @include uploads-dataset-configuration-panel;

  .header {
    .left {
      @include flex("block", "row", "start", "center");

      .icon-memory {
        padding-top: 2px;
      }
    }

    .right {
      .checkbox {
        @include flex("block", "row", "start", "center");
        color: clr('white');
        cursor: pointer;
        font-size: $font-size-xs;
        transition: opacity $interaction-transition-time;

        &:hover {
          opacity: 0.7;
        }

        .base-toggle-button {
          margin-right: 0.5rem;

          label {
            border: 1px solid rgba(clr('white'), 0.7);
            height: 1.3em;
          }
        }
      }

      .icon-help {
        margin: 0 0 0 0.5rem;
      }
    }
  }

  .body {
    @include flex("block", "column", "center", "center");

    .count {
      .applied {
        @include flex("block", "row", "center", "center");
        font-size: $font-size-xs;
        margin-top: 0.3rem;

        &:first-child {
          margin-top: 0;
        }

        .strong {
          color: clr("white");
          font-weight: $font-weight-medium;
          margin-right: 0.3rem;
        }
      }
    }

    .ex-adore-themes {
      @include flex("block", "row", "center", "center");
      font-size: $font-size-xs;
      margin-top: 0.5rem;

      .checkbox {
        @include flex("block", "row", "center", "center");
        cursor: pointer;

        .base-checkbox {
          margin-right: 0.5rem;
        }
      }

      .icon {
        margin-left: 0.5rem;
        width: 1rem;
      }
    }
  }
}
</style>
