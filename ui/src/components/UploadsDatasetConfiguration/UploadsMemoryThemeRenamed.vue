<template>
  <section class="uploads-memory-theme-renamed">
    <renamed-edit-action :action="editAction" @clear="onClear"></renamed-edit-action>

    <section v-if="showSort" class="header">
      <span class="header-item header-new" @click="onSort('new')">
        <arrow-down-icon class="icon" :class="{ asc: sortAsc }" v-if="sortBy === 'new'"/>
        New Label
      </span>
      <span class="header-item header-old" @click="onSort('old')">
        <arrow-down-icon class="icon" :class="{ asc: sortAsc }" v-if="sortBy === 'old'"/>
        Original Label
      </span>
      <span class="control-space"></span>
    </section>

    <!-- List -->
    <section class="list" v-if="dataList.length">
      <memory-renamed-theme v-for="item in dataList"
        :key="item.id"
        :action="item"
        class="list-item"
        @selectItem="onSelectItem"
      />
    </section>

    <!-- Empty Msg -->
    <section v-else class="empty">
      <span class="text">No "Rename Theme" actions exist.</span>
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';
import { ArrowDownIcon } from 'vue-feather-icons';
import MemoryRenamedTheme from '@/components/UploadsDatasetConfiguration/MemoryRenamedTheme/MemoryRenamedTheme';
import RenamedEditAction from '@/components/UploadsDatasetConfiguration/MemoryRenamedTheme/RenamedEditAction';
import SavedActionsType from '@/enum/saved-actions-type';

export default {
  name: 'uploads-memory-theme-renamed',

  components: {
    ArrowDownIcon,
    MemoryRenamedTheme,
    RenamedEditAction,
  },

  computed: {
    ...mapState('savedActions', {
      savedActions: 'savedActions',
    }),

    dataList() {
      return this.savedActions
        .filter(item => item?.type === SavedActionsType.RENAME_THEME.name)
        .sort((a, b) => {
          const { newLabel: aNew, originalLabel: aOld } = a.components;
          const { newLabel: bNew, originalLabel: bOld } = b.components;
          // by new label
          if (this.sortBy === 'new') {
            if (this.sortAsc) {
              return aNew.localeCompare(bNew);
            }
            return bNew.localeCompare(aNew);
          }
          // by original label
          if (this.sortAsc) {
            return aOld.localeCompare(bOld);
          }
          return bOld.localeCompare(aOld);
        });
    },
  },

  data() {
    return {
      editAction: null,
      showSort: false,
      sortAsc: true,
      sortBy: 'new',
    };
  },

  methods: {
    onClear() {
      this.editAction = null;
    },

    onSort(val) {
      if (this.sortBy === val) {
        this.sortAsc = !this.sortAsc;
      } else {
        this.sortBy = val;
        this.sortAsc = true;
      }
    },

    onSelectItem(item) {
      this.editAction = item;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.uploads-memory-theme-renamed {
  @include flex("block", "column", "start", "stretch");
  @include stretch;

  overflow: hidden;

  .header {
    @include flex("block", "row", "start", "center");

    margin-bottom: 0.5rem;

    .header-item {
      @include flex("block", "row", "start", "center");

      cursor: pointer;
      font-size: $font-size-xxs;
      font-weight: $font-weight-bold;
      margin-left: 0.5rem;
      overflow: hidden;
      text-overflow: ellipsis;
      text-transform: uppercase;
      white-space: nowrap;
      width: 50%;

      &:hover {
        opacity: 0.7;
      }

      .icon {
        height: 0.8rem;
        margin-right: 0.5rem;
        transition: all $interaction-transition-time;
        width: 0.8rem;

        &.asc {
          transform: rotate(180deg);
        }
      }
    }

    .control-space {
      margin: 0 1rem;
      width: 60px;
    }
  }

  .list {
    @include flex("block", "column", "start", "stretch");
    @include stretch;

    overflow-y: auto;
    scrollbar-width: thin;
  }

  .empty {
    @include flex("block", "row", "center", "center");
    @include stretch;

    color: #352691;
  }
}
</style>
