<template>
  <section class="uploads-memory-theme-from-merge">
    <memory-edit-action/>

    <section v-if="actionsMergeThemes.length > 0" class="theme-list">
      <memory-merge-theme v-for="action in actionsMergeThemes" :key="action.id" :action="action"/>
    </section>

    <!-- Empty Msg -->
    <section v-else class="empty">
      <span class="text">No "Merge Themes" actions exist.</span>
    </section>
  </section>
</template>

<script>
import { mapGetters } from 'vuex';

import MemoryEditAction from '@/components/UploadsDatasetConfiguration/MemoryViewMerge/MemoryEditAction';
import MemoryMergeTheme from '@/components/UploadsDatasetConfiguration/MemoryViewMerge/MemoryMergeTheme';

export default {
  name: 'uploads-memory-theme-from-merge',

  components: {
    MemoryEditAction,
    MemoryMergeTheme,
  },

  computed: {
    ...mapGetters('savedActions', ['actionsMergeThemes']),
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.uploads-memory-theme-from-merge {
  @include flex("block", "column", "start", "stretch");
  @include stretch;

  overflow: hidden;

  .theme-list {
    @include flex("block", "column", "start", "stretch");
    @include stretch;

    overflow-y: auto;
    scrollbar-width: thin;
  }

  .empty {
    @include flex("block", "row", "center", "center");
    @include stretch;

    color: #352691;
  }
}
</style>
