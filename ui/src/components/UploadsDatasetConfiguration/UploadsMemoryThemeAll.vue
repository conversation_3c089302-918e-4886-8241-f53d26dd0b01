<template>
  <section class="uploads-memory-theme-all">
    <section v-if="selectedList == null" class="saved-list" :class="{ saving }">
      <section class="left">
        <span class="label no-collection">No Collection Selected</span>
      </section>

      <section v-if="creatingNew" class="right">
        <base-input :focus="true" placeholder="Enter a name..." v-model="newLabel"/>

        <base-button colour="dark" size="small" @click="onSaveNew">Save</base-button>
        <base-button colour="light" size="small" @click="onCancelNew">Cancel</base-button>
      </section>

      <section v-else class="right">
        <base-button colour="dark" :disabled="actionsToUpload.length === 0" size="small" @click="creatingNew = true">
          Create Collection From Selected Actions
        </base-button>
      </section>
    </section>

    <section v-if="selectedList != null" class="saved-list" :class="{ saving }">
      <section class="left">
        <span v-if="creatingNew" class="label">New Collection</span>
        <span v-else class="label">{{ selectedList.label }}</span>
      </section>

      <section v-if="creatingNew" class="right">
        <base-input :focus="true" placeholder="Enter a name..." v-model="newLabel"/>

        <base-button colour="dark" size="small" @click="onSaveNew">Save</base-button>
        <base-button colour="light" size="small" @click="onCancelNew">Cancel</base-button>
      </section>

      <section v-else class="right">
        <base-button colour="dark" size="small" @click="onSave">Save Collection</base-button>
        <base-button colour="dark" size="small" @click="creatingNew = true">Save As New Collection...</base-button>
        <base-button colour="light" size="small" @click="onClearSelected">Clear</base-button>
      </section>
    </section>

    <section class="lists">
      <memory-theme-list v-for="([actions, type], i) in displayList"
        :key="i"
        :actions="actions"
        :actionType="type"
        @click-edit="onClickEdit"
      />
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import MemoryThemeList from '@/components/UploadsDatasetConfiguration/MemoryViewAll/MemoryThemeList';
import SavedActionsType from '@/enum/saved-actions-type';

import { savedActionApi } from '@/services/api';

export default {
  name: 'uploads-memory-theme-all',

  components: {
    BaseButton,
    BaseInput,
    MemoryThemeList,
  },

  data() {
    return {
      creatingNew: false,
      newLabel: '',
      SavedActionsType,
      saving: false,
    };
  },

  computed: {
    ...mapState('savedActions', [
      'actionsToUpload',
      'savedActions',
      'selectedList',
    ]),

    ...mapGetters('savedActions', [
      'actionsCreateSearchTheme',
      'actionsDeleteTheme',
      'actionsMergeThemes',
      'actionsRenameTheme',
    ]),

    ...mapState('user', ['activeWorkspace']),

    allIds() {
      return this.savedActions.map(a => a.id);
    },

    displayList() {
      return [
        [this.actionsCreateSearchTheme, SavedActionsType.CREATE_SEARCH_THEME],
        [this.actionsMergeThemes, SavedActionsType.MERGE_THEMES],
        [this.actionsRenameTheme, SavedActionsType.RENAME_THEME],
        [this.actionsDeleteTheme, SavedActionsType.DELETE_THEME],
      ];
    },
  },

  methods: {
    ...mapActions('savedActions', [
      'addSavedActionsToUpload',
      'removeSavedActionsFromUpload',
      'setSavedActionLists',
      'setSelectedList',
    ]),

    onCancelNew() {
      this.newLabel = '';
      this.creatingNew = false;
    },

    onClearSelected() {
      this.setSelectedList({ list: null });
      this.removeSavedActionsFromUpload({ ids: this.allIds });
    },

    onClickEdit(type) {
      this.$emit('click-edit', type);
    },

    async onSave() {
      this.saving = true;

      const updated = { ...this.selectedList, list: this.validateActionIds(this.actionsToUpload) };

      await savedActionApi.updateSavedActionList(this.activeWorkspace?.id, updated);

      const savedActionLists = await savedActionApi.getSavedActionLists(this.activeWorkspace?.id);
      this.setSavedActionLists({ savedActionLists });

      this.saving = false;
    },

    async onSaveNew() {
      this.saving = true;

      const createdId = await savedActionApi.createSavedActionList(this.activeWorkspace?.id, this.newLabel, this.validateActionIds(this.actionsToUpload));

      const savedActionLists = await savedActionApi.getSavedActionLists(this.activeWorkspace?.id);
      this.setSavedActionLists({ savedActionLists });

      this.setSelectedList({ list: savedActionLists.find(list => list.id === createdId) });

      this.saving = false;
      this.creatingNew = false;
    },

    validateActionIds(idList) {
      // remove all deleted saved-action-ids - which are actually deleted (not the delete-action) - from collection
      return idList.filter(id => this.allIds.includes(id));
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.uploads-memory-theme-all {
  @include flex("block", "column", "start", "stretch");
  @include stretch;

  .saved-list {
    @include flex("block", "row", "between", "stretch");
    @include rigid;

    border-bottom: $border-standard;
    padding: 0.5rem 0;
    margin-bottom: 0.5rem;

    .left {
      @include flex("block", "row", "start", "center");
      @include stretch;
      @include truncate;

      color: #352691;

      .label {
        @include truncate;

        font-weight: $font-weight-bold;
      }
    }

    .right {
      @include flex("block", "row", "center", "center");
      @include rigid;

      .base-button {
        margin-right: 1rem;
        padding: 0.3rem 0.6rem;

        &:last-child {
          margin-right: 0;
        }
      }

      .base-input {
        font-size: $font-size-xs;
        margin-right: 1rem;
      }
    }
  }

  .lists {
    @include stretch;

    display: grid;
    grid-gap: 1rem;
    grid-template-columns: 1fr 1fr 1fr 1fr;
  }
}
</style>
