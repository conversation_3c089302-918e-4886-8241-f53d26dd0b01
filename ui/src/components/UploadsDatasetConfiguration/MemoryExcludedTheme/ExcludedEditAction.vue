<template>
  <section class="excluded-edit-action">
    <uploads-memory-edit-controls v-if="selected != null" @clear="resetState">
      {{ themeLabel }}
    </uploads-memory-edit-controls>

    <section class="edit-controls">
      <section class="label-section">
        <base-input v-model="localLabel"
                    placeholder="Deleted Theme Label"
                    @submit="onClickSubmit"
        />
      </section>
      <section class="apply">
        <loading-blocks-overlay v-if="loading" class="loading" />
        <base-button v-else colour="dark" size="small" @click="onClickSubmit" :disabled="!validInput">
          {{ selected != null ? 'Update' : 'Create' }} Delete Action
        </base-button>
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import SavedActionsType from '@/enum/saved-actions-type';
import UploadsMemoryEditControls from '@/components/UploadsDatasetConfiguration/UploadsMemoryEditControls';

import { savedActionApi } from '@/services/api';

export default {
  name: 'excluded-edit-action',

  components: {
    BaseButton,
    BaseInput,
    LoadingBlocksOverlay,
    UploadsMemoryEditControls,
  },

  beforeDestroy() {
    this.selectSavedAction({ id: null });
  },

  computed: {
    ...mapGetters('savedActions', ['getActionByNewLabel']),

    ...mapState('savedActions', ['selected']),

    ...mapState('user', ['activeWorkspace']),

    themeLabel() {
      return this.selected?.components?.newLabel || '';
    },
  },

  data() {
    return {
      loading: false,
      localLabel: '',
      validInput: false,
    };
  },

  methods: {
    ...mapActions('savedActions', ['selectSavedAction', 'setSavedActions']),

    async doCreate() {
      const savedAction = {
        type: SavedActionsType.DELETE_THEME.name,
        components: {
          newLabel: this.localLabel,
          originalLabel: this.localLabel,
        },
      };

      await savedActionApi.createSavedAction(this.activeWorkspace?.id, [savedAction]);
    },

    async doUpdate() {
      return savedActionApi.updateSavedAction(this.activeWorkspace?.id, {
        ...this.selected,
        components: {
          newLabel: this.localLabel,
          originalLabel: this.localLabel,
        },
      });
    },

    async loadSavedActions() {
      const savedActions = await savedActionApi.getSavedActions(this.activeWorkspace?.id);
      this.setSavedActions({ savedActions });
    },

    async onClickSubmit() {
      if (!this.validInput) {
        return;
      }

      this.loading = true;
      if (this.selected) {
        await this.doUpdate();
      } else {
        await this.doCreate();
      }
      await this.loadSavedActions();

      this.resetState();
    },

    resetState() {
      this.selectSavedAction({ id: null });
      this.setupData();
    },

    setupData() {
      this.localLabel = this.selected?.components?.newLabel || '';
      this.validInput = false;
      this.loading = false;
    },
  },

  watch: {
    localLabel() {
      this.validInput = this.localLabel?.trim().length > 0;
      if (this.validInput) {
        this.validInput = (this.getActionByNewLabel(this.localLabel?.trim(), SavedActionsType.DELETE_THEME) == null);
      }
    },

    selected() {
      this.setupData();
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.excluded-edit-action {
  @include flex("block", "column", "start", "stretch");
  @include rigid;

  border-bottom: $border-standard;
  padding-bottom: 1rem;
  margin: 1rem 0;
  max-height: 50%;
  overflow-y: auto;

  .base-button {
    &.disabled-true {
      cursor: not-allowed;
    }
  }

  .edit-state {
    @include flex("block", "row", "between", "center");

    background-color: rgba(#352691, 0.1);
    border-radius: $border-radius-medium;
    font-size: $font-size-sm;
    margin-bottom: 1rem;
    padding: 0.5rem;

    .text {
      @include flex("block", "row", "start", "center");

      overflow: hidden;

      .info {
        min-width: fit-content;
      }

      .label {
        @include truncate;

        font-weight: $font-weight-bold;
        margin-left: 0.5rem;
      }
    }

    .controls {
      @include flex("block", "row", "start", "center");

      .base-button {
        font-size: $font-size-xxs;
        margin-left: 0.5rem;
        padding: 0.4rem 0.8rem;
      }
    }
  }

  .edit-controls {
    @include flex("block", "row", "start", "center");
    @include stretch;

    .label-section {
      border-right: $border-standard;
      padding-right: 1rem;
      width: 100%;

      .base-input {
        font-size: $font-size-xs;
      }
    }

    .apply {
      min-width: fit-content;
      padding-left: 1rem;

      .loading-blocks-overlay {
        height: 2rem;
      }
    }
  }
}
</style>
