<template>
  <section class="memory-excluded-theme" :class="{ deleting, editing: isEditing }" @click="onClick">
    <section class="active-bar"></section>
    <section class="label">{{ themeLabel }}</section>
    <section class="controls">
      <section class="control-button edit-button">
        <edit-icon class="icon" @click.prevent.stop="onClickEdit"></edit-icon>
      </section>
      <section class="control-button delete-button">
        <trash-2-icon class="icon" @click.prevent.stop="onClickDelete"></trash-2-icon>
      </section>
    </section>
  </section>
</template>

<script>
import { EditIcon, Trash2Icon } from 'vue-feather-icons';
import { mapActions, mapState } from 'vuex';

import { savedActionApi } from '@/services/api';

export default {
  name: 'memory-excluded-theme',

  components: {
    EditIcon,
    Trash2Icon,
  },

  props: {
    action: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapState('savedActions', ['selected']),

    ...mapState('user', ['activeWorkspace']),

    isEditing() {
      return this.selected?.id === this.action.id;
    },

    themeLabel() {
      return this.action?.components?.newLabel || '';
    },
  },

  data() {
    return {
      deleting: false,
    };
  },

  methods: {
    ...mapActions('savedActions', ['selectSavedAction', 'setSavedActions']),

    async loadSavedActions() {
      const savedActions = await savedActionApi.getSavedActions(this.activeWorkspace?.id);
      this.setSavedActions({ savedActions });
    },

    onClick() {
      if (this.isEditing) {
        this.selectSavedAction({ id: null });
      } else {
        this.selectSavedAction({ id: this.action.id });
      }
    },

    async onClickDelete() {
      if (this.deleting) return;
      this.deleting = true;

      const deletingId = this.action.id;
      await savedActionApi.deleteSavedActions(this.activeWorkspace?.id, [deletingId]);
      await this.loadSavedActions();

      if (this.selected?.id === deletingId) {
        this.selectSavedAction({ id: null });
      }
    },

    onClickEdit() {
      this.selectSavedAction({ id: this.action.id });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.memory-excluded-theme {
  @include flex("block", "row", "start", "center");
  @include rigid;
  cursor: pointer;
  height: 40px;

  &:hover {
    background-color: rgba(0,27,255,0.04);

    .active-bar {
      width: 4px;
    }
  }

  &.deleting {
    background-color: rgba(clr('red'), 0.1);
  }

  &.editing {
    background-color: rgba(#352691, 0.1);

    .active-bar {
      width: 4px;
    }
  }

  .active-bar {
    @include flex("block", "row", "start", "stretch");
    @include rigid;

    background-color: rgba(#352691, 0.4);
    height: 100%;
    transition: width $interaction-transition-time;
    width: 0;
  }

  .label {
    @include truncate;

    font-weight: $font-weight-bold;
    margin-left: 0.5rem;
    width: 100%;
  }

  .controls {
    @include flex("block", "row", "start", "center");

    margin: 0 1rem;

    .control-button {
      @include flex("block", "row", "center", "center");

      opacity: 0.5;
      transition: opacity $interaction-transition-time;

      &.delete-button {
        margin-left: 1rem;

        .icon {
          color: clr('red');
        }
      }

      &:hover {
        opacity: 1;
      }

      .icon {
        height: $font-size-base;
        width: $font-size-base;
      }
    }
  }
}
</style>
