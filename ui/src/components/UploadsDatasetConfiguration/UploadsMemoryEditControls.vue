<template>
  <section class="uploads-memory-edit-controls">
    <section class="text">
      <section class="info">Currently editing:</section>
      <section class="label">
        <slot></slot>
      </section>
    </section>

    <section class="controls">
      <base-button colour="dark" @click="onClickClear">Clear</base-button>
    </section>
  </section>
</template>

<script>
import BaseButton from '@/components/Base/BaseButton';

export default {
  name: 'uploads-memory-edit-controls',

  components: {
    BaseButton,
  },

  methods: {
    onClickClear() {
      this.$emit('clear');
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.uploads-memory-edit-controls {
  @include flex("block", "row", "between", "center");

  background-color: rgba(#352691, 0.1);
  border-radius: $border-radius-medium;
  font-size: $font-size-sm;
  margin-bottom: 1rem;
  padding: 0.5rem;

  .text {
    @include flex("block", "row", "start", "center");

    overflow: hidden;

    .label {
      @include truncate;

      font-weight: $font-weight-bold;
      margin-left: 0.5rem;
    }
  }

  .controls {
    @include flex("block", "row", "start", "center");

    .base-button {
      font-size: $font-size-xxs;
      margin-left: 0.5rem;
      padding: 0.4rem 0.8rem;
    }
  }
}
</style>
