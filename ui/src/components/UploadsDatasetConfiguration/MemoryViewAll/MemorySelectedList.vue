<template>
  <section class="memory-selected-list">
    <section class="left">
      <span v-if="creatingNew" class="label">New Collection</span>
      <span v-else class="label">{{ selectedList.label }}</span>
    </section>

    <section v-if="creatingNew" class="right">
      <base-input :focus="true" placeholder="Enter a name..." v-model="newLabel"/>

      <base-button colour="dark" size="small" @click="onSaveNew">Save</base-button>
      <base-button colour="light" size="small" @click="onCancelNew">Cancel</base-button>
    </section>

    <section v-else class="right">
      <base-button colour="dark" size="small" @click="onSave">Save Collection</base-button>
      <base-button colour="dark" size="small" @click="creatingNew = true">Save As New Collection...</base-button>
      <base-button colour="light" size="small" @click="onClearSelected">Clear</base-button>
    </section>
  </section>
</template>

<script>
export default {
  name: 'memory-selected-list',
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.memory-selected-list {
}
</style>
