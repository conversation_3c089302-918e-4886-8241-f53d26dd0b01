<template>
  <section class="memory-theme-list">
    <section class="header">
      <section class="left">
        <component class="icon" v-if="showIcon" :is="showIcon"/>
        <span class="text">{{ headerText }}</span>
      </section>
      <section class="right">
        <base-button colour="dark" size="small" @click="onClickEdit">Edit</base-button>
      </section>
    </section>

    <section v-if="hasActions" class="search">
      <base-input v-model="searchText" :placeholder="placeholderText"/>
    </section>

    <section v-if="hasActions" class="item-all" @click="toggleAll">
      <base-checkbox :value="isAllSelected"/>
      <span class="text">Select All Themes</span>
    </section>

    <section v-if="hasActions" class="list">
      <section v-for="(item, i) in filteredList"
               :key="i"
               class="list-item"
               :class="{ active: isActive(item) }"
               @click="onSelect(item)"
      >
        <base-checkbox :value="isActive(item)"/>
        <span class="text">
          {{ item.components.parentId ? `${getParentLabel(item.components.parentId)} ▶` : '' }}
          {{ item.components.newLabel || '' }}
        </span>
      </section>
    </section>

    <section v-if="hasActions && filteredList.length === 0 && searchText !== ''" class="empty empty-search">
      No matches found.
    </section>

    <section v-if="!hasActions" class="empty">
      <section class="text">{{ emptyText }}</section>
      <base-button colour="dark" size="small" @click="onClickEdit">Create</base-button>
    </section>
  </section>
</template>

<script>
import { GitMergeIcon, SearchIcon, TypeIcon, XOctagonIcon } from 'vue-feather-icons';
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseCheckbox from '@/components/Base/BaseCheckbox';
import BaseInput from '@/components/Base/BaseInput';
import SavedActionsType from '@/enum/saved-actions-type';

const typeMap = {
  [SavedActionsType.CREATE_SEARCH_THEME]: {
    emptyText: 'No "Search Theme" actions exist',
    icon: SearchIcon,
    placeholderText: 'Find Searched Theme...',
  },
  [SavedActionsType.DELETE_THEME]: {
    emptyText: 'No "Delete Theme" actions exist',
    icon: XOctagonIcon,
    placeholderText: 'Find Deleted Theme...',
  },
  [SavedActionsType.MERGE_THEMES]: {
    emptyText: 'No "Merge Themes" actions exist',
    icon: GitMergeIcon,
    placeholderText: 'Find Merged Theme...',
  },
  [SavedActionsType.RENAME_THEME]: {
    emptyText: 'No "Rename Theme" actions exist',
    icon: TypeIcon,
    placeholderText: 'Find Renamed Theme...',
  },
};

export default {
  name: 'memory-theme-list',

  components: {
    BaseButton,
    BaseCheckbox,
    BaseInput,
    GitMergeIcon,
    SearchIcon,
    TypeIcon,
    XOctagonIcon,
  },

  props: {
    actions: {
      type: Array,
      required: true,
    },

    actionType: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      searchText: '',
    };
  },

  computed: {
    ...mapState('savedActions', ['actionsToUpload', 'savedActions']),

    emptyText() {
      return typeMap[this.actionType]?.emptyText || 'No actions of this type exist';
    },

    filteredList() {
      return this.actions
        .filter(item => item?.components?.newLabel.toLowerCase().indexOf(this.searchText.toLowerCase()) !== -1);
    },

    hasActions() {
      return this.actions.length > 0;
    },

    headerText() {
      return this.actionType.text();
    },

    isAllSelected() {
      return this.actions.every(item => this.actionsToUpload.includes(item.id));
    },

    placeholderText() {
      return typeMap[this.actionType]?.placeholderText || 'Find Saved Actions...';
    },

    showIcon() {
      return typeMap[this.actionType]?.icon;
    },
  },

  methods: {
    ...mapActions('savedActions', ['addSavedActionsToUpload', 'removeSavedActionsFromUpload']),

    isActive(item) {
      return this.actionsToUpload.includes(item.id);
    },

    getParentLabel(id) {
      return this.actions.find(action => action.id === id)?.components?.newLabel;
    },

    onClickEdit() {
      this.$emit('click-edit', this.actionType);
    },

    onSelect(item) {
      if (this.isActive(item)) {
        this.removeSavedActionsFromUpload({ ids: [item.id] });
      } else {
        this.addSavedActionsToUpload({ ids: [item.id] });
      }
    },

    toggleAll() {
      if (this.isAllSelected) {
        this.filteredList.forEach(item => this.removeSavedActionsFromUpload({ ids: [item.id] }));
      } else {
        const inactiveList = this.filteredList.filter(item => !this.isActive(item));
        inactiveList.forEach(item => this.addSavedActionsToUpload({ ids: [item.id] }));
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.memory-theme-list {
  @include flex("block", "column", "start", "stretch");

  border-right: 1px solid $border-color;
  overflow-y: hidden;

  &:last-child {
    border: none;
  }

  .header {
    @include flex("block", "row", "between", "center");

    margin-top: 1rem;
    padding-right: 1rem;

    .left {
      @include flex("block", "row", "start", "center");

      font-size: $font-size-sm;
      font-weight: $font-weight-bold;

      .icon {
        height: 1rem;
        margin-right: 0.5rem;
        width: 1rem;
      }
    }

    .right {
      .base-button {
        padding: 0.3rem 0.6rem;
      }
    }
  }

  .search {
    margin-top: 0.5rem;
    padding-right: 1rem;

    .base-input {
      font-size: $font-size-sm;
      padding: 0.4rem 0.8rem;
    }
  }

  .item-all {
    @include flex("block", "row", "start", "center");

    border-bottom: 1px solid $border-color;
    cursor: pointer;
    margin-top: 0.5rem;
    margin-right: 1rem;
    padding: 0.4rem;

    &:hover {
      background-color: rgba(0,27,255,0.1);;
    }

    .base-checkbox {
      margin-right: 0.5rem;
    }

    .text {
      font-size: $font-size-xs;
      font-weight: $font-weight-bold;
    }
  }

  .list {
    @include size-evenly;

    overflow-x: hidden;
    overflow-y: auto;
    scrollbar-width: thin;

    .list-item {
      @include flex("block", "row", "start", "center");
      @include truncate;

      cursor: pointer;
      margin-right: 1rem;
      padding: 0.4rem;

      &:hover {
        background-color: rgba(0,27,255,0.1);;
      }

      .base-checkbox {
        margin-right: 0.5rem;
        min-width: 1rem;
      }

      .text {
        @include truncate;

        font-size: $font-size-xs;
      }
    }
  }

  .empty {
    @include flex("block", "column", "center", "center");

    color: #352691;
    font-size: $font-size-xs;
    height: 100%;

    .base-button {
      margin-top: 0.5rem;
      padding: 0.3rem 0.6rem;
    }
  }
}
</style>
