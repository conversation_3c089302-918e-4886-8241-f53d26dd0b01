<template>
  <section class="memory-merge-theme" :class="{ deleting, editing: isEditing }" @click="onClick">
    <section class="active-bar"></section>

    <section class="label">{{ label }}</section>

    <section class="merge-labels">
      <section class="labels">
        {{ mergeLabels.join(', ') }}
      </section>
    </section>

    <section class="controls">
      <section class="control-button edit-button">
        <edit-icon class="icon" @click.prevent.stop="onClickEdit"/>
      </section>

      <section class="control-button delete-button">
        <trash-2-icon class="icon" @click.prevent.stop="onClickDelete"/>
      </section>
    </section>
  </section>
</template>

<script>
import { EditIcon, Trash2Icon } from 'vue-feather-icons';
import { mapActions, mapState } from 'vuex';

import { savedActionApi } from '@/services/api';

export default {
  name: 'memory-merge-theme',

  components: {
    EditIcon, Trash2Icon,
  },

  props: {
    action: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      deleting: false,
    };
  },

  computed: {
    ...mapState('savedActions', ['selected']),

    ...mapState('user', ['activeWorkspace']),

    isEditing() {
      return this.selected === this.action.id;
    },

    label() {
      return this.action.components.newLabel;
    },

    mergeLabels() {
      return this.action.components.mergeThemesLabels;
    },
  },

  methods: {
    ...mapActions('savedActions', ['selectSavedAction', 'setSavedActions']),

    onClick() {
      if (this.selected != null) this.selectSavedAction({ id: null });
      else this.selectSavedAction({ id: this.action.id });
    },

    async onClickDelete() {
      this.deleting = true;

      await savedActionApi.deleteSavedActions(this.activeWorkspace?.id, [this.action.id]);

      const savedActions = await savedActionApi.getSavedActions(this.activeWorkspace?.id);
      this.setSavedActions({ savedActions });

      this.deleting = false;
    },

    onClickEdit() {
      this.selectSavedAction({ id: this.action.id });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.memory-merge-theme {
  @include flex("block", "row", "start", "center");
  @include rigid;

  cursor: pointer;
  height: 40px;

  &:hover {
    background-color: rgba(0,27,255,0.04);

    .active-bar {
      width: 4px;
    }
  }

  &.deleting {
    background-color: rgba(clr('red'), 0.1);
  }

  &.editing {
    background-color: rgba(#352691, 0.1);

    .active-bar {
      width: 4px;
    }
  }

  .active-bar {
    @include flex("block", "row", "start", "stretch");
    @include rigid;

    background-color: rgba(#352691, 0.4);
    height: 100%;
    transition: width $interaction-transition-time;
    width: 0;
  }

  .label {
    @include truncate;

    color: #352691;
    font-weight: $font-weight-bold;
    margin-left: 0.5rem;
    min-width: 30%;
    width: 30%;
  }

  .merge-labels {
    @include flex("block", "row", "start", "stretch");
    @include stretch;
    @include truncate;

    .labels {
      @include truncate;

      color: #352691;
      font-style: italic;
      margin-left: 1rem;
    }
  }

  .controls {
    @include flex("block", "row", "start", "center");

    margin: 0 1rem;

    .control-button {
      @include flex("block", "row", "center", "center");

      opacity: 0.5;
      transition: opacity $interaction-transition-time;

      &.delete-button {
        margin-left: 1rem;

        .icon {
          color: clr('red');
        }
      }

      &:hover {
        opacity: 1;
      }

      .icon {
        height: $font-size-base;
        width: $font-size-base;
      }
    }
  }
}
</style>
