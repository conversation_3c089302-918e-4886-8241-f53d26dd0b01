<template>
  <section class="memory-edit-action">
    <uploads-memory-edit-controls v-if="selected != null" @clear="resetState">
      {{ themeLabel }}
    </uploads-memory-edit-controls>

    <section v-if="!conflict" class="edit-controls">
      <section class="labels-section merge-labels">
        <section v-for="(item, index) in mergeList" :key="index" class="item">
          <base-input :data-index="index"
            placeholder="Enter Theme Name to Merge..."
            :value="item"
            @blur="onLabelBlur"
            @input="onLabelInput"
            @submit="onLabelSubmit"
          />
        </section>
      </section>

      <section class="labels-section new-label">
        <base-input v-model="newLabel" placeholder="New Theme Label..."/>
      </section>

      <section class="apply">
        <base-button colour="dark" size="small" @click="onClickCreate" :disabled="saveDisabled">
          {{ selected == null ? 'Create' : 'Update' }} Merge Action
        </base-button>
      </section>
    </section>

    <uploads-memory-conflict v-else @cancel="onClickCancel" @delete="onClickDelete"/>
  </section>
</template>

<script>
import { difference } from 'lodash-es';
import { PlusIcon } from 'vue-feather-icons';
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import SavedActionsType from '@/enum/saved-actions-type';
import UploadsMemoryConflict from '@/components/UploadsDatasetConfiguration/UploadsMemoryConflict';
import UploadsMemoryEditControls from '@/components/UploadsDatasetConfiguration/UploadsMemoryEditControls';

import { savedActionApi } from '@/services/api';

export default {
  name: 'memory-edit-action',

  components: {
    BaseButton,
    BaseInput,
    PlusIcon,
    UploadsMemoryConflict,
    UploadsMemoryEditControls,
  },

  props: {
    action: {
      type: Object,
      required: false,
    },
  },

  data() {
    return {
      conflict: false,
      mergeThemesLabels: [],
      newLabel: '',
    };
  },

  computed: {
    ...mapState('savedActions', ['selected']),

    ...mapState('user', ['activeWorkspace']),

    components() {
      return {
        mergeThemesLabels: this.mergeThemesLabels,
        newLabel: this.newLabel,
      };
    },

    mergeList() {
      return [...this.mergeThemesLabels, ''];
    },

    saveDisabled() {
      return this.mergeThemesLabels.length === 0 || this.mergeThemesLabels.includes('')
        || this.newLabel === '' || this.selectedAndNewAreSame;
    },

    selectedAndNewAreSame() {
      const mapper = str => str.trim().toLowerCase();

      const newLabel = this.newLabel.trim().toLowerCase();
      const selectedLabel = this.selected?.components?.newLabel?.trim().toLowerCase();

      const mergeLabels = this.mergeThemesLabels.map(mapper);
      const selectedMergeLabels = this.selected?.components?.mergeThemesLabels?.map(mapper);

      return newLabel === selectedLabel && difference(mergeLabels, selectedMergeLabels).length === 0;
    },

    themeLabel() {
      return this.selected?.components?.newLabel || '';
    },
  },

  watch: {
    selected() {
      this.setupData();
    },
  },

  methods: {
    ...mapActions('savedActions', ['selectSavedAction', 'setSavedActions']),

    async loadSavedActions() {
      const savedActions = await savedActionApi.getSavedActions(this.activeWorkspace?.id);

      this.setSavedActions({ savedActions });
    },

    onClickCancel() {
      this.conflict = false;
    },

    async onClickCreate() {
      let success = true;

      if (this.selected != null) {
        const newSavedAction = { ...this.selected, components: this.components };

        const { error } = await savedActionApi.updateSavedAction(this.activeWorkspace?.id, newSavedAction);

        if (error?.response?.status === 409) {
          this.conflict = true;
          success = false;
        }
      } else {
        const savedAction = {
          type: SavedActionsType.MERGE_THEMES.name,
          components: this.components,
        };

        await savedActionApi.createSavedAction(this.activeWorkspace?.id, [savedAction]);
      }

      if (success) {
        await this.loadSavedActions();

        this.resetState();
      }
    },

    async onClickDelete() {
      await savedActionApi.deleteSavedActions(this.activeWorkspace?.id, [this.selected.id]);
      await this.loadSavedActions();

      this.resetState();
    },

    onLabelBlur(value) {
      if (value === '') {
        this.mergeThemesLabels = this.mergeThemesLabels.filter(item => item !== '');
      }
    },

    onLabelInput(value, element) {
      const mergeThemesLabelsCopy = [...this.mergeThemesLabels];

      mergeThemesLabelsCopy[element.dataset.index] = value;

      this.mergeThemesLabels = mergeThemesLabelsCopy;
    },

    onLabelSubmit(_, element) {
      element.blur();
    },

    resetState() {
      this.selectSavedAction({ id: null });
      this.setupData();
    },

    setupData() {
      this.conflict = false;
      this.mergeThemesLabels = this.selected?.components?.mergeThemesLabels || [];
      this.newLabel = this.selected?.components?.newLabel || '';
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.memory-edit-action {
  @include flex("block", "column", "start", "stretch");
  @include rigid;

  border-bottom: $border-standard;
  padding-bottom: 1rem;
  margin: 1rem 0;
  max-height: 50%;
  overflow: hidden;

  .base-button {
    &.disabled-true {
      cursor: not-allowed;
    }
  }

  .edit-controls {
    @include flex("block", "row", "start", "stretch");
    @include stretch;

    overflow: hidden;

    .labels-section {
      border-right: $border-standard;
      padding: 0 1rem;

      .base-input {
        font-size: $font-size-xs;
      }
    }

    .merge-labels {
      @include size-evenly;

      overflow-y: auto;
      padding-left: 0;

      .item {
        @include flex("block", "row", "start", "center");

        &:not(:last-child) {
          margin-bottom: 0.5rem;
        }

        .base-button {
          padding: 0.2rem;
          margin-left: 1rem;
        }
      }
    }

    .new-label {
      @include size-evenly;
    }

    .apply {
      @include rigid;

      padding-left: 1rem;
    }
  }
}
</style>
