<template>
  <section class="uploads-memory-lists">
    <section v-if="selectedList != null" class="selected">
      <section class="left">
        <span class="info">Selected:</span>
        <span class="label" :title="selectedList.label">{{ selectedList.label }}</span>
      </section>

      <section class="right">
        <base-button colour="dark" size="small" @click="onView">View/Edit</base-button>
        <base-button colour="light" size="small" @click="onClearSelected">Clear</base-button>
      </section>
    </section>

    <section v-if="savedActionLists.length > 0" class="list">
      <memory-lists-item v-for="item in savedActionLists"
        :key="item.id"
        :actions="actions"
        :item="item"
        @click.native="selectItem(item)"
        @delete="onDelete"
      />
    </section>

    <section v-else class="no-lists">
      <span class="text">No Collections exist. Create one in the "Select Actions" tab.</span>
      <base-button colour="dark" size="small" @click="onClickTab">Go to Select Actions</base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import MemoryListsItem from '@/components/UploadsDatasetConfiguration/MemoryLists/MemoryListsItem';

import { savedActionApi } from '@/services/api';

export default {
  name: 'uploads-memory-lists',

  components: {
    BaseButton,
    MemoryListsItem,
  },

  computed: {
    ...mapState('savedActions', [
      'savedActions',
      'savedActionLists',
      'selectedList',
    ]),

    ...mapGetters('savedActions', [
      'actionsCreateSearchTheme',
      'actionsDeleteTheme',
      'actionsMergeThemes',
      'actionsRenameTheme',
    ]),

    ...mapState('user', ['activeWorkspace']),

    actions() {
      return {
        delete: this.actionsDeleteTheme.map(a => a.id),
        merge: this.actionsMergeThemes.map(a => a.id),
        rename: this.actionsRenameTheme.map(a => a.id),
        search: this.actionsCreateSearchTheme.map(a => a.id),
      };
    },

    allIds() {
      return this.savedActions.map(a => a.id);
    },
  },

  methods: {
    ...mapActions('savedActions', [
      'addSavedActionsToUpload',
      'removeSavedActionsFromUpload',
      'setSavedActionLists',
      'setSelectedList',
    ]),

    onClearSelected() {
      this.setSelectedList({ list: null });
      this.removeSavedActionsFromUpload({ ids: this.allIds });
    },

    onClickTab() {
      this.$emit('go-to-select');
    },

    async onDelete(item) {
      await savedActionApi.deleteSavedActionList(this.activeWorkspace?.id, item.id);

      const savedActionLists = await savedActionApi.getSavedActionLists(this.activeWorkspace?.id);
      this.setSavedActionLists({ savedActionLists });
    },

    onView() {
      this.$emit('go-to-select');
    },

    selectItem(item) {
      this.removeSavedActionsFromUpload({ ids: this.allIds });
      this.setSelectedList({ list: item });
      this.addSavedActionsToUpload({ ids: item.list });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.uploads-memory-lists {
  @include flex("block", "column", "start", "stretch");
  @include stretch;

  overflow: hidden;

  .selected {
    @include flex("block", "row", "between", "stretch");

    border-bottom: $border-standard;
    padding: 0.5rem 0;
    margin-bottom: 1rem;

    .left {
      @include flex("block", "row", "start", "center");
      @include stretch;
      @include truncate;

      color: #352691;

      .label {
        @include truncate;

        font-weight: $font-weight-bold;
        margin-left: 0.5rem;
      }
    }

    .right {
      @include flex("block", "row", "center", "center");
      @include rigid;

      .base-button {
        margin-left: 1rem;
        padding: 0.3rem 0.6rem;
      }
    }
  }

  .list {
    @include flex("block", "column", "start", "stretch");
    @include stretch;

    overflow-y: auto;
    scrollbar-width: thin;
  }

  .no-lists {
    @include flex("block", "column", "center", "center");
    @include stretch;

    .base-button {
      margin-top: 1rem;
    }
  }
}
</style>
