<template>
  <section class="uploads-memory-theme-from-search">
    <uploads-memory-edit-controls v-if="selected != null" @clear="onClickClear">
      {{ selected.components.newLabel }}
    </uploads-memory-edit-controls>

    <searched-theme-search-bar @open-dropdown="open = true" @close="open = false"/>
    <searched-theme-list v-if="hasItems" :class="{open}" />

    <!-- Empty Msg -->
    <section v-else class="empty">
      <span class="text">No "Search Theme" actions exist.</span>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import SearchedThemeList from '@/components/UploadsDatasetConfiguration/MemorySearchedTheme/SearchedThemeList';
import SearchedThemeSearchBar from '@/components/UploadsDatasetConfiguration/MemorySearchedTheme/SearchedThemeSearchBar';
import UploadsMemoryEditControls from '@/components/UploadsDatasetConfiguration/UploadsMemoryEditControls';

export default {
  name: 'uploads-memory-theme-from-search',

  components: {
    BaseButton,
    SearchedThemeList,
    SearchedThemeSearchBar,
    UploadsMemoryEditControls,
  },

  data() {
    return {
      open: false,
    };
  },

  computed: {
    ...mapState('savedActions', ['selected']),

    ...mapGetters('savedActions', ['actionsCreateSearchTheme']),

    hasItems() {
      return this.actionsCreateSearchTheme.length > 0;
    },
  },

  created() {
    this.resetSelected();
  },

  methods: {
    ...mapActions('savedActions', ['resetSelected', 'selectSavedAction']),

    onClickClear() {
      this.selectSavedAction({ id: null });
    },
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.uploads-memory-theme-from-search {
  @include flex("block", "column", "start", "stretch");
  @include stretch;

  width: 100%;

  .searched-theme-list {
    border-top: $border-standard;
    margin-top: 1rem;
    padding-top: 1rem;

    &.open {
      margin-top: 3.5rem;
    }
  }

  .empty {
    @include flex("block", "row", "center", "center");
    @include stretch;

    color: #352691;
  }
}
</style>
