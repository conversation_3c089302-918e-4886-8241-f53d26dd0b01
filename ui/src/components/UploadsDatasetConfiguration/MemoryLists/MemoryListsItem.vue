<template>
  <section class="memory-lists-item" :class="{ active, deleting }">
    <section class="active-bar"></section>
    <section class="left">
      <section class="label" :title="item.label">{{ item.label }}</section>
      <section class="info">
        <span class="count">
          {{ searchCount }} Searched Theme{{ searchCount === 1 ? '' : 's' }}
        </span>

        <span class="count">
          {{ mergeCount }} Merged Theme{{ mergeCount === 1 ? '' : 's' }}
        </span>

        <span class="count">
          {{ renameCount }} Renamed Theme{{ renameCount === 1 ? '' : 's' }}
        </span>

        <span class="count">
          {{ deleteCount }} Deleted Theme{{ deleteCount === 1 ? '' : 's' }}
        </span>
      </section>
    </section>

    <section class="delete-button">
      <trash-2-icon class="icon" @click.prevent.stop="onClickDelete"></trash-2-icon>
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';
import { Trash2Icon } from 'vue-feather-icons';

const reduceToSum = (listIds, allIds) => {
  return listIds.reduce((acc, id) => {
    if (allIds.includes(id)) acc += 1;

    return acc;
  }, 0);
};

export default {
  name: 'memory-lists-item',

  components: {
    Trash2Icon,
  },

  data() {
    return {
      deleting: false,
    };
  },

  props: {
    actions: {
      type: Object,
      required: true,
    },

    item: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapState('savedActions', ['selectedList']),

    active() {
      return this.selectedList?.id === this.item.id;
    },

    deleteCount() {
      return reduceToSum(this.item.list, this.actions.delete);
    },

    mergeCount() {
      return reduceToSum(this.item.list, this.actions.merge);
    },

    renameCount() {
      return reduceToSum(this.item.list, this.actions.rename);
    },

    searchCount() {
      return reduceToSum(this.item.list, this.actions.search);
    },
  },

  methods: {
    async onClickDelete() {
      this.deleting = true;

      this.$emit('delete', this.item);
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.memory-lists-item {
  @include flex("block", "row", "between", "center");

  cursor: pointer;
  height: 40px;
  overflow: hidden;

  &:hover {
    background-color: rgba(0,27,255,0.04);

    .active-bar {
      width: 4px;
    }
  }

  &.active {
    background-color: rgba(0,27,255,0.04);
  }

  &.deleting {
    background-color: rgba(clr('red'), 0.1);
  }

  .active-bar {
    @include flex("block", "row", "start", "stretch");
    @include rigid;

    background-color: rgba(#352691, 0.4);
    height: 100%;
    transition: width $interaction-transition-time;
    width: 0;
  }

  .left {
    @include flex("block", "row", "between", "center");
    @include stretch;

    overflow: hidden;
    margin-left: 0.5rem;

    .label {
      @include stretch;
      @include truncate;

      color: #352691;
      font-weight: $font-weight-bold;
      width: 38%;
    }

    .info {
      @include flex("block", "row", "start", "center");
      @include stretch;

      color: #352691;
      font-style: italic;
      margin-left: 0.5rem;
      width: 62%;

      .count {
        @include flex("block", "row", "start", "center");
        @include size-evenly;

        font-size: $font-size-sm;
        margin-right: 1rem;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .delete-button {
    @include flex("block", "row", "center", "center");
    @include rigid;

    margin: 0 1rem;
    opacity: 0.5;
    transition: opacity $interaction-transition-time;

    &:hover {
      opacity: 1;
    }

    .icon {
      color: clr('red');
      height: $font-size-base;
      width: $font-size-base;
    }
  }
}
</style>
