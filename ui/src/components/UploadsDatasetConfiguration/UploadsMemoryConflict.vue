<template>
  <section class="uploads-memory-conflict">
    <section class="text">An action with these changes already exists. Delete this action instead to continue.</section>
    <section class="controls">
      <base-button colour="light" size="small" type="outline" @click="onClickCancel">Cancel</base-button>
      <base-button colour="danger" size="small" @click="onClickDelete">Delete</base-button>
    </section>
  </section>
</template>

<script>
import BaseButton from '@/components/Base/BaseButton';

export default {
  name: 'uploads-memory-conflict',

  components: {
    BaseButton,
  },

  methods: {
    onClickCancel() {
      this.$emit('cancel');
    },

    onClickDelete() {
      this.$emit('delete');
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.uploads-memory-conflict {
  @include flex("block", "row", "between", "center");

  font-size: $font-size-xs;

  .base-button {
    font-size: $font-size-xxs;
    margin-left: 0.5rem;
    padding: 0.4rem 0.8rem;
  }
}
</style>
