<template>
  <section class="renamed-edit-action">
    <uploads-memory-edit-controls v-if="selected != null" @clear="resetState">
      {{ themeLabel }}
    </uploads-memory-edit-controls>

    <section v-if="!conflict" class="edit-controls">
      <section class="label-section label-new">
        <base-input v-model="localNewLabel" placeholder="New Label"/>
      </section>

      <section class="label-section label-old">
        <base-input v-model="localOldLabel" placeholder="Original Label"/>
      </section>

      <section class="apply">
        <loading-blocks-overlay v-if="loading" class="loading" />
        <base-button v-else colour="dark" size="small" :disabled="disabled" @click="onClickSubmit">
          {{ selected == null ? 'Create' : 'Update' }} Rename Action
        </base-button>
      </section>
    </section>

    <uploads-memory-conflict v-else @cancel="onClickCancel" @delete="onClickDelete"/>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import SavedActionsType from '@/enum/saved-actions-type';
import UploadsMemoryConflict from '@/components/UploadsDatasetConfiguration/UploadsMemoryConflict';
import UploadsMemoryEditControls from '@/components/UploadsDatasetConfiguration/UploadsMemoryEditControls';

import { savedActionApi } from '@/services/api';

export default {
  name: 'renamed-edit-action',

  components: {
    BaseButton,
    BaseInput,
    LoadingBlocksOverlay,
    UploadsMemoryConflict,
    UploadsMemoryEditControls,
  },

  data() {
    return {
      conflict: false,
      loading: false,
      localNewLabel: '',
      localOldLabel: '',
    };
  },

  computed: {
    ...mapState('savedActions', ['selected']),

    ...mapState('user', ['activeWorkspace']),

    disabled() {
      const localOld = this.localOldLabel?.trim().toLowerCase();
      const localNew = this.localNewLabel?.trim().toLowerCase();

      const selectedOld = this.selected?.components?.originalLabel?.trim().toLowerCase();
      const selectedNew = this.selected?.components?.newLabel?.trim().toLowerCase();

      return !this.validNew || !this.validOld
        || (localOld === selectedOld && localNew === selectedNew);
    },

    themeLabel() {
      return this.selected?.components?.newLabel || '';
    },

    validNew() {
      return this.localNewLabel?.trim().length > 0;
    },

    validOld() {
      return this.localOldLabel?.trim().length > 0;
    },
  },

  watch: {
    selected() {
      this.setupData();
    },
  },

  beforeDestroy() {
    this.selectSavedAction({ id: null });
  },

  methods: {
    ...mapActions('savedActions', ['selectSavedAction', 'setSavedActions']),

    async doCreate() {
      const savedAction = {
        type: SavedActionsType.RENAME_THEME.name,
        components: {
          newLabel: this.localNewLabel,
          originalLabel: this.localOldLabel,
        },
      };

      await savedActionApi.createSavedAction(this.activeWorkspace?.id, [savedAction]);
    },

    async doUpdate() {
      return savedActionApi.updateSavedAction(this.activeWorkspace?.id, {
        ...this.selected,
        components: {
          newLabel: this.localNewLabel,
          originalLabel: this.localOldLabel,
        },
      });
    },

    async loadSavedActions() {
      const savedActions = await savedActionApi.getSavedActions(this.activeWorkspace?.id);

      this.setSavedActions({ savedActions });
    },

    onClickCancel() {
      this.conflict = false;
    },

    async onClickDelete() {
      await savedActionApi.deleteSavedActions(this.activeWorkspace?.id, [this.selected.id]);
      await this.loadSavedActions();

      this.resetState();
    },

    async onClickSubmit() {
      if (!this.validNew || !this.validOld) return;

      this.loading = true;
      let success = true;

      if (this.selected) {
        const { error } = await this.doUpdate();

        if (error?.response?.status === 409) {
          this.conflict = true;
          success = false;
        }
      } else await this.doCreate();

      this.loading = false;

      if (success) {
        this.resetState();

        await this.loadSavedActions();
        this.resetState();
      }
    },

    resetState() {
      this.selectSavedAction({ id: null });
      this.setupData();
    },

    setupData() {
      this.conflict = false;
      this.localNewLabel = this.selected?.components?.newLabel || '';
      this.localOldLabel = this.selected?.components?.originalLabel || '';
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.renamed-edit-action {
  @include flex("block", "column", "start", "stretch");
  @include rigid;

  border-bottom: $border-standard;
  padding-bottom: 1rem;
  margin: 1rem 0;
  max-height: 50%;
  overflow-y: auto;

  .base-button {
    &.disabled-true {
      cursor: not-allowed;
    }
  }

  .edit-controls {
    @include flex("block", "row", "start", "center");
    @include stretch;

    .label-section {
      border-right: $border-standard;
      padding-right: 1rem;
      width: 100%;

      &.label-old {
        padding-left: 1rem;
      }

      .base-input {
        font-size: $font-size-xs;
      }
    }

    .apply {
      min-width: fit-content;
      padding-left: 1rem;

      .loading-blocks-overlay {
        height: 2rem;
        width: 10.5rem;
      }
    }
  }
}
</style>
