<template>
  <section class="memory-renamed-theme" :class="{ editing: isEditing }" @click="onClick">
    <section class="active-bar"></section>

    <section class="label label-new">{{ textNewLabel }}</section>
    <section class="label label-old">{{ textOldLabel }}</section>

    <section class="controls">
      <section class="control-button edit-button">
        <edit-icon class="icon" @click.prevent.stop="onClickEdit"></edit-icon>
      </section>

      <section class="control-button delete-button">
        <trash-2-icon class="icon" @click.prevent.stop="onClickDelete"></trash-2-icon>
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { EditIcon, Trash2Icon } from 'vue-feather-icons';

import { savedActionApi } from '@/services/api';

export default {
  name: 'memory-renamed-theme',

  components: {
    EditIcon,
    Trash2Icon,
  },

  props: {
    action: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapState('savedActions', ['selected']),

    ...mapState('user', ['activeWorkspace']),

    isEditing() {
      return this.action.id === this.selected;
    },

    textNewLabel() {
      return this.action?.components?.newLabel || '';
    },

    textOldLabel() {
      return this.action?.components?.originalLabel || '';
    },
  },

  data() {
    return {
      deleting: false,
    };
  },

  methods: {
    ...mapActions('savedActions', ['selectSavedAction', 'setSavedActions']),

    async loadSavedActions() {
      const savedActions = await savedActionApi.getSavedActions(this.activeWorkspace?.id);
      this.setSavedActions({ savedActions });
    },

    onClick() {
      if (this.isEditing) this.selectSavedAction({ id: null });
      else this.selectSavedAction({ id: this.action.id });
    },

    async onClickDelete() {
      if (this.deleting) return;
      this.deleting = true;

      const deletingId = this.action.id;
      await savedActionApi.deleteSavedActions(this.activeWorkspace?.id, [deletingId]);
      await this.loadSavedActions();

      if (this.selected?.id === deletingId) {
        this.selectSavedAction({ id: null });
      }
    },

    onClickEdit() {
      this.selectSavedAction({ id: this.action.id });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.memory-renamed-theme {
  @include flex("block", "row", "between", "center");
  @include rigid;

  cursor: pointer;
  height: 40px;

  &:hover {
    background-color: rgba(0,27,255,0.04);

    .active-bar {
      width: 4px;
    }
  }

  &.editing {
    background-color: rgba(#352691, 0.1);

    .active-bar {
      width: 4px;
    }
  }

  .active-bar {
    @include flex("block", "row", "start", "stretch");
    @include rigid;

    background-color: rgba(#352691, 0.4);
    height: 100%;
    transition: width $interaction-transition-time;
    width: 0;
  }

  .label {
    @include stretch;
    @include truncate;

    color: #352691;
    font-weight: $font-weight-bold;
    margin-left: 0.5rem;

    &.label-new {
      width: 45%;
    }

    &.label-old {
      font-style: italic;
      font-weight: $font-weight-normal;
      width: 55%;
    }
  }

  .controls {
    @include flex("block", "row", "start", "center");

    margin: 0 1rem;

    .control-button {
      @include flex("block", "row", "center", "center");

      opacity: 0.5;
      transition: opacity $interaction-transition-time;

      &.delete-button {
        margin-left: 1rem;

        .icon {
          color: clr('red');
        }
      }

      &:hover {
        opacity: 1;
      }

      .icon {
        height: $font-size-base;
        width: $font-size-base;
      }
    }
  }
}
</style>
