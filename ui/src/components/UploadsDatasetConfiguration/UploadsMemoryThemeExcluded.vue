<template>
  <section class="uploads-memory-theme-excluded">
    <excluded-edit-action />

    <!-- List -->
    <section class="action-list" v-if="dataList.length">
      <memory-excluded-theme v-for="item in dataList"
                             :key="item.id"
                             :action="item"
                             class="action-item"
      >
      </memory-excluded-theme>
    </section>

    <!-- Empty Msg -->
    <section v-else class="empty">
      <span class="text">No "Delete Theme" actions exist.</span>
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';
import ExcludedEditAction from '@/components/UploadsDatasetConfiguration/MemoryExcludedTheme/ExcludedEditAction';
import MemoryExcludedTheme from '@/components/UploadsDatasetConfiguration/MemoryExcludedTheme/MemoryExcludedTheme';
import SavedActionsType from '@/enum/saved-actions-type';

export default {
  name: 'uploads-memory-theme-excluded',

  components: {
    ExcludedEditAction,
    MemoryExcludedTheme,
  },

  computed: {
    ...mapState('savedActions', ['savedActions']),

    dataList() {
      return this.savedActions
        .filter(item => item?.type === SavedActionsType.DELETE_THEME.name)
        .sort((a, b) => {
          const { newLabel: aNewLabel } = a.components;
          const { newLabel: bNewLabel } = b.components;
          return aNewLabel.localeCompare(bNewLabel);
        });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.uploads-memory-theme-excluded {
  @include flex("block", "column", "start", "stretch");
  @include stretch;

  overflow: hidden;

  .action-list {
    @include flex("block", "column", "start", "stretch");
    @include stretch;

    overflow-y: auto;
    scrollbar-width: thin;
  }

  .empty {
    @include flex("block", "row", "center", "center");
    @include stretch;

    color: #352691;
  }
}
</style>
