<template>
  <section class="uploads-memory-main-modal">
    <section class="header">
      <section class="left">
        <section class="top">
          <i class="fa fa-save icon"/>
          <h2>Memory</h2>
        </section>

        <section class="bottom">
          <i class="fa fa-save icon"/>
          Apply actions you have saved to memory to automatically rename, merge or create themes during the upload process.
        </section>
      </section>

      <section class="right">
        <x-icon class="icon" @click="closeModal"/>
      </section>
    </section>

    <section class="tabs">
      <section v-for="tab in tabList"
        :key="tab.index"
        class="tab"
        :class="{ active: activeTab.index === tab.index }"
        @click="onSelectTab(tab)"
      >
        <component class="icon" v-if="tab.icon" :is="tab.icon"></component>
        <span>{{ tab.text }}</span>
      </section>
    </section>

    <section class="body">
      <uploads-memory-lists v-if="activeTab.index === 0" @go-to-select="onGoToSelect"/>
      <uploads-memory-theme-all v-if="activeTab.index === 1" @click-edit="onClickEdit"/>
      <uploads-memory-theme-from-search v-if="activeTab.index === 2"/>
      <uploads-memory-theme-from-merge v-if="activeTab.index === 3"/>
      <uploads-memory-theme-renamed v-if="activeTab.index === 4"/>
      <uploads-memory-theme-excluded v-if="activeTab.index === 5"/>
    </section>

    <section class="footer">
      <base-button colour="light" class="btn-cancel" size="small" type="link" @click="closeModal">Close</base-button>
    </section>
  </section>
</template>

<script>
import { CheckSquareIcon, GitMergeIcon, HelpCircleIcon, ListIcon, SearchIcon, TypeIcon, XIcon, XOctagonIcon } from 'vue-feather-icons';
import { mapActions } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import UploadsMemoryLists from '@/components/UploadsDatasetConfiguration/UploadsMemoryLists';
import UploadsMemoryThemeAll from '@/components/UploadsDatasetConfiguration/UploadsMemoryThemeAll';
import UploadsMemoryThemeExcluded from '@/components/UploadsDatasetConfiguration/UploadsMemoryThemeExcluded';
import UploadsMemoryThemeFromMerge from '@/components/UploadsDatasetConfiguration/UploadsMemoryThemeFromMerge';
import UploadsMemoryThemeFromSearch from '@/components/UploadsDatasetConfiguration/UploadsMemoryThemeFromSearch';
import UploadsMemoryThemeRenamed from '@/components/UploadsDatasetConfiguration/UploadsMemoryThemeRenamed';
import SavedActionsType from '@/enum/saved-actions-type';

const tabs = [
  {
    icon: ListIcon,
    index: 0,
    text: 'Collections',
  },
  {
    icon: CheckSquareIcon,
    index: 1,
    text: 'Select Actions',
  },
  {
    icon: SearchIcon,
    index: 2,
    text: 'Searched Themes',
    type: SavedActionsType.CREATE_SEARCH_THEME,
  },
  {
    icon: GitMergeIcon,
    index: 3,
    text: 'Merged Themes',
    type: SavedActionsType.MERGE_THEMES,
  },
  {
    icon: TypeIcon,
    index: 4,
    text: 'Renamed Themes',
    type: SavedActionsType.RENAME_THEME,
  },
  {
    icon: XOctagonIcon,
    index: 5,
    text: 'Deleted Themes',
    type: SavedActionsType.DELETE_THEME,
  },
];

export default {
  name: 'uploads-memory-main-modal',

  components: {
    BaseButton,
    HelpCircleIcon,
    UploadsMemoryLists,
    UploadsMemoryThemeAll,
    UploadsMemoryThemeExcluded,
    UploadsMemoryThemeFromMerge,
    UploadsMemoryThemeFromSearch,
    UploadsMemoryThemeRenamed,
    XIcon,
  },

  data() {
    return {
      activeTab: tabs[0],
      tabList: [...tabs],
    };
  },

  computed: {
    tabExclTheme() {
      return this.tabList.find(t => t.index === 5);
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    onClickEdit(type) {
      const tab = this.tabList.find(t => t.type === type);

      this.activeTab = tab;
    },

    onGoToSelect() {
      this.activeTab = this.tabList.find(t => t.index === 1);
    },

    onSelectTab(tab) {
      this.activeTab = tab;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.uploads-memory-main-modal {
  @include modal;
  width: 1200px;

  .header {
    @include flex("block", "row", "between", "center");
    border-bottom: 1px solid $border-color;
    padding: 1.5rem 2rem 1.2rem;

    .left {
      @include flex("block", "column", "start", "start");
      @include stretch;

      .top {
        @include flex("block", "row", "start", "center");

        .icon {
          color: $body-copy-light;
          margin-right: 0.5rem;
        }

        h2 {
          font-size: $font-size-base;
          font-weight: $font-weight-bold;
        }
      }

      .bottom {
        @include flex("block", "row", "start", "center");
        @include truncate;
        margin-top: 0.3rem;
        font-size: $font-size-xs;

        .icon {
          font-size: $font-size-base;
          margin-right: 0.5rem;
          visibility: hidden;
        }
      }
    }

    .right {
      cursor: pointer;
    }
  }

  .tabs {
    @include flex("block", "row", "start", "center");
    @include stretch;

    background-color: rgba(72,76,255,0.11);
    border-bottom: 1px solid $border-color;
    padding: 0 2rem;

    .tab {
      @include flex("block", "row", "center", "center");

      color: $body-copy-light;
      cursor: pointer;
      font-size: $font-size-xs;
      font-weight: $font-weight-bold;
      margin-right: 1rem;
      padding: 0.6rem 0;

      &:hover {
        color: $body-copy;
      }

      &.active {
        border-bottom: 2px solid clr("purple");
        color: clr("purple");
      }

      .icon {
        height: 1rem;
        margin-right: 0.3rem;
        width: 1rem;
      }
    }
  }

  .body {
    @include flex("block", "row", "start", "stretch");

    background-color: unset;
    min-height: 500px;
    overflow: hidden;
    padding: 0.5rem 2rem;
  }

  .footer {
    padding: 1rem 2rem;

    .base-button {
      padding: 0.6rem 1.2rem;
    }

    .btn-cancel {
      margin-left: -1.2rem;
    }
  }
}
</style>
