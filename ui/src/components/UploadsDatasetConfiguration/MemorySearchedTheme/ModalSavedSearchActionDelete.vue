<template>
  <section class="modal-saved-search-action-delete">
    <section class="header">
      <h2>Delete saved search action</h2>
      <x-icon class="icon" @click="closeModal"/>
    </section>

    <section class="body">
      <section class="text">
        This action will delete
        <span class="label">&lsquo;{{ label }}&rsquo;</span>
        . Are you sure you wish to continue?
      </section>
    </section>

    <section class="footer">
      <base-button class="cancel" colour="light" type="link" @click="closeModal">Cancel</base-button>
      <base-button class="delete" colour="danger" @click="onClickDelete">Delete saved search action</base-button>
    </section>
  </section>
</template>

<script>
import { XIcon } from 'vue-feather-icons';
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import bus from '@/helpers/bus';
import SearchDeletedToast from '@/components/Search/Toast/SearchDeletedToast';

import { savedActionApi } from '@/services/api';

export default {
  name: 'modal-saved-search-action-delete',

  components: {
    BaseButton,
    XIcon,
  },

  computed: {
    ...mapState('savedActions', ['selected']),

    ...mapState('user', ['activeWorkspace']),

    label() {
      return this.selected?.components?.newLabel;
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('toast', { addToast: 'add' }),

    ...mapActions('savedActions', ['setSavedActions']),

    async onClickDelete() {
      await savedActionApi.deleteSavedActions(this.activeWorkspace?.id, [this.selected.id]);
      const savedActions = await savedActionApi.getSavedActions(this.activeWorkspace?.id);
      this.setSavedActions({ savedActions });

      bus.$emit('clear-theme-from-search-input');
      this.closeModal();
      this.addToast({ toast: { component: SearchDeletedToast, id: 'search-action-deleted' } });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.modal-saved-search-action-delete {
  @include modal;

  .body {
    padding: $search-modal-padding;

    .text {
      font-size: $font-size-xs;
      margin: 0.5rem 0;

      .label {
        font-weight: $font-weight-bold;
      }
    }
  }

  .footer {
    font-size: $font-size-md;

    padding: $search-modal-padding;

    .cancel {
      font-weight: $font-weight-normal;
      padding-left: 0;
    }

    .delete {
      font-weight: $font-weight-medium;
    }
  }

  .header {
    @include flex("block", "row", "between", "center");

    padding: $search-modal-padding;

    h2 {
      @include flex("block", "row", "start", "center");

      font-weight: $font-weight-bold;
    }

    .icon {
      @include flex("block", "row", "end", "center");

      cursor: pointer;
      height: 1.25rem;
      opacity: 0.3;
      &:hover {
        opacity: 1;
      }

      &:active,
      &:focus {
        border: none;
        outline: none;
      }
    }
  }
}
</style>
