<template>
  <section class="modal-saved-search-action-create">
    <section class="header">
      <h2>Save Search Action</h2>
      <x-icon class="icon" @click="cancel"/>
    </section>

    <section class="body">
      <section class="input-section">
        <h3>Theme Name:</h3>
        <base-input ref="input"
          v-model="localLabel"
          @submit="create"
        ></base-input>
      </section>

      <section class="input-section">
        <h3>Add as Subtopic to Theme:</h3>

        <section class="select-parent">
          <base-dropdown :data="parentItems" :open="open" :search="true" @close="open = false" @select="onSelectParent">
            <base-dropdown-button :active="open" @click.prevent.stop="open = !open">
              {{ selectedParent ? selectedParent.components.newLabel : 'Don\'t add to theme' }}
            </base-dropdown-button>
          </base-dropdown>

          <base-button colour="dark" :disabled="selectedParent == null" size="small" @click="selectedParent = null">
            Clear
          </base-button>
        </section>
      </section>
    </section>

    <section class="footer">
      <base-button class="cancel" colour="light" type="link" @click="cancel">Cancel</base-button>
      <base-button class="create" :disabled="saveDisabled" @click="create">Create Search Action</base-button>
    </section>
  </section>
</template>

<script>
import { XIcon } from 'vue-feather-icons';
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseDropdown from '@/components/Dropdown/BaseDropdown';
import BaseDropdownButton from '@/components/Dropdown/BaseDropdownButton';
import BaseInput from '@/components/Base/BaseInput';
import bus from '@/helpers/bus';
import SavedActionsType from '@/enum/saved-actions-type';
import SearchCreatedToast from '@/components/Search/Toast/SearchCreatedToast';

import { savedActionApi } from '@/services/api';

export default {
  name: 'modal-saved-search-action-create',

  components: {
    BaseButton,
    BaseDropdown,
    BaseDropdownButton,
    BaseInput,
    XIcon,
  },

  data() {
    return {
      labelValue: '',
      open: false,
      selectedParent: null,
    };
  },

  computed: {
    ...mapGetters('savedActions', ['actionsSearchThemeParent']),

    ...mapState('search', ['distinct', 'exactSearch']),

    ...mapGetters('search', ['query']),

    ...mapState('user', ['activeWorkspace']),

    localLabel: {
      get() {
        return this.labelValue;
      },

      set(value) {
        this.labelValue = value;
      },
    },

    parentItems() {
      return this.actionsSearchThemeParent.map(action => {
        return {
          content: action.components.newLabel,
          value: action.id,
        };
      });
    },

    saveDisabled() {
      return this.labelValue == null || this.labelValue === '';
    },
  },

  mounted() {
    this.$refs.input.$el.focus();
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('savedActions', ['setSavedActions']),

    ...mapActions('toast', { addToast: 'add' }),

    cancel() {
      this.closeModal();
    },

    async create() {
      if (this.saveDisabled) return;

      const params = {
        newLabel: this.labelValue,
        searchQuery: this.query,
        searchExact: this.exactSearch,
        searchDistinctContent: this.distinct,
      };

      if (this.selectedParent != null) params.parentId = this.selectedParent.id;

      const savedAction = {
        type: SavedActionsType.CREATE_SEARCH_THEME.name,
        components: params,
      };

      await savedActionApi.createSavedAction(this.activeWorkspace?.id, [savedAction]);

      const savedActions = await savedActionApi.getSavedActions(this.activeWorkspace?.id);
      this.setSavedActions({ savedActions });

      bus.$emit('clear-theme-from-search-input');
      this.closeModal();
      this.addToast({ toast: { component: SearchCreatedToast, id: 'search-action-created' } });
    },

    onSelectParent(item) {
      const selectedParent = this.actionsSearchThemeParent.find(action => action.id === item.value);

      this.selectedParent = selectedParent;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.modal-saved-search-action-create {
  @include modal;

  .body {
    padding: $search-modal-padding;

    .input-section {
      @include flex("block", "column", "start", "stretch");

      margin-bottom: 1rem;

      &:last-child {
        margin-bottom: 0;
      }

      h3 {
        font-size: $font-size-xs;
        font-weight: $font-weight-bold;
      }

      .base-input {
        background-color: clr("white");
        border: $border-standard;
        border-radius: $border-radius-medium;
        margin-top: 1rem;
        padding: 0.5rem;
      }

      .select-parent {
        @include flex("block", "row", "start", "center");

        margin-top: 1rem;

        .base-dropdown {
          @include stretch;
        }

        .base-button {
          margin-left: 1rem;
        }
      }
    }
  }

  .footer {
    font-size: $font-size-md;

    padding: $search-modal-padding;

    .cancel {
      font-weight: $font-weight-normal;
      padding-left: 0;
    }

    .create {
      font-weight: $font-weight-medium;
    }
  }

  .header {
    @include flex("block", "row", "between", "center");

    padding: $search-modal-padding;

    h2 {
      @include flex("block", "row", "start", "center");

      font-weight: $font-weight-bold;
    }

    .icon {
      @include flex("block", "row", "end", "center");

      cursor: pointer;
      height: 1.25rem;
      opacity: 0.3;
      &:hover {
        opacity: 1;
      }

      &:active,
      &:focus {
        border: none;
        outline: none;
      }
    }
  }
}
</style>
