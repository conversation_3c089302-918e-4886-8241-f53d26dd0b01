<template>
  <section class="searched-theme-list">
    <loading-switch :status="loaderStatus">
      <template #default>
        <section v-if="searchItems.length > 0" class="list">
          <recycle-scroller
              class="scroller"
              :items="searchItems"
              :item-size="46"
              v-slot="{ item }"
          >
            <searched-theme-item
                class="list-item"
                :data="item"
            ></searched-theme-item>
          </recycle-scroller>
        </section>
      </template>
      <template #loading>
        <loading-blocks-overlay class="loader">{{ loaderText }}</loading-blocks-overlay>
      </template>
    </loading-switch>
  </section>
</template>

<script>
import { RecycleScroller } from 'vue-virtual-scroller';

import BlurCloseable from '@/components/Mixins/BlurCloseable';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import LoadingSwitch from '@/components/LoadingSwitch';
import NetworkKeys from '@/enum/network-keys';
import SavedActionsType from '@/enum/saved-actions-type';
import SearchDropdownSearchItem from '@/components/Search/Dropdown/SearchDropdownSearchItem';
import SearchedThemeItem from '@/components/UploadsDatasetConfiguration/MemorySearchedTheme/SearchedThemeItem';

import { mapGetters, mapState } from 'vuex';

export default {
  name: 'searched-theme-list',

  components: {
    LoadingBlocksOverlay,
    LoadingSwitch,
    RecycleScroller,
    SearchDropdownSearchItem,
    SearchedThemeItem,
  },

  mixins: [BlurCloseable],

  data() {
    return {
      loaderText: 'Loading Saved Search Actions',
    };
  },

  computed: {
    ...mapGetters('network', ['status']),

    ...mapState('savedActions', ['savedActions']),

    loaderStatus() {
      return this.status(NetworkKeys.SAVED_ACTIONS);
    },

    searchItems() {
      return this.savedActions
        .filter(item => item?.type === SavedActionsType.CREATE_SEARCH_THEME.name)
        .map(item => {
          return {
            id: item.id,
            content: item.components.newLabel,
            label: item.components.newLabel,
            parentId: item.components.parentId,
            query: item.components.searchQuery.replace(/(<->)/gm, ' '),
            exact: item.components.searchExact || false,
            distinct: item.components.searchDistinctContent || false,
          };
        });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.searched-theme-list {
  @include flex("inline", "column", "start", "stretch");

  padding-bottom: 1.175rem;
  transition: all $interaction-transition-time;
  width: 100%;

  .scroller {
    height: 100%;
  }

  .list {
    max-height: 400px;
    overflow-x: hidden;
    overflow-y: auto;
    scrollbar-width: thin;

    .list-item {
      z-index: 1;
    }
  }

  .loader {
    margin: 1rem;
  }
}
</style>
