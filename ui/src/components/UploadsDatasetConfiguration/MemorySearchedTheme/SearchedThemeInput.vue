<template>
  <section class="searched-theme-input" @click="onClick">
    <span v-if="!focused && this.text.length === 0" class="placeholder">
      Type your query here... e.g. Coffee
      <span class="tag blue">AND</span>
      taste
      <span class="tag blue">AND</span>
      other
    </span>
    <editor
        :clear="clear || this.text === ''"
        :focused="focused"
        :query="query"
        :tag="tag"
        @blur="onBlurEditor"
        @clear="onClearEditor"
        @focus="onFocusEditor"
        @input="onInputEditor"
        @query-updated="query = null"
        @tag-inserted="tag = null"
    ></editor>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import bus from '@/helpers/bus';
import Editor from '@/components/Search/Editor/Editor';

export default {
  name: 'searched-theme-input',

  components: {
    Editor,
  },

  props: {
    clear: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      focused: false,
      query: null,
      tag: null,
    };
  },

  computed: {
    ...mapState('savedActions', ['selected']),

    ...mapState('search', ['text']),
  },

  watch: {
    selected() {
      if (this.selected != null) {
        this.query = this.selected?.components?.searchQuery.replace(/(<->)/gm, ' ');
        this.setExactSearch({ exactSearch: this.selected?.components?.searchExact });
        this.setDistinct({ distinct: this.selected?.components?.searchDistinctContent });
      } else {
        this.setExactSearch({ exactSearch: false });
        this.setDistinct({ distinct: false });
      }
    },
  },

  created() {
    bus.$on('select-search-operator', this.onSelectOperator);
  },

  beforeDestroy() {
    bus.$off('select-search-operator', this.onSelectOperator);
    this.onInputEditor('');
  },

  methods: {
    ...mapActions('search', [
      'setDistinct',
      'setExactSearch',
      'setText',
    ]),

    ...mapActions('savedActions', ['resetSelected']),

    onBlurEditor() {
      this.focused = false;
    },

    onClearEditor() {
      this.$emit('clear');
    },

    onClick() {
      this.$emit('open-dropdown');
      this.focused = true;
    },

    onFocusEditor() {
      this.focused = true;
    },

    onInputEditor(text) {
      this.setText({ text });
    },

    onSelectOperator(operator) {
      this.tag = operator;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.searched-theme-input {
  @include flex("inline", "row", "start", "center");
  @include stretch;

  background:clr("white");
  cursor: text;
  height: 100%;
  margin-left: 0.8rem;
  overflow-x: auto;
  position: relative;
  transition: all $interaction-transition-time;

  &:hover {
    background:clr("white");
  }

  .placeholder {
    color: $search-dropdown-txt;
    font-size: $font-size-sm;
    font-style: italic;
    opacity: 0.5;
    pointer-events: none;
    position: absolute;
  }

  .tag {
    @include button-gradient;
    @include flex("inline", "row", "center", "center");

    border: $border-standard;
    border-radius: 3px;
    font-family: "Courier New", Courier, monospace;
    font-size: $font-size-sm;
    font-weight: $font-weight-bold;
    margin: 0 0.2rem;
    padding: 0.16rem 0;
    transform: skew(-10deg);
    white-space: nowrap;
    width: 2.2rem;

    &.blue {
      color: clr("blue");
      border-color: clr("blue");
    }
  }
}
</style>
