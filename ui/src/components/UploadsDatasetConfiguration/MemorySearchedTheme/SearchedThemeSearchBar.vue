<template>
  <section class="searched-theme-search-bar">
    <section class="search-input" :class="{valid: valid || loading, open}">
      <searched-theme-input :clear="clear" @open-dropdown="onOpenOperatorDropdown" @clear="onInputClear" />
      <search-bar-close @clear="onClickClear" />
      <search-dropdown-operator :open="openOperatorDropdown" @close="onCloseOperatorDropdown"/>
    </section>
    <searched-theme-save />
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import bus from '@/helpers/bus';
import SearchBarClose from '@/components/Search/Bar/SearchBarClose';
import SearchDropdownOperator from '@/components/Search/Dropdown/SearchDropdownOperator';
import SearchedThemeInput from '@/components/UploadsDatasetConfiguration/MemorySearchedTheme/SearchedThemeInput';
import SearchedThemeSave from '@/components/UploadsDatasetConfiguration/MemorySearchedTheme/SearchedThemeSave';

import { debounce } from 'lodash-es';
import { searchRequest } from '@/services/request';

export default {
  name: 'searched-theme-search-bar',

  components: {
    SearchBarClose,
    SearchDropdownOperator,
    SearchedThemeInput,
    SearchedThemeSave,
  },

  data() {
    return {
      clear: false,
      openOperatorDropdown: false,
      searching: false,
    };
  },

  computed: {
    ...mapGetters('search', ['query']),

    ...mapState('search', [
      'distinct',
      'exactSearch',
      'loading',
      'text',
      'valid',
    ]),

    open() {
      return this.openOperatorDropdown;
    },
  },

  watch: {
    async distinct() {
      this.searching = true;
      await this.debounceValidate();
    },

    async exactSearch() {
      this.searching = true;
      await this.debounceValidate();
    },

    async query() {
      this.searching = true;
      await this.debounceValidate();
    },
  },

  created() {
    bus.$on('clear-theme-from-search-input', this.onControlsClear);
  },

  beforeDestroy() {
    bus.$off('clear-theme-from-search-input', this.onControlsClear);
  },

  methods: {
    ...mapActions('savedActions', ['resetSelected']),

    onCloseOperatorDropdown() {
      this.openOperatorDropdown = false;
      this.$emit('close');
    },

    onControlsClear() {
      this.resetSelected();
      if (this.text !== '') this.clear = true;
    },

    onClickClear() {
      this.clear = true;
    },

    onInputClear() {
      this.clear = false;
    },

    onOpenOperatorDropdown() {
      if (!this.openOperatorDropdown) {
        this.openOperatorDropdown = true;
        this.$emit('open-dropdown');
      }
    },

    debounceValidate: debounce(async function debounceValidate() {
      if (this.query === '') {
        this.searching = false;
        return;
      }

      try {
        await searchRequest.validateWithoutDataset();
      } finally {
        this.searching = false;
      }
    }, 250),
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.searched-theme-search-bar {
  @include flex("block", "row", "start", "stretch");

  height: $search-bar-height;
  margin-top: 0.6rem;
  position: relative;
  transition: all $interaction-transition-time;
  width: 100%;

  .search-input {
    @include flex("block", "row", "start", "center");
    @include stretch;
    @include panel;

    border: $border-light solid clr("red");
    height: $search-bar-height;
    margin: auto;
    position: relative;
    transition: all $interaction-transition-time;

    &.valid {
      border: $border-purple;
    }

    &.open {
      border-bottom-color: #A69eD6;
    }

    .search-dropdown-operator {
      .search-controls-distinct {
        span {
          font-size: $font-size-xxs;
        }
      }

      .search-controls-exact-search {
        span {
          font-size: $font-size-xxs;
        }
      }

      .search-controls-operators {
        span {
          font-size: $font-size-xs;
        }

        .search-controls-operators-tag {
          font-size: $font-size-xs;
          width: 2rem;
        }
      }
    }
  }
}
</style>
