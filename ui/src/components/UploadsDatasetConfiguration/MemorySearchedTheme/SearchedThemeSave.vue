<template>
  <section class="searched-theme-save" @click.stop>
    <base-button colour="dark" size="small" @click="onClickSave" :disabled="!saveEnabled">
      {{buttonLabel}}
    </base-button>
  </section>
</template>

<script>
import { ChevronDownIcon, SaveIcon } from 'vue-feather-icons';
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import ModalSavedSearchActionCreate from '@/components/UploadsDatasetConfiguration/MemorySearchedTheme/ModalSavedSearchActionCreate';
import ModalSavedSearchActionOverwrite from '@/components/UploadsDatasetConfiguration/MemorySearchedTheme/ModalSavedSearchActionOverwrite';

export default {
  name: 'searched-theme-save',

  components: {
    BaseButton,
    ChevronDownIcon,
    ModalSavedSearchActionCreate,
    SaveIcon,
  },

  data() {
    return {
      buttonLabel: 'Create Search Action',
      update: false,
    };
  },

  computed: {
    ...mapGetters('search', ['query']),

    ...mapState('savedActions', ['selected']),

    ...mapState('search', ['valid']),

    saveEnabled() {
      return this.valid && this.query != null && this.query !== '';
    },
  },

  watch: {
    selected() {
      if (this.selected != null) {
        this.buttonLabel = 'Update Search Action';
        this.update = true;
      } else {
        this.buttonLabel = 'Create Search Action';
        this.update = false;
      }
    },
  },

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    onClickSave() {
      if (this.saveDisabled) return;

      if (this.selected != null) {
        this.setModalComponent({ component: ModalSavedSearchActionOverwrite });
      } else {
        this.setModalComponent({ component: ModalSavedSearchActionCreate });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.searched-theme-save {
  @include flex("block", "row", "center", "center");
  @include rigid;

  color: $body-copy-light;
  margin-left: 0.5rem;

  .base-button {
    @include flex("block", "row", "start", "center");

    font-size: 0.75rem;
    height: $search-bar-height;
    margin-left: 0.5rem;
    padding: 0.4rem 0.8rem;

    &.disabled-true {
      cursor: not-allowed;
    }
  }
}
</style>
