<template>
  <section class="searched-theme-item-content">
    <span v-for="(item, index) in queryArrays" :key="index">
      <span v-if="item === '&'" class="tag blue">AND</span>
      <span v-else-if="item === '|'" class="tag yellow">OR</span>
      <span v-else-if="item === '!'" class="tag red">NOT</span>
      <span v-else-if="item === '('" class="tag light">(</span>
      <span v-else-if="item === ')'" class="tag light">)</span>
      <span v-else>{{ item }}</span>
    </span>
  </section>
</template>

<script>
import BaseButton from '@/components/Base/BaseButton';

export default {
  name: 'searched-theme-item-content',

  components: {
    BaseButton,
  },

  props: {
    data: {
      type: Object,
      required: true,
    },
  },

  computed: {
    queryArrays() {
      this.data.query = this.data.query.replaceAll('&!', '!');
      return this.data.query.split('');
    },
  },

  methods: {
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.searched-theme-item-content {
  @include flex("block", "row", "start", "center");

  color: #352691;
  font-size: $font-size-xs;
  overflow-x: hidden;
  padding-right: 2px;
  text-overflow: ellipsis;
  transition: all $interaction-transition-time;
  white-space: nowrap;

  span {
    color: #352691;
    font-style: italic;
    white-space: pre;
  }

  .tag {
    @include button-gradient;
    @include flex("block", "row", "center", "center");

    border: $border-standard;
    border-radius: 3px;
    font-family: "Courier New", Courier, monospace;
    font-size: $font-size-xs;
    font-weight: $font-weight-bold;
    margin: 0 0.6rem;
    padding: 0.1875rem 0;
    transform: skew(-10deg);
    white-space: nowrap;
    width: 2.3rem;

    &.light {
      color: $body-copy-light;
      border-color: $body-copy-light;
      width: $operator-tag-light-width;
    }

    &.red {
      border-color: clr("red");
      color: clr("red");
    }

    &.yellow {
      border-color: clr("yellow");
      color: clr("yellow");
    }

    &.blue {
      border-color: clr("blue");
      color: clr("blue");
    }
  }
}
</style>
