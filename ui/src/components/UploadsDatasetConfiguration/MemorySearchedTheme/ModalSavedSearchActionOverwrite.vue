<template>
  <section class="modal-saved-search-action-overwrite">
    <section class="header">
      <h2>Update Search Action</h2>
      <x-icon class="icon" @click="closeModal"/>
    </section>

    <section class="body">
      <section class="input-section">
        <h3>Theme From Search Label</h3>
        <base-input v-model="newLabel" @submit="onClickOverwrite"/>

        <section v-if="conflict" class="conflict">
          An action with these changes already exists, you must enter a different theme name.
        </section>
      </section>

      <section class="input-section">
        <h3>Add as Subtopic to Theme:</h3>

        <section class="select-parent">
          <base-dropdown :data="parentItems" :open="open" :search="true" @close="open = false" @select="onSelectParent">
            <base-dropdown-button :active="open" @click.prevent.stop="open = !open">
              {{ selectedParent ? selectedParent.components.newLabel : 'Don\'t add to theme' }}
            </base-dropdown-button>
          </base-dropdown>

          <base-button colour="dark" :disabled="selectedParent == null" size="small" @click="selectedParent = null">
            Clear
          </base-button>
        </section>
      </section>
    </section>

    <section class="footer">
      <base-button class="cancel" colour="light" type="link" @click="closeModal">Cancel</base-button>
      <base-button class="update" @click="onClickOverwrite">Update Search Action</base-button>
    </section>
  </section>
</template>

<script>
import { XIcon } from 'vue-feather-icons';
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseDropdown from '@/components/Dropdown/BaseDropdown';
import BaseDropdownButton from '@/components/Dropdown/BaseDropdownButton';
import BaseInput from '@/components/Base/BaseInput';
import bus from '@/helpers/bus';
import SearchUpdatedToast from '@/components/Search/Toast/SearchUpdatedToast';

import { savedActionApi } from '@/services/api';

export default {
  name: 'modal-saved-search-action-overwrite',

  components: {
    BaseButton,
    BaseDropdown,
    BaseDropdownButton,
    BaseInput,
    XIcon,
  },

  data() {
    return {
      conflict: false,
      newLabel: '',
      open: false,
      selectedParent: null,
    };
  },

  computed: {
    ...mapGetters('savedActions', ['actionsSearchThemeParent']),

    ...mapState('savedActions', ['selected']),

    ...mapGetters('search', ['query']),

    ...mapState('search', ['distinct', 'exactSearch']),

    ...mapState('user', ['activeWorkspace']),

    label() {
      return this.selected?.components?.newLabel;
    },

    parentId() {
      return this.selected?.components?.parentId;
    },

    parentItems() {
      return this.actionsSearchThemeParent.map(action => {
        return {
          content: action.components.newLabel,
          value: action.id,
        };
      }).filter(item => item.value !== this.selected?.id);
    },
  },

  mounted() {
    this.newLabel = this.label;

    const selectedParent = this.actionsSearchThemeParent.find(action => action.id === this.parentId);

    this.selectedParent = selectedParent;
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('savedActions', ['setSavedActions']),

    ...mapActions('toast', { addToast: 'add' }),

    async onClickOverwrite() {
      if (!this.newLabel || this.newLabel.trim() === '' || this.newLabel.length > 200) {
        return;
      }

      const components = {
        newLabel: this.newLabel,
        searchQuery: this.query,
        searchExact: this.exactSearch,
        searchDistinctContent: this.distinct,
      };

      if (this.selectedParent != null) components.parentId = this.selectedParent.id;

      const { error } = await savedActionApi.updateSavedAction(this.activeWorkspace?.id, {
        ...this.selected,
        components,
      });

      if (error?.response?.status === 409) {
        this.conflict = true;
      } else {
        const savedActions = await savedActionApi.getSavedActions(this.activeWorkspace?.id);
        this.setSavedActions({ savedActions });

        bus.$emit('clear-theme-from-search-input');

        this.closeModal();
        this.addToast({ toast: { component: SearchUpdatedToast, id: 'search-action-updated' } });
      }
    },

    onSelectParent(item) {
      const selectedParent = this.actionsSearchThemeParent.find(action => action.id === item.value);

      this.selectedParent = selectedParent;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.modal-saved-search-action-overwrite {
  @include modal;

  .body {
    font-size: $font-size-sm;
    padding: $search-modal-padding;

    .input-section {
      @include flex("block", "column", "start", "stretch");

      margin-bottom: 1rem;
    }

    .base-input {
      background-color: clr("white");
      border: $border-standard;
      border-radius: $border-radius-medium;
      margin-top: 1rem;
      padding: 0.5rem;
    }

    h3 {
      font-size: $font-size-xs;
      font-weight: $font-weight-bold;
    }

    .label {
      font-weight: $font-weight-bold;
    }

    .conflict {
      background-color: rgba(clr('red'), 0.1);
      border-radius: $border-radius-medium;
      font-size: $font-size-xs;
      margin-top: 1rem;
      padding: 0.5rem;
    }

    .select-parent {
      @include flex("block", "row", "start", "center");

      margin-top: 1rem;

      .base-dropdown {
        @include stretch;
      }

      .base-button {
        margin-left: 1rem;
      }
    }
  }

  .footer {
    font-size: $font-size-md;

    padding: $search-modal-padding;

    .cancel {
      font-weight: $font-weight-normal;
      padding-left: 0;
    }

    .update {
      font-weight: $font-weight-medium;
    }
  }

  .header {
    @include flex("block", "row", "between", "center");

    padding: $search-modal-padding;

    h2 {
      @include flex("block", "row", "start", "center");

      font-weight: $font-weight-bold;
    }

    .icon {
      @include flex("block", "row", "end", "center");

      cursor: pointer;
      height: 1.25rem;
      opacity: 0.3;
      &:hover {
        opacity: 1;
      }

      &:active,
      &:focus {
        border: none;
        outline: none;
      }
    }
  }
}
</style>
