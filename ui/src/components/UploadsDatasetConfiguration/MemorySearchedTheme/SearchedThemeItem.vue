<template>
  <section class="searched-theme-item" :class="{ editing: isEditing }" @click="onClick">
    <section class="text left">
      {{ parentLabel ? `${parentLabel} ▶` : '' }}

      {{ data.label }}
    </section>

    <searched-theme-item-content :data="data" />

    <section class="right">
      <section class="exact-distinct">
        <crosshair-icon
            class="icon"
            v-tooltip.bottom="{ class: 'tooltip-saved-search-controls', content: 'Exact Search', delay: 0 }"
            :style="{ visibility: data.exact ? 'visible': 'hidden' }"
        />

        <filter-icon
            class="icon"
            v-tooltip.bottom="{ class: 'tooltip-saved-search-controls', content: 'Remove Duplicates', delay: 0 }"
            :style="{ visibility: data.distinct ? 'visible': 'hidden' }"
        />
      </section>

      <section class="edit-delete">
        <edit-icon
            class="icon"
            v-tooltip.bottom="{ class: 'tooltip-saved-search-controls', content: 'Edit Search Action', delay: 0 }"
            @click.prevent.stop="onClickOverwrite"
        />

        <trash-2-icon
            class="icon danger"
            v-tooltip.bottom="{ class: 'tooltip-saved-search-controls', content: 'Delete Search Action', delay: 0 }"
            @click.prevent.stop="onClickDelete"
        />
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';
import { CrosshairIcon, EditIcon, FilterIcon, Trash2Icon } from 'vue-feather-icons';

import BaseButton from '@/components/Base/BaseButton';
import ModalSavedSearchActionDelete from '@/components/UploadsDatasetConfiguration/MemorySearchedTheme/ModalSavedSearchActionDelete';
import SearchedThemeItemContent from '@/components/UploadsDatasetConfiguration/MemorySearchedTheme/SearchedThemeItemContent';

export default {
  name: 'searched-theme-item',

  components: {
    BaseButton,
    CrosshairIcon,
    EditIcon,
    FilterIcon,
    SearchedThemeItemContent,
    Trash2Icon,
  },

  computed: {
    ...mapGetters('savedActions', ['actionsSearchThemeParent']),

    ...mapState('savedActions', ['selected']),

    isEditing() {
      return this.selected?.id === this.data.id;
    },

    parentLabel() {
      if (this.data.parentId == null) return null;

      const parent = this.actionsSearchThemeParent.find(action => action.id === this.data.parentId);

      return parent?.components?.newLabel;
    },
  },

  props: {
    data: {
      type: Object,
      required: true,
    },
  },

  beforeDestroy() {
    this.selectSavedAction({ id: null });
  },

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    ...mapActions('savedActions', ['selectSavedAction']),

    onClick() {
      this.selectSavedAction({ id: this.data.id });
    },

    onClickDelete() {
      this.selectSavedAction({ id: this.data.id });
      this.setModalComponent({ component: ModalSavedSearchActionDelete });
    },

    onClickOverwrite() {
      this.selectSavedAction({ id: this.data.id });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.searched-theme-item {
  @include flex('block', 'row', 'start', 'center');
  @include stretch;

  cursor: pointer;
  height: 46px;
  transition: all $interaction-transition-time;

  &:hover {
    background-color: rgba(0,27,255,0.04);
    border-left: 4px solid;
    border-left-color: rgba(#352691, 0.4);
  }

  &.editing {
    background-color: rgba(#352691, 0.1);
    border-left: 4px solid;
    border-left-color: rgba(#352691, 0.4);
  }

  .left {
    @include truncate;

    font-weight: $font-weight-bold;
    min-width: 30%;
    width: 30%;
  }

  .right {
    @include flex("block", "row", "end", "center");

    flex: 1 0 0;
    padding-right: 0.4rem;
    transition: all $interaction-transition-time;

    .edit-delete {
      @include flex("block", "row", "start", "center");

      .icon {
        cursor: pointer;
        height: $font-size-base;
        margin-left: 1rem;
        opacity: 0.5;
        width: $font-size-base;

        &:hover {
          opacity: 1;
        }

        &.danger {
          color: clr('red');
          margin-left: 0.4rem;
        }
      }
    }

    .exact-distinct {
      @include flex("block", "row", "start", "center");

      color: #352691;

      .icon {
        cursor: pointer;
        height: $font-size-sm;
        margin-left: 0.4rem;
        opacity: 1;
        width: $font-size-sm;
      }
    }
  }

  .text {
    color: #352691;
    font-size: $font-size-sm;
    padding: 0.7rem 0.7rem;
    overflow-x: hidden;
    text-overflow: ellipsis;
    transition: all $interaction-transition-time;
    white-space: nowrap;

    &.left {
      padding-left: 0.4rem;
    }
  }
}
</style>
