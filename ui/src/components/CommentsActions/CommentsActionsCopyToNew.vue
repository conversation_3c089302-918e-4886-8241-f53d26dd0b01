<template>
  <section class="comments-actions-copy-to-new">
    <section class="header">
      <h2>
        <i class="fa-regular fa-copy icon" />
        Copy Selection to New Theme
      </h2>

      <section v-if="error" class="error">
        A theme with the name '<span class="value">{{ inputValue.trim() }}</span>' already exists.
      </section>
    </section>

    <section class="body">
      <h3>New Theme Name</h3>
      <base-input placeholder="Type Theme Name" v-model="inputValue" @submit="onConfirm"/>
    </section>

    <section class="footer">
      <base-button class="cancel" colour="light" size="small" type="link" @click="onCancel">Cancel</base-button>

      <loading-blocks-overlay v-if="loading"/>
      <base-button v-else class="confirm" colour="base" :disabled="disabled" size="small" @click="onConfirm">Copy</base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import ErrorMessage from '@/enum/error-message';
import intercomEvent from '@/services/IntercomEvent';
import intercomEvents from '@/enum/intercom-events';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import StatusCode from '@/enum/status-code';

import { snippetApi } from '@/services/api';
import { themesRequest } from '@/services/request';

export default {
  name: 'comments-actions-copy-to-new',

  components: {
    BaseButton,
    BaseInput,
    LoadingBlocksOverlay,
  },

  data() {
    return {
      error: false,
      inputValue: '',
      loading: false,
    };
  },

  computed: {
    ...mapState('datasets', ['active']),

    ...mapGetters('snippets', ['getFilterBackendMappingList', 'getParentDatasetCommentIdsToReassign']),

    ...mapState('snippets', [
      'commentsToReassign',
      'selectedAll',
      'viewUnassigned',
    ]),

    ...mapGetters('snippetsFilter', [
      'activeChildDatasetIsUnsaved',
      'getActiveChildDatasetId',
      'isViewingChildDataset',
    ]),

    ...mapState('themes', [
      'selectedSubtopic',
      'selectedTheme',
      'themes',
    ]),

    ...mapGetters('themesFilter', ['getParentThemeIdsByChildThemeIds']),

    disabled() {
      return this.inputValue.trim() === '';
    },

    selectedItem() {
      if (this.selectedSubtopic) {
        return this.selectedSubtopic;
      }
      return this.selectedTheme || null;
    },
  },

  watch: {
    inputValue() {
      this.error = false;
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('snippets', ['setTargetThemeIdsToReassign']),

    onCancel() {
      this.closeModal();
    },

    async onConfirm() {
      if (this.disabled || this.loading) return;

      const response = await this.reassign();
      intercomEvent.send(intercomEvents.COPY_COMMENTS_TO_NEW);
      this.loading = false;

      if (response.status === StatusCode.BAD_REQUEST.code() && response.data === ErrorMessage.DUPLICATE_THEME_NAME.message()) {
        this.error = true;
        return;
      }

      this.closeModal();
    },

    async reassign() {
      this.loading = true;

      const requestBody = {
        allSnippets: this.selectedAll,
        destinationTopicIds: null,
        filters: this.getFilterBackendMappingList,
        newThemeLabel: this.inputValue.trim(),
        originalTopicId: this.selectedItem?.id || -1,
        removeFromOriginal: false,
        removeFromOthers: false,
        snippetIds: this.commentsToReassign,
        unassigned: this.viewUnassigned,
      };

      let response;

      if (this.isViewingChildDataset) {
        response = snippetApi.reassign(this.getActiveChildDatasetId, requestBody);

        // send the same request for parent dataset
        // when users are working on a temporary child dataset
        if (this.activeChildDatasetIsUnsaved) {
          requestBody.snippetIds = [...this.getParentDatasetCommentIdsToReassign];
          if (this.selectedItem?.id != null) {
            const parentDatasetThemeIds = this.getParentThemeIdsByChildThemeIds({ ids: [this.selectedItem.id] });
            requestBody.originalTopicId = parentDatasetThemeIds?.length > 0 ? parentDatasetThemeIds[0] : -1;
          }

          await snippetApi.reassign(this.active, requestBody);
          // refresh reference list after actions
          await themesRequest.storeThemesOfParentDatasetForReferences(this.active);
        }
      } else {
        response = snippetApi.reassign(this.active, requestBody);
      }

      return response;
    },
  },

  mounted() {
    this.setTargetThemeIdsToReassign({ ids: [] });
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.comments-actions-copy-to-new {
  @include modal;

  .header {
    transition: all $interaction-transition-time;
    align-items: start;
    padding: 1.5rem 2rem 1.5rem 2rem;

    .error {
      align-self: flex-start;
      color: clr('red');
      font-size: $font-size-sm;
      margin-top: 1rem;

      .value {
        font-weight: $font-weight-bold;
      }
    }
  }

  .body {
    padding: 2rem;

    h3 {
      margin-bottom: 0.5rem;
    }
  }

  .footer {
    padding: 1rem 2rem 1rem 1.5rem;

    .loading-blocks-overlay {
      height: 2rem;
    }
  }
}
</style>
