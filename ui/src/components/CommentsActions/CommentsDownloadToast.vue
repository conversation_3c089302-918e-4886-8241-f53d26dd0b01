<template>
  <section class="comments-download-toast">
    <section class="left">
      <span class="text">
        Your download will begin shortly. Please keep this window open.
      </span>
    </section>
    <section class="right">
      <base-button class="dismiss" colour="light" @click="onDismiss">Dismiss</base-button>
    </section>
  </section>
</template>

<script>
import { mapActions } from 'vuex';
import BaseButton from '@/components/Base/BaseButton';
import Toast from '@/components/Mixins/Toast';

export default {
  name: 'comments-download-toast',

  components: {
    BaseButton,
  },

  mixins: [Toast],

  created() {
    setTimeout(this.close, 3000);
  },

  methods: {
    ...mapActions('toast', ['remove']),

    onDismiss() {
      this.remove({ id: 'comments-download' });
      this.close();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-download-toast {
  @include toast;

  background-color: $swot-strength;
}
</style>
