<template>
  <section class="comments-actions-delete-all-modal">
    <section class="header">
      <section class="left">
        <h2>Delete All Comments</h2>
      </section>

      <section class="right" @click="onCancel">
        <x-icon class="icon"/>
      </section>
    </section>
    <section class="body">
      <section class="warning">
        <alert-circle-icon class="icon"/>
        <section class="text">This action cannot be undone.</section>
      </section>

      <section class="info">
        This will delete all comments of the dataset, that means this dataset will be deleted as well. Are you sure?
      </section>
    </section>
    <section class="footer">
      <base-button class="cancel" colour="light" size="small" type="link" @click="onCancel">Cancel</base-button>
      <base-button v-if="!proceeding" class="confirm" colour="danger" size="small" @click="onConfirm">Delete Dataset</base-button>
      <loading-blocks-overlay v-else />
    </section>
  </section>
</template>

<script>
import { AlertCircleIcon, XIcon } from 'vue-feather-icons';
import { mapActions, mapState, mapGetters } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import DatasetsDeletedToast from '@/components/Toasts/DatasetsDeletedToast';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';

import { datasetApi } from '@/services/api';
import { datasetsRequestV0, metadataRequest, themesRequest } from '@/services/request';

export default {
  name: 'comments-actions-delete-all-modal',

  components: {
    AlertCircleIcon,
    BaseButton,
    LoadingBlocksOverlay,
    XIcon,
  },

  computed: {
    ...mapState('datasets', ['active', 'overviews']),

    ...mapGetters('snippetsFilter', [
      'activeChildDatasetIsUnsaved',
      'getActiveChildDatasetId',
      'getActiveFilterViewObj',
      'isViewingChildDataset',
    ]),
  },

  data() {
    return {
      proceeding: false,
    };
  },

  methods: {
    ...mapActions('datasets', [
      'deselect',
      'removeOverviews',
      'setActiveViewing',
    ]),

    ...mapActions('modal', ['closeModal']),

    ...mapActions('snippets', { resetSnippetState: 'reset' }),

    ...mapActions('snippetsFilter', { resetSnippetFilterState: 'reset', setShowFilterPanel: 'setShowFilterPanel' }),

    ...mapActions('themes', { resetThemeState: 'reset', setThemes: 'setThemes' }),

    ...mapActions('toast', ['add']),

    async doDeleteFilterViewAndResetFilter() {
      await metadataRequest.deleteFilterViewById(this.getActiveFilterViewObj.parentId, this.getActiveFilterViewObj.id);
      this.setActiveViewing({ id: null });
      this.resetSnippetFilterState();
      this.setShowFilterPanel({ value: true });
      // note - extra miles
      // should check the split list then reload split-view only?
      // and update the split count on main filter-obj as well?
    },

    async fetchThemes() {
      const themes = await themesRequest.fetchThemes();
      this.setThemes({ themes });
    },

    async getSnippets() {
      await metadataRequest.filterCommentsOnMetadata();
      await metadataRequest.filterCommentsCountOnMetadata();
    },

    onCancel() {
      return this.closeModal();
    },

    async onConfirm() {
      if (this.proceeding) {
        return;
      }
      this.proceeding = true;

      if (this.isViewingChildDataset && !this.activeChildDatasetIsUnsaved) {
        // delete filter-view if it's saved
        await this.doDeleteFilterViewAndResetFilter();
      } else {
        // if it's unsaved, just delete the parent-dataset
        // would save an extra call and couple redundant steps
        const ids = [];
        ids.push(this.active);
        await datasetApi.destroy(ids);

        this.deselect({ ids });
        this.removeOverviews({ ids });
      }

      this.resetSnippetState();
      this.resetThemeState();
      this.add({ toast: { component: DatasetsDeletedToast, id: 'dataset-delete-success' } });

      if (this.overviews?.length) {
        await datasetsRequestV0.reloadSelected();
        await this.fetchThemes();
        await this.getSnippets();
      }

      this.closeModal();
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.comments-actions-delete-all-modal {
  @include modal;

  .header {
    @include modal-header;
  }

  .body {
    font-size: $font-size-sm;
    padding: 2rem 1.5rem;

    .warning {
      @include flex("block", "row", "start", "center");

      background-color: rgba(clr('red'), 0.1);
      border: 1px solid clr('red');
      border-radius: $border-radius-medium;
      color: clr("red");
      font-weight: $font-weight-medium;
      padding: 0.5rem;

      .icon {
        height: 1rem;
        margin-right: 0.5rem;
        width: 1rem;
      }

      .text {
        font-size: $font-size-xs;
      }
    }

    .info {
      margin-top: 1rem;
    }
  }

  .footer {
    .cancel {
      margin-left: -0.5rem;
    }

    .confirm, .loading-blocks-overlay {
      height: 2rem;
    }
  }
}
</style>
