<template>
  <section class="comments-actions-delete-toast">
    <section class="toast-content">
      <section class="left">
        <check-icon class="icon"/>
        <section class="text">
          <span class="text">{{textCommentListSize}} comment(s) were successfully deleted.</span>
        </section>
      </section>
      <section class="right">
        <base-button class="dismiss" colour="light" @click="onDismiss">Dismiss</base-button>
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { CheckIcon } from 'vue-feather-icons';

import BaseButton from '@/components/Base/BaseButton';
import Toast from '@/components/Mixins/Toast';

export default {
  name: 'comments-actions-delete-toast',

  components: {
    BaseButton,
    CheckIcon,
  },

  mixins: [Toast],

  data() {
    return {
      count: 3,
      countWatcher: null,
    };
  },

  computed: {
    ...mapState('toast', ['toastData']),

    textCommentListSize() {
      return this.toastData.docCount;
    },
  },

  created() {
    this.countWatcher = setInterval(() => {
      this.count -= 1;
      if (this.count === 0) {
        clearInterval(this.countWatcher);
        this.close();
      }
    }, 1000);
  },

  beforeDestroy() {
    this.remove({ id: 'comments-delete' });
    this.removeToastData();
  },

  methods: {
    ...mapActions('toast', ['remove', 'removeToastData']),

    onDismiss() {
      this.close();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-actions-delete-toast {
  @include flex("block", "row", "end", "start");

  .toast-content {
    @include toast;

    background-color: clr("purple");
    padding: 1rem;

    .left {
      @include flex("block", "row", "start", "center");

      .icon {
        font-size: $font-size-lg;
        margin-right: 0.2rem;
      }

      .text {
        @include flex("block", "row", "start", "start");

        .highlight {
          font-weight: $font-weight-bold;
          margin: 0 0.5rem;
        }
      }
    }

    .right {
      margin-left: 2rem;
    }
  }
}
</style>
