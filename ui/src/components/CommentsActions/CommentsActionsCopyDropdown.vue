<template>
  <section class="comments-actions-copy-dropdown">
    <section class="item" @click="onClickExisting">
      <i class="fa-regular fa-copy icon" />
      <span class="text">Copy To <span class="strong">Existing Theme</span></span>
    </section>
    <section class="item" @click="onClickNew">
      <i class="fa-regular fa-copy icon" />
      <span class="text">Copy To <span class="strong">New Theme</span></span>
    </section>
    <section class="item" @click="onClickClipboard">
      <i class="fa-solid fa-clipboard-check icon" />
      <span class="text">Copy To <span class="strong">Clipboard</span></span>
      <textarea ref="copyInput" class="copy-comments-text" />
    </section>
  </section>
</template>

<script>
import Papa from 'papaparse';
import { mapActions, mapGetters, mapState } from 'vuex';

import BlurCloseable from '@/components/Mixins/BlurCloseable';
import CommentsActionsCopyToast from '@/components/CommentsActions/CommentsActionsCopyToast';
import CommentsActionsCopyToExisting from '@/components/CommentsActions/CommentsActionsCopyToExisting';
import CommentsActionsCopyToNew from '@/components/CommentsActions/CommentsActionsCopyToNew';
import intercomEvent from '@/services/IntercomEvent';
import intercomEvents from '@/enum/intercom-events';

export default {
  name: 'comments-actions-copy-dropdown',

  mixins: [BlurCloseable],

  computed: {
    ...mapGetters('snippets', ['selectedSnippetsText', 'snippetsCount']),

    ...mapState('snippets', ['selectedSnippets', 'selectedAll']),

    ...mapState('themes', ['index']),
  },

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    ...mapActions('snippets', ['setCommentsToReassign']),

    ...mapActions('themes', ['setIndexReassign']),

    ...mapActions('toast', { addToast: 'add' }),

    initReassign() {
      this.setIndexReassign({ index: null });

      if (this.selectedAll) {
        this.setCommentsToReassign({ ids: new Array(this.snippetsCount) });
      } else {
        this.setCommentsToReassign({ ids: this.selectedSnippets });
      }

      this.$emit('close');
    },

    onClickClipboard() {
      this.$emit('close');
      if (!this.selectedSnippets?.length) {
        return;
      }

      this.$refs.copyInput.value = Papa.unparse(this.selectedSnippetsText);
      this.$refs.copyInput.select();
      document.execCommand('copy');

      this.addToast({
        toast: {
          component: CommentsActionsCopyToast,
          id: 'comments-copy',
        },
      });

      intercomEvent.send(intercomEvents.COPY_COMMENTS);
    },

    onClickExisting() {
      this.initReassign();
      this.setModalComponent({ component: CommentsActionsCopyToExisting });
    },

    onClickNew() {
      this.initReassign();
      this.setModalComponent({ component: CommentsActionsCopyToNew });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.comments-actions-copy-dropdown {
  @include flex("block", "column", "start", "stretch");

  .item {
    @include flex("block", "row", "start", "center");

    cursor: pointer;
    padding: 0.5rem 1rem;
    transition: all $interaction-transition-time;

    &:hover {
      color: clr('purple');
    }

    &:not(:first-child) {
      border-top: solid 1px $border-color;
    }

    .icon {
      color: inherit;
      font-size: $font-size-sm;
      height: $font-size-sm;
      margin-right: 0.5rem;
      width: $font-size-sm;
    }

    .text {
      color: inherit;
      font-size: $font-size-xs;
      font-weight: 400;

      .strong {
        font-weight: 600;
      }
    }

    .copy-comments-text {
      opacity: 0;
      pointer-events: none;
      position: absolute;
      width: 0;
      height: 0;
      left: -9999px;
    }
  }
}
</style>
