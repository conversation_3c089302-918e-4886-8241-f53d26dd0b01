<template>
  <section class="comments-actions-target-search"
           :class="{ active }"
  >
    <i class="fa fa-search icon icon-search" />
    <base-input v-model="searchString"
                :placeholder="placeholder"
                @blur="onInputBlur"
                @focus="onInputFocus" />
    <i class="fa fa-x icon icon-x" @click="searchString = ''" v-if="searchString"/>
  </section>
</template>

<script>
import BaseInput from '@/components/Base/BaseInput';

export default {
  name: 'comments-actions-target-search',

  components: {
    BaseInput,
  },

  props: {
    placeholder: {
      type: String,
      default: 'Search Themes',
    },
  },

  data() {
    return {
      active: false,
      searchString: '',
    };
  },

  methods: {
    onInputBlur() {
      this.active = false;
    },

    onInputFocus() {
      this.active = true;
    },
  },

  watch: {
    searchString() {
      this.$emit('searching', this.searchString);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-actions-target-search {
  @include flex("block", "row", "start", "center");

  background-color: #FFFFFF;
  border: 1px solid #7362B7;
  border-radius: $border-radius-small;
  padding: 0.4rem 0.5rem;
  width: 100%;

  &.active {
    border: 1px solid clr("purple");

    .icon-search {
      color: clr("purple");
      opacity: 1;
    }
  }

  .icon {
    color: $body-copy-light;
    font-size: $font-size-sm;
    opacity: 0.5;

    &.icon-search {
      margin-right: 0.5rem;
    }

    &.icon-x {
      cursor: pointer;
      margin-left: 0.5rem;

      &:hover {
        color: clr("red");
        opacity: 1;
      }
    }
  }

  input {
    border: none;
    outline: none;
    padding: 0;
    width: 100%;

    &:active,
    &:hover,
    &:focus {
      border: none;
      outline: none;
    }

    &::placeholder {
      font-size: $font-size-sm;
    }
  }
}
</style>
