<template>
  <section class="comments-actions-remove-modal">
    <section class="header">
      <h2>
        <i class="fa-regular fa-ban icon" />
        Remove Selection From Theme
      </h2>
    </section>

    <section class="body">
      <section v-if="selectedItem">
        <span class="text">Remove selection from <span class="strong">{{ selectedItem.topicLabel }}</span> theme. Are you sure?</span>
        <section v-if="selectedItem" class="remove-from-others" @click="removeFromOthers = !removeFromOthers">
          <base-checkbox :value="removeFromOthers" />
          <span class="text">Remove from all other themes and subtopics.</span>
        </section>
      </section>
      <section v-else>
        <span class="text">Remove selection from <span class="strong">all themes and subtopics</span>. Are you sure?</span>
      </section>
    </section>

    <section class="footer">
      <base-button class="cancel" colour="light" size="small" type="link" @click="onCancel">Cancel</base-button>

      <loading-blocks-overlay v-if="loading" />
      <base-button v-else class="confirm" colour="base" size="small" @click="onConfirm">Remove</base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseCheckbox from '@/components/Base/BaseCheckbox';
import intercomEvent from '@/services/IntercomEvent';
import intercomEvents from '@/enum/intercom-events';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';

import { snippetApi } from '@/services/api';
import { themesRequest } from '@/services/request';

export default {
  name: 'comments-actions-remove-modal',

  components: {
    BaseButton,
    BaseCheckbox,
    LoadingBlocksOverlay,
  },

  data() {
    return {
      loading: false,
      removeFromOthers: false,
    };
  },

  computed: {
    ...mapState('datasets', ['active']),

    ...mapGetters('snippets', ['getFilterBackendMappingList', 'getParentDatasetCommentIdsToReassign']),

    ...mapState('snippets', [
      'commentsToReassign',
      'selectedAll',
      'viewUnassigned',
    ]),

    ...mapGetters('snippetsFilter', [
      'activeChildDatasetIsUnsaved',
      'getActiveChildDatasetId',
      'isViewingChildDataset',
    ]),

    ...mapState('themes', ['selectedSubtopic', 'selectedTheme']),

    ...mapGetters('themesFilter', ['getParentThemeIdsByChildThemeIds']),

    selectedItem() {
      if (this.selectedSubtopic) {
        return this.selectedSubtopic;
      }
      return this.selectedTheme || null;
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('snippets', ['setTargetThemeIdsToReassign']),

    onCancel() {
      this.closeModal();
    },

    async onConfirm() {
      if (this.loading) return;

      await this.reassign();
      intercomEvent.send(intercomEvents.REMOVE_COMMENTS_FROM_THEME);

      this.loading = false;
      this.closeModal();
    },

    async reassign() {
      this.loading = true;

      const requestBody = {
        allSnippets: this.selectedAll,
        destinationTopicIds: null,
        filters: this.getFilterBackendMappingList,
        newThemeLabel: null,
        originalTopicId: this.selectedItem?.id || -1,
        removeFromOriginal: true,
        removeFromOthers: this.selectedItem ? this.removeFromOthers : true,
        snippetIds: this.commentsToReassign,
        unassigned: this.viewUnassigned,
      };

      if (this.isViewingChildDataset) {
        await snippetApi.reassign(this.getActiveChildDatasetId, requestBody);

        // send the same request for parent dataset
        // when users are working on a temporary child dataset
        if (this.activeChildDatasetIsUnsaved) {
          requestBody.snippetIds = [...this.getParentDatasetCommentIdsToReassign];
          if (this.selectedItem?.id != null) {
            const parentDatasetThemeIds = this.getParentThemeIdsByChildThemeIds({ ids: [this.selectedItem.id] });
            requestBody.originalTopicId = parentDatasetThemeIds?.length > 0 ? parentDatasetThemeIds[0] : -1;
          }

          await snippetApi.reassign(this.active, requestBody);
          // refresh reference list after actions
          await themesRequest.storeThemesOfParentDatasetForReferences(this.active);
        }
      } else {
        await snippetApi.reassign(this.active, requestBody);
      }
    },
  },

  mounted() {
    this.setTargetThemeIdsToReassign({ ids: [] });
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.comments-actions-remove-modal {
  @include modal;

  .header {
    align-items: start;
    padding: 1.5rem 2rem 1.5rem 2rem;
  }

  .body {
    padding: 2rem;

    .text {
      font-size: $font-size-sm;

      .strong {
        font-weight: $font-weight-bold;
      }
    }

    .remove-from-others {
      @include flex("block", "row", "start", "center");
      cursor: pointer;
      margin-top: 1rem;

      .text {
        font-size: $font-size-xs;
        margin-left: 0.5rem;
      }
    }
  }

  .footer {
    padding: 1rem 2rem 1rem 1.5rem;

    .loading-blocks-overlay {
      height: 2rem;
    }
  }
}
</style>
