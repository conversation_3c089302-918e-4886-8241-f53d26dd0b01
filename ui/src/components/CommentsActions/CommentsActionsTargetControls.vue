<template>
  <section class="comments-actions-target-controls">
    <section class="select-all" @click="onClickSelectAll">
      <base-checkbox :value="isAllSelected" class="checkbox" />
      <span class="label">Select all</span>
    </section>
    <common-sort-item v-for="item in sortList"
                      :key="item.name"
                      class="sort-item"
                      :class="item.name"
                      :current="currentSortItem"
                      :font-size-xs="true"
                      :header="item"
                      @sort-event="sortEvent"
    />
  </section>
</template>

<script>
import { mapState } from 'vuex';
import BaseCheckbox from '@/components/Base/BaseCheckbox';
import CommonSortItem from '@/components/CommonComponent/CommonSortItem';
import ResultsTabs from '@/enum/results-tabs';
import SortDirection from '@/enum/sort-direction';
import ThemesSort from '@/enum/themes-sort';

export default {
  name: 'comments-actions-target-controls',

  components: {
    BaseCheckbox,
    CommonSortItem,
  },

  props: {
    isAllSelected: {
      type: Boolean,
      required: true,
    },
    sort: {
      type: Object,
      default: ThemesSort.NAME,
    },
    sortDirection: {
      type: Object,
      default: SortDirection.ASC,
    },
  },

  computed: {
    ...mapState('themes', ['selectedTab', 'sortTypeAvailable']),

    currentSortItem() {
      return {
        direction: this.sortDirection,
        sort: this.sort,
      };
    },

    sortList() {
      if (this.selectedTab === ResultsTabs.TREND_ANALYSIS) {
        return this.sortTypeAvailable.filter(t => t !== ThemesSort.ADORESCORE)
          .sort((a, b) => b.displayOrder() - a.displayOrder());
      }

      return this.sortTypeAvailable
        .sort((a, b) => b.displayOrder() - a.displayOrder());
    },
  },

  methods: {
    onClickSelectAll() {
      this.$emit('selectAll');
    },

    sortEvent(item) {
      this.$emit('sort-event', { direction: item.direction, sort: item.sort });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-actions-target-controls {
  align-items: center;
  display: grid;
  grid-column-gap: 0.5rem;
  grid-template-columns: 1fr minmax(12%, auto) minmax(12%, auto) 12%;
  text-transform: uppercase;

  .select-all {
    align-items: center;
    cursor: pointer;
    display: grid;
    font-size: $font-size-xs;
    font-weight: $font-weight-bold;
    grid-column-gap: 0.5rem;
    grid-template-columns: 14px 1fr;
  }
}
</style>
