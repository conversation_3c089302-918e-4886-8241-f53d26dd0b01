<template>
  <section class="comments-actions-copy-to-existing">
    <section class="header">
      <h2>
        <i class="fa-regular fa-copy icon" />
        {{ textHeader }}
      </h2>
    </section>

    <section class="body">
      <section class="title">
        Select Themes & Subtopics
        <i class="fa-light fa-arrow-down" />
        <span v-if="targetThemeIdsToReassign.length" class="selected">
          - {{ targetThemeIdsToReassign.length }} Selected
        </span>
      </section>
      <comments-actions-target-search @searching="onSearching" />
      <comments-actions-target-controls :is-all-selected="isAllSelected"
                                        :sort="targetSort"
                                        :sort-direction="targetSortDirection"
                                        @selectAll="onSelectAll"
                                        @sort-event="onSortEvent"
      />
      <section v-if="targetList.length" class="target-list">
        <comments-actions-target-existing-theme
            v-for="item in targetList"
            class="target-item"
            :key="item.id"
            :theme="item"
        />
      </section>
      <section v-else class="target-list empty">
        {{ searchingLabel ? 'No matching theme.' : 'No existing theme.'}}
      </section>
    </section>

    <section class="footer">
      <base-button class="cancel" colour="light" size="small" type="link" @click="onCancel">Cancel</base-button>

      <loading-blocks-overlay v-if="loading" />
      <base-button v-else class="confirm" colour="base" :disabled="disabled" size="small" @click="onConfirm">
        <i class="fa-regular fa-copy icon" />
        Copy comment
      </base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import CommentsActionsTargetControls from '@/components/CommentsActions/CommentsActionsTargetControls';
import CommentsActionsTargetExistingTheme from '@/components/CommentsActions/CommentsActionsTargetExistingTheme';
import CommentsActionsTargetSearch from '@/components/CommentsActions/CommentsActionsTargetSearch';
import intercomEvent from '@/services/IntercomEvent';
import intercomEvents from '@/enum/intercom-events';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';

import { snippetApi } from '@/services/api';
import { themesRequest } from '@/services/request';

export default {
  name: 'comments-actions-copy-to-existing',

  components: {
    BaseButton,
    CommentsActionsTargetControls,
    CommentsActionsTargetExistingTheme,
    CommentsActionsTargetSearch,
    LoadingBlocksOverlay,
  },

  data() {
    return {
      loading: false,
      searchingLabel: '',
    };
  },

  computed: {
    ...mapState('datasets', ['active']),

    ...mapGetters('snippets', ['getFilterBackendMappingList', 'getParentDatasetCommentIdsToReassign']),

    ...mapState('snippets', [
      'commentsToReassign',
      'selectedAll',
      'targetSort',
      'targetSortDirection',
      'targetThemeIdsToReassign',
      'viewUnassigned',
    ]),

    ...mapGetters('snippetsFilter', [
      'activeChildDatasetIsUnsaved',
      'getActiveChildDatasetId',
      'isViewingChildDataset',
    ]),

    ...mapGetters('themes', ['getThemesByLabelAndOtherFilters']),

    ...mapState('themes', ['selectedSubtopic', 'selectedTheme']),

    ...mapGetters('themesFilter', ['getParentThemeIdsByChildThemeIds']),

    disabled() {
      return !this.targetThemeIdsToReassign?.length;
    },

    isAllSelected() {
      if (this.targetList?.length && this.targetThemeIdsToReassign?.length) {
        return this.targetList.every(item => this.targetThemeIdsToReassign.includes(item.id));
      }
      return false;
    },

    selectedItem() {
      if (this.selectedSubtopic) {
        return this.selectedSubtopic;
      }
      return this.selectedTheme || null;
    },

    targetList() {
      const ignoreIds = this.selectedItem ? [this.selectedItem.id] : [];
      return this.getThemesByLabelAndOtherFilters(this.searchingLabel, this.targetSort, this.targetSortDirection, ignoreIds);
    },

    textHeader() {
      return this.commentsToReassign?.length !== 1
        ? 'Copy Comments to Existing Theme(s)'
        : 'Copy Comment to Existing Theme(s)';
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('snippets', [
      'addTargetThemeIdsToReassign',
      'removeTargetThemeIdsToReassign',
      'setTargetSort',
      'setTargetSortDirection',
      'setTargetThemeIdsToReassign',
    ]),

    onCancel() {
      this.closeModal();
    },

    async onConfirm() {
      if (this.disabled || this.loading) {
        return;
      }

      await this.reassign();
      intercomEvent.send(intercomEvents.COPY_COMMENTS_TO_EXISTING);

      this.loading = false;
      await this.closeModal();
    },

    onSearching(val) {
      this.searchingLabel = val;
    },

    onSelectAll() {
      const showingIds = this.targetList.map(item => item.id);
      if (this.isAllSelected) {
        this.removeTargetThemeIdsToReassign({ ids: showingIds });
      } else {
        this.addTargetThemeIdsToReassign({ ids: showingIds });
      }
    },

    onSortEvent({ direction, sort }) {
      this.setTargetSort({ val: sort });
      this.setTargetSortDirection({ val: direction });
    },

    async reassign() {
      this.loading = true;

      const requestBody = {
        allSnippets: this.selectedAll,
        destinationTopicIds: this.targetThemeIdsToReassign,
        filters: this.getFilterBackendMappingList,
        newThemeLabel: null,
        originalTopicId: this.selectedItem?.id || -1,
        removeFromOriginal: false,
        removeFromOthers: false,
        snippetIds: this.commentsToReassign,
        unassigned: this.viewUnassigned,
      };

      if (this.isViewingChildDataset) {
        await snippetApi.reassign(this.getActiveChildDatasetId, requestBody);

        // send the same request for parent dataset
        // when users are working on a temporary child dataset
        if (this.activeChildDatasetIsUnsaved) {
          requestBody.destinationTopicIds = this.getParentThemeIdsByChildThemeIds({ ids: [...this.targetThemeIdsToReassign] });
          requestBody.snippetIds = [...this.getParentDatasetCommentIdsToReassign];
          if (this.selectedItem?.id != null) {
            const parentDatasetThemeIds = this.getParentThemeIdsByChildThemeIds({ ids: [this.selectedItem.id] });
            requestBody.originalTopicId = parentDatasetThemeIds?.length > 0 ? parentDatasetThemeIds[0] : -1;
          }

          await snippetApi.reassign(this.active, requestBody);
          // refresh reference list after actions
          await themesRequest.storeThemesOfParentDatasetForReferences(this.active);
        }
      } else {
        await snippetApi.reassign(this.active, requestBody);
      }
    },
  },

  mounted() {
    this.setTargetThemeIdsToReassign({ ids: [] });
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.comments-actions-copy-to-existing {
  @include modal;

  .header {
    align-items: start;
    padding: 1.5rem 2rem 1.5rem 2rem;
  }

  .body {
    padding: 2rem;

    .title {
      font-size: $font-size-sm;
      margin-bottom: 1rem;

      .selected {
        font-weight: $font-weight-medium;
      }
    }

    .comments-actions-target-search {
      margin-bottom: 1rem;
    }

    .comments-actions-target-controls {
      margin-bottom: 1rem;
      padding: 0 0.5rem;

      .common-sort-item {
        .header {
          border: none;
          font-weight: $font-weight-bold;
          padding: 0;
        }
      }
    }

    .target-list {
      @include scrollbar-thin;
      height: 280px;
      overflow-x: hidden;
      overflow-y: auto;

      &.empty {
        opacity: 0.7;
        padding-top: 120px;
        text-align: center;
      }
    }
  }

  .footer {
    padding: 1rem 2rem 1rem 1.5rem;

    .loading-blocks-overlay {
      height: 2rem;
    }

    .confirm {
      .icon {
        margin-right: 0.3rem;
      }
    }
  }
}
</style>
