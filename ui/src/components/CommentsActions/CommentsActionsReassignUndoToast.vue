<template>
  <section class="comments-actions-reassign-undo-toast">
    <section class="toast-content">
      <section class="left">
        <i class="fa fa-exchange icon"></i>
        <section class="text">
          <section class="text-count-snippet">{{ countSnippet }}</section>
          will be reassigned in
          <section class="text-count-time">{{ countTime }}</section>
        </section>
      </section>
      <section class="right">
        <base-button class="undo" colour="light" @click="onUndo" type="link">Undo</base-button>
        <base-button class="dismiss" colour="light" @click="onDismiss">Dismiss</base-button>
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import BaseButton from '@/components/Base/BaseButton';
import Toast from '@/components/Mixins/Toast';

export default {
  name: 'comments-actions-reassign-undo-toast',

  components: {
    BaseButton,
  },

  mixins: [Toast],

  beforeD<PERSON>roy() {
    this.remove({ id: 'snippets-reassign-undo' });
  },

  computed: {
    ...mapState('snippets', ['commentsToReassign']),

    countSnippet() {
      if (this.commentsToReassign.length > 1) {
        return `${this.commentsToReassign.length} snippets`;
      }
      return `${this.commentsToReassign.length} snippet`;
    },

    countTime() {
      if (this.count > 1) {
        return ` ${this.count} secs.`;
      }
      return ` ${this.count} sec.`;
    },
  },

  created() {
    this.countWatcher = setInterval(() => {
      this.count -= 1;
      if (this.count === 0) {
        clearInterval(this.countWatcher);
        this.close();
      }
    }, 1000);
  },

  data() {
    return {
      count: 3,
      countWatcher: null,
    };
  },

  methods: {
    ...mapActions('themes', ['setIndexReassign']),

    ...mapActions('snippets', ['setCommentsToReassign']),

    ...mapActions('toast', ['remove']),

    onDismiss() {
      this.close();
    },

    onUndo() {
      this.setIndexReassign({ index: null });
      this.setCommentsToReassign({ ids: [] });
      this.close();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-actions-reassign-undo-toast {
  @include flex("block", "row", "end", "start");

  .toast-content {
    @include toast;

    .left {
      @include flex("block", "row", "start", "center");
      .icon {
        margin-right: 0.5rem;
      }

      .text {
        @include flex("block", "row", "start", "center");

        .text-count-snippet {
          margin: 0 0.2rem;
        }

        .text-count-time {
          font-weight: $font-weight-medium;
          margin: 0 0.2rem;
        }
      }
    }

    .right {
      margin-left: 2rem;
    }
  }
}
</style>
