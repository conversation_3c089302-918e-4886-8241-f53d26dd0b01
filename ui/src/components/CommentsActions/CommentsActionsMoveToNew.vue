<template>
  <section class="comments-actions-move-to-new">
    <section class="header">
      <h2>
        <i class="fa fa-plus icon"></i>
        Move Selection to New Theme
      </h2>

      <section v-if="error" class="error">
        A theme with the name '<span class="value">{{ inputValue.trim() }}</span>' already exists.
      </section>
    </section>

    <section class="body">
      <h3>New Theme Name</h3>
      <base-input placeholder="Type Theme Name" v-model="inputValue" @submit="onConfirm"/>
      <section v-if="selectedItem" class="remove-from-others" @click="removeFromOthers = !removeFromOthers">
        <base-checkbox :value="removeFromOthers" />
        <span class="text">Remove from all other themes and subtopics.</span>
      </section>
      <section v-else class="remove-from-others no-selected-theme">
        <span class="text">This action will remove comments from all other themes and subtopics.</span>
      </section>
    </section>

    <section class="footer">
      <base-button class="cancel" colour="light" size="small" type="link" @click="onCancel">Cancel</base-button>

      <loading-blocks-overlay v-if="loading"/>
      <base-button v-else class="confirm" colour="base" :disabled="disabled" size="small" @click="onConfirm">Move</base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseCheckbox from '@/components/Base/BaseCheckbox';
import BaseInput from '@/components/Base/BaseInput';
import intercomEvent from '@/services/IntercomEvent';
import intercomEvents from '@/enum/intercom-events';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';

import { snippetApi } from '@/services/api';
import { themesRequest } from '@/services/request';

export default {
  name: 'comments-actions-move-to-new',

  components: {
    BaseButton,
    BaseCheckbox,
    BaseInput,
    LoadingBlocksOverlay,
  },

  data() {
    return {
      error: false,
      inputValue: '',
      loading: false,
      removeFromOthers: false,
    };
  },

  computed: {
    ...mapState('datasets', ['active']),

    ...mapGetters('snippets', ['getFilterBackendMappingList', 'getParentDatasetCommentIdsToReassign']),

    ...mapState('snippets', [
      'commentsToReassign',
      'selectedAll',
      'viewUnassigned',
    ]),

    ...mapGetters('snippetsFilter', [
      'activeChildDatasetIsUnsaved',
      'getActiveChildDatasetId',
      'isViewingChildDataset',
    ]),

    ...mapState('themes', [
      'selectedSubtopic',
      'selectedTheme',
      'themes',
    ]),

    ...mapGetters('themesFilter', ['getParentThemeIdsByChildThemeIds']),

    disabled() {
      return this.inputValue.trim() === '';
    },

    isLabelUnique() {
      const labels = this.themes.map(t => t.topicLabel.toLowerCase());

      return !labels.includes(this.inputValue.toLowerCase().trim());
    },

    selectedItem() {
      if (this.selectedSubtopic) {
        return this.selectedSubtopic;
      }
      return this.selectedTheme || null;
    },
  },

  watch: {
    inputValue() {
      this.error = false;
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('snippets', ['setTargetThemeIdsToReassign']),

    onCancel() {
      this.closeModal();
    },

    async onConfirm() {
      if (this.disabled || this.loading) return;

      if (!this.isLabelUnique) {
        this.error = true;
        return;
      }

      await this.reassign();
      intercomEvent.send(intercomEvents.MOVE_COMMENTS_TO_NEW);

      this.loading = false;
      this.closeModal();
    },

    async reassign() {
      this.loading = true;

      const requestBody = {
        allSnippets: this.selectedAll,
        destinationTopicIds: null,
        filters: this.getFilterBackendMappingList,
        newThemeLabel: this.inputValue.trim(),
        originalTopicId: this.selectedItem?.id || -1,
        removeFromOriginal: true,
        removeFromOthers: this.selectedItem ? this.removeFromOthers : true,
        snippetIds: this.commentsToReassign,
        unassigned: this.viewUnassigned,
      };

      if (this.isViewingChildDataset) {
        await snippetApi.reassign(this.getActiveChildDatasetId, requestBody);

        // send the same request for parent dataset
        // when users are working on a temporary child dataset
        if (this.activeChildDatasetIsUnsaved) {
          requestBody.snippetIds = [...this.getParentDatasetCommentIdsToReassign];
          if (this.selectedItem?.id != null) {
            const parentDatasetThemeIds = this.getParentThemeIdsByChildThemeIds({ ids: [this.selectedItem.id] });
            requestBody.originalTopicId = parentDatasetThemeIds?.length > 0 ? parentDatasetThemeIds[0] : -1;
          }

          await snippetApi.reassign(this.active, requestBody);
          // refresh reference list after actions
          await themesRequest.storeThemesOfParentDatasetForReferences(this.active);
        }
      } else {
        await snippetApi.reassign(this.active, requestBody);
      }
    },
  },

  mounted() {
    this.setTargetThemeIdsToReassign({ ids: [] });
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.comments-actions-move-to-new {
  @include modal;

  .header {
    transition: all $interaction-transition-time;
    align-items: start;
    padding: 1.5rem 2rem 1.5rem 2rem;

    .error {
      align-self: flex-start;
      color: clr('red');
      font-size: $font-size-sm;
      margin-top: 1rem;

      .value {
        font-weight: $font-weight-bold;
      }
    }
  }

  .body {
    padding: 2rem;

    h3 {
      margin-bottom: 0.5rem;
    }

    .remove-from-others {
      @include flex("block", "row", "start", "center");
      cursor: pointer;
      font-size: $font-size-xs;
      margin-top: 1rem;

      &.no-selected-theme {
        cursor: default;

        .text {
          margin-left: 0;
        }
      }

      .text {
        margin-left: 0.5rem;
      }
    }
  }

  .footer {
    padding: 1rem 2rem 1rem 1.5rem;

    .loading-blocks-overlay {
      height: 2rem;
    }
  }
}
</style>
