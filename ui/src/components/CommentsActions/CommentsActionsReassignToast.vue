<template>
  <section class="comments-actions-reassign-toast">
    <section class="toast-content">
      <section class="left">
        <i class="fa fa-exchange icon"></i>
        <section class="text">
          <span class="highlight">{{ textCommentListSize }} Comments</span>
          reassigned to
          <span class="highlight">{{ textTargetLabel }}</span>.
        </section>
      </section>
      <section class="right">
        <base-button class="dismiss" colour="light" @click="onDismiss">Dismiss</base-button>
      </section>
    </section>
  </section>
</template>

<script>
import { GitMergeIcon } from 'vue-feather-icons';
import { mapActions, mapState } from 'vuex';
import BaseButton from '@/components/Base/BaseButton';
import Toast from '@/components/Mixins/Toast';
import TopicAction from '@/enum/topic-action';

export default {
  name: 'comments-actions-reassign-toast',

  components: {
    BaseButton,
    GitMergeIcon,
  },

  mixins: [Toast],

  data() {
    return {
      count: 3,
      countWatcher: null,
    };
  },

  computed: {
    ...mapState('toast', ['toastData']),

    textCommentListSize() {
      if (this.toastData.action === TopicAction.ACTION_SNIPPET_REASSIGN_EXISTING_THEME.value()) {
        return this.toastData.docCount;
      }
      return this.toastData.analysisTopic.numOfDocuments;
    },

    textTargetLabel() {
      return this.toastData.analysisTopic.topicLabel;
    },
  },

  created() {
    this.countWatcher = setInterval(() => {
      this.count -= 1;
      if (this.count === 0) {
        clearInterval(this.countWatcher);
        this.close();
      }
    }, 1000);
  },

  beforeDestroy() {
    this.setCommentsToReassign({ ids: [] });
    this.remove({ id: 'themes-reassign' });
    this.removeToastData();
  },

  methods: {
    ...mapActions('snippets', ['setCommentsToReassign']),

    ...mapActions('toast', ['remove', 'removeToastData']),

    onDismiss() {
      this.close();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-actions-reassign-toast {
  @include flex("block", "row", "end", "start");

  .toast-content {
    @include toast;

    background-color: clr("purple");
    padding: 1rem;

    .left {
      @include flex("block", "row", "start", "center");

      .icon {
        font-size: $font-size-lg;
        margin-right: 0.2rem;
      }

      .text {
        @include flex("block", "row", "start", "start");

        .highlight {
          font-weight: $font-weight-bold;
          margin: 0 0.5rem;
        }
      }
    }

    .right {
      margin-left: 2rem;
    }
  }
}
</style>
