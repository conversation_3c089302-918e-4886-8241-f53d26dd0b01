<template>
  <section class="comments-actions-target-existing-sub-topic-list">
    <section class="sub-topic-list">
      <comments-actions-target-existing-sub-topic
          v-for="subTopic in sortedList"
          :key="subTopic.id"
          :sub-topic="subTopic"
      />
    </section>
    <section class="sub-topic-end">
      <span class="icon-wrapper" @click="onClickClose">
        <i class="fa fa-x icon" />
      </span>
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';
import CommentsActionsTargetExistingSubTopic from '@/components/CommentsActions/CommentsActionsTargetExistingSubTopic';
import SortDirection from '@/enum/sort-direction';
import ThemesSort from '@/enum/themes-sort';

export default {
  name: 'comments-actions-target-existing-sub-topic-list',

  components: {
    CommentsActionsTargetExistingSubTopic,
  },

  props: {
    subTopicList: {
      type: Array,
      required: true,
    },
  },

  computed: {
    ...mapState('snippets', ['targetSort', 'targetSortDirection']),

    sortedList() {
      return this.subTopicList
        .sort((a, b) => {
          // score
          if (this.targetSort === ThemesSort.ADORESCORE && this.targetSortDirection === SortDirection.ASC) {
            return a.polarity - b.polarity;
          }
          if (this.targetSort === ThemesSort.ADORESCORE && this.targetSortDirection === SortDirection.DESC) {
            return b.polarity - a.polarity;
          }
          // vol
          if (this.targetSort === ThemesSort.VOLUME && this.targetSortDirection === SortDirection.ASC) {
            return a.numOfDocuments - b.numOfDocuments;
          }
          if (this.targetSort === ThemesSort.VOLUME && this.targetSortDirection === SortDirection.DESC) {
            return b.numOfDocuments - a.numOfDocuments;
          }
          // label - default: label ASC
          if (this.targetSort === ThemesSort.NAME && this.targetSortDirection === SortDirection.DESC) {
            return b.topicLabel.toLowerCase().localeCompare(a.topicLabel.toLowerCase());
          }
          return a.topicLabel.toLowerCase().localeCompare(b.topicLabel.toLowerCase());
        });
    },
  },

  methods: {
    onClickClose() {
      this.$emit('onClose');
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-actions-target-existing-sub-topic-list {

  .sub-topic-list {
    @include scrollbar-thin;
    max-height: 160px;
    overflow-x: hidden;
    overflow-y: auto;

    // Support overlay scrollbar for webkit browsers
    @media screen and (-webkit-min-device-pixel-ratio: 0) {
      overflow: overlay;
    }

    .comments-actions-target-existing-sub-topic {
      margin-bottom: 1rem;
      padding: 0 0.5rem 0 1.5rem;
    }
  }

  .sub-topic-end {
    align-items: center;
    display: grid;
    justify-items: center;
    height: 0;

    .icon-wrapper {
      align-items: center;
      background-color: #2B1093;
      border-radius: 50%;
      cursor: pointer;
      display: grid;
      height: 1.2rem;
      justify-items: center;
      position: relative;
      top: 0.4rem;
      width: 1.2rem;

      &:hover {
        .icon {
          transform: scale(1.2);
        }
      }

      .icon {
        color: #FFF;
        font-size: 0.6rem;
      }
    }
  }
}
</style>
