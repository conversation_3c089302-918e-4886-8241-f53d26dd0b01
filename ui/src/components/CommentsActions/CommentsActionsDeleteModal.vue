<template>
  <section class="comments-actions-delete-modal">
    <section class="header">
      <section class="left">
        <h2>Delete Comments</h2>
      </section>

      <section class="right" @click="onCancel">
        <x-icon class="icon"/>
      </section>
    </section>
    <section class="body">
      <section class="warning">
        <alert-circle-icon class="icon"/>
        <section class="text">This action cannot be undone.</section>
      </section>

      <section class="info">
        This will delete {{ selectedCount }} comment{{ selectedCount === 1 ? '' : 's' }} from the dataset. Are you sure?
      </section>
    </section>
    <section class="footer">
      <base-button class="cancel" colour="light" size="small" type="link" @click="onCancel">Cancel</base-button>
      <base-button class="confirm" colour="danger" size="small" @click="onConfirm">Delete Comments</base-button>
    </section>
  </section>
</template>

<script>
import { AlertCircleIcon, XIcon } from 'vue-feather-icons';
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';

import { datasetApi, snippetApi } from '@/services/api';
import { datasetsRequest, datasetsRequestV0, metadataRequest, themesRequest } from '@/services/request';

export default {
  name: 'comments-actions-delete-modal',

  components: {
    AlertCircleIcon,
    BaseButton,
    XIcon,
  },

  computed: {
    ...mapState('datasets', { datasetId: 'active' }),

    ...mapGetters('searchFilters', { filters: 'transformed' }),

    ...mapGetters('snippets', [
      'getFilterBackendMappingList',
      'selectedSnippetsParentDocIds',
      'snippetsCount',
    ]),

    ...mapState('snippets', ['selectedSnippets', 'selectedAll']),

    ...mapGetters('snippetsFilter', [
      'activeChildDatasetIsUnsaved',
      'getActiveChildDatasetId',
      'getActiveFilterViewObj',
      'isViewingChildDataset',
    ]),

    ...mapGetters('themes', ['snippetTopicId']),

    ...mapGetters('themesFilter', ['getParentThemeIdsByChildThemeIds']),

    selectedCount() {
      return this.selectedAll ? this.snippetsCount : this.selectedSnippets.length;
    },
  },

  methods: {
    ...mapActions('datasets', ['addOverviews', 'removeOverviews']),

    ...mapActions('modal', ['closeModal']),

    ...mapActions('snippets', [
      'confirmRemove',
      'deselectSnippets',
      'deselectAllSnippets',
      'remove',
      'removeAll',
      'resetSnippets',
      'selectAllSnippets',
    ]),

    ...mapActions('themes', ['selectTheme', 'setThemes']),

    async doDeleteAllComments() {
      this.removeAll();

      const requestBody = {
        topicId: this.snippetTopicId,
        filters: this.getFilterBackendMappingList,
        searchScoreFilters: this.filters,
      };

      if (this.isViewingChildDataset) {
        await snippetApi.deleteAllSnippets(this.getActiveChildDatasetId, requestBody);

        // send the same request for parent dataset
        // when users are working on a temporary child dataset
        if (this.activeChildDatasetIsUnsaved) {
          const parentDatasetThemeIds = this.getParentThemeIdsByChildThemeIds({ ids: [this.snippetTopicId] });
          requestBody.topicId = parentDatasetThemeIds?.length > 0 ? parentDatasetThemeIds[0] : null;

          await snippetApi.deleteAllSnippets(this.datasetId, requestBody);
        }
      } else {
        await snippetApi.deleteAllSnippets(this.datasetId, requestBody);
      }
    },

    async doDeleteCommentsByIds() {
      this.remove({ ids: this.selectedSnippets });

      if (this.isViewingChildDataset) {
        await snippetApi.deleteSnippets(this.selectedSnippets, this.getActiveChildDatasetId);

        // send the same request for parent dataset
        // when users are working on a temporary child dataset
        if (this.activeChildDatasetIsUnsaved) {
          // note - should just re-apply filters? is it better / faster?
          await snippetApi.deleteSnippets(this.selectedSnippetsParentDocIds, this.datasetId);
        }
      } else {
        await snippetApi.deleteSnippets(this.selectedSnippets, this.datasetId);
      }

      this.confirmRemove({ ids: this.selectedSnippets });
      this.deselectSnippets({ ids: this.selectedSnippets });
    },

    async doReloadOverviews() {
      const dsIds = [];
      if (this.isViewingChildDataset) {
        dsIds.push(this.getActiveChildDatasetId);
        if (this.activeChildDatasetIsUnsaved) {
          dsIds.push(this.datasetId);
        }
      } else {
        dsIds.push(this.datasetId);
      }

      const overviews = await datasetsRequest.fetchOverviews([...dsIds], 'overview');
      this.removeOverviews({ ids: dsIds });
      this.addOverviews({ overviews });
    },

    async fetchThemes() {
      const themes = await themesRequest.fetchThemes();
      this.setThemes({ themes });
    },

    async getSnippets() {
      this.deselectAllSnippets();
      this.resetSnippets();
      await metadataRequest.filterCommentsOnMetadata();
      await metadataRequest.filterCommentsCountOnMetadata();
    },

    onCancel() {
      return this.closeModal();
    },

    async onConfirm() {
      this.closeModal();
      let deleteAll = false;

      if (this.selectedAll) {
        deleteAll = true;
        await this.doDeleteAllComments();
      } else {
        deleteAll = this.selectedSnippets.length === this.snippetsCount;
        await this.doDeleteCommentsByIds();
      }

      await this.doReloadOverviews();

      if (deleteAll) this.selectTheme({ theme: null });

      await Promise.all([
        datasetsRequestV0.reloadSelected(),
        this.getSnippets(),
        this.fetchThemes(),
      ]);

      // call this after reloadSelected() / getDatasets()
      // reload child filter-views after parent-dataset
      if (this.isViewingChildDataset) {
        await datasetApi.refresh([this.getActiveChildDatasetId]);
        await metadataRequest.getAllFilterViewsIntoLocalDatasetsList(this.datasetId);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.comments-actions-delete-modal {
  @include modal;

  .header {
    @include modal-header;
  }

  .body {
    font-size: $font-size-sm;
    padding: 2rem;

    .warning {
      @include flex("block", "row", "start", "center");

      background-color: rgba(clr('red'), 0.1);
      border: 1px solid clr('red');
      border-radius: $border-radius-medium;
      color: clr("red");
      font-weight: $font-weight-medium;
      padding: 0.5rem;

      .icon {
        height: 1rem;
        margin-right: 0.5rem;
        width: 1rem;
      }

      .text {
        font-size: $font-size-xs;
      }
    }

    .info {
      margin-top: 1rem;
    }
  }
}
</style>
