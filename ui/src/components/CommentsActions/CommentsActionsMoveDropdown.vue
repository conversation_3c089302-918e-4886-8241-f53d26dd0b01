<template>
  <section class="comments-actions-move-dropdown">
    <section v-if="index != null && selectedTheme != null" class="item" @click="onClickEmotion">
      <smile-icon class="icon"/>
      <span class="text">Move Current Emotion</span>
    </section>

    <section class="item" @click="onClickExisting">
      <i class="fa fa-exchange icon"></i>
      <span class="text">Move To <span class="strong">Existing Theme</span></span>
    </section>

    <section class="item" @click="onClickNew">
      <i class="fa fa-exchange icon"></i>
      <span class="text">Move To <span class="strong">New Theme</span></span>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';
import { SmileIcon } from 'vue-feather-icons';

import BlurCloseable from '@/components/Mixins/BlurCloseable';
import CommentsActionsMoveEmotion from '@/components/CommentsActions/CommentsActionsMoveEmotion';
import CommentsActionsMoveToExisting from '@/components/CommentsActions/CommentsActionsMoveToExisting';
import CommentsActionsMoveToNew from '@/components/CommentsActions/CommentsActionsMoveToNew';

export default {
  name: 'comments-actions-move-dropdown',

  components: {
    SmileIcon,
  },

  mixins: [BlurCloseable],

  computed: {
    ...mapState('themes', ['index', 'selectedTheme']),

    ...mapGetters('snippets', ['snippetsCount']),

    ...mapState('snippets', ['selectedSnippets', 'selectedAll']),
  },

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    ...mapActions('snippets', ['setCommentsToReassign']),

    ...mapActions('themes', ['setIndexReassign']),

    initReassign() {
      this.setIndexReassign({ index: null });

      if (this.selectedAll) {
        this.setCommentsToReassign({ ids: new Array(this.snippetsCount) });
      } else {
        this.setCommentsToReassign({ ids: this.selectedSnippets });
      }

      this.$emit('close');
    },

    onClickEmotion() {
      if (this.index != null) {
        this.initReassign();
        this.setModalComponent({ component: CommentsActionsMoveEmotion });
      }
    },

    onClickExisting() {
      this.initReassign();
      this.setModalComponent({ component: CommentsActionsMoveToExisting });
    },

    onClickNew() {
      this.initReassign();
      this.setModalComponent({ component: CommentsActionsMoveToNew });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.comments-actions-move-dropdown {
  @include flex("block", "column", "start", "stretch");

  .item {
    @include flex("block", "row", "start", "center");

    cursor: pointer;
    padding: 0.5rem 1rem;
    transition: all $interaction-transition-time;

    &:hover {
      color: clr('purple');
    }

    &:not(:first-child) {
      border-top: solid 1px $border-color;
    }

    .icon {
      color: inherit;
      font-size: $font-size-sm;
      height: $font-size-sm;
      margin-right: 0.5rem;
      width: $font-size-sm;
    }

    .text {
      color: inherit;
      font-size: $font-size-xs;
      font-weight: 400;

      .strong {
        font-weight: 600;
      }
    }
  }
}
</style>
