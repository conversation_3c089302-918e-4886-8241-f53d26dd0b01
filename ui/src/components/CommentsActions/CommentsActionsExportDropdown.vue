<template>
  <section class="comments-actions-export-dropdown">
    <section class="item" @click="onClickExportTop15Vol">
      <i class="fa-regular fa-list-ol icon" />
      <span class="text">Top 15 Vol Themes</span>
    </section>
    <section class="item" :class="{ disabled: !hasSelectedComments }" @click="onClickExportSelectedComments">
      <i class="fa-regular fa-comment icon" />
      <span class="text">Selected Comments</span>
    </section>
    <section class="item" :class="{ disabled: !hasSelectedThemes }" @click="onClickExportSelectedThemes">
      <i class="fa-regular fa-list icon" />
      <span class="text">Selected Themes</span>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BlurCloseable from '@/components/Mixins/BlurCloseable';
import CommentsActionsExportMoreModal from '@/components/CommentsActions/CommentsActionsExportMoreModal';

export default {
  name: 'comments-actions-export-dropdown',

  mixins: [BlurCloseable],

  computed: {
    ...mapState('snippets', ['selectedSnippets']),

    ...mapState('themes', ['selectedThemes']),

    hasSelectedComments() {
      return this.selectedSnippets?.length > 0;
    },

    hasSelectedThemes() {
      return this.selectedThemes?.length > 0;
    },
  },

  methods: {
    ...mapActions('modal', ['setModalComponentAndProps']),

    onClickExportSelectedComments() {
      if (this.hasSelectedComments) {
        this.$emit('close');
        this.setModalComponentAndProps({
          component: CommentsActionsExportMoreModal,
          props: {
            fromSelectedComments: true,
            fromTop15Vol: false,
          },
        });
      }
    },

    onClickExportSelectedThemes() {
      if (this.hasSelectedThemes) {
        this.$emit('close');
        this.setModalComponentAndProps({
          component: CommentsActionsExportMoreModal,
          props: {
            fromSelectedComments: false,
            fromTop15Vol: false,
          },
        });
      }
    },

    onClickExportTop15Vol() {
      this.$emit('close');
      this.setModalComponentAndProps({
        component: CommentsActionsExportMoreModal,
        props: {
          fromSelectedComments: false,
          fromTop15Vol: true,
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.comments-actions-export-dropdown {
  @include flex("block", "column", "start", "stretch");

  .item {
    @include flex("block", "row", "start", "center");

    cursor: pointer;
    padding: 0.5rem 1rem;
    transition: all $interaction-transition-time;

    &:hover {
      &:not(.disabled) {
        color: clr('purple');
      }
    }

    &:not(:first-child) {
      border-top: solid 1px $border-color;
    }

    &.disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }

    .icon {
      color: inherit;
      font-size: $font-size-sm;
      height: $font-size-sm;
      margin-right: 0.5rem;
      width: $font-size-sm;
    }

    .text {
      color: inherit;
      font-size: $font-size-xs;
      font-weight: 400;
    }
  }
}
</style>
