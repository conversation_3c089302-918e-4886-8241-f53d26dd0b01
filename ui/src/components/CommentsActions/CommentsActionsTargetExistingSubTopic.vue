<template>
  <section class="comments-actions-target-existing-sub-topic">
    <section class="target-sub-topic-body" @click="onClick">
      <base-checkbox :value="isSelected" class="checkbox" />
      <section class="label">
        {{ textSubTopicLabel }}
      </section>
    </section>
    <section class="target-sub-topic-vol">
      <span class="strong">Vol</span> {{ textSubTopicVol }}
    </section>
    <section class="target-sub-topic-score">
      <adorescore-box-mini :score="adorescore" :bucket="bucket" />
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';
import AdorescoreBoxMini from '@/components/AdorescoreBox/AdorescoreBoxMini';
import BaseCheckbox from '@/components/Base/BaseCheckbox';

export default {
  name: 'comments-actions-target-existing-sub-topic',

  components: {
    AdorescoreBoxMini,
    BaseCheckbox,
  },

  props: {
    subTopic: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapGetters('benchmark', ['classifyAdorescore']),

    ...mapState('snippets', ['targetThemeIdsToReassign']),

    adorescore() {
      return Math.round(this.subTopic.polarity * 100);
    },

    bucket() {
      return this.classifyAdorescore(this.adorescore);
    },

    isSelected() {
      return this.targetThemeIdsToReassign?.includes(this.subTopic.id) || false;
    },

    textSubTopicLabel() {
      return this.subTopic.topicLabel || 'Unknown label';
    },

    textSubTopicVol() {
      return this.subTopic.numOfDocuments || 0;
    },
  },

  methods: {
    ...mapActions('snippets', ['addTargetThemeIdsToReassign', 'removeTargetThemeIdsToReassign']),

    onClick() {
      if (this.isSelected) {
        this.removeTargetThemeIdsToReassign({ ids: [this.subTopic.id] });
      } else {
        this.addTargetThemeIdsToReassign({ ids: [this.subTopic.id] });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-actions-target-existing-sub-topic {
  align-items: center;
  display: grid;
  grid-column-gap: 0.5rem;
  grid-template-columns: 1fr minmax(12%, auto) 10%;

  .target-sub-topic-body {
    align-items: center;
    cursor: pointer;
    display: grid;
    font-size: $font-size-sm;
    grid-column-gap: 0.5rem;
    grid-template-columns: 14px 1fr;

    .label {
      @include truncate;
    }
  }

  .target-sub-topic-vol {
    font-size: $font-size-sm;

    .strong {
      font-weight: $font-weight-bold;
    }
  }

  .target-sub-topic-score {
    font-size: $font-size-sm;
  }
}
</style>
