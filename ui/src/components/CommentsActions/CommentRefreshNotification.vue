<template>
  <section class="comment-refresh-notification">
    <h3 v-if="selectedCount > 1">Some topics have pending changes which may affect the scores.</h3>
    <h3 v-else-if="isFinished">This topic has pending changes which may affect the scores.</h3>
    <h3 v-else>This dataset is being re-processed.</h3>
    <base-button icon="refresh-ccw" size="small" type="link" @click="refreshAll">Refresh</base-button>
  </section>
</template>

<script>
import { RefreshCcwIcon } from 'vue-feather-icons';
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';

import { datasetsRequest, datasetsRequestV0, themesRequest } from '@/services/request';

export default {
  name: 'comment-refresh-notification',

  components: {
    BaseButton,
    RefreshCcwIcon,
  },

  computed: {
    ...mapGetters('datasets', [
      'getAllPendingChanges',
      'isFinished',
      'selectedCount',
      'selectedLimited',
    ]),

    ...mapState('themes', ['type']),
  },

  methods: {
    ...mapActions('datasets', ['setOverviews']),

    ...mapActions('themes', ['setThemes']),

    async refreshAll() {
      await Promise.all([
        datasetsRequestV0.reloadSelected(),
        this.getSnippets(),
        this.fetchThemes(),
      ]);
      const themes = await themesRequest.fetchThemes();

      const overviews = await datasetsRequest.fetchOverviews([this.datasetId], 'overview');
      this.removeOverviews({ ids: [this.datasetId] });
      this.addOverviews({ overviews });

      this.setOverviews({ overviews });
      this.setThemes({ themes });
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.comment-refresh-notification {
  @include flex("block", "row", "start", "center");
  @include rigid;

  background-color: clr("orange");
  color: clr("white");
  padding: 0.4em 1.4em;
  position: sticky;
  top: 0px;
  z-index: 9999;

  .base-button {
    &.size-small {
      padding: 0.3rem 0.5rem;
      margin: 0;
    }

    span {
      color: rgba(clr("white"), 0.6);
      font-size: $font-size-xs;
      transition: all $interaction-transition-time;

      &:hover {
        color: clr("white");
      }

      .base-icon {
        height: $font-size-sm;
        margin-right: 0;
      }
    }
  }

  h3 {
    font-size: $font-size-sm;
    margin: 0;
  }
}
</style>
