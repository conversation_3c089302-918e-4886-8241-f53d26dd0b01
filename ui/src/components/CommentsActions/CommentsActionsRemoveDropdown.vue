<template>
  <section class="comments-actions-remove-dropdown">
    <section class="item" @click="onClickRemove">
      <i class="fa-regular fa-ban icon" />
      <span class="text">Remove from <span class="strong">Theme</span></span>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BlurCloseable from '@/components/Mixins/BlurCloseable';
import CommentsActionsRemoveModal from '@/components/CommentsActions/CommentsActionsRemoveModal';

export default {
  name: 'comments-actions-remove-dropdown',

  mixins: [BlurCloseable],

  computed: {
    ...mapGetters('snippets', ['snippetsCount']),

    ...mapState('snippets', ['selectedSnippets', 'selectedAll']),
  },

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    ...mapActions('snippets', ['setCommentsToReassign']),

    ...mapActions('themes', ['setIndexReassign']),

    initReassign() {
      this.setIndexReassign({ index: null });

      if (this.selectedAll) {
        this.setCommentsToReassign({ ids: new Array(this.snippetsCount) });
      } else {
        this.setCommentsToReassign({ ids: this.selectedSnippets });
      }

      this.$emit('close');
    },

    onClickRemove() {
      this.initReassign();
      this.setModalComponent({ component: CommentsActionsRemoveModal });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.comments-actions-remove-dropdown {
  @include flex("block", "column", "start", "stretch");

  .item {
    @include flex("block", "row", "start", "center");

    cursor: pointer;
    padding: 0.5rem 1rem;
    transition: all $interaction-transition-time;

    &:hover {
      color: clr("red");
    }

    .icon {
      color: inherit;
      font-size: $font-size-sm;
      height: $font-size-sm;
      margin-right: 0.5rem;
      width: $font-size-sm;
    }

    .text {
      color: inherit;
      font-size: $font-size-xs;
      font-weight: 400;

      .strong {
        font-weight: 600;
      }
    }
  }
}
</style>
