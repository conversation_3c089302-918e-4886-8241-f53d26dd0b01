<template>
  <section class="comments-actions-export-more-modal">
    <section class="header">
      <h2>
        <i class="fa-regular fa-file-export icon"></i>
        <span>{{ textHeader }}</span>
      </h2>
    </section>
    <section class="body">
      <section class="body-block contribution">
        <section class="subtitle">Include:</section>
        <section class="opts">
          <section class="opt-item" @click.stop="onClickAdoreScore">
            <base-checkbox-solid :value="adoreScore" />
            <span class="label">Adorescore</span>
          </section>
          <section class="opt-item" @click.stop="onClickContributionScore">
            <base-checkbox-solid :value="contributionScore" />
            <span class="label">Contribution Score</span>
          </section>
          <section class="opt-item" @click.stop="onClickContributionBreakdown">
            <base-checkbox-solid :value="contributionBreakdown" />
            <span class="label">Breakdown (Adored / Floored / Ignored)</span>
          </section>
        </section>
      </section>
      <section class="body-block metadata" v-if="metadataOptions.length > 0">
        <section class="title">Dataset Metadata:</section>
        <section class="opts opts-metadata">
          <section class="opt-item metadata-all" @click.stop="onClickMetadataAll">
            <base-checkbox-solid :value="isMetadataAllSelected" />
            <span class="label">Select All</span>
          </section>
          <section class="opts-metadata-wrapper">
            <section v-for="metadata in metadataOptions"
                     class="opt-item list-item" @click.stop="onClickMetadataOption(metadata.column)">
              <base-checkbox-solid :value="metadataSelected.includes(metadata.column)" />
              <span class="label">{{ metadata.name}}</span>
            </section>
          </section>
        </section>
      </section>
    </section>
    <section class="footer">
      <base-button class="cancel" colour="light" size="small" type="link" @click="onCancel">Cancel</base-button>
      <base-button class="confirm" colour="base" size="small" @click="onConfirm">
        <i class="fa-solid fa-download icon" />
        Download
      </base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import api from '@/helpers/api';
import BaseButton from '@/components/Base/BaseButton';
import BaseCheckboxSolid from '@/components/Base/BaseCheckboxSolid';
import CommentsDownloadToast from '@/components/CommentsActions/CommentsDownloadToast';

import { snippetApi } from '@/services/api';

export default {
  name: 'comments-actions-export-more-modal',

  components: {
    BaseButton,
    BaseCheckboxSolid,
    CommentsDownloadToast,
  },

  props: {
    componentProps: {
      fromSelectedComments: {
        type: Boolean,
        required: true,
      },
      fromTop15Vol: {
        type: Boolean,
        required: true,
      },
    },
  },

  computed: {
    ...mapGetters('datasets', { getDataset: 'get' }),

    ...mapState('datasets', { datasetId: 'active' }),

    ...mapGetters('snippets', ['getFilterMetadataList', 'selectedSnippetsParentDocIds']),

    ...mapState('snippets', ['selectedAll', 'selectedSnippets']),

    ...mapGetters('snippetsFilter', ['isViewingChildDataset']),

    ...mapGetters('themes', ['snippetTopicId']),

    ...mapState('themes', ['selectedThemes']),

    dataset() {
      return this.getDataset(this.datasetId);
    },

    isMetadataAllSelected() {
      return this.metadataOptions?.length > 0
        && this.metadataOptions.length === this.metadataSelected.length;
    },

    textHeader() {
      if (this.componentProps.fromSelectedComments) {
        return 'Export Selected Comments';
      }
      return this.componentProps.fromTop15Vol ? 'Export Comments from Top 15 Vol Themes' : 'Export Comments from Selected Themes';
    },
  },

  created() {
    const { metadataColumns, metadataHeaders, metadataTypes } = this.getFilterMetadataList({ datasetId: this.datasetId });

    if (metadataColumns.length && metadataHeaders.length && metadataTypes.length) {
      for (let i = 0; i < metadataColumns.length; i += 1) {
        // -1: Comments text
        if (metadataColumns[i] !== -1) {
          this.metadataOptions.push({
            column: metadataColumns[i],
            name: metadataHeaders[i],
            type: metadataTypes[i],
          });
        }
      }

      this.metadataOptions = this.metadataOptions.sort((a, b) => a.name.localeCompare(b.name));
    }
    this.metadataSelected = [];
  },

  data() {
    return {
      adoreScore: true,
      contributionBreakdown: true,
      contributionScore: true,
      metadataOptions: [],
      metadataSelected: [],
    };
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('toast', { addToast: 'add', removeToast: 'remove' }),

    onCancel() {
      this.closeModal();
    },

    async onConfirm() {
      this.removeToast({ id: 'comments-download' });
      // show the pop-up right away, so users don't have to wait and can continue on their things
      this.addToast({ toast: { component: CommentsDownloadToast, id: 'comments-download' } });

      this.closeModal();

      let docIds;
      let themeIds;
      if (this.componentProps.fromSelectedComments) {
        docIds = this.isViewingChildDataset ? this.selectedSnippetsParentDocIds : this.selectedSnippets;
        themeIds = this.snippetTopicId == null ? [] : [this.snippetTopicId];
      } else {
        docIds = [];
        themeIds = [...this.selectedThemes];
      }

      const requestBody = {
        docIds: this.componentProps.fromTop15Vol ? [] : docIds,
        fromTopThemesByVol: this.componentProps.fromTop15Vol ? 15 : 0,
        metadataColumns: this.metadataSelected,
        selectAll: this.selectedAll,
        showAdoreScore: this.adoreScore,
        showContributionBreakdown: this.contributionBreakdown,
        showContributionScore: this.contributionScore,
        themeIds: this.componentProps.fromTop15Vol ? [] : themeIds,
      };
      const response = await api.instance().post(
        snippetApi.exportSnippets(this.datasetId),
        requestBody,
        {
          responseType: 'blob',
          params: null,
        },
      );
      if (response.status === 200) {
        const blob = new Blob([response.data]);
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `${this.dataset.label} - Exported Comments.csv`;
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    },

    onClickAdoreScore() {
      this.adoreScore = !this.adoreScore;
    },

    onClickContributionBreakdown() {
      this.contributionBreakdown = !this.contributionBreakdown;
    },

    onClickContributionScore() {
      this.contributionScore = !this.contributionScore;
    },

    onClickMetadataAll() {
      if (this.isMetadataAllSelected) {
        this.metadataSelected = [];
      } else {
        this.metadataSelected = this.metadataOptions.map(item => item.column);
      }
    },

    onClickMetadataOption(column) {
      if (this.metadataSelected.includes(column)) {
        this.metadataSelected = this.metadataSelected.filter(item => item !== column);
      } else {
        this.metadataSelected.push(column);
      }
    },
  },
};

</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.comments-actions-export-more-modal {
  @include modal;

  .header {
    @include flex("block", "row", "start", "center");
    padding: 1.5rem 2rem 1.5rem 2rem;

    h2 {
      font-size: 18px;
    }
  }

  .body {
    max-height: 620px;
    padding: 0;

    .body-block {
      padding: 2rem;

      &:not(:last-child) {
        border-bottom: 1px solid $border-color;
      }

      .subtitle {
        font-style: italic;
        opacity: 0.8;
        margin-bottom: 1rem;
      }

      .title {
        font-size: $font-size-sm;
        font-weight: $font-weight-bold;
        margin-bottom: 1rem;
      }

      .opts {
        font-size: $font-size-sm;

        .opt-item {
          @include flex("block", "row", "start", "center");
          cursor: pointer;

          &:hover {
            color: clr('purple');
            opacity: 0.7;
          }

          &:not(:first-child) {
            margin-top: 0.5rem;
          }

          &.list-item {
            margin-left: 1.5rem;
          }

          &.metadata-all {
            margin-bottom: 0.5rem;
          }

          .label {
            margin-left: 0.5rem;
            overflow-x: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }

      &.metadata {
        padding-bottom: 0;

        .opts-metadata-wrapper {
          @include scrollbar-thin;
          max-height: 200px;
          overflow-y: auto;
          padding-bottom: 2rem;
        }
      }
    }
  }

  .footer {
    padding: 1rem 2rem 1rem 1.5rem;

    .confirm {
      font-weight: $font-weight-bold;

      .icon {
        margin-right: 0.3rem;
      }
    }
  }
}

</style>
