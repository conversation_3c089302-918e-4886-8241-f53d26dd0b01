<template>
  <section class="comments-actions-copy-toast">
    <section class="toast-content">
      <section class="left">
        <clipboard-icon class="icon"/>
        <section class="text">
          {{ selectedCount }} Comment{{ selectedCount > 1 ? 's' : '' }} Copied to Clipboard
        </section>
      </section>
      <section class="right">
        <base-button class="dismiss" colour="light" @click="onDismiss">Dismiss</base-button>
      </section>
    </section>
  </section>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import { ClipboardIcon } from 'vue-feather-icons';

import BaseButton from '@/components/Base/BaseButton';
import Toast from '@/components/Mixins/Toast';

export default {
  name: 'comments-actions-copy-toast',

  components: {
    BaseButton,
    ClipboardIcon,
  },

  mixins: [Toast],

  computed: {
    ...mapGetters('search', ['visibleExcludedComments']),

    ...mapState('search', [
      'count',
      'countOfVisibleDataset',
      'excluded',
      'hiddenDatasets',
      'snippets',
      'text',
    ]),

    ...mapState('snippets', ['selectedSnippets']),

    excludedSearchCount() {
      if (this.excluded?.length) {
        return this.visibleExcludedComments.length;
      }
      return 0;
    },

    isSearchView() {
      return Boolean(this.text);
    },

    selectedCount() {
      return this.isSearchView ? this.selectedSearchCount : this.selectedSnippets.length;
    },

    selectedSearchCount() {
      return this.snippetSearchCount - this.excludedSearchCount;
    },

    snippetSearchCount() {
      if (this.hiddenDatasets?.length) {
        return this.countOfVisibleDataset;
      }
      return this.count;
    },
  },

  created() {
    setTimeout(this.close, 3000);
  },

  methods: {
    onDismiss() {
      this.close();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-actions-copy-toast {
  @include flex("block", "row", "end", "start");

  .toast-content {
    @include toast;

    .left {
      @include flex("block", "row", "start", "center");

      .icon {
        margin-right: 0.5rem;
      }

      .text {
        @include flex("block", "row", "start", "center");
      }
    }

    .right {
      margin-left: 2rem;
    }
  }
}
</style>
