<template>
  <section class="comments-actions-target-existing-theme"
           :class="{ isExpanded }"
  >
    <section class="target-theme-wrapper">
      <section class="target-theme-body" @click="onClick">
        <base-checkbox :value="isSelected" class="checkbox" />
        <section class="label">
          {{ textThemeLabel }}
        </section>
      </section>
      <section class="target-theme-subtopics-count"
               :class="{ hasSubTopics }"
               @click="onClickSubTopicList">
        <comments-actions-target-existing-sub-topic-tag v-if="hasSubTopics" :isExpanded="isExpanded" :theme="theme" />
      </section>
      <section class="target-theme-vol">
        <span class="strong">Vol</span> {{ textThemeVol }}
      </section>
      <section class="target-theme-score">
        <adorescore-box-mini :score="adorescore" :bucket="bucket" />
      </section>
    </section>
    <section v-show="hasSubTopics && isExpanded"
             class="target-theme-sub-topic-list-wrapper"
    >
      <loading-blocks-overlay v-if="loading">Loading subtopics...</loading-blocks-overlay>
      <comments-actions-target-existing-sub-topic-list
          v-else
          :sub-topic-list="subTopicList"
          @onClose="onCloseSubTopicList"
      />
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';
import AdorescoreBoxMini from '@/components/AdorescoreBox/AdorescoreBoxMini';
import BaseCheckbox from '@/components/Base/BaseCheckbox';
import CommentsActionsTargetExistingSubTopicList from '@/components/CommentsActions/CommentsActionsTargetExistingSubTopicList';
import CommentsActionsTargetExistingSubTopicTag from '@/components/CommentsActions/CommentsActionsTargetExistingSubTopicTag';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';

import { themesRequest } from '@/services/request';

export default {
  name: 'comments-actions-target-existing-theme',

  components: {
    AdorescoreBoxMini,
    BaseCheckbox,
    CommentsActionsTargetExistingSubTopicList,
    CommentsActionsTargetExistingSubTopicTag,
    LoadingBlocksOverlay,
  },

  props: {
    theme: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      isExpanded: false,
      loading: false,
      subTopicList: [],
    };
  },

  computed: {
    ...mapGetters('benchmark', ['classifyAdorescore']),

    ...mapState('snippets', ['targetThemeIdsToReassign']),

    adorescore() {
      return Math.round(this.theme.polarity * 100);
    },

    bucket() {
      return this.classifyAdorescore(this.adorescore);
    },

    hasSubTopics() {
      return (this.theme?.numOfSubTopics > 0) || false;
    },

    isSelected() {
      return this.targetThemeIdsToReassign?.includes(this.theme.id) || false;
    },

    textThemeLabel() {
      return this.theme.topicLabel || 'Unknown label';
    },

    textThemeVol() {
      return this.theme.numOfDocuments || 0;
    },
  },

  methods: {
    ...mapActions('snippets', ['addTargetThemeIdsToReassign', 'removeTargetThemeIdsToReassign']),

    async loadSubTopicList() {
      if (this.loading || this.subTopicList?.length) {
        return;
      }

      this.loading = true;
      const response = await themesRequest.fetchSubtopics([this.theme], null);
      if (response && typeof response === 'object') {
        this.subTopicList = response.filter(t => t.parentId === this.theme.id);
      } else {
        this.subTopicList = [];
      }

      this.loading = false;
    },

    onClick() {
      if (this.isSelected) {
        this.removeTargetThemeIdsToReassign({ ids: [this.theme.id] });
      } else {
        this.addTargetThemeIdsToReassign({ ids: [this.theme.id] });
      }
    },

    onClickSubTopicList() {
      if (this.hasSubTopics) {
        this.isExpanded = !this.isExpanded;
      } else {
        this.isExpanded = false;
      }
    },

    onCloseSubTopicList() {
      this.isExpanded = false;
    },
  },

  watch: {
    async isExpanded() {
      if (this.isExpanded) {
        await this.loadSubTopicList();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-actions-target-existing-theme {
  border: solid 1px transparent;
  margin-right: 0.5rem;
  padding: 1rem 0 1rem 0.5rem;

  &:not(:last-child) {
    border-bottom: solid 1px $border-color;
  }

  &:has(+ .isExpanded) {
    border-bottom: solid 1px transparent;
  }

  &.isExpanded {
    background-color: rgba(230, 227, 240, 0.5);
    border: solid 1px #3833E7;
    border-radius: 4px;

    &:has(+ .isExpanded) {
      margin-bottom: 1rem;
    }
  }

  .target-theme-wrapper {
    align-items: center;
    display: grid;
    grid-column-gap: 0.5rem;
    grid-template-columns: 1fr minmax(12%, auto) minmax(12%, auto) 10%;
    padding-right: 0.4rem;

    .target-theme-body {
      align-items: center;
      cursor: pointer;
      display: grid;
      font-size: $font-size-sm;
      grid-column-gap: 0.5rem;
      grid-template-columns: 14px 1fr;

      .label {
        @include truncate;
      }
    }

    .target-theme-subtopics-count {
      font-size: $font-size-sm;

      &.hasSubTopics {
        cursor: pointer;
      }
    }

    .target-theme-vol {
      font-size: $font-size-sm;

      .strong {
        font-weight: $font-weight-bold;
      }
    }

    .target-theme-score {
      font-size: $font-size-sm;
    }
  }

  .target-theme-sub-topic-list-wrapper {
    margin-top: 1rem;
  }
}
</style>
