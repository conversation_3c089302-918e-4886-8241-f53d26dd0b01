<template>
  <section class="comments-actions">
    <section class="action action-move"
             :class="{ disabled: selectedSnippetsAreEmpty }"
             v-show="isEditor"
             v-tooltip.bottom.start.notrigger="{
          html: 'comments-actions-move-dropdown',
          class: 'tooltip-reassign-action',
          delay: 0,
          visible: showMoveDropdown,
        }"
             @click.prevent.stop="onClickMoveDropdown"
    >
      <section class="btn-left">
        <i class="fa fa-exchange icon"></i>
        <section class="text">Move</section>
      </section>
      <section class="btn-right">
        <i class="fa fa-caret-down icon" :class="{ open: showMoveDropdown }"></i>
      </section>
    </section>

    <section class="action action-copy"
             :class="{ disabled: selectedSnippetsAreEmpty }"
             v-show="isEditor"
             v-tooltip.bottom.start.notrigger="{
          html: 'comments-actions-copy-dropdown',
          class: 'tooltip-reassign-action',
          delay: 0,
          visible: showCopyDropdown,
        }"
             @click.prevent.stop="onClickCopyDropdown"
    >
      <section class="btn-left">
        <i class="fa-regular fa-copy icon" />
        <section class="text">Copy</section>
      </section>
      <section class="btn-right">
        <i class="fa fa-caret-down icon" :class="{ open: showCopyDropdown }"></i>
      </section>
    </section>

    <section class="action action-remove"
             :class="{ disabled: selectedSnippetsAreEmpty }"
             v-show="isEditor"
             v-tooltip.bottom.start.notrigger="{
          html: 'comments-actions-remove-dropdown',
          class: 'tooltip-reassign-action',
          delay: 0,
          visible: showRemoveDropdown,
        }"
             @click.prevent.stop="onClickRemoveDropdown"
    >
      <section class="btn-left">
        <i class="fa-regular fa-ban icon" />
        <section class="text">Remove</section>
      </section>
      <section class="btn-right">
        <i class="fa fa-caret-down icon" :class="{ open: showRemoveDropdown }"></i>
      </section>
    </section>

    <section class="action action-export"
             v-show="isEditor"
             v-tooltip.bottom.start.notrigger="{
                html: 'comments-actions-export-dropdown',
                class: 'tooltip-reassign-action',
                delay: 0,
                visible: showExportDropdown,
              }"
             @click.prevent.stop="onClickExportDropdown"
    >
      <section class="btn-left">
        <i class="fa-regular fa-file-export icon"></i>
        <section class="text">Export</section>
      </section>
      <section class="btn-right">
        <i class="fa fa-caret-down icon" :class="{ open: showExportDropdown }"></i>
      </section>
    </section>

    <comments-actions-copy-dropdown id="comments-actions-copy-dropdown" @close="showCopyDropdown = false"/>
    <comments-actions-move-dropdown id="comments-actions-move-dropdown" @close="showMoveDropdown = false"/>
    <comments-actions-remove-dropdown id="comments-actions-remove-dropdown" @close="showRemoveDropdown = false"/>
    <comments-actions-export-dropdown id="comments-actions-export-dropdown" @close="showExportDropdown = false"/>

    <section class="action action-delete" v-if="isEditor" :class="{ disabled: selectedSnippetsAreEmpty }" @click="onClickDeleteSnippets">
      <i class="fa fa-trash-o icon"></i>
      <section class="text">Delete</section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseCheckbox from '@/components/Base/BaseCheckbox';
import PreventSampleAction from '@/components/Mixins/PreventSampleAction';
import SnippetType from '@/enum/snippet-type';
import CommentsActionsCopyDropdown from '@/components/CommentsActions/CommentsActionsCopyDropdown';
import CommentsActionsDeleteAllModal from '@/components/CommentsActions/CommentsActionsDeleteAllModal';
import CommentsActionsDeleteModal from '@/components/CommentsActions/CommentsActionsDeleteModal';
import CommentsActionsExportDropdown from '@/components/CommentsActions/CommentsActionsExportDropdown';
import CommentsActionsMoveDropdown from '@/components/CommentsActions/CommentsActionsMoveDropdown';
import CommentsActionsRemoveDropdown from '@/components/CommentsActions/CommentsActionsRemoveDropdown';

import { datasetApi, snippetApi } from '@/services/api';
import { datasetsRequest, datasetsRequestV0, metadataRequest, snippetsRequest, themesRequest } from '@/services/request';

export default {
  name: 'comments-actions',

  components: {
    BaseCheckbox,
    CommentsActionsCopyDropdown,
    CommentsActionsExportDropdown,
    CommentsActionsMoveDropdown,
    CommentsActionsRemoveDropdown,
  },

  mixins: [PreventSampleAction],

  data() {
    return {
      showCopyDropdown: false,
      showExportDropdown: false,
      showGlobalBookmark: false,
      showMoveDropdown: false,
      showRemoveDropdown: false,
      SnippetType,
    };
  },

  computed: {
    ...mapGetters('datasets', ['get', 'isEditable']),

    ...mapState('datasets', {
      datasetId: 'active',
    }),

    ...mapGetters('snippets', ['selectedSnippetsParentDocIds', 'snippetsCount']),

    ...mapState('snippets', {
      reassignList: 'commentsToReassign',
      selectedSnippets: 'selectedSnippets',
      selectedAll: 'selectedAll',
    }),

    ...mapGetters('snippetsFilter', [
      'activeChildDatasetIsUnsaved',
      'getActiveChildDatasetId',
      'getActiveFilterViewObj',
      'isViewingChildDataset',
    ]),

    ...mapState('themes', ['indexReassign', 'selectedTheme']),

    isDeletingAllComments() {
      // viewing saved child-dataset
      if (this.isViewingChildDataset && !this.activeChildDatasetIsUnsaved) {
        return this.selectedCount === this.get(this.getActiveChildDatasetId).documentCount;
      }
      // note: when viewing unsaved child-dataset, deleting-all logic should be the same as viewing parent-dataset
      // return true when selected count (on child-dataset) = total comment count (of parent-dataset)
      // otherwise, false. continue with other cases
      return this.selectedCount === this.dataset.documentCount;
    },

    isEditor() {
      return this.isEditable(this.datasetId);
    },

    isSample() {
      return this.get(this.datasetId).localSample;
    },

    selectedCount() {
      return this.selectedAll ? this.snippetsCount : this.selectedSnippets.length;
    },

    selectedSnippetsAreEmpty() {
      return this.selectedSnippets.length === 0;
    },

    dataset() {
      return this.get(this.datasetId);
    },
  },

  watch: {
    indexReassign() {
      if (this.indexReassign) {
        setTimeout(async () => {
          if (this.indexReassign) {
            this.doReassignComments();
          }
        }, 3000);
      }
    },

    selectedTheme() {
      this.setViewUnassigned({ view: false });
    },

    showUnassignedButton() {
      if (this.showUnassignedButton === false) {
        this.setViewUnassigned({ view: false });
      }
    },
  },

  methods: {
    ...mapActions('datasets', ['addOverviews', 'removeOverviews']),

    ...mapActions('modal', ['setModalComponent']),

    ...mapActions('snippets', [
      'confirmRemove',
      'deselectSnippets',
      'deselectAllSnippets',
      'remove',
      'resetSnippets',
      'setViewUnassigned',
      'selectAllSnippets',
    ]),

    ...mapActions('themes', ['setThemes', 'selectTheme']),

    async doDeleteOneComment() {
      const dsIds = [];
      const deleteAll = this.selectedSnippets.length === this.snippetsCount;
      this.remove({ ids: this.selectedSnippets });

      try {
        if (this.isViewingChildDataset) {
          dsIds.push(this.getActiveChildDatasetId);
          await snippetApi.deleteSnippets(this.selectedSnippets, this.getActiveChildDatasetId);

          // send the same request for parent dataset
          // when users are working on a temporary child dataset
          if (this.activeChildDatasetIsUnsaved) {
            dsIds.push(this.datasetId);
            await snippetApi.deleteSnippets(this.selectedSnippetsParentDocIds, this.datasetId);
          }
        } else {
          dsIds.push(this.datasetId);
          await snippetApi.deleteSnippets(this.selectedSnippets, this.datasetId);
        }
      } finally {
        this.confirmRemove({ ids: this.selectedSnippets });
        this.deselectSnippets({ ids: this.selectedSnippets });
        await this.doReloadOverviews(dsIds);
      }

      if (deleteAll) this.selectTheme({ theme: null });

      await Promise.all([
        datasetsRequestV0.reloadSelected(),
        this.getSnippets(),
        this.fetchThemes(),
      ]);

      // call this after reloadSelected() / getDatasets()
      // reload child filter-views after parent-dataset
      if (this.isViewingChildDataset) {
        await datasetApi.refresh([this.getActiveChildDatasetId]);
        await metadataRequest.getAllFilterViewsIntoLocalDatasetsList(this.datasetId);
      }
    },

    async doReassignComments() {
      await snippetApi.flagSnippet(this.datasetId, this.indexReassign.id, this.reassignList, this.indexReassign.old, this.indexReassign.new);
      this.deselectSnippets({ ids: this.reassignList });
      await Promise.all([this.getSnippets(), snippetsRequest.fetchBookmarks([this.datasetId])]);
      this.resetCommentsToReassign();
    },

    async doReloadOverviews(dsIds) {
      const overviews = await datasetsRequest.fetchOverviews([...dsIds], 'overview');
      this.removeOverviews({ ids: dsIds });
      this.addOverviews({ overviews });
    },

    async fetchThemes() {
      const themes = await themesRequest.fetchThemes();
      this.setThemes({ themes });
    },

    async getSnippets() {
      this.deselectAllSnippets();
      this.resetSnippets();
      await metadataRequest.filterCommentsOnMetadata();
      await metadataRequest.filterCommentsCountOnMetadata();
    },

    onClickCopyDropdown() {
      this.showExportDropdown = false;
      this.showMoveDropdown = false;
      this.showRemoveDropdown = false;
      if (!this.selectedSnippetsAreEmpty && !this.isSample) {
        this.showCopyDropdown = !this.showCopyDropdown;
      }

      if (this.isSample) this.preventSampleAction();
    },

    async onClickDeleteSnippets() {
      if (this.selectedSnippetsAreEmpty) return;

      if (this.isSample) {
        this.preventSampleAction();
      } else if (this.isDeletingAllComments) {
        this.setModalComponent({ component: CommentsActionsDeleteAllModal });
      } else if (this.selectedSnippets.length === 1) {
        // 1 comment - delete right away, no confirmation step
        await this.doDeleteOneComment();
      } else {
        this.setModalComponent({ component: CommentsActionsDeleteModal });
      }
    },

    onClickExportDropdown() {
      this.showCopyDropdown = false;
      this.showMoveDropdown = false;
      this.showRemoveDropdown = false;
      if (!this.isSample) {
        this.showExportDropdown = !this.showExportDropdown;
      }
    },

    onClickMoveDropdown() {
      this.showCopyDropdown = false;
      this.showExportDropdown = false;
      this.showRemoveDropdown = false;
      if (!this.selectedSnippetsAreEmpty && !this.isSample) {
        this.showMoveDropdown = !this.showMoveDropdown;
      }

      if (this.isSample) this.preventSampleAction();
    },

    onClickRemoveDropdown() {
      this.showCopyDropdown = false;
      this.showExportDropdown = false;
      this.showMoveDropdown = false;
      if (!this.selectedSnippetsAreEmpty && !this.isSample) {
        this.showRemoveDropdown = !this.showRemoveDropdown;
      }

      if (this.isSample) this.preventSampleAction();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-actions {
  @include flex("block", "row", "start", "center");
  @include stretch;

  align-items: center;
  background: clr("white");
  border-bottom: $border-standard;
  flex-wrap: wrap;
  min-height: 50px;
  padding: 0 1rem;
  width: 100%;

  .action {
    @include flex("block", "row", "start", "center");

    cursor: pointer;
    background: clr("purple", "rich");
    border: solid 1px clr("purple", "rich");
    border-radius: 2.5px;
    color:#FFF;
    font-size: 0.65rem;
    font-weight: $font-weight-bold;
    margin: 0 0.4rem 0 0;
    padding: 0.3rem 0.5rem;
    text-transform: uppercase;
    -webkit-transition: all 0.2s;
    -moz-transition: all 0.2s;
    transition: all 0.2s;

    &:hover {
       background:darken(clr("purple", "rich"), 10%);
    }

    &.disabled {
      cursor: not-allowed;
      opacity: 0.5;

      .icon {
        transform: scale(1);
      }
    }

    &.action-export {
      @include flex("block", "row", "start", "center");
      background: clr("white");
      border: solid 1px #d0d0d0;
      color: clr("purple", "rich");
      padding: 0;

      &:hover {
        border-color: clr("purple", "rich");

        .btn-left {
          border-right: 1px solid clr("purple", "rich");
        }
      }

      .btn-left {
        @include flex("block", "row", "start", "center");
        border-right: 1px solid #d0d0d0;
        padding: 0.3rem 0.5rem;
        transition: all 0.2s;
      }

      .btn-right {
        padding: 0.3rem 0.5rem;

        .icon {
          transition: all $interaction-transition-time;

          &.open {
            transform: rotate(180deg);
          }
        }
      }
    }

    &.action-delete {
      background-color: clr("white");
      border:$btn-delete-bdr 1px solid;
      color: clr("red");
      margin-left: auto;

      &:hover {
        border:solid 1px clr("red");
        background-color: clr("white");
      }

      .icon {
        stroke: clr("white");
      }
    }

    &.action-bookmarked, &.action-unassigned {
      background:none;
      color: clr("purple", "rich");
      font-weight:normal;
      align-items:end;
      border:none;
      line-height: 1rem;

      &:hover, &.active {
        background: $list-item-hover-color;
      }
    }

    &.action-move, &.action-copy {
      @include flex("block", "row", "start", "center");
      padding: 0;

      .btn-left {
        @include flex("block", "row", "start", "center");
        border-right: 1px solid #ffffff;
        padding: 0.3rem 0.5rem;
      }

      .btn-right {
        padding: 0.3rem 0.5rem;

        .icon {
          transition: all $interaction-transition-time;

          &.open {
            transform: rotate(180deg);
          }
        }
      }
    }

    &.action-remove {
      @include flex("block", "row", "start", "center");
      background-color: clr("white");
      border:1px solid #ffa09d;
      color: clr("red");
      padding: 0;

      &:hover {
        border:solid 1px clr("red");
        background-color: clr("white");
      }

      .btn-left {
        @include flex("block", "row", "start", "center");
        border-right: 1px solid #ffa09d;
        padding: 0.3rem 0.5rem;
      }

      .btn-right {
        padding: 0.3rem 0.5rem;

        .icon {
          stroke: clr("white");
          transition: all $interaction-transition-time;

          &.open {
            transform: rotate(180deg);
          }
        }
      }
    }

    .text {
      @include flex("block", "column", "start", "start");
      margin-left: 0.3rem;
    }

    .icon-right {
      margin-left: 0.3rem;
    }
  }
}
</style>
