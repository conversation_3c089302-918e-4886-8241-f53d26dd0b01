<template>
  <section class="comments-actions-move-emotion">
    <section class="header">
      <section class="header-title">
        <i class="fa fa-exchange icon"></i>
        <h2>Move Snippet Emotions</h2>
      </section>
      <section class="header-info">
        Move
        <section class="header-count">{{ headerCount }}</section>
        to a new emotion index.
      </section>
    </section>
    <section class="body">
      <section class="emotions">
        <section class="emotion-green">
          <section v-for="item in emotionGreen"
                   :key="item.name"
                   class="emotion"
                   :class="{
                     active: item === themeIndex,
                     selected: item === localSelectedIndex,
                   }"
                   @click="selectIndex(item)">
            <section class="emotion-active-icon" :class="{ active: item === themeIndex }">
              <i class="fa fa-check-circle icon"></i>
            </section>
            <section class="emotion-label">{{ item.titleCase() }}</section>
          </section>
        </section>
        <section class="emotion-red">
          <section v-for="item in emotionRed"
                   :key="item.name"
                   class="emotion"
                   :class="{
                     active: item === themeIndex,
                     selected: item === localSelectedIndex,
                   }"
                   @click="selectIndex(item)">
            <section class="emotion-active-icon" :class="{ active: item === themeIndex }">
              <i class="fa fa-check-circle icon"></i>
            </section>
            <section class="emotion-label">{{ item.titleCase() }}</section>
          </section>
        </section>
      </section>
    </section>
    <section class="footer">
      <base-button class="cancel" colour="light" size="small" type="link" @click="onCancel">Cancel</base-button>
      <base-button class="confirm" colour="base" size="small" @click="onConfirm" :disabled="hasError">Move Emotion</base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import BaseButton from '@/components/Base/BaseButton';
import Index from '@/enum/index';
import intercomEvent from '@/services/IntercomEvent';
import intercomEvents from '@/enum/intercom-events';
import CommentsActionsReassignUndoToast from '@/components/CommentsActions/CommentsActionsReassignUndoToast';

export default {
  name: 'comments-actions-move-emotion',

  components: {
    BaseButton,
  },

  computed: {
    ...mapState('snippets', ['commentsToReassign']),

    ...mapState('themes', {
      selectedTheme: 'selectedTheme',
      themeIndex: 'index',
    }),

    emotionGreen() {
      return [
        Index.JOY,
        Index.TRUST,
        Index.INTEREST,
        Index.SURPRISE,
      ];
    },

    emotionRed() {
      return [
        Index.SADNESS,
        Index.DISGUST,
        Index.ANGER,
        Index.FEAR,
      ];
    },

    hasError() {
      return !this.localSelectedIndex || this.localSelectedIndex === this.themeIndex;
    },

    headerCount() {
      return this.commentsToReassign.length > 1
        ? `${this.commentsToReassign.length} snippets`
        : `${this.commentsToReassign.length} snippet`;
    },
  },

  data() {
    return {
      Index,
      localSelectedIndex: null,
    };
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('themes', ['setIndexReassign']),

    ...mapActions('toast', ['add']),

    onCancel() {
      this.closeModal();
    },

    async onConfirm() {
      this.setIndexReassign({ index: { id: this.selectedTheme.id, old: this.themeIndex, new: this.localSelectedIndex } });
      this.add({
        toast: { component: CommentsActionsReassignUndoToast, id: 'snippets-reassign-undo' },
      });
      this.closeModal();
      intercomEvent.send(intercomEvents.REASSIGN_COMMENTS_TO_EMOTION);
    },

    selectIndex(item) {
      this.localSelectedIndex = item;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-actions-move-emotion {
  @include modal;
  width: 600px;

  .body {
    .emotions {
      @include flex("block", "column", "start", "center");

      .emotion-green {
        @include flex("block", "row", "center", "center");

        .emotion {
          margin-bottom: 0.5rem;
          margin-right: 0.5rem;

          &:last-child {
            margin-right: 0;
          }

          .emotion-label {
            color: darken($good-color, 10%);
          }
        }
      }

      .emotion-red {
        @include flex("block", "row", "center", "center");

        .emotion {
          margin-right: 0.5rem;

          &:last-child {
            margin-right: 0;
          }

          .emotion-label {
            color: darken($bad-color, 10%);
          }
        }
      }

      .emotion {
        @include flex("block", "column", "start", "start");
        background-color: clr("white");
        border: $border-standard;
        border-radius: $border-radius-medium;
        cursor: pointer;
        height: 4rem;
        width: 8rem;

        &.selected {
          border: 2px solid $border-color-active;
        }

        .emotion-active-icon {
          @include flex("block", "row", "end", "start");
          opacity: 0;
          padding-right: 0.5rem;
          padding-top: 0.2rem;
          width: inherit;

          &.active {
            opacity: 1;
          }
        }

        .emotion-label {
          @include flex("block", "row", "center", "start");
          font-size: $font-size-sm;
          font-weight: $font-weight-medium;
          margin-top: 0.2rem;
          width: inherit;
        }
      }
    }
  }

  .footer {
    .cancel {
      margin-left: -0.6rem;
    }
  }

  .header {
    @include flex("block", "column", "start", "start");

    .header-info {
      @include flex("block", "row", "start", "start");
      font-size: $font-size-xs;

      .header-count {
        font-weight: $font-weight-bold;
        margin: 0 0.2rem;
      }
    }

    .header-title {
      @include flex("block", "row", "start", "center");
      margin-bottom: 1rem;

      .icon {
        font-size: 1.5rem;
        margin-right: 0.5rem;
      }
    }
  }
}
</style>
