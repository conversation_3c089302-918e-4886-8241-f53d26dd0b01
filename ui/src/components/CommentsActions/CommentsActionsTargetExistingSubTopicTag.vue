<template>
  <section class="comments-actions-target-existing-sub-topic-tag"
           :class="{ isExpanded }"
  >
    {{ textNumOfSubTopics }}
    <i class="fa-solid fa-caret-down icon" :class="{ isExpanded }" />
  </section>
</template>

<script>
export default {
  name: 'comments-actions-target-existing-sub-topic-tag',

  props: {
    isExpanded: {
      type: Boolean,
      default: false,
    },
    theme: {
      type: Object,
      required: true,
    },
  },

  computed: {
    textNumOfSubTopics() {
      return this.theme?.numOfSubTopics || 0;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-actions-target-existing-sub-topic-tag {
  @include flex("block", "row", "center", "center");
  @include truncate;
  background: rgba(37, 16, 167, 0.39);
  border: 1px solid rgba(37, 16, 167, 0.54);
  border-radius: 3px;
  color: clr('white');
  cursor: pointer;
  font-size: $font-size-xxs;
  font-weight: $font-weight-bold;
  padding: 3px 8px;
  text-transform: uppercase;
  width: fit-content;

  &:hover, &.isExpanded {
    background-color: rgba(37, 16, 167, 0.61);
    border: 1px solid rgba(37, 16, 167, 0.92);
  }

  .icon {
    color: clr('white');
    font-size: 0.8rem;
    margin-left: 0.2rem;
    transition: opacity $interaction-transition-time;

    &.isExpanded {
      transform: rotate(180deg);
    }
  }
}
</style>
