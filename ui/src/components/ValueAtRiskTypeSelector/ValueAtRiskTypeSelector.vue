<template>
  <section class="value-at-risk-type-selector">
    <value-at-risk-section-header :position="1">
      <span slot="header">Select Calculation Type</span>
    </value-at-risk-section-header>

    <value-at-risk-type-button :active="type === 'CUSTOMER'" :type="'CUSTOMER'" @select="onClick">
      <span class="label" slot="label">Customer Experience (CX)</span>
      <span class="description" slot="description">Cost of Customer Lifetime Value</span>
    </value-at-risk-type-button>

    <value-at-risk-type-button :active="type === 'EMPLOYEE'" :type="'EMPLOYEE'" @select="onClick">
      <span class="label" slot="label">Employee Experience (EX)</span>
      <span class="description" slot="description">Cost of Employee Turnover</span>
    </value-at-risk-type-button>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import ValueAtRiskSectionHeader from '@/components/ValueAtRisk/ValueAtRiskSectionHeader';
import ValueAtRiskTypeButton from '@/components/ValueAtRiskTypeSelector/ValueAtRiskTypeButton';
import ValueAtRiskRequest from '@/services/request/ValueAtRiskRequest';

export default {
  name: 'value-at-risk-type-selector',

  components: {
    ValueAtRiskSectionHeader,
    ValueAtRiskTypeButton,
  },

  data() {
    return {
      type: null,
    };
  },

  computed: {
    ...mapState('valueAtRisk', ['datasetId', 'varInfo']),
  },

  created() {
    this.type = this.varInfo?.valueAtRiskType;
  },

  methods: {
    ...mapActions('valueAtRisk', ['setVarInfo']),

    async onClick(type) {
      this.type = type;

      const infoToUpdate = {
        ...this.varInfo,
        valueAtRiskType: type,
      };

      this.setVarInfo({ info: infoToUpdate });

      await ValueAtRiskRequest.previewVarInfo();
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.value-at-risk-type-selector {
  @include flex("block", "row", "start", "stretch");

  border-bottom: 1px solid $border-color;
  padding: 1.5rem 0;

  .value-at-risk-type-button {
    margin-right: 2rem;
  }
}
</style>
