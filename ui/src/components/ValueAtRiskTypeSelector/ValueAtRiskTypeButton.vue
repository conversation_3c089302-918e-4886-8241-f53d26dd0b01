<template>
  <section class="value-at-risk-type-button" :class="{ active }" @click="onClick">
    <section class="radio">
      <section class="icon-radio-wrapper">
        <i class="fa-solid fa-check icon-check icon-radio" />
      </section>
    </section>

    <section class="info">
      <slot name="label"></slot>
      <slot name="description"></slot>
    </section>
  </section>
</template>

<script>
export default {
  name: 'value-at-risk-type-button',

  props: {
    active: {
      type: Boolean,
      required: true,
    },

    type: {
      type: String,
      required: true,
    },
  },

  methods: {
    onClick() {
      this.$emit('select', this.type);
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.value-at-risk-type-button {
  @include flex("block", "row", "start", "stretch");

  border-radius: $border-radius-medium;
  border: 1px solid $border-color;
  color: $nps-blue;
  cursor: pointer;
  height: 55px;
  transition: all $interaction-transition-time;
  width: 280px;

  &:hover {
    background-color: rgba(108, 96, 200, 0.1);
    border-color: rgba(108, 96, 200, 0.6);
  }

  &.active {
    background-color: #6C60C9;
    color: white;
    border-color: #3F348F;

    .radio .icon-radio-wrapper {
      background-color: white;
      border-color: #3F348F;

      .icon-radio {
        visibility: visible;
      }
    }
  }

  .radio {
    @include flex("block", "row", "center", "center");

    color: #3B7DFF;
    height: 100%;
    width: 2rem;

    .icon-radio-wrapper {
      @include flex("block", "row", "center", "center");

      border-radius: 50%;
      border: 1px solid #D9D9D9;
      height: 1rem;
      min-width: 1rem;
      width: 1rem;

      .icon-radio {
        font-size: 8px;
        visibility: hidden;
      }
    }
  }

  .info {
    @include flex("block", "column", "center", "start");

    height: 100%;

    .label {
      font-size: $font-size-xs;
      font-weight: $font-weight-bold;
      margin-bottom: 0.3rem;
    }

    .description {
      font-size: 11px;
      opacity: 0.7;
    }
  }
}
</style>
