<template>
  <section class="journey-stage-modal-query" :class="{ active }">
    <section class="query-option" @click="setLoad">
      <base-radio radio-size="small" :value="isLoad"/>
      <span class="option-text">Saved Search Query</span>
    </section>

    <journey-stage-modal-query-load :active="active && isLoad" :class="{ active: isLoad }"/>

    <section class="query-option" @click="setNew">
      <base-radio radio-size="small" :value="isNew"/>
      <span class="option-text">New Search Query</span>
    </section>

    <journey-stage-modal-query-new :active="active && isNew" :class="{ active: isNew }"/>

    <section class="options">
      <h3 class="options-title">Query options:</h3>

      <section class="option" @click="onClickDistinct">
        <base-checkbox :value="distinct"/>
        <span class="option-text">Remove Duplicates</span>
      </section>

      <section class="option" @click="onClickExactSearch">
        <base-checkbox :value="exactSearch"/>
        <span class="option-text">Exact Search</span>
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseCheckbox from '@/components/Base/BaseCheckbox';
import BaseRadio from '@/components/Base/BaseRadio';
import JourneyStageModalQueryNew from '@/components/JourneyStageModal/JourneyStageModalQueryNew';
import JourneyStageModalQueryLoad from '@/components/JourneyStageModal/JourneyStageModalQueryLoad';
import JourneyStagePropertyMode from '@/enum/journey-stage-property-mode';

export default {
  name: 'journey-stage-modal-query',

  components: {
    BaseCheckbox,
    BaseRadio,
    JourneyStageModalQueryNew,
    JourneyStageModalQueryLoad,
  },

  props: {
    active: {
      type: Boolean,
      required: true,
    },
  },

  computed: {
    ...mapState('journey', ['queryMode']),

    ...mapState('search', ['distinct', 'exactSearch']),

    isNew() {
      return this.queryMode === JourneyStagePropertyMode.NEW;
    },

    isLoad() {
      return this.queryMode === JourneyStagePropertyMode.LOAD;
    },
  },

  methods: {
    ...mapActions('journey', ['setQueryMode']),

    ...mapActions('search', ['setDistinct', 'setExactSearch']),

    onClickDistinct() {
      this.setDistinct({ distinct: !this.distinct });
    },

    onClickExactSearch() {
      this.setExactSearch({ exactSearch: !this.exactSearch });
    },

    setNew() {
      this.setQueryMode({ mode: JourneyStagePropertyMode.NEW });
    },

    setLoad() {
      this.setQueryMode({ mode: JourneyStagePropertyMode.LOAD });
    },
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.journey-stage-modal-query {
  @include journey-stage-modal;

  overflow-y: hidden;

  .query-option {
    @include flex("block", "row", "start", "center");
    @include rigid;

    background-color: clr('white');
    border: $border-standard;
    border-radius: $border-radius-medium;
    cursor: pointer;
    padding: 0.7rem;
    margin-bottom: 0.5rem;
    transition: all $interaction-transition-time;

    &:hover {
      border: 1px solid clr('blue');
    }

    .option-text {
      margin-left: 0.5rem;
      font-size: $font-size-sm;
      font-weight: $font-weight-bold;
    }
  }

  .journey-stage-modal-query-new,
  .journey-stage-modal-query-load {
    @include stretch;

    height: 0;
    max-height: 0;
    overflow: hidden;
    transition: all $interaction-transition-time;

    &.active {
      height: 100%;
      max-height: 100%;
      overflow-y: auto;
    }
  }

  .options {
    @include flex("block", "row", "start", "center");

    border-top: $border-standard;
    padding: 0.8rem 0 0.4rem;

    .options-title {
      color: $body-copy-light;
      font-size: $font-size-xs;
      letter-spacing: $letter-spacing-base;
      text-transform: uppercase;
    }

    .option {
      @include flex("block", "row", "start", "center");

      cursor: pointer;
      margin-left: 1rem;

      .option-text {
        font-size: $font-size-xs;
        margin-left: 0.4rem;
      }
    }
  }
}
</style>
