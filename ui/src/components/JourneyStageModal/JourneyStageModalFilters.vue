<template>
  <section class="journey-stage-modal-filters" :class="{ active }">
    <section class="query-option" @click="setLoad">
      <base-radio radio-size="small" :value="isLoad"/>
      <span class="option-text">Saved Filter Lists</span>
    </section>
    <journey-stage-modal-filters-load :active="active && isLoad" :class="{ active: isLoad }"/>
    <section class="query-option" @click="setNew">
      <base-radio radio-size="small" :value="isNew"/>
      <span class="option-text">New Filters</span>
    </section>
    <journey-stage-modal-filters-new :active="active && isNew" :class="{ active: isNew }"/>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseRadio from '@/components/Base/BaseRadio';
import JourneyStageModalFiltersNew from '@/components/JourneyStageModal/JourneyStageModalFiltersNew';
import JourneyStageModalFiltersLoad from '@/components/JourneyStageModal/JourneyStageModalFiltersLoad';
import JourneyStagePropertyMode from '@/enum/journey-stage-property-mode';

export default {
  name: 'journey-stage-modal-filters',

  components: {
    BaseRadio,
    JourneyStageModalFiltersLoad,
    JourneyStageModalFiltersNew,
  },

  props: {
    active: {
      type: Boolean,
      required: true,
    },
  },

  computed: {
    ...mapState('journey', ['filterMode']),

    isNew() {
      return this.filterMode === JourneyStagePropertyMode.NEW;
    },

    isLoad() {
      return this.filterMode === JourneyStagePropertyMode.LOAD;
    },
  },

  methods: {
    ...mapActions('journey', ['setFilterMode']),

    setLoad() {
      this.setFilterMode({ mode: JourneyStagePropertyMode.LOAD });
    },

    setNew() {
      this.setFilterMode({ mode: JourneyStagePropertyMode.NEW });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.journey-stage-modal-filters {
  @include journey-stage-modal;

  overflow-y: hidden;

  // TODO: Shared with JourneyStageModalQuery, refactor
  .query-option {
    @include flex("block", "row", "start", "center");
    @include rigid;

    background-color: clr('white');
    border: $border-standard;
    border-radius: $border-radius-medium;
    cursor: pointer;
    padding: 0.7rem;
    margin-bottom: 0.5rem;
    transition: all $interaction-transition-time;
    white-space: nowrap;

    &:hover {
      border: 1px solid clr('blue');
    }

    .option-text {
      margin-left: 0.5rem;
      font-size: $font-size-sm;
      font-weight: $font-weight-bold;
    }
  }

  .journey-stage-modal-filters-new,
  .journey-stage-modal-filters-load {
    @include stretch;

    height: 0;
    max-height: 0;
    overflow: hidden;
    transition: all $interaction-transition-time;
    white-space: nowrap;

    &.active {
      height: 100%;
      max-height: 100%;
      overflow-y: auto;
    }
  }
}
</style>
