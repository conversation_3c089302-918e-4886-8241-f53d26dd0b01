<template>
  <section class="journey-stage-modal-query-load">
    <search-input placeholder="Find a search query" v-model="querySearch"/>

    <modal-list>
      <modal-list-radio-item v-for="search in filtered"
        :key="search.id"
        :value="isSelected(search)"
        @select="onSelect(search)"
      >
        <span class="label">{{ search.label }}</span>
        <span class="raw">{{ search.query }}</span>
      </modal-list-radio-item>
    </modal-list>
  </section>
</template>

<script>
import { SearchIcon } from 'vue-feather-icons';
import { mapActions, mapState } from 'vuex';

import BaseInput from '@/components/Base/BaseInput';
import BaseRadio from '@/components/Base/BaseRadio';
import JourneyStagePropertyMode from '@/enum/journey-stage-property-mode';
import ModalList from '@/components/ModalList/ModalList';
import ModalListRadioItem from '@/components/ModalList/ModalListRadioItem';
import SearchInput from '@/components/SearchInput/SearchInput';

import { searchQueryApi } from '@/services/api';

export default {
  name: 'journey-stage-modal-query-load',

  components: {
    BaseInput,
    BaseRadio,
    ModalList,
    ModalListRadioItem,
    SearchIcon,
    SearchInput,
  },

  props: {
    active: {
      type: Boolean,
      required: true,
    },
  },

  data() {
    return {
      querySearch: '',
    };
  },

  computed: {
    ...mapState('searchQueries', ['searches', 'selected']),

    filtered() {
      if (this.querySearch === '') return this.searches;

      return this.searches.filter(search => {
        return search.label.toLowerCase().indexOf(this.querySearch.toLowerCase()) > -1;
      });
    },
  },

  watch: {
    active() {
      this.preventNavigation();
    },

    selected() {
      this.preventNavigation();
    },
  },

  async created() {
    const searches = await searchQueryApi.fetch();

    this.setQueries({ searches });
    this.preventNavigation();
  },

  methods: {
    ...mapActions('pagedModal', ['setPreventNavigation']),

    ...mapActions('journey', ['setQueryMode']),

    ...mapActions('search', ['setText']),

    ...mapActions('searchQueries', ['select', 'setQueries']),

    isSelected(search) {
      return search.id === this.selected?.id;
    },

    onSelect(search) {
      this.setText({ text: search.query });
      this.select({ id: search.id });
      this.setQueryMode({ mode: JourneyStagePropertyMode.NEW });
    },

    preventNavigation() {
      if (this.selected == null && this.active) {
        this.setPreventNavigation({ prevent: true });
      } else {
        this.setPreventNavigation({ prevent: false });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.journey-stage-modal-query-load {
  @include flex("block", "column", "start", "stretch");
  @include stretch;

  .search-input {
    padding: 0.5rem;
  }

  .modal-list {
    margin-top: 0.5rem;
  }

  .modal-list-radio-item {
    margin-bottom: 0.5rem;

    .label {
      font-weight: $font-weight-medium;
      font-size: $font-size-sm;
    }

    .raw {
      color: $body-copy-light;
      font-size: $font-size-xs;
      margin-top: 0.2rem;
    }
  }
}
</style>
