<template>
  <section class="journey-stage-modal-query-new">
    <p class="text">Enter the search query for your stage below:</p>
    <section class="editor-wrapper">
      <editor :focused="focused"
        :query="localText"
        :tag="tag"
        @input="onEditorInput"
        @tag-inserted="tag = null"
      />
    </section>
    <search-controls-operators/>

    <section v-if="query !== '' && searching" class="loading">
      <loading-blocks-overlay/>
    </section>

    <section v-if="valid && query !== '' && !searching" class="results">
      <check-circle-icon class="icon"/>
      <h3>Search Results:</h3>
      <span>{{ count }} (~{{ searchPercentage }}% of dataset)</span>
    </section>

    <section v-if="snippets && snippets.length > 0" class="preview">
      <section class="preview-header">
        <check-circle-icon class="icon"/>
        <h3 v-if="count > 100">Preview (first 100):</h3>
        <h3 v-else>Preview:</h3>
      </section>
      <section class="preview-content">
        <journey-stage-modal-preview-snippets v-for="snippet in snippets" :key="snippet.id" :snippet="snippet"></journey-stage-modal-preview-snippets>
      </section>
    </section>

    <section v-if="!valid && query !== '' && !searching" class="errors">
      <section class="header">
        <x-circle-icon class="icon"/>
        <h3>There are errors in your query:</h3>
      </section>

      <section class="messages">
        <span v-for="(error, index) in queryErrors" :key="index">
          {{ error }}
        </span>

        <span
          v-if="queryErrors.length === 0"
        >We're not sure what's wrong with your query. Here are some general guidelines for creating search queries - if you're still not sure, please contact us and we'll try to help you out.</span>
        <span v-if="queryErrors.length === 0">Balance any Boolean operators (AND, OR, NOT) by making sure there is text on either side.</span>
        <span v-if="queryErrors.length === 0">Only use one NOT operator per group - use parentheses to create groups.</span>
        <span v-if="queryErrors.length === 0">Balance groups by making sure every opening parenthesis has a closing parenthesis.</span>
      </section>
    </section>
  </section>
</template>

<script>
import { debounce } from 'lodash-es';
import { CheckCircleIcon, XCircleIcon } from 'vue-feather-icons';
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseCheckbox from '@/components/Base/BaseCheckbox';
import bus from '@/helpers/bus';
import Editor from '@/components/Search/Editor/Editor';
import ******************************** from '@/components/JourneyStageModal/********************************';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import SearchControlsOperators from '@/components/Search/Controls/SearchControlsOperators';
import searchValidators from '@/store/utils/search-validators';

import { searchRequest } from '@/services/request';

export default {
  name: 'journey-stage-modal-query-new',

  components: {
    BaseCheckbox,
    CheckCircleIcon,
    Editor,
    ********************************,
    LoadingBlocksOverlay,
    SearchControlsOperators,
    XCircleIcon,
  },

  props: {
    active: {
      type: Boolean,
      required: true,
    },
  },

  data() {
    return {
      focused: false,
      localText: null,
      tag: null,
      searching: false,
    };
  },

  computed: {
    ...mapState('datasets', {
      datasetId: 'active',
    }),

    ...mapGetters('datasets', {
      getDataset: 'get',
      getFromSelected: 'getFromSelected',
    }),

    ...mapState('search', [
      'count',
      'distinct',
      'exactSearch',
      'snippets',
      'text',
      'valid',
    ]),

    ...mapGetters('search', ['query', 'queryErrors']),

    datasetSnippetCount() {
      return this.getFromSelected(this.datasetId).documentCount || this.getDataset(this.datasetId).documentCount;
    },

    isQueryValid() {
      const invalidValidators = searchValidators
        .map(validator => validator(this.query))
        .filter(v => !v.valid);
      return !invalidValidators || invalidValidators.length === 0;
    },

    searchPercentage() {
      if (this.count === 0 || this.count == null) return 0;

      return Math.round(this.count / this.datasetSnippetCount * 100);
    },
  },

  watch: {
    active() {
      this.focused = this.active;
      this.localText = this.text;
      this.select({ id: null });
      this.preventNavigation();
    },

    async distinct() {
      this.searching = true;
      await this.debounceSearch();
    },

    async exactSearch() {
      this.searching = true;
      await this.debounceSearch();
    },

    async query() {
      if (this.isQueryValid) {
        this.preventNavigation();
        this.searching = true;
        await this.debounceSearch();
      }
    },
  },

  async created() {
    bus.$on('select-search-operator', this.onSelectTag);

    this.preventNavigation();

    this.setSnippets({ snippets: [] });
    if (this.text && this.isQueryValid) {
      this.searching = true;
      await this.debounceSearch();
    }
  },

  methods: {
    ...mapActions('pagedModal', ['setPreventNavigation']),

    ...mapActions('search', [
      'setDistinct',
      'setExactSearch',
      'setSnippets',
      'setText',
    ]),

    ...mapActions('searchQueries', ['select']),

    debounceSearch: debounce(async function debounceSearch() {
      try {
        this.setSnippets({ snippets: [] });
        await searchRequest.search({ allSelected: true });
        await searchRequest.count({ allSelected: true });
      } finally {
        this.searching = false;
      }
    }, 500),

    onEditorInput(text) {
      this.setText({ text });
    },

    onSelectTag(tag) {
      this.tag = tag;
    },

    preventNavigation() {
      if (this.text === '' && this.active) {
        this.setPreventNavigation({ prevent: true });
      } else {
        this.setPreventNavigation({ prevent: false });
      }
    },
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.journey-stage-modal-query-new {
  .editor-wrapper {
    @include flex("block", "row", "start", "center");

    background-color: clr('white');
    border: $border-standard;
    border-radius: $border-radius-medium;
    height: 50px;
    padding: 0 0.5rem;
    width: 100%;

    .editor {
      position: relative;
    }
  }

  .search-controls-operators {
    @include flex("block", "row", "start", "center");

    padding: 0.5rem 0;
  }

  .results {
    @include flex("block", "row", "start", "center");
    @include panel;

    font-size: $font-size-sm;
    padding: 1rem;
    margin-top: 0.5rem;

    h3 {
      font-size: $font-size-sm;
      margin: 0 0.5rem;
    }

    .icon {
      color: clr('green');
      height: $font-size-base;
      width: $font-size-base;
    }
  }

  .preview {
    @include flex("block", "column", "start", "stretch");
    @include panel;

    margin: 0.5rem 0;
    padding: 1rem;

    .preview-header {
      @include flex("block", "row", "start", "stretch");
      h3 {
        font-size: $font-size-sm;
        margin: 0 0.5rem;
      }

      .icon {
        color: clr('green');
        height: $font-size-base;
        width: $font-size-base;
      }
    }

    .preview-content {
      @include flex("block", "column", "start", "stretch");
      .journey-stage-modal-preview-snippets {
        white-space: pre-wrap;
      }
    }
  }

  .errors {
    @include flex("block", "column", "start", "stretch");
    @include panel;

    margin-top: 0.5rem;
    padding: 1rem;

    .header {
      @include flex("block", "row", "start", "center");

      margin-bottom: 0.5rem;

      h3 {
        font-size: $font-size-sm;
        margin: 0 0.5rem;
      }

      .icon {
        color: clr('red');
        height: $font-size-base;
        width: $font-size-base;
      }
    }

    .messages {
      @include flex("block", "column", "start", "stretch");
      @include stretch;

      font-size: $font-size-sm;
      white-space: normal;

      span {
        margin-bottom: 0.3rem;
      }
    }
  }
}
</style>
