<template>
  <section class="journey-stage-modal-filters-load">
    <search-input placeholder="Find a filter list..." v-model="filterSearch"/>

    <p class="hint">Select one by clicking the labels, or combine multiple by clicking the checkboxes</p>

    <modal-list>
      <modal-list-checkbox-item v-for="list in filteredLists"
        :key="list.id"
        :value="isSelected(list.id)"
        @select-control="onSelectMulti(list.id)"
        @select-content="onSelectOne(list.id)"
      >
        <span class="label">{{ list.label }}</span>
        <section class="filters">
          <search-filter-item v-for="filter in list.filters" :key="filter.type.name" :filter="filter"/>
        </section>
      </modal-list-checkbox-item>
    </modal-list>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import ModalList from '@/components/ModalList/ModalList';
import ModalListCheckboxItem from '@/components/ModalList/ModalListCheckboxItem';
import SearchFilterItem from '@/components/SearchFilter/SearchFilterItem';
import SearchInput from '@/components/SearchInput/SearchInput';

import { searchFiltersApi } from '@/services/api';

export default {
  name: 'journey-stage-modal-filters-load',

  components: {
    ModalList,
    ModalListCheckboxItem,
    SearchFilterItem,
    SearchInput,
  },

  data() {
    return {
      filterSearch: '',
    };
  },

  computed: {
    ...mapState('searchFilters', ['selected']),

    ...mapGetters('searchFilters', ['convertedLists']),

    filteredLists() {
      return this.convertedLists.filter(item => {
        return item.label.toLowerCase().includes(this.filterSearch.toLowerCase());
      });
    },
  },

  async created() {
    await searchFiltersApi.fetch();
  },

  methods: {
    ...mapActions('searchFilters', [
      'deselect',
      'deselectAll',
      'select',
      'setLists',
    ]),

    isSelected(id) {
      return this.selected.includes(id);
    },

    onSelectMulti(id) {
      if (this.isSelected(id)) this.deselect({ id });
      else this.select({ id });
    },

    onSelectOne(id) {
      this.deselectAll();
      this.select({ id });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.journey-stage-modal-filters-load {
  @include flex("block", "column", "start", "stretch");
  @include stretch;

  .search-input {
    padding: 0.5rem;
  }

  .hint {
    color: $body-copy-light;
    font-size: $font-size-xs;
    font-style: italic;
  }

  .modal-list {
    margin-top: 0.5rem;
  }

  .modal-list-checkbox-item {
    margin-bottom: 0.5rem;

    .label {
      font-weight: $font-weight-medium;
      font-size: $font-size-sm;
    }

    .filters {
      @include flex("block", "row", "start", "stretch", "wrap");

      margin-top: 0.3rem;
    }
  }
}
</style>
