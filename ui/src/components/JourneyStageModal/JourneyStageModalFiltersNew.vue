<template>
  <section class="journey-stage-modal-filters-new">
    <section class="wrapper">
      <filters-edit-modal-list/>
      <filters-edit-modal-new/>
    </section>
  </section>
</template>

<script>
import FiltersEditModalList from '@/components/Search/FiltersEditModal/FiltersEditModalList';
import FiltersEditModalNew from '@/components/Search/FiltersEditModal/FiltersEditModalNew';

export default {
  name: 'journey-stage-modal-filters-new',

  components: {
    FiltersEditModalList,
    FiltersEditModalNew,
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.journey-stage-modal-filters-new {
  @include flex("block", "column", "start", "stretch");
  @include stretch;

  .wrapper {
    @include stretch;

    margin-bottom: 0.5rem;
    padding: 1.5rem 0 0.5rem;
  }
}
</style>
