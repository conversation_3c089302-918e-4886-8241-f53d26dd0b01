<template>
  <section class="journey-stage-modal-review" :class="{ active }">
    <h3 class="section-header">Stage Name</h3>
    <section v-if="label" class="section">{{ label }}</section>
    <section v-else class="section danger">You haven't named your stage.</section>

    <h3 class="section-header">Search Query</h3>
    <section v-if="text" class="editor-wrapper">
      <editor :query="query" :readonly="true"/>
    </section>
    <section v-else class="section danger">No search query set.</section>

    <h3 class="section-header">Search Filters <span class="hint">(optional)</span></h3>
    <section class="section filters" v-if="filterList.length > 0">
      <search-filter-item v-for="filter in filterList" :key="filter.type.name" :filter="filter"/>
    </section>
    <section v-else class="section warning">No filters added.</section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import Editor from '@/components/Search/Editor/Editor';
import SearchFilterItem from '@/components/SearchFilter/SearchFilterItem';
import JourneyStagePropertyMode from '@/enum/journey-stage-property-mode';

export default {
  name: 'journey-stage-modal-review',

  components: {
    Editor,
    SearchFilterItem,
  },

  props: {
    active: {
      type: Boolean,
      required: true,
    },
  },

  data() {
    return {
      query: null,
    };
  },

  computed: {
    ...mapState('journey', [
      'filterMode',
      'newLabel',
      'queryMode',
    ]),

    ...mapState('pagedModal', ['disabled']),

    ...mapState('searchFilters', ['editFilters']),

    ...mapGetters('searchFilters', ['combinedSelected']),

    ...mapState('search', ['text', 'valid']),

    ...mapState('searchQueries', ['selected']),

    filterList() {
      return this.filterMode === JourneyStagePropertyMode.NEW
        ? this.editFilters
        : this.combinedSelected;
    },

    label() {
      return this.newLabel;
    },

    validStage() {
      return this.newLabel !== '' && (
        this.queryMode === JourneyStagePropertyMode.NEW && this.text !== '' && this.valid
        || this.queryMode === JourneyStagePropertyMode.LOAD && this.selected != null
      );
    },
  },

  watch: {
    active() {
      if (this.active) this.query = this.text;
    },

    validStage() {
      if (this.validStage) this.setDisabled({ disabled: false });
      else this.setDisabled({ disabled: true });
    },
  },

  methods: {
    ...mapActions('pagedModal', ['setDisabled']),
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.journey-stage-modal-review {
  @include journey-stage-modal;

  overflow-y: hidden;

  .section-header {
    @include rigid;

    .hint {
      color: $body-copy-light;
      font-size: $font-size-xs;
      font-style: italic;
      font-weight: $font-weight-normal;
    }
  }

  .editor-wrapper {
    @include panel;

    margin: 0.5rem 0 1rem;
    padding: 1rem;

    .editor {
      position: relative;
    }
  }

  .section {
    @include panel;

    padding: 1rem;
    margin: 0.5rem 0 1rem;

    &.danger {
      border: 1px solid clr('red');
      color: clr('red');
    }

    &.warning {
      border: 1px solid clr('yellow');
      color: clr('yellow');
    }

    &.filters {
      padding: 0.8rem 1rem 1rem;
    }
  }
}
</style>
