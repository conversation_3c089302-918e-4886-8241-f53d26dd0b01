<template>
  <section class="journey-stage-modal-preview-snippets">
    <p class="content">{{ snippet.content }}</p>
  </section>
</template>

<script>
export default {
  name: 'journey-stage-modal-preview-snippets',

  props: {
    snippet: {
      type: Object,
      required: true,
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.journey-stage-modal-preview-snippets {
  @include flex("block", "column", "start", "stretch");

  .content {
    border-bottom: $border-standard;
    font-size: $font-size-sm;
    padding: 0.5em 0;
  }
}
</style>
