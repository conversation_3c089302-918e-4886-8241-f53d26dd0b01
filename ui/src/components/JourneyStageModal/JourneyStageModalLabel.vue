<template>
  <section class="journey-stage-modal-label" :class="{ active }">
    <p class="text">Enter a name for your new stage:</p>
    <base-input ref="input"
      v-model="label"
      :focus="true"
      :tabindex="active ? 0 : -1"
      @submit="$emit('next-page')"
    />
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseInput from '@/components/Base/BaseInput';

export default {
  name: 'journey-stage-modal-label',

  props: {
    active: {
      type: Boolean,
      required: true,
    },
  },

  components: {
    BaseInput,
  },

  computed: {
    ...mapState('journey', ['newLabel']),

    label: {
      get() {
        return this.newLabel;
      },
      set(label) {
        this.setNewLabel({ label });
      },
    },
  },

  watch: {
    active() {
      if (this.active && this.$refs.input != null) {
        this.$refs.input.$el.focus();
      }

      this.preventNavigation();
    },

    label() {
      this.preventNavigation();
    },
  },

  created() {
    this.preventNavigation();
  },

  methods: {
    ...mapActions('journey', ['setNewLabel']),

    ...mapActions('pagedModal', ['setPreventNavigation']),

    preventNavigation() {
      if (this.label === '' && this.active) {
        this.setPreventNavigation({ prevent: true });
      } else {
        this.setPreventNavigation({ prevent: false });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.journey-stage-modal-label {
  @include journey-stage-modal;

  overflow-y: hidden;

  .base-input {
    border: $border-standard;
    border-radius: $border-radius-medium;
    background-color: clr('white');
    padding: 0.5rem;
  }
}
</style>
