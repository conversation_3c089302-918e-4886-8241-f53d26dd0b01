<template>
  <section class="emotion-chart-panel-header">
    <section class="emotion-chart-panel-header-left">
      <section class="chart-mode-toggle"
               @click="onClickShowChartMode"
               v-tooltip.top="{
                class: 'tooltip-padding-025',
                content: 'Expand / Collapse the Chart',
                delay: 0,
              }"
      >
        <i class="fa fa-indent" v-if="showChartMaxWidth"></i>
        <i class="fa fa-outdent" v-else></i>
      </section>
      <section class="dataset"
               v-tooltip.top="{
                class: 'tooltip-padding-025',
                content: selectedTitle,
                delay: 0,
              }"
      >
        <span class="title" :class="{ maxWidth: showChartMaxWidth }">
          {{ selectedTitle }}
        </span>
      </section>
    </section>
    <section class="emotion-chart-panel-header-right">
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

export default {
  name: 'emotion-chart-panel-header',

  components: {
  },

  created() {
    this.setShowChartMaxWidth({ value: false });
  },

  computed: {

    ...mapState('datasets', ['active', 'overviews']),

    ...mapState('emotionChart', ['showChartMaxWidth']),

    selectedTitle() {
      const dataset = this.overviews.find(d => d.id === this.active);

      if (dataset != null) return dataset.label;

      return 'No dataset active';
    },
  },

  methods: {
    ...mapActions('emotionChart', ['setShowChartMaxWidth']),

    onClickShowChartMode() {
      this.setShowChartMaxWidth({ value: !this.showChartMaxWidth });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.emotion-chart-panel-header {
  @include flex("block", "row", "between", "stretch");

  padding-bottom: 0.7rem;

  .chart-mode-toggle {
    border: 1px solid $border-color;
    border-radius: $border-radius-small;
    cursor: pointer;
    font-size: $font-size-sm;
    margin-right: 0.5rem;
    padding: 0.3rem;
  }

  .dataset {
    @include flex("block", "row", "start", "center");

    margin-right: 1em;

    .title {
      font-size: $font-size-sm;
      font-weight: $font-weight-bold;
      letter-spacing: $letter-spacing-xs;
      max-width: 300px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &.maxWidth {
        max-width: 500px;
      }
    }
  }

  .emotion-chart-panel-header-left {
    @include flex("block", "row", "center", "center");
  }

  .emotion-chart-panel-header-right {
    @include flex("block", "row", "center", "center");
  }
}
</style>
