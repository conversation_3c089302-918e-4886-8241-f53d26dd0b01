<template>
  <section class="emotion-chart-panel">
    <emotion-chart-panel-header />

    <tabbed-panel :tabs="tabs" @selected="onSelectTab">
      <component v-if="summaries.length > 0" :is="chartComponent"></component>
    </tabbed-panel>

    <section class="themes" v-show="showThemesNavigator || showThemesPlaceholder || showNoThemes">
      <emotion-analysis-themes-navigator v-show="showThemesNavigator" :tab="selectedTab" />
      <emotion-analysis-themes-placeholder v-show="showThemesPlaceholder" :chart="selectedTab.titleCase()" />
      <emotion-analysis-no-themes v-show="showNoThemes" />
    </section>
  </section>
</template>

<script>
import { without } from 'lodash-es';
import { mapActions, mapGetters, mapState } from 'vuex';

import DatasetRefreshNotification from '@/components/DatasetRefreshNotification';
import Distribution from '@/components/DistributionChart/Distribution';
import EmotionAnalysisNoThemes from '@/components/EmotionAnalysis/EmotionAnalysisNoThemes';
import EmotionAnalysisOverLimit from '@/components/EmotionAnalysis/EmotionAnalysisOverLimit';
import EmotionAnalysisScoreSection from '@/components/EmotionAnalysis/EmotionAnalysisScoreSection';
import EmotionAnalysisThemesNavigator from '@/components/EmotionAnalysis/EmotionAnalysisThemesNavigator';
import EmotionAnalysisThemesPlaceholder from '@/components/EmotionAnalysis/EmotionAnalysisThemesPlaceholder';
import EmotionAnalysisTimeRange from '@/components/EmotionAnalysis/EmotionAnalysisTimeRange';
import EmotionChartPanelHeader from '@/components/EmotionChartPanel/EmotionChartPanelHeader';
import Indexes from '@/components/IndexesChart/Indexes';
import Intensity from '@/components/IntensityChart/Intensity';
import NetworkKeys from '@/enum/network-keys';
import NetworkStatus from '@/enum/network-status';
import ResultsTabs from '@/enum/results-tabs';
import TabbedPanel from '@/components/TabbedPanel/TabbedPanel';
import ThemesType from '@/enum/themes-type';
import Timeline from '@/components/TimelineChart/Timeline';

import { themesRequest } from '@/services/request';

export default {
  name: 'emotion-chart-panel',

  components: {
    Distribution,
    EmotionAnalysisNoThemes,
    EmotionAnalysisOverLimit,
    DatasetRefreshNotification,
    EmotionAnalysisScoreSection,
    EmotionAnalysisThemesNavigator,
    EmotionAnalysisThemesPlaceholder,
    EmotionAnalysisTimeRange,
    EmotionChartPanelHeader,
    Indexes,
    Intensity,
    TabbedPanel,
    Timeline,
  },

  data() {
    return {
      areThemesLoaded: false,
      ResultsTabs,
    };
  },

  computed: {
    ...mapGetters('datasets', ['strongestIndex']),

    ...mapGetters('network', ['isLoading', 'status']),

    ...mapState('datasets', [
      'active',
      'selected',
      'datasets',
      'summaries',
    ]),

    ...mapState('themes', [
      'index',
      'range',
      'selectedTab',
      'themes',
      'type',
    ]),

    ...mapState('timeSeries', ['time']),

    ...mapState('user', ['user']),

    areThemesLoading() {
      if (this.selectedTab === ResultsTabs.TREND_ANALYSIS) {
        return (!this.areThemesLoaded && this.isLoading(NetworkKeys.TIME_SERIES_TOPICS));
      }

      return !this.areThemesLoaded && this.isLoading(NetworkKeys.THEMES);
    },

    chartComponent() {
      if (this.selectedTab === ResultsTabs.TREND_ANALYSIS) return 'Timeline';

      return this.selectedTab.titleCase();
    },

    hasSelected() {
      return this.selected.length > 0;
    },

    hasThemes() {
      return this.themes.length > 0;
    },

    isTimeSeries() {
      return this.selectedTab === ResultsTabs.TREND_ANALYSIS;
    },

    showNoThemes() {
      return this.areThemesLoaded && !this.hasThemes && !this.areThemesLoading && !this.isTimeSeries;
    },

    showThemes() {
      // topic overview only - index
      if (this.hasThemes && this.type === ThemesType.OVERVIEW && this.index != null) return true;
      // topic overview only - intensity
      if (this.hasThemes && this.type === ThemesType.OVERVIEW && this.range != null) return true;
      if (this.type === ThemesType.TIME_SERIES) return true;

      return false;
    },

    showThemesNavigator() {
      return this.showTimeRange;
    },

    showThemesPlaceholder() {
      return !this.areThemesLoaded && !this.showThemes && !this.areThemesLoading;
    },

    showTimeRange() {
      return this.isTimeSeries
          && this.time.from != null
          && this.time.to != null;
    },

    strongestIndexValue() {
      return this.strongestIndex(this.active);
    },

    tabs() {
      let tabs = [...ResultsTabs.enumValues];

      if (this.user.productFeatures != null) {
        if (!this.user.productFeatures.includes('intensity')) {
          tabs = without(tabs, ResultsTabs.INTENSITY);
        }
      }

      return tabs;
    },

    themesStatus() {
      if (this.themes.length > 0) return NetworkStatus.SUCCESS;

      if (this.selectedTab === ResultsTabs.TREND_ANALYSIS) return this.status(NetworkKeys.TIME_SERIES_TOPICS);

      return this.status(NetworkKeys.THEMES);
    },
  },

  watch: {
    selectedTab() {
      if (this.selectedTab === ResultsTabs.TREND_ANALYSIS) {
        this.resetSnippetsFilters();
        this.setFiltersDisabled({ value: true });
        this.setFilterShow({ value: false });
      } else {
        this.setFiltersDisabled({ value: false });
      }
    },

    async strongestIndexValue() {
      this.setIndex({ index: this.strongestIndexValue });
      await this.fetchThemes();
    },

    themes() {
      if (this.hasThemes) this.areThemesLoaded = true;
    },
  },

  created() {
    this.setSelectedTab({ value: ResultsTabs.INDEXES });
  },

  methods: {
    ...mapActions('snippets', [
      'resetSnippetsFilters',
      'setFiltersDisabled',
      'setFilterShow',
    ]),

    ...mapActions('themes', [
      'setIndex',
      'setRange',
      'setSelectedTab',
      'setThemes',
      'setType',
    ]),

    ...mapActions('themes', { resetThemes: 'reset' }),

    async fetchThemes() {
      const themes = await themesRequest.fetchThemes();

      this.setThemes({ themes });
    },

    onSelectTab(tab) {
      this.areThemesLoaded = false;

      if (this.selectedTab === ResultsTabs.TREND_ANALYSIS && (tab === ResultsTabs.INDEXES || tab === ResultsTabs.DISTRIBUTION)) {
        this.setThemes({ themes: [] });
        this.setRange({ range: null });
        this.setType({ type: ThemesType.OVERVIEW });
        this.setIndex({ index: this.strongestIndexValue });
        this.fetchThemes();
      }

      this.setSelectedTab({ value: tab });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.emotion-chart-panel {
  @include flex("block", "column", "start", "stretch");
  @include stretch;
  @include rigid;
  grid-area: emotion-chart-panel;

  animation: $load-up;
  flex-shrink: 0;
  padding: 1rem;

  .themes {
    @include flex("block", "column", "start", "stretch");
    @include stretch;

    min-height: 0;
  }
}
</style>
