<template>
  <section class="themes-list-item" :class="{
      active,
      isExpanded,
      'show-renaming': showRenaming,
      'show-removing': showRemoving
  }">
    <section class="theme-and-sub-list-wrapper" :class="{ selected }">
      <section
        class="theme-item"
        :class="{
          active,
          'controls-disabled': disableControls,
          'show-renaming': showRenaming,
          'show-removing': showRemoving,
          renaming,
          removing,
          isExpanded,
        }"
        @mouseenter="onMouseEnter"
        @mouseleave="onMouseLeave"
      >

        <section class="theme-and-sub-list-wrapper-reassigning" v-if="reassigning">
          <barberpole-overlay colour="red"/>
        </section>

        <section class="inner normal" :class="{
          'all-theme-no-subtopics': !disableControls && !themesWithSubtopics.length,
          'controls-disabled': disableControls,
        }">
          <themes-list-item-interaction
            v-if="!disableControls"
            :hover="hover"
            :selected="selected"
            :theme="theme"
          />

          <section class="item-body" :class="{ active, showSwotScore }" @click="onClick(false)">
            <!-- item body - left -->
            <themes-list-item-label @remove="showRemoving = true" @rename="showRenaming = true"
              :active="active"
              :disable-controls="disableControls"
              :hover="hover"
              :is-swot="isSwot"
              :theme="theme"
            />

            <!-- item body - right -->
            <section class="sub-topic-tag">
              <themes-list-item-sub-topic-tag v-if="hasSubtopics && !showSwotScore" :theme="theme" />
              <span class="none-icon to-keep-themes-align" v-else />
            </section>

            <!-- show a/score -->
            <section v-show="!showSwotScore" class="info">
              <section class="volume" :class="{sorted: isSortedByVol}">
                <strong>Vol</strong>&nbsp;
                {{ theme.numOfDocuments }} ({{ volumePercent }})
              </section>

              <section class="adorescore" :class="{ sorted: isSortedByScore }" v-tooltip.bottom="{
                  class: 'tooltip-base-drag',
                  delay: 0,
                  content: `Adorescore`,
                }">
                <adorescore-box-mini :bucket="bucket" :score="adorescore"/>
              </section>
            </section>

            <!-- show swot score -->
            <section v-show="showSwotScore" class="info">
              <swot-presentation-quadrant-badge class="swot-label" :swot="Swot[swotType]" min-width="7rem" />
              <section class="swot-score" :style="{ color: swotColor }">{{ swotScore }}</section>
            </section>
          </section>
        </section>

        <themes-list-item-renaming v-if="showRenaming" :theme="theme" @cancel="cancelRename" @confirm="confirmRename" />
        <themes-list-item-removing v-if="showRemoving" :theme="theme" @cancel="cancelRemove" @confirm="confirmRemove" />
      </section>

      <transition name="expand">
        <theme-subtopic-list
          v-show="isExpanded"
          :active="active"
          :disable-controls="disableControls"
          :theme="theme"
          @onClickSubtopic="onClick"
        />
      </transition>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import AdorescoreBoxMini from '@/components/AdorescoreBox/AdorescoreBoxMini';
import BarberpoleOverlay from '@/components/BarberpoleOverlay';
import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import BaseTag from '@/components/Base/BaseTag';
import DragDropHelper from '@/helpers/drag-drop-helper';
import PreventSampleAction from '@/components/Mixins/PreventSampleAction';
import ResultsTabs from '@/enum/results-tabs';
import Route from '@/enum/route';
import router from '@/router';
import Swot from '@/enum/swot';
import SwotPresentationQuadrantBadge from '@/components/SwotPresentation/SwotPresentationQuadrantBadge';
import ThemesConfirmMerge from '@/components/ThemesActions/ThemesConfirmMerge';
import ThemesListItemInteraction from '@/components/ThemesListItem/ThemesListItemInteraction';
import ThemesListItemLabel from '@/components/ThemesListItem/ThemesListItemLabel';
import ThemesListItemRemoving from '@/components/ThemesListItem/ThemesListItemRemoving';
import ThemesListItemRenaming from '@/components/ThemesListItem/ThemesListItemRenaming';
import ThemesListItemSubTopicTag from '@/components/ThemesListItem/ThemesListItemSubTopicTag';
import ThemesSort from '@/enum/themes-sort';
import ThemeSubtopicList from '@/components/ThemesListItem/ThemeSubtopicList';

import { Draggable } from '@shopify/draggable';
import { snippetApi, timeSeriesApi } from '@/services/api';
import { metadataRequest, snippetsRequest, themesRequest } from '@/services/request';

const dragDropHelper = new DragDropHelper();

export default {
  name: 'themes-list-item',

  mixins: [PreventSampleAction],

  components: {
    AdorescoreBoxMini,
    BarberpoleOverlay,
    BaseButton,
    BaseInput,
    BaseTag,
    SwotPresentationQuadrantBadge,
    ThemesListItemInteraction,
    ThemesListItemLabel,
    ThemesListItemRemoving,
    ThemesListItemRenaming,
    ThemesListItemSubTopicTag,
    ThemeSubtopicList,
  },

  props: {
    active: {
      type: Boolean,
      required: true,
    },

    disableControls: {
      type: Boolean,
      default: false,
    },

    isSwot: {
      type: Boolean,
      default: false,
    },

    reassigning: {
      type: Boolean,
      default: false,
    },

    renaming: {
      type: Boolean,
      default: false,
    },

    removing: {
      type: Boolean,
      default: false,
    },

    selected: {
      type: Boolean,
      required: true,
    },

    theme: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      dragHandleClass: 'theme-subtopic-item',
      dragItemClass: 'theme-subtopic-item',
      hover: false,
      showRenaming: false,
      showRemoving: false,
      Swot,
    };
  },

  computed: {
    ...mapState('benchmark', ['buckets']),

    ...mapGetters('benchmark', ['classifyAdorescore']),

    ...mapGetters('datasets', ['get', 'viewingDatasetId']),

    ...mapState('datasets', { datasetId: 'active' }),

    ...mapGetters('snippetsFilter', ['getActiveChildDatasetId', 'isViewingChildDataset']),

    ...mapState('themes', [
      'expandedThemes',
      'hoverTheme',
      'hoverTopic',
      'selectedSubtopic',
      'selectedTab',
      'selectedThemes',
      'sort',
      'swotColorMap',
      'swotFilterShow',
    ]),

    ...mapGetters('themes', [
      'areSubtopicsLoaded',
      'getSubtopics',
      'themesWithSubtopics',
    ]),

    ...mapGetters('timeSeries', ['emotionIndex', 'metadataIndex']),

    adorescore() {
      return Math.round(this.theme.polarity * 100);
    },

    areTimelineThemes() {
      return this.$route.name === Route.EMOTION_ANALYSIS && this.selectedTab === ResultsTabs.TREND_ANALYSIS;
    },

    bucket() {
      return this.classifyAdorescore(this.adorescore);
    },

    hasSubtopics() {
      return this.theme?.numOfSubTopics > 0;
    },

    isExpanded() {
      return this.expandedThemes.includes(this.theme.id);
    },

    isSample() {
      return this.get(this.datasetId).localSample;
    },

    isSortedByScore() {
      return this.sort?.name === ThemesSort.ADORESCORE.name;
    },

    isSortedByVol() {
      return this.sort?.name === ThemesSort.VOLUME.name;
    },

    showSwotScore() {
      return this.$route.name === Route.SWOT_ANALYSIS && this.swotFilterShow;
    },

    subtopicList() {
      return this.getSubtopics(this.theme.id);
    },

    swotColor() {
      return this.swotColorMap[this.swotType];
    },

    swotScore() {
      return Math.round(this.theme.swot.intensity * 100);
    },

    swotType() {
      if (!this.isSwot) return 'NONE';

      return this.theme.swot.attribute;
    },

    volumePercent() {
      const value = Math.round(this.theme.numOfDocuments / this.get(this.viewingDatasetId).documentCount * 100);
      return value === 0 ? '<1%' : `${value}%`;
    },
  },

  watch: {
    async isExpanded() {
      if (this.isExpanded && !this.areSubtopicsLoaded(this.theme.id)) {
        await this.getTopics();
      }
    },
  },

  async mounted() {
    if (this.active && router.currentRoute.name !== Route.COMMENTS) {
      await this.fetchData();
    }

    // configuration for drag & drop
    let dragId;

    new Draggable(this.$el, {
      delay: {
        mouse: 0,
        drag: 100,
        touch: 100,
      },
      distance: 18,
      draggable: `.${this.dragItemClass}`,
      handle: `.${this.dragHandleClass}`,
      mirror: {
        cursorOffsetX: 10,
        cursorOffsetY: 15,
      },
    }).on('drag:start', (evt) => {
      const currentTarget = evt.originalEvent.target;

      if (dragDropHelper.isPreventedDrag(currentTarget, [
        'checkbox',
        'icons',
        'renaming',
        'removing',
      ])) evt.cancel();

      dragId = evt.originalSource.id.replace('theme-subtopic-item-', '');
      evt.source.childNodes[0].style.backgroundColor = '#dee1e4';
    }).on('drag:stop', () => {
      setTimeout(() => {
        if (this.hoverTopic || this.hoverTheme) {
          const hoverItem = this.hoverTopic ? this.hoverTopic : this.hoverTheme;

          if (this.selectedThemes.length === 0 && Number(dragId) === hoverItem.id) return;
          if (this.selectedThemes.length === 1 && Number(dragId) === hoverItem.id && Number(dragId) === this.selectedThemes[0]) return;

          this.addMergingListOnDrag({ ids: [Number(dragId), hoverItem.id] });
          this.selectThemes({ ids: [Number(dragId), hoverItem.id] });
          this.setMergeLabel({ label: hoverItem.topicLabel });
          this.setModalComponent({ component: ThemesConfirmMerge });
        }
      }, 50);
    }).on('mirror:created', (evt) => {
      evt.mirror.style.backgroundColor = '#4299F7';
      evt.mirror.style.borderRadius = '3px';
      evt.mirror.style.color = '#FFF';
      evt.mirror.style.fontSize = '12px';
      evt.mirror.style.height = `${evt.source.clientHeight}px`;
      evt.mirror.style.padding = '6px 0 0 20px';
      evt.mirror.style.width = `${evt.source.clientWidth}px`;
      evt.mirror.style.zIndex = 2;

      let movingCount = this.selectedThemes?.length || 0;
      movingCount = this.selectedThemes?.includes(Number(dragId))
        ? Number(movingCount - 1)
        : movingCount;
      movingCount = movingCount > 0
        ? ` and ${movingCount} other ${movingCount > 1 ? 'items.' : 'item.'}`
        : '.';
      const movingSubtopic = this.subtopicList.find(i => i.id === Number(dragId)) || {};
      let movingLabel = movingSubtopic.topicLabel || 'One Sub-Topic';
      movingLabel = movingLabel.length > 20
        ? `"${movingLabel.substring(0, 20)}..."`
        : `"${movingLabel}"`;
      evt.mirror.textContent = `Merging ${movingLabel}${movingCount}`;
    });
  },

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    ...mapActions('snippets', [
      'deselectAllSnippets',
      'resetSnippets',
      'setSnippets',
    ]),

    ...mapActions('themes', [
      'addMergingListOnDrag',
      'addSubtopics',
      'deselectThemes',
      'selectLabel',
      'selectSubtopic',
      'selectTheme',
      'selectThemes',
      'setFetchParents',
      'setHoverTheme',
      'setMergeLabel',
    ]),

    cancelRemove() {
      this.showRemoving = false;
    },

    cancelRename() {
      this.showRenaming = false;
    },

    confirmRemove(saveActionRemove) {
      if (this.active) {
        this.selectTheme({ theme: null });
        this.selectLabel({ value: null });

        this.fetchData();
      }

      this.$emit('remove', this.theme.id, saveActionRemove);

      this.showRemoving = false;
    },

    confirmRename(name, saveActionRename) {
      this.$emit('rename', this.theme.id, name, saveActionRename);
      this.showRenaming = false;
    },

    async getBookmarks() {
      const datasetIds = [this.datasetId];
      if (this.isViewingChildDataset) {
        datasetIds.push(this.getActiveChildDatasetId);
      }
      await snippetsRequest.fetchBookmarks(datasetIds);
    },

    async fetchData() {
      this.resetSnippets();

      await Promise.all([
        this.getSnippets(),
        this.getBookmarks(),
        this.getTopics(),
      ]);
    },

    async getSnippets() {
      if (!this.areTimelineThemes) {
        await metadataRequest.filterCommentsOnMetadata();
        await metadataRequest.filterCommentsCountOnMetadata();
      } else {
        const meta = await timeSeriesApi.getSnippets(
          this.datasetId,
          this.theme.ids,
          {
            metadataIndex: this.metadataIndex,
          },
        );
        this.setSnippets({ meta, page: 0 });
        if (meta.snippets?.length) {
          await snippetApi.retrieveTopicIdsBySnippet(
            this.viewingDatasetId,
            meta.snippets.map(s => s.contentId),
          );
        }
      }
    },

    async getTopics() {
      if (!this.areTimelineThemes) {
        this.setFetchParents({ parents: [this.theme] });
        const response = await themesRequest.fetchSubtopics([this.theme], null);
        const topics = response == null ? [] : response;

        this.addSubtopics({ parentIds: [this.theme.id], subtopics: topics });
      }
    },

    async onClick(clickedSubtopic) {
      // rename or removing parent theme
      if ((this.showRenaming || this.showRemoving) && !clickedSubtopic) {
        return;
      }

      if (clickedSubtopic) {
        if (this.active) {
          return;
        }
        this.selectTheme({ theme: this.theme });
        this.selectLabel({ value: this.theme.topicLabel });
      } else {
        this.deselectAllSnippets();
        this.selectSubtopic({ topic: null });

        if (!this.active || this.areTimelineThemes) {
          this.selectTheme({ theme: this.theme });
          this.selectLabel({ value: this.theme.topicLabel });
        } else {
          this.selectTheme({ theme: null });
          this.selectLabel({ value: null });
        }
        await this.fetchData();
      }
    },

    onMouseEnter() {
      this.hover = true;
      this.setHoverTheme({ hoverTheme: this.theme });
    },

    onMouseLeave() {
      this.hover = false;
      this.setHoverTheme({ hoverTheme: null });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

@keyframes removing {
  0% {
    background-color: clr("white");
  }
  50% {
    background-color: lighten(clr("red"), 30%);
  }
  100% {
    background-color: clr("white");
  }
}

@keyframes renaming {
  0% {
    background-color: clr("white");
  }
  50% {
    background-color: lighten(clr("purple"), 20%);
  }
  100% {
    background-color: clr("white");
  }
}

.themes-list-item {
  background: #FCFCFC;
  border: none;

  &.active {
    .theme-and-sub-list-wrapper {
      background: clr("white");
      border: solid 2px $theme-item-bdr-active;
      margin: 1px;

      &.selected {
        border: solid 2px $theme-item-bdr-active;
      }
    }
  }

  &:hover:not(.active) {
    .theme-and-sub-list-wrapper {
      background: clr("white");
      border: solid 1px $theme-item-bdr-active;
      margin: 1px;
    }
  }

  &.isExpanded:not(.active) {
    .theme-and-sub-list-wrapper {
      border: solid 1px $theme-item-bdr-active;
      margin: 1px;
    }
  }

  &.show-renaming:not(.isExpanded), &.show-removing:not(.isExpanded) {
    .theme-and-sub-list-wrapper {
      border: solid 1px $theme-item-bdr-active;
      margin: 0;
    }
  }

  .theme-and-sub-list-wrapper {
    border: solid 1px $theme-item-bdr;
    border-radius: $border-radius-medium;
    margin: 1px;
    width: inherit;

    &.selected {
      border: 1px solid rgb(37 16 167 / 31%);
    }

    .expand-enter-active,
    .expand-leave-active {
      transition: opacity $interaction-transition-time;
    }

    .expand-enter,
    .expand-leave-to {
      opacity: 0;
    }

    .theme-item {
      @include rigid;

      border-radius: 4px;
      position: relative;

      .theme-and-sub-list-wrapper-reassigning {
        border-radius: 4px;
        height: 100%;
        overflow: hidden;
        pointer-events: none;
        position: absolute;
        width: 100%;
      }

      &.show-renaming, &.show-removing {
        background-color: clr('blue', 'lighter');
        .inner.normal {
          display: none;
        }
      }

      &.removing {
        animation: removing 1.5s infinite;
        color: clr("red");
      }

      &.renaming {
        animation: renaming 1.5s infinite;
        color: clr("purple");
      }

      .inner {
        align-items: stretch;
        color: $body-copy;
        display: grid;
        grid-template-columns: 48px auto;
        grid-template-rows: $themes-item-height;
        justify-items: stretch;
        max-width: 100%;

        &.all-theme-no-subtopics {
          grid-template-columns: 45px auto;
        }

        &.controls-disabled {
          grid-template-columns: auto;
          padding-left: 1rem;;
        }

        .item-body {
          cursor: pointer;
          display: grid;
          grid-template-columns: 1fr 4rem 11rem;
          grid-template-rows: $themes-item-height;
          justify-items: stretch;

          &.active {
            font-weight: $font-weight-bold;
            color: #2B1093;

            .volume {
              &.sorted {
                filter: brightness(0.7);
              }
            }
          }

          &.showSwotScore {
            grid-template-columns: 1fr 10% 42%;

            .score {
              @include flex("block", "row", "space-between", "center");
            }
          }

          .info {
            @include flex("block", "row", "start", "center");

            font-size: 0.7rem;
            user-select: none;
            white-space: nowrap;

            .adorescore {
              @include flex("block", "row", "start", "center");
              justify-self: start;
              width: 30%;

              &.sorted {
                color: $theme-item-text-sorted;
              }
            }

            .volume {
              @include flex("block", "row", "start", "center");
              padding-left: 0.5rem;
              padding-right: 0.3rem;
              width: 70%;

              &.sorted {
                color: $theme-item-text-sorted;
              }
            }

            .swot-score {
              padding-left: 1.2rem;
            }
          }

          .sub-topic-tag {
            align-self: center;
            justify-self: right;
            user-select: none;
            white-space: nowrap;
          }
        }
      }
    }
  }
}
</style>
