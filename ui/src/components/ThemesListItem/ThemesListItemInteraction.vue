<template>
  <section class="themes-list-item-interaction">
    <themes-item-drag v-if=hover @click.stop/>

    <section class="checkbox" @click.stop>
      <section class="connector"></section>
      <base-checkbox-solid :value="selected" @input="onSelect" />
      <section class="connector" :class="{ show: hasSubtopics && isExpanded }"></section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseCheckboxSolid from '@/components/Base/BaseCheckboxSolid';
import ThemesItemDrag from '@/components/ThemesListItem/ThemesItemDrag';

export default {
  name: 'themes-list-item-interaction',

  components: {
    BaseCheckboxSolid,
    ThemesItemDrag,
  },

  props: {
    hover: {
      type: Boolean,
      default: false,
    },

    selected: {
      type: Boolean,
      required: true,
    },

    theme: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
    };
  },

  computed: {
    ...mapState('themes', ['expandedThemes']),

    ...mapGetters('themes', ['isCustomTheme']),

    hasSubtopics() {
      return this.theme?.numOfSubTopics > 0;
    },

    isExpanded() {
      return this.expandedThemes.includes(this.theme.id);
    },
  },

  methods: {
    ...mapActions('themes', ['addCustomThemes', 'removeCustomThemes']),

    onSelect() {
      this.$parent.$emit('select', this.theme.id, this.selected);

      if (this.isCustomTheme(this.theme)) {
        this.removeCustomThemes({ themes: [this.theme] });
      } else {
        this.addCustomThemes({ themes: [this.theme] });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-list-item-interaction {
  @include flex("block", "row", "end", "stretch");

  .checkbox {
    @include flex("block", "column", "start", "center");
    @include rigid;

    cursor: pointer;
    margin-right: 0.6rem;

    .base-checkbox-solid .input-checkbox {
      border: $border-light solid $checkbox-bdr-purple;
    }

    .connector {
      @include stretch;

      &.show {
        border-left: 1px solid $connector;
      }
    }
  }

  .themes-item-drag {
    @include flex("block", "row", "end", "center");
    cursor: grab;
    margin-right: 0.2rem;
  }
}
</style>
