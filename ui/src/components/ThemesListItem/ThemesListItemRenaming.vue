<template>
  <section class="themes-list-item-renaming">
    <section class="renaming-input">
      <i class="fa-regular fa-pen icon edit"/>

      <b>Edit</b>

      <section class="input">
        <base-input v-model="localRename" :focus="true" @submit="onConfirm"/>
      </section>

      <base-tag @click="onConfirm"
        :background-colour="'#00BD1E'"
        :border-radius="'2rem'"
        :colour-hover="'#00A51A'"
        :colour="'#00A51A'"
        :padding="'0.3rem 0.5rem 0.3rem 0.5rem'"
        :text-colour="'white'"
        :text="'Confirm'"
      />

      <i class="fa-solid fa-x icon x-icon" @click="onCancel"/>
    </section>

    <themes-list-item-saved-action-in-place :save-action="saveActionRename" @toggle="onToggleSaveAction"/>
  </section>
</template>

<script>
import BaseInput from '@/components/Base/BaseInput';
import BaseTag from '@/components/Base/BaseTag';
import ThemesListItemSavedActionInPlace from '@/components/ThemesList/ThemesListItemSavedActionInPlace';

export default {
  name: 'themes-list-item-renaming',

  components: {
    BaseInput,
    BaseTag,
    ThemesListItemSavedActionInPlace,
  },

  props: {
    theme: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      localRename: '',
      saveActionRename: false,
    };
  },

  created() {
    this.localRename = this.theme.topicLabel;
  },

  methods: {
    onCancel() {
      this.$emit('cancel');
    },

    onConfirm() {
      if (this.localRename.trim() === this.theme.topicLabel.trim()) {
        this.$emit('cancel');
      } else {
        this.$emit('confirm', this.localRename, this.saveActionRename);
      }
    },

    onToggleSaveAction() {
      this.saveActionRename = !this.saveActionRename;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-list-item-renaming {
  align-items: stretch;
  background: rgba(255, 255, 255, 0.35);
  border-radius: 4px;
  color: $body-copy;
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: $themes-item-height + 10 $themes-item-height;
  justify-items: stretch;
  max-width: 100%;

  .renaming-input {
    display: grid;
    grid-template-columns: 1.5rem 2.5rem 1fr 5.5rem min-content;
    padding-left: 0.3rem;
    padding-right: 0.3rem;
    margin-left: 0.3rem;
    margin-right: 0.3rem;
    border-bottom: 1px solid rgba(100, 90, 160, 0.05);

    b {
      font-size: $font-size-sm;
      align-self: center;
      justify-self: center;
    }

    .input {
      display: grid;
      margin-right: 1.5rem;
      overflow-x: hidden;
      padding-left: 0.3rem;
      place-items: center stretch;

      .base-input {
        background-color: clr('white');
        border-radius: $border-radius-medium;
        border: $border-standard;
        font-size: $font-size-sm;
        padding: 0.3rem;
      }
    }

    .icon {
      align-self: center;
      justify-self: center;
      transition: opacity $interaction-transition-time;

      &:hover {
        opacity: 0.6;
      }

      &.edit {
        &:hover {
          opacity: 1;
        }
      }
    }

    .x-icon {
      cursor: pointer;
    }
  }
}
</style>
