<template>
  <section class="theme-subtopic-item-interaction">
    <themes-item-drag v-if=hover @click.stop />

    <section class="icon-list" @click.stop>
      <section class="connector top"></section>
      <base-checkbox-solid :value="isCheckMarked" @input="onSelect"/>
      <section class="connector bottom"></section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseCheckboxSolid from '@/components/Base/BaseCheckboxSolid';
import ThemesItemDrag from '@/components/ThemesListItem/ThemesItemDrag';

export default {
  name: 'theme-subtopic-item-interaction',

  components: {
    BaseCheckboxSolid,
    ThemesItemDrag,
  },

  props: {
    hover: {
      type: Boolean,
      default: false,
    },

    topic: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapState('themes', ['customThemes', 'selectedThemes']),

    isCheckMarked() {
      return this.selectedThemes.includes(this.topic.id);
    },
  },

  methods: {
    ...mapActions('themes', [
      'addCustomThemes',
      'deselectThemes',
      'removeCustomThemes',
      'selectThemes',
    ]),

    isCustom(theme) {
      return this.customThemes.find(customTheme => customTheme.id === theme.id) != null;
    },

    onSelect() {
      if (this.isCheckMarked) {
        this.deselectThemes({ ids: [this.topic.id] });
      } else {
        this.selectThemes({ ids: [this.topic.id] });
      }

      if (this.isCustom(this.topic)) {
        this.removeCustomThemes({ themes: [this.topic] });
      } else {
        this.addCustomThemes({ themes: [this.topic] });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.theme-subtopic-item-interaction {
  @include flex("block", "row", "end", "stretch");
  min-width: 48px;

  .themes-item-drag {
    @include flex("block", "row", "end", "center");

    cursor: grab;
    margin-right: 0.2rem;
  }

  .icon-list {
    @include flex("block", "column", "center", "center");
    @include rigid;

    font-size: $font-size-xs;
    margin-right: 0.6rem;

    .base-checkbox-solid .input-checkbox {
      border: $border-light solid $checkbox-bdr-purple;
    }

    .connector {
      @include stretch;
      border-left: 1px solid $connector;
    }
  }
}
</style>
