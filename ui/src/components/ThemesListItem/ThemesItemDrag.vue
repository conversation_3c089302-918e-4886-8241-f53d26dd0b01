<template>
  <section class="themes-item-drag">
    <section v-tooltip.bottom="{
        class: 'tooltip-base-drag',
        delay: 0,
        content: `Drag to Merge`,
      }">
      <i class="fa-solid fa-grip-dots-vertical draggable-icon" />
    </section>
  </section>
</template>

<script>
export default {
  name: 'themes-item-drag',
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-item-drag {
  .draggable-icon {
    @include flex("block", "row", "center", "center");

    color: #BBB;
    font-size: 0.9rem;

    &::before {
      padding: 2px 4px;
    }

    &:hover::before {
      background-color: rgba(191, 191, 191, 0.2);
      border-radius: 1px;
    }
  }
}
</style>
