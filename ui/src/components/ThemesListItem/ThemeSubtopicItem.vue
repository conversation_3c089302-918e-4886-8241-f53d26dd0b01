<template>
  <section
    class="theme-subtopic-item"
    :class="{
      active: isActive,
      'controls-disabled': disableControls,
      'show-renaming': showRenaming,
      'show-removing': showRemoving,
      'show-upgrading': showUpgrading,
      'theme-active': themeActive,
    }"
    @click="onClickSubtopic"
    @mouseenter="onMouseEnter"
    @mouseleave="onMouseLeave"
  >
    <section class="inner normal">
      <theme-subtopic-item-interaction
        v-if="!disableControls"
        class="item-action"
        :hover="hover"
        :topic="topic"
      />

      <section class="item-body">
        <section class="label">
          <section class="text">
            {{ topic.topicLabel }}
          </section>

          <section class="icons" v-if="!disableControls && (hover || activeSummary)">
            <icon-promote v-if="hover" class="icon-control" @click.stop="showUpgrading = true"
              v-tooltip.bottom="{
                content: 'Promote to Theme',
                class: 'tooltip-base-dark',
                delay: 0,
              }"
            />

            <icon-rename v-if="hover" class="icon-control" @click.stop="showRenaming = true"
              v-tooltip.bottom="{
                content: 'Rename Theme',
                class: 'tooltip-base-dark',
                delay: 0,
              }"
            />

            <icon-delete v-if="hover" class="icon-control" @click.stop="showRemoving = true"
              v-tooltip.bottom="{
                content: 'Rename Theme',
                class: 'tooltip-base-dark',
                delay: 0,
              }"
            />

            <icon-summary v-if="isCommentsView && (hover || activeSummary)"
              :active="activeSummary"
              class="icon-control"
              @click.stop="onClickSummary"
              v-tooltip.bottom="{
                content: 'Theme Summary',
                class: 'tooltip-base-dark',
                delay: 0,
                }"
            />
          </section>
        </section>

        <section class="info">
          <section class="volume" :class="{ sorted: isSortedByVol }">
            <strong>Vol</strong>&nbsp;
            {{ topic.numOfDocuments }} ({{ volumePercent }})
          </section>

          <section v-if="!disableControls" class="adorescore" :class="{ sorted: isSortedByScore }" v-tooltip.bottom="{
            class: 'tooltip-base-drag',
            delay: 0,
            content: `Adorescore`,
          }">
            <adorescore-box-mini :bucket="bucket" :score="adorescore"/>
          </section>
        </section>
        <!-- NOTE: subtopics dont have swot attribute -->
      </section>
    </section>

    <themes-list-item-renaming v-if="showRenaming" :theme="topic" @cancel="cancelRename" @confirm="confirmRename" />
    <themes-list-item-removing v-if="showRemoving" :theme="topic" @cancel="cancelRemove" @confirm="confirmRemove" />

    <section v-if="showUpgrading" class="inner upgrading" @click.stop>
      <section class="upgrading-input">
        <i class="fa-solid fa-chevrons-up icon promote" />

        <section class="text">Promote Sub-Theme to Main Theme?</section>

        <base-tag
          :background-colour="'#00BD1E'"
          :colour="'#00A51A'"
          :colour-hover="'#00A51A'"
          :text="'Confirm'"
          :text-colour="'white'"
          :border-radius="'2rem'"
          :padding="'0.3rem 0.5rem 0.3rem 0.5rem'"
          @click="confirmUpgrade"
        />

        <i class="fa-solid fa-x icon x-icon" @click.stop="cancelUpgrade" />
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import AdorescoreBoxMini from '@/components/AdorescoreBox/AdorescoreBoxMini';
import BaseButton from '@/components/Base/BaseButton';
import BaseCheckboxSolid from '@/components/Base/BaseCheckboxSolid';
import BaseInput from '@/components/Base/BaseInput';
import BaseTag from '@/components/Base/BaseTag';
import IconDelete from '@/components/Icons/IconDelete';
import IconPromote from '@/components/Icons/IconPromote';
import IconRename from '@/components/Icons/IconRename';
import IconSummary from '@/components/Icons/IconSummary';
import ResultsTabs from '@/enum/results-tabs';
import Route from '@/enum/route';
import ThemesListItemRemoving from '@/components/ThemesListItem/ThemesListItemRemoving';
import ThemesListItemRenaming from '@/components/ThemesListItem/ThemesListItemRenaming';
import ThemesSort from '@/enum/themes-sort';
import ThemeSubtopicItemInteraction from '@/components/ThemesListItem/ThemeSubtopicItemInteraction';
import ThemeSubtopicItemTextError from '@/components/ThemesList/ThemeSubtopicItemTextError';

import { snippetApi, themeApi, timeSeriesApi } from '@/services/api';
import { datasetsRequest, datasetsRequestV0, metadataRequest, themesRequest } from '@/services/request';

export default {
  name: 'theme-subtopic-item',

  components: {
    AdorescoreBoxMini,
    BaseButton,
    BaseCheckboxSolid,
    BaseInput,
    BaseTag,
    IconDelete,
    IconPromote,
    IconRename,
    IconSummary,
    ThemesListItemRemoving,
    ThemesListItemRenaming,
    ThemeSubtopicItemInteraction,
    ThemeSubtopicItemTextError,
  },

  props: {
    disableControls: {
      type: Boolean,
      required: true,
    },
    parent: {
      type: Object,
      required: true,
    },
    themeActive: {
      type: Boolean,
      required: true,
    },
    topic: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      hover: false,
      localUpgrade: '',
      saveActionUpgrade: false,
      showRenaming: false,
      showUpgrading: false,
      showRemoving: false,
    };
  },

  computed: {
    ...mapState('benchmark', ['buckets']),

    ...mapGetters('benchmark', ['classifyAdorescore']),

    ...mapState('datasets', ['active']),

    ...mapGetters('datasets', [
      'get',
      'selectedLimited',
      'viewingDatasetId',
    ]),

    ...mapState('layout', ['showThemeSummary']),

    ...mapState('themes', [
      'selectedSubtopic',
      'selectedTab',
      'sort',
      'themes',
      'type',
    ]),

    ...mapGetters('themes', ['isSubtopicNameExisted']),

    ...mapGetters('timeSeries', ['metadataIndex']),

    activeSummary() {
      return this.showThemeSummary && this.isActive;
    },

    adorescore() {
      return Math.round(this.topic.polarity * 100);
    },

    areTimelineThemes() {
      return this.$route.name === Route.EMOTION_ANALYSIS && this.selectedTab === ResultsTabs.TREND_ANALYSIS;
    },

    bucket() {
      return this.classifyAdorescore(this.adorescore);
    },

    isActive() {
      return this.selectedSubtopic?.id === this.topic.id;
    },

    isCommentsView() {
      return this.$route.name === Route.COMMENTS;
    },

    isSortedByScore() {
      return this.sort?.name === ThemesSort.ADORESCORE.name;
    },

    isSortedByVol() {
      return this.sort?.name === ThemesSort.VOLUME.name;
    },

    topicLabel() {
      return this.topic.topicLabel;
    },

    volumePercent() {
      const value = Math.round(this.topic.numOfDocuments / this.get(this.viewingDatasetId).documentCount * 100);
      return value === 0 ? '<1%' : `${value}%`;
    },
  },

  methods: {
    ...mapActions('datasets', ['setOverviews']),

    ...mapActions('layout', ['setShowThemeSummary']),

    ...mapActions('modal', ['setModalComponent']),

    ...mapActions('snippets', [
      'deselectAllSnippets',
      'resetSnippets',
      'setSnippets',
    ]),

    ...mapActions('themes', [
      'addSubtopics',
      'selectLabel',
      'selectSubtopic',
      'selectTheme',
      'setHoverTopic',
      'setThemes',
    ]),

    cancelRemove() {
      this.showRemoving = false;
    },

    cancelRename() {
      this.showRenaming = false;
    },

    cancelUpgrade() {
      this.showUpgrading = false;
    },

    async confirmRemove(saveActionRemove) {
      await themeApi.deleteTopics(this.active, [this.topic.id], { saveAction: saveActionRemove });

      await this.fetchPageData({ reloadThemes: false });

      this.showRemoving = false;
    },

    async confirmRename(name, saveActionRename) {
      if (this.isSubtopicNameExisted(this.topic.parentId, this.topic.id, name)) {
        this.showRenaming = false;
        this.setModalComponent({ component: ThemeSubtopicItemTextError });
        return;
      }

      await themeApi.renameTopic(
        this.active,
        this.topic.id,
        name,
        saveActionRename,
      );

      await this.fetchPageData({ reloadThemes: false });

      this.showRenaming = false;
    },

    async confirmUpgrade() {
      await themeApi.upgradeTopic(
        this.active,
        this.topic.id,
        this.saveActionUpgrade,
      );

      await this.fetchPageData();

      this.showUpgrading = false;
    },

    async fetchPageData({ reloadThemes = true, reloadTopics = true } = {}) {
      await datasetsRequestV0.getDatasets();
      await datasetsRequestV0.reloadSelected();

      if (reloadThemes) {
        const themes = await themesRequest.fetchThemes();
        this.setThemes({ themes });
      }

      if (reloadTopics) {
        const topics = await themesRequest.fetchSubtopics([this.parent], null);
        this.addSubtopics({ parentIds: [this.topic.parentId], subtopics: topics });
      }

      this.setOverviews({
        overviews: await datasetsRequest.fetchOverviews(this.selectedLimited, this.type.lowerCase()),
      });
    },

    async getSnippets() {
      this.resetSnippets();
      if (!this.areTimelineThemes) {
        await metadataRequest.filterCommentsOnMetadata();
        await metadataRequest.filterCommentsCountOnMetadata();
      } else {
        const meta = await timeSeriesApi.getSnippets(
          this.active,
          this.isActive ? this.topic.ids : this.parent.ids,
          {
            metadataIndex: this.metadataIndex,
          },
        );
        this.setSnippets({ meta, page: 0 });
        if (meta.snippets?.length) {
          await snippetApi.retrieveTopicIdsBySnippet(
            this.viewingDatasetId,
            meta.snippets.map(s => s.contentId),
          );
        }
      }
    },

    onClickSubtopic() {
      if (!this.themeActive) {
        this.$emit('onClickSubtopic');
      }

      this.deselectAllSnippets();

      if (this.showRenaming || this.showRemoving || this.showUpgrading) {
        return;
      }

      this.selectSubtopic({ topic: this.isActive ? null : this.topic });
      this.getSnippets();
    },

    onClickSummary() {
      if (this.activeSummary) {
        this.setShowThemeSummary({ value: false });
      } else {
        const theme = this.themes.find(t => t.id === this.topic.parentId);
        this.selectSubtopic({ topic: this.topic });
        this.selectTheme({ theme });
        this.selectLabel({ value: theme.topicLabel });
        this.setShowThemeSummary({ value: true });
      }
    },

    onMouseEnter() {
      this.hover = true;
      this.setHoverTopic({ hoverTopic: this.topic });
    },

    onMouseLeave() {
      this.hover = false;
      this.setHoverTopic({ hoverTopic: null });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

$subtopic-item-height: 30px;
$theme-colour-green: #00BD1E;

.theme-subtopic-item {
  @include rigid;

  &:first-child {
    margin-top: -3px;
  }

  &:last-child {
    padding-bottom: 1.2rem;
    border-bottom: 1px solid $theme-subtopic-item-bdr;
  }

  &.active {
    font-weight: $font-weight-bold;

    .adorescore, .volume {
      &.sorted {
        filter: brightness(0.7);
      }
    }
  }

  &.controls-disabled {
    .inner.normal {
      grid-template-columns: auto;
      padding-left: 2.3rem;

      .item-body {
        grid-template-columns: 1fr 6.2rem;
      }
    }
  }

  // keep this before/above others - .show-renaming, &.show-removing, &.show-upgrading...
  &.theme-active {
    background: clr("white");// $theme-active-bg;
    color: #2B1093;
  }

  &.show-renaming, &.show-removing, &.show-upgrading {
    background-color: clr('blue', 'lighter');
    border-radius: 3px;

    .inner.normal {
      display: none;
    }

    &:first-child {
      margin-top: 0;
    }
  }

  &.show-upgrading {
    // we dont have save-action for upgrade, so need to override the parent height ( can remove it later after implemented save-action for upgrading)
    height: $subtopic-item-height;

    .inner {
      grid-template-columns: 1fr;
      grid-template-rows: $subtopic-item-height;
      background: rgba(255, 255, 255, 0.35);

      .upgrading-input {
        display: grid;
        grid-template-columns: min-content 1fr 5.5rem min-content;
        padding-left: 1.3rem;
        padding-right: 0.3rem;

        .icon {
          align-self: center;
          cursor: pointer;
          font-size: $font-size-sm;
          justify-self: center;

          &:hover {
            opacity: 0.6;
          }

          &.promote {
            color: $theme-colour-green;
            height: 1rem;
            width: 1rem;
          }
        }

        .text {
          align-self: center;
          color: $theme-colour-green;
          font-size: $font-size-sm;
          padding-left: 0.5rem;
        }

        .base-button.colour-base {
          padding-right: 0.4rem;
        }
      }
    }
  }

  .inner {
    align-items: stretch;
    display: grid;
    justify-items: stretch;
    max-width: 100%;

    .item-body {
      cursor: pointer;
      display: grid;
      grid-template-columns: 1fr 11rem;
      grid-template-rows: $subtopic-item-height;
      justify-items: stretch;

      .label {
        @include flex("block", "row", "between", "stretch");
        @include truncate;

        .text {
          @include truncate;
          align-self: center;
          font-size: 0.7rem;
        }
      }

      .x-icon {
        border-radius: 50%;
        border: 1px solid;
      }
    }

    &:hover {
      font-weight: 500;
    }

    &.normal {
      cursor: pointer;
      display: grid;
      grid-template-columns: 48px auto;
      grid-auto-rows: $subtopic-item-height;

      .base-checkbox-solid .input-checkbox {
        border: $border-light solid $checkbox-bdr-purple;
      }

      .icons {
        @include flex("block", "row", "end", "center");

        margin-right: 1rem;

        .icon-control {
          margin-left: 0.625rem;
        }
      }

      .info {
        @include flex("block", "row", "start", "center");

        align-self: center;
        font-size: 0.7rem;
        white-space: nowrap;

        .adorescore {
          justify-self: start;
          padding-left: 0.35rem;
          width: 33%;

          &.sorted {
            color: $theme-item-text-sorted;
          }
        }

        .volume {
          justify-self: start;
          padding-left: 0.5rem;
          width: 67%;

          &.sorted {
            color: $theme-item-text-sorted;
          }
        }
      }
    }
  }
}
</style>

<style lang="scss">
.theme-subtopic-item {
  &:last-child .inner .item-action .icon-list .connector.bottom {
    border-left: none !important;
  }
}
</style>
