<template>
  <section class="themes-list-item-label">
    <section class="text" :title="theme.topicLabel">
      <section v-if="isSwot" class="swot-attribute" :style="{ backgroundColor: swotColor }" />
      <section class="text-inner">{{ theme.topicLabel }}</section>
    </section>

    <section v-if="!disableControls && (hover || activeSummary)" class="icons">
      <icon-rename v-if="hover" class="icon" @click.stop="onClickRename"
        v-tooltip.bottom="{
          content: 'Edit Theme',
          class: 'tooltip-base-dark',
          delay: 0,
        }"
      />

      <icon-delete v-if="hover" class="icon" @click.stop="onClickRemove"
        v-tooltip.bottom="{
          content: 'Delete Theme',
          class: 'tooltip-base-dark',
          delay: 0,
        }"
      />

      <icon-summary v-if="isCommentsView && (hover || activeSummary)"
        :active="activeSummary"
        class="icon"
        @click.stop="onClickSummary"
        v-tooltip.bottom="{
          content: 'Theme Summary',
          class: 'tooltip-base-dark',
          delay: 0,
        }"
      />
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import IconDelete from '@/components/Icons/IconDelete';
import IconRename from '@/components/Icons/IconRename';
import IconSummary from '@/components/Icons/IconSummary';
import Route from '@/enum/route';

export default {
  name: 'themes-list-item-label',

  components: {
    IconDelete,
    IconRename,
    IconSummary,
  },

  props: {
    active: {
      type: Boolean,
      required: true,
    },

    disableControls: {
      type: Boolean,
      default: false,
    },

    hover: {
      type: Boolean,
      default: false,
    },

    isSwot: {
      type: Boolean,
      default: false,
    },

    theme: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapState('layout', ['showThemeSummary']),

    ...mapState('themes', ['selectedSubtopic', 'swotColorMap']),

    activeSummary() {
      return this.showThemeSummary && this.isSelected;
    },

    isCommentsView() {
      return this.$route.name === Route.COMMENTS;
    },

    isSelected() {
      return this.active && !this.selectedSubtopic;
    },

    swotColor() {
      return this.swotColorMap[this.swotType];
    },

    swotType() {
      if (!this.isSwot) return 'NONE';

      return this.theme.swot.attribute;
    },
  },

  methods: {
    ...mapActions('layout', ['setShowThemeSummary']),

    ...mapActions('themes', [
      'selectLabel',
      'selectSubtopic',
      'selectTheme',
    ]),

    onClickRemove() {
      this.$emit('remove');
    },

    onClickRename() {
      this.$emit('rename');
    },

    onClickSummary() {
      if (this.activeSummary) {
        this.setShowThemeSummary({ value: false });
      } else {
        this.selectSubtopic({ topic: null });
        this.selectTheme({ theme: this.theme });
        this.selectLabel({ value: this.theme.topicLabel });
        this.setShowThemeSummary({ value: true });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-list-item-label {
  @include flex("block", "row", "between", "stretch");
  @include truncate;

  font-size: 0.75rem;

  .text {
    @include truncate;

    cursor: pointer;
    display: grid;
    grid-auto-flow: column;

    .swot-attribute {
      align-self: center;
      border-radius: 0.5rem;
      height: 0.5rem;
      margin-right: 0.3rem;
      opacity: 0.7;
      width: 0.5rem;
    }

    .text-inner {
      @include truncate;

      align-self: center;
      user-select: none;
    }
  }

  .icons {
    @include flex("block", "row", "end", "center");

    .icon {
      margin-left: 0.625rem;
    }
  }
}
</style>
