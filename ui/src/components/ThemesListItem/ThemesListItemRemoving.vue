<template>
  <section class="themes-list-item-removing">
    <section class="removing-input">
      <i class="fa-solid fa-trash-can icon danger"/>

      <section class="text">
        <b>Delete Theme?</b>&nbsp;&nbsp;This can't be undone.
      </section>

      <base-tag @click="onConfirm"
        :background-colour="'#CB1509'"
        :border-radius="'2rem'"
        :colour-hover="'#CB1509'"
        :colour="'#A91208'"
        :padding="'0.3rem 0.5rem 0.3rem 0.5rem'"
        :text-colour="'white'"
        :text="'Confirm'"
      />
      <i class="fa-solid fa-x icon x-icon" @click="onCancel" />
    </section>

    <themes-list-item-saved-action-in-place :save-action="saveActionDelete" @toggle="onToggleSaveAction"/>
  </section>
</template>

<script>
import BaseInput from '@/components/Base/BaseInput';
import BaseTag from '@/components/Base/BaseTag';
import ThemesListItemSavedActionInPlace from '@/components/ThemesList/ThemesListItemSavedActionInPlace';

export default {
  name: 'themes-list-item-removing',

  components: {
    BaseInput,
    BaseTag,
    ThemesListItemSavedActionInPlace,
  },

  props: {
    theme: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      localRename: '',
      saveActionDelete: false,
    };
  },

  created() {
    this.localRename = this.theme.topicLabel;
  },

  methods: {
    onCancel() {
      this.$emit('cancel');
    },

    onConfirm() {
      this.$emit('confirm', this.saveActionDelete);
    },

    onToggleSaveAction() {
      this.saveActionDelete = !this.saveActionDelete;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-list-item-removing {
  align-items: stretch;
  background: rgba(255, 255, 255, 0.35);
  border-radius: 4px;
  color: $body-copy;
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: $themes-item-height+10 $themes-item-height;
  justify-items: stretch;
  max-width: 100%;

  .removing-input {
    display: grid;
    grid-template-columns: 1.5rem 1fr 5.5rem min-content;
    padding-right: 0.3rem;
    margin-left: 0.3rem;
    margin-right: 0.3rem;
    border-bottom: 1px solid rgba(100, 90, 160, 0.05);

    b {
      color: clr('red');
    }

    .icon {
      align-self: center;
      justify-self: center;

      &.danger {
        color: clr('red');
      }
    }

    .text {
      align-self: center;
      font-size: $font-size-sm;
    }

    .x-icon {
      cursor: pointer;
    }
  }
}
</style>
