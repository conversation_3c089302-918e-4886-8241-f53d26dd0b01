<template>
  <section class="themes-list-item-sub-topic-tag">
    <span class="tag-input" :class="{ selected }" @click.stop="onClickExpandTheme">
      {{ theme.numOfSubTopics }}
      <i class="fa-solid fa-caret-down icon" :class="{ selected }" />
    </span>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

export default {
  name: 'themes-list-item-sub-topic-tag',

  props: {
    theme: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapState('themes', ['expandedThemes']),

    selected() {
      return this.expandedThemes.includes(this.theme.id);
    },
  },

  methods: {
    ...mapActions('themes', ['toggleExpandedTheme']),

    async onClickExpandTheme() {
      this.toggleExpandedTheme({ themeId: this.theme.id });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-list-item-sub-topic-tag {
  @include flex("block", "row", "center", "center");

  .tag-input {
    @include flex("block", "row", "center", "center");

    background: rgba(37, 16, 167, 0.39);
    border: $border-light solid rgba(37, 16, 167, 0.54);
    border-radius: 3px;
    color: clr('white');
    cursor: pointer;
    font-size: $font-size-xxs;
    font-weight: $font-weight-bold;
    line-height: inherit;
    margin: 0.1rem 0.3rem;
    overflow: hidden;
    padding: 2px 6px;
    text-overflow: ellipsis;
    text-transform: uppercase;
    white-space: nowrap;

    &:hover, &.selected {
      background-color: rgba(37, 16, 167, 0.61);
      border: $border-light solid rgba(37, 16, 167, 0.92);
    }

    .icon {
      @include flex("block", "row", "center", "center");

      color: clr('white');
      height: 0.8rem;
      min-height: 0.8rem;
      min-width: 0.8rem;
      transition: opacity $interaction-transition-time;
      width: 0.8rem;

      &.selected {
        transform: rotate(180deg);
      }
    }
  }
}
</style>
