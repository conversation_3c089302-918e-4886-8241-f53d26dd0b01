<template>
  <section class="theme-subtopic-list" :class="{ active }">
    <section v-if="loading" class="loading">
      <loading-blocks-overlay>Loading Topics...</loading-blocks-overlay>
    </section>

    <section v-if="!loading" class="subtopic-item">
      <theme-subtopic-item v-for="topic in subtopicList"
        :disable-controls="disableControls"
        :id="`theme-subtopic-item-${topic.id}`"
        :key="topic.id"
        :parent="theme"
        :theme-active="active"
        :topic="topic"
        @onClickSubtopic="onClickSubtopic"
      />
    </section>
    <section class="subtopic-end" @click.stop="onClickCloseExpand">
      <section class="x-icon">
        <img :src="require('@/assets/icon-close-white.svg')" class="x-icon-img"/>
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import NetworkKeys from '@/enum/network-keys';
import NetworkStatus from '@/enum/network-status';
import ThemeSubtopicItem from '@/components/ThemesListItem/ThemeSubtopicItem';

export default {
  name: 'theme-subtopic-list',

  components: {
    LoadingBlocksOverlay,
    ThemeSubtopicItem,
  },

  props: {
    active: {
      type: Boolean,
      required: true,
    },

    disableControls: {
      type: Boolean,
      default: false,
    },

    theme: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapGetters('network', ['status']),

    ...mapState('poll', ['topicsState']),

    ...mapState('themes', ['expandedThemes', 'fetchParents']),

    ...mapGetters('themes', ['areSubtopicsLoaded', 'getSubtopics']),

    isExpanded() {
      return this.expandedThemes.includes(this.theme.id);
    },

    subtopicList() {
      const rs = this.getSubtopics(this.theme.id);
      return rs;
    },

    loading() {
      return (this.topicsState === NetworkStatus.LOADING
                || (this.status(NetworkKeys.THEMES_TOPICS) === NetworkStatus.LOADING) && !this.areSubtopicsLoaded(this.theme.id))
                && this.fetchParents.map(i => i.id).includes(this.theme.id);
    },
  },

  methods: {
    ...mapActions('themes', ['toggleExpandedTheme']),

    onClickCloseExpand() {
      if (this.isExpanded) this.toggleExpandedTheme({ themeId: this.theme.id });
    },

    onClickSubtopic() {
      this.$emit('onClickSubtopic', true);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.theme-subtopic-list {

  .subtopic-item {
    @include scrollbar-thin;

    max-height: 200px;
    overflow: auto;

    @media screen and (-webkit-min-device-pixel-ratio: 0) { // Support overlay scrollbar for webkit browsers
      overflow: overlay;
    }
  }

  .subtopic-end {
    @include flex("block", "row", "center", "center", "wrap");
    @include rigid;

    background-color: rgba(230, 227, 240, 0.33);
    height: 14px;
    font-size: 0.6rem;
    position: relative;
    text-transform: uppercase;
    line-height: 0.5rem;

    .x-icon {
      height: 1rem;
      width: 1rem;
      border-radius: 50%;
      background-color: #2B1093;
      bottom: 0.5rem;
      cursor: pointer;
      position: relative;

      .x-icon-img {
        height: 0.6rem;
        margin: 0.2rem;
        width: 0.6rem;
      }

      &:hover {
        transform: scale(1.2);
      }
    }
  }
}
</style>
