<template>
  <section class="storyteller-action-plans-modal-themes-list">
    <storyteller-action-plans-modal-themes-item
      v-for="(item, index) in selectedThemesInsights"
      :key="item.topicId"
      :order="index+1"
      :theme="item.insightsTopicsModel"
    />
  </section>
</template>

<script>
import { mapGetters } from 'vuex';

import StorytellerActionPlansModalThemesItem from '@/components/StorytellerHeaderLeft/StorytellerActionPlansModalThemesItem';

export default {
  name: 'storyteller-action-plans-modal-themes-list',

  components: {
    StorytellerActionPlansModalThemesItem,
  },

  computed: {
    ...mapGetters('storyteller', ['slideThemesInsights']),

    selectedThemesInsights() {
      return this.slideThemesInsights.slideData.insightThemesData;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-action-plans-modal-themes-list {
  @include flex("block", "column", "start", "start");

  margin-bottom: 1rem;
  width: 100%;
}
</style>
