<template>
  <section class="storyteller-action-plans-modal">
    <section class="header">
      <i class="fa-solid fa-wand-magic-sparkles icon-magic text-gradient" />
      <span class="text text-gradient">Suggested Action Plans</span>
    </section>
    <section class="body">
      <storyteller-action-plans-modal-message />
      <storyteller-action-plans-modal-themes-list />
    </section>
    <section class="footer">
      <base-button class="cancel-btn" colour="light" size="small" type="link" @click="onClickCancel">Cancel</base-button>
      <loading-blocks-overlay v-if="saving" size="small" />
      <base-button v-else class="done-btn" :disabled="disabled" size="small" @click="onSubmit">
        <span>{{doneMessage}}</span>
      </base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseCheckboxSolid from '@/components/Base/BaseCheckboxSolid';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import ModalEvent from '@/components/Mixins/ModalEvent';
import StorytellerActionPlansModalMessage from '@/components/StorytellerHeaderLeft/StorytellerActionPlansModalMessage';
import StorytellerActionPlansModalThemesList from '@/components/StorytellerHeaderLeft/StorytellerActionPlansModalThemesList';
import StorytellerActionPlansRequest from '@/services/request/StorytellerActionPlansRequest';

export default {
  name: 'storyteller-action-plans-modal',

  components: {
    BaseButton,
    BaseCheckboxSolid,
    LoadingBlocksOverlay,
    StorytellerActionPlansModalMessage,
    StorytellerActionPlansModalThemesList,
  },

  mixins: [ModalEvent],

  data() {
    return {
      saving: false,
    };
  },

  computed: {
    ...mapState('storyteller', ['activeReport']),

    ...mapState('themes', ['selectedThemes']),

    disabled() {
      return this.selectedThemes.length === 0;
    },

    doneMessage() {
      if (this.noActionPlans) return 'Generate Action Plan';
      return 'Update Action Plan';
    },

    noActionPlans() {
      return !(this.activeReport.actionPlanThemeIds?.length);
    },
  },

  created() {
    this.resetThemes();

    if (this.noActionPlans) {
      this.selectThemes({ ids: this.activeReport.themeIds.slice(0, 3) });
    } else {
      this.selectThemes({ ids: this.activeReport.actionPlanThemeIds });
    }
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('themes', {
      resetThemes: 'reset',
      selectThemes: 'selectThemes',
    }),

    onClickCancel() {
      this.closeModal();
    },

    async onSubmit() {
      this.saving = true;
      if (this.noActionPlans) {
        await StorytellerActionPlansRequest.generate();
      } else {
        await StorytellerActionPlansRequest.updateThemes();
      }
      this.saving = false;
      this.closeModal();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-action-plans-modal {
  @include panel;

  color: $nps-blue;
  position: relative;
  width: 380px;

  .header {
    @include flex("block", "row", "start", "center");
    @include truncate;

    border-bottom: 1px solid rgba(19, 28, 41, 0.1);
    font-size: 15px;
    font-weight: $font-weight-bold;
    padding: 1rem 1.5rem;
    width: 100%;

    .icon-magic {
      margin-right: 0.4rem;
    }

    .text-gradient {
      background: linear-gradient(to bottom, #A43CD1 ,  #735DEC);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .body {
    font-size: $font-size-xs;
    width: 100%;
  }

  .footer {
    @include flex("block", "row", "space-between", "center");

    border-top: 1px solid rgba(19, 28, 41, 0.1);
    padding: 1rem 1.5rem;
    width: 100%;

    .base-button {
      font-size: $font-size-xxs;
      font-weight: 600;
      text-transform: uppercase;

      &.cancel-btn {
        padding-left: 0;
      }

      &.done-btn {
        background-color: #2D1757;
        padding: 0.5rem 0.8rem;
        width: fit-content;
      }

      &:hover, &:focus {
        &.done-btn {
          background-color: rgba(19, 28, 41);
        }
      }
    }
  }
}
</style>
