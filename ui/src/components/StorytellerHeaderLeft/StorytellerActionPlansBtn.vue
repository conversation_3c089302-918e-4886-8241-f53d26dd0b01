<template>
  <section class="storyteller-action-plans-btn">
    <section class="btn-action-plans" @click.stop="onClickActionPlans">
      <section class="btn-content">
        <i class="fa-solid fa-plus plus-icon text-gradient" />
        <span class="text-gradient">Actions</span>
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import StorytellerActionPlansModal from '@/components/StorytellerHeaderLeft/StorytellerActionPlansModal';

export default {
  name: 'storyteller-action-plans-btn',

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    onClickActionPlans() {
      this.setModalComponent({ component: StorytellerActionPlansModal });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

$element-height: 26px;

.storyteller-action-plans-btn {
  @include flex("block", "row", "start", "center");

  .btn-action-plans {
    @include flex("block", "row", "center", "center");

    background:
        linear-gradient(to right, white, white),
        linear-gradient(to right, #A43CD1 ,  #735DEC);
    background-clip: padding-box, border-box;
    background-origin: padding-box, border-box;
    border-radius: $element-height;
    border: 1px solid transparent;
    cursor: pointer;
    font-size: $font-size-xxs;
    font-weight: $font-weight-extra-bold;
    height: $element-height;
    position: relative;
    text-transform: uppercase;
    width: 84px;

    .btn-content {
      position: absolute;

      .plus-icon {
        margin-right: 2px;
      }

      .text-gradient {
        background: linear-gradient(to bottom, #A43CD1 ,  #735DEC);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }

    &:hover {
      border-width: 2px;

      background:
          linear-gradient(to right, white, white),
          linear-gradient(to right, #705FED ,  #2F1FA0);
      background-clip: padding-box, border-box;
      background-origin: padding-box, border-box;

      .text-gradient {
        background: linear-gradient(to bottom, #705FED ,  #2F1FA0);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }
}
</style>
