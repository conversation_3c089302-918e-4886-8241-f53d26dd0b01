<template>
  <section class="storyteller-action-plans-modal-themes-item" @click="onClickTheme">
    <section class="content">
      <base-checkbox-solid :value="selected" />
      <span class="order">{{order}}</span>
      <span class="name">{{theme.topicLabel}}</span>
      <adorescore-box-mini :bucket="bucket" :score="adorescore"/>
      <section class="border-bottom" />
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import AdorescoreBoxMini from '@/components/AdorescoreBox/AdorescoreBoxMini';
import BaseCheckboxSolid from '@/components/Base/BaseCheckboxSolid';

export default {
  name: 'storyteller-action-plans-modal-themes-item',

  components: {
    AdorescoreBoxMini,
    BaseCheckboxSolid,
  },

  props: {
    order: {
      type: Number,
      required: true,
    },
    theme: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapGetters('benchmark', ['classifyAdorescore']),

    ...mapState('themes', ['selectedThemes']),

    adorescore() {
      return Math.round(this.theme.polarity * 100);
    },

    bucket() {
      return this.classifyAdorescore(this.adorescore);
    },

    selected() {
      return this.selectedThemes.includes(this.theme.id);
    },
  },

  methods: {
    ...mapActions('themes', ['deselectThemes', 'selectThemes']),

    onClickTheme() {
      if (this.selected) {
        this.deselectThemes({ ids: [this.theme.id] });
      } else {
        this.selectThemes({ ids: [this.theme.id] });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-action-plans-modal-themes-item {
  @include flex("block", "row", "start", "center");

  border-radius: 3px;
  border: 1px solid transparent;
  cursor: pointer;
  font-size: $font-size-xs;
  margin-left: 0.5rem;
  margin-right: 0.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  position: relative;
  width: calc(100% - 1rem);

  &:last-child {
    .content .border-bottom {
      display: none;
    }
  }

  &:hover {
    background-color: #E6EAFF;

    .draggable-icon {
      visibility: visible;
    }
  }

  .content {
    @include flex("block", "row", "start", "center");

    padding: 0.6rem 0;
    position: relative;
    width: 100%;

    .base-checkbox-solid {
      margin-right: 0.5rem;
    }

    .order {
      @include flex("block", "row", "center", "center");

      background-color: rgba(217, 217, 217, 0.3);
      border-radius: 50%;
      font-size: 11px;
      font-weight: $font-weight-bold;
      margin-right: 0.4rem;
      min-height: 1.3rem;
      min-width: 1.3rem;
    }

    .name {
      @include truncate;

      margin-right: 10px;
      overflow: hidden;
    }

    .adorescore-box-mini {
      margin-left: auto;
    }

    .border-bottom {
      background-color: rgba(19, 28, 41, 0.1);
      bottom: -1px;
      height: 1px;
      position: absolute;
      width: 100%;
    }
  }
}
</style>
