<template>
  <section class="storyteller-header-left">
    <storyteller-trial-badge />
    <i class="fa-solid fa-book icon-book"></i>
    <span class="item">{{reportName}}</span>
    <storyteller-action-plans-btn class="item" v-if="noActionPlans" />
    <storyteller-refresh-themes-btn v-if="reportChanged" />
  </section>
</template>

<script>
import { mapState } from 'vuex';

import StorytellerActionPlansBtn from '@/components/StorytellerHeaderLeft/StorytellerActionPlansBtn';
import StorytellerRefreshThemesBtn from '@/components/StorytellerHeaderLeft/StorytellerRefreshThemesBtn';
import StorytellerRequest from '@/services/request/StorytellerRequest';
import StorytellerTrialBadge from '@/components/Storyteller/StorytellerTrialBadge';

export default {
  name: 'storyteller-header-left',

  components: {
    StorytellerActionPlansBtn,
    StorytellerRefreshThemesBtn,
    StorytellerTrialBadge,
  },

  computed: {
    ...mapState('storyteller', ['activeReport', 'reportStatus']),

    noActionPlans() {
      return !(this.activeReport.actionPlanThemeIds?.length);
    },

    reportChanged() {
      return this.reportStatus?.changed;
    },

    reportName() {
      return this.activeReport.settings.reportName;
    },
  },

  created() {
    StorytellerRequest.checkStatus();
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-header-left {
  @include flex("block", "row", "start", "center");

  font-size: $font-size-base;
  font-weight: $font-weight-bold;

  .icon-book {
    margin-right: 0.4rem;
  }

  .item {
    margin-right: 1.5rem;
  }
}
</style>
