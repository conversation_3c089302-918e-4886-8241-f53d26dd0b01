<template>
  <section class="storyteller-refresh-themes-btn">
    <loading-blocks-overlay v-if="loading" size="small" />
    <section v-else class="content">
      <span>{{message}}</span>
      <base-button class="btn" @click="onClickBtn">
        <i class="fa-regular fa-arrows-rotate icon-refresh" />
        <span class="text">Update Slides</span>
      </base-button>
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import StorytellerRequest from '@/services/request/StorytellerRequest';

export default {
  name: 'storyteller-refresh-themes-btn',

  components: {
    BaseButton,
    LoadingBlocksOverlay,
  },

  data() {
    return {
      loading: false,
    };
  },

  computed: {
    ...mapState('storyteller', ['reportStatus']),

    message() {
      const { removedThemeIds, renamedThemeIds, varChangedThemeIds, varChanged, themeChanged } = this.reportStatus;
      const removedCount = removedThemeIds.length;
      const renamedCount = renamedThemeIds.length;
      const varChangedThemeCount = varChangedThemeIds.length;
      let msg = '';

      if (themeChanged) {
        if (removedCount > 0 && renamedCount === 0) {
          msg = 'Theme(s) have been removed from the dataset';
        } else if (removedCount === 0 && renamedCount > 0) {
          msg = 'Theme(s) have been renamed in the dataset';
        } else if (removedCount > 0 && renamedCount > 0) {
          msg = 'Theme(s) have been renamed or removed in the dataset';
        }
        if (varChanged || varChangedThemeCount > 0) {
          return `Revenue at Risk has been modified and ${msg}`;
        }
        return `${msg}.`;
      }

      if (varChanged) {
        return 'Revenue at Risk has been modified.';
      }

      return 'Revenue at Risk has been modified.';
    },
  },

  methods: {
    async onClickBtn() {
      this.loading = true;
      await StorytellerRequest.refreshReport();
      this.loading = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

$element-height: 21px;

.storyteller-refresh-themes-btn {
  @include flex("block", "row", "start", "center");

  .loading {
    padding-left: 5rem;
  }

  .content {
    @include flex("block", "row", "start", "center");

    background-color: #F4F4F4;
    border-radius: $element-height;
    font-size: $font-size-xxs;
    font-weight: $font-weight-normal;
    height: $element-height;
    padding: 0.3rem 0 0.3rem 0.6rem;
  }

  .btn {
    background-color: rgba(107, 99, 200, 0.8);
    border-radius: 21px;
    font-size: $font-size-xxs;
    height: $element-height;
    margin-left: 0.8rem;
    padding: 0 0.6rem;
    text-transform: uppercase;

    .icon-refresh {
      margin-right: 0.3rem;
    }

    &:hover, &:focus {
      background-color: rgba(107, 99, 200, 1);
    }
  }
}
</style>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-refresh-themes {
  @include flex("block", "row", "start", "center");

  .loading-blocks-overlay {
    margin-left: 6rem;
  }
}
</style>
