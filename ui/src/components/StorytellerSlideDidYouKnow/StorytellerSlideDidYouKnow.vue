<template>
  <section class="storyteller-slide-did-you-know" :style="{fontSize}" @mouseenter="hovered = true" @mouseleave="hovered = false">
    <section class="margin-top" />
    <section class="body">
      <span class="header">{{slideData.header}}</span>
      <section class="body-content">
        <section class="content" :class="{editable}">
          <section class="text-header">
            <span class="text-title">Did you know?</span>
            <storyteller-slide-setting-btn v-if="editable"
              :icon="'fa-light fa-arrow-up-arrow-down'"
              :size="'25px'"
              :text="'Select Emotion'"
              @click="onClickSettings"
            />
          </section>
          <storyteller-slide-text
            :additional-buttons="additionalButtons"
            :editable="editable"
            :hovered="hovered"
            :font-size="fontSizeText"
            :text-align="textAlign"
            :model-value="text"
            @selectAlign="selectAlign"
            @selectVolumeMinus="selectVolumeMinus"
            @selectVolumePlus="selectVolumePlus"
            @stopEditing="stopEditing"
          />
        </section>
      </section>
    </section>
    <storyteller-slide-footer :slide-width="slideWidth" />
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import StorytellerRegenerateInsight from '@/components/StorytellerSlideText/StorytellerRegenerateInsight';
import StorytellerSlideDidYouKnowSettingsModal from '@/components/StorytellerSlideDidYouKnow/StorytellerSlideDidYouKnowSettingsModal';
import StorytellerSlideFooter from '@/components/StorytellerSlides/StorytellerSlideFooter';
import StorytellerSlideSettingBtn from '@/components/Storyteller/StorytellerSlideSettingBtn';
import StorytellerSlideText from '@/components/StorytellerSlideText/StorytellerSlideText';
import UpdateSlide from '@/components/Mixins/UpdateSlide';

export default {
  name: 'storyteller-slide-did-you-know',

  components: {
    StorytellerSlideFooter,
    StorytellerSlideText,
    StorytellerSlideSettingBtn,
  },

  mixins: [UpdateSlide],

  computed: {
    additionalButtons() {
      return [StorytellerRegenerateInsight];
    },
  },

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    onClickSettings() {
      this.setModalComponent({ component: StorytellerSlideDidYouKnowSettingsModal });
    },
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-did-you-know {
  background-color: #FFF;
  color: $nps-blue;
  display: grid;
  font-family: Inter, serif;
  font-weight: $font-weight-semi-light;
  grid-template-rows: 13% 74% 13%;
  height: 100%;
  padding: 0 2em;
  width: 100%;

  &:hover {
    .body .body-content .content .text-header .storyteller-slide-setting-btn {
      display: flex;
    }
  }

  .body {
    @include flex("block", "column", "center", "center");

    height: 100%;
    position: relative;
    width: 100%;

    .header {
      font-size: 0.5em;
      font-weight: 800;
      left: 0;
      letter-spacing: 0.04em;
      opacity: 0.5;
      position: absolute;
      text-transform: uppercase;
      top: 0;
    }

    .body-content {
      @include flex("block", "column", "center", "center");

      margin: auto;
      width: 82%;

      .content {
        @include flex("block", "column", "start", "start");

        &.editable {
          padding-left: 35px;
        }

        .text-header {
          @include flex("block", "row", "start", "center");

          margin-bottom: 0.35em;
          position: relative;

          .text-title {
            font-size: 0.7em;
            font-weight: $font-weight-bold;
          }

          .storyteller-slide-setting-btn {
            display: none;
            position: absolute;
            right: -1.5em;
          }
        }

        .storyteller-slide-text {
          line-height: 130%;
          //Todo temporary fix.
          .text {
            .text-content {
              p {
                margin: 0;
                display: -webkit-box;
                -webkit-line-clamp: 5;
                -webkit-box-orient: vertical;
                overflow: hidden;
              }
            }
          }
        }
      }
    }
  }
}
</style>
