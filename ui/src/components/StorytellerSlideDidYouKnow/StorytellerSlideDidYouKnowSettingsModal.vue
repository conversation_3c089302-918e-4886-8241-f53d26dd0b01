<template>
  <section class="storyteller-slide-did-you-know-settings-modal" @click.stop>
    <section class="header">
      <section class="header-title">
        <i class="fa-regular fa-heart icon-heart" />
        <span class="header-text">Select Highlighted Emotion*</span>
      </section>
      <span class="header-description">*Changing the emotion will regenerate the insight</span>
    </section>
    <section class="body">
      <section class="body-header">
        <span class="body-header-text">Emotion</span>
        <span class="body-header-text">Emotion Score</span>
      </section>
      <section class="body-items">
        <section class="body-item" v-for="(emotion, index) in emotions" :key="index" @click="onClickEmotion(index)">
          <section class="body-item-emotion">
            <base-radio-with-tick-mark :value="selectedEmotion.name === emotion.name" />
            <span :class="['emotion', emotion.name, { selected: selectedEmotion.name === emotion.name }]">
              {{emotion.name}}
            </span>
          </section>
          <span class="body-item-score">{{ emotion.score }}</span>
        </section>
      </section>
    </section>
    <section class="footer">
      <base-button class="cancel-btn" colour="light" size="small" type="link" @click="closeModal">Cancel</base-button>
      <loading-blocks-overlay v-if="saving" size="small" />
      <base-button v-else class="done-btn" size="small" @click="onClickDone">
        <span>Done</span>
      </base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseRadioWithTickMark from '@/components/Base/BaseRadioWithTickMark';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import ThemesRequest from '@/services/request/ThemesRequest';
import StorytellerSlideRequest from '@/services/request/StorytellerSlideRequest';

export default {
  name: 'storyteller-slide-did-you-know-settings-modal',

  components: {
    BaseButton,
    BaseRadioWithTickMark,
    LoadingBlocksOverlay,
  },

  data() {
    return {
      emotions: [],
      saving: false,
      selectedEmotion: {
        name: null,
      },
    };
  },

  computed: {
    ...mapGetters('datasets', ['themeEmotionPercentage']),

    ...mapState('datasets', {
      datasetId: 'active',
    }),

    ...mapState('storyteller', ['selectedSlide']),
  },

  async created() {
    const theme = await ThemesRequest.fetchTopic(this.selectedSlide.slideData.themeId);
    this.emotions = [
      {
        name: 'joy',
        score: this.emoPercentage(theme.emotionIndexes[1], 1),
      },
      {
        name: 'trust',
        score: this.emoPercentage(theme.emotionIndexes[7], 7),
      },
      {
        name: 'interest',
        score: this.emoPercentage(theme.emotionIndexes[3], 3),
      },
      {
        name: 'surprise',
        score: this.emoPercentage(theme.emotionIndexes[2], 2),
      },
      {
        name: 'sadness',
        score: this.emoPercentage(theme.emotionIndexes[0], 0),
      },
      {
        name: 'disgust',
        score: this.emoPercentage(theme.emotionIndexes[6], 6),
      },
      {
        name: 'anger',
        score: this.emoPercentage(theme.emotionIndexes[5], 5),
      },
      {
        name: 'apprehension',
        score: this.emoPercentage(theme.emotionIndexes[4], 4),
      },
    ];

    if (this.selectedSlide.slideData.selectedEmotion) {
      this.selectedEmotion = this.emotions.find(emotion => emotion.name === this.selectedSlide.slideData.selectedEmotion);
    }
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    async onClickDone() {
      this.saving = true;
      await StorytellerSlideRequest.generateDidYouKnowSlideWithEmotion(this.selectedEmotion.name);
      this.saving = false;
      this.closeModal();
    },

    onClickEmotion(index) {
      this.selectedEmotion = this.emotions[index];
    },

    emoPercentage(emoValue, emoIndex) {
      const { gapScore, percent } = this.themeEmotionPercentage(
        this.datasetId,
        emoValue,
        emoIndex,
      );

      if (gapScore === 0) {
        return '+0%';
      }

      return `${gapScore > 0 ? '+' : '-'}${percent === '<1' ? '1' : percent}%`;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-did-you-know-settings-modal {
  @include flex("block", "column", "start", "start");
  @include panel;

  cursor: auto;
  font-size: $font-size-sm;
  font-variant-ligatures: none;
  white-space: nowrap;
  width: 368px;

  .header {
    @include flex("block", "column", "center", "start");

    border-bottom: 1px solid rgba(19, 28, 41, 0.2);
    padding: 1.25rem 1.5rem;
    width: 100%;

    .header-title {
      .icon-heart {
        font-size: 1rem;
      }

      .header-text {
        font-size: $font-size-sm;
        font-weight: $font-weight-bold;
        margin-left: 0.3rem;
      }
    }

    .header-description {
      color: rgba(19, 28, 41, 0.5);
      font-size: 11px;
      font-style: italic;
      margin-left: 1.4rem;
      margin-top: 0.625rem;
    }
  }

  .body {
    @include flex("block", "column", "start", "start");

    border-bottom: 1px solid rgba(19, 28, 41, 0.2);
    padding: 1.25rem;
    width: 100%;

    .body-header {
      display: grid;
      grid-template-columns: 50% 50%;
      width: 100%;

      span {
        color: rgba(31, 39, 52, 0.5);
        font-size: $font-size-xxs;
        font-weight: $font-weight-extra-bold;
        letter-spacing: 0.3px;
        text-transform: uppercase;
      }
    }

    .body-items {
      width: 100%;

      .body-item {
        align-items: center;
        border-bottom: 1px solid rgba(19, 28, 41, 0.1);
        cursor: pointer;
        display: grid;
        font-size: $font-size-xs;
        grid-template-columns: 50% 50%;
        padding: 0.6rem 0;
        width: 100%;

        &:last-child {
          border-bottom: none;
        }

        &:hover, &:focus {
          background-color: #E6EAFF;
        }

        .body-item-emotion {
          @include flex("block", "row", "start", "center");

          .emotion {
            margin-left: 0.625rem;
            text-transform: capitalize;

            &.joy, &.trust, &.interest, &.surprise {
              color: #62CC50;
            }

            &.sadness, &.disgust, &.anger, &.apprehension {
              color: #FF5454;
            }

            &.selected {
              font-weight: $font-weight-bold;
            }
          }
        }
      }
    }
  }

  .footer {
    @include flex("block", "row", "space-between", "center");

    padding: 1.25rem 1.5rem;
    width: 100%;

    .base-button {
      font-size: $font-size-xxs;
      letter-spacing: 0.3px;
      text-transform: uppercase;
      font-weight: $font-weight-bold;

      &.cancel-btn {
        color: rgba(141, 141, 141, 0.7);
        padding-left: 0;
      }

      &.done-btn {
        background-color: rgba(19, 28, 41, 0.8);
        padding: 0.375rem 1.125rem;
      }

      &:hover, &:focus {
        &.cancel-btn {
          color: rgba(141, 141, 141, 1);
        }

        &.done-btn {
          background-color: rgba(19, 28, 41);
        }
      }
    }
  }
}
</style>
