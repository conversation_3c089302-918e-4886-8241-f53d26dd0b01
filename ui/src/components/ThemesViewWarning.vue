<template>
  <section class="themes-view-warning">
    <h3 v-if="areSelectedPendingChanges">This dataset is being processed, and will be ready shortly.</h3>
    <h3 v-else>We couldn't find any themes for this dataset. Some older datasets will require re-analysis to enable this feature.</h3>
    <base-button
      v-if="!areSelectedPendingChanges"
      icon="refresh-ccw"
      size="small"
      type="link"
      @click="onClick"
    >Re-Analyse Dataset</base-button>
  </section>
</template>

<script>
import { RefreshCcwIcon } from 'vue-feather-icons';
import { mapGetters } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';

import { datasetApi } from '@/services/api';
import { datasetsRequestV0 } from '@/services/request';

export default {
  name: 'themes-view-warning',

  components: {
    BaseButton,
    RefreshCcwIcon,
  },

  computed: {
    ...mapGetters('datasets', ['areSelectedPendingChanges']),
  },

  props: {
    active: {
      type: Number,
      required: true,
    },
  },

  methods: {
    async onClick() {
      await datasetApi.reanalyse(this.active);
      await datasetsRequestV0.getDatasets();
      await datasetsRequestV0.reloadSelected();
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-view-warning {
  @include flex("block", "row", "start", "center");
  @include rigid;

  background-color: clr("orange");
  color: clr("white");
  padding: 0.4em 1.4em;
  position: sticky;
  top: 0px;
  z-index: 9999;

  .base-button {
    &.size-small {
      padding: 0.3rem 0.5rem;
      margin: 0;
    }

    span {
      color: rgba(clr("white"), 0.6);
      font-size: $font-size-xs;
      transition: all $interaction-transition-time;

      &:hover {
        color: clr("white");
      }

      .base-icon {
        height: $font-size-sm;
        margin-right: 0;
      }
    }
  }

  h3 {
    font-size: $font-size-sm;
    margin: 0;
  }
}
</style>
