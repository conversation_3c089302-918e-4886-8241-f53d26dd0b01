<template>
  <section class="storyteller-settings-modal-metric-dropdown" v-click-outside-handler="{handler: 'onClickOutside'}">
    <section class="item" v-for="(value, key) in availablePredictImprovements" :key="key" @click.stop="onClickItem(key, value)">
      <span>{{value}}</span>
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import clickOutsideHandler from '@/directives/click-outside-handler';

export default {
  name: 'storyteller-settings-modal-metric-dropdown',

  directives: {
    clickOutsideHandler,
  },

  computed: {
    ...mapState('storyteller', ['activeReport']),

    availablePredictImprovements() {
      return this.activeReport.settings.insightSettings.availablePredictImprovements;
    },
  },

  methods: {
    onClickItem(key, value) {
      this.$emit('select', { key, value });
    },

    onClickOutside() {
      this.$emit('close');
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-settings-modal-metric-dropdown {
  @include flex("block", "column", "start", "start");
  @include panel;

  border: 1px solid #7362B7;
  cursor: auto;
  font-size: $font-size-xs;
  left: 0;
  position: absolute;
  top: 32px;
  white-space: nowrap;
  width: calc(100% - 3px);
  z-index: 99;

  .item {
    @include flex("block", "row", "start", "center");

    cursor: pointer;
    padding: 0.5rem;
    width: 100%;

    .text {
      @include truncate;
    }

    &:hover {
      background-color: #f9f8ff;
    }

    &:first-child {
      border-top-left-radius: $border-radius-medium;
      border-top-right-radius: $border-radius-medium;
    }

    &:last-child {
      border-bottom-left-radius: $border-radius-medium;
      border-bottom-right-radius: $border-radius-medium;
    }
  }
}
</style>
