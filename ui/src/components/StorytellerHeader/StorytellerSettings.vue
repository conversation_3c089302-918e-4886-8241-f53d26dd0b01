<template>
  <section class="storyteller-settings" @click="onClickSettings">
    <i class="fa-solid fa-gear icon-gear"></i>
    <span>Settings</span>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import StorytellerSettingsModal from '@/components/StorytellerHeader/StorytellerSettingsModal';

export default {
  name: 'storyteller-settings',

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    onClickSettings() {
      this.setModalComponent({ component: StorytellerSettingsModal });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-settings {
  @include flex("block", "row", "start", "center");

  border-radius: $border-radius-small;
  border: 1px solid rgba(157, 157, 157, 0.4);
  cursor: pointer;
  font-size: $font-size-xxs;
  font-weight: $font-weight-bold;
  margin-left: 1rem;
  padding: 0.3rem 0.6rem;
  text-transform: uppercase;

  .icon-gear {
    font-size: $font-size-xxxs;
    margin-right: 0.2rem;
  }

  &:hover {
    background-color: rgba(19, 28, 41, 0.1);
  }
}
</style>
