<template>
  <section class="storyteller-settings-modal-report-category">
    <span class="title">Report Category</span>
    <section class="report-category-type">
      <section class="report-type" :class="{selected: isReportCx}" @click="selectReportCx">
        <section class="radio"><i class="fa-solid fa-check icon-check" /></section>
        <span>Customer Experience (CX)</span>
      </section>
      <section class="report-type" :class="{selected: isReportEx}" @click="selectReportEx">
        <section class="radio"><i class="fa-solid fa-check icon-check" /></section>
        <span>Employee Experience (EX)</span>
      </section>
      <section class="report-type" :class="{selected: isReportOther}" @click="selectReportOther">
        <section class="radio"><i class="fa-solid fa-check icon-check" /></section>
        <span>Other</span>
      </section>
    </section>
    <section class="report-other" v-if="isReportOther">
      <span class="report-other-label">Please Specify ‘Other’:</span>
      <base-input v-model="activeReport.settings.reportCategoryExtraInfo" :placeholder="'e.g. Patient Experience'" />
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import BaseInput from '@/components/Base/BaseInput';
import StorytellerReportType from '@/enum/storyteller-report-type';

export default {
  name: 'storyteller-settings-modal-report-category',

  components: {
    BaseInput,
  },

  computed: {
    ...mapState('storyteller', ['activeReport']),

    isReportCx() {
      return this.activeReport.settings.reportCategory === StorytellerReportType.CX.value();
    },

    isReportEx() {
      return this.activeReport.settings.reportCategory === StorytellerReportType.EX.value();
    },

    isReportOther() {
      return this.activeReport.settings.reportCategory === StorytellerReportType.OTHER.value();
    },
  },

  methods: {
    selectReportCx() {
      this.activeReport.settings.reportCategory = StorytellerReportType.CX.value();
    },

    selectReportEx() {
      this.activeReport.settings.reportCategory = StorytellerReportType.EX.value();
    },

    selectReportOther() {
      this.activeReport.settings.reportCategory = StorytellerReportType.OTHER.value();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-settings-modal-report-category {
  @include flex("block", "column", "start", "start");

  border-bottom: 1px solid rgba(19, 28, 41, 0.2);
  padding: 1.5rem;
  width: 100%;

  .title {
    color: #2D1757;
    font-size: $font-size-sm;
    font-weight: $font-weight-bold;
    margin-bottom: 1rem;
  }

  .report-category-type {
    display: grid;
    font-size: 11px;
    gap: 0.5rem;
    grid-template-columns: repeat(3, 1fr);
    width: 100%;

    .report-type {
      @include flex("block", "row", "start", "center");

      border-radius: $border-radius-medium;
      border: 1px solid #D9D9D9;
      cursor: pointer;
      font-weight: $font-weight-bold;
      height: 44px;
      padding: 0.5rem 0 0.5rem 0.5rem;

      .radio {
        @include flex("block", "row", "center", "center");

        border-radius: 50%;
        border: 1px solid #D9D9D9;
        height: 1rem;
        margin-right: 0.4rem;
        min-width: 1rem;
        width: 1rem;

        .icon-check {
          font-size: 8px;
          visibility: hidden;
        }
      }

      &:hover {
        border-color: rgba(19, 28,49, 0.8);
      }

      &.selected {
        background-color: #3981F7;
        border-color: #1A5AC3;
        color: #FFF;

        .radio {
          background-color: #FFF;
          border-color: #FFF;
          color: #3981F7;

          .icon-check {
            visibility: visible;
          }
        }
      }
    }
  }

  .report-other {
    margin-top: 1rem;
    width: 100%;
    font-size: $font-size-xs;

    .base-input {
      margin-top: 0.5rem;

      &::placeholder {
        color: #BDBDBD;
      }
    }
  }
}
</style>
