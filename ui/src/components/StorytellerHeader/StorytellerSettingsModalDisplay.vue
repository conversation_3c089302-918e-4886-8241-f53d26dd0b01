<template>
  <section class="storyteller-settings-modal-display">
    <span class="title">Display Settings</span>
    <span class="title-small">Slide Display</span>
    <span class="description">
      Select slide types to display in Storyteller presentation
      <i class="fa-regular fa-arrow-down" />
    </span>
    <section class="settings-list">
      <section v-for="(item, index) in settingsItems"
        :key="index"
        :class="['settings-item', {disabled: item.disabled}]"
        @click="onClickSlide(item)"
      >
        <base-checkbox-solid :disabled="item.disabled" :value="item.value" />
        <section class="text">{{item.text}}</section>
        <img :src="require(`@/assets/storyteller/${item.image}`)" class="slide-image" alt="storyteller-image"/>
      </section>
    </section>
    <span class="title cover">Cover Slide Display</span>
    <section class="footer-settings-list">
      <section class="footer-settings-item left" @click="onClickOrgCoverLogo">
        <section class="logo-info">
          <base-checkbox-solid :value="activeReport.settings.displaySettings.displayOrgCoverLogo" />
          <section class="text">Org Logo</section>
        </section>
        <section class="logo-image">
          <img :src="localOrgLogo" class="org-logo" alt="organisation-logo"/>
        </section>
      </section>
    </section>
    <span class="title footer">Footer Display</span>
    <section class="footer-settings-list">
      <section class="footer-settings-item left" @click="onClickOrgLogo">
        <section class="logo-info">
          <base-checkbox-solid :value="activeReport.settings.displaySettings.displayOrgLogo" />
          <section class="text">Org Logo</section>
        </section>
        <section class="logo-image">
          <img :src="localOrgLogo" class="org-logo" alt="organisation-logo"/>
        </section>
      </section>
      <section class="footer-settings-item right" @click="onClickAdoreboardLogo">
        <section class="logo-info">
          <base-checkbox-solid :value="activeReport.settings.displaySettings.displayAdoreboardLogo" />
          <section class="text">Adoreboard Logo</section>
        </section>
        <section class="logo-image">
          <img :src="require('@/assets/logo/logo-power-by-adoreboard.svg')" class="adoreboard-logo" alt="power-by-logo"/>
        </section>
      </section>
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import BaseCheckboxSolid from '@/components/Base/BaseCheckboxSolid';

export default {
  name: 'storyteller-settings-modal-display',

  components: {
    BaseCheckboxSolid,
  },

  computed: {
    ...mapState('organisation', ['localOrgLogo']),

    ...mapState('storyteller', ['activeReport']),

    disabledFlowSlide() {
      const {
        displayInsightSlide,
        displayDidYouKnowSlide,
        displayImplicationSlide,
        displayCommentSlide,
      } = this.activeReport.settings.displaySettings;

      return !displayInsightSlide || !displayDidYouKnowSlide || !displayImplicationSlide || !displayCommentSlide;
    },

    settingsItems() {
      const {
        displayCoverSlide,
        displayThemesInsightsSlide,
        displayPresentationFlowSlide,
        displayInsightSlide,
        displayDidYouKnowSlide,
        displayImplicationSlide,
        displayCommentSlide,
      } = this.activeReport.settings.displaySettings;

      const toggleSlide = (slide) => {
        this.activeReport.settings.displaySettings[slide] = !this.activeReport.settings.displaySettings[slide];
      };

      return [
        {
          text: 'Cover',
          image: 'storyteller-slide-intro.svg',
          value: displayCoverSlide,
          onClick: () => toggleSlide('displayCoverSlide'),
        },
        {
          text: 'Scorecard',
          image: 'storyteller-slide-scorecard.svg',
          value: displayThemesInsightsSlide,
          onClick: () => toggleSlide('displayThemesInsightsSlide'),
        },
        {
          text: 'Presentation Flow',
          image: 'storyteller-slide-flow.svg',
          value: displayPresentationFlowSlide,
          disabled: this.disabledFlowSlide,
          onClick: () => toggleSlide('displayPresentationFlowSlide'),
        },
        {
          text: 'Insight',
          image: 'storyteller-slide-insight.svg',
          value: displayInsightSlide,
          onClick: () => toggleSlide('displayInsightSlide'),
        },
        {
          text: 'Did you know?',
          image: 'storyteller-slide-did-you-know.svg',
          value: displayDidYouKnowSlide,
          onClick: () => toggleSlide('displayDidYouKnowSlide'),
        },
        {
          text: 'Implication',
          image: 'storyteller-slide-implication.svg',
          value: displayImplicationSlide,
          onClick: () => toggleSlide('displayImplicationSlide'),
        },
        {
          text: 'Comment(s)',
          image: 'storyteller-slide-comment.svg',
          value: displayCommentSlide,
          onClick: () => toggleSlide('displayCommentSlide'),
        },
      ];
    },
  },

  methods: {
    onClickAdoreboardLogo() {
      this.activeReport.settings.displaySettings.displayAdoreboardLogo = !this.activeReport.settings.displaySettings.displayAdoreboardLogo;
    },

    onClickOrgLogo() {
      this.activeReport.settings.displaySettings.displayOrgLogo = !this.activeReport.settings.displaySettings.displayOrgLogo;
    },

    onClickOrgCoverLogo() {
      this.activeReport.settings.displaySettings.displayOrgCoverLogo = !this.activeReport.settings.displaySettings.displayOrgCoverLogo;
    },

    onClickSlide(item) {
      if (item.disabled) return;
      item.onClick();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-settings-modal-display {
  @include flex("block", "column", "start", "start");

  padding: 1.5rem;
  width: 100%;

  .title {
    color: #2D1757;
    font-size: $font-size-sm;
    font-weight: $font-weight-bold;
    margin-bottom: 1rem;

    &.cover, &.footer {
      padding-top: 1.5rem;
    }
  }

  .description {
    color: rgba(19, 28, 41, 0.56);
    font-size: 12.5px;
    font-weight: $font-weight-medium;
    line-height: 23px;
    margin-bottom: 1rem;
  }

  .title-small {
    color: #2E2D8D;
    font-size: 13px;
    margin-bottom: 1rem;
  }

  .settings-list {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    width: 100%;

    .settings-item {
      @include flex("block", "row", "start", "start");

      background-color: rgba(244, 244, 244, 0.5);
      border-radius: 3px;
      border: 1px solid #F4F4F4;
      cursor: pointer;
      height: 135px;
      padding-top: 1rem;
      position: relative;
      width: calc(50% - 0.5rem);

      &.disabled {
        cursor: not-allowed;
      }

      .base-checkbox-solid {
        margin-left: 1rem;
        margin-right: 0.6rem;
      }

      .text {
        padding-top: 2px;
        font-size: 11px;
      }

      .slide-image {
        bottom: 0;
        position: absolute;
        right: 0;
        width: 155px;
      }
    }
  }

  .footer-settings-list {
    @include flex("block", "row", "start", "start");

    width: 100%;

    .footer-settings-item {
      @include flex("block", "column", "start", "start");

      cursor: pointer;
      width: 200px;

      .logo-info {
        @include flex("block", "row", "start", "start");

        .base-checkbox-solid {
          margin-right: 0.6rem;
        }

        .text {
          padding-top: 2px;
          font-size: 11px;
        }
      }

      .logo-image {
        @include flex("block", "row", "center", "center");

        background-color: rgba(244, 244, 244, 0.5);
        border-radius: 3px;
        border: 1px solid #F4F4F4;
        height: 60px;
        margin-top: 1rem;
        width: 100%;

        .org-logo {
          max-height: 50px;
          max-width: 150px;
        }
      }

      &.left {
        margin-right: 1.5rem;
      }
    }
  }
}
</style>
