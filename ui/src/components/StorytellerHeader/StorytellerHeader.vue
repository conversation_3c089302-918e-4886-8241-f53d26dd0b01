<template>
  <section class="storyteller-header">
    <storyteller-header-left />
    <storyteller-header-right />
  </section>
</template>

<script>
import StorytellerHeaderLeft from '@/components/StorytellerHeaderLeft/StorytellerHeaderLeft';
import Storyteller<PERSON>eaderRight from '@/components/StorytellerHeader/StorytellerHeaderRight';

export default {
  name: 'storyteller-header',

  components: {
    StorytellerHeaderLeft,
    StorytellerHeaderRight,
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-header {
  @include flex("block", "row", "space-between", "center");

  border-bottom: 1px solid #E1E1E1;
  height: $dataset-storyteller-selector-height;
  min-height: $dataset-storyteller-selector-height;
  padding: 0 1.5rem;
  width: 100%;
}
</style>
