<template>
  <section class="storyteller-settings-modal-metric">
    <span class="title">Metric Settings</span>
    <span class="description">Select Presentation Metric</span>
    <section class="input" @click.stop="open = !open" >
      <base-input :readonly=true class="input-dropdown" v-model="selectedMetric"/>
      <section class="dropdown-icon" :class="{ open }" >
        <i class="fa fa-caret-down icon-dropdown" :class="{ open }" />
      </section>
      <storyteller-settings-modal-metric-dropdown v-if="open" @select="select" @close="open = false" />
    </section>
    <span class="description name">Metric Name</span>
    <section class="input name">
      <base-input :value="selectedMetricDisplay" @input="onDisplayInput"/>
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import BaseInput from '@/components/Base/BaseInput';
import StorytellerSettingsModalMetricDropdown from '@/components/StorytellerHeader/StorytellerSettingsModalMetricDropdown';

export default {
  name: 'storyteller-settings-modal-metric',

  components: {
    BaseInput,
    StorytellerSettingsModalMetricDropdown,
  },

  data() {
    return {
      open: false,
    };
  },

  computed: {
    ...mapState('storyteller', ['activeReport']),

    availablePredictImprovements() {
      return this.activeReport.settings.insightSettings.availablePredictImprovements;
    },

    displayPredictImprovements() {
      return this.activeReport.settings.insightSettings.displayPredictImprovements;
    },

    selectedMetric() {
      return this.availablePredictImprovements[this.selectedMetricIndex];
    },

    selectedMetricDisplay() {
      return this.displayPredictImprovements[this.selectedMetricIndex];
    },

    selectedMetricIndex() {
      const { predictImprovementBy } = this.activeReport.settings.insightSettings;
      return Object.keys(predictImprovementBy)[0];
    },
  },

  methods: {
    onDisplayInput(value) {
      this.activeReport.settings.insightSettings.displayPredictImprovements[this.selectedMetricIndex] = value;
    },

    select({ key, value }) {
      this.activeReport.settings.insightSettings.predictImprovementBy = { [key]: value };
      this.open = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-settings-modal-metric {
  @include flex("block", "column", "start", "start");

  border-bottom: 1px solid rgba(19, 28, 41, 0.2);
  padding: 1.5rem;
  width: 100%;

  .title {
    color: #2D1757;
    font-size: $font-size-sm;
    font-weight: $font-weight-bold;
    margin-bottom: 1rem;
  }

  .description {
    color: rgba(19, 28, 41, 0.56);
    font-size: 12.5px;
    font-weight: $font-weight-medium;
    line-height: 23px;

    &.name {
      margin-top: 1rem;
    }
  }

  .input {
    @include flex('inline', 'row', 'start', 'center');

    height: 1.75rem;
    margin-top: 0.6rem;
    position: relative;
    width: 200px;

    &.name {
      width: 197px;
    }

    .base-input {
      @include flex('inline', 'row', 'start', 'center');

      background-color: clr("white");
      border: 1px solid #7362B7;
      font-size: $font-size-xs;
      height: 100%;
      padding: 0.5rem;

      &.input-dropdown {
        border-radius: $border-radius-medium 0 0 $border-radius-medium;
        border-right: none;
        cursor: pointer;
      }
    }

    .dropdown-icon {
      @include panel;
      @include flex('inline', 'row', 'start', 'center');

      border-radius: $border-radius-medium;
      border: 1px solid #7362B7;
      box-shadow: unset;
      cursor: pointer;
      font-size: $font-size-xs;
      height: 100%;
      left: -3px;
      padding-left: 0.6rem;
      position: relative;
      width: 2.1rem;

      .icon-dropdown {
        transition: all $interaction-transition-time;

        &.open {
          transform: rotate(180deg);
        }
      }
    }
  }
}
</style>
