<template>
  <section class="storyteller-settings-modal">
    <section class="header">
      <i class="fa-solid fa-gear icon-settings" />
      <span class="text">Presentation Settings</span>
      <storyteller-report-tag />
    </section>
    <section class="body">
      <section class="panel-left">
        <span class="setting-item" :class="{active: activeReportCategory}" @click="scrollToReportCategory">Report Category</span>
        <span class="setting-item" :class="{active: activeMetric}" v-if="availablePredictImprovements" @click="scrollToMetric">Metric Settings</span>
        <span class="setting-item" :class="{active: activeDisplay}" @click="scrollToDisplay">Display Settings</span>
      </section>
      <section class="panel-right" @scroll="handleScroll" ref="panelRight">
        <storyteller-settings-modal-report-category ref="reportCategory" />
        <storyteller-settings-modal-metric v-if="availablePredictImprovements" ref="metric" />
        <storyteller-settings-modal-display ref="display" />
      </section>
    </section>
    <section class="footer">
      <base-button class="cancel-btn" colour="light" size="small" type="link" @click="onClickCancel">Cancel</base-button>
      <loading-blocks-overlay v-if="saving" size="small" />
      <base-button v-else class="done-btn" size="small" @click="onClickDone">
        <span>Done</span>
      </base-button>
    </section>
  </section>
</template>

<script>
import { cloneDeep } from 'lodash-es';
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseCheckboxSolid from '@/components/Base/BaseCheckboxSolid';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import StorytellerReportTag from '@/components/Storyteller/StorytellerReportTag';
import StorytellerRequest from '@/services/request/StorytellerRequest';
import StorytellerSettingsModalDisplay from '@/components/StorytellerHeader/StorytellerSettingsModalDisplay';
import StorytellerSettingsModalMetric from '@/components/StorytellerHeader/StorytellerSettingsModalMetric';
import StorytellerSettingsModalReportCategory from '@/components/StorytellerHeader/StorytellerSettingsModalReportCategory';

export default {
  name: 'storyteller-settings-modal',

  components: {
    BaseButton,
    BaseCheckboxSolid,
    LoadingBlocksOverlay,
    StorytellerReportTag,
    StorytellerSettingsModalDisplay,
    StorytellerSettingsModalMetric,
    StorytellerSettingsModalReportCategory,
  },

  data() {
    return {
      displayTop: 0,
      initialSettings: {},
      metricTop: 0,
      reportCategoryTop: 0,
      saving: false,
      scrollPosition: 0,
    };
  },

  computed: {
    ...mapState('storyteller', ['activeReport']),

    activeDisplay() {
      return this.scrollPosition >= this.displayTop;
    },

    activeMetric() {
      return this.scrollPosition >= this.metricTop && this.scrollPosition < this.displayTop;
    },

    activeReportCategory() {
      return this.scrollPosition >= this.reportCategoryTop && this.scrollPosition < this.metricTop;
    },

    availablePredictImprovements() {
      return this.activeReport.settings.insightSettings.availablePredictImprovements;
    },
  },

  mounted() {
    this.reportCategoryTop = this.$refs.reportCategory.$el.offsetTop - this.$refs.panelRight.offsetTop;
    if (this.availablePredictImprovements) this.metricTop = this.$refs.metric.$el.offsetTop - this.$refs.panelRight.offsetTop;
    this.displayTop = this.$refs.display.$el.offsetTop - this.$refs.panelRight.offsetTop;
  },

  created() {
    this.initialSettings = cloneDeep(this.activeReport.settings);
  },

  destroyed() {
    this.activeReport.settings = cloneDeep(this.initialSettings);
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    handleScroll(event) {
      this.scrollPosition = event.target.scrollTop;
    },

    onClickCancel() {
      this.closeModal();
    },

    async onClickDone() {
      this.saving = true;
      await StorytellerRequest.updateReportSettings();
      this.initialSettings = cloneDeep(this.activeReport.settings);
      this.saving = false;
      this.closeModal();
    },

    scrollToDisplay() {
      const scrollTo = this.$refs.display.$el.offsetTop - this.$refs.panelRight.offsetTop;

      this.$refs.panelRight.scrollTo({
        top: scrollTo,
        behavior: 'smooth',
      });
    },

    scrollToMetric() {
      const scrollTo = this.$refs.metric.$el.offsetTop - this.$refs.panelRight.offsetTop;

      this.$refs.panelRight.scrollTo({
        top: scrollTo,
        behavior: 'smooth',
      });
    },

    scrollToReportCategory() {
      const scrollTo = this.$refs.reportCategory.$el.offsetTop - this.$refs.panelRight.offsetTop;

      this.$refs.panelRight.scrollTo({
        top: scrollTo,
        behavior: 'smooth',
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-settings-modal {
  @include panel;

  color: $nps-blue;
  position: relative;
  width: 650px;

  .header {
    @include flex("block", "row", "start", "center");
    @include truncate;

    border-bottom: 1px solid rgba(19, 28, 41, 0.2);
    font-size: $font-size-sm;
    font-weight: $font-weight-bold;
    padding: 1rem 1.5rem;
    width: 100%;

    .icon-settings {
      margin-right: 0.4rem;
    }

    .text {
      margin-right: 0.8rem;
    }
  }

  .body {
    display: grid;
    grid-template-columns: 26% 74%;
    width: 100%;

    .panel-left {
      @include flex("block", "column", "start", "start");

      border-right: 1px solid rgba(19, 28, 41, 0.2);
      font-size: $font-size-xs;
      padding-left: 1.5rem;
      padding-top: 1.5rem;

      .setting-item {
        cursor: pointer;
        margin-bottom: 1.5rem;

        &.active {
          color: #3858FF;
        }
      }
    }

    .panel-right {
      @include flex("block", "column", "start", "start");
      @include scrollbar-thin;

      height: 400px;
      overflow-y: auto;
    }
  }

  .footer {
    @include flex("block", "row", "space-between", "center");

    border-top: 1px solid rgba(19, 28, 41, 0.2);
    padding: 1rem 1.5rem;
    width: 100%;

    .base-button {
      font-size: $font-size-xxs;
      font-weight: 600;
      text-transform: uppercase;

      &.cancel-btn {
        padding-left: 0;
      }

      &.done-btn {
        background-color: #2D1757;
        padding: 0.5rem 0.8rem;
        width: 90px;
      }

      &:hover, &:focus {
        &.done-btn {
          background-color: rgba(19, 28, 41);
        }
      }
    }
  }
}
</style>
