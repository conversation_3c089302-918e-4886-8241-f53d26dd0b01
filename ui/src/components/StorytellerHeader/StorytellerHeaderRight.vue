<template>
  <section class="storyteller-header-right">
    <storyteller-var-btn />
    <storyteller-settings v-if="isEditor" />
  </section>
</template>

<script>
import { mapGetters, mapState } from 'vuex';

import StorytellerSettings from '@/components/StorytellerHeader/StorytellerSettings';
import StorytellerVarBtn from '@/components/StorytellerHeader/StorytellerVarBtn';

export default {
  name: 'storyteller-header-right',

  components: {
    StorytellerSettings,
    StorytellerVarBtn,
  },

  computed: {
    ...mapGetters('datasets', ['get', 'isEditable']),

    ...mapState('datasets', {
      datasetId: 'active',
    }),

    isEditor() {
      return this.isEditable(this.datasetId);
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-header-right {
  @include flex("block", "row", "end", "center");
}
</style>
