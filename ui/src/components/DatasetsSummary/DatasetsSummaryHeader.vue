<template>
  <section class="datasets-summary-header">
    <section class="header-left">
      <i class="fa-regular fa-wand-magic-sparkles icon-magic" />
      <span class="title">Adoreboard AI</span>
    </section>
    <section class="icon-wrapper" @click="onClickClose">
      <i class="fa-regular fa-xmark icon-x" />
    </section>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

export default {
  name: 'datasets-summary-header',

  methods: {
    ...mapActions('layout', ['setShowContainerDatasetSummary']),

    onClickClose() {
      this.setShowContainerDatasetSummary({ value: false });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.datasets-summary-header {
  @include flex("block", "row", "start", "center");

  border-bottom: 1px solid rgba(114, 86, 173, 0.2);
  padding: 1.2rem 35px;
  width: 100%;

  .header-left {
    @include flex("block", "row", "start", "center");

    background: linear-gradient(180deg, #A43CD1 0%, #705FED 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 13px;

    .icon-magic {
      margin-right: 0.4rem;
    }

    .title {
      font-weight: $font-weight-bold;
    }
  }

  .icon-wrapper {
    @include flex("block", "row", "center", "center");

    border-radius: 50%;
    cursor: pointer;
    height: 20px;
    margin-left: auto;
    width: 20px;

    &:hover {
      background-color: rgba(40, 30, 81, 0.3);
    }

    .icon-x {
      color: #281E51;
      cursor: pointer;
      font-size: $font-size-base;
    }
  }
}
</style>
