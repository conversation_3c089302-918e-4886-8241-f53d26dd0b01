<template>
  <section class="datasets-summary-content">
    <loading-blocks-overlay v-if="loading" />
    <section v-show="!loading" class="content">
      <section class="title">Summary of ‘{{dataset.label}}’</section>
      <section class="intro">{{summary.intro}}</section>
      <section class="title key-points">
        <i class="fa-solid fa-sparkles" />
        <span>Key Points</span>
      </section>
      <section class="key-point" v-for="(item, index) in summary.keyPoints" :key="index">
        <span class="dot-icon">•</span>
        <span>{{item}}</span>
      </section>
      <section class="control-row">
        <base-button v-if="isEditor" class="new-report-btn" size="small" @click="onClickNewReport">
          <i class="fa-regular fa-plus icon-plus"></i>
          New Report
        </base-button>
        <section class="regenerate-btn" @click="onClickRegenerate">
          <section class="icon-wrapper">
            <i class="fa-regular fa-arrows-rotate icon-regenerate" />
          </section>
          <section class="text-wrapper">
            <span class="text">Regenerate</span>
            <span class="text">Summary</span>
          </section>
        </section>
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import DatasetsItemStorytellerNewReportModal from '@/components/DatasetsItemStoryteller/DatasetsItemStorytellerNewReportModal';
import DatasetsRequest from '@/services/request/DatasetsRequest';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import NetworkKeys from '@/enum/network-keys';

export default {
  name: 'datasets-summary-content',

  components: {
    BaseButton,
    LoadingBlocksOverlay,
  },

  data() {
    return {
      summary: {
        intro: null,
        keyPoints: [],
      },
    };
  },

  computed: {
    ...mapGetters('datasets', ['get', 'isEditable']),

    ...mapState('datasets', ['summaryId']),

    ...mapGetters('network', ['isLoading']),

    dataset() {
      return this.get(this.summaryId);
    },

    loading() {
      return this.isLoading(NetworkKeys.DATASETS_SUMMARY);
    },

    isEditor() {
      return this.isEditable(this.summaryId);
    },
  },

  created() {
    this.fetchSummary();
  },

  watch: {
    summaryId() {
      if (this.summaryId) this.fetchSummary();
    },
  },

  methods: {
    ...mapActions('modal', ['setModalComponentAndProps']),

    async fetchSummary() {
      const res = await DatasetsRequest.fetchSummary(this.summaryId);
      this.summary = res.summary;
    },

    onClickNewReport() {
      this.setModalComponentAndProps({
        component: DatasetsItemStorytellerNewReportModal,
        props: {
          datasetId: this.summaryId,
        },
      });
    },

    async onClickRegenerate() {
      const res = await DatasetsRequest.regenerateSummary(this.summaryId);
      this.summary = res.summary;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.datasets-summary-content {
  @include flex("block", "column", "center", "center");

  padding: 1.75rem 35px;
  width: 100%;

  .content {
    width: 100%;
  }

  .title {
    font-size: $font-size-sm;
    font-weight: $font-weight-bold;

    &.key-points {
      background: linear-gradient(180deg, #A43CD1 0%, #705FED 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-size: 12px;
      margin-bottom: 10px;
      margin-top: 1.3rem;
    }
  }

  .intro {
    font-size: $font-size-xs;
    font-weight: 350;
    line-height: 130%;
    margin-top: 0.4rem;
  }

  .key-point {
    @include flex("block", "row", "start", "start");

    font-size: $font-size-xs;
    font-weight: 350;
    line-height: 130%;
    margin-top: 0.4rem;

    .dot-icon {
      margin-right: 0.4rem;
    }
  }

  .control-row {
    @include flex("block", "row", "between", "center");
    margin-top: 30px;

    .new-report-btn {
      background-color: #3981F7;
      border: 1px solid #2269DC;
      font-size: $font-size-xxs;
      font-weight: $font-weight-bold;
      height: 24px;
      text-transform: uppercase;

      .icon-plus {
        margin-right: 0.2rem;
        padding-bottom: 2px;
      }

      &:hover, &:focus {
        background-color: rgba(57, 129, 247, 0.7);
      }
    }

    .regenerate-btn {
      @include flex("block", "row", "center", "center");
      border-radius: 15px;
      cursor: pointer;
      height: 25px;
      padding-right: 8px;

      &:hover {
        background-color: rgba(147, 74, 219, 0.1);

        .icon-wrapper {
          background-color: rgba(99, 67, 164, 1);

          .icon-regenerate {
            color: #FFFFFF;
          }
        }
      }

      .icon-wrapper {
        @include flex("block", "row", "center", "center");
        border: solid 1px rgba(62, 53, 98, 1);
        border-radius: 50%;
        height: 24px;
        width: 24px;

        .icon-regenerate {
          font-size: 12px;
          color: rgba(62, 53, 98, 1);
          padding-left: 1px;
        }
      }

      .text-wrapper {
        @include flex("block", "column", "center", "start");
        margin-left: 5px;

        .text {
          font-size: 10px;
          font-weight: 600;
        }
      }
    }
  }
}
</style>
