<template>
  <section class="datasets-summary">
    <section class="dataset-summary-wrapper">
      <datasets-summary-header />
      <datasets-summary-content />
      <icon-collapse class="collapse-btn" @click.stop="onClickCollapse" />
    </section>
  </section>
</template>

<script>
import DatasetsSummaryContent from '@/components/DatasetsSummary/DatasetsSummaryContent';
import DatasetsSummaryHeader from '@/components/DatasetsSummary/DatasetsSummaryHeader';
import IconCollapse from '@/components/Icons/IconCollapse';
import { mapActions } from 'vuex';

export default {
  name: 'datasets-summary',

  components: {
    DatasetsSummaryContent,
    DatasetsSummaryHeader,
    IconCollapse,
  },

  methods: {
    ...mapActions('layout', ['setShowContainerDatasetSummary']),

    onClickCollapse() {
      this.setShowContainerDatasetSummary({ value: false });
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.datasets-summary {
  @include flex("block", "column", "start", "start");

  background-color: clr("white");
  border-bottom-left-radius: 5px;
  border-top-left-radius: 5px;
  border: 1.5px solid #8F4CDD;
  box-shadow: 0 0 7px 0 rgba(0, 0, 0, 0.35);
  height: calc(100vh - #{$header-height});
  margin-top: $header-height;
  transition: all $interaction-transition-time;
  width: 500px;

  .dataset-summary-wrapper {
    background: radial-gradient(36.01% 46.87% at 92.42% 5.77%, rgba(201, 153, 255, 0.4) 0%, rgba(255, 255, 255, 0.4) 100%);
    height: 100%;
    position: relative;
    width: 100%;

    .collapse-btn {
      left: -9px;
      position: absolute;
      top: 86px;
      z-index: 99;
      rotate: 90deg;
    }
  }
}
</style>
