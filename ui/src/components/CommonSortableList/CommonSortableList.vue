<script>
import { Sortable } from '@shopify/draggable';

export default {
  name: 'Common-Sortable-List',

  props: {
    itemClass: {
      type: String,
      default: 'sortable-item',
    },
    handleClass: {
      type: String,
      default: 'sortable-handle',
    },
  },

  provide() {
    return {
      itemClass: this.itemClass,
      handleClass: this.handleClass,
    };
  },

  render() {
    return this.$slots.default[0];
  },

  mounted() {
    // eslint-disable-next-line
    new Sortable(this.$el, {
      draggable: `.${this.itemClass}`,
      handle: `.${this.handleClass}`,
      mirror: {
        constrainDimensions: true,
      },
    }).on('sortable:stop', ({ oldIndex, newIndex }) => {
      this.$emit('sorting', { oldIndex, newIndex });
    });
  },
};
</script>
