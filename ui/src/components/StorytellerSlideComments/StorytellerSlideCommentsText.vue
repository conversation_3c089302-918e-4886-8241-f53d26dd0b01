<template>
  <section class="storyteller-slide-comments-text">
    <section v-if="commentList.length === 1" class="single-content">
      <storyteller-slide-comments-text-item :comment="comment1" :hovered="hovered" :editable="editable" :max-height="maxHeight" @stopEditing="stopEditingSingle" />
    </section>
    <section v-else-if="commentList.length === 2" class="double-content">
      <storyteller-slide-comments-text-item :comment="comment1" :hovered="hovered" :editable="editable" :max-height="maxHeight" @stopEditing="stopEditingDouble1" />
      <storyteller-slide-comments-text-item :comment="comment2" :hovered="hovered" :editable="editable" :max-height="maxHeight" @stopEditing="stopEditingDouble2" />
    </section>
    <section v-else-if="commentList.length === 3" class="triple-content">
      <storyteller-slide-comments-text-item :comment="comment1" :hovered="hovered" :editable="editable" :max-height="maxHeight" @stopEditing="stopEditingTriple1" />
      <storyteller-slide-comments-text-item :comment="comment2" :hovered="hovered" :editable="editable" :max-height="maxHeight" @stopEditing="stopEditingTriple2" />
      <storyteller-slide-comments-text-item :comment="comment3" :hovered="hovered" :editable="editable" :max-height="maxHeight" @stopEditing="stopEditingTriple3" />
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import StorytellerSlideCommentsTextItem from '@/components/StorytellerSlideComments/StorytellerSlideCommentsTextItem';
import StorytellerSlideRequest from '@/services/request/StorytellerSlideRequest';

export default {
  name: 'storyteller-slide-comments-text',

  components: {
    StorytellerSlideCommentsTextItem,
  },

  props: {
    hovered: {
      type: Boolean,
      required: true,
    },
    editable: {
      type: Boolean,
      default: false,
    },
    maxHeight: {
      type: String,
      required: true,
    },
    slideData: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapState('storyteller', ['activeReport', 'selectedSlide']),

    commentList() {
      const extractComments = (text) => {
        const regex = /<comment>([\s\S]*?)<\/comment>/g;
        const matches = text.matchAll(regex);
        return Array.from(matches, match => {
          return {
            text: match[1],
          };
        });
      };

      const comments = extractComments(this.slideData.text);

      if (this.displayMetadata) {
        for (let i = 0; i < this.slideData.commentSlideData.selectedComments.length; i += 1) {
          comments[i].metadata = this.slideData.commentSlideData.selectedComments[i].metadata;
        }
      }

      return comments;
    },

    displayMetadata() {
      return this.activeReport.settings.commentSettings.displayMetadata;
    },

    comment1() {
      return this.commentList[0];
    },

    comment2() {
      return this.commentList[1];
    },

    comment3() {
      return this.commentList[2];
    },
  },

  methods: {
    stopEditingDouble1(content) {
      this.selectedSlide.slideData.text = `<comment>${content}</comment><comment>${this.comment2.text}</comment>`;
      StorytellerSlideRequest.updateSlide();
    },

    stopEditingDouble2(content) {
      this.selectedSlide.slideData.text = `<comment>${this.comment1.text}</comment><comment>${content}</comment>`;
      StorytellerSlideRequest.updateSlide();
    },

    stopEditingSingle(content) {
      this.selectedSlide.slideData.text = `<comment>${content}</comment>`;
      StorytellerSlideRequest.updateSlide();
    },

    stopEditingTriple1(content) {
      this.selectedSlide.slideData.text = `<comment>${content}</comment><comment>${this.comment2.text}</comment><comment>${this.comment3.text}</comment>`;
      StorytellerSlideRequest.updateSlide();
    },

    stopEditingTriple2(content) {
      this.selectedSlide.slideData.text = `<comment>${this.comment1.text}</comment><comment>${content}</comment><comment>${this.comment3.text}</comment>`;
      StorytellerSlideRequest.updateSlide();
    },

    stopEditingTriple3(content) {
      this.selectedSlide.slideData.text = `<comment>${this.comment1.text}</comment><comment>${this.comment2.text}</comment><comment>${content}</comment>`;
      StorytellerSlideRequest.updateSlide();
    },
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-comments-text {
  @include flex("block", "row", "center", "center");

  height: 100%;
  overflow: visible;
  padding: 0.8em;
  width: 100%;

  .single-content {
    @include flex("block", "column", "center", "center");

    font-size: 1.1em;
    line-height: 1.3em;
    text-align: center;
    width: 80%;
  }

  .double-content {
    align-items: start;
    display: grid;
    font-size: 0.8em;
    grid-gap: 6%;
    grid-template-columns: 47% 47%;
    line-height: 1.3em;
    width: 100%;
  }

  .triple-content {
    align-items: start;
    display: grid;
    font-size: 0.7em;
    grid-gap: 5%;
    grid-template-columns: 30% 30% 30%;
    line-height: 1.3em;
    width: 100%;
  }
}
</style>
