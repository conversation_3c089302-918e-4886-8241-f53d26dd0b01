<template>
  <section class="storyteller-slide-comments" :style="{fontSize}" @mouseenter="hovered = true" @mouseleave="hovered = false">
    <section class="margin-top" />
    <section class="body">
      <section class="title-header">
        <span class="header">{{slideData.header}}</span>
        <storyteller-slide-setting-btn v-if="editable"
          :icon="'fa-solid fa-quote-left icon-quote'"
          :text="'Select Comments'"
          @click="onClickQuote"
        />
        <storyteller-slide-setting-btn v-if="editable && showSettings"
          class="settings"
          :icon="'fa-solid fa-gear icon-setting'"
          :text="'Comments Settings'"
          @click="onClickSettings"
        />
      </section>
      <storyteller-slide-comments-text :max-height="maxHeightText" :hovered="hovered" :editable="editable" :slide-data="slideData" />
    </section>
    <storyteller-slide-footer :slide-width="slideWidth" />
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import StorytellerSlideCommentsSettingModal from '@/components/StorytellerSlideComments/StorytellerSlideCommentsSettingModal';
import StorytellerSlideCommentsText from '@/components/StorytellerSlideComments/StorytellerSlideCommentsText';
import StorytellerSlideFooter from '@/components/StorytellerSlides/StorytellerSlideFooter';
import StorytellerSlideSettingBtn from '@/components/Storyteller/StorytellerSlideSettingBtn';

export default {
  name: 'storyteller-slide-comments',

  components: {
    StorytellerSlideCommentsText,
    StorytellerSlideFooter,
    StorytellerSlideSettingBtn,
  },

  props: {
    editable: {
      type: Boolean,
      default: false,
    },
    slideData: {
      type: Object,
      required: true,
    },
    slideWidth: {
      type: Number,
      required: true,
    },
  },

  data() {
    return {
      hovered: false,
    };
  },

  computed: {
    ...mapState('datasets', ['metadata']),

    fontSize() {
      return `${50 * this.ratio}px`;
    },

    maxHeightText() {
      return `${this.slideWidth * 0.4}px`;
    },

    ratio() {
      return this.slideWidth / 1920;
    },

    showSettings() {
      return this.metadata.length > 0;
    },
  },

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    ...mapActions('storyteller', ['setSelectingCommentsView']),

    onClickQuote() {
      this.setSelectingCommentsView({ selectingCommentsView: true });
    },

    onClickSettings() {
      this.setModalComponent({ component: StorytellerSlideCommentsSettingModal });
    },
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-comments {
  background-color: #FFF;
  color: $nps-blue;
  display: grid;
  font-family: Inter, serif;
  font-weight: $font-weight-semi-light;
  grid-template-rows: 13% 74% 13%;
  height: 100%;
  padding: 0 2em;
  width: 100%;

  &:hover {
    .body .title-header .storyteller-slide-setting-btn {
      display: flex;
    }
  }

  strong {
    font-weight: 600;
  }

  .comments-settings {
    position: absolute;
    top: 0;
    left: 0;
    visibility: hidden;
  }

  .body {
    @include flex("block", "column", "start", "start");

    height: 100%;
    width: 100%;

    .title-header {
      @include flex("block", "row", "start", "center");

      position: relative;
      width: fit-content;

      .text-header {
        font-size: 0.5em;
        font-weight: 800;
        letter-spacing: 0.04em;
        opacity: 0.5;
        text-transform: uppercase;
      }

      .storyteller-slide-setting-btn {
        display: none;
        position: absolute;
        right: -1.5em;

        &.settings {
          right: -3em;
        }
      }
    }

    .header {
      @include flex("block", "row", "start", "end");

      font-size: 0.5em;
      font-weight: 800;
      letter-spacing: 0.04em;
      opacity: 0.5;
      text-transform: uppercase;
    }
  }

  &:hover {
    .comments-settings {
      visibility: visible;
    }
  }
}
</style>
