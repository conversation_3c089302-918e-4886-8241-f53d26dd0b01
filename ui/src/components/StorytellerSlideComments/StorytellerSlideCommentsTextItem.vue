<template>
  <section class="storyteller-slide-comments-text-item" :class="{editable, hovered}">
    <storyteller-text-tiptap v-if="editing" :init-content="comment.text" :max-characters="400" :max-height="maxHeight" @blur="stopEditing"/>
    <section class="description" v-if="!editing">
      <span class="text" v-html="formatText" @dblclick="onClickEdit" />
      <span class="metadata" v-if="displayMetadata && comment.metadata">{{comment.metadata}}</span>
    </section>
    <section class="icons" v-if="!editing && editable">
      <section class="icon-wrapper edit" @click.stop="onClickEdit"
        v-tooltip.top="{
          content: 'Edit',
          class: 'tooltip-base-dark',
          delay: 0,
        }"
      >
        <i class="fa-light fa-pen icon" />
      </section>
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import StorytellerTextTiptap from '@/components/StorytellerSlideText/StorytellerTextTiptap';

export default {
  name: 'storyteller-slide-comments-text-item',

  components: {
    StorytellerTextTiptap,
  },

  props: {
    comment: {
      type: Object,
      required: true,
    },
    hovered: {
      type: Boolean,
      required: true,
    },
    editable: {
      type: Boolean,
      default: false,
    },
    maxHeight: {
      type: String,
      required: true,
    },
  },

  data() {
    return {
      editing: false,
    };
  },

  computed: {
    ...mapState('storyteller', ['activeReport']),

    displayMetadata() {
      return this.activeReport.settings.commentSettings.displayMetadata;
    },

    formatText() {
      const leftQuote = '<p><span class="quote">&ldquo;</span>';
      const rightQuote = '<span class="quote quote-right">&rdquo;</span></p>';
      return `${leftQuote}${this.truncatedText}${rightQuote}`;
    },

    truncatedText() {
      // TODO: this only temporary solution, we should find a way to shorten paragraph when it's overflow y axis
      const maxChars = this.displayMetadata && this.comment.metadata ? 340 : 400;
      return this.comment.text.length > maxChars ? `${this.comment.text.substring(0, maxChars)}...` : this.comment.text;
    },
  },

  methods: {
    onClickEdit() {
      this.editing = true;
    },

    stopEditing(content) {
      this.editing = false;
      if (content.startsWith('<p>') && content.endsWith('</p>')) {
        content = content.slice(3, -4);
      }
      if (content !== this.comment.text) {
        this.$emit('stopEditing', content);
      }
    },
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-comments-text-item {
  @include flex("block", "row", "center", "center");

  position: relative;

  p {
    font-style: italic;
    position: relative;
  }

  .description {
    display: flex;
    flex-direction: column;

    .metadata {
      font-size: 0.6em;
      font-weight: $font-weight-bold;
      opacity: 0.66;
    }
  }

  .quote {
    -webkit-text-fill-color: white;
    -webkit-text-stroke: 1px black;
    font-family: Helvetica, sans-serif;
    font-size: 1.6em;
    font-weight: $font-weight-extra-bold;
    font-style: normal;

    &.quote-right {
      position: absolute;
      bottom: -0.5em;
    }
  }

  .icons {
    @include flex("block", "row", "center", "center");

    font-size: 1em;
    height: 100%;
    position: absolute;
    right: -1.2em;
    visibility: hidden;
  }

  .icon-wrapper {
    @include flex("block", "row", "center", "center");

    border-radius: 50%;
    border: 1px solid #3981F7;
    color: #3981F7;
    cursor: pointer;
    height: 1em;
    position: relative;
    width: 1em;

    .icon {
      font-size: 0.5em;
    }

    &:hover {
      background-color: rgba(57, 129, 247, 0.3);
    }
  }

  .storyteller-text-tiptap {
    font-style: italic;
    text-align: center;
  }

  &.editable {
    &.hovered {
      .icons {
        visibility: visible;
      }
    }
  }
}
</style>
