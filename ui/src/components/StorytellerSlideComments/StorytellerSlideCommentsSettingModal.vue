<template>
  <section class="storyteller-slide-comments-setting-modal">
    <section class="header">
      <i class="fa-solid fa-gear icon-settings" />
      <span class="text">Comment Settings</span>
    </section>
    <section class="body">
      <section class="description" @click.stop="localMetadataDisplay = !localMetadataDisplay">
        <base-checkbox-solid :value="localMetadataDisplay" />
        <span class="text">Display Metadata label with comments</span>
      </section>
      <section class="input" @click.stop="open = !open" >
        <base-input :readonly=true class="input-dropdown" v-model="localMetadataHeader" />
        <section class="dropdown-icon" :class="{ open }" >
          <i class="fa fa-caret-down icon-dropdown" :class="{ open }" />
        </section>
        <storyteller-slide-comments-setting-dropdown v-if="open" @close="open = false" @select="onSelect" />
      </section>
    </section>
    <section class="footer">
      <base-button class="cancel-btn" colour="light" size="small" type="link" @click="onClickCancel">Cancel</base-button>
      <loading-blocks-overlay v-if="saving" size="small" />
      <base-button v-else class="done-btn" size="small" @click="onClickDone">
        <span>Done</span>
      </base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseCheckboxSolid from '@/components/Base/BaseCheckboxSolid';
import BaseInput from '@/components/Base/BaseInput';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import StorytellerRequest from '@/services/request/StorytellerRequest';
import StorytellerSlideCommentsSettingDropdown from '@/components/StorytellerSlideComments/StorytellerSlideCommentsSettingDropdown';

export default {
  name: 'storyteller-slide-comments-setting-modal',

  components: {
    BaseButton,
    BaseCheckboxSolid,
    BaseInput,
    LoadingBlocksOverlay,
    StorytellerSlideCommentsSettingDropdown,
  },

  data() {
    return {
      localMetadataDisplay: false,
      localMetadataIndex: null,
      open: false,
      saving: false,
    };
  },

  computed: {
    ...mapState('datasets', ['metadata']),

    ...mapState('storyteller', ['activeReport']),

    localMetadataHeader() {
      const match = this.metadata.find(
        item => item.metadataIndex === this.localMetadataIndex,
      );
      return match ? match.metadataHeader : null;
    },
  },

  created() {
    const match = this.metadata.find(
      item => item.metadataIndex === this.activeReport.settings.commentSettings.metadataIndex,
    );
    this.localMetadataIndex = match ?
      match.metadataIndex
      : this.metadata[0].metadataIndex;

    this.localMetadataDisplay = this.activeReport.settings.commentSettings.displayMetadata;
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    onClickCancel() {
      this.closeModal();
    },

    async onClickDone() {
      this.saving = true;
      this.activeReport.settings.commentSettings.displayMetadata = this.localMetadataDisplay;
      this.activeReport.settings.commentSettings.metadataIndex = this.localMetadataIndex;
      await StorytellerRequest.updateCommentSettings();
      this.saving = false;
      this.closeModal();
    },

    onSelect(metadata) {
      this.localMetadataIndex = metadata.metadataIndex;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-slide-comments-setting-modal {
  @include panel;

  color: $nps-blue;
  position: relative;
  width: 320px;

  .header {
    @include flex("block", "row", "start", "center");
    @include truncate;

    border-bottom: 1px solid rgba(19, 28, 41, 0.2);
    font-size: $font-size-sm;
    font-weight: $font-weight-bold;
    padding: 1rem 1.5rem;
    width: 100%;

    .icon-settings {
      margin-right: 0.4rem;
    }

    .text {
      margin-right: 0.8rem;
    }
  }

  .body {
    @include flex("block", "column", "start", "start");

    font-size: $font-size-xs;
    padding: 1rem 1.5rem;
    width: 100%;

    .description {
      @include flex("block", "row", "start", "center");

      cursor: pointer;

      .base-checkbox-solid {
        margin-right: 0.5rem;
      }
    }

    .input {
      @include flex('inline', 'row', 'start', 'center');

      height: 1.75rem;
      margin-top: 0.6rem;
      position: relative;
      width: 100%;

      &.name {
        width: 197px;
      }

      .base-input {
        @include flex('inline', 'row', 'start', 'center');

        background-color: clr("white");
        border: 1px solid #7362B7;
        font-size: $font-size-xs;
        height: 100%;
        padding: 0.5rem;

        &.input-dropdown {
          border-radius: $border-radius-medium 0 0 $border-radius-medium;
          border-right: none;
          cursor: pointer;
        }
      }

      .dropdown-icon {
        @include panel;
        @include flex('inline', 'row', 'start', 'center');

        border-radius: $border-radius-medium;
        border: 1px solid #7362B7;
        box-shadow: unset;
        cursor: pointer;
        font-size: $font-size-xs;
        height: 100%;
        left: -3px;
        padding-left: 0.6rem;
        position: relative;
        width: 2.1rem;

        .icon-dropdown {
          transition: all $interaction-transition-time;

          &.open {
            transform: rotate(180deg);
          }
        }
      }
    }
  }

  .footer {
    @include flex("block", "row", "space-between", "center");

    border-top: 1px solid rgba(19, 28, 41, 0.2);
    padding: 1rem 1.5rem;
    width: 100%;

    .base-button {
      font-size: $font-size-xxs;
      font-weight: 600;
      text-transform: uppercase;

      &.cancel-btn {
        padding-left: 0;
      }

      &.done-btn {
        background-color: #2D1757;
        padding: 0.5rem 0.8rem;
        width: 90px;
      }

      &:hover, &:focus {
        &.done-btn {
          background-color: rgba(19, 28, 41);
        }
      }
    }
  }
}
</style>
