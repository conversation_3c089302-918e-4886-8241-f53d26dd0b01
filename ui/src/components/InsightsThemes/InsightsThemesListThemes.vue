<template>
  <section class="insights-themes-list-themes" :class="{ presentation }">
    <section class="header"># / Theme</section>
    <section class="header">Score</section>
    <section class="header">Vol</section>
    <section class="header">Insights</section>
    <section class="header">Illustrative Comments</section>

    <section class="border"></section>

    <template v-for="(item, index) in dataList">
      <section :key="`${index}-label`" class="label">
        <section class="index" :class="{ active: isThemeActive && hasInteractivity }">
          {{ Number(index) + 1 + offset }}
        </section>
        <span>{{ topicLabelText(item) }}</span>
      </section>

      <section :key="`${index}-score`" class="score">
        <common-adore-score-square :color-border="true"
         :footer="true"
         :score="Math.round(item.polarity * 100)"
         :signifier="true"
         :size="2.3"/>
      </section>

      <section :key="`${index}-volume`" class="volume">
        <common-adore-score-square :score="volumeText(item)" :size="2.3"/>
      </section>

      <section :key="`${index}-description`" class="description">
        <section class="description-text">
          {{ descriptionTexts[item.id] }}
        </section>

        <section v-if="hasMetaData(datasetBenchmark) && themesMetadata.length" class="improvement">
          Addressing "{{ topicLabelText(item) }}" could improve
          <span class="metric">{{ metadataHeader(datasetBenchmark) }}</span>
          by
          <span class="percent">{{ improvementPercent(item) }}</span>
        </section>
        <section v-if="!presentation && !isSample && isEditor" class="edit-btn" @click="onClickEditEmotion(item)">Edit</section>
      </section>

      <section v-if="comments[item.id]" :key="`${index}-comment`" class="comment">
        <section class="content" :title="comments[item.id]">"{{ comments[item.id].trim() }}"</section>
        <section v-if="!presentation && !isSample && isEditor" class="edit-btn" @click="onClickEditComment(item)">Edit</section>
      </section>

      <section v-else :key="`${index}-comment`" class="comment">
        <loading-blocks-overlay>Loading Comment...</loading-blocks-overlay>
      </section>
    </template>
  </section>
</template>

<script>
import { zipObject } from 'lodash-es';
import { ChevronDownIcon, ChevronUpIcon } from 'vue-feather-icons';
import { mapActions, mapGetters, mapState } from 'vuex';

import CommonAdoreScoreSquare from '@/components/CommonComponent/CommonAdoreScoreSquare';
import IllustrativeCommentModal from '@/components/IllustrativeCommentModal/IllustrativeCommentModal';
import InsightsModalEmotion from '@/components/Insights/InsightsModalEmotion';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';

import { nameByApiIndex, positionApiByLabel } from '@/helpers/index-utils';
import { datasetInsightApiV0 } from '@/services/api';

export default {
  name: 'insights-themes-list-themes',

  components: {
    ChevronDownIcon,
    ChevronUpIcon,
    CommonAdoreScoreSquare,
    LoadingBlocksOverlay,
  },

  props: {
    dataList: {
      type: Array,
      required: true,
    },

    offset: {
      type: Number,
      default: 0,
    },

    presentation: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      defaultComments: {},
      hasInteractivity: false,
    };
  },

  computed: {
    ...mapGetters('datasets', [
      'get',
      'hasMetaData',
      'isEditable',
      'metadataHeader',
    ]),

    ...mapState('datasetsInsights', [
      'datasetBenchmark',
      'scorecards',
      'summaries',
      'themesMetadata',
    ]),

    ...mapState('themes', ['selectedLabel', 'selectedTheme']),

    comments() {
      if (Object.keys(this.defaultComments).length === 0) return {};

      return this.dataList.reduce((acc, item) => {
        const persistedComment = this.persistedComments.find(p => p.themeId === item.id);
        const defaultComment = this.defaultComments[item.id];

        if (persistedComment == null && defaultComment == null) return acc;

        if (persistedComment == null) acc[item.id] = defaultComment;
        else acc[item.id] = persistedComment.comment;

        return acc;
      }, {});
    },

    dataset() {
      return this.get(this.datasetBenchmark);
    },

    descriptionTexts() {
      if (this.summary == null) return {};

      return this.dataList.reduce((acc, item) => {
        const { gapScore, indexName, percent } = this.emotionPercentages[item.id];

        if (gapScore === 0) {
          acc[item.id] = `${percent} - Same ${indexName} score as dataset`;
        } else {
          acc[item.id] = `${percent} ${gapScore > 0 ? 'more' : 'less'} ${indexName} than dataset`;
        }

        return acc;
      }, {});
    },

    emotions() {
      return this.dataList.reduce((acc, item) => {
        const persistedEmotion = this.persistedEmotions.find(p => item.id === p.themeId);

        if (persistedEmotion == null) acc[item.id] = nameByApiIndex(item.maxEmotionPosition);
        else acc[item.id] = persistedEmotion.emotion;

        return acc;
      }, {});
    },

    emotionIndexAverages() {
      return this.summary?.emotionIndexesAvg || [];
    },

    emotionPercentages() {
      if (this.summary == null) return {};

      return this.dataList.reduce((acc, item) => {
        const emoLabel = this.emotions[item.id];
        const emoIndex = positionApiByLabel(emoLabel);
        const emoScore = item.emotionIndexes[emoIndex];

        const datasetScore = this.emotionIndexAverages[emoIndex];
        const gapScore = Math.abs(emoScore) - Math.abs(datasetScore);

        const percentNumb = Math.round(Math.abs(gapScore / datasetScore) * 100);
        const percent = percentNumb < 1 ? '<1%' : `${percentNumb}%`;

        acc[item.id] = {
          datasetScore,
          gapScore,
          percent,
          indexName: emoLabel,
        };

        return acc;
      }, {});
    },

    isEditor() {
      return this.isEditable(this.datasetBenchmark);
    },

    isSample() {
      return this.dataset.localSample || false;
    },

    persistedComments() {
      return this.scorecard.insightsScorecard?.persistedComments || [];
    },

    persistedEmotions() {
      return this.scorecard.insightsScorecard?.persistedEmotions || [];
    },

    scorecard() {
      return this.scorecards.find(s => s.datasetId === this.datasetBenchmark);
    },

    summary() {
      return this.summaries.find(s => s.id === this.datasetBenchmark);
    },
  },

  async mounted() {
    await this.fetchComments();
  },

  methods: {
    ...mapActions('datasetsInsights', [
      'setCommentSelection',
      'setCommentTheme',
      'setSelectedEmotion',
    ]),

    ...mapActions('modal', ['setModalComponent']),

    async fetchComments() {
      const themeIds = this.dataList.map(t => t.id);

      const comments = await datasetInsightApiV0.getComments(this.datasetBenchmark, themeIds);

      this.defaultComments = zipObject(themeIds, comments);
    },

    improvementPercent(item) {
      const optimiseObj = this.themesMetadata.find(m => m.topicId === item.id);
      const percent = Math.round(optimiseObj?.optimiseChange * 100) || 0;

      return percent ? `${percent}%` : '<1%';
    },

    isThemeActive(item) {
      return this.selectedTheme?.id === item.id
        || this.selectedLabel === item.topicLabel;
    },

    onClickEditComment(theme) {
      const pComment = this.persistedComments?.find(c => c.themeId === theme.id) || {};

      if (pComment?.commentId) {
        this.setCommentSelection({ comment: { id: pComment.commentId, content: pComment.comment } });
      }
      this.setCommentTheme({ theme });
      this.setModalComponent({ component: IllustrativeCommentModal });
    },

    onClickEditEmotion(theme) {
      this.setCommentTheme({ theme });
      this.setSelectedEmotion({ emotion: this.emotions[theme.id] });
      this.setModalComponent({ component: InsightsModalEmotion });
    },

    topicLabelText(item) {
      const label = item.topicLabel;

      return label.length > 50 ? `${label.substring(0, 50)}...` : label;
    },

    volumeText(item) {
      const percent = Math.round((item.numOfDocuments / this.dataset.documentCount) * 100);

      return percent === 0 ? '<1%' : `${percent}%`;
    },
  },

  watch: {
    dataList() {
      this.fetchComments();
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.insights-themes-list-themes {
  display: grid;
  grid-auto-rows: minmax(0, min-content);
  grid-column-gap: 0.8rem;
  grid-row-gap: 1rem;
  grid-template-columns: 150px 2.5rem 2.5rem 190px 1fr;

  .header {
    font-size: 0.7rem;
    font-weight: $font-weight-bold;
    text-transform: uppercase;
  }

  .border {
    border-bottom: $border-standard;
    grid-column: 1 / -1;
  }

  .label {
    line-height: 1rem;

    span {
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .label, .volume, .type, .description, .comment {
    font-size: 0.7rem;
  }

  .label {
    @include flex("block", "row", "start", "center");

    align-self: start;
    color: $insights-blue;
    font-size: $font-size-xs;
    font-weight: $font-weight-bold;

    .index {
      @include flex("block", "row", "center", "center");
      @include rigid;

      background: $insights-blue;
      border-radius: $border-radius-medium;
      color: clr('white');
      font-size: 0.7rem;
      height: 1rem;
      margin-right: 0.5rem;
      width: 1rem;
    }
  }

  .type {
    font-weight: $font-weight-bold;
    margin-top: 0.2rem;

    &.b {
      color: clr('red', 'badText');
    }

    &.g {
      color: clr('green', 'goodText');
    }
  }

  .description {
    font-size: 11px;
    margin-bottom:10px;

    .description-text {
      @include flex("block", "row", "start", "center");
      font-weight: $font-weight-bold;
      line-height: 0.8rem;

      .icon {
        height: 1rem;
        width: 1rem;
      }
    }

    .improvement {
      line-height: 1rem;
      margin-top: 0.2rem;

      .metric {
        font-style: italic;
      }

      .percent {
        font-weight: $font-weight-bold;
      }
    }
  }

  .comment {
    font-size: 11px;
    line-height: 1rem;

    .content {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 5;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .loading-blocks-overlay {
      height: 2rem;

      .text {
        font-size: $font-size-xxs;
      }

      .container {
        margin-top: 0;
      }
    }
  }

  .edit-btn {
    cursor: pointer;
    font-size: $font-size-xxs;
    font-weight: bold;
    line-height: 1rem;
    text-decoration: underline;

    &:hover {
      opacity: 0.7;
    }
  }
}
</style>
