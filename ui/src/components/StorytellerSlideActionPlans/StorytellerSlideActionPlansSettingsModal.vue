<template>
  <section class="storyteller-slide-action-plans-settings-modal">
    <section class="header">
      <section class="header-title">
        <i class="fa-solid fa-table icon-display" />
        <span class="text-title">Suggested Action Plan Display Settings</span>
      </section>
      <span class="text-desc">Choose the sections you want to display.</span>
    </section>
    <section class="body">
      <section class="settings-item disabled">
        <base-checkbox-solid :background="false" :indeterminate="true" />
        <span class="settings-item-text">Themes</span>
      </section>
      <section class="settings-item disabled">
        <base-checkbox-solid :background="false" :indeterminate="true" />
        <span class="settings-item-text">Suggested Actions</span>
      </section>
      <section class="settings-item" v-if="availableDisplays.predictImprovement" @click.stop="toggleSetting('displayImpact')">
        <base-checkbox-solid :value="selectedSlide.slideData.settings.displayImpact" />
        <span class="settings-item-text">Potential Impact</span>
      </section>
      <section class="settings-item" @click.stop="toggleSetting('displayEffort')">
        <base-checkbox-solid :value="selectedSlide.slideData.settings.displayEffort" />
        <span class="settings-item-text">Effort to Implement</span>
      </section>
      <section class="settings-item" v-if="availableDisplays.valueAtRisk" @click.stop="toggleSetting('displayValueAtRisk')">
        <base-checkbox-solid :value="selectedSlide.slideData.settings.displayValueAtRisk" />
        <span class="settings-item-text">Revenue at Risk</span>
      </section>
    </section>
    <section class="footer">
      <base-button class="cancel-btn" colour="light" size="small" type="link" @click="onClickCancel">Cancel</base-button>
      <loading-blocks-overlay v-if="saving" size="small" />
      <base-button v-else class="done-btn" size="small" @click="onClickDone">
        <span>Done</span>
      </base-button>
    </section>
  </section>
</template>

<script>
import { cloneDeep } from 'lodash-es';
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseCheckboxSolid from '@/components/Base/BaseCheckboxSolid';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import StorytellerActionPlansRequest from '@/services/request/StorytellerActionPlansRequest';
import StorytellerSlideType from '@/enum/storyteller-slide-type';

export default {
  name: 'storyteller-slide-action-plans-settings-modal',

  components: {
    BaseButton,
    BaseCheckboxSolid,
    LoadingBlocksOverlay,
  },

  data() {
    return {
      initialSettings: {},
      saving: false,
    };
  },

  computed: {
    ...mapGetters('storyteller', ['availableDisplays']),

    ...mapState('storyteller', ['selectedSlide', 'slides']),
  },

  created() {
    this.initialSettings = cloneDeep(this.selectedSlide.slideData.settings);
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    onClickCancel() {
      this.closeModal();
    },

    async onClickDone() {
      this.saving = true;
      await StorytellerActionPlansRequest.updateSettings();
      this.saving = false;
      this.closeModal();
    },

    toggleSetting(key) {
      // Toggle the value of the setting for the selected slide
      const newValue = !this.selectedSlide.slideData.settings[key];

      // Update the setting for all slides of type 'ACTION_PLANS'
      this.slides
        .filter(slide => slide.slideType === StorytellerSlideType.ACTION_PLANS.name)
        .forEach(slide => {
          slide.slideData.settings[key] = newValue;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-slide-action-plans-settings-modal {
  @include panel;

  color: $nps-blue;
  position: relative;
  width: 375px;

  .header {
    @include flex("block", "column", "center", "start");

    padding: 1.75rem 1.75rem 0.5rem 1.75rem;
    width: 100%;

    .header-title {
      @include flex("block", "row", "start", "center");

      font-size: $font-size-sm;
      font-weight: $font-weight-bold;
      width: 100%;

      .icon-display {
        margin-right: 0.4rem;
      }
    }

    .text-desc {
      color: rgba(19, 28, 41, 0.7);
      font-size: $font-size-xs;
      margin-top: 1rem;
    }
  }

  .body {
    @include flex("block", "column", "start", "start");

    padding: 0 1.75rem;
    width: 100%;

    .settings-item {
      @include flex("block", "row", "start", "center");

      border-bottom: 1px solid #F2F1FB;
      cursor: pointer;
      padding: 1rem 0;
      width: 100%;

      &.disabled {
        .settings-item-text {
          color: #707070;
        }
      }

      &:last-child {
        border-bottom: none;
      }

      .settings-item-text {
        color: #2D1757;
        font-size: $font-size-xs;
        margin-left: 0.5rem;
      }
    }
  }

  .footer {
    @include flex("block", "row", "space-between", "center");

    border-top: 1px solid rgba(19, 28, 41, 0.2);
    padding: 1.2rem 1.75rem;
    width: 100%;

    .base-button {
      font-size: $font-size-xxs;
      font-weight: 600;
      text-transform: uppercase;

      &.cancel-btn {
        padding-left: 0;
      }

      &.done-btn {
        background-color: #2D1757;
        padding: 0.5rem 0.8rem;
        width: 90px;
      }

      &:hover, &:focus {
        &.done-btn {
          background-color: rgba(19, 28, 41);
        }
      }
    }
  }
}
</style>
