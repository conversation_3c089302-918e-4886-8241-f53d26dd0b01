<template>
  <section class="storyteller-slide-action-plans-content" :style="style">
    <storyteller-slide-action-plans-theme-name    class="row" :actions="slideData.actions" />
    <storyteller-slide-action-plans-suggested     class="row" :actions="slideData.actions" :editable="editable" />
    <storyteller-slide-action-plans-impact        class="row" :actions="slideData.actions" :editable="editable" v-if="displayImpact" />
    <storyteller-slide-action-plans-effort        class="row" :actions="slideData.actions" :editable="editable" v-if="displayEffort" />
    <storyteller-slide-action-plans-value-at-risk class="row" :actions="slideData.actions"                      v-if="displayValueAtRisk" />
  </section>
</template>

<script>
import { mapState } from 'vuex';

import StorytellerSlideActionPlansEffort from '@/components/StorytellerSlideActionPlans/StorytellerSlideActionPlansEffort';
import StorytellerSlideActionPlansImpact from '@/components/StorytellerSlideActionPlansImpact/StorytellerSlideActionPlansImpact';
import StorytellerSlideActionPlansSuggested from '@/components/StorytellerSlideActionPlansSuggested/StorytellerSlideActionPlansSuggested';
import StorytellerSlideActionPlansThemeName from '@/components/StorytellerSlideActionPlans/StorytellerSlideActionPlansThemeName';
import StorytellerSlideActionPlansValueAtRisk from '@/components/StorytellerSlideActionPlans/StorytellerSlideActionPlansValueAtRisk';

export default {
  name: 'storyteller-slide-action-plans-content',

  components: {
    StorytellerSlideActionPlansEffort,
    StorytellerSlideActionPlansImpact,
    StorytellerSlideActionPlansSuggested,
    StorytellerSlideActionPlansThemeName,
    StorytellerSlideActionPlansValueAtRisk,
  },

  props: {
    editable: {
      type: Boolean,
      default: false,
    },
    slideData: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapState('storyteller', ['selectedSlide']),

    displayEffort() {
      if (!this.slideData.settings.displayEffort) return false;
      if (this.editable) return true;
      return this.slideData.actions.some(action => action.effort.text !== null);
    },

    displayImpact() {
      if (!this.slideData.settings.displayImpact) return false;
      if (this.editable) return true;
      return this.slideData.actions.some(action => action.impact !== '<p></p>');
    },

    displayValueAtRisk() {
      return this.slideData.settings.displayValueAtRisk;
    },

    style() {
      let optionalCount = 0;

      if (this.displayEffort) optionalCount += 1;
      if (this.displayImpact) optionalCount += 1;
      if (this.displayValueAtRisk) optionalCount += 1;

      if (optionalCount === 0) {
        return {
          '--gridTemplateRows': '12% 88%',
        };
      }

      // Calculate how much to increase the actions row percentage based on hidden settings
      // For each hidden setting, increase by 10% (5 settings minus the displayed ones)
      const actionsPercent = 58 + (3 - optionalCount) * 10;

      // Return the grid template rows style, where:
      // - The first row is fixed at 10%
      // - The second row (actions row) is calculated based on hidden settings
      // - Remaining rows are repeated based on the number of displayed settings (each at 10%)
      return {
        '--gridTemplateRows': `12% ${actionsPercent}% repeat(${optionalCount}, 10%)`,
      };
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-action-plans-content {
  display: grid;
  grid-template-rows: var(--gridTemplateRows);
  height: 100%;
  padding: 0.8em 0;
  width: 100%;

  .row {
    display: grid;
    grid-template-columns: 10% 30% 30% 30%;
  }
}
</style>
