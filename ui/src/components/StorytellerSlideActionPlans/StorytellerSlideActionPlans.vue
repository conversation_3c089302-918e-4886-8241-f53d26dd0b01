<template>
  <section class="storyteller-slide-action-plans" :style="style" @mouseenter="hovered = true" @mouseleave="hovered = false">
    <section class="margin-top" />
    <section class="body">
      <section class="title-header">
        <span class="text-header">Suggested Actions</span>
        <storyteller-slide-setting-btn v-if="editable" :text="'Action Plans Settings'" @click="onClickSetting" />
      </section>
      <storyteller-slide-action-plans-content :editable="editable" :slide-data="slideData" />
    </section>
    <storyteller-slide-footer :slide-width="slideWidth" />
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import StorytellerSlideActionPlansContent from '@/components/StorytellerSlideActionPlans/StorytellerSlideActionPlansContent';
import StorytellerSlideActionPlansSettingsModal from '@/components/StorytellerSlideActionPlans/StorytellerSlideActionPlansSettingsModal';
import StorytellerSlideFooter from '@/components/StorytellerSlides/StorytellerSlideFooter';
import StorytellerSlideSettingBtn from '@/components/Storyteller/StorytellerSlideSettingBtn';
import UpdateSlide from '@/components/Mixins/UpdateSlide';

export default {
  name: 'storyteller-slide-action-plans',

  components: {
    StorytellerSlideActionPlansContent,
    StorytellerSlideFooter,
    StorytellerSlideSettingBtn,
  },

  mixins: [UpdateSlide],

  computed: {
    fontSize() {
      return `${50 * this.ratio}px`;
    },

    ratio() {
      return this.slideWidth / 1920;
    },

    style() {
      return {
        '--textFontSize': `${50 * this.ratio}px`,
      };
    },
  },

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    onClickSetting() {
      this.setModalComponent({ component: StorytellerSlideActionPlansSettingsModal });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-action-plans {
  background-color: #FFF;
  color: $nps-blue;
  display: grid;
  font-family: Inter, serif;
  font-size: var(--textFontSize);
  font-weight: $font-weight-semi-light;
  grid-template-rows: 13% 74% 13%;
  height: 100%;
  padding: 0 2em;
  width: 100%;

  &:hover {
    .body .title-header .storyteller-slide-setting-btn {
      display: flex;
    }
  }

  strong {
    font-weight: 600;
  }

  .body {
    @include flex("block", "column", "start", "start");

    height: 100%;
    width: 100%;

    .title-header {
      @include flex("block", "row", "start", "center");

      position: relative;
      width: fit-content;

      .text-header {
        font-size: 0.5em;
        font-weight: 800;
        letter-spacing: 0.04em;
        opacity: 0.5;
        text-transform: uppercase;
      }

      .storyteller-slide-setting-btn {
        display: none;
        position: absolute;
        right: -1.5em;
      }
    }
  }
}
</style>
