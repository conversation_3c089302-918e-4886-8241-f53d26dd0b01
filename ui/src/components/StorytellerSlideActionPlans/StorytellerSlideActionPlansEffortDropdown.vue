<template>
  <section class="storyteller-slide-action-plans-effort-dropdown" v-click-outside-handler="{ handler: 'close' }">
    <section class="title">
      <span>Select Progress</span>
    </section>
    <section class="body">
      <section class="item low" :style="{color: lowEffort.customCssStyle.color}" @click="onClickLow">
        <section class="item-content">
          <span>{{lowEffort.text}}</span>
        </section>
      </section>
      <section class="item medium" :style="{color: mediumEffort.customCssStyle.color}" @click="onClickMedium">
        <section class="item-content">
          <span>{{mediumEffort.text}}</span>
        </section>
      </section>
      <section class="item high" :style="{color: highEffort.customCssStyle.color}" @click="onClickHigh">
        <section class="item-content">
          <span>{{highEffort.text}}</span>
        </section>
      </section>
      <section class="item custom" :class="{active: isCustom}" @click="isCustom = !isCustom">
        <section class="item-content">
          <section>Custom Value</section>
          <i class="fa-regular fa-chevron-right" />
        </section>
      </section>
      <section class="custom-element" v-if="isCustom">
        <base-input class="input" :focus="true" :style="{color: customEffort.customCssStyle.color}" v-model="customEffort.text" placeholder="e.g. Very High" />
        <storyteller-slide-action-plans-effort-color-control :init-color="customEffort.customCssStyle.color" @selectColor="onSelectColor" />
      </section>
      <base-button v-if="isCustom" class="done-btn" size="small" :disabled="disabledDoneBtn" @click="onClickDone">
        <span>Done</span>
      </base-button>
    </section>
  </section>
</template>

<script>
import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import ClickOutsideHandler from '@/directives/click-outside-handler';
import StorytellerActionPlansRequest from '@/services/request/StorytellerActionPlansRequest';
import StorytellerSlideActionPlansEffortColorControl from '@/components/StorytellerSlideActionPlans/StorytellerSlideActionPlansEffortColorControl';

export default {
  name: 'storyteller-slide-action-plans-effort-dropdown',

  components: {
    BaseButton,
    BaseInput,
    StorytellerSlideActionPlansEffortColorControl,
  },

  directives: {
    ClickOutsideHandler,
  },

  props: {
    action: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      isCustom: false,
      customEffort: {
        text: null,
        customCssStyle: {
          color: '#EB534C',
        },
      },
      lowEffort: {
        text: 'Low',
        customCssStyle: {
          color: '#64B656',
        },
      },
      mediumEffort: {
        text: 'Medium',
        customCssStyle: {
          color: '#EB7E4C',
        },
      },
      highEffort: {
        text: 'High',
        customCssStyle: {
          color: '#EB534C',
        },
      },
    };
  },

  computed: {
    disabledDoneBtn() {
      return !this.customEffort.text || this.customEffort.text.trim() === '';
    },
  },

  created() {
    if (this.action.effort.text &&
        this.action.effort.text !== this.lowEffort.text &&
        this.action.effort.text !== this.mediumEffort.text &&
        this.action.effort.text !== this.highEffort.text
    ) {
      this.customEffort.text = this.action.effort.text;
      this.customEffort.customCssStyle.color = this.action.effort.customCssStyle.color;
    }
  },

  methods: {
    close() {
      this.$emit('close', this.action);
    },

    handleClick(effort) {
      this.action.effort = effort;
      StorytellerActionPlansRequest.updateSlide();
      this.close();
    },

    onClickDone() {
      if (this.disabledDoneBtn) return;
      this.handleClick(this.customEffort);
    },

    onClickHigh() {
      this.handleClick(this.highEffort);
    },

    onClickLow() {
      this.handleClick(this.lowEffort);
    },

    onClickMedium() {
      this.handleClick(this.mediumEffort);
    },

    onSelectColor(color) {
      this.customEffort.customCssStyle.color = color;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-action-plans-effort-dropdown {
  @include flex("block", "column", "start", "start");
  @include panel;

  border: 1px solid rgba(115, 98, 183, 0.6);
  color: $nps-blue;
  cursor: auto;
  font-size: $font-size-xs;
  white-space: nowrap;
  width: 244px;
  z-index: 99;

  .title {
    color: rgba(19, 28, 41, 0.3);
    font-weight: $font-weight-extra-bold;
    padding: 1rem 1rem 0.3rem 1rem;
    text-transform: uppercase;
  }

  .body {
    padding: 0 1rem 0.5rem 1rem;
    width: 100%;

    .item {
      border-bottom: 1px solid rgba(115, 98, 183, 0.3);
      border-radius: 2px;
      cursor: pointer;
      font-weight: $font-weight-medium;
      padding: 0.5rem 0;
      width: 100%;

      &.custom {
        border-bottom: none;
        color: #646464;
      }

      &:hover, &.active {
        &.low {
          background-color: rgba(43, 200, 61, 0.1);
        }

        &.medium {
          background-color: rgba(235, 126, 76, 0.1);
        }

        &.high {
          background-color: rgba(235, 83, 76, 0.1);
        }

        &.custom {
          background-color: rgba(100, 100, 100, 0.05);
        }
      }

      .item-content {
        @include flex("block", "row", "space-between", "center");

        padding: 0 0.5rem;
        width: 100%;
      }
    }

    .custom-element {
      @include flex("block", "row", "space-between", "center");

      height: 26px;
      margin-top: 0.5rem;

      .input {
        font-weight: $font-weight-medium;
        height: 100%;
        margin-right: 0.5rem;
      }
    }

    .done-btn {
      background-color: #2D1757;
      float: right;
      font-size: $font-size-xxs;
      font-weight: 600;
      margin-left: auto;
      margin-top: 0.5rem;
      padding: 0.5rem 0.8rem;
      text-transform: uppercase;
      width: 75px;

      &:hover, &:focus {
        background-color: rgba(19, 28, 41);
      }
    }
  }
}
</style>
