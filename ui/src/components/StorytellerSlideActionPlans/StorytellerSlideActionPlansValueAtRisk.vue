<template>
  <section class="storyteller-slide-action-plans-value-at-risk">
    <span class="column header">Revenue At Risk</span>
    <section v-for="(action, index) in actions" class="column content" :class="{middle: index === 1}">
    <span class="text">{{varCurrency}}{{varAmount(action.valueAtRisk)}}</span>
  </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import formatCurrencyNumber from '@/helpers/number-utils';

export default {
  name: 'storyteller-slide-action-plans-value-at-risk',

  props: {
    actions: {
      type: Array,
      required: true,
    },
  },

  computed: {
    ...mapState('storyteller', ['activeReport']),

    varCurrency() {
      return this.activeReport.settings.valueAtRiskInfo.currencySymbol;
    },
  },

  methods: {
    varAmount(number) {
      return formatCurrencyNumber(number);
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-action-plans-value-at-risk {
  .column {
    border-top: $border-light solid rgba(19, 28, 41, 0.1);
  }

  .header {
    @include flex("block", "row", "start", "center");

    color: rgba(19, 28, 41, 0.3);
    font-size: 0.38em;
    font-weight: $font-weight-extra-bold;
    text-transform: uppercase;
  }

  .content {
    @include flex("block", "row", "start", "center");

    padding: 0 0.6em;

    .text {
      font-size: 0.6em;
    }

    &.middle {
      background-color: rgba(241, 241, 241, 0.5);
      border-bottom-left-radius: 0.08em;
      border-bottom-right-radius: 0.08em;
    }
  }
}
</style>
