<template>
  <section class="storyteller-slide-action-plans-effort-color-control">
    <button class="btn" @click.stop="open = !open" v-click-outside-handler="{handler: 'onBlur'}">
      <i class="fa-regular fa-palette icon-color" />
      <span class="color-preview">
        <span class="color-preview-inner" :style="{'background-color': color.hex}" />
      </span>
      <span v-if="open" class="select-color-dropdown" @click.stop="onBlur">
        <sketch v-model="color" :preset-colors="presetColours"/>
      </span>
    </button>
  </section>
</template>

<script>
import { Sketch } from 'vue-color';

import ClickOutsideHandler from '@/directives/click-outside-handler';

export default {
  name: 'storyteller-slide-action-plans-effort-color-control',

  components: {
    Sketch,
  },

  directives: {
    ClickOutsideHandler,
  },

  props: {
    initColor: {
      type: String,
      default: '#000000',
    },
  },

  data() {
    return {
      color: {
        hex: '#000000',
      },
      open: false,
      presetColours: [
        '#FFEE62',
        '#62CC50',
        '#64B656',
        '#EB534C',
        '#FF9531',
        '#888D95',
        '#131C29',
        '#3981F7',
      ],
    };
  },

  watch: {
    color() {
      this.$emit('selectColor', this.color.hex);
    },
  },

  created() {
    this.color.hex = this.initColor;
  },

  methods: {
    onBlur() {
      this.open = false;
    },
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-action-plans-effort-color-control {
  @include flex("block", "row", "start", "center");

  background-color: #2B333E;
  border-radius: 3px;
  height: 100%;
  position: relative;

  &:hover {
    background-color: rgba(19, 28, 41, 0.9);
  }

  .btn {
    @include flex("block", "row", "space-between", "center");

    background-color: transparent;
    border: none;
    cursor: pointer;
    font-size: $font-size-xxs;
    height: 100%;
    padding: 0 0.7rem;
    width: 100%;

    .icon-color {
      color: clr('white');
      margin-right: 0.4rem;
    }

    .color-preview {
      @include flex("block", "row", "center", "center");

      border-radius: 50%;
      border: 1px solid rgba(255, 255, 255, 0.6);
      height: 13px;
      position: relative;
      width: 13px;

      .color-preview-inner {
        border-radius: 50%;
        border: 1px solid rgba(255, 255, 255, 1);
        height: 100%;
        width: 100%;
      }
    }
  }

  .select-color-dropdown {
    position: absolute;
    right: -97px;
    top: calc(100% + 2px);
    z-index: 999;

    .vc-sketch {
      .vc-sketch-saturation-wrap, .vc-sketch-controls, .vc-sketch-field {
        display: none;
      }

      .vc-sketch-presets {
        padding-top: 0;
        border-top: none;
      }
    }
  }
}
</style>
