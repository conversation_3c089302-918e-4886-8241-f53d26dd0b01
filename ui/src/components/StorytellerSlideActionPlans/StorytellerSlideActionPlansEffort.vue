<template>
  <section class="storyteller-slide-action-plans-effort" :class="{editable}">
    <span class="column header">Effort To Implement</span>
    <section v-for="(action, index) in actions" class="column content" :class="{middle: index === 1}">
      <section v-if="action.effort.text" class="btn existing" :style="{color: action.effort.customCssStyle.color}" @click.stop="onClickDropdown(action)">
        <span class="text">{{action.effort.text}}</span>
        <i class="fa-solid fa-caret-down icon-down" />
      </section>
      <section v-else-if="editable" class="btn new" @click.stop="onClickDropdown(action)">
        <span class="text">Add Effort</span>
        <i class="fa-solid fa-caret-down icon-down" />
      </section>
      <section v-else class="btn new">
        <span class="text">-</span>
      </section>
      <storyteller-slide-action-plans-effort-dropdown
        v-if="isOpenDropdown(action)"
        :action="action"
        :key="index"
        @close="onCloseDropdown(action)"
      />
    </section>
  </section>
</template>

<script>
import ClickOutsideHandler from '@/directives/click-outside-handler';
import StorytellerSlideActionPlansEffortDropdown from '@/components/StorytellerSlideActionPlans/StorytellerSlideActionPlansEffortDropdown';

export default {
  name: 'storyteller-slide-action-plans-effort',

  components: {
    StorytellerSlideActionPlansEffortDropdown,
  },

  directives: {
    ClickOutsideHandler,
  },

  props: {
    actions: {
      type: Array,
      required: true,
    },
    editable: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      openDropdown: null,
    };
  },

  methods: {
    isOpenDropdown(action) {
      return this.openDropdown === action.themeId;
    },

    onClickDropdown(action) {
      if (!this.editable) return;

      if (this.isOpenDropdown(action)) {
        this.onCloseDropdown(action);
      } else {
        this.onOpenDropdown(action);
      }
    },

    onCloseDropdown(action) {
      if (this.openDropdown === action.themeId) {
        this.openDropdown = null;
      }
    },

    onOpenDropdown(action) {
      this.openDropdown = action.themeId;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-action-plans-effort {
  .column {
    border-top: $border-light solid rgba(19, 28, 41, 0.1);
  }

  &.editable {
    .content {
      .btn {
        &:hover {
          .text {
            text-decoration: underline;
          }

          .icon-down {
            visibility: visible;
          }

          &.new {
            color: #363E49;
          }
        }
      }
    }
  }

  .header {
    @include flex("block", "row", "start", "center");

    color: rgba(19, 28, 41, 0.3);
    font-size: 0.38em;
    font-weight: $font-weight-extra-bold;
    text-transform: uppercase;
  }

  .content {
    @include flex("block", "row", "start", "center");

    font-size: 0.6em;
    padding: 0 1em;
    position: relative;
    font-weight: $font-weight-semi-bold;

    &.middle {
      background-color: rgba(241, 241, 241, 0.5);
    }

    .btn {
      @include flex("block", "row", "start", "center");

      .text {
        cursor: pointer;
      }

      .icon-down {
        cursor: pointer;
        font-size: 0.9em;
        margin-left: 0.3em;
      }

      &.existing {
        .icon-down {
          visibility: hidden;
        }
      }

      &.new {
        @include flex("block", "row", "start", "center");

        color: #A4A4A4;
      }
    }

    .storyteller-slide-action-plans-effort-dropdown {
      left: 1em;
      position: absolute;
      bottom: 100%;
    }
  }
}
</style>
