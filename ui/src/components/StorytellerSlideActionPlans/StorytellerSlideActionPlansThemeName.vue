<template>
  <section class="storyteller-slide-action-plans-theme-name">
    <span class="header">Themes</span>
    <section v-for="(action, index) in actions" class="title" :class="{middle: index === 1}">
      <span class="order">{{action.actionOrder}}</span>
      <span class="text">{{action.themeName}}</span>
    </section>
  </section>
</template>

<script>

export default {
  name: 'storyteller-slide-action-plans-theme-name',

  props: {
    actions: {
      type: Array,
      required: true,
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-action-plans-theme-name {
  .header {
    @include flex("block", "row", "start", "center");

    color: rgba(19, 28, 41, 0.3);
    font-size: 0.38em;
    font-weight: $font-weight-extra-bold;
    text-transform: uppercase;
  }

  .title {
    @include flex("block", "row", "start", "center");
    @include truncate;

    padding-left: 1.2em;
    position: relative;

    .order {
      @include flex("block", "row", "center", "center");

      background-color: rgba(19, 28, 41, 0.1);
      border-radius: 50%;
      font-size: 0.4em;
      font-weight: $font-weight-bold;
      left: 1.2em;
      min-height: 1.35em;
      min-width: 1.35em;
      position: absolute;
    }

    .text {
      @include truncate;

      font-size: 0.64em;
      font-weight: $font-weight-medium;
    }

    &.middle {
      background-color: rgba(241, 241, 241, 0.5);
      border-top-left-radius: 0.08em;
      border-top-right-radius: 0.08em;
    }
  }
}
</style>
