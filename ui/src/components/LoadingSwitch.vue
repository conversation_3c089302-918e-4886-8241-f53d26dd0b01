<script>
import NetworkStatus from '@/enum/network-status';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';

export default {
  name: 'loading-switch',

  functional: true,

  props: {
    status: {
      type: Object,
      required: true,
    },
  },

  render(h, ctx) {
    const { status } = ctx.props;
    const { error, idle, loading } = ctx.slots();

    if (status === NetworkStatus.ERROR) return error || '';
    if (status === NetworkStatus.LOADING) return loading || h(LoadingBlocksOverlay);
    if (status === NetworkStatus.SUCCESS) return ctx.slots().default;

    return idle || '';
  },
};
</script>
