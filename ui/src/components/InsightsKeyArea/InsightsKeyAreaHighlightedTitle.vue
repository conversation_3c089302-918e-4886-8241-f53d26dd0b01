<template>
  <section class="insights-key-area-highlighted-title">
    <section class="header">Display Settings</section>

    <section class="highlight-selection">
      <insights-key-area-dropdown-emotion class="emotion-dropdown"/>
      <section class="border"></section>
      <insights-key-area-dropdown-stat class="stat-dropdown"/>
    </section>

    <section class="headline">
      <span class="headline-title">Title</span>
      <section class="button" @click="generateHeadline">
        <i class="fa-solid fa-arrow-rotate-left icon" />
        <span class="text">Generate New Title</span>
      </section>
    </section>

    <section class="headline-input">
      <base-input v-model.lazy="headlineModel"/>
    </section>

    <section class="custom-description">
      <insights-key-area-custom-description />
    </section>

    <section class="preview">
      <insights-key-area-preview/>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseInput from '@/components/Base/BaseInput';
import InsightsKeyAreaCustomDescription from '@/components/InsightsKeyArea/InsightsKeyAreaCustomDescription';
import InsightsKeyAreaDropdownEmotion from '@/components/InsightsKeyArea/InsightsKeyAreaDropdownEmotion';
import InsightsKeyAreaPreview from '@/components/InsightsKeyArea/InsightsKeyAreaPreview';
import InsightsKeyAreaDropdownStat from '@/components/InsightsKeyArea/InsightsKeyAreaDropdownStat';

import { datasetInsightApiV0 } from '@/services/api';

export default {
  name: 'insights-key-area-highlighted-title',

  components: {
    BaseInput,
    InsightsKeyAreaCustomDescription,
    InsightsKeyAreaDropdownEmotion,
    InsightsKeyAreaPreview,
    InsightsKeyAreaDropdownStat,
  },

  computed: {
    ...mapState('datasetsInsights', ['datasetBenchmark', 'editKeyAreaTheme']),

    datasetId() {
      return this.datasetBenchmark;
    },

    headlineModel: {
      get() {
        return this.editKeyAreaTheme.headline;
      },

      set(val) {
        const theme = { ...this.editKeyAreaTheme };
        theme.headline = val;
        this.setEditKeyAreaTheme({ theme });
      },
    },
  },

  methods: {
    ...mapActions('datasetsInsights', ['setEditKeyAreaTheme']),

    async generateHeadline() {
      const headline = await datasetInsightApiV0.getHeadline(this.datasetId, this.editKeyAreaTheme.themeId);
      const theme = { ...this.editKeyAreaTheme };
      theme.headline = headline;
      this.setEditKeyAreaTheme({ theme });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-key-area-highlighted-title {
  @include flex("block", "column", "start", "start");
  width: 100%;

  .header {
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
  }

  .headline {
    @include flex("block", "row", "between", "center");
    margin-top: 1.5rem;
    width: inherit;

    .headline-title {
      font-size: $font-size-xxs;
      font-weight: $font-weight-bold;
      text-transform: uppercase;
    }

    .button {
      @include flex("block", "row", "center", "center");
      color: #5F52C5;
      cursor: pointer;
      user-select: none;

      &:hover {
        opacity: 0.8;

        .text {
        text-decoration: underline;
        }
      }

      .icon {
        font-size: $font-size-xxs;
        margin-right: 0.2rem;
        transition: opacity $interaction-transition-time;
      }

      .text {
        font-size: $font-size-xxs;
        font-weight: $font-weight-bold;
        text-transform: uppercase;
      }
    }
  }

  .headline-input {
    margin-top: 0.5rem;
    width: inherit;

    .base-input {
      background-color: clr('white');
      border: $border-standard;
      border-radius: $border-radius-medium;
      font-size: $font-size-xs;
      padding: 0.4rem 1rem;
    }
  }

  .custom-description {
    margin-top: 1.5rem;
    width: inherit;
  }

  .highlight-selection {
    display: grid;
    grid-template-columns: 48% 4% 48%;
    margin-top: 1.5rem;
    width: 100%;
  }

  .preview {
    margin-top: 1.5rem;
    width: inherit;
  }
}
</style>
