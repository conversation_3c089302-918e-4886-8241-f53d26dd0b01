<template>
  <section class="insights-key-area-modal-edit">
    <section class="header">
      <section class="left">
        <edit-icon class="icon"/>
        <h2>Edit Highlight {{ Number(editKeyArea + 1) }}</h2>
      </section>

      <section class="right">
        <x-icon class="icon" @click="onClickCancel"/>
      </section>
    </section>

    <section class="body">
      <section class="themes-list">
        <h3>Highlight {{ Number(editKeyArea + 1) }}: Select a Theme</h3>

        <insights-theme-selector :active-themes="[activeTheme]"
          :is-key-theme="true"
          :required-selectable="1"
          @select="onSelectTheme"
        />
      </section>

      <insights-key-area-highlighted-title class="info"/>
    </section>

    <section class="footer">
      <base-button class="btn-cancel" colour="light" type="link" @click="onClickCancel">Cancel</base-button>
      <base-button @click="onClickSave">Save</base-button>
    </section>
  </section>
</template>

<script>
import { EditIcon, XIcon } from 'vue-feather-icons';
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import InsightsKeyAreaHighlightedTitle from '@/components/InsightsKeyArea/InsightsKeyAreaHighlightedTitle';
import InsightsThemeSelector from '@/components/Insights/InsightsThemeSelector';
import intercomEvent from '@/services/IntercomEvent';
import intercomEvents from '@/enum/intercom-events';

import { datasetInsightApiV0 } from '@/services/api';

export default {
  name: 'insights-key-area-modal-edit',

  components: {
    BaseButton,
    EditIcon,
    InsightsKeyAreaHighlightedTitle,
    InsightsThemeSelector,
    XIcon,
  },

  data() {
    return {
      activeTheme: null,
    };
  },

  computed: {
    ...mapState('datasetsInsights', [
      'datasetBenchmark',
      'editKeyArea',
      'editKeyAreaTheme',
      'scorecards',
    ]),

    datasetId() {
      return this.datasetBenchmark;
    },

    keyAreas() {
      const scorecard = this.scorecards.find(s => s.datasetId === this.datasetId);

      return scorecard == null ? [] : scorecard.insightsScorecard.decisionReadyInsights.themes;
    },
  },

  created() {
    this.activeTheme = this.editKeyAreaTheme.themeId;
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('datasetsInsights', ['setEditKeyAreaTheme', 'updateScorecard']),

    onClickCancel() {
      this.closeModal();
    },

    async onClickSave() {
      const themes = [...this.keyAreas];

      themes.splice(this.editKeyArea, 1, { ...this.editKeyAreaTheme });

      await datasetInsightApiV0.updateKeyAreas(this.datasetId, themes);

      this.closeModal();

      intercomEvent.send(intercomEvents.EDIT_INSIGHTS_HIGHLIGHT_THEME);

      const scorecard = await datasetInsightApiV0.getScorecard(this.datasetId);

      this.updateScorecard({ scorecard });
    },

    async onSelectTheme(themeList) {
      const [theme] = themeList;

      this.activeTheme = theme;

      const headline = await datasetInsightApiV0.getHeadline(this.datasetId, theme);

      this.setEditKeyAreaTheme({
        theme: {
          themeId: theme,
          headline,
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-key-area-modal-edit {
  @include modal;
  overflow: hidden;
  width: 900px;

  .header {
    @include modal-header;
  }

  .body {
    display: grid;
    grid-template-columns: 50% 50%;
    max-height: 500px;
    overflow: hidden;
    padding: 0;

    .themes-list {
      @include flex("block", "column", "start", "stretch");
      @include stretch;

      background-color: clr('white');
      padding: 1rem 1.5rem;
      overflow: hidden;

      h3 {
        font-size: $font-size-sm;
        margin-bottom: 0.5rem;
      }
    }

    .info {
      border-left: $border-standard;
      padding: 1rem 1.5rem;
    }
  }

  .footer {
    padding-right: 1.5rem;

    .base-button {
      padding: 0.5rem 2rem;

      &.btn-cancel {
        padding-left: 0;
      }
    }
  }
}
</style>
