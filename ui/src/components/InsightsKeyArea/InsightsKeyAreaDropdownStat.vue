<template>
  <section class="insights-key-area-dropdown-stat">
    <span class="stat-header">
      Key Stat Display
    </span>
    <section class="dropdown" v-click-outside-handler="{ handler: 'onClickOutside' }">
      <base-dropdown :component="InsightsKeyAreaDropdownStatItem"
                     :data="dataList"
                     :open="open"
                     :search="false"
                     @select="onSelectOption"
      >
        <section class="selected" @click="open = !open">
          <div class="label">
            <span class="text">{{ textSelected }}</span>
          </div>
          <div class="icon">
            <i class="fa fa-caret-down" :class="{ open }"></i>
          </div>
        </section>
      </base-dropdown>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';
import BaseDropdown from '@/components/Dropdown/BaseDropdown';
import clickOutsideHandler from '@/directives/click-outside-handler';
import InsightsKeyAreaDropdownStatItem from '@/components/InsightsKeyArea/InsightsKeyAreaDropdownStatItem';
import InsightsKeyStatDisplay from '@/enum/insights-key-stat-display';

const modifyOption = type => {
  return type === InsightsKeyStatDisplay.POTENTIAL
    ? 'Potential Change'
    : type.titleCase();
};

export default {
  name: 'insights-key-area-dropdown-stat',

  components: {
    BaseDropdown,
  },

  directives: {
    clickOutsideHandler,
  },

  computed: {
    ...mapGetters('datasetsInsights', [
      'checkThemeKeyStatAvailable',
      'getThemeHighlightedKeyStat',
      'getThemeMetadataOptimise',
    ]),

    ...mapState('datasetsInsights', ['datasetBenchmark', 'editKeyAreaTheme']),

    dataList() {
      return InsightsKeyStatDisplay.enumValues
        .map((t, index) => {
          return {
            available: this.isKeyStatAvailable(t.name),
            content: modifyOption(t),
            index,
            selected: this.isSelected(t.name),
            val: t.name,
          };
        });
    },

    textSelected() {
      const keyStat = this.editKeyAreaTheme.highlightedKeyStat != null
        ? this.editKeyAreaTheme.highlightedKeyStat
        : this.getThemeHighlightedKeyStat(this.datasetBenchmark, this.editKeyAreaTheme.themeId);

      return this.dataList.find(t => t.val === keyStat).content;
    },
  },

  data() {
    return {
      InsightsKeyAreaDropdownStatItem,
      open: false,
    };
  },

  methods: {
    ...mapActions('datasetsInsights', ['setEditKeyAreaTheme']),

    isKeyStatAvailable(keyStat) {
      const rs = this.checkThemeKeyStatAvailable(this.datasetBenchmark, this.editKeyAreaTheme.themeId, keyStat);

      if (keyStat === InsightsKeyStatDisplay.POTENTIAL.name) {
        return rs && Math.round(this.getThemeMetadataOptimise(this.editKeyAreaTheme.themeId)?.optimiseChange * 100);
      }

      return rs;
    },

    isSelected(name) {
      if (this.editKeyAreaTheme.highlightedKeyStat != null) {
        return name === this.editKeyAreaTheme.highlightedKeyStat;
      }
      return name === this.getThemeHighlightedKeyStat(this.datasetBenchmark, this.editKeyAreaTheme.themeId);
    },

    onClickOutside() {
      if (this.open) {
        this.open = false;
      }
    },

    onSelectOption(option) {
      if (option.available) {
        this.setEditKeyAreaTheme({
          theme: {
            ...this.editKeyAreaTheme,
            highlightedKeyStat: option.val,
          },
        });

        this.open = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-key-area-dropdown-stat {
  @include flex("block", "column", "start", "start");
  width: 100%;

  .stat-header {
    font-size: $font-size-xxs;
    font-weight: $font-weight-bold;
    text-transform: uppercase;
  }

  .dropdown {
    cursor: pointer;
    margin-top: 0.5rem;
    width: inherit;

    .base-dropdown {
      background-color: clr('white');

      .selected {
        @include flex("block", "row", "start", "center");
        @include stretch;
        border: 1px solid $border-color;
        border-radius: $border-radius-medium;
        height: 1.8rem;

        .label {
          @include flex("block", "row", "start", "center");
          @include stretch;
          padding: 0 1rem;

          .text {
            @include truncate;
            display: inline-block;
            font-size: $font-size-xs;
            max-width: 210px;
          }
        }

        .icon {
          @include flex("block", "row", "center", "center");
          border-left: 1px solid $border-color;
          height: 1.8rem;
          width: 1.8rem;

          .fa {
            stroke-width: 3px;
            transition: all $interaction-transition-time;

            &.open{
              transform: rotate(180deg);
            }
          }
        }
      }
    }
  }
}
</style>
