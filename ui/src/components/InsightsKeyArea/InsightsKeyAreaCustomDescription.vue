<template>
  <section class="insights-key-area-custom-description">
    <section class="header">
      <span class="header-text">Description</span>
      <section class="btn-reset" @click="onClickReset">
        <i class="fa-solid fa-arrow-rotate-left icon" />
        <span class="text">Reset</span>
      </section>
    </section>
    <textarea rows="4" class="input-textarea input-description" v-model="localDescription" />
    <span class="warning-note" :class="{ 'too-long': descriptionLength > 240 }">
      {{ descriptionLength }} / 240 Character Limit.<span v-if="descriptionLength > 240"> Text over the limit might not be fully visible.</span>
    </span>
  </section>
</template>

<script>

import { mapActions, mapGetters, mapState } from 'vuex';

export default {
  name: 'insights-key-area-custom-description',

  computed: {
    ...mapGetters('datasets', ['themeEmotionPercentage']),

    ...mapGetters('datasetsInsights', [
      'getTheme',
      'getThemeContentEmotionIndex',
      'getThemeHighlightedEmotionIndex',
    ]),

    ...mapState('datasetsInsights', ['datasetBenchmark', 'editKeyAreaTheme']),

    datasetId() {
      return this.datasetBenchmark;
    },

    defaultDescription() {
      return this.getThemeContentEmotionIndex(this.datasetId, this.themeId, this.emoPercentageObj);
    },

    descriptionLength() {
      return this.localDescription?.length || 0;
    },

    emoPercentageObj() {
      const emoIndex = this.editKeyAreaTheme.highlightedEmotionIndex != null
        ? this.editKeyAreaTheme.highlightedEmotionIndex
        : this.getThemeHighlightedEmotionIndex(this.datasetId, this.themeId);

      return this.themeEmotionPercentage(
        this.datasetId,
        this.theme.emotionIndexes[emoIndex],
        emoIndex,
      );
    },

    emotionIndex() {
      return this.editKeyAreaTheme.highlightedEmotionIndex;
    },

    localDescription: {
      get() {
        return this.editKeyAreaTheme?.highlightedDescription || '';
      },
      set(val) {
        this.setEditKeyAreaTheme({
          theme: {
            ...this.editKeyAreaTheme,
            highlightedDescription: val?.replace(/\s\s+/g, ' ') || '',
          },
        });
      },
    },

    theme() {
      return this.getTheme(this.datasetId, this.themeId);
    },

    themeId() {
      return this.editKeyAreaTheme.themeId;
    },
  },

  methods: {
    ...mapActions('datasetsInsights', ['setEditKeyAreaTheme']),

    onClickReset() {
      this.setEditKeyAreaTheme({
        theme: {
          ...this.editKeyAreaTheme,
          highlightedDescription: this.defaultDescription,
        },
      });
    },
  },

  mounted() {
    if (!this.editKeyAreaTheme?.highlightedDescription?.trim().length) {
      this.onClickReset();
    }
  },

  watch: {
    emotionIndex() {
      this.onClickReset();
    },

    themeId() {
      this.onClickReset();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.insights-key-area-custom-description {
  width: 100%;

  .header {
    display: grid;
    grid-template-columns: 50% 50%;
    width: inherit;

    .header-text {
      font-size: $font-size-xxs;
      font-weight: $font-weight-bold;
      text-transform: uppercase;
    }

    .btn-reset {
      @include flex("block", "row", "end", "center");
      color: #5F52C5;
      cursor: pointer;
      font-size: $font-size-xxs;
      font-weight: $font-weight-bold;
      text-transform: uppercase;

      &:hover {
        opacity: 0.8;

        .text {
          text-decoration: underline;
        }
      }

      .icon {
        margin-right: 0.2rem;
        transition: opacity $interaction-transition-time;
      }
    }
  }

  .input-textarea {
    @include scrollbar-thin(8px, 5px);
    border: 1px solid $border-color;
    border-radius: $border-radius-medium;
    color: $body-copy;
    font-size: $font-size-xs;
    margin-top: 0.5rem;
    outline: none;
    padding: 0.5rem 1rem;
    resize: none;
    width: inherit;
  }

  .warning-note {
    font-size: 0.6rem;
    line-height: 1rem;
    opacity: 0.6;

    &.too-long {
      color: clr('red');
    }
  }
}
</style>
