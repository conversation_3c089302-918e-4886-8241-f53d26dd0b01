<template>
  <section class="insights-key-area" :class="{ separate :saveAsSeparate }">
    <section v-if="decisionReadyInsights != null" class="key-area-title">
      <section class="left">
        <i class="fa fa-fw fa-eye icon"></i>
        <h2 class="title">{{ decisionReadyInsights.title }}</h2>
      </section>
      <section v-if="isEditor" class="right">
        <section class="dropdown-btn"
          v-if="!presentation"
          v-tooltip.bottom.end.notrigger="{
            html: 'key-area-dropdown',
            class: 'tooltip-insights-dropdown',
            delay: 0,
            visible: dropdownOpen,
          }"
          @click.stop="dropdownOpen = !dropdownOpen">
          •••
        </section>
        <insights-key-area-dropdown v-if="!presentation" id="key-area-dropdown" @close="dropdownOpen = false"/>
      </section>
    </section>

    <section class="sub-title">
      {{ decisionReadyInsights.description }}
    </section>

    <section v-if="showKeyAreas" class="key-area-list">
      <insights-key-area-item v-for="(item, i) in decisionReadyInsights.themes"
        :key="item.themeId"
        :item="item"
        :index="i"
      />
    </section>

    <section v-else class="loading">
      <loading-blocks-overlay>Loading Key Areas...</loading-blocks-overlay>
    </section>
  </section>
</template>

<script>
import { mapGetters, mapState } from 'vuex';

import InsightsKeyAreaDropdown from '@/components/InsightsKeyArea/InsightsKeyAreaDropdown';
import InsightsKeyAreaItem from '@/components/InsightsKeyArea/InsightsKeyAreaItem';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';

export default {
  name: 'insights-key-area',

  components: {
    InsightsKeyAreaDropdown,
    InsightsKeyAreaItem,
    LoadingBlocksOverlay,
  },

  props: {
    presentation: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      dropdownOpen: false,
    };
  },

  computed: {
    ...mapGetters('datasets', [
      'hasScoreMetadata',
      'isEditable',
      'scoreColumns',
    ]),

    ...mapState('datasetsInsights', [
      'datasetBenchmark',
      'saveAsSeparate',
      'scorecards',
      'themeSets',
      'themesMetadata',
    ]),

    ...mapGetters('datasetsInsights', ['selectedMetadata']),

    decisionReadyInsights() {
      const scorecard = this.scorecards.find(s => s.datasetId === this.datasetBenchmark);

      return scorecard == null ? {} : scorecard.insightsScorecard.decisionReadyInsights;
    },

    isEditor() {
      return this.isEditable(this.datasetBenchmark);
    },

    keyAreaIds() {
      const scorecard = this.decisionReadyInsights;
      const { themes } = scorecard;

      return themes == null ? [] : themes.map(theme => theme.themeId);
    },

    showKeyAreas() {
      return this.decisionReadyInsights != null;
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.insights-key-area {
  @include flex("block", "column", "start", "stretch");
  width: 100%;

  &.separate {
    border-color: darken($border-color, 50%);
    border-radius: $border-radius-medium;
    border-style: dashed;
    border-width: 2px;
    padding: 0.5rem 14px 0;
  }

  .key-area-title {
    @include flex("block", "row", "between", "center");

    margin-bottom: 0.5rem;

    .left {
      @include flex("block", "row", "start", "stretch");

      h2 {
        color: $insights-blue;
        font-size: $font-size-base;
      }

      .icon {
        color: $insights-blue;
        height: $font-size-sm;
        margin-right: 0.7rem;
        width: $font-size-sm;
      }
    }

    .dropdown-btn {
      border: 1px solid $insights-bdr-key;
      border-radius: $border-radius-medium;
      cursor: pointer;
      font-size: $font-size-xs;
      padding: 0.1rem 0.4rem;
      transition: all $interaction-transition-time;
      user-select: none;

      &:hover {
        border-color: $body-copy;
      }
    }
  }

  .sub-title {
    color: $insights-text-dark;
    font-size: $font-size-xs;
    margin-bottom: 1rem;
    margin-top: 0.2rem;
    line-height:1.1rem;
  }

  .key-area-list {
    @include stretch;
  }

  .loading {
    @include flex("block", "row", "center", "center");
    @include stretch;

    margin: 1rem;
  }
}
</style>
