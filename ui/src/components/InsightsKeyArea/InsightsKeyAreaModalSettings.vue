<template>
  <section class="insights-modal-key-area-settings">
    <section class="header">
      <section class="left">
        <edit-icon class="icon"/>
        <h2>Edit Insights Title and Description for {{ datasetName }}</h2>
      </section>
      <section class="right">
        <x-icon class="icon" @click="onCancel"/>
      </section>
    </section>
    <section class="body">
      <section class="settings-item item-title">
        <span class="label">Title</span>
        <textarea class="input input-title" v-model="title" @keydown.enter.prevent.stop="onConfirm"></textarea>
      </section>
      <section class="settings-item item-description">
        <span class="label">Description</span>
        <textarea class="input input-description" v-model="description" @keydown.enter.prevent.stop="onConfirm"></textarea>
      </section>
    </section>
    <section class="footer">
      <base-button class="cancel" colour="light" type="link" @click="onCancel">Cancel</base-button>
      <base-button class="confirm" colour="base" :disabled="hasError" @click="onConfirm">Save</base-button>
    </section>
  </section>
</template>

<script>
import { EditIcon, XIcon } from 'vue-feather-icons';
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import intercomEvent from '@/services/IntercomEvent';
import intercomEvents from '@/enum/intercom-events';

import { datasetInsightApiV0 } from '@/services/api';

export default {
  name: 'insights-modal-key-area-settings',

  components: {
    BaseButton,
    EditIcon,
    XIcon,
  },

  data() {
    return {
      description: '',
      title: '',
    };
  },

  computed: {
    ...mapGetters('datasets', ['get']),

    ...mapState('datasetsInsights', ['datasetBenchmark', 'scorecards']),

    datasetName() {
      return this.get(this.datasetBenchmark).label;
    },

    /**
     * @description currently allows user to leave those inputs empty
     * title & description will be default one
     * @returns {boolean}
     */
    hasError() {
      return false;
    },

    scorecard() {
      return this.scorecards.find(s => s.datasetId === this.datasetBenchmark);
    },
  },

  created() {
    const { decisionReadyInsights } = this.scorecard.insightsScorecard;

    this.title = decisionReadyInsights.title;
    this.description = decisionReadyInsights.description;
  },

  methods: {
    ...mapActions('datasetsInsights', ['updateScorecard']),

    ...mapActions('modal', ['closeModal']),

    onCancel() {
      this.closeModal();
    },

    async onConfirm() {
      if (!this.hasError) {
        await datasetInsightApiV0.updateKeyAreaTitles(
          this.datasetBenchmark,
          this.title,
          this.description,
        );

        intercomEvent.send(intercomEvents.EDIT_INSIGHTS_HIGHLIGHTS_TITLE);

        this.closeModal();

        const scorecard = await datasetInsightApiV0.getScorecard(this.datasetBenchmark);

        this.updateScorecard({ scorecard });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.insights-modal-key-area-settings {
  @include modal;

  width: 600px;

  .body {
    @include flex("block", "column", "start", "stretch");

    overflow: auto;
    width: 100%;

    .settings-item {
      @include flex("block", "column", "start", "stretch");

      margin-bottom: 1rem;

      .input {
        border: 1px solid $border-color;
        border-radius: $border-radius-medium;
        color: $body-copy;
        font-size: $font-size-sm;
        margin-top: 0.2rem;
        outline: none;
        padding: 0.5rem;
        resize: none;

        &.input-description {
          height: 6rem;
        }

        &.input-title {
          height: 4rem;
        }
      }

      .label {
        font-size: $font-size-sm;
        font-weight: $font-weight-bold;
      }
    }
  }

  .footer {
    .cancel {
      margin-left: -0.6rem;
    }

    .confirm {
      &.hasError {
        cursor: not-allowed;
      }
    }
  }

  .header {
    @include modal-header;
  }
}
</style>
