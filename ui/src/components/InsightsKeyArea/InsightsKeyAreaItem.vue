<template>
  <section class="insights-key-area-item">
    <section class="area-left" :class="{ structured: hasStructuredData(datasetId) }">
      <section class="index">
        <span class="index-text">{{ index + 1 }}</span>
      </section>
      <section class="symbol">
        <img :src="symbolText"/>
      </section>
      <section class="content">
        <section class="content-title" :class="{ isStrength, isOpportunity, isThreat, isWeakness }">
          {{ contentTitle }}
        </section>
        <section class="content-text">
          {{ textDescription }}
        </section>
      </section>
    </section>

    <insights-key-stat :theme="theme" :type="keyStatType"/>
  </section>
</template>

<script>
import { kebabCase } from 'lodash-es';
import { mapGetters, mapState } from 'vuex';

import InsightsKeyStat from '@/components/Insights/InsightsKeyStat';
import InsightsKeyStatDisplay from '@/enum/insights-key-stat-display';
import nonSwot from '@/assets/insight/icon-area-of-interest.svg';
import opportunity1 from '@/assets/insight/icon-opportunity-1.svg';
import opportunity2 from '@/assets/insight/icon-opportunity-2.svg';
import strength1 from '@/assets/insight/icon-strength-1.svg';
import strength2 from '@/assets/insight/icon-strength-2.svg';
import threat1 from '@/assets/insight/icon-threat-1.svg';
import threat2 from '@/assets/insight/icon-threat-2.svg';
import weakness1 from '@/assets/insight/icon-weakness-1.svg';
import weakness2 from '@/assets/insight/icon-weakness-2.svg';

export default {
  name: 'insights-key-area-item',

  components: {
    InsightsKeyStat,
  },

  props: {
    index: {
      type: Number,
      required: true,
    },
    item: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapGetters('benchmark', ['classifyAdorescore']),

    ...mapGetters('datasets', ['hasStructuredData', 'themeEmotionPercentage']),

    ...mapGetters('datasetsInsights', [
      'checkThemeKeyStatAvailable',
      'getTheme',
      'getThemeContentEmotionIndex',
      'getThemeHighlightedEmotionIndex',
      'getThemeMetadataOptimise',
    ]),

    ...mapState('datasetsInsights', ['datasetBenchmark']),

    contentTitle() {
      return this.item.headline || '';
    },

    datasetId() {
      return this.datasetBenchmark;
    },

    emoPercentageObj() {
      const emoIndex = this.item.highlightedEmotionIndex != null
        ? this.item.highlightedEmotionIndex
        : this.getThemeHighlightedEmotionIndex(this.datasetId, this.themeId);

      return this.themeEmotionPercentage(
        this.datasetId,
        this.theme.emotionIndexes[emoIndex],
        emoIndex,
      );
    },

    isOpportunity() {
      return this.primaryAttribute === 'opportunity';
    },

    isStrength() {
      return this.primaryAttribute === 'strength';
    },

    isThreat() {
      return this.primaryAttribute === 'threat';
    },

    isWeakness() {
      return this.primaryAttribute === 'weakness';
    },

    keyStatType() {
      if (this.showHighlightedPotential) return InsightsKeyStatDisplay.POTENTIAL;
      if (this.showHighlightedSwot) return InsightsKeyStatDisplay.SWOT;

      return InsightsKeyStatDisplay.ADORESCORE;
    },

    optimiseChange() {
      const optimiseObj = this.getThemeMetadataOptimise(this.themeId);
      return Math.round(optimiseObj?.optimiseChange * 100) || 0;
    },

    primaryAttribute() {
      return this.theme.swot?.attribute?.toLowerCase();
    },

    scoreType() {
      return kebabCase(this.scoreTypeText.toLowerCase());
    },

    scoreTypeText() {
      return this.classifyAdorescore(Math.round(this.theme.polarity * 100)).name;
    },

    showHighlightedPotential() {
      return (this.themeHighlightedKeyStat == null || this.themeHighlightedKeyStat === InsightsKeyStatDisplay.POTENTIAL.name)
        && this.checkThemeKeyStatAvailable(
          this.datasetId,
          this.themeId,
          InsightsKeyStatDisplay.POTENTIAL.name,
        )
        && (this.optimiseChange > 0);
    },

    showHighlightedSwot() {
      return (this.themeHighlightedKeyStat == null || this.themeHighlightedKeyStat === InsightsKeyStatDisplay.SWOT.name)
        && this.checkThemeKeyStatAvailable(
          this.datasetId,
          this.themeId,
          InsightsKeyStatDisplay.SWOT.name,
        );
    },

    symbolText() {
      const rs = this.theme.id % 2;
      if (this.isOpportunity) return rs === 0 ? opportunity2 : opportunity1;
      if (this.isStrength) return rs === 0 ? strength2 : strength1;
      if (this.isThreat) return rs === 0 ? threat2 : threat1;
      if (this.isWeakness) return rs === 0 ? weakness2 : weakness1;
      return nonSwot;
    },

    textDefaultDescription() {
      return this.getThemeContentEmotionIndex(this.datasetId, this.themeId, this.emoPercentageObj);
    },

    textDescription() {
      if (this.item.highlightedDescription?.trim().length) {
        return this.item.highlightedDescription.trim();
      }
      return this.textDefaultDescription;
    },

    theme() {
      return this.getTheme(this.datasetId, this.themeId);
    },

    themeId() {
      return this.item.themeId;
    },

    themeHighlightedKeyStat() {
      return this.item.highlightedKeyStat;
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.insights-key-area-item {
  @include flex("block", "row", "start", "center");

  margin-bottom: 0.8rem;
  width: 100%;

  .area-left {
    @include flex("block", "row", "start", "center");
    @include stretch;

    height: inherit;
    border-radius: 4px 4px 4px 4px;
    padding-right: 0.5rem;

    .content {
      @include flex("block", "column", "center", "start");
      padding-right: 0.3rem;

      .content-text {
        color: $insights-text-dark;
        display: -webkit-box;
        font-size: 0.6rem;
        line-height: 0.8rem;
        margin: 0.2rem 0 0 0;
        overflow: hidden;
        padding: 0 0.5rem 0 0;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
      }

      .content-title {
        @include truncate;
        font-size: $font-size-xs;
        font-weight: $font-weight-bold;
        line-height: 1rem;
        max-width: 490px;

        &.isOpportunity {
          color: $swot-opportunity;

          .edit-btn {
            border: 1px solid $swot-opportunity;
          }
        }

        &.isStrength {
          color: $swot-strength;

          .edit-btn {
            border: 1px solid $swot-strength;
          }
        }

        &.isThreat {
          color: $swot-threat;

          .edit-btn {
            border: 1px solid $swot-threat;
          }
        }

        &.isWeakness {
          color: $swot-weakness;

          .edit-btn {
            border: 1px solid $swot-weakness;
          }
        }

        .edit-btn {
          border-radius: $font-size-xxs;
          cursor: pointer;
          font-size: $font-size-xxs;
          font-weight: bold;
          padding: 0.05rem 0.3rem;

          &:hover {
            opacity: 0.7;
          }
        }
      }
    }

    .index {
      @include flex("block", "column", "center", "center");
      color: $dataimport;
      font-size: $font-size-xs;
      font-weight: $font-weight-medium;
      margin: 0 0.8rem 0 0.2rem;

      .index-text {
        @include flex("block", "column", "center", "center");

        background: $insights-blue;
        border-radius: $border-radius-medium;
        color: clr('white');
        font-size: 0.7rem;
        height: 1rem;
        width: 1rem;
      }
    }

    .symbol {
      @include flex("block", "column", "center", "center");
      font-size: $font-size-xl;
      font-weight: $font-weight-bold;
      margin: 0 1rem 0 0;

      &.isOpportunity {
        color: $swot-opportunity;
      }

      &.isStrength {
        color: $swot-strength;
      }

      &.isThreat {
        color: $swot-threat;
      }

      &.isWeakness {
        color: $swot-weakness;
      }
    }
  }

  .insights-key-stat {
    @include rigid;
  }
}
</style>
