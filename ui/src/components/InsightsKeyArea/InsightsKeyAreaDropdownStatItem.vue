<template>
  <section class="insights-key-area-dropdown-stat-item"
           :class="{ unavailable: !data.available }"
  >
    <i v-if="data.selected" class="fa-solid fa-circle-check icon-check" />
    <span v-else class="icon-circle" />
    <span class="text">{{ textContent }}</span>
  </section>
</template>

<script>
export default {
  name: 'insights-key-area-dropdown-stat-item',

  props: {
    data: {
      type: Object,
      required: true,
    },
  },

  computed: {
    textContent() {
      return this.data.content;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.insights-key-area-dropdown-stat-item {
  cursor: pointer;
  display: grid;
  font-size: $font-size-xs;
  font-weight: $font-weight-normal;
  grid-template-columns: 1.2rem auto;
  min-height: 2.2rem;
  min-width: 11.5rem;
  padding: 0.5rem;

  &:hover:not(.unavailable) {
    .icon-circle {
      border-color: clr("blue");
    }
  }

  &.unavailable {
    cursor: not-allowed;
    opacity: 0.4;
  }

  .icon-check {
    color: clr("blue");
    font-size: 14px;
  }

  .icon-circle {
    border: 2px solid $border-color;
    border-radius: 50%;
    height: 14px;
    width: 14px;
  }

  .text {
    width: 100%;
  }
}
</style>
