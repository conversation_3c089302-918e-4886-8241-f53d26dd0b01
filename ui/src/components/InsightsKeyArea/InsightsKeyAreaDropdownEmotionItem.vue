<template>
  <section class="insights-key-area-dropdown-emotion-item">
    <i v-if="data.selected" class="fa-solid fa-circle-check icon-check" />
    <span v-else class="icon-circle" />
    <span class="text">{{ textContent }}</span>
    <span class="percentage">{{ percentage }}</span>
  </section>
</template>

<script>
export default {
  name: 'insights-key-area-dropdown-emotion-item',

  props: {
    data: {
      type: Object,
      required: true,
    },
  },

  computed: {
    percentage() {
      return this.data.percentage;
    },

    textContent() {
      return this.data.content;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.insights-key-area-dropdown-emotion-item {
  cursor: pointer;
  display: grid;
  font-size: $font-size-xs;
  font-weight: $font-weight-normal;
  grid-template-columns: 1.2rem auto 3rem;
  min-height: 2.2rem;
  min-width: 11.5rem;
  padding: 0.5rem;

  &:hover {
    .icon-circle {
      border-color: clr("blue");
    }
  }

  .icon-check {
    color: clr("blue");
    font-size: 14px;
  }

  .icon-circle {
    border: 2px solid $border-color;
    border-radius: 50%;
    height: 14px;
    width: 14px;
  }

  .text {
    width: 100%;
  }

  .percentage {
    display: inherit;
    justify-content: end;
  }
}
</style>
