<template>
  <section class="insights-key-area-preview">
    <section class="header">Preview</section>
    <section class="body">
      <section class="symbol">
        <img :src="symbolText"/>
      </section>
      <section class="description">
        <section class="description-header" :class="{ isStrength, isOpportunity, isThreat, isWeakness }">
          {{ editKeyAreaTheme.headline }}
        </section>
        <section class="description-text">
          {{ textDescription }}
        </section>
      </section>

      <insights-key-stat :theme="theme" :type="keyStatType"/>
    </section>
  </section>
</template>

<script>
import { mapGetters, mapState } from 'vuex';

import InsightsKeyStat from '@/components/Insights/InsightsKeyStat';
import InsightsKeyStatDisplay from '@/enum/insights-key-stat-display';
import nonSwot from '@/assets/insight/icon-area-of-interest.svg';
import opportunity1 from '@/assets/insight/icon-opportunity-1.svg';
import strength1 from '@/assets/insight/icon-strength-1.svg';
import threat1 from '@/assets/insight/icon-threat-1.svg';
import weakness1 from '@/assets/insight/icon-weakness-1.svg';

export default {
  name: 'insights-key-area-preview',

  components: {
    InsightsKeyStat,
  },

  computed: {
    ...mapState('datasetsInsights', ['datasetBenchmark', 'editKeyAreaTheme']),

    ...mapGetters('datasetsInsights', [
      'checkThemeKeyStatAvailable',
      'getTheme',
      'getThemeMetadataOptimise',
    ]),

    datasetId() {
      return this.datasetBenchmark;
    },

    isOpportunity() {
      return this.primaryAttribute === 'opportunity';
    },

    isStrength() {
      return this.primaryAttribute === 'strength';
    },

    isThreat() {
      return this.primaryAttribute === 'threat';
    },

    isWeakness() {
      return this.primaryAttribute === 'weakness';
    },

    keyStatType() {
      if (this.showHighlightedPotential) return InsightsKeyStatDisplay.POTENTIAL;
      if (this.showHighlightedSwot) return InsightsKeyStatDisplay.SWOT;

      return InsightsKeyStatDisplay.ADORESCORE;
    },

    optimiseChange() {
      const optimiseObj = this.getThemeMetadataOptimise(this.themeId);

      return Math.round(optimiseObj?.optimiseChange * 100) || 0;
    },

    primaryAttribute() {
      return this.theme.swot?.attribute?.toLowerCase();
    },

    showHighlightedPotential() {
      return (this.themeHighlightedKeyStat == null || this.themeHighlightedKeyStat === InsightsKeyStatDisplay.POTENTIAL.name)
        && this.checkThemeKeyStatAvailable(
          this.datasetId,
          this.themeId,
          InsightsKeyStatDisplay.POTENTIAL.name,
        )
        && (this.optimiseChange > 0);
    },

    showHighlightedSwot() {
      return (this.themeHighlightedKeyStat == null || this.themeHighlightedKeyStat === InsightsKeyStatDisplay.SWOT.name)
        && this.checkThemeKeyStatAvailable(
          this.datasetId,
          this.themeId,
          InsightsKeyStatDisplay.SWOT.name,
        );
    },

    symbolText() {
      if (this.isOpportunity) return opportunity1;
      if (this.isStrength) return strength1;
      if (this.isThreat) return threat1;
      if (this.isWeakness) return weakness1;
      return nonSwot;
    },

    textDescription() {
      return this.editKeyAreaTheme?.highlightedDescription?.trim() || '';
    },

    theme() {
      return this.getTheme(this.datasetId, this.themeId);
    },

    themeId() {
      return this.editKeyAreaTheme.themeId;
    },

    themeHighlightedKeyStat() {
      return this.editKeyAreaTheme.highlightedKeyStat;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-key-area-preview {
  @include flex("block", "column", "start", "start");
  width: 100%;

  .header {
    color: #5F52C5;
    font-size: $font-size-xxs;
    font-weight: $font-weight-bold;
    text-transform: uppercase;
  }

  .body {
    display: grid;
    grid-template-columns: 2rem auto 140px;
    margin-top: 0.5rem;
    width: inherit;

    .symbol img {
      height: 25px;
      width: 25px;
    }

    .description {
      @include scrollbar-thin(8px, 5px);
      max-height: 120px;
      overflow-y: auto;
      padding-right: 0.3rem;

      .description-header {
        font-size: $font-size-xs;
        font-weight: $font-weight-bold;

        &.isOpportunity {
          color: $swot-opportunity;
        }

        &.isStrength {
          color: $swot-strength;
        }

        &.isThreat {
          color: $swot-threat;
        }

        &.isWeakness {
          color: $swot-weakness;
        }
      }

      .description-text {
        font-size: $font-size-xs;
        line-height: 1rem;
        margin-top: 0.5rem;
      }
    }
  }
}
</style>
