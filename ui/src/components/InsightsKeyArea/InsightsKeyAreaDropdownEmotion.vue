<template>
  <section class="insights-key-area-dropdown-emotion">
    <span class="emotion-header">
      Highlighted Emotion
    </span>
    <section class="dropdown" v-click-outside-handler="{ handler: 'onClickOutside' }">
      <base-dropdown :component="InsightsKeyAreaDropdownEmotionItem"
                     :data="dataList"
                     :open="open"
                     :search="false"
                     @select="onSelectOption"
      >
        <section class="selected" @click="open = !open">
          <div class="label">
            <span class="text">{{ textSelected }}</span>
          </div>
          <div class="icon">
            <i class="fa fa-caret-down" :class="{ open }"></i>
          </div>
        </section>
      </base-dropdown>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseDropdown from '@/components/Dropdown/BaseDropdown';
import clickOutsideHandler from '@/directives/click-outside-handler';
import indexes from '@/helpers/indexes';
import InsightsKeyAreaDropdownEmotionItem from '@/components/InsightsKeyArea/InsightsKeyAreaDropdownEmotionItem';

export default {
  name: 'insights-key-area-dropdown-emotion',

  components: {
    BaseDropdown,
  },

  directives: {
    clickOutsideHandler,
  },

  computed: {
    ...mapGetters('datasets', ['themeEmotionPercentage']),

    ...mapGetters('datasetsInsights', ['getTheme', 'getThemeHighlightedEmotionIndex']),

    ...mapState('datasetsInsights', ['datasetBenchmark', 'editKeyAreaTheme']),

    dataList() {
      return indexes
        .map(t => {
          return {
            content: t.label,
            index: t.position.api,
            percentage: this.emoPercentage(t.position.api),
            selected: this.isSelected(t.position.api),
          };
        })
        .sort((a, b) => a.index - b.index);
    },

    datasetId() {
      return this.datasetBenchmark;
    },

    textSelected() {
      const emoIndex = this.editKeyAreaTheme.highlightedEmotionIndex != null
        ? this.editKeyAreaTheme.highlightedEmotionIndex
        : this.getThemeHighlightedEmotionIndex(this.datasetBenchmark, this.editKeyAreaTheme.themeId);
      return this.dataList.find(t => t.index === emoIndex).content;
    },

    theme() {
      return this.getTheme(this.datasetId, this.themeId);
    },

    themeId() {
      return this.editKeyAreaTheme.themeId;
    },
  },

  data() {
    return {
      InsightsKeyAreaDropdownEmotionItem,
      open: false,
    };
  },

  methods: {
    ...mapActions('datasetsInsights', ['setEditKeyAreaTheme']),

    emoPercentage(emoIndex) {
      const { gapScore, percent } = this.themeEmotionPercentage(
        this.datasetId,
        this.theme.emotionIndexes[emoIndex],
        emoIndex,
      );

      if (gapScore === 0) {
        return '+0%';
      }

      return `${gapScore > 0 ? '+' : '-'}${percent === '<1' ? '1' : percent}%`;
    },

    isSelected(i) {
      if (this.editKeyAreaTheme.highlightedEmotionIndex != null) {
        return i === this.editKeyAreaTheme.highlightedEmotionIndex;
      }
      return i === this.getThemeHighlightedEmotionIndex(this.datasetBenchmark, this.editKeyAreaTheme.themeId);
    },

    onClickOutside() {
      if (this.open) {
        this.open = false;
      }
    },

    onSelectOption(o) {
      const rs = { ...this.editKeyAreaTheme };
      rs.highlightedEmotionIndex = o.index;
      this.setEditKeyAreaTheme({ theme: rs });

      this.open = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-key-area-dropdown-emotion {
  @include flex("block", "column", "start", "start");
  width: 100%;

  .emotion-header {
    font-size: $font-size-xxs;
    font-weight: $font-weight-bold;
    text-transform: uppercase;
  }

  .dropdown {
    cursor: pointer;
    margin-top: 0.5rem;
    width: inherit;

    .base-dropdown {
      background-color: clr('white');

      .selected {
        @include flex("block", "row", "start", "center");
        @include stretch;
        border: 1px solid $border-color;
        border-radius: $border-radius-medium;
        height: 1.8rem;

        .label {
          @include flex("block", "row", "start", "center");
          @include stretch;
          padding: 0 1rem;

          .text {
            @include truncate;
            display: inline-block;
            font-size: $font-size-xs;
            max-width: 210px;
          }
        }

        .icon {
          @include flex("block", "row", "center", "center");
          border-left: 1px solid $border-color;
          height: 1.8rem;
          width: 1.8rem;

          .fa {
            stroke-width: 3px;
            transition: all $interaction-transition-time;

            &.open{
              transform: rotate(180deg);
            }
          }
        }
      }
    }
  }
}
</style>
