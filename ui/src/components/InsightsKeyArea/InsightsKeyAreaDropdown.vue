<template>
  <section class="insights-key-area-dropdown">
    <h3>Edit Decision Ready Insights</h3>
    <section class="item" @click="onClickEditTitle">
      <edit-2-icon class="icon"/>
      <span class="text">Edit Title and Description</span>
    </section>
    <section class="item" @click="onClickEditItem(0)">
      <edit-2-icon class="icon"/>
      <span class="text">Edit Highlight 1</span>
    </section>
    <section class="item" @click="onClickEditItem(1)">
      <edit-2-icon class="icon"/>
      <span class="text">Edit Highlight 2</span>
    </section>
    <section class="item" @click="onClickEditItem(2)">
      <edit-2-icon class="icon"/>
      <span class="text">Edit Highlight 3</span>
    </section>
  </section>
</template>

<script>
import { Edit2Icon } from 'vue-feather-icons';
import { mapActions, mapGetters, mapState } from 'vuex';

import BlurCloseable from '@/components/Mixins/BlurCloseable';
import InsightsKeyAreaModalEdit from '@/components/InsightsKeyArea/InsightsKeyAreaModalEdit';
import InsightsKeyAreaModalSettings from '@/components/InsightsKeyArea/InsightsKeyAreaModalSettings';
import PreventSampleActionModal from '@/components/Modal/PreventSampleActionModal';

export default {
  name: 'insights-key-area-dropdown',

  components: {
    Edit2Icon,
  },

  mixins: [BlurCloseable],

  computed: {
    ...mapGetters('datasets', ['get']),

    ...mapState('datasetsInsights', ['datasetBenchmark', 'scorecards']),

    dataset() {
      return this.get(this.datasetBenchmark);
    },

    scorecard() {
      return this.scorecards.find(s => s.datasetId === this.datasetBenchmark);
    },

    themes() {
      return this.scorecard?.insightsScorecard?.decisionReadyInsights?.themes;
    },
  },

  methods: {
    ...mapActions('datasetsInsights', ['setEditKeyArea', 'setEditKeyAreaTheme']),

    ...mapActions('modal', ['setModalComponent']),

    onClickEditTitle() {
      if (this.dataset.localSample) {
        this.setModalComponent({ component: PreventSampleActionModal });
      } else {
        this.setModalComponent({ component: InsightsKeyAreaModalSettings });
      }

      this.$emit('close');
    },

    onClickEditItem(index) {
      if (this.dataset.localSample) {
        this.setModalComponent({ component: PreventSampleActionModal });
      } else {
        this.setEditKeyArea({ area: index });
        this.setEditKeyAreaTheme({ theme: { ...this.themes[index] } });
        this.setModalComponent({ component: InsightsKeyAreaModalEdit });
      }

      this.$emit('close');
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-key-area-dropdown {
  @include panel;

  padding: 0.8rem;

  h3 {
    font-size: 0.7rem;
    font-weight: $font-weight-bold;
    padding: 0 0.2rem;
    text-transform: uppercase;
  }

  .item {
    @include flex("block", "row", "start", "center");

    border-radius: $border-radius-medium;
    cursor: pointer;
    padding: 0.2rem 0.4rem;
    margin-top: 0.3rem;

    &:hover {
      background-color: lighten($body-copy, 75%);
    }

    .icon {
      height: $font-size-xs;
      width: $font-size-xs;
    }

    .text {
      font-size: 0.7rem;
      margin-left: 0.4rem;
    }
  }
}
</style>
