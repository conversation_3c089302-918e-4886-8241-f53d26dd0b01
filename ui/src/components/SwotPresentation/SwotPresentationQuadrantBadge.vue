<template>
  <section class="swot-presentation-quadrant-badge"
           :class="[ swot.name ]"
           :style="{ 'min-width': minWidth }"
  >
    {{ swot.name }}
  </section>
</template>

<script>
import Swot from '@/enum/swot';

export default {
  name: 'swot-presentation-quadrant-badge',

  props: {
    minWidth: {
      type: String,
      default: 'fit-content',
    },
    swot: {
      type: Object,
      default: Swot.NONE,
    },
  },

  data() {
    return {
      Swot,
    };
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.swot-presentation-quadrant-badge {
  @include flex("block", "row", "center", "center");
  border-radius: 1rem;
  font-size: $font-size-xxs;
  font-weight: $font-weight-bold;
  padding: 0.1rem 0.3rem 0.15rem;
  text-transform: uppercase;

  &.NONE {
    border: 1px solid $border-color;
  }

  &.OPPORTUNITY {
    color: $swot-opportunity;
    border: 1px solid $swot-opportunity;
  }

  &.STRENGTH {
    color: $swot-strength;
    border: 1px solid $swot-strength;
  }

  &.THREAT {
    color: $swot-threat;
    border: 1px solid $swot-threat;
  }

  &.WEAKNESS {
    color: $swot-weakness;
    border: 1px solid $swot-weakness;
  }
}
</style>
