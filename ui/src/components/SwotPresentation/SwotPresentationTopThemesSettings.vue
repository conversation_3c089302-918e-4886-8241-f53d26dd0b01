<template>
  <section class="swot-presentation-top-themes-settings" v-click-outside-handler="{ handler: 'onClickOutside' }">
    <section class="item" @click="onClick('default')">
      Show top score themes
    </section>
    <section class="item" @click="onClick('custom')">
      Show custom themes
    </section>
  </section>
</template>

<script>
import clickOutsideHandler from '@/directives/click-outside-handler';

export default {
  name: 'swot-presentation-top-themes-settings',

  directives: {
    clickOutsideHandler,
  },

  methods: {
    onClick(val) {
      this.$emit('settingsClick', val);
    },

    onClickOutside() {
      this.$emit('settingsClick');
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.swot-presentation-top-themes-settings {
  @include panel;
  padding: 0.8rem;

  .item {
    @include flex("block", "row", "start", "center");
    border-radius: $border-radius-medium;
    cursor: pointer;
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;

    &:hover {
      background-color: lighten($body-copy, 75%);
    }
  }
}
</style>
