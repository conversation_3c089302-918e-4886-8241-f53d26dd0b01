<template>
  <section class="swot-presentation-count-badge">
    <span class="strong">{{ label }}</span>
    <span class="number">{{ count }}</span>
  </section>
</template>

<script>
export default {
  name: 'swot-presentation-count-badge',

  props: {
    count: {
      type: Number,
      required: true,
    },
    label: {
      type: String,
      required: true,
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.swot-presentation-count-badge {
  @include flex("block", "row", "start", "center");
  align-items: center;
  border: 1px solid $border-color;
  border-radius: 1rem;
  font-size: $font-size-xs;
  padding: 0.1rem 0.3rem 0.15rem;
  width: min-content;

  .strong {
    font-weight: $font-weight-bold;
  }

  .number {
    margin-left: 0.2rem;
  }
}
</style>
