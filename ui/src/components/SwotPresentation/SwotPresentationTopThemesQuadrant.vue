<template>
  <section class="swot-presentation-top-themes-quadrant">
    <section class="title">
      <span class="text" :class="[ swot.name ]">{{ textTitle }}</span>
      <swot-presentation-quadrant-badge :swot="this.swot" />
    </section>
    <section v-if="themeList.length > 0" class="counts">
      <swot-presentation-count-badge class="count-item" label="Themes" :count="themeList.length" />
      <swot-presentation-count-badge class="count-item" label="Subtopics" :count="totalSubtopics" />
      <swot-presentation-count-badge class="count-item" label="Comments" :count="totalComments" />
    </section>
    <section v-if="themeList.length > 0" class="list-header">
      <span class="index">#</span>
      <span class="name">Theme</span>
      <span class="score">A/Score</span>
      <span class="volume">Vol</span>
    </section>
    <swot-presentation-top-themes-item class="list-item"
                                       v-for="(t, i) in themeList.slice(0, 3)"
                                       :index="i"
                                       :key="i"
                                       :theme="t"
    >
    </swot-presentation-top-themes-item>
    <section v-if="themeList.length === 0" class="no-themes">
      No {{ pluralText }} Detected
    </section>
  </section>
</template>

<script>
import { mapGetters, mapState } from 'vuex';

import Swot from '@/enum/swot';
import SwotPresentationCountBadge from '@/components/SwotPresentation/SwotPresentationCountBadge';
import SwotPresentationQuadrantBadge from '@/components/SwotPresentation/SwotPresentationQuadrantBadge';
import SwotPresentationTopThemesItem from '@/components/SwotPresentation/SwotPresentationTopThemesItem';
import { snippetApi } from '@/services/api';

export default {
  name: 'swot-presentation-top-themes-quadrant',

  components: {
    SwotPresentationCountBadge,
    SwotPresentationQuadrantBadge,
    SwotPresentationTopThemesItem,
  },

  props: {
    showThemes: {
      type: String,
      default: 'default',
    },
    swot: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      Swot,
      swotHeaders: {
        OPPORTUNITY: 'Areas to Optimise',
        STRENGTH: 'Areas to Build On',
        THREAT: 'Areas to Eliminate',
        WEAKNESS: 'Areas to Mitigate',
      },
      totalComments: 0,
      totalSubtopics: 0,
    };
  },

  computed: {
    ...mapState('datasets', ['active']),

    ...mapState('themes', ['customThemes', 'themes']),

    ...mapGetters('themes', ['plural']),

    pluralText() {
      return this.plural(this.swot).titleCase();
    },

    textTitle() {
      return this.swotHeaders[this.swot.name];
    },

    themeList() {
      const themes = this.showThemes === 'default' ? this.themes : this.customThemes;
      if (!themes?.length) {
        return [];
      }

      return themes
        .filter(t => !t.parentId && t.swot.attribute === this.swot.name)
        .sort((a, b) => {
          if ([Swot.WEAKNESS, Swot.THREAT].includes(this.swot)) {
            return a.polarity - b.polarity;
          }
          return b.polarity - a.polarity;
        });
    },
  },

  watch: {
    themeList() {
      this.updateCounts();
    },
  },

  async mounted() {
    await this.updateCounts();
  },

  methods: {
    async updateCounts() {
      const subtopicCounts = this.themeList.reduce((acc, t) => {
        acc += t.numOfSubTopics;

        return acc;
      }, 0);

      this.totalSubtopics = subtopicCounts;

      if (this.themeList.length) {
        this.totalComments = await snippetApi.countSnippetsInTopics(this.active, this.themeList.map(t => t.id));
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.swot-presentation-top-themes-quadrant {
  @include flex("block", "column", "start", "stretch");

  .title {
    @include flex("block", "row", "start", "center");

    .text {
      margin-right: 0.5rem;
      font-size: $font-size-sm;
      font-weight: $font-weight-medium;

      &.OPPORTUNITY {
        color: $swot-opportunity;
      }
      &.STRENGTH {
        color: $swot-strength;
      }
      &.THREAT {
        color: $swot-threat;
      }
      &.WEAKNESS {
        color: $swot-weakness;
      }
    }
  }

  .counts {
    @include flex("block", "row", "start", "center");
    margin-top: 0.8rem;

    .count-item {
      margin-right: 1rem;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .list-header {
    border-bottom: 1px solid $border-color;
    display: grid;
    font-size: $font-size-xxs;
    font-weight: $font-weight-bold;
    grid-template-columns: 2rem auto 4rem 2rem;
    margin-top: 0.8rem;
    padding-bottom: 0.5rem;
    text-transform: uppercase;

    .index {
      @include flex("block", "row", "center", "center");
      width: 1rem;
    }
  }

  .list-item {
    margin-top: 0.8rem;
  }

  .no-themes {
    @include flex("block", "row", "center", "center");
    @include stretch;
  }
}
</style>
