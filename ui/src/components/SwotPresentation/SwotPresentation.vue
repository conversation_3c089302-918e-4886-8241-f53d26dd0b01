<template>
  <section class="swot-presentation">
    <section class="header">
      <section class="description">
        Swot for dataset:
        <span class="label">{{ textLabel }}</span>
        •
        Slide {{ page + 1 }}/{{ totalPages }}
      </section>

      <section class="close">
        <x-icon class="icon" @click="onClickClose"/>
      </section>
    </section>

    <section class="content" ref="content">
      <swot-presentation-content :page="page"/>
    </section>

    <section v-if="page > 0" class="scroll scroll-left" @click="changePage(-1)">
      <chevron-left-icon class="icon"/>
    </section>

    <section v-if="page + 1 < totalPages" class="scroll scroll-right" @click="changePage(1)">
      <chevron-right-icon class="icon"/>
    </section>

    <section class="controls">
      <section v-if="page === 0"
        class="toggle-label-btn"
        @click="onClickCustomMarkers"
      >
        <span class="text">Show Custom Markers</span>
        <base-toggle-button :value="showSwotCustomMarker"></base-toggle-button>
      </section>

      <section v-if="page === 0"
        class="toggle-label-btn"
        @click="onClickLabels"
      >
        <span class="text">Show Labels</span>
        <base-toggle-button :value="showSwotLabel"></base-toggle-button>
      </section>

      <section class="download-btn" @click="downloadPage">
        Download Slide (PNG)
        <i class="fa fa-download"></i>
      </section>
    </section>
  </section>
</template>

<script>
import BaseToggleButton from '@/components/Base/BaseToggleButton';
import domToImage from 'dom-to-image';
import { saveAs } from 'file-saver';
import { ChevronLeftIcon, ChevronRightIcon, XIcon } from 'vue-feather-icons';
import { mapActions, mapGetters, mapState } from 'vuex';

import SwotPresentationContent from '@/components/SwotPresentation/SwotPresentationContent';

export default {
  name: 'swot-presentation',

  components: {
    BaseToggleButton,
    ChevronLeftIcon,
    ChevronRightIcon,
    SwotPresentationContent,
    XIcon,
  },

  data() {
    return {
      page: 0,
      totalPages: 2,
    };
  },

  computed: {
    ...mapGetters('datasets', ['get']),

    ...mapState('datasets', ['active']),

    ...mapState('swotChart', {
      showSwotLabel: state => state.chartOptions.showLabel,
      showSwotCustomMarker: state => state.chartOptions.showCustomMarker,
    }),

    textLabel() {
      return this.get(this.active).label;
    },
  },

  mounted() {
    this.setEditQuadrants({ editQuadrants: false });
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('swotChart', [
      'setEditQuadrants',
      'setShowCustomMarker',
      'setShowLabel',
    ]),

    changePage(amount) {
      this.page += amount;
    },

    async downloadPage() {
      const blob = await domToImage.toBlob(this.$refs.content, {
        height: 1200,
        width: 1600,
        style: {
          transform: 'scale(2)',
          transformOrigin: 'top left',
          textRendering: 'optimizeLegibility',
        },
      });

      const label = `${this.textLabel} Swot Presentation - Slide ${this.page + 1}.png`;

      saveAs(blob, label);
    },

    onClickClose() {
      this.closeModal();
    },

    onClickCustomMarkers() {
      this.setShowCustomMarker({ show: !this.showSwotCustomMarker });
    },

    onClickLabels() {
      this.setShowLabel({ show: !this.showSwotLabel });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.swot-presentation {
  display: grid;
  row-gap: 1rem;
  grid-template-columns: 4rem 800px 4rem;
  grid-template-rows: 2rem 600px 2rem;

  .header {
    @include flex("block", "row", "between", "center");
    align-self: end;
    color: clr('white');
    font-size: $font-size-sm;
    grid-area: 1 / 2 / 2 / 3;

    .label {
      font-weight: $font-weight-bold;
    }

    .icon {
      cursor: pointer;
      transition: opacity $interaction-transition-time;

      &:hover {
        opacity: 0.5;
      }
    }
  }

  .scroll {
    align-self: center;
    color: clr('white');

    &.scroll-left {
      grid-area: 2 / 1 / 3 / 2;
    }

    &.scroll-right {
      grid-area: 2 / 3 / 3 / 4;
    }

    .icon {
      cursor: pointer;
      height: 4rem;
      transition: opacity $interaction-transition-time;
      width: 4rem;

      &:hover {
        opacity: 0.5;
      }
    }
  }

  .content {
    @include flex("block", "row", "stretch", "stretch");
    background-color: clr('white');
    border-radius: $border-radius-medium;
    grid-area: 2 / 2 / 3 / 3;
    overflow: hidden;
  }

  .controls {
    @include flex("block", "row", "end", "center");
    justify-self: end;
    grid-area: 3 / 2 / 4 / 3;

    .toggle-label-btn {
      @include flex("block", "row", "end", "center");
      cursor: pointer;
      margin-right: 1rem;

      .text {
        color: clr("white");
        font-size: $font-size-xxs;
        font-weight: $font-weight-medium;
        text-transform: uppercase;
      }
    }

    .download-btn {
      background-color: $presentation-download-btn-bg;
      border-radius: $border-radius-medium;
      color: clr('white');
      cursor: pointer;
      font-size: 0.65rem;
      font-weight: $font-weight-bold;
      padding: 0.4rem 0.8rem;
      text-transform: uppercase;
      transition: opacity $interaction-transition-time;

      &:hover {
        opacity: 0.8;
      }

      .fa {
        margin-left: 0.3rem;
      }
    }
  }
}
</style>
