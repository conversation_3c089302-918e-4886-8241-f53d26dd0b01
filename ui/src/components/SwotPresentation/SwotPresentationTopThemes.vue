<template>
  <section class="swot-presentation-top-themes">
    <section class="border-horizontal"></section>
    <section class="border-vertical"></section>

    <section class="header">
      Top SWOT themes
    </section>

    <section class="settings">
      <section class="dropdown-btn"
               v-if="!presentation"
               v-tooltip.bottom.end.notrigger="{
                 html: 'swot-top-themes-settings',
                 class: 'tooltip-insights-dropdown',
                 delay: 0,
                 visible: dropdownOpen,
               }"
               @click.stop="dropdownOpen = !dropdownOpen">
        •••
      </section>
      <swot-presentation-top-themes-settings v-if="!presentation"
                                             id="swot-top-themes-settings"
                                             @settingsClick="onSettingsClick"/>
    </section>

    <swot-presentation-top-themes-quadrant v-for="(s, i) in swotList"
                                           :class="[`quadrant-${i}`]"
                                           :key="i"
                                           :showThemes="showThemes"
                                           :swot="s">
    </swot-presentation-top-themes-quadrant>
  </section>
</template>

<script>
import SwotPresentationTopThemesQuadrant from '@/components/SwotPresentation/SwotPresentationTopThemesQuadrant';
import SwotPresentationTopThemesSettings from '@/components/SwotPresentation/SwotPresentationTopThemesSettings';
import Swot from '@/enum/swot';

export default {
  name: 'swot-presentation-top-themes',

  components: {
    SwotPresentationTopThemesQuadrant,
    SwotPresentationTopThemesSettings,
  },

  props: {
    presentation: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      dropdownOpen: false,
      showThemes: 'default',
      Swot,
    };
  },

  computed: {
    swotList() {
      return Swot.enumValues.filter(s => s !== Swot.NONE);
    },
  },

  methods: {
    onSettingsClick(val) {
      if (val) {
        this.showThemes = val;
      }
      this.dropdownOpen = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.swot-presentation-top-themes {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 2rem 1fr 1fr;
  height: 100%;
  padding: 0 1rem;

  .border-horizontal {
    border-bottom: 1px solid $border-color;
    grid-area: 1 / 1 / 3 / 3;
    margin: 0 1rem;
  }

  .border-vertical {
    border-right: 1px solid $border-color;
    grid-area: 1 / 1 / 4 / 2;
  }

  .header {
    @include flex("block", "row", "start", "end");
    font-weight: $font-weight-bold;
    grid-area: 1 / 1 / 2 / 2;
    padding-left: 1rem;
  }

  .settings {
    @include flex("block", "row", "end", "end");
    font-weight: $font-weight-bold;
    grid-area: 1 / 2 / 2 / 3;
    padding-right: 1rem;

    .dropdown-btn {
      border: 1px solid $insights-bdr-key;
      border-radius: $border-radius-medium;
      cursor: pointer;
      font-size: $font-size-xs;
      padding: 0.1rem 0.4rem;
      transition: all $interaction-transition-time;
      user-select: none;

      &:hover {
        border-color: $body-copy;
      }
    }
  }

  .quadrant-0{
    grid-area: 2 / 1 / 3 / 2;
    padding: 1rem;
  }

  .quadrant-1 {
    grid-area: 2 / 2 / 3 / 3;
    padding: 1rem;
  }

  .quadrant-2 {
    grid-area: 3 / 1 / 4 / 2;
    padding: 1rem;
  }

  .quadrant-3 {
    grid-area: 3 / 2 / 4 / 3;
    padding: 1rem;
  }
}
</style>
