<template>
  <section class="swot-presentation-content">
    <swot-presentation-chart v-if="page === 0"/>
    <swot-presentation-top-themes v-if="page === 1"/>
  </section>
</template>

<script>
import SwotPresentationChart from '@/components/SwotPresentation/SwotPresentationChart';
import SwotPresentationTopThemes from '@/components/SwotPresentation/SwotPresentationTopThemes';

export default {
  name: 'swot-presentation-content',

  components: {
    SwotPresentationChart,
    SwotPresentationTopThemes,
  },

  props: {
    page: {
      type: Number,
      required: true,
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.swot-presentation-content {
  @include flex("block", "column", "start", "stretch");
  @include stretch;
  overflow: hidden;
}
</style>
