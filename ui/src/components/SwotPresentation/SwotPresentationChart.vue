<template #default>
  <section class="swot-presentation-chart">
    <section class="title">{{selectedTitle}}</section>
    <swot-chart scId="swot-chart-presentation" :presentation="true" />
  </section>
</template>

<script>
import { mapState } from 'vuex';

import CustomChartType from '@/enum/custom-chart-type';
import SwotChart from '@/components/SwotChart/SwotChart';

export default {
  name: 'swot-presentation-chart',

  components: {
    SwotChart,
  },

  computed: {
    ...mapState('themes', ['customChartTitles']),

    selectedTitle() {
      return this.customChartTitles[CustomChartType.SWOT.name];
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.swot-presentation-chart {
  padding: 1rem 1rem 0 1rem;

  .swot-chart {
    height: 600px;
  }

  .title {
    font-weight: $font-weight-bold;
    padding-bottom: 0.5rem;
    padding-left: 2rem;
  }
}
</style>
