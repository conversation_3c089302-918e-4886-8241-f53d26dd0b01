<template>
  <section class="swot-presentation-top-themes-item">
    <common-index-number :index="index + 1" />
    <span class="name">{{ textThemeLabel }}</span>
    <common-adore-score-square :colorBorder="true"
                               :footer="true"
                               :score="themeScore"
                               :signifier="true"
                               :size=2.2>
    </common-adore-score-square>
    <common-adore-score-square :percentage="true"
                               :score="themeVolume"
                               :size=2.2>
    </common-adore-score-square>
  </section>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import CommonAdoreScoreSquare from '@/components/CommonComponent/CommonAdoreScoreSquare';
import CommonIndexNumber from '@/components/CommonComponent/CommonIndexNumber';

export default {
  name: 'swot-presentation-top-themes-item',

  components: {
    CommonAdoreScoreSquare,
    CommonIndexNumber,
  },

  props: {
    index: {
      type: Number,
      required: true,
    },
    theme: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapGetters('datasets', ['themeVolumePercentage']),

    ...mapState('datasets', ['active']),

    textThemeLabel() {
      return this.theme.topicLabel;
    },

    themeScore() {
      return Math.round(this.theme.polarity * 100);
    },

    themeVolume() {
      return this.themeVolumePercentage(this.active, this.theme.numOfDocuments, true);
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.swot-presentation-top-themes-item {
  align-items: center;
  display: grid;
  grid-template-columns: 2rem auto 4rem 2rem;

  .name {
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
    overflow: hidden;
    padding-right: 0.5rem;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
