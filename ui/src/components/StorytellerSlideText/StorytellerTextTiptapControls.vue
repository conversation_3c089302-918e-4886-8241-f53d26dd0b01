<template>
  <section class="storyteller-text-tiptap-controls">
    <section class="buttons">
      <button class="btn" @click.stop="editor.chain().focus().toggleBold().run()">
        <i class="fa-solid fa-bold icon" />
      </button>
    </section>
    <storyteller-text-tiptap-control-text-size v-if="showTextSizeControl" :editor="editor" @minusText="minusText" @plusText="plusText" />
    <storyteller-text-tiptap-control-text-color :editor="editor" />
    <section class="additional-buttons">
      <component v-for="(btn, index) in additionalButtons" class="additional-button" :is="btn" :key="index" />
    </section>
  </section>
</template>

<script>
import { Editor } from '@tiptap/vue-2';

import StorytellerTextTiptapControlTextColor from '@/components/StorytellerSlideText/StorytellerTextTiptapControlTextColor';
import StorytellerTextTiptapControlTextSize from '@/components/StorytellerSlideText/StorytellerTextTiptapControlTextSize';

export default {
  name: 'storyteller-text-tiptap-controls',

  components: {
    StorytellerTextTiptapControlTextColor,
    StorytellerTextTiptapControlTextSize,
  },

  props: {
    additionalButtons: {
      type: Array,
      required: false,
    },
    editor: {
      type: Editor,
      required: true,
    },
    showTextSizeControl: {
      type: Boolean,
      default: true,
    },
  },

  methods: {
    minusText() {
      this.$emit('minusText');
    },

    plusText() {
      this.$emit('plusText');
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-text-tiptap-controls {
  @include flex("block", "row", "start", "center");

  bottom: -3rem;
  position: absolute;
  white-space: nowrap;
  width: fit-content;
  z-index: 999;

  .buttons {
    @include flex("block", "row", "start", "center");

    background-color: #2B333E;
    border-bottom-left-radius: 3px;
    border-right: 1px solid #131C29;
    border-top-left-radius: 3px;
    height: 1.5rem;
  }

  .btn {
    @include flex("block", "row", "center", "center");

    background-color: transparent;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: $font-size-xxs;
    height: 100%;
    padding: 0.4rem 0.6rem;

    &:hover {
      background-color: rgba(19, 28, 41, 0.9);
    }
  }

  .icon {
    color: clr('white');
  }

  .additional-buttons {
    @include flex("block", "row", "start", "center");
  }
}
</style>
