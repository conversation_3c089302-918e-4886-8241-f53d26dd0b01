<template>
  <section v-if="editor" class="storyteller-text-tiptap" :style="style()" v-click-outside-handler-mousedown="{handler: 'onBlur'}">
    <editor-content :editor="editor" @keydown.native="onKeydown"/>
    <storyteller-text-tiptap-controls
      :additional-buttons="additionalButtons"
      :editor="editor"
      :show-text-size-control="showTextSizeControl"
      @minusText="minusText"
      @plusText="plusText"
    />
  </section>
</template>

<script>
import { Editor, EditorContent } from '@tiptap/vue-2';

import clickOutsideHandlerMousedown from '@/directives/click-outside-handler-mousedown';
import Color from '@tiptap/extension-color';
import StarterKit from '@tiptap/starter-kit';
import StorytellerTextTiptapControls from '@/components/StorytellerSlideText/StorytellerTextTiptapControls';
import TextStyle from '@tiptap/extension-text-style';

export default {
  name: 'storyteller-text-tiptap',

  components: {
    EditorContent,
    StorytellerTextTiptapControls,
  },

  directives: {
    clickOutsideHandlerMousedown,
  },

  props: {
    additionalButtons: {
      type: Array,
      required: false,
    },
    initContent: {
      type: String,
      required: true,
    },
    maxCharacters: {
      type: Number,
      required: false,
      default: 0, // Set your desired character limit here
    },
    maxHeight: {
      type: String,
      default: 'none',
    },
    showTextSizeControl: {
      type: Boolean,
      default: true,
    },
    textAlign: {
      type: String,
      required: false,
    },
  },

  data() {
    return {
      editor: null,
    };
  },

  mounted() {
    this.editor = new Editor({
      editorProps: {
        attributes: {
          class: 'custom-editor',
        },
      },
      content: this.initContent,
      extensions: [
        Color,
        StarterKit,
        TextStyle,
      ],
      onUpdate: ({ editor }) => {
        this.$emit('updateText', editor.getHTML());
      },
    });

    this.editor.commands.focus('end');
  },

  beforeUnmount() {
    this.editor.destroy();
  },

  watch: {
    initContent(newContent) {
      if (this.editor) {
        this.editor.commands.setContent(newContent);
      }
    },
  },

  methods: {
    // selectAlign() {
    //   if (this.textAlign === 'start') this.$emit('selectAlign', 'center');
    //   if (this.textAlign === 'center') this.$emit('selectAlign', 'end');
    //   if (this.textAlign === 'end') this.$emit('selectAlign', 'start');
    // },

    minusText() {
      this.$emit('minusText');
    },

    onBlur() {
      this.removeEmptyParagraph();
      this.$emit('blur', this.editor.getHTML());
    },

    onKeydown(e) {
      const { state } = this.editor;
      const currentLength = state.doc.textContent.length;
      const max = this.maxCharacters;

      e.stopPropagation();

      if (!e.shiftKey && e.which === 13) {
        e.preventDefault(); // Prevent the default enter behavior
        this.onBlur();
      }

      if (e.key === 'Escape' || e.key === 'Esc') {
        this.onBlur();
      }

      // Check if the current length exceeds the maximum characters allowed
      if (max > 0 && currentLength >= max && e.key !== 'Backspace' && e.key !== 'Delete') {
        e.preventDefault();
      }
    },

    plusText() {
      this.$emit('plusText');
    },

    removeEmptyParagraph() {
      const { state, view } = this.editor;
      const { doc, tr } = state;
      const lastNode = doc.lastChild;

      if (lastNode && lastNode.type.name === 'paragraph' && lastNode.content.size === 0) {
        const lastPos = doc.content.size - 1;
        tr.delete(lastPos - 1, lastPos);
        view.dispatch(tr);
      }
    },

    style() {
      return {
        '--maxHeight': this.maxHeight,
        '--overflowY': this.maxHeight === 'none' ? 'unset' : 'auto',
      };
    },
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-text-tiptap {
  border-radius: 1px;
  outline-offset: 0.2em;
  outline: 3px solid #3556FF;
  position: relative;
  width: 100%;

  .custom-editor {
    @include scrollbar-thin;

    cursor: text;
    overflow-y: var(--overflowY);
    max-height: var(--maxHeight);

    p {
      margin: 0;
      //text-wrap: balance;
    }
  }

  .ProseMirror-focused {
    outline: none;
  }
}
</style>
