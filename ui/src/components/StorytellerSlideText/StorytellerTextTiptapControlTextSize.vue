<template>
  <section class="storyteller-text-tiptap-control-text-size">
    <button class="btn" @click="minusText">
      <i class="fa-regular fa-minus icon icon-volume" />
    </button>
    <section class="text-symbol">
      <i class="fa-solid fa-text-size icon-text" />
    </section>
    <button class="btn" @click="plusText">
      <i class="fa-regular fa-plus icon icon-volume" />
    </button>
  </section>
</template>

<script>
import { Editor } from '@tiptap/vue-2';

export default {
  name: 'storyteller-text-tiptap-control-text-size',

  props: {
    editor: {
      type: Editor,
      required: true,
    },
  },

  methods: {
    minusText() {
      this.$emit('minusText');
    },

    plusText() {
      this.$emit('plusText');
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-text-tiptap-control-text-size {
  @include flex("block", "row", "start", "center");

  background-color: #2B333E;
  border-right: 1px solid #131C29;
  height: 1.5rem;
  position: relative;

  .btn {
    @include flex("block", "row", "center", "center");

    background-color: transparent;
    border-radius: 3px;
    border: none;
    cursor: pointer;
    font-size: $font-size-xxs;
    height: 100%;
    padding: 0.4rem 0.6rem;

    &:hover {
      background-color: rgba(19, 28, 41, 0.9);
    }
  }

  .text-symbol {
    @include flex("block", "row", "center", "center");

    .icon-text {
      color: clr('white');
      font-size: $font-size-xs;
    }
  }

  .icon-volume {
    color: clr('white');
    font-size: $font-size-xxxs;
  }
}
</style>
