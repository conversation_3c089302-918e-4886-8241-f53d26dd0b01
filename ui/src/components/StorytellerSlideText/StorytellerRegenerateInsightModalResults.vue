<template>
  <section class="storyteller-regenerate-insight-modal-results">
    <section v-for="(text, index) in textList"
      class="item-insight"
      v-html="text"
      :key="index"
      @click="onClickInsight(text)"
    />
    <section class="btn-custom" @click="onClickCustom">
      <span>I want to customise my insight</span>
      <i class="fa-light fa-sparkles" />
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import StorytellerSlideRequest from '@/services/request/StorytellerSlideRequest';

export default {
  name: 'storyteller-regenerate-insight-modal-results',

  props: {
    textList: {
      type: Array,
      required: true,
    },
  },

  computed: {
    ...mapState('storyteller', ['selectedSlide']),
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    onClickInsight(text) {
      this.selectedSlide.slideData.text = text;
      StorytellerSlideRequest.updateSlide();
      this.closeModal();
    },

    onClickCustom() {
      this.$emit('selectCustomInsight');
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-regenerate-insight-modal-results {
  width: 100%;

  .item-insight {
    border-radius: $border-radius-medium;
    border: 1px solid rgba(115, 80, 255, 0.4);
    cursor: pointer;
    font-size: $font-size-sm;
    line-height: 15px;
    margin-top: 1rem;
    padding: 0.6rem 1rem;
    position: relative;
    width: 100%;
    word-break: break-word;

    &:hover, &:focus {
      border: 1px solid rgba(115, 80, 255, 1);
      background-color: rgba(115, 80, 255, 0.1);
    }
  }

  .btn-custom {
    @include flex("block", "row", "center", "center");

    margin-top: 1.4rem;
    width: 100%;

    span {
      cursor: pointer;
      margin-right: 0.2rem;
      text-decoration: underline;
    }

    &:hover {
      color: rgba(115, 80, 255, 1);
    }
  }
}
</style>
