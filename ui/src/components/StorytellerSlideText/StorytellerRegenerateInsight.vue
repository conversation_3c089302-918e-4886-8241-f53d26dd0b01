<template>
  <section class="storyteller-regenerate-insight">
    <button class="btn-regenerate" @click.stop="onClickRegenerate">
      <i class="fa-regular fa-wand-magic-sparkles magic-icon" />
      <span>Regenerate Insight</span>
    </button>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import StorytellerRegenerateInsightModal from '@/components/StorytellerSlideText/StorytellerRegenerateInsightModal';

export default {
  name: 'storyteller-regenerate-insight',

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    onClickRegenerate() {
      this.setModalComponent({ component: StorytellerRegenerateInsightModal });
    },
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-regenerate-insight {
  @include flex("block", "row", "start", "center");

  height: 100%;
  margin-left: 1rem;

  .btn-regenerate {
    @include flex("block", "row", "center", "center");

    background: linear-gradient(180deg, #A739CE 0%, #4E75FF 150%) repeat-y;
    border-radius: 3px;
    border: none;
    color: clr('white');
    cursor: pointer;
    font-size: $font-size-xxs;
    font-weight: $font-weight-bold;
    height: 100%;
    padding: 0.4rem 0.6rem;
    text-transform: uppercase;

    .magic-icon {
      margin-right: 0.2rem;
    }

    &:hover, &:focus {
      background: linear-gradient(180deg, #792BA1 0%, #B35CE0 150%) repeat-y;
    }
  }
}
</style>
