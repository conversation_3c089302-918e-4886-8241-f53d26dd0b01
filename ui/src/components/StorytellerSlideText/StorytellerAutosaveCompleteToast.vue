<template>
  <section class="storyteller-autosave-complete-toast">
    <section class="toast">
      <span class="text">Autosave Complete</span>
    </section>
  </section>
</template>

<script>
import Toast from '@/components/Mixins/Toast';

export default {
  name: 'storyteller-autosave-complete-toast',

  mixins: [Toast],

  data() {
    return {
      count: 3,
      countWatcher: null,
    };
  },

  created() {
    this.countWatcher = setInterval(() => {
      this.count -= 1;
      if (this.count === 0) {
        clearInterval(this.countWatcher);
        this.close();
      }
    }, 1000);
  },

  beforeDestroy() {
    clearInterval(this.countWatcher);
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-autosave-complete-toast {
  @include flex("block", "row", "end", "start");

  .toast {
    @include toast;

    margin-top: 1.3rem;
    background-color: #555C65;
    border-radius: $border-radius-medium;
    padding: 0.5rem 1rem;
  }
}
</style>
