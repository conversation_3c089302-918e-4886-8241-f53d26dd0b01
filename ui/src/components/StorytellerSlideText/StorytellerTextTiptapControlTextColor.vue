<template>
  <section class="storyteller-text-tiptap-control-text-color">
    <button class="btn" @click.stop="open = !open" v-click-outside-handler="{handler: 'onBlur'}">
      <i class="fa-regular fa-palette icon-color" />
      <span class="color-preview">
        <span class="color-preview-inner" :style="{'background-color': color.hex}" />
      </span>
      <span v-if="open" class="select-color-dropdown" @click.stop="onBlur">
        <sketch v-model="color" :preset-colors="presetColours"/>
      </span>
    </button>
  </section>
</template>

<script>
import { Editor } from '@tiptap/vue-2';
import { Sketch } from 'vue-color';

import ClickOutsideHandler from '@/directives/click-outside-handler';

export default {
  name: 'storyteller-text-tiptap-control-text-color',

  components: {
    Sketch,
  },

  directives: {
    ClickOutsideHandler,
  },

  props: {
    editor: {
      type: Editor,
      required: true,
    },
  },

  data() {
    return {
      color: {
        hex: '#000000',
      },
      open: false,
      presetColours: [
        '#FFEE62',
        '#62CC50',
        '#64B656',
        '#EB534C',
        '#FF9531',
        '#888D95',
        '#131C29',
        '#3981F7',
      ],
    };
  },

  computed: {
    textColor() {
      return this.editor.getAttributes('textStyle').color;
    },
  },

  watch: {
    color() {
      this.editor.chain().focus().setColor(this.color.hex).run();
    },

    textColor() {
      this.setColor();
    },
  },

  created() {
    this.setColor();
  },

  methods: {
    onBlur() {
      this.open = false;
    },

    setColor() {
      if (this.textColor) {
        this.color.hex = this.textColor;
      } else {
        this.color.hex = '#000000';
      }
    },
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-text-tiptap-control-text-color {
  @include flex("block", "row", "start", "center");

  background-color: #2B333E;
  border-bottom-right-radius: 3px;
  border-top-right-radius: 3px;
  height: 1.5rem;
  position: relative;
  width: 2.6rem;

  .btn {
    @include flex("block", "row", "space-between", "center");

    background-color: transparent;
    border-radius: 3px;
    border: none;
    cursor: pointer;
    font-size: $font-size-xxs;
    height: 100%;
    padding: 0 0.4rem;
    width: 100%;

    &:hover {
      background-color: rgba(19, 28, 41, 0.9);
    }

    .icon-color {
      color: clr('white');
    }

    .color-preview {
      @include flex("block", "row", "center", "center");

      border-radius: 50%;
      border: 1px solid rgba(255, 255, 255, 0.6);
      height: 13px;
      position: relative;
      width: 13px;

      .color-preview-inner {
        border-radius: 50%;
        border: 1px solid rgba(255, 255, 255, 1);
        height: 100%;
        width: 100%;
      }
    }
  }

  .select-color-dropdown {
    position: absolute;
    right: -97px;
    top: 26px;
    z-index: 999;

    .vc-sketch {
      .vc-sketch-saturation-wrap, .vc-sketch-controls, .vc-sketch-field {
        display: none;
      }

      .vc-sketch-presets {
        padding-top: 0;
        border-top: none;
      }
    }
  }
}
</style>
