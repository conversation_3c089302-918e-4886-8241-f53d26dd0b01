<template>
  <section class="storyteller-slide-text" :style="style" :class="{editable, hovered}">
    <transition mode="out-in" name="fade">
      <section v-if="!editing" class="text" @dblclick="onClickEdit">
        <section v-html="modelValue" class="text-content" />
        <section class="icons" v-if="editable">
          <section class="icon-wrapper edit" @click.stop="onClickEdit"
            v-tooltip.top="{
             content: 'Edit',
             class: 'tooltip-base-dark',
             delay: 0,
            }"
          >
            <i class="fa-light fa-pen icon" />
          </section>
        </section>
      </section>
      <storyteller-text-tiptap v-else
        :additional-buttons="additionalButtons"
        :init-content="modelValue"
        :text-align="textAlign"
        @blur="stopEditing"
        @minusText="minusText"
        @plusText="plusText"
        @selectAlign="selectAlign"
      />
    </transition>
  </section>
</template>

<script>
import ClickOutsideHandler from '@/directives/click-outside-handler';
import StorytellerTextTiptap from '@/components/StorytellerSlideText/StorytellerTextTiptap';

export default {
  name: 'storyteller-slide-text',

  components: {
    StorytellerTextTiptap,
  },

  directives: {
    ClickOutsideHandler,
  },

  props: {
    additionalButtons: {
      type: Array,
      required: false,
    },
    editable: {
      type: Boolean,
      required: true,
    },
    hovered: {
      type: Boolean,
      required: true,
    },
    fontSize: {
      type: String,
      default: '16px',
    },
    modelValue: {
      type: String,
      required: true,
    },
    textAlign: {
      type: String,
      default: 'start',
    },
  },

  data() {
    return {
      editing: false,
    };
  },

  computed: {
    style() {
      return {
        '--textFontSize': this.fontSize,
        '--justify-content': this.textAlign,
      };
    },
  },

  methods: {
    onClickEdit() {
      this.editing = true;
    },

    selectAlign(alignment) {
      this.$emit('selectAlign', alignment);
    },

    minusText() {
      this.$emit('selectVolumeMinus');
    },

    plusText() {
      this.$emit('selectVolumePlus');
    },

    stopEditing(content) {
      this.editing = false;
      if (content !== this.modelValue) {
        this.$emit('stopEditing', content);
      }
    },
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-text {
  @include flex("block", "row", "start", "center");

  font-size: var(--textFontSize);
  text-align: var(--justify-content);
  justify-content: var(--justify-content);

  strong {
    font-weight: 600;
  }

  .text {
    @include flex("block", "row", "start", "center");

    position: relative;
    width: fit-content;
    word-break: break-word;

    .text-content {
      p {
        margin: 0;
      }
    }

    .icons {
      @include flex("block", "row", "center", "center");

      height: 100%;
      margin-left: 0.3rem;
      visibility: hidden;
    }

    .icon-wrapper {
      @include flex("block", "row", "center", "center");

      border-radius: 50%;
      border: 1px solid #3981F7;
      color: #3981F7;
      cursor: pointer;
      font-size: 25px;
      height: 25px;
      position: relative;
      width: 25px;

      .icon {
        font-size: 12px;
      }

      &:hover {
        background-color: rgba(57, 129, 247, 0.3);
      }
    }
  }

  &.editable {
    &.hovered {
      .icons {
        visibility: visible;
      }
    }
  }
}

.fade-enter-active {
  transition: outline-width 0.1s ease;
}

.fade-enter, .fade-leave-to
  /* .component-fade-leave-active below version 2.1.8 */ {
  outline-width: 0;
}
</style>
