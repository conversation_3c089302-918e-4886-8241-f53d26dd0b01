<template>
  <section class="storyteller-regenerate-insight-modal">
    <section class="header">
      <section class="header-title">
        <i class="fa-solid fa-wand-magic-sparkles icon-magic" />
        <span class="text-title">Regenerate Insight</span>
      </section>
      <i class="fa-light fa-xmark x-icon" @click="closeModal" />
    </section>
    <section class="body">
      <section class="ai-chat">
        <span class="bot-message" v-html="botMessage" />
        <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M0 10L10 0V10H0Z" fill="#F0F0F0"/>
        </svg>
      </section>
      <storyteller-regenerate-insight-modal-options
        v-if="viewing === 'OPTIONS_VIEW'"
        @selectCustomInsight="selectCustomInsight"
        @selectNewInsight="selectNewInsight"
        @selectRevert="selectRevert"
      />
      <loading-dots class="loading" v-else-if="viewing === 'LOADING'" />
      <storyteller-regenerate-insight-modal-results
        v-else-if="viewing === 'RESULTS_VIEW'"
        :text-list="textList"
        @selectCustomInsight="selectCustomInsight"
      />
      <storyteller-regenerate-insight-modal-custom
        v-else-if="viewing === 'CUSTOM_VIEW'"
        @submitCustom="submitCustom"
      />
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import LoadingDots from '@/components/LoadingDots';
import StorytellerRegenerateInsightModalCustom from '@/components/StorytellerSlideText/StorytellerRegenerateInsightModalCustom';
import StorytellerRegenerateInsightModalOptions from '@/components/StorytellerSlideText/StorytellerRegenerateInsightModalOptions';
import StorytellerRegenerateInsightModalResults from '@/components/StorytellerSlideText/StorytellerRegenerateInsightModalResults';
import StorytellerSlideRequest from '@/services/request/StorytellerSlideRequest';

export default {
  name: 'storyteller-slide-themes-insights-modal',

  components: {
    LoadingDots,
    StorytellerRegenerateInsightModalCustom,
    StorytellerRegenerateInsightModalOptions,
    StorytellerRegenerateInsightModalResults,
  },

  data() {
    return {
      botMessage: 'Hey there! 👋 What would you like to do?',
      textList: [],
      viewing: 'OPTIONS_VIEW', // viewing should be OPTIONS_VIEW, LOADING, RESULTS_VIEW, CUSTOM_VIEW
    };
  },

  computed: {
    ...mapGetters('storyteller', ['slideThemesInsights']),

    ...mapState('storyteller', ['selectedSlide']),

    themeName() {
      const theme = this.slideThemesInsights.slideData.insightThemesData.find(t => t.topicId === this.selectedSlide.slideData.themeId);
      return theme.insightsTopicsModel.topicLabel;
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    selectCustomInsight() {
      this.botMessage = 'You Selected: <b>Customise Insight.</b> Can you describe what you want from the insight in more detail?';
      this.viewing = 'CUSTOM_VIEW';
    },

    async selectNewInsight() {
      this.viewing = 'LOADING';
      this.botMessage = 'You Selected: <b>Generate New Insight.</b> <br>Give me a moment... ⏰';
      this.textList = await StorytellerSlideRequest.generateAiText('');
      this.botMessage = `Here are 3 insight suggestions based on comments for <b>${this.themeName}</b>. Select one to add it to your presentation <i class="fa-regular fa-arrow-down" />`;
      this.viewing = 'RESULTS_VIEW';
    },

    async selectRevert() {
      this.viewing = 'LOADING';
      await StorytellerSlideRequest.revertText();
      this.closeModal();
    },

    async submitCustom(input) {
      this.viewing = 'LOADING';
      this.botMessage = 'Thanks! Please give me a moment ⏰ I’m coming up with some new suggestions.';
      this.textList = await StorytellerSlideRequest.generateAiText(input);
      this.botMessage = `Here are 3 insight suggestions based on comments for <b>${this.themeName}</b>. Select one to add it to your presentation <i class="fa-regular fa-arrow-down" />`;
      this.viewing = 'RESULTS_VIEW';
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-regenerate-insight-modal {
  @include panel;

  color: $nps-blue;
  font-family: Inter, serif;
  min-height: 290px;
  padding: 1.5rem 2rem 2rem 2rem;
  position: relative;
  width: 450px;

  .header {
    @include flex("block", "row", "space-between", "center");

    margin-bottom: 1.5rem;
    width: 100%;

    .header-title {
      @include flex("block", "row", "start", "center");

      background: linear-gradient(180deg, #A43CD1, #705FED) repeat-y;
      font-size: 19px;
      font-weight: $font-weight-bold;
      width: 100%;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;

      .icon-magic {
        margin-right: 0.4rem;
      }
    }

    .x-icon {
      cursor: pointer;
      font-size: $font-size-lg;
    }
  }

  .body {
    @include flex("block", "column", "start", "start");

    font-size: $font-size-sm;
    width: 100%;

    .ai-chat {
      background-color: #F0F0F0;
      border-radius: $border-radius-medium;
      padding: 0.6rem 1rem;
      position: relative;
      width: 100%;

      .bot-message {
        line-height: 15px;
        width: 100%;
      }

      svg {
        bottom: 10px;
        left: -10px;
        position: absolute;
      }
    }

    .loading {
      margin: 3rem auto;
      width: 100%;
    }
  }
}
</style>
