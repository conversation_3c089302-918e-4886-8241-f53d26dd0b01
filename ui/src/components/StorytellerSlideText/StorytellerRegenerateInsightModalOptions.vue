<template>
  <section class="storyteller-regenerate-insight-modal-options">
    <section class="btn-insight" @click="onClickGenerateNewInsight">
      Generate New Insight
    </section>
    <section class="btn-insight" @click="onClickCustomiseInsight">
      Customise Insight
    </section>
    <section class="btn-revert" @click="onClickRevert">
      <span>Revert to default insight</span>
      <i class="fa-regular fa-arrows-rotate-reverse" />
    </section>
  </section>
</template>

<script>
export default {
  name: 'storyteller-regenerate-insight-modal-options',

  methods: {
    onClickCustomiseInsight() {
      this.$emit('selectCustomInsight');
    },

    onClickGenerateNewInsight() {
      this.$emit('selectNewInsight');
    },

    onClickRevert() {
      this.$emit('selectRevert');
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-regenerate-insight-modal-options {
  width: 100%;

  .btn-insight {
    @include flex("block", "row", "center", "center");

    border-radius: $border-radius-medium;
    border: 1px solid rgba(115, 80, 255, 0.3);
    color: #472EE4;
    cursor: pointer;
    font-weight: $font-weight-bold;
    margin-top: 1.2rem;
    padding: 0.6rem 1rem;
    width: 100%;

    &:hover, &:focus {
      border: 1px solid rgba(115, 80, 255, 1);
      background-color: rgba(115, 80, 255, 0.1);
    }
  }

  .btn-revert {
    @include flex("block", "row", "center", "center");

    margin-top: 1.4rem;
    width: 100%;

    span {
      cursor: pointer;
      margin-right: 0.2rem;
      text-decoration: underline;
    }

    &:hover {
      color: rgba(115, 80, 255, 1);
    }
  }
}
</style>
