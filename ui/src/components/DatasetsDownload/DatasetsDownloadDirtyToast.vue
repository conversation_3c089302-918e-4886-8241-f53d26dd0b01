<template>
  <section class="datasets-download-dirty-toast">
    <section class="left">
      <span class="text">
        Cannot download Analysis of Dataset which has pending-changes. Please de-select or refresh it to continue!
      </span>
    </section>
    <section class="right">
      <base-button class="dismiss" colour="light" @click="onDismiss">Dismiss</base-button>
    </section>
  </section>
</template>

<script>
import { mapActions } from 'vuex';
import BaseButton from '@/components/Base/BaseButton';
import Toast from '@/components/Mixins/Toast';

export default {
  name: 'datasets-download-dirty-toast',

  components: {
    BaseButton,
  },

  mixins: [Toast],

  methods: {
    ...mapActions('toast', ['remove']),

    onDismiss() {
      this.remove({ id: 'datasets-dirty-download' });
      this.close();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.datasets-download-dirty-toast {
  @include toast;

  background-color: darken(clr("yellow"), 10%);
}
</style>
