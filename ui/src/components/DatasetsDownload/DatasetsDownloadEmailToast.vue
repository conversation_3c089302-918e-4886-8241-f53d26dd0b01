<template>
  <section class="datasets-download-email-toast">
    <section class="left">
      <span class="text">
        We are generating your analysis. You will receive a download link in your email shortly!
      </span>
    </section>
    <section class="right">
      <base-button class="dismiss" colour="light" @click="onDismiss">Dismiss</base-button>
    </section>
  </section>
</template>

<script>
import { mapActions } from 'vuex';
import BaseButton from '@/components/Base/BaseButton';
import Toast from '@/components/Mixins/Toast';

export default {
  name: 'datasets-download-email-toast',

  components: {
    BaseButton,
  },

  mixins: [Toast],

  methods: {
    ...mapActions('toast', ['remove']),

    onDismiss() {
      this.remove({ id: 'datasets-email-download' });
      this.close();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.datasets-download-email-toast {
  @include toast;

  background-color: darken(clr("blue"), 15%);
}
</style>
