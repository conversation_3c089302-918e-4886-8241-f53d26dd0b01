<template>
  <section class="user-workspace-avatar">
    <img v-if="hasAvatar" :src="memberItem.avatar">

    <img v-if="!hasAvatar && gravatar" :src="gravatar">

    <i v-else class="fa-solid fa-user icon-avatar"></i>
  </section>
</template>

<script>
import api from '@/helpers/api';
import md5 from 'crypto-js/md5';

export default {
  name: 'user-workspace-avatar',

  components: {
  },

  props: {
    memberItem: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      gravatar: false,
    };
  },

  computed: {
    hasAvatar() {
      return this.memberItem.avatar != null;
    },

    hash() {
      return md5(this.memberItem.email).toString();
    },
  },

  async created() {
    try {
      const image = await api.instanceExternal().get(`https://www.gravatar.com/avatar/${this.hash}?d=404&s=100`);
      this.gravatar = image.config.url;
    } catch (error) {
      this.gravatar = false;
    }
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.user-workspace-avatar {
  img {
    border-radius: 50%;
    height: 26px;
    width: 26px;
  }

  .icon-avatar {
    @include flex("block", "row", "center", "center");

    background-color: rgba(40, 0, 155, 0.1);
    border-radius: 50%;
    color: #28009B;
    font-size: 12px;
    height: 26px;
    width: 26px;
  }
}
</style>
