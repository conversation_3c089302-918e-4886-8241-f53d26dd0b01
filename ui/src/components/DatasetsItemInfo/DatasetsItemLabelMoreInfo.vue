<template>
  <section class="datasets-item-label-more-info">
    <section v-if="datasetOwner && datasetOwner.id" class="dataset-owner">
      <i class="fa-thin fa-user icon" />
      <span class="text">{{ textOwnerName }}</span>
      <i class="fa-thin fa-circle separator-icon"></i>
    </section>
    <section class="comments">
      <i class="fa-thin fa-comment icon" />
      <span class="text">{{ datasetCommentCount }}</span>
    </section>
  </section>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: 'datasets-item-label-more-info',

  props: {
    dataset: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapGetters('workspaces', ['getMembersFromWorkspaceByIds']),

    datasetCommentCount() {
      return this.dataset.documentCount || 0;
    },

    datasetOwner() {
      return this.getMembersFromWorkspaceByIds([this.dataset.userId])[0] || null;
    },

    textOwnerName() {
      const rs = [];
      if (this.datasetOwner?.firstName?.trim().length) {
        rs.push(this.datasetOwner.firstName.trim());
      }
      if (this.datasetOwner?.lastName?.trim().length) {
        rs.push(this.datasetOwner.lastName.trim());
      }
      return rs.join(' ');
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.datasets-item-label-more-info {
  @include flex("block", "row", "start", "center");
  @include stretch;

  color: #2D1757;
  font-size: 11px;
  font-weight: $font-weight-normal;
  width: 100%;

  .dataset-owner {
    @include flex("block", "row", "start", "center");
    margin-right: 0.5rem;

    .text {
      @include truncate;
      margin: 0 0.5rem 0 0.2rem;
      max-width: 180px;
    }

    .separator-icon {
      font-size: 4px;
    }
  }

  .comments {
    @include flex("block", "row", "start", "center");

    .text {
      margin-left: 0.2rem;
    }
  }
}
</style>
