<template>
  <section class="datasets-item-label" @mouseenter="hover = true" @mouseleave="hover = false">
    <base-input v-if="showRenaming"
      v-model="label"
      :disabled="dataset.localSample"
      :focus="true"
      class="label-input"
      ref="labelinput"
      @blur="onLabelBlur"
      @keydown.native.enter="onLabelBlur"
      @focus="onLabelFocus"
    />
    <span v-else
      class="label-text"
      :class="{ active }"
      v-tooltip.top="{
        class: 'tooltip-dataset-name',
        content: label,
        delay: 0,
        visible: visible,
      }"
      @click="onClickDataset"
      @mouseenter="onMouseOverName"
    >
      {{ textDatasetLabel }}
    </span>
    <icon-rename class="icon" v-if="isEditable(id) && !showRenaming && (hover || activeSummary)"
      @click.stop="onClickRename"
      v-tooltip.top="{
        class: 'tooltip-base-dark',
        content: 'Edit Dataset Name',
        delay: 0,
      }"
    />
    <icon-summary class="icon" v-if="isEditable(id) && !showRenaming && (hover || activeSummary)"
      :active="activeSummary"
      @click.stop="onClickSummary"
      v-tooltip.top="{
        class: 'tooltip-base-dark',
        content: 'Dataset Summary',
        delay: 0,
      }"
    />
  </section>
</template>

<script>
import { v4 as uuid } from 'uuid';
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseInput from '@/components/Base/BaseInput';
import IconRename from '@/components/Icons/IconRename';
import IconSummary from '@/components/Icons/IconSummary';
import Route from '@/enum/route';
import ToastError from '@/components/Toasts/ToastError';

import { datasetApi } from '@/services/api';
import { datasetsRequestV0 } from '@/services/request';

export default {
  name: 'datasets-item-label',

  components: {
    BaseInput,
    IconRename,
    IconSummary,
  },

  props: {
    id: {
      type: Number,
      required: true,
    },
  },

  data() {
    return {
      hover: false,
      label: '',
      labelFocussed: false,
      showRenaming: false,
      visible: false,
    };
  },

  computed: {
    ...mapGetters('datasets', ['get', 'isEditable']),

    ...mapState('datasets', ['selected', 'summaryId']),

    ...mapState('layout', ['showContainerDatasetSummary']),

    active() {
      return !this.dataset.pendingChanges && this.selected.includes(this.id);
    },

    activeSummary() {
      return this.showContainerDatasetSummary && this.summaryId === this.id;
    },

    dataset() {
      return this.get(this.id);
    },

    textDatasetLabel() {
      return this.label.trim() || 'Dataset Label';
    },
  },

  mounted() {
    this.label = this.dataset.label?.trim() || '';
  },

  watch: {
    dataset() {
      this.label = this.dataset.label?.trim() || '';
    },
  },

  methods: {
    ...mapActions('datasets', [
      'select',
      'setActive',
      'setSummaryId',
      'updateDatasetLabel',
      'updateOverviewLabel',
    ]),

    ...mapActions('datasetsInsights', ['setShowInsights']),

    ...mapActions('layout', ['setShowContainerDatasetSummary']),

    ...mapActions('snippets', {
      resetSnippet: 'reset',
    }),

    ...mapActions('toast', {
      addToast: 'add',
      removeToast: 'remove',
    }),

    onClickDataset() {
      this.resetSnippet();
      if (!this.selected.includes(this.id)) {
        this.select({ ids: [this.id] });
      }
      this.setActive({ id: this.id });
      this.$router.push({
        name: Route.COMMENTS,
        query: {
          ids: this.selected.join(','),
        },
      });
    },

    onClickRename() {
      this.showRenaming = !this.showRenaming;
    },

    onClickSummary() {
      this.setShowInsights({ value: false });

      if (this.activeSummary) {
        this.setShowContainerDatasetSummary({ value: false });
      } else {
        this.setSummaryId({ id: this.id });
        this.setShowContainerDatasetSummary({ value: true });
      }
    },

    async onLabelBlur() {
      if (this.$refs.labelinput.value?.trim() !== this.dataset.label?.trim()) {
        const result = await datasetApi.rename(this.id, this.$refs.labelinput.value.trim());

        if (result.error != null) {
          this.label = this.dataset.label?.trim() || '';
          const toastId = uuid();
          this.addToast({
            toast: {
              id: toastId,
              component: ToastError,
              message: 'Sorry - there was an error and we couldn\'t rename your dataset.',
            },
          });
          setTimeout(() => this.removeToast({ id: toastId }), 5000);
        } else {
          this.updateDatasetLabel({ id: this.id, label: this.$refs.labelinput.value.trim() });
          this.updateOverviewLabel({ id: this.id, label: this.$refs.labelinput.value.trim() });
        }

        await datasetsRequestV0.reloadSelected();
      }
      this.labelFocussed = false;
      this.showRenaming = false;
    },

    onLabelFocus() {
      this.labelFocussed = true;
    },

    onMouseOverName(e) {
      this.visible = e.target.offsetHeight + 1 < e.target.scrollHeight;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.datasets-item-label {
  @include flex("block", "row", "start", "center");

  height: 32px;

  .label-input {
    background-color: transparent;
    border: none;
    border-bottom: $border-light solid transparent;
    border-radius: 0;
    color: #2D1757;
    font-size: $font-size-sm;
    font-weight: $font-weight-normal;
    padding: 0;
    transition: all $interaction-transition-time;
    width: 280px;

    &:hover:not(:disabled),
    &:focus {
      border-bottom: $border-light solid clr("purple");
    }

    &:focus,
    &:active {
      outline: none;
    }

    &:disabled {
      background-color: transparent;
    }
  }

  .label-text {
    @include truncate;
    color: #2D1757;
    cursor: pointer;
    font-size: $font-size-sm;
    font-weight: $font-weight-bold;
    max-width: 280px;
    word-wrap: break-word;

    @supports (-webkit-line-clamp: 1) {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: initial;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
    }

    &.active {
      color: $selection-blue-dark;
      font-weight: $font-weight-bold;
    }

    &:hover {
      color: $selection-blue-dark;
      text-decoration: underline;
    }
  }

  .icon {
    margin-left: 0.5rem;
  }
}
</style>
