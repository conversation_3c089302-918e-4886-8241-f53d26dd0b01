<template>
  <section class="datasets-item-filter-view-count" @click="onClickFilter" :class="{expanded}">
    <i class="fa-solid fa-filter icon-filter"></i>
    <span class="text">{{dataset.filterViewCount}}</span>
    <i class="fa-solid fa-caret-right icon-caret"></i>
  </section>
</template>

<script>
import { mapActions } from 'vuex';
import { metadataRequest } from '@/services/request';

export default {
  name: 'datasets-item-filter-view-count',

  props: {
    dataset: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      expanded: false,
    };
  },

  methods: {
    ...mapActions('datasets', ['addFilterViewsIntoDatasetById']),

    async onClickFilter() {
      if (this.dataset.filterViews?.length > 0) {
        this.addFilterViewsIntoDatasetById({ id: this.dataset.id, filterViews: null });
      } else {
        await metadataRequest.getFilterViews(this.dataset.id);
      }

      this.expanded = this.dataset.filterViews?.length > 0;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.datasets-item-filter-view-count {
  @include flex("block", "row", "start", "center");

  border-radius: $border-radius-small;
  border: $border-light solid rgba(84, 75, 131, 0.3);
  color: #544B83;
  cursor: pointer;
  font-size: $font-size-xxs;
  padding: 0.3rem 0.5rem;
  transition: all $interaction-transition-time;

  &:hover {
    border: $border-light solid rgba(84, 75, 131, 1);
  }

  &.expanded {
    background-color: #544B83;
    color: clr('white');

    .icon-caret {
      transform: rotate(90deg);
    }
  }

  .text {
    padding: 0 0.2rem;
  }
}
</style>
