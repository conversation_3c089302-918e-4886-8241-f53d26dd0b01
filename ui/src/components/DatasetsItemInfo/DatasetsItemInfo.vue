<template>
  <section class="datasets-item-info-count">
    <section class="info">
      <datasets-item-label :id="dataset.id" />
      <datasets-item-label-more-info :dataset="dataset" />
    </section>
    <datasets-item-filter-view-count v-if="dataset.filterViewCount" :dataset="dataset" />
  </section>
</template>

<script>
import DatasetsItemFilterViewCount from '@/components/DatasetsItemInfo/DatasetsItemFilterViewCount';
import DatasetsItemLabel from '@/components/DatasetsItemInfo/DatasetsItemLabel';
import DatasetsItemLabelMoreInfo from '@/components/DatasetsItemInfo/DatasetsItemLabelMoreInfo';

export default {
  name: 'datasets-item-info',

  components: {
    DatasetsItemFilterViewCount,
    DatasetsItemLabel,
    DatasetsItemLabelMoreInfo,
  },

  props: {
    dataset: {
      type: Object,
      required: true,
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.datasets-item-info-count {
  @include flex("block", "row", "start", "center");

  width: 100%;

  .info {
    width: 100%;
  }
}
</style>
