<template>
  <section class="storyteller-selected-themes-action-plans-list">
    <draggable :disabled="!isEditor" class="draggable" v-model="selectedThemesActionPlans">
      <storyteller-selected-themes-list-item
        v-for="(item, index) in selectedThemesActionPlans"
        :active="isActive(item.insightsTopicsModel)"
        :key="item.topicId"
        :order="index+1"
        :theme="item.insightsTopicsModel"
        @click="onClickTheme"
      />
    </draggable>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import draggable from 'vuedraggable';
import StorytellerSelectedThemesListItem from '@/components/StorytellerSelectedThemes/StorytellerSelectedThemesListItem';
import StorytellerSlideType from '@/enum/storyteller-slide-type';
import StorytellerActionPlansRequest from '@/services/request/StorytellerActionPlansRequest';

export default {
  name: 'storyteller-selected-themes-action-plans-list',

  components: {
    draggable,
    StorytellerSelectedThemesListItem,
  },

  computed: {
    ...mapGetters('datasets', ['isEditable']),

    ...mapState('datasets', {
      datasetId: 'active',
    }),

    ...mapGetters('storyteller', ['activeSlides', 'slideThemesInsights']),

    ...mapState('storyteller', ['activeReport', 'selectedSlide']),

    ...mapState('storytellerActionPlans', ['selectedThemeId']),

    isEditor() {
      return this.isEditable(this.datasetId);
    },

    selectedThemesActionPlans: {
      get() {
        const { insightThemesData } = this.slideThemesInsights.slideData;
        const { actionPlanThemeIds } = this.activeReport;

        return insightThemesData
          .filter(data => actionPlanThemeIds.includes(data.insightsTopicsModel.id))
          .sort((a, b) => {
            return actionPlanThemeIds.indexOf(a.insightsTopicsModel.id) - actionPlanThemeIds.indexOf(b.insightsTopicsModel.id);
          });
      },
      set(value) {
        const themeIds = value.map(item => item.insightsTopicsModel.id);
        this.activeReport.actionPlanThemeIds = themeIds;
        StorytellerActionPlansRequest.reorderThemes(themeIds);
      },
    },
  },

  methods: {
    ...mapActions('storyteller', ['selectSlide']),

    ...mapActions('storytellerActionPlans', ['selectThemeId']),

    isActive(theme) {
      return StorytellerSlideType.isActionPlansSlide(this.selectedSlide.slideType) && this.selectedThemeId === theme.id;
    },

    onClickTheme(theme) {
      this.selectThemeId({ themeId: theme.id });

      const selectedSlide = this.activeSlides
        .filter(s => StorytellerSlideType.isActionPlansSlide(s.slideType))
        .find(s => s.slideData.actions.some(action => action.themeId === theme.id));

      if (selectedSlide) this.selectSlide({ slide: selectedSlide });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-selected-themes-action-plans-list {
  @include flex("block", "column", "start", "start");

  margin-top: 0.6rem;
  width: 100%;

  .draggable {
    width: 100%;
  }
}
</style>
