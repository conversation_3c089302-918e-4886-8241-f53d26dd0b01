<template>
  <section class="storyteller-selected-themes-loading">
    <storyteller-selected-themes-action :count="count" />
    <storyteller-selected-themes-list-item
      v-for="(item, index) in defaultThemes"
      :key="item.id"
      :order="index+1"
      :theme="item"
    />
  </section>
</template>

<script>
import { mapState } from 'vuex';

import StorytellerSelectedThemesAction from '@/components/StorytellerSelectedThemes/StorytellerSelectedThemesAction';
import StorytellerSelectedThemesList from '@/components/StorytellerSelectedThemes/StorytellerSelectedThemesList';
import StorytellerSelectedThemesListItem from '@/components/StorytellerSelectedThemes/StorytellerSelectedThemesListItem';

export default {
  name: 'storyteller-selected-themes-loading',

  components: {
    StorytellerSelectedThemesAction,
    StorytellerSelectedThemesList,
    StorytellerSelectedThemesListItem,
  },

  computed: {
    ...mapState('themes', ['themes']),

    count() {
      return Math.min(this.themes.length, 7);
    },

    defaultThemes() {
      return this.themes
        .sort((a, b) => b.numOfDocuments - a.numOfDocuments)
        .slice(0, 7);
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-selected-themes-loading {
  @include flex("block", "column", "start", "start");

  width: 100%;
}
</style>
