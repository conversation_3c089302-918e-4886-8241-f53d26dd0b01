<template>
  <section class="storyteller-selected-themes-list-item" :class="{active}" @click="onClickTheme">
    <i class="fa-solid fa-grip-dots-vertical draggable-icon" />
    <section class="content">
      <span class="order">{{order}}</span>
      <span class="name">{{theme.topicLabel}}</span>
      <adorescore-box-mini class="score" :bucket="bucket" :score="adorescore"/>
      <section class="border-bottom" />
    </section>
  </section>
</template>

<script>
import { mapGetters } from 'vuex';

import AdorescoreBoxMini from '@/components/AdorescoreBox/AdorescoreBoxMini';

export default {
  name: 'storyteller-selected-themes-list-item',

  components: {
    AdorescoreBoxMini,
  },

  props: {
    active: {
      type: Boolean,
      default: false,
    },

    order: {
      type: Number,
      required: true,
    },

    theme: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      selectedThemes: [],
    };
  },

  computed: {
    ...mapGetters('benchmark', ['classifyAdorescore']),

    adorescore() {
      return Math.round(this.theme.polarity * 100);
    },

    bucket() {
      return this.classifyAdorescore(this.adorescore);
    },
  },

  methods: {
    onClickTheme() {
      this.$emit('click', this.theme);
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-selected-themes-list-item {
  @include flex("block", "row", "start", "center");

  border-radius: 3px;
  border: 1px solid transparent;
  cursor: pointer;
  font-size: $font-size-xs;
  margin-left: 0.5rem;
  margin-right: 0.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  position: relative;
  width: calc(100% - 1rem);

  &.active {
    background-color: #E6EAFF;
    border: 1px solid #D0D9FF;

    .content .order {
      background-color: rgba(120, 131, 191, 0.8);
      color: clr('white');
    }
  }

  &:hover {
    background-color: #E6EAFF;

    .draggable-icon {
      visibility: visible;
    }
  }

  &:last-child {
    .content .border-bottom {
      display: none;
    }
  }

  .draggable-icon {
    color: #DBDBDB;
    cursor: grab;
    position: absolute;
    left: 4px;
    visibility: hidden;
  }

  .content {
    @include flex("block", "row", "start", "center");

    padding: 0.6rem 0;
    position: relative;
    width: 100%;

    .order {
      @include flex("block", "row", "center", "center");

      background-color: rgba(217, 217, 217, 0.3);
      border-radius: 50%;
      font-size: 11px;
      font-weight: $font-weight-bold;
      margin-right: 0.4rem;
      min-height: 1.3rem;
      min-width: 1.3rem;
    }

    .name {
      overflow: hidden;
      margin-right: 10px;
    }

    .adorescore-box-mini {
      margin-left: auto;
    }

    .border-bottom {
      background-color: rgba(19, 28, 41, 0.1);
      bottom: -1px;
      height: 1px;
      position: absolute;
      width: 100%;
    }
  }
}
</style>
