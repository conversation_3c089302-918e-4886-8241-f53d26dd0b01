<template>
  <section class="storyteller-selected-themes">
    <storyteller-selected-themes-header />
    <storyteller-selected-themes-body />
  </section>
</template>

<script>
import StorytellerSelectedThemesBody from '@/components/StorytellerSelectedThemes/StorytellerSelectedThemesBody';
import StorytellerSelectedThemesHeader from '@/components/StorytellerSelectedThemes/StorytellerSelectedThemesHeader';

export default {
  name: 'reports-selected-themes',

  components: {
    StorytellerSelectedThemesBody,
    StorytellerSelectedThemesHeader,
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-selected-themes {
  @include flex("block", "column", "start", "start");

  height: 100%;
  overflow-y: auto;
  width: 100%;
}
</style>
