<template>
  <section class="storyteller-selected-themes-insight">
    <storyteller-selected-themes-action :count="count" />
    <storyteller-selected-themes-list />
  </section>
</template>

<script>
import { mapGetters } from 'vuex';

import StorytellerSelectedThemesAction from '@/components/StorytellerSelectedThemes/StorytellerSelectedThemesAction';
import StorytellerSelectedThemesList from '@/components/StorytellerSelectedThemes/StorytellerSelectedThemesList';

export default {
  name: 'storyteller-selected-themes-insight',

  components: {
    StorytellerSelectedThemesAction,
    StorytellerSelectedThemesList,
  },

  computed: {
    ...mapGetters('storyteller', ['slideThemesInsights']),

    count() {
      return this.slideThemesInsights.slideData.insightThemesData.length;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-selected-themes-insight {
  @include flex("block", "column", "start", "start");

  width: 100%;
}
</style>
