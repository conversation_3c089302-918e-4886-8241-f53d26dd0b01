<template>
  <section class="storyteller-selected-themes-action-plans">
    <storyteller-selected-themes-action-plans-action />
    <storyteller-selected-themes-action-plans-list />
  </section>
</template>

<script>
import StorytellerSelectedThemesActionPlansAction from '@/components/StorytellerSelectedThemes/StorytellerSelectedThemesActionPlansAction';
import StorytellerSelectedThemesActionPlansList from '@/components/StorytellerSelectedThemes/StorytellerSelectedThemesActionPlansList';

export default {
  name: 'storyteller-selected-themes-action-plans',

  components: {
    StorytellerSelectedThemesActionPlansAction,
    StorytellerSelectedThemesActionPlansList,
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-selected-themes-action-plans {
  @include flex("block", "column", "start", "start");

  margin-top: 1.75rem;
  width: 100%;
}
</style>
