<template>
  <section class="storyteller-selected-themes-list">
    <draggable :disabled="!isEditor" class="draggable" v-model="selectedThemesInsights">
      <storyteller-selected-themes-list-item
        v-for="(item, index) in selectedThemesInsights"
        :active="isActive(item.insightsTopicsModel)"
        :key="item.topicId"
        :order="index+1"
        :theme="item.insightsTopicsModel"
        @click="onClickTheme"
      />
    </draggable>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import draggable from 'vuedraggable';
import StorytellerRequest from '@/services/request/StorytellerRequest';
import StorytellerSelectedThemesListItem from '@/components/StorytellerSelectedThemes/StorytellerSelectedThemesListItem';
import StorytellerSlideType from '@/enum/storyteller-slide-type';

export default {
  name: 'reports-selected-themes-list',

  components: {
    draggable,
    StorytellerSelectedThemesListItem,
  },

  computed: {
    ...mapGetters('datasets', ['isEditable']),

    ...mapState('datasets', {
      datasetId: 'active',
    }),

    ...mapGetters('storyteller', ['activeSlides', 'slideThemesInsights']),

    ...mapState('storyteller', ['selectedSlide']),

    isEditor() {
      return this.isEditable(this.datasetId);
    },

    selectedThemesInsights: {
      get() {
        return this.slideThemesInsights.slideData.insightThemesData;
      },
      set(value) {
        this.slideThemesInsights.slideData.insightThemesData = value;
        const themeIds = value.map(v => v.topicId);
        StorytellerRequest.reorderSelectedThemes(themeIds);
      },
    },
  },

  methods: {
    ...mapActions('storyteller', ['selectSlide']),

    isActive(theme) {
      return StorytellerSlideType.isInsightSlide(this.selectedSlide.slideType) && this.selectedSlide.slideData.themeId === theme.id;
    },

    onClickTheme(theme) {
      const selectedSlide = this.activeSlides
        .filter(s => StorytellerSlideType.isInsightSlide(s.slideType))
        .find(s => s.slideData.themeId === theme.id);

      if (selectedSlide) this.selectSlide({ slide: selectedSlide });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-selected-themes-list {
  @include flex("block", "column", "start", "start");

  margin-top: 0.6rem;
  width: 100%;

  .draggable {
    width: 100%;
  }
}
</style>
