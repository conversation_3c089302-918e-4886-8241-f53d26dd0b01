<template>
  <section class="storyteller-selected-themes-edit-btn" @click="onClickBtn" v-if="isEditor">
    <i class="fa-solid fa-pen-to-square icon-edit"></i>
    <span class="text">Edit</span>
  </section>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: 'storyteller-selected-themes-edit-btn',

  computed: {
    ...mapGetters('datasets', ['isEditable']),

    isEditor() {
      return this.isEditable(this.datasetId);
    },
  },

  methods: {
    onClickBtn() {
      this.$emit('click');
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-selected-themes-edit-btn {
  @include flex("block", "row", "start", "start");

  color: rgb(123, 47, 248, 0.8);
  cursor: pointer;
  font-size: $font-size-xxs;

  .icon-edit {
    margin-right: 0.4rem;
  }

  .text {
    font-weight: $font-weight-extra-bold;
    letter-spacing: 0.3px;
    text-decoration: underline;
    text-transform: uppercase;
  }

  &:hover{
    color: rgb(123, 47, 248, 1.4);
  }
}
</style>
