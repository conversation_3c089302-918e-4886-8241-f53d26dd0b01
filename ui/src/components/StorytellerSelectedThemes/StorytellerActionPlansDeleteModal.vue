<template>
  <section class="storyteller-action-plans-delete-modal">
    <section class="header">
      <h2>Remove Suggested Actions?</h2>
    </section>
    <section class="body">
      <span>This will the remove suggested action plans from the slide deck, any changes will be lost.</span>
    </section>
    <section class="footer">
      <base-button class="cancel" colour="light" type="link" @click="onCancel">Cancel</base-button>
      <loading-blocks-overlay v-if="deleting" size="small" />
      <base-button v-else class="delete" colour="danger" @click="onRemove">
        <span>Remove</span>
      </base-button>
    </section>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import StorytellerActionPlansRequest from '@/services/request/StorytellerActionPlansRequest';

export default {
  name: 'storyteller-action-plans-delete-modal',

  components: {
    LoadingBlocksOverlay,
    BaseButton,
  },

  data() {
    return {
      deleting: false,
    };
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('themes', {
      resetThemes: 'reset',
    }),

    onCancel() {
      this.closeModal();
    },

    async onRemove() {
      this.deleting = true;
      this.resetThemes();
      await StorytellerActionPlansRequest.updateThemes();
      this.deleting = false;
      this.closeModal();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-action-plans-delete-modal {
  @include modal;

  .header {
    @include flex('block', 'column', 'center', 'start');

    padding: 1.5rem;
  }

  .body {
    @include flex("block", "column", "start", "stretch");

    span {
      font-size: $font-size-sm;
      margin: 0.5rem 0;
    }
  }
}
</style>
