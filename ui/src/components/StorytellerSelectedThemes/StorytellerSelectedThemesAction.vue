<template>
  <section class="storyteller-selected-themes-action">
    <span class="count">{{count}} Themes</span>
    <storyteller-selected-themes-edit-btn v-if="isEditor" class="edit" @click="onClickEdit" />
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import StorytellerSelectedThemesEditBtn from '@/components/StorytellerSelectedThemes/StorytellerSelectedThemesEditBtn';

export default {
  name: 'storyteller-selected-themes-action',

  props: {
    count: {
      type: Number,
      required: true,
    },
  },

  components: {
    StorytellerSelectedThemesEditBtn,
  },

  computed: {
    ...mapGetters('datasets', ['isEditable']),

    ...mapState('datasets', {
      datasetId: 'active',
    }),

    isEditor() {
      return this.isEditable(this.datasetId);
    },
  },

  methods: {
    ...mapActions('storyteller', ['setSelectingThemesView']),

    onClickEdit() {
      this.setSelectingThemesView({ selectingThemesView: true });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-selected-themes-action {
  @include flex("block", "row", "space-between", "center");

  color: rgba(19, 28, 41, 0.5);
  font-size: 11px;
  font-weight: $font-weight-extra-bold;
  margin-top: 1.2rem;
  padding: 0 1.5rem;
  text-transform: uppercase;
  width: 100%;

  .count {
    @include flex("block", "row", "start", "center");
  }

  .edit {
    @include flex("block", "row", "end", "center");
  }
}
</style>
