<template>
  <section class="storyteller-selected-themes-panel-body">
    <storyteller-selected-themes-loading v-if="buildingReport" />
    <storyteller-selected-themes-insight v-else-if="slideThemesInsights" />
    <storyteller-selected-themes-action-plans v-if="!noActionPlans" />
  </section>
</template>

<script>
import { mapGetters, mapState } from 'vuex';

import NetworkKeys from '@/enum/network-keys';
import NetworkStatus from '@/enum/network-status';
import StorytellerSelectedThemesActionPlans from '@/components/StorytellerSelectedThemes/StorytellerSelectedThemesActionPlans';
import StorytellerSelectedThemesInsight from '@/components/StorytellerSelectedThemes/StorytellerSelectedThemesInsight';
import StorytellerSelectedThemesLoading from '@/components/StorytellerSelectedThemes/StorytellerSelectedThemesLoading';

export default {
  name: 'storyteller-selected-themes-panel-body',

  components: {
    StorytellerSelectedThemesActionPlans,
    StorytellerSelectedThemesInsight,
    StorytellerSelectedThemesLoading,
  },

  computed: {
    ...mapGetters('network', ['status']),

    ...mapGetters('storyteller', ['slideThemesInsights']),

    ...mapState('storyteller', ['activeReport']),

    buildingReport() {
      return this.status(NetworkKeys.STORYTELLER_REPORT_GENERATE) === NetworkStatus.LOADING;
    },

    noActionPlans() {
      return !(this.activeReport.actionPlanThemeIds?.length);
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-selected-themes-panel-body {
  @include flex("block", "column", "start", "start");
  @include scrollbar-thin;

  overflow-y: auto;
  width: 100%;
}
</style>
