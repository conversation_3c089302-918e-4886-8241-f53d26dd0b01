<template>
  <section class="storyteller-selected-themes-action-plans-action">
    <section class="title">
      <i class="fa-regular fa-square-check icon-checkbox" />
      <span class="text">Suggested Actions</span>
      <section class="icon-wrapper" @click="onClickDelete">
        <i class="fa-regular fa-trash icon-delete"
          v-tooltip.top="{
            content: 'Delete Suggested Actions',
            class: 'tooltip-base-dark',
            delay: 0,
          }"
        />
      </section>
    </section>
    <section class="action">
      <span class="count">{{count}} Themes</span>
      <storyteller-selected-themes-edit-btn class="edit" @click="onClickEdit" />
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import StorytellerActionPlansDeleteModal from '@/components/StorytellerSelectedThemes/StorytellerActionPlansDeleteModal';
import StorytellerActionPlansModal from '@/components/StorytellerHeaderLeft/StorytellerActionPlansModal';
import StorytellerSelectedThemesEditBtn from '@/components/StorytellerSelectedThemes/StorytellerSelectedThemesEditBtn';

export default {
  name: 'storyteller-selected-themes-action-plans-action',

  components: {
    StorytellerSelectedThemesEditBtn,
  },

  computed: {
    ...mapState('storyteller', ['activeReport']),

    count() {
      return this.activeReport.actionPlanThemeIds.length;
    },
  },

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    onClickDelete() {
      this.setModalComponent({ component: StorytellerActionPlansDeleteModal });
    },

    onClickEdit() {
      this.setModalComponent({ component: StorytellerActionPlansModal });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-selected-themes-action-plans-action {
  @include flex("block", "column", "center", "start");

  color: $nps-blue;
  font-size: $font-size-xs;
  font-weight: $font-weight-extra-bold;
  padding: 0 1.5rem;
  width: 100%;

  .title {
    @include flex("block", "row", "start", "center");

    width: 100%;

    &:hover {
      .icon-wrapper {
        visibility: visible;
      }
    }

    .icon-checkbox {
      margin-right: 0.5rem;
    }

    .icon-wrapper {
      @include flex("block", "row", "center", "center");

      border-radius: 50%;
      border: 1px solid #FF5454;
      cursor: pointer;
      font-size: 22px;
      height: 22px;
      margin-left: auto;
      position: relative;
      visibility: hidden;
      width: 22px;

      .icon-delete {
        color: #FF5454;
        font-size: 11px;
      }

      &:hover {
        background-color: rgba(255, 16, 16, 0.1);
      }
    }
  }

  .action {
    @include flex("block", "row", "space-between", "center");

    margin-top: 1rem;
    width: 100%;

    .count {
      @include flex("block", "row", "start", "center");

      color: rgba(19, 28, 41, 0.5);
      font-size: 11px;
      text-transform: uppercase;
    }

    .edit {
      @include flex("block", "row", "end", "center");
    }
  }
}
</style>
