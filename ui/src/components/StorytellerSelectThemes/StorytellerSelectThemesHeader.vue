<template>
  <section class="storyteller-select-themes-header">
    <i class="fa-regular fa-arrow-down icon-down" />
    <span class="text help">&nbsp;Select 3 to 7 Themes for</span>
    <i class="fa-light fa-book icon-book" />
    <span class="text title">Insight Storyteller</span>
    <loading-blocks-overlay v-if="loading" size="small" />
    <storyteller-select-themes-confirm-btn v-if="!loading" class="btn" @click="onClickConfirm" />
    <storyteller-select-themes-cancel-btn v-if="!loading" class="btn cancel" @click="onClickCancel" />
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import StorytellerRequest from '@/services/request/StorytellerRequest';
import StorytellerSelectThemesCancelBtn from '@/components/StorytellerSelectThemes/StorytellerSelectThemesCancelBtn';
import StorytellerSelectThemesConfirmBtn from '@/components/StorytellerSelectThemes/StorytellerSelectThemesConfirmBtn';

export default {
  name: 'storyteller-select-themes-header',

  components: {
    LoadingBlocksOverlay,
    StorytellerSelectThemesCancelBtn,
    StorytellerSelectThemesConfirmBtn,
  },

  data() {
    return {
      loading: false,
    };
  },

  methods: {
    ...mapActions('storyteller', ['setSelectingThemesView']),

    onClickCancel() {
      this.setSelectingThemesView({ selectingThemesView: false });
    },

    async onClickConfirm() {
      this.loading = true;
      await StorytellerRequest.updateSelectedThemes();
      this.setSelectingThemesView({ selectingThemesView: false });
      this.loading = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-select-themes-header {
  @include flex("block", "row", "start", "center");

  background-color: rgba(214, 184, 255, 0.3);
  height: 50px;
  min-height: 50px;
  padding: 0 1rem;
  width: 100%;

  .text {
    font-size: $font-size-sm;

    &.help {
      font-weight: $font-weight-bold;
    }

    &.title {
      margin-left: 0.4rem;
      margin-right: 1rem;
    }
  }

  .icon-book {
    margin-left: 0.4rem;
  }

  .btn {
    text-transform: uppercase;

    &.cancel {
      margin-left: 1.4rem;
    }
  }
}
</style>
