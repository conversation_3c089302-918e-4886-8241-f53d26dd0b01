<template>
  <section class="storyteller-select-themes">
    <storyteller-select-themes-header />
    <storyteller-select-themes-body />
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import MetadataRequest from '@/services/request/MetadataRequest';
import StorytellerSelectThemesBody from '@/components/StorytellerSelectThemes/StorytellerSelectThemesBody';
import StorytellerSelectThemesHeader from '@/components/StorytellerSelectThemes/StorytellerSelectThemesHeader';
import ThemesRequest from '@/services/request/ThemesRequest';

export default {
  name: 'storyteller-select-themes',

  components: {
    StorytellerSelectThemesBody,
    StorytellerSelectThemesHeader,
  },

  async created() {
    this.resetSnippets();
    this.resetThemes();

    await ThemesRequest.fetchAndSetThemes();
    await MetadataRequest.filterCommentsOnMetadata();
    await MetadataRequest.filterCommentsCountOnMetadata();
  },

  methods: {
    ...mapActions('snippets', {
      resetSnippets: 'reset',
    }),

    ...mapActions('themes', {
      resetThemes: 'reset',
    }),
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-select-themes {
  @include flex("block", "column", "start", "stretch");

  height: calc(100vh - $header-height - $dataset-storyteller-selector-height);
  width: 100%;
}
</style>
