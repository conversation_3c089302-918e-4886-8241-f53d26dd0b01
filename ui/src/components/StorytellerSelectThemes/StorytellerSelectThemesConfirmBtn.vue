<template>
  <section class="storyteller-select-themes-confirm-btn" @click="onClickBtn" :class="{disabled}">
    <base-button size="small" :disabled="disabled">
      <i class="fa-regular fa-check icon-check" />
      <span>Confirm</span>
    </base-button>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';

export default {
  name: 'storyteller-select-themes-confirm-btn',

  components: {
    BaseButton,
  },

  data() {
    return {
      loading: false,
    };
  },

  computed: {
    ...mapState('themes', ['selectedThemes']),

    disabled() {
      return this.selectedThemes.length < 3 || this.selectedThemes.length > 7;
    },
  },

  methods: {
    onClickBtn() {
      if (this.disabled) return;
      this.$emit('click');
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-select-themes-confirm-btn {
  @include flex("block", "row", "start", "center");

  .base-button {
    @include flex("block", "row", "start", "center");

    background-color: rgba(64, 45, 179, 1);
    padding: 0.4rem 0.6rem;

    .icon-check {
      margin-right: 0.4rem;
    }

    &:hover, &:focus {
      background-color: rgba(64, 45, 179, 0.7);
    }
  }

  &.disabled {
    .base-button {
      cursor: not-allowed;
    }
  }
}
</style>
