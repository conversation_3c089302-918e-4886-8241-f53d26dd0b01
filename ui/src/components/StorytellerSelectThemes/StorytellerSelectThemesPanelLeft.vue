<template>
  <section class="storyteller-select-themes-panel-left" :class="{ disabled }">
    <themes-search />
    <themes-controls />
    <themes-list />
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import ThemesControls from '@/components/ThemesControls/ThemesControls';
import ThemesList from '@/components/ThemesList/ThemesList';
import ThemesSearch from '@/components/ThemesSearch/ThemesSearch';

export default {
  name: 'storyteller-select-themes-panel-left',

  components: {
    ThemesList,
    ThemesSearch,
    ThemesControls,
  },

  computed: {
    ...mapState('storyteller', ['activeReport']),

    ...mapState('themes', ['selectedThemes']),

    disabled() {
      return this.selectedThemes.length < 3 || this.selectedThemes.length > 7;
    },
  },

  mounted() {
    this.selectThemes({ ids: this.activeReport.themeIds.map(id => id) });
  },

  methods: {
    ...mapActions('themes', ['selectThemes']),
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-select-themes-panel-left {
  @include stretch;

  background-color: #fcfcfc;
  border: none;
  border-right: 1px solid #dee1e4;
  display: grid;
  grid-template-areas:
    'themes-search'
    'themes-controls'
    'themes-list';
  grid-template-columns: 1fr;
  grid-template-rows: 50px 50px 1fr;
  min-height: 600px;
  overflow: hidden;
  position: relative;

  .themes-controls {
    border-bottom: none;
  }
}
</style>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-select-themes-panel-left {
  &.disabled {
    #themes-selected-count {
      color: clr('red');
    }
  }
}
</style>
