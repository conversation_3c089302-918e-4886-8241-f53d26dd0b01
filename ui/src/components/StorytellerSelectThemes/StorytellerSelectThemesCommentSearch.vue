<template>
  <section class="storyteller-select-themes-comment-search">
    <section class="search-wrapper" >
      <search-icon class="icon" />
      <input v-model="localSearch"
        class="input"
        placeholder="Search Comments"
      />
      <x-icon class="icon x-icon"
        v-if="localSearch"
        @click="clearSearch"
      />
    </section>
  </section>
</template>

<script>
import { SearchIcon, XIcon } from 'vue-feather-icons';

export default {
  name: 'storyteller-select-themes-comment-search',

  components: {
    SearchIcon,
    XIcon,
  },

  data() {
    return {
      search: '',
    };
  },

  computed: {
    localSearch: {
      get() {
        return this.search;
      },
      set(search) {
        this.search = search;
        this.$emit('search', search);
      },
    },
  },

  methods: {
    clearSearch() {
      this.search = '';
      this.$emit('search', this.search);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/mixins";
@import "src/styles/variables";

.storyteller-select-themes-comment-search {
  @include flex("block", "row", "start", "center");
  @include stretch;

  border-bottom: $border-standard;
  grid-area: storyteller-select-themes-comment-search;
  padding: 1rem;

  .search-wrapper {
    @include flex("block", "row", "start", "center");
    @include shrink;

    background: clr("white");
    border: solid 1px $border-color-purple;
    border-radius: $border-radius-medium;
    flex-grow: 1;
    height: 30px;
    margin-right: 0;
    padding: 0.2rem 0.5rem;
    transition: all $interaction-transition-time;

    .icon {
      color: #8176c6;
      cursor: pointer;
      height: 1.2rem;

      &:hover {
        color: darken(#8176c6, 30%);
      }

      &.x-icon {
        &:hover {
          color: clr("red");
          transform: scale(1.1);
        }
      }
    }

    .input {
      @include stretch;

      border: none;
      font-size: $font-size-sm;
      margin: 0 0.5em;
      outline: none;

      &::placeholder {
        color: $search-dropdown-txt;
        font-style: italic;
        opacity: 0.5;
      }
    }
  }
}
</style>
