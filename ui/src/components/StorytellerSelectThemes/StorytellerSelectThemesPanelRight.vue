<template>
  <section class="storyteller-select-themes-panel-right">
    <storyteller-select-themes-comment-search @search="onSearch" />

    <section v-if="!loading" class="storyteller-select-themes-comments-list">
      <storyteller-select-themes-comment-item
        v-for="snippet in localSnippets"
        :key="snippet.userDocId"
        :snippet="snippet"
      />
    </section>

    <section v-else class="loading">
      <loading-blocks-overlay>Loading Comments...</loading-blocks-overlay>
    </section>
  </section>
</template>

<script>
import { mapGetters, mapState } from 'vuex';

import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import NetworkKeys from '@/enum/network-keys';
import NetworkStatus from '@/enum/network-status';
import StorytellerSelectThemesCommentItem from '@/components/StorytellerSelectThemes/StorytellerSelectThemesCommentItem';
import StorytellerSelectThemesCommentSearch from '@/components/StorytellerSelectThemes/StorytellerSelectThemesCommentSearch';

export default {
  name: 'storyteller-select-themes-panel-right',

  components: {
    LoadingBlocksOverlay,
    StorytellerSelectThemesCommentItem,
    StorytellerSelectThemesCommentSearch,
  },

  data() {
    return {
      search: '',
    };
  },

  computed: {
    ...mapGetters('network', ['status']),

    ...mapState('snippets', ['snippets']),

    localSnippets() {
      if (this.search) {
        return this.snippets.filter(snippet => snippet.content.toLowerCase().includes(this.search.toLowerCase()));
      }
      return this.snippets;
    },

    loading() {
      return this.snippetsStatus === NetworkStatus.LOADING;
    },

    snippetsStatus() {
      return this.status(NetworkKeys.METADATA_FILTER_COMMENT);
    },
  },

  methods: {
    onSearch(search) {
      this.search = search;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-select-themes-panel-right {
  @include stretch;

  background-color: #FFF;
  display: grid;
  grid-template-areas:
    'storyteller-select-themes-comment-search'
    'storyteller-select-themes-comments-list';
  grid-template-columns: 1fr;
  grid-template-rows: repeat(1, 50px) 1fr;
  height: 100%;
  min-height: 600px;
  position: relative;
  width: 100%;

  .storyteller-select-themes-comments-list {
    @include scrollbar-thin;

    grid-area: storyteller-select-themes-comments-list;
    height: 100%;
    overflow: auto;
    padding: 0 1rem;
  }

  .loading {
    @include flex("block", "row", "center", "center");
    @include stretch;

    height: 100%;
  }
}
</style>
