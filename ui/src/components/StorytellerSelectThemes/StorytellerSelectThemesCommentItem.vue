<template>
  <section class="storyteller-select-themes-comment-item">
    {{snippet.content}}
  </section>
</template>

<script>

export default {
  name: 'storyteller-select-themes-comment-item',

  props: {
    snippet: {
      type: Object,
      required: true,
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-select-themes-comment-item {
  @include flex("block", "row", "start", "center");
  @include stretch;

  border-bottom: 1px solid #FAFAFA;
  font-size: 0.8rem;
  font-weight: $font-weight-normal;
  line-height: 1.2rem;
  padding: 1rem 0;
  width: 100%;
}
</style>
