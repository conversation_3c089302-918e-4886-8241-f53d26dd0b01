<template>
  <section class="storyteller-select-themes-body">
    <storyteller-select-themes-panel-left />
    <storyteller-select-themes-panel-right />
  </section>
</template>

<script>
import StorytellerSelectThemesPanelLeft from '@/components/StorytellerSelectThemes/StorytellerSelectThemesPanelLeft';
import StorytellerSelectThemesPanelRight from '@/components/StorytellerSelectThemes/StorytellerSelectThemesPanelRight';

export default {
  name: 'storyteller-select-themes-body',

  components: {
    StorytellerSelectThemesPanelLeft,
    StorytellerSelectThemesPanelRight,
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-select-themes-body {
  display: grid;
  grid-template-columns: $themes-width-expanded 1fr;
  min-height: 600px;
}
</style>
