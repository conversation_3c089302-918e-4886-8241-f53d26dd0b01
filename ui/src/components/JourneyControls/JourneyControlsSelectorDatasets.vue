<template>
  <base-dropdown class="journey-controls-selector-datasets"
    :data="items"
    :open="open"
    @select="onSelect"
    @close="open = false">
    <section class="button" @click.stop="open = !open">
      <section class="score" :style="{ backgroundColor: colour(active) }">
        <img :src="require('@/assets/logo-white.svg')" />
        <span>{{ score }}</span>
      </section>

      <section class="label">
        <h3>{{ label }}</h3>
        <chevron-down-icon class="icon" :class="{ active: open }"/>
      </section>
    </section>
  </base-dropdown>
</template>

<script>
import { ChevronDownIcon } from 'vue-feather-icons';
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseDropdown from '@/components/Dropdown/BaseDropdown';
import BaseDropdownButton from '@/components/Dropdown/BaseDropdownButton';

export default {
  name: 'journey-controls-selector-datasets',

  components: {
    BaseDropdown,
    BaseDropdownButton,
    ChevronDownIcon,
  },

  data() {
    return {
      open: false,
    };
  },

  computed: {
    ...mapState('datasets', ['active', 'selected']),

    ...mapGetters('datasets', [
      'colour',
      'get',
      'getFromSelected',
    ]),

    items() {
      return this.selected.map(s => {
        return {
          content: this.getDataset(s).label,
          id: s,
        };
      });
    },

    label() {
      return this.getDataset(this.active).label;
    },

    score() {
      return this.getDataset(this.active).adoreScore;
    },
  },

  methods: {
    ...mapActions('datasets', ['setActive']),

    getDataset(id) {
      return this.getFromSelected(id).id
        ? this.getFromSelected(id)
        : this.get(id);
    },

    onSelect(item) {
      this.setActive({ id: item.id });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.journey-controls-selector-datasets {
  .button {
    @include flex("block", "row", "center", "center");

    cursor: pointer;
    padding: 0.2rem 0;
    transition: opacity $interaction-transition-time;

    &:hover {
      opacity: 0.8;
    }

    .score {
      @include flex("block", "column", "center", "center");
      @include rigid;

      border-radius: $border-radius-large;
      color: clr("white");
      font-size: $font-size-xs;
      height: 1.8rem;
      width: 1.8rem;

      img {
        width: 7px;
      }
    }

    .label {
      @include flex("block", "row", "center", "center");

      border-bottom: 1px solid $body-copy;
      margin: 0 0.3rem;

      h3 {
        font-size: $font-size-base;
      }

      .icon {
        height: $font-size-base;
        margin-left: 0.2rem;
        transition: transform $interaction-transition-time;
        width: $font-size-base;

        &.active {
          transform: rotate(180deg);
        }
      }
    }
  }
}
</style>
