<template>
  <section class="journey-controls-selector-actions">
    <section class="icon-wrapper"
      v-tooltip="{ class: 'tooltip-search-bar-save', content: 'Create New Journey', delay: 0 }"
      @click="onClickCreate">
      <plus-icon class="icon"/>
    </section>

    <section class="icon-wrapper"
      :class="{ disabled }"
      v-tooltip="{ class: 'tooltip-search-bar-save', content: 'Edit Journey', delay: 0 }"
      @click="onClickEdit">
      <edit-2-icon class="icon"/>
    </section>

    <section class="icon-wrapper danger"
      :class="{ disabled }"
      v-tooltip="{ class: 'tooltip-search-bar-save', content: 'Delete Journey', delay: 0 }"
      @click="onClickDelete">
      <trash-2-icon class="icon"/>
    </section>
  </section>
</template>

<script>
import { Edit2Icon, PlusIcon, Trash2Icon } from 'vue-feather-icons';
import { mapActions, mapState } from 'vuex';

import JourneyModalCreate from '@/components/JourneyModal/JourneyModalCreate';
import JourneyModalDelete from '@/components/JourneyModal/JourneyModalDelete';
import JourneyModalEdit from '@/components/JourneyModal/JourneyModalEdit';

export default {
  name: 'journey-controls-selector-actions',

  components: {
    Edit2Icon,
    PlusIcon,
    Trash2Icon,
  },

  computed: {
    ...mapState('journey', ['selectedJourney']),

    disabled() {
      return this.selectedJourney == null;
    },

  },

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    onClickCreate() {
      this.setModalComponent({ component: JourneyModalCreate });
    },

    onClickDelete() {
      this.setModalComponent({ component: JourneyModalDelete });
    },

    onClickEdit() {
      this.setModalComponent({ component: JourneyModalEdit });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.journey-controls-selector-actions {
  @include flex("block", "row", "center", "center");

  margin-left: 0.5rem;

  .icon-wrapper {
    @include flex("block", "row", "center", "center");

    border: 1px solid $body-copy-light;
    border-radius: 0.75rem;
    cursor: pointer;
    height: 1.2rem;
    margin: 0 0.2rem;
    padding: 0.2rem;
    transition: all $interaction-transition-time;
    width: 1.2rem;

    &:hover {
      background-color: $body-copy-light;

      .icon {
        color: clr('white');
      }
    }

    &.disabled {
      opacity: 0.5;
      pointer-events: none;
    }

    &.danger {
      border: 1px solid clr('red');

      &:hover {
        background-color: clr('red');

        .icon {
          color: clr('white');
        }
      }

      .icon {
        color: clr('red');
      }
    }

    .icon {
      color: $body-copy-light;
      height: 0.8rem;
      transition: all $interaction-transition-time;
      width: 0.8rem;
    }
  }
}
</style>
