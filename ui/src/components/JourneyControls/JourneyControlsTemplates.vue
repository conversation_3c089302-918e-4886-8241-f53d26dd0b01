<template>
  <section class="journey-templates">
    <input-with-button icon="save" placeholder="Name your template" v-model="localTemplateName">Save</input-with-button>
    <base-dropdown :data="templates" :open="open" @close="open = false">
      <base-dropdown-button :active="open" @click.stop="open = !open">Load from template</base-dropdown-button>
    </base-dropdown>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseDropdown from '@/components/Dropdown/BaseDropdown';
import BaseDropdownButton from '@/components/Dropdown/BaseDropdownButton';
import InputWithButton from '@/components/InputWithButton';

export default {
  name: 'journey-templates',

  components: {
    BaseDropdown,
    BaseDropdownButton,
    InputWithButton,
  },

  data() {
    return {
      open: false,
    };
  },

  computed: {
    ...mapState('journey', ['templateName', 'templates']),

    localTemplateName: {
      get() {
        return this.templateName;
      },
      set(name) {
        this.setTemplateName({ name });
      },
    },
  },

  methods: {
    ...mapActions('journey', ['setTemplateName']),
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.journey-templates {
  @include flex("block", "row", "end", "center");

  margin: 1rem 0;

  .input-with-button {
    margin-right: 1rem;
  }

  .base-dropdown-button {
    height: 2rem;
  }
}
</style>
