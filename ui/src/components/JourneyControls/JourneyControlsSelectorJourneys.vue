<template>
  <base-dropdown class="journey-controls-selector-journeys"
    :data="items"
    :open="open"
    :search="true"
    @close="open = false"
    @select="onSelect">
    <base-dropdown-button v-if="!selectedJourney" :active="open" @click.stop="open = !open">
      <span class="button-text">Select a Journey</span>
    </base-dropdown-button>

    <section v-else class="button" @click.stop="open = !open">
      <h3>{{ label }}</h3>
      <chevron-down-icon class="icon" :class="{ open }"/>
    </section>
  </base-dropdown>
</template>

<script>
import { ChevronDownIcon } from 'vue-feather-icons';
import { mapActions, mapState } from 'vuex';

import BaseDropdown from '@/components/Dropdown/BaseDropdown';
import BaseDropdownButton from '@/components/Dropdown/BaseDropdownButton';

export default {
  name: 'journey-controls-selector-journeys',

  components: {
    BaseDropdown,
    BaseDropdownButton,
    ChevronDownIcon,
  },

  data() {
    return {
      open: false,
    };
  },

  computed: {
    ...mapState('journey', ['journeys', 'selectedJourney']),

    items() {
      if (!this.journeys) {
        return [];
      }
      return this.journeys.map(journey => {
        return {
          content: journey.label,
          journey,
        };
      });
    },

    label() {
      return this.selectedJourney.label;
    },
  },

  methods: {
    ...mapActions('journey', ['setSelectedJourney']),

    onSelect(item) {
      this.setSelectedJourney({ journey: item.journey });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.journey-controls-selector-journeys {
  @include flex("block", "row", "center", "center");

  .button {
    @include flex("block", "row", "center", "center");

    border-bottom: 1px solid $body-copy;
    cursor: pointer;
    transition: opacity $interaction-transition-time;

    &:hover {
      opacity: 0.7;
    }

    h3 {
      font-size: $font-size-base;
    }

    .icon {
      height: $font-size-base;
      margin-left: 0.2rem;
      width: $font-size-base;
    }
  }
}
</style>
