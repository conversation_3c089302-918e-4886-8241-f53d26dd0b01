<template>
  <section class="journey-controls">
    <journey-controls-selector/>
    <!-- <journey-controls-templates v-if="selectedJourney"/> -->
  </section>
</template>

<script>
import { mapState } from 'vuex';

import JourneyControlsTemplates from '@/components/JourneyControls/JourneyControlsTemplates';
import JourneyControlsSelector from '@/components/JourneyControls/JourneyControlsSelector';

export default {
  name: 'journey-controls',

  components: {
    JourneyControlsTemplates,
    JourneyControlsSelector,
  },

  computed: {
    ...mapState('journey', ['selectedJourney']),
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.journey-controls {
  @include flex("block", "row", "between", "center");
}
</style>
