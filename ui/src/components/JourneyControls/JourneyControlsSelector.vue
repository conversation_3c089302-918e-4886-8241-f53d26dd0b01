<template>
  <section class="journey-controls-selector">
    <journey-controls-selector-journeys/>
    <span class="separator-text">in</span>
    <journey-controls-selector-datasets/>
    <journey-controls-selector-actions v-if="isEditor"/>
  </section>
</template>

<script>
import JourneyControlsSelectorActions from '@/components/JourneyControls/JourneyControlsSelectorActions';
import JourneyControlsSelectorDatasets from '@/components/JourneyControls/JourneyControlsSelectorDatasets';
import JourneyControlsSelectorJourneys from '@/components/JourneyControls/JourneyControlsSelectorJourneys';
import { mapGetters, mapState } from 'vuex';

export default {
  name: 'journey-controls-selector',

  components: {
    JourneyControlsSelectorActions,
    JourneyControlsSelectorDatasets,
    JourneyControlsSelectorJourneys,
  },

  computed: {
    ...mapGetters('datasets', ['isEditable']),

    ...mapState('datasets', ['active']),

    isEditor() {
      return this.isEditable(this.active);
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.journey-controls-selector {
  @include flex("block", "row", "center", "center");

  .button-text {
    color: $body-copy-light;
  }

  .button {
    @include flex("block", "row", "center", "center");

    .dataset-label {
      margin-left: 0.5rem;
    }

    .icon {
      height: $font-size-sm;
      margin-right: -0.2rem;
      transition: transform $interaction-transition-time;

      &.open {
        transform: rotate(180deg);
      }
    }
  }

  .separator-text {
    margin: 0 0.5rem;
    padding: 0;
  }

  .score {
    @include flex("block", "column", "center", "center");
    @include rigid;

    border-radius: $border-radius-large;
    color: clr("white");
    font-size: $font-size-sm;
    height: 2rem;
    margin-left: 0.5em;
    width: 2rem;

    img {
      width: 7px;
    }
  }
}
</style>
