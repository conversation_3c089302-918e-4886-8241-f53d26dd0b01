<template>
  <section class="datasets-analysing-info-progress">
    <section class="progress-bar" :style="{ width: progress(id) + '%'}"></section>
  </section>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: 'datasets-analysing-info-progress',

  props: {
    id: {
      type: [String, Number],
      required: true,
    },
  },

  computed: {
    ...mapGetters('datasetsProgress', ['progress']),
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.datasets-analysing-info-progress {
  @include flex("block", "row", "start", "center");

  background-color: $border-color;
  border-radius: 4px;
  height: 6px;
  width: 100%;

  .progress-bar {
    background: linear-gradient(90deg, #561c94, #a15aef) repeat-x;
    background-size: 100% 100%;
    border-radius: 4px;
    height: 100%;
    transition: all $dataset-progress-transition-time;
  }
}
</style>
