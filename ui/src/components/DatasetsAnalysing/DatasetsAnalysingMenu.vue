<template>
  <section class="datasets-analysing-menu">
    <x-circle-icon class="icon" @click="remove"/>
  </section>
</template>

<script>
import { XCircleIcon } from 'vue-feather-icons';
import { mapGetters } from 'vuex';
import { datasetApi } from '@/services/api';
import { datasetsRequestV0 } from '@/services/request';

export default {
  name: 'datasets-analysing-menu',

  components: {
    XCircleIcon,
  },

  props: {
    id: {
      type: [String, Number],
      required: true,
    },
  },

  computed: {
    ...mapGetters('datasets', ['get']),

    dataset() {
      return this.get(this.id);
    },
  },

  methods: {
    async remove() {
      if (this.dataset.executionId > 1) {
        await datasetApi.cancel(this.dataset);
      } else {
        await datasetApi.destroy([this.id]);
      }

      await datasetsRequestV0.getDatasets();
      await datasetsRequestV0.reloadSelected();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.datasets-analysing-menu {
  @include flex;
  @include rigid;

  .icon {
    color: clr("red");
    cursor: pointer;
    height: 1.2em;
    margin: 0 0.8em 0 1.2em;

    &:hover {
      color: darken(clr("red"), 30%);
    }
  }
}
</style>
