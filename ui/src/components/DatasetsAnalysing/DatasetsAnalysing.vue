<template>
  <section class="datasets-analysing">
    <datasets-analysing-info :id="id"></datasets-analysing-info>
    <datasets-analysing-menu :id="id"></datasets-analysing-menu>
  </section>
</template>

<script>
import DatasetsAnalysingInfo from '@/components/DatasetsAnalysing/DatasetsAnalysingInfo';
import DatasetsAnalysingMenu from '@/components/DatasetsAnalysing/DatasetsAnalysingMenu';

export default {
  name: 'datasets-analysing',

  components: {
    DatasetsAnalysingInfo,
    DatasetsAnalysingMenu,
  },

  props: {
    id: {
      type: [String, Number],
      required: true,
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.datasets-analysing {
  @include flex("block", "row", "start", "center");
  @include size-evenly;
  // @include panel;
  @include truncate;

  margin: 0 0.5rem;
  height: 75px;
  min-width: 350px;
  width: 100%;

  &:first-child {
    margin-left: 0;
  }

  &:last-child {
    margin-right: 0;
  }
}
</style>
