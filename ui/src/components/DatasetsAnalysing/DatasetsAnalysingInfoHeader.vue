<template>
  <section class="datasets-analysing-info-header">
    <section class="header">{{ dataset.label }}</section>
    <section class="info">
      <section class="status">{{ statusText }}</section>
      <section class="eta">{{ etaText }}</section>
    </section>
  </section>
</template>

<script>
import { mapGetters } from 'vuex';

const statusMap = new Map([
  ['uploading', 'Preparation'],
  ['analysing', 'Emotional Analysis'],
  ['aggregating', 'Results Aggregation'],
  ['topic_analysing', 'Topic Analysis'],
  ['applying_actions', 'Applying Saved Actions'],
  ['generating', 'Results File Generation'],
  ['s3_uploading', 'Uploading Results'],
  ['finished', 'Finished'],
]);

export default {
  name: 'datasets-analysing-info-header',

  props: {
    id: {
      type: [String, Number],
      required: true,
    },
  },

  computed: {
    ...mapGetters('datasets', ['get']),

    ...mapGetters('datasetsProgress', ['eta', 'status']),

    dataset() {
      return this.get(this.id);
    },

    etaText() {
      const eta = this.parseEta();

      if (!eta || (eta.hours === 0 && eta.minutes === 0 && eta.seconds === 0)) {
        return '...';
      }

      const minutes = eta.minutes < 10 ? `0${eta.minutes}` : eta.minutes;
      const seconds = eta.seconds < 10 ? `0${eta.seconds}` : eta.seconds;

      if (eta.hours > 0) return `${eta.hours}:${minutes}:${seconds}`;
      if (eta.minutes > 0) return `${eta.minutes}:${seconds}`;

      return `${eta.seconds}s`;
    },

    statusText() {
      if (this.status(this.id) !== 'unknown') {
        return statusMap.get(this.status(this.id));
      }

      return statusMap.get(this.dataset.status);
    },
  },

  methods: {
    parseEta() {
      const eta = this.eta(this.id);

      if (eta == null || eta === '') return false;

      const matches = eta.match(/(\d+ hours)|(\d+ minutes)|(\d+ seconds)/gi);

      return {
        hours: Number(matches[0].match(/\d+/)[0]),
        minutes: Number(matches[1].match(/\d+/)[0]),
        seconds: Number(matches[2].match(/\d+/)[0]),
      };
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.datasets-analysing-info-header {
  @include flex("block", "row", "between", "end");
  @include truncate;

  flex-wrap: wrap;
  font-size: $font-size-xs;
  font-weight: $font-weight-medium;

  .header {
    @include truncate;

    color: $body-copy;
    font-size: $font-size-sm;
    margin-right: 0.5rem;
  }

  .info {
    @include flex("block", "row", "between", "center");
    @include stretch;

    color: $body-copy-light;

    .status {
      font-size: $font-size-xxs;
      position: relative;
    }
  }
}
</style>
