<template>
  <section class="datasets-analysing-info">
    <datasets-analysing-info-header :id="id"></datasets-analysing-info-header>
    <datasets-analysing-info-progress :id="id"></datasets-analysing-info-progress>
  </section>
</template>

<script>
import DatasetsAnalysingInfoHeader from '@/components/DatasetsAnalysing/DatasetsAnalysingInfoHeader';
import DatasetsAnalysingInfoProgress from '@/components/DatasetsAnalysing/DatasetsAnalysingInfoProgress';

export default {
  name: 'datasets-analysing-info',

  components: {
    DatasetsAnalysingInfoHeader,
    DatasetsAnalysingInfoProgress,
  },

  props: {
    id: {
      type: [String, Number],
      required: true,
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.datasets-analysing-info {
  @include flex("block", "column", "around", "stretch");
  @include stretch;
  @include truncate;

  padding: 0.5rem 0.5rem 0.5rem 2.5rem;
  height: 100%;
}
</style>
