<template functional>
  <section class="loading-blocks-overlay" :class="$options.classList(props)">
    <section class="text">
      <slot></slot>
    </section>
    <section class="container">
      <section></section>
      <section></section>
      <section></section>
      <section></section>
      <section></section>
    </section>
  </section>
</template>

<script>
export default {
  name: 'loading-blocks-overlay',

  props: {
    size: {
      type: String,
      default: 'base',
      validator: value => ['small', 'base'].includes(value),
    },
  },

  classList: (props) => {
    return `${Object.entries(props)
      .map(entry => entry.join('-'))
      .join(' ')}`;
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

@-webkit-keyframes sk-stretchdelay {
  0%,
  40%,
  100% {
    -webkit-transform: scaleY(0.4);
  }
  20% {
    -webkit-transform: scaleY(1);
  }
}

@keyframes sk-stretchdelay {
  0%,
  40%,
  100% {
    transform: scaleY(0.4);
    -webkit-transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
    -webkit-transform: scaleY(1);
  }
}

.loading-blocks-overlay {
  @include flex("block", "column", "center", "center");

  .text {
    color: $body-copy-light;
    font-size: $font-size-sm;
    text-transform: uppercase;
  }

  .container {
    margin: 1rem auto;
    width: 50px;
    height: 40px;
    text-align: center;
    font-size: 10px;

    section {
      background-color: clr("purple");
      height: 100%;
      width: 3px;
      display: inline-block;

      -webkit-animation: sk-stretchdelay 1.2s infinite ease-in-out;
      animation: sk-stretchdelay 1.2s infinite ease-in-out;
    }

    section:nth-child(2) {
      -webkit-animation-delay: -1.1s;
      animation-delay: -1.1s;
    }

    section:nth-child(3) {
      -webkit-animation-delay: -1s;
      animation-delay: -1s;
    }

    section:nth-child(4) {
      -webkit-animation-delay: -0.9s;
      animation-delay: -0.9s;
    }

    section:nth-child(5) {
      -webkit-animation-delay: -0.8s;
      animation-delay: -0.8s;
    }
  }

  &.size-small {
    .text {
      font-size: $font-size-sm;
    }

    .container {
      margin: 0 auto;
      width: 25px;
      height: 20px;
      font-size: 5px;
    }

    section {
      width: 2px;
    }
  }
}
</style>
