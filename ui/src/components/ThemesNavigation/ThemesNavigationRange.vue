<template>
  <section class="themes-navigation-range">
    <themes-navigation-dropdown :component="component" :data="data" :height="500">
      <h3>{{ label }}</h3>
    </themes-navigation-dropdown>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import ThemesNavigationDropdown from '@/components/ThemesNavigation/ThemesNavigationDropdown';
import ThemesNavigationRangeItem from '@/components/ThemesNavigation/ThemesNavigationRangeItem';

export default {
  name: 'themes-navigation-range',

  components: {
    ThemesNavigationDropdown,
  },

  data() {
    return {
      component: ThemesNavigationRangeItem,
    };
  },

  computed: {
    ...mapState('themes', ['bucketCount', 'range']),

    data() {
      return [...Array(this.bucketCount).keys()];
    },

    label() {
      return this.range.lower != null && this.range.upper != null ?
        `${Math.round(this.range.lower)} → ${Math.round(this.range.upper)}`
        : '...';
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-navigation-range {
}
</style>
