<template>
  <section class="themes-navigation-index-item" @click="onClick">
    <span class="score">{{ score }}</span>
    <span class="label">{{ data.index.titleCase() }}</span>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import { position } from '@/helpers/index-utils';
import { themesRequest } from '@/services/request';

export default {
  name: 'themes-navigation-index-item',

  props: {
    data: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapState('datasets', ['active']),

    ...mapGetters('datasets', ['indexScore']),

    score() {
      return Math.round(this.indexScore(this.active, position(this.data.index)));
    },
  },

  methods: {
    ...mapActions('themes', ['setIndex', 'setThemes']),

    async onClick() {
      this.setIndex({ index: this.data.index });

      const themes = await themesRequest.fetchThemes();
      this.setThemes({ themes });

      this.$emit('close');
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-navigation-index-item {
  @include flex("block", "row", "start", "center");

  cursor: pointer;
  color: $body-copy-light;
  font-weight: $font-weight-medium;
  transition: $transition-base;
  font-size: $font-size-xs;
  letter-spacing: 0;
  padding: 0.75em;

  &:hover {
    background-color: clr("blue", "lighter");
    color: $body-copy;
  }

  .score {
    @include flex("block", "row", "center", "center");
    @include rigid;
    border-radius: $border-radius-large;
    height: 2.5em;
    border: 1px solid $body-copy;
    color: $body-copy;
    margin-right: 0.5em;
    width: 2.5em;
  }

  .label {
    @include truncate;
  }
}
</style>
