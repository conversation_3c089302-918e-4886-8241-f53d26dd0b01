<template>
  <section class="themes-navigation-range-item" @click="onClick">
    <section class="colour-border" :style="{ backgroundColor: rangeColour }"></section>
    <section class="text">{{ rangeLabel }}</section>
  </section>
</template>

<script>
import chroma from 'chroma-js';

import { mapActions, mapState } from 'vuex';

import { themesRequest } from '@/services/request';

export default {
  name: 'themes-navigation-range-item',

  props: {
    data: {
      type: Number,
      required: true,
    },
  },

  computed: {
    ...mapState('datasets', ['active']),

    ...mapState('themes', ['bucketCount']),

    range() {
      const lower = -100 + 200 / this.bucketCount * this.data;
      const upper = -100 + 200 / this.bucketCount * (this.data + 1);

      return { lower, upper };
    },

    rangeColour() {
      return chroma.scale([
        '#ff4343',
        '#ffffff',
        '#35d61a',
      ]).colors(this.bucketCount)[this.data];
    },

    rangeLabel() {
      return `${Math.round(this.range.lower)} → ${Math.round(this.range.upper)}`;
    },
  },

  methods: {
    ...mapActions('themes', [
      'selectFirstTheme',
      'setRange',
      'setThemes',
    ]),

    async onClick() {
      this.setRange({ range: this.range });

      const themes = await themesRequest.fetchThemes();
      this.setThemes({ themes });

      this.selectFirstTheme();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-navigation-range-item {
  @include flex("block", "row", "start", "center");

  cursor: pointer;
  color: $body-copy-light;
  font-weight: $font-weight-medium;
  transition: $transition-base;
  font-size: $font-size-xs;
  letter-spacing: 0;

  &:hover {
    background-color: clr("blue", "lighter");
    color: $body-copy;
  }

  .colour-border {
    height: #{$font-size-xs + (0.75rem * 2)};
    width: 4px;
  }

  .text {
    padding: 0 0.75rem;
  }
}
</style>
