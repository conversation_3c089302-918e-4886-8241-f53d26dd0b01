<template>
  <section class="themes-navigation-dataset-item" @click="selectDataset">
    <span class="score" :style="{ backgroundColor }">{{ score }}</span>
    <span class="label">{{ label }}</span>
  </section>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';

export default {
  name: 'themes-navigation-dataset-item',

  props: {
    data: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapGetters('datasets', ['colour', 'getFromSelected']),

    backgroundColor() {
      return this.colour(this.dataset);
    },

    dataset() {
      return this.data.dataset;
    },

    label() {
      return this.getFromSelected(this.dataset).label;
    },

    score() {
      return this.getFromSelected(this.dataset).adoreScore;
    },
  },

  methods: {
    ...mapActions('datasets', ['setActive']),

    async selectDataset() {
      this.setActive({ id: this.dataset });
      this.$emit('close');
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-navigation-dataset-item {
  @include flex("block", "row", "start", "center");

  cursor: pointer;
  color: $body-copy-light;
  font-weight: $font-weight-medium;
  transition: $transition-base;
  font-size: $font-size-xs;
  letter-spacing: 0;
  padding: 0.75em;

  &:hover {
    background-color: clr("blue", "lighter");
    color: $body-copy;
  }

  .score {
    @include flex("block", "row", "center", "center");
    @include rigid;

    border-radius: $border-radius-large;
    color: clr("white");
    height: 2.5em;
    margin-right: 0.5em;
    width: 2.5em;
  }

  .label {
    @include truncate;
  }
}
</style>
