<template>
  <section class="themes-navigation-index">
    <section class="score">
      <img :src="require('@/assets/logo-rich.svg')" />
      <span>{{ score }}</span>
    </section>

    <themes-navigation-dropdown :component="component" :data="data">
      <h3>{{ label }}</h3>
    </themes-navigation-dropdown>
  </section>
</template>

<script>
import { mapGetters, mapState } from 'vuex';

import Index from '@/enum/index';
import ThemesNavigationDropdown from '@/components/ThemesNavigation/ThemesNavigationDropdown';
import ThemesNavigationIndexItem from '@/components/ThemesNavigation/ThemesNavigationIndexItem';

import { position } from '@/helpers/index-utils';

export default {
  name: 'themes-navigation-index',

  components: {
    ThemesNavigationDropdown,
  },

  data() {
    return {
      component: ThemesNavigationIndexItem,
    };
  },

  computed: {
    ...mapState('datasets', ['active']),

    ...mapState('themes', ['index']),

    ...mapGetters('datasets', ['indexScore']),

    data() {
      return [
        ...Index.enumValues.map(i => {
          return {
            content: i.name,
            index: i,
          };
        }),
      ];
    },

    label() {
      return this.index ? this.index.displayName() : '...';
    },

    score() {
      return this.index ? Math.round(this.indexScore(this.active, position(this.index))) : '...';
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-navigation-index {
  @include flex("inline", "row", "center", "center");

  .score {
    @include flex("block", "column", "center", "center");
    @include rigid;

    border: $border-light solid clr("purple", "rich");
    border-radius: $border-radius-large;
    color: $body-copy;
    height: $results-index-score-height;
    margin-left: 0.5em;
    width: $results-index-score-width;

    img {
      width: 7px;
    }
  }
}
</style>
