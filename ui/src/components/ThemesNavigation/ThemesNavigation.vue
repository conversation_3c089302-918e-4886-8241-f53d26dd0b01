<template>
  <section class="themes-navigation">
    <slot></slot>
  </section>
</template>

<script>
export default {
  name: 'themes-navigation',
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-navigation {
  @include flex("block", "row", "start", "center");

  animation: $load-up;
  color: $body-copy-light;
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  text-transform: uppercase;
}
</style>
