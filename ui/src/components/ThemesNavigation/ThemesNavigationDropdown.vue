<template>
  <section class="themes-navigation-dropdown">
    <base-dropdown v-bind="$props" :open="open" @close="open = false">
      <section class="button" @click.stop="open = !open">
        <slot></slot>
        <chevron-down-icon class="icon" :class="{ open }" />
      </section>
    </base-dropdown>
  </section>
</template>

<script>
import { ChevronDownIcon } from 'vue-feather-icons';

import BaseDropdown from '@/components/Dropdown/BaseDropdown';

export default {
  name: 'themes-navigation-dropdown',

  components: {
    BaseDropdown,
    ChevronDownIcon,
  },

  props: {
    component: {
      type: Object,
      required: true,
    },

    data: {
      type: Array,
      required: true,
    },

    height: {
      type: Number,
      required: false,
    },
  },

  data() {
    return {
      open: false,
    };
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-navigation-dropdown {
  @include flex("inline", "row", "center", "center");

  margin-right: 0.5rem;

  .button {
    @include flex("inline", "row", "center", "center");

    border-bottom: 1px solid $body-copy;
    color: $body-copy;
    cursor: pointer;
    margin: 0 0 0 0.5rem;
    transition: opacity $interaction-transition-time;

    &:hover {
      opacity: 0.8;
    }

    h3 {
      font-size: $font-size-sm;
      margin: 0;
    }

    .icon {
      height: $font-size-sm;
      margin-right: -0.2rem;
      transition: transform $interaction-transition-time;

      &.open {
        transform: rotate(180deg);
      }
    }
  }
}
</style>
