<template>
  <section class="themes-navigation-dataset">
    <section class="score" :style="{ backgroundColor: colour(active) }">
      <img :src="require('@/assets/logo-white.svg')" />
      <span>{{ score }}</span>
    </section>
    <themes-navigation-dropdown :component="component" :data="data">
      <h3>{{ label }}</h3>
    </themes-navigation-dropdown>
  </section>
</template>

<script>
import { mapGetters, mapState } from 'vuex';

import ThemesNavigationDatasetItem from '@/components/ThemesNavigation/ThemesNavigationDatasetItem';
import ThemesNavigationDropdown from '@/components/ThemesNavigation/ThemesNavigationDropdown';

export default {
  name: 'themes-navigation-dataset',

  components: {
    ThemesNavigationDropdown,
  },

  data() {
    return {
      component: ThemesNavigationDatasetItem,
    };
  },

  computed: {
    ...mapState('datasets', ['active']),

    ...mapGetters('datasets', [
      'colour',
      'get',
      'getFromSelected',
      'selectedLimited',
    ]),

    data() {
      return [
        ...this.selectedLimited.map(s => {
          return {
            content: s,
            dataset: s,
          };
        }),
      ];
    },

    dataset() {
      return this.getFromSelected(this.active).id
        ? this.getFromSelected(this.active)
        : this.get(this.active);
    },

    label() {
      return this.dataset ? this.dataset.label : '...';
    },

    score() {
      return this.dataset ? this.dataset.adoreScore : '...';
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-navigation-dataset {
  @include flex("inline", "row", "center", "center");

  .score {
    @include flex("block", "column", "center", "center");
    @include rigid;

    border-radius: $border-radius-large;
    color: clr("white");
    font-size: $font-size-sm;
    height: $results-index-score-height;
    margin-left: 0.5em;
    width: $results-index-score-width;

    img {
      width: 7px;
    }
  }
}
</style>
