<template>
  <section class="themes-navigation-time">{{ from }} → {{ to }}</section>
</template>

<script>
import { format } from 'date-fns';
import { mapState } from 'vuex';

export default {
  name: 'themes-navigation-time',

  data() {
    return {
      formatter: 'eee do MMM, h:mm a',
    };
  },

  computed: {
    ...mapState('timeSeries', ['time']),

    from() {
      if (this.time.from != null) return format(this.time.from, this.formatter);

      return '...';
    },

    to() {
      if (this.time.to != null) return format(this.time.to, this.formatter);

      return '...';
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-navigation-time {
  color: $body-copy;
  margin: 0 0.5rem;
  text-transform: none;
}
</style>
