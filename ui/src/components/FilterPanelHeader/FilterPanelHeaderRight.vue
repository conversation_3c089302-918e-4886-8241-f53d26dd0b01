<template>
  <section class="filter-panel-header-right">
    <filter-panel-save v-if="isEditor" />
    <img class="icon-arrow" :src="iconArrow" alt="icon-arrow" @click="onClickArrow" />
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import FilterPanelSave from '@/components/FilterPanelHeader/FilterPanelSave';
import iconArrow from '@/assets/icon-arrow-left.svg';

export default {
  name: 'filter-panel-header-right',

  components: {
    FilterPanelSave,
  },

  data() {
    return {
      iconArrow,
    };
  },

  computed: {
    ...mapGetters('datasets', ['isEditable']),

    ...mapState('datasets', ['active']),

    isEditor() {
      return this.isEditable(this.active);
    },
  },

  methods: {
    ...mapActions('snippetsFilter', ['setShowFilterPanel']),

    onClickArrow() {
      this.setShowFilterPanel({ value: false });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.filter-panel-header-right {
  height: 100%;
  width: 100%;

  .icon-arrow {
    background-color: #1F2734;
    border-radius: $border-radius-small;
    cursor: pointer;
    padding: 0.4rem;

    &:hover {
      background-color: lighten(#1F2734, 5%);
    }
  }
}
</style>
