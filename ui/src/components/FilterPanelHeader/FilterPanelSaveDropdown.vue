<template>
  <section class="filter-panel-save-dropdown"
           v-click-outside-handler="{
             handler: 'onClickOutside',
           }"
  >
    <section class="item" @click="onClickSave">
      <i class="fa-solid fa-floppy-disk icon-save"></i>
      <span>Save</span>
    </section>
    <section class="item" @click="onClickSaveAs">
      <i class="fa-solid fa-floppy-disk icon-save"></i>
      <span>Save As</span>
    </section>
  </section>
</template>

<script>
import clickOutsideHandler from '@/directives/click-outside-handler';

export default {
  name: 'filter-panel-save-dropdown',

  directives: {
    clickOutsideHandler,
  },

  methods: {
    onClickOutside() {
      this.$emit('close');
    },

    onClickSave() {
      this.$emit('save');
    },

    onClickSaveAs() {
      this.$emit('saveAs');
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.filter-panel-save-dropdown {
  @include panel;
  @include flex('block', 'column', 'center', 'start');

  background-color: #ffffff;
  border-radius: 4px;
  border: 1px solid #dee1e4;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.07);
  color: #1F2734;
  cursor: pointer;
  font-size: 0.65rem;
  font-weight: $font-weight-bold;
  margin-right: 0.6rem;
  position: absolute;
  right: 0;
  text-transform: uppercase;
  top: 30px;
  width: 100px;

  .item {
    @include flex('block', 'row', 'start', 'start');

    cursor: pointer;
    padding: 0.5rem 1rem;
    width: 100%;
    opacity: 1;

    &:hover {
      background-color: clr('purple', 'lighter');
      border-radius: 4px;
    }

    .icon-save {
      padding-right: 0.3rem;
    }
  }
}
</style>
