<template>
  <section class="filter-panel-header">
    <filter-panel-header-left class="left" />
    <filter-panel-header-right class="right" />
  </section>
</template>

<script>
import FilterPanelHeaderLeft from '@/components/FilterPanelHeader/FilterPanelHeaderLeft';
import FilterPanelHeaderRight from '@/components/FilterPanelHeader/FilterPanelHeaderRight';

export default {
  name: 'filter-panel-header',

  components: {
    FilterPanelHeaderLeft,
    FilterPanelHeaderRight,
  },

  computed: {
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.filter-panel-header {
  @include flex("block", "row", "space-between", "center");

  background-color: $cfp-btn;
  height: $search-height;
  min-height: $search-height;
  padding: 1.5rem;
  width: 100%;

  .left {
    @include flex("block", "row", "start", "center");
  }

  .right {
    @include flex("block", "row", "end", "center");
  }
}
</style>
