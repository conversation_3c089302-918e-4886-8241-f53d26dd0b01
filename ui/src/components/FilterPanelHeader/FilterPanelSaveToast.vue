<template>
  <section class="filter-panel-footer-apply-toast">
    <section class="toast">
      <section class="left">
        <span class="label">Filter {{toastData.label}} is saved</span>
      </section>
      <section class="right">
        <base-button class="dismiss" colour="light" @click="onDismiss">Dismiss</base-button>
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import Toast from '@/components/Mixins/Toast';

export default {
  name: 'filter-panel-save-toast',

  mixins: [Toast],

  components: {
    BaseButton,
  },

  data() {
    return {
      count: 3,
      countWatcher: null,
    };
  },

  computed: {
    ...mapState('toast', ['toastData']),
  },

  created() {
    this.countWatcher = setInterval(() => {
      this.count -= 1;
      if (this.count === 0) {
        clearInterval(this.countWatcher);
        this.close();
      }
    }, 1000);
  },

  beforeDestroy() {
    this.remove({ id: 'filter-panel-save-toast' });
  },

  methods: {
    ...mapActions('toast', ['remove']),

    onDismiss() {
      this.close();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.filter-panel-footer-apply-toast {
  @include flex("block", "row", "end", "start");

  .toast {
    @include toast;

    border-radius: $border-radius-medium;
    padding: 1rem 1.5rem;

    .left {
      @include flex("block", "row", "start", "center");

      margin-right: 4rem;

      span {
        font-weight: $font-weight-normal;
        margin-left: 0.3rem;

        &.label {
          font-weight: $font-weight-bold;
        }
      }
    }

    .base-button {
      padding: 0.6rem 1.6rem;
    }
  }
}
</style>
