<template>
  <section class="filter-panel-save-as-modal">
    <section class="header">
      <i class="fa-regular fa-floppy-disk icon"></i>
      <span class="text">Save Filter to {{dataset.label}}</span>
    </section>
    <section class="body">
      <base-input v-model="label"
        class="dataset-label"
        :focus="true"
        ref="label"
        placeholder="Please Enter Filter Label"
        @submit="onConfirm"
      />
    </section>
    <section class="footer">
      <base-button class="cancel" colour="light" size="small" type="link" @click="onCancel">Cancel</base-button>
      <loading-blocks-overlay class="loading" size="small" v-if="saving" />
      <base-button v-else class="confirm" colour="base" size="small" @click="onConfirm" :disabled="hasError">Save</base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import FilterPanelSaveToast from '@/components/FilterPanelHeader/FilterPanelSaveToast';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';

import { metadataRequest } from '@/services/request';

export default {
  name: 'filter-panel-save-as-modal',

  components: {
    BaseButton,
    BaseInput,
    LoadingBlocksOverlay,
  },

  data() {
    return {
      filterLabel: null,
      saving: false,
    };
  },

  computed: {
    ...mapGetters('datasets', ['get']),

    ...mapState('datasets', ['active']),

    dataset() {
      return this.get(this.active);
    },

    hasError() {
      return !(this.filterLabel?.length && this.filterLabel.length < 200);
    },

    label: {
      get() {
        return this.filterLabel;
      },

      set(value) {
        this.filterLabel = value?.trim() || null;
      },
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('toast', {
      addToast: 'add',
      addToastData: 'addToastData',
    }),

    onCancel() {
      this.closeModal();
    },

    async onConfirm() {
      if (!this.hasError && !this.saving) {
        this.saving = true;

        await metadataRequest.saveAsFilter(this.filterLabel);

        await this.addToastData({
          toastData: {
            label: this.filterLabel,
          },
        });
        await this.addToast({ toast: { component: FilterPanelSaveToast, id: 'filter-panel-save-toast' } });

        this.closeModal();

        this.saving = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.filter-panel-save-as-modal {
  @include modal;

  width: 450px;

  .header {
    @include flex("block", "row", "start", "center");

    padding: 1rem 2rem;

    h2 {
      font-size: $font-size-sm;
    }

    .icon {
      height: $font-size-sm;
      margin-right: 0.5rem;
      width: $font-size-sm;
    }
  }

  .body {
    @include flex("block", "column", "start", "start");

    padding: 2rem;

    .dataset-label {
      background-color: clr('white');
      border: $border-standard;
      border-radius: $border-radius-medium;
      font-size: $font-size-sm;
      padding: 0.5rem;
    }
  }

  .footer {
    padding: 1rem 2rem;

    .base-button {
      padding: 0.5rem 1rem;
    }

    .cancel {
      margin-left: -0.6rem;
    }

    .confirm {
      &.disabled-true {
        cursor: not-allowed;
      }
    }
  }
}
</style>
