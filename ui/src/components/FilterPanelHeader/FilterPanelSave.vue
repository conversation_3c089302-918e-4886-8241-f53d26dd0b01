<template>
  <section class="filter-panel-save">
    <loading-blocks-overlay class="loading" size="small" v-if="saving" />
    <section v-else class="btn-save" v-show="allowSaveBtn">
      <section class="btn-left" @click="doSave">
        <section class="text">Save</section>
      </section>
      <section class="btn-right" @click.stop="open = !open">
        <i class="fa fa-caret-down icon"></i>
      </section>
    </section>
    <filter-panel-save-dropdown v-if="open" @close="open = false" @save="doSave" @saveAs="doSaveAs" />
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import FilterPanelSaveAsModal from '@/components/FilterPanelHeader/FilterPanelSaveAsModal';
import FilterPanelSaveDropdown from '@/components/FilterPanelHeader/FilterPanelSaveDropdown';
import FilterPanelSaveToast from '@/components/FilterPanelHeader/FilterPanelSaveToast';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';

import { metadataRequest } from '@/services/request';

export default {
  name: 'filter-panel-save',

  components: {
    FilterPanelSaveDropdown,
    LoadingBlocksOverlay,
  },

  data() {
    return {
      open: false,
      saving: false,
    };
  },

  computed: {
    ...mapGetters('snippets', ['getFilterValidatedList']),

    ...mapState('snippets', ['filters']),

    ...mapState('snippetsFilter', ['savedFilter', 'metadataFilterList']),

    allowSaveBtn() {
      return this.metadataFilterList;
    },

    hasErrors() {
      return this.filters.filter(o => o.column).length !== this.getFilterValidatedList.length;
    },
  },

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    ...mapActions('snippets', ['applyFilters', 'setFiltersAttempted']),

    ...mapActions('toast', {
      addToast: 'add',
      addToastData: 'addToastData',
    }),

    async doSave() {
      if (this.saving || !this.allowSaveBtn) return;

      this.saving = true;

      if (this.hasErrors) {
        this.setFiltersAttempted({ value: true });
      } else {
        this.setFiltersAttempted({ value: false });
        this.applyFilters();
        if (this.savedFilter) {
          await metadataRequest.saveFilter();
          await this.addToastData({
            toastData: {
              label: this.savedFilter.metadataFilterList.label,
            },
          });
          await this.addToast({ toast: { component: FilterPanelSaveToast, id: 'filter-panel-save-toast' } });
        } else {
          this.setModalComponent({ component: FilterPanelSaveAsModal });
        }
      }

      this.saving = false;
    },

    doSaveAs() {
      if (this.hasErrors) {
        this.setFiltersAttempted({ value: true });
      } else if (this.allowSaveBtn) {
        this.setFiltersAttempted({ value: false });
        this.applyFilters();
        this.setModalComponent({ component: FilterPanelSaveAsModal });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.filter-panel-save {
  @include flex("block", "row", "start", "center");
  position: relative;

  .loading {
    margin-right: 1rem;
  }

  .btn-save {
    @include flex("block", "row", "start", "center");

    color: #1F2734;
    cursor: pointer;
    font-size: 0.65rem;
    font-weight: $font-weight-bold;
    margin-right: 0.6rem;
    text-transform: uppercase;

    .btn-left {
      background-color: clr('white');
      border-radius: $border-radius-small 0 0 $border-radius-small;
      border-right: $border-light solid $cfp-bottom-border-clr;
      padding: 0.4rem 0.5rem;

      .text {
        margin-left: 0.2rem;
      }

      &:hover {
        background-color: rgba(clr('white'), 0.8);
      }
    }

    .btn-right {
      background-color: clr('white');
      border-radius: 0 $border-radius-small $border-radius-small 0;
      padding: 0.4rem 0.5rem;

      &:hover {
        background-color: rgba(clr('white'), 0.8);
      }
    }

    .icon-down {
      margin-left: 0.5rem;
      transition: all $interaction-transition-time;
    }
  }
}
</style>
