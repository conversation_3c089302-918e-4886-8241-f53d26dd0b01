<template>
  <section class="filter-panel-header-left">
    <i class="fa-solid fa-filter icon-filter"></i>
    <section class="text">
      <span class="title">Filters</span>
      <span v-if="savedFilter" class="description">Saved</span>
      <span v-else class="description">Not Saved</span>
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';

export default {
  name: 'filter-panel-header-left',

  computed: {
    ...mapState('snippetsFilter', ['savedFilter']),
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.filter-panel-header-left {
  width: 100%;
  height: 100%;

  .icon-filter {
    font-size: $font-size-xxs;
    margin-right: 0.4rem;
  }

  .text {
    @include flex("block", "column", "start", "start");

    position: relative;

    .title {
      font-size: 0.9rem;
      font-weight: $font-weight-bold;
    }

    .description {
      bottom: -14px;
      font-size: $font-size-xxs;
      font-style: italic;
      font-weight: $font-weight-normal;
      opacity: 0.6;
      position: absolute;
      white-space: nowrap;
    }
  }
}
</style>
