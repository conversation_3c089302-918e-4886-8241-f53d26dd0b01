<template>
  <section class="user-organisation-role">
    {{textRole}}
  </section>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: 'user-organisation-role',

  computed: {
    ...mapGetters('organisation', ['getOrganisationRole']),

    textRole() {
      return `${this.getOrganisationRole}`;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.user-organisation-role {
  background-color: #6686D9;
  border-radius: 0.7rem;
  color: clr('white');
  font-size: 0.7rem;
  font-weight: $font-weight-bold;
  padding: 0.3rem 0.5rem;
  text-transform: uppercase;
  width: fit-content;
}
</style>
