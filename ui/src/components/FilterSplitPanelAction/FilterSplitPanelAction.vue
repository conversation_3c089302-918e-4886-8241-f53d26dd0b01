<template>
  <section class="filter-split-panel-action">
    <section class="left">
      <i class="fa-solid fa-arrow-left icon-arrow" @click="onClickBack"></i>
      <span class="text"><b>Split By:</b> {{splitMetadataType.name}}</span>
    </section>
    <section class="right">
      <i class="fa-light fa-circle-xmark icon-clear"
         v-tooltip.top="{
          class: 'tooltip-base-tag',
          content: 'Clear All Splits',
          delay: 0,
        }"
         @click="onClear">
      </i>
    </section>
  </section>
</template>

<script>

import { mapActions, mapState } from 'vuex';

export default {
  name: 'filter-split-panel-action',

  computed: {
    ...mapState('snippetsFilter', ['splitMetadataType']),
  },

  methods: {
    ...mapActions('snippetsFilter', ['resetSplitFilterViews', 'setShowSplitPanel']),

    async onClear() {
      this.resetSplitFilterViews();
      this.setShowSplitPanel({ value: false });
    },

    onClickBack() {
      this.setShowSplitPanel({ value: false });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.filter-split-panel-action {
  @include flex("block", "row", "space-between", "center");

  background-color: #7568AF;
  border-bottom: $border-light solid $cfp-bottom-border-clr;
  font-size: $font-size-xxs;
  height: 50px;
  padding: 1.5rem;
  width: 100%;

  .left {
    @include flex("block", "row", "start", "center");

    .icon-arrow {
      @include flex("block", "row", "center", "center");

      background-color: clr('white');
      border-radius: 50%;
      color: #7568AF;
      cursor: pointer;
      height: 0.875rem;
      margin-right: 0.4rem;
      padding: 0.1rem;
      width: 0.875rem;

      &:hover {
        color: lighten(#7568AF, 15%);
      }
    }
  }

  .right {
    @include flex("block", "end", "start", "center");

    .icon-clear {
      border-radius: 50%;
      color: #FF837B;
      cursor: pointer;
      font-size: $font-size-base;

      &:hover {
        background-color: rgba(163, 36, 36, 0.4);
      }
    }
  }
}
</style>
