<template>
  <section class="insights-top-indicators-emotion-item">
    <section class="item" :class="{ isOff }">
      <div class="status-icon item-left">
        <i v-if="statusActive" class="fa-solid fa-circle-check icon-check" />
        <span v-else class="circle" />
      </div>
      <div class="label item-left">
        <i class="fa-solid fa-eye-slash icon-off" v-if="isOff" />
        <span class="text">{{ textContent }}</span>
      </div>
      <div class="contribution-percentage item-right">
        <span class="text">{{ textContributionPercentage }}</span>
      </div>
    </section>
  </section>
</template>

<script>

export default {
  name: 'insights-top-indicators-emotion-item',

  props: {
    data: {
      type: Object,
      required: true,
    },
  },

  computed: {
    isOff() {
      return this.data.emotionIndex === -1;
    },

    statusActive() {
      return this.data.active;
    },

    textContent() {
      return this.data.content;
    },

    textContributionPercentage() {
      if (this.data.contributionPercentage == null) {
        return '';
      }
      return `${Math.round(this.data.contributionPercentage)}%`;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.insights-top-indicators-emotion-item {

  .item {
    cursor: pointer;
    display: grid;
    grid-template-columns: 1.2rem auto 4rem;
    margin: 0 0.5rem;
    padding: 0.5rem 0;

    &:hover {
      .status-icon {
        .circle {
          border-color: clr("blue");
        }
      }
    }

    .item-left {
      @include flex("block", "row", "start", "center");
    }

    .item-right {
      @include flex("block", "row", "end", "center");
    }

    .status-icon {
      .circle {
        border: 2px solid $border-color;
        border-radius: 50%;
        height: 14px;
        width: 14px;
      }

      .icon-check {
        color: clr("blue");
      }
    }

    .label {
      @include stretch;

      .icon-off {
        margin-right: 0.2rem;
        opacity: 0.8;
      }
    }
  }
}
</style>
