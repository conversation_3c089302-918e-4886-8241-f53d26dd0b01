<template>
  <section class="insights-top-indicators-emotion-list"
           v-click-outside-handler="{
             handler: 'onClickOutside',
             excludedParentClasses: ['base-dropdown-list'],
           }"
  >
    <base-dropdown :component="InsightsTopIndicatorsEmotionItem"
                   :data="dataList"
                   :open="open"
                   @select="onSelectOption"
                   tooltip-class="tooltip-insights-top-indicators-emotion-dropdown">
      <section class="selected" @click="open = !open">
        <div class="label">
          <i class="fa-solid fa-eye-slash icon-off" v-if="isOff" />
          <span class="text">
            {{ textSelected }}
          </span>
        </div>
        <div class="icon">
          <i class="fa fa-caret-down" :class="{ open }"></i>
        </div>
      </section>
    </base-dropdown>
  </section>
</template>

<script>

import BaseDropdown from '@/components/Dropdown/BaseDropdown';
import clickOutsideHandler from '@/directives/click-outside-handler';
import indexes from '@/helpers/indexes';
import InsightsTopIndicatorsEmotionItem from '@/components/InsightsTopIndicators/InsightsTopIndicatorsEmotionItem';
import { nameByApiIndex } from '@/helpers/index-utils';

export default {
  name: 'insights-top-indicators-emotion-list',

  components: {
    BaseDropdown,
  },

  props: {
    contributionPercentages: {
      type: Array,
      required: true,
    },
    selectedEmotion: {
      type: Number,
      required: true,
    },
  },

  directives: {
    clickOutsideHandler,
  },

  data() {
    return {
      InsightsTopIndicatorsEmotionItem,
      open: false,
    };
  },

  computed: {
    dataList() {
      const emotions = indexes.map(e => {
        return {
          active: e.position.api === this.selectedEmotion,
          content: e.label,
          contributionPercentage: this.contributionPercentages[e.position.api],
          emotionIndex: e.position.api,
        };
      });
      return [
        ...emotions, {
          active: this.selectedEmotion === -1,
          content: 'Off',
          contributionPercentage: null,
          emotionIndex: -1,
        },
      ];
    },

    isOff() {
      return this.selectedEmotion === -1;
    },

    textSelected() {
      if (this.selectedEmotion === -99) {
        return 'Default';
      }
      if (this.isOff) {
        return 'Off';
      }
      return nameByApiIndex(this.selectedEmotion);
    },
  },

  methods: {
    onClickOutside() {
      this.open = false;
    },

    onSelectOption(o) {
      if (!o.active) {
        this.$emit('select', o);
      }
      this.open = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.insights-top-indicators-emotion-list {
  @include flex("block", "row", "start", "center");
  cursor: pointer;
  width: 100%;

  .base-dropdown {
    width: inherit;

    .selected {
      @include flex("block", "row", "start", "center");
      @include stretch;
      border: 1px solid $border-color;
      border-radius: $border-radius-medium;
      height: 2.2rem;

      .label {
        @include flex("block", "row", "start", "center");
        @include stretch;
        padding: 0 0 0 0.5rem;

        .icon-off {
          font-size: 12px;
          margin-right: 0.2rem;
          opacity: 0.8;
        }

        .text {
          @include truncate;
          display: inline-block;
          max-width: 100px;
        }
      }

      .icon {
        @include flex("block", "row", "center", "center");
        border-left: 1px solid $border-color;
        height: 2.2rem;
        width: 2.2rem;

        .fa {
          stroke-width: 3px;
          transition: all $interaction-transition-time;

          &.open{
            transform: rotate(180deg);
          }
        }
      }
    }
  }
}
</style>
