<template>
  <section class="insights-top-indicators-edit-dropdown-item">
    <section v-if="data.index === -1" class="header">
      <div class="label">Theme</div>
      <div class="vol item-right">Vol</div>
      <div class="adorescore item-right">Score</div>
    </section>
    <section v-else class="item">
      <div class="status-icon item-left">
        <i v-if="statusActive" class="fa-solid fa-circle-check icon-check" />
        <span v-else class="circle" />
      </div>
      <div class="label item-left">
        <span class="text">{{ textContent }}</span>
      </div>
      <div class="vol item-right">
        <span class="text">{{ themeVol }}%</span>
      </div>
      <div class="adorescore item-right">
        <adorescore-box-mini :bucket="bucket" :score="adoreScore" />
      </div>
    </section>
  </section>
</template>

<script>
import { mapGetters } from 'vuex';
import AdorescoreBoxMini from '@/components/AdorescoreBox/AdorescoreBoxMini';

export default {
  name: 'insights-top-indicators-edit-dropdown-item',

  components: {
    AdorescoreBoxMini,
  },

  props: {
    data: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapGetters('benchmark', ['classifyAdorescore']),

    ...mapGetters('datasets', ['themeVolumePercentage']),

    adoreScore() {
      return Math.round(this.data.polarity * 100);
    },

    bucket() {
      return this.classifyAdorescore(this.adoreScore);
    },

    statusActive() {
      return this.data.active;
    },

    textContent() {
      return this.data.content;
    },

    themeVol() {
      return this.themeVolumePercentage(this.data.datasetId, this.data.numOfDocuments, true);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.insights-top-indicators-edit-dropdown-item {
  // todo - it looks fine on chrome without padding, but not firefox
  padding-right: 10px; // for the y-scroll-bar
  width: 427px;

  .header {
    color: #5F52C5;
    display: grid;
    font-size: $font-size-xxs;
    font-weight: $font-weight-bold;
    grid-template-columns: auto 3rem 4rem;
    margin: 1rem 0.5rem 0 0.5rem;
    text-transform: uppercase;

    .item-right {
      @include flex("block", "row", "end", "center");
    }

    .label {
      @include stretch;
    }
  }

  .item {
    border-bottom: 1px solid $border-color;
    cursor: pointer;
    display: grid;
    grid-template-columns: 1.2rem auto 3rem 4rem;
    height: 60px;
    margin: 0 0.5rem;

    &:hover {
      .status-icon {
        .circle {
          border-color: clr("blue");
        }
      }
    }

    .item-left {
      @include flex("block", "row", "start", "center");
    }

    .item-right {
      @include flex("block", "row", "end", "center");
    }

    .status-icon {
      .circle {
        border: 2px solid $border-color;
        border-radius: 50%;
        height: 14px;
        width: 14px;
      }

      .icon-check {
        color: clr("blue");
      }
    }

    .label {
      @include stretch;

      .text {
        @include truncate;
        display: inline-block;
        max-width: 260px;
      }
    }
  }
}
</style>
