<template>
  <section class="insights-top-indicators-benchmark-badge"
           :class="{
             blue: color === 'blue',
             purple: color === 'purple',
           }"
  >
    {{ label }}
  </section>
</template>

<script>
export default {
  name: 'insights-top-indicators-benchmark-badge',

  props: {
    label: {
      type: String,
      required: true,
    },
    color: {
      type: String,
      default: 'purple',
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.insights-top-indicators-benchmark-badge {
  @include flex("block", "row", "start", "start");

  border-radius: 1rem;
  color: clr('white');
  font-size: $font-size-xxs;
  font-weight: $font-weight-bold;
  padding: 0.1rem 0.3rem;
  text-transform: uppercase;
  width: min-content;

  &.purple {
    background-color: $insights-compare-benchmark;
    border: 1px solid $insights-compare-benchmark-dark;
  }

  &.blue {
    background-color: $insights-compare-comparison;
    border: 1px solid $insights-compare-comparison-dark;
  }
}
</style>
