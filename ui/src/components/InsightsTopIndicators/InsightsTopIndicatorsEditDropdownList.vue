<template>
  <section class="insights-top-indicators-edit-dropdown-list"
           v-click-outside-handler="{
             handler: 'onClickOutside',
             excludedParentClasses: ['base-dropdown-list', 'base-dropdown-search'],
           }"
  >
    <section v-if="showRenaming" class="renaming">
      <i class="fa-regular fa-pencil"></i>
      <b>Edit</b>
      <section class="renaming-input" >
        <section class="input">
          <base-input
              v-model="localRename"
              :focus="true"
              @submit="confirmRename"
          />
        </section>
        <base-tag
            :background-colour="'#00BD1E'"
            :colour="'#00A51A'"
            :colour-hover="'#00A51A'"
            :text="'Confirm'"
            :text-colour="'white'"
            :border-radius="'2rem'"
            :padding="'0.3rem 0.5rem 0.3rem 0.5rem'"
            @click="confirmRename"
        />
        <i class="fa-solid fa-x x-icon" @click.stop="cancelRename" />
      </section>
    </section>
    <base-dropdown v-else :component="InsightsTopIndicatorsEditDropdownItem"
                   :data="dataList"
                   :height="500"
                   :open="open"
                   :search="true"
                   @select="onSelectOption"
                   tooltip-class="tooltip-insights-top-indicators-dropdown">
      <section class="selected" @click="open = !open">
        <div class="label">
          <span class="text"
                v-tooltip.bottom="{
                              class: 'tooltip-base-dark',
                              content: textSelected,
                              delay: 0,
                              visible: visible,
                           }"
                @mouseenter="onMouseOverName"
          >
            {{ textSelected }}
          </span>
        </div>
        <div class="icon">
          <i class="fa fa-caret-down" :class="{ open }"></i>
        </div>
      </section>
    </base-dropdown>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseDropdown from '@/components/Dropdown/BaseDropdown';
import BaseInput from '@/components/Base/BaseInput';
import BaseTag from '@/components/Base/BaseTag';
import clickOutsideHandler from '@/directives/click-outside-handler';
import InsightsTopIndicatorsEditDropdownItem from '@/components/InsightsTopIndicators/InsightsTopIndicatorsEditDropdownItem';
import ThemesListItemTextError from '@/components/ThemesList/ThemesListItemTextError';

import { themeApi } from '@/services/api';

export default {
  name: 'insights-top-indicators-edit-dropdown-list',

  components: {
    BaseDropdown,
    BaseInput,
    BaseTag,
  },

  props: {
    index: {
      type: Number,
      required: true,
    },
    item: {
      type: Object,
      required: true,
    },
    showRenaming: {
      type: Boolean,
      require: true,
    },
  },

  directives: {
    clickOutsideHandler,
  },

  data() {
    return {
      InsightsTopIndicatorsEditDropdownItem,
      localRename: '',
      open: false,
      visible: false,
    };
  },

  computed: {
    ...mapGetters('datasetsInsights', ['topIndicators', 'topIndicatorsDropdownList']),

    ...mapState('datasetsInsights', ['editTopIndicatorsDatasetId', 'editTopIndicatorsList']),

    dataList() {
      const themeList = this.topIndicatorsDropdownList(this.editTopIndicatorsDatasetId)
        .filter(o => !this.ignoredIdList.includes(o.id))
        .map((o, i) => {
          return {
            ...o,
            active: o.id === this.item.id,
            content: o.topicLabel,
            index: i,
          };
        });

      return [
        {
          // header
          content: '',
          index: -1,
        }, ...themeList,
      ];
    },

    // other themes which are selected alr
    ignoredIdList() {
      return this.editTopIndicatorsList
        .filter(o => o.id !== this.item.id)
        .map(o => o.id);
    },

    textSelected() {
      return this.item.topicLabel;
    },
  },

  watch: {
    editTopIndicatorsList() {
      this.localRename = this.item.topicLabel;
    },
  },

  created() {
    this.localRename = this.item.topicLabel;
  },

  methods: {
    ...mapActions('datasetsInsights', ['renameThemeSet', 'updateEditTopIndicatorsList']),

    ...mapActions('modal', ['setModalComponent']),

    cancelRename() {
      this.localRename = this.item.topicLabel;
      this.$emit('close');
    },

    async confirmRename() {
      if (this.localRename === this.item.topicLabel) {
        this.$emit('close');
        return;
      }

      if (!this.isLabelUnique(this.localRename)) {
        this.setModalComponent({ component: ThemesListItemTextError });
        return;
      }

      await themeApi.renameTopic(
        this.item.datasetId,
        this.item.id,
        this.localRename,
        false,
      );

      this.renameThemeSet({ datasetId: this.item.datasetId, topicId: this.item.id, label: this.localRename });
      const value = this.dataList.find(i => i.id === this.item.id);
      this.updateEditTopIndicatorsList({ index: this.index, value });

      this.$emit('close');
    },

    isLabelUnique(label) {
      const themeList = this.topIndicatorsDropdownList(this.editTopIndicatorsDatasetId);
      const labels = themeList.map(t => t.topicLabel.toLowerCase());

      return !labels.includes(label.toLowerCase().trim());
    },

    onClickOutside() {
      this.open = false;
    },

    onSelectOption(o) {
      if (o.index < 0) {
        return;
      }
      this.updateEditTopIndicatorsList({ index: this.index, value: o });
      this.localRename = o.topicLabel;
      this.open = false;
    },

    onMouseOverName(e) {
      this.visible = e.target.offsetWidth + 1 < e.target.scrollWidth;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.insights-top-indicators-edit-dropdown-list {
  @include flex("block", "row", "start", "center");
  cursor: pointer;
  width: inherit;

  .base-dropdown {
    width: inherit;

    .selected {
      @include flex("block", "row", "start", "center");
      @include stretch;
      border: 1px solid $border-color;
      border-radius: $border-radius-medium;
      height: 2.2rem;

      .label {
        @include flex("block", "row", "start", "center");
        @include stretch;
        padding: 0 0 0 0.5rem;

        .text {
          @include truncate;
          display: inline-block;
          max-width: 200px;
        }
      }

      .icon {
        @include flex("block", "row", "center", "center");
        border-left: 1px solid $border-color;
        height: 2.2rem;
        width: 2.2rem;

        .fa {
          stroke-width: 3px;
          transition: all $interaction-transition-time;

          &.open{
            transform: rotate(180deg);
          }
        }
      }
    }
  }

  .renaming {
    align-items: center;
    display: grid;
    grid-template-columns: 1.4rem 3rem 1fr;

    .renaming-input {
      @include panel;

      align-items: center;
      border: 1px solid #977ED2;
      display: grid;
      grid-template-columns: auto 5rem 2rem;

      .input {
        border-right: 1px solid rgba(152, 126, 210, 0.1);

        .base-input {
          border: none;
        }
      }
    }

    .x-icon {
      @include flex('block', 'row', 'center', 'center');

      border: 1px solid #2A1854;
      border-radius: 50%;
      -moz-border-radius: 50%;
      -webkit-border-radius: 50%;
      cursor: pointer;
      font-size: 0.5rem;
      height: 1.3rem;
      transition: all $interaction-transition-time;
      width: 1.3rem;

      &:hover {
        background-color: $helper-txt-hvr;
        color: clr('white');
      }
    }
  }
}
</style>
