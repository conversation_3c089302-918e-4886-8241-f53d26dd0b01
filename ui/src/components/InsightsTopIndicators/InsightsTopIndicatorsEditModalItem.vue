<template>
  <section class="insights-top-indicators-edit-modal-item">
    <section class="number section-right">
      <common-index-number :index="index + 1"></common-index-number>
    </section>
    <section class="order">
      <span class="icon" :class="{ posFirstItem }" @click="moveUp"><i class="fa fa-caret-up"></i></span>
      <span class="icon" :class="{ posLastItem }" @click="moveDown"><i class="fa fa-caret-down"></i></span>
    </section>
    <section class="dropdown">
      <insights-top-indicators-edit-dropdown-list :index="index"
                                                  :item="item"
                                                  :show-renaming="showRenaming"
                                                  @close="showRenaming = false"
      />
    </section>
    <section class="edit section-right">
      <i class="fa-solid fa-pen icon"
         :class="{ 'show-renaming': showRenaming }"
         @click.stop="showRenaming = true"
         v-tooltip.bottom="{ content: 'Edit Theme', class: 'tooltip-base-dark', delay: 0 }"
      />
    </section>
    <section class="emotion">
      <insights-top-indicators-emotion-list :contribution-percentages="contributionPercentages"
                                            :selected-emotion="selectedEmotion"
                                            @select="onSelectEmotion"
      />
    </section>
    <section class="vol section-right">
      <span class="text">{{ themeVol }}%</span>
    </section>
    <section class="adorescore section-right">
      <adorescore-box-mini :bucket="bucket" :score="adoreScore" />
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import AdorescoreBoxMini from '@/components/AdorescoreBox/AdorescoreBoxMini';
import CommonIndexNumber from '@/components/CommonComponent/CommonIndexNumber';
import InsightsTopIndicatorsEditDropdownList from '@/components/InsightsTopIndicators/InsightsTopIndicatorsEditDropdownList';
import InsightsTopIndicatorsEmotionList from '@/components/InsightsTopIndicators/InsightsTopIndicatorsEmotionList';

export default {
  name: 'insights-top-indicators-edit-modal-item',

  components: {
    AdorescoreBoxMini,
    CommonIndexNumber,
    InsightsTopIndicatorsEditDropdownList,
    InsightsTopIndicatorsEmotionList,
  },

  props: {
    index: {
      type: Number,
      required: true,
    },
    item: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      showRenaming: false,
    };
  },

  computed: {
    ...mapGetters('benchmark', ['classifyAdorescore']),

    ...mapGetters('datasets', ['themeVolumePercentage']),

    adoreScore() {
      return Math.round(this.item.polarity * 100);
    },

    bucket() {
      return this.classifyAdorescore(this.adoreScore);
    },

    contributionPercentages() {
      return this.item.contributionPercentages || [];
    },

    posFirstItem() {
      return this.index <= 0;
    },

    posLastItem() {
      return this.index >= 4;
    },

    selectedEmotion() {
      return this.item.selectedEmotion != null ? this.item.selectedEmotion : -99; // -99 for null val
    },

    textSelected() {
      return this.item.topicLabel || '';
    },

    themeVol() {
      return this.themeVolumePercentage(this.item.datasetId, this.item.numOfDocuments, true);
    },
  },

  methods: {
    ...mapActions('datasetsInsights', ['updateEditTopIndicatorsList']),

    moveDown() {
      if (!this.posLastItem) {
        this.updateEditTopIndicatorsList({ index: this.index, indexTo: this.index + 1 });
        this.showRenaming = false;
      }
    },

    moveUp() {
      if (!this.posFirstItem) {
        this.updateEditTopIndicatorsList({ index: this.index, indexTo: this.index - 1 });
        this.showRenaming = false;
      }
    },

    onSelectEmotion(emotion) {
      const newVal = {
        ...this.item,
        selectedEmotion: emotion.emotionIndex,
      };
      this.updateEditTopIndicatorsList({ index: this.index, value: newVal });
      this.showRenaming = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.insights-top-indicators-edit-modal-item {
  display: grid;
  grid-template-columns: 1.4rem 4rem auto 3rem 9rem 3rem 4rem;
  padding: 1rem 0;

  .section-right {
    @include flex("block", "row", "end", "center");
  }

  .number {
    margin-right: 0.5rem;

    .common-index-number {
      background-color: #5F52C5;
    }
  }

  .order {
    @include flex("block", "row", "start", "center");

    .icon {
      @include flex("block", "row", "center", "center");
      border: 1px solid $border-color-hover;
      border-radius: $border-radius-rounded;
      cursor: pointer;
      height: 1.2rem;
      margin-right: 0.5rem;
      width: 1.2rem;

      &.posFirstItem, &.posLastItem {
        cursor: not-allowed;
        opacity: 0.5;
      }

      &:hover {
        border-color: $border-color-active;
      }

      &:last-child {
        margin-right: 0;
      }

      .fa-caret-up {
        padding-bottom: 0.3rem;
      }

      .fa-caret-down {
        padding-bottom: 0.1rem;
      }
    }
  }

  .dropdown {
    @include flex("block", "row", "start", "center");
    width: 100%;
  }

  .emotion {
    @include flex("block", "row", "start", "center");
    width: 100%;
  }

  .edit {
    @include flex("block", "row", "start", "center");

    .icon {
      @include flex('block', 'row', 'center', 'center');

      border: 1px solid rgba(36, 18, 77, 0.45);
      border-radius: 50%;
      -moz-border-radius: 50%;
      -webkit-border-radius: 50%;
      cursor: pointer;
      font-size: 0.6rem;
      height: 1.4rem;
      margin-left: 1rem;
      transition: all $interaction-transition-time;
      width: 1.4rem;

      &.show-renaming, &:hover {
        background-color: $helper-txt-hvr;
        color: clr('white');
      }
    }
  }

  .vol, .adorescore {
    font-size: $font-size-sm;
  }
}
</style>
