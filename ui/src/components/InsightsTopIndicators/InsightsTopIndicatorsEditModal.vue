<template>
  <section class="insights-top-indicators-edit-modal">
    <section class="header">
      <span class="header-item header-label">Select Top Indicators for </span>
      <span class="header-item">
        <insights-top-indicators-benchmark-badge :color="isEditingBenchmark ? 'purple' : 'blue'"
                                                 class="header-item"
                                                 :label="textBadgeLabel"
        />
      </span>
    </section>
    <section class="body">
      <div class="instruction">
        <span class="text">Select and Order Top Indicators</span>
        <i class="fa fa-long-arrow-down" />
      </div>
      <div class="header-list">
        <span class="header-item header-right number">#</span>
        <span class="header-item order"></span>
        <span class="header-item theme">Select Theme</span>
        <span class="header-item edit-icon"></span>
        <span class="header-item emotion">Emotion</span>
        <span class="header-item header-right volume">Vol</span>
        <span class="header-item header-right adore-score">Score</span>
      </div>
      <div class="list">
        <insights-top-indicators-edit-modal-item v-for="(item, i) in topIndicatorList"
                                                 :key="i"
                                                 :index="i"
                                                 :item="item"
                                                 class="list-item">
        </insights-top-indicators-edit-modal-item>
      </div>
    </section>
    <section class="footer">
      <base-button class="cancel" colour="light" type="link" @click="onClickCancel">Cancel</base-button>
      <base-button class="confirm" colour="base" @click="onClickConfirm">Save</base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';
import BaseButton from '@/components/Base/BaseButton';
import InsightsTopIndicatorsBenchmarkBadge from '@/components/InsightsTopIndicators/InsightsTopIndicatorsBenchmarkBadge';
import InsightsTopIndicatorsEditModalItem from '@/components/InsightsTopIndicators/InsightsTopIndicatorsEditModalItem';
import { datasetInsightApiV0 } from '@/services/api';

export default {
  name: 'insights-top-indicators-edit-modal',

  components: {
    BaseButton,
    InsightsTopIndicatorsBenchmarkBadge,
    InsightsTopIndicatorsEditModalItem,
  },

  computed: {
    ...mapGetters('datasets', ['get']),

    ...mapState('datasetsInsights', [
      'datasetBenchmark',
      'editTopIndicatorsDatasetId',
      'editTopIndicatorsList',
    ]),

    isEditingBenchmark() {
      return this.editTopIndicatorsDatasetId === this.datasetBenchmark;
    },

    textBadgeLabel() {
      return this.isEditingBenchmark ? 'Benchmark' : 'Comparison';
    },

    textDatasetLabel() {
      return this.get(this.editTopIndicatorsDatasetId).label;
    },

    topIndicatorList() {
      return this.editTopIndicatorsList;
    },
  },

  methods: {
    ...mapActions('datasetsInsights', [
      'setEditTopIndicatorsDatasetId',
      'setEditTopIndicatorsList',
      'updateScorecards',
    ]),

    ...mapActions('modal', ['closeModal']),

    onClickCancel() {
      this.setEditTopIndicatorsDatasetId({ value: null });
      this.setEditTopIndicatorsList({ value: [] });
      this.closeModal();
    },

    async onClickConfirm() {
      const list = this.editTopIndicatorsList.map(o => {
        return {
          themeId: o.id,
          selectedEmotion: typeof o.selectedEmotion === 'number' ? o.selectedEmotion : null,
        };
      });

      await datasetInsightApiV0.updateTopIndicators(this.editTopIndicatorsDatasetId, list);
      const scorecard = await datasetInsightApiV0.getScorecard(this.editTopIndicatorsDatasetId);
      this.updateScorecards({ ids: [this.editTopIndicatorsDatasetId], values: [scorecard] });

      this.setEditTopIndicatorsDatasetId({ value: null });
      this.setEditTopIndicatorsList({ value: [] });
      this.closeModal();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.insights-top-indicators-edit-modal {
  @include modal;
  width: 700px;

  .header {
    @include flex("block", "row", "start", "center");
    font-size: $font-size-base;
    font-weight: $font-weight-medium;
    padding: 1.5rem;

    .header-item {
      margin-right: 1rem;

      &:last-child {
        margin-right: 0;
      }

      .fa {
        font-size: $font-size-lg;

        &.fa-long-arrow-up {
          margin-bottom: 4px;
        }
      }
    }

    .header-label {
      @include truncate;
      max-width: 450px;
    }
  }

  .body {
    @include flex("block", "column", "start", "start");
    background-color: clr('white');

    .instruction {
      @include flex("block", "row", "start", "center");

      .text {
        font-size: $font-size-sm;
        margin-right: 0.5rem;
      }

      .fa {
        font-size: $font-size-base;
      }
    }

    .header-list {
      border-bottom: 1px solid $border-color;
      color: #5F52C5;
      display: grid;
      font-size: $font-size-xxs;
      font-weight: $font-weight-bold;
      line-height: 2rem;
      grid-template-columns: 1.4rem 4rem auto 3rem 9rem 3rem 4rem;
      margin-top: 1rem;
      padding-bottom: 0.5rem;
      text-transform: uppercase;
      width: 100%;

      .header-right {
        @include flex("block", "row", "end", "start");
      }

      .number {
        margin-right: 0.5rem;
      }
    }

    .list {
      width: 100%;

      .list-item {
        border-bottom: 1px solid $border-color;
        width: inherit;

        &:last-child {
          border-bottom: none;
        }
      }
    }
  }

  .footer {
    padding: 1rem 1.5rem;

    .cancel {
      margin-left: -1.8rem;
    }
  }
}
</style>
