<template>
  <section class="insights-top-indicators-item">
    <div class="index">
      <common-index-number :index="index + 1"></common-index-number>
    </div>
    <section class="content">
      <section class="label"
               v-tooltip.bottom="{
                              class: 'tooltip-insights-label',
                              content: textLabel,
                              delay: 0,
                              visible: visible,
                           }"
               @mouseenter="onMouseOverName">
        {{ textLabel }}
      </section>
      <section class="contribution" v-if="showContribution">
        Contributes <span class="strong">{{ textEmotion }}</span>
      </section>
      <section class="swot"
               :class="{
                 strength: isStrength,
                 opportunity: isOpportunity,
                 threat: isThreat,
                 weakness: isWeakness,
               }"
      >
        <circle-icon class="icon circle" />
        {{ textSwot }}
      </section>
    </section>
    <section class="adore-score">
      <common-adore-score-square :colorBorder="true"
                                 :footer="true"
                                 :score="adoreScore"
                                 :signifier="true"
                                 :size=2.2>
      </common-adore-score-square>
    </section>
    <section class="vol">
      <common-adore-score-square :percentage="true"
                                 :score="themeVol"
                                 :size=2.2>
      </common-adore-score-square>
    </section>
  </section>
</template>

<script>
import { CircleIcon } from 'vue-feather-icons';
import { mapGetters } from 'vuex';
import CommonAdoreScoreSquare from '@/components/CommonComponent/CommonAdoreScoreSquare';
import CommonIndexNumber from '@/components/CommonComponent/CommonIndexNumber';
import Index from '@/enum/index';

export default {
  name: 'insights-top-indicators-item',

  components: {
    CircleIcon,
    CommonAdoreScoreSquare,
    CommonIndexNumber,
  },

  props: {
    index: {
      type: Number,
      required: true,
    },
    item: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      visible: false,
    };
  },

  computed: {
    ...mapGetters('datasets', ['themeVolumePercentage']),

    adoreScore() {
      return Math.round(this.item.polarity * 100);
    },

    isOpportunity() {
      return this.textSwot.toLowerCase() === 'opportunity';
    },

    isStrength() {
      return this.textSwot.toLowerCase() === 'strength';
    },

    isThreat() {
      return this.textSwot.toLowerCase() === 'threat';
    },

    isWeakness() {
      return this.textSwot.toLowerCase() === 'weakness';
    },

    showContribution() {
      return this.item.selectedEmotion !== -1;
    },

    textEmotion() {
      const { contributionPercentages, selectedEmotion } = this.item;
      let val;
      let valIndex;

      if (typeof selectedEmotion !== 'number' || selectedEmotion < 0) {
        val = Math.max(...contributionPercentages);
        valIndex = contributionPercentages.indexOf(val);
      } else {
        val = contributionPercentages[selectedEmotion];
        valIndex = selectedEmotion;
      }

      const emotion = Index.enumValues.find(o => o.backendIndex() === valIndex);

      return `${val < 1 ? '<1' : val}% ${emotion.titleCase()}` || '';
    },

    textLabel() {
      return this.item.topicLabel || '';
    },

    textSwot() {
      return `${this.item.swot?.attribute}` || '';
    },

    themeVol() {
      return this.themeVolumePercentage(this.item.datasetId, this.item.numOfDocuments, true);
    },
  },

  methods: {
    onMouseOverName(e) {
      this.visible = e.target.offsetWidth + 1 < e.target.scrollWidth;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.insights-top-indicators-item {
  @include rigid;

  display: grid;
  grid-template-columns: 1.5rem 1fr min-content 3rem;
  padding: 0 1rem;
  width: 100%;

  .index {
    margin-right: 0.5rem;
    //margin-top: 1px;
  }

  .content {
    @include flex("block", "column", "start", "stretch");

    line-height: 1.2rem;
    min-height: 72px;

    .label {
      @include truncate;

      color: #454396;
      font-size: $font-size-sm;
      font-weight: $font-weight-bold;
      max-width: 230px;
    }

    .contribution {
      font-size: $font-size-xs;
      margin-top: 0.2rem;

      .strong {
        font-weight: $font-weight-bold;
      }
    }

    .swot {
      @include flex("block", "row", "start", "center");
      font-size: $font-size-xs;
      font-weight: $font-weight-bold;

      &.opportunity {
        color: $swot-opportunity;
        .circle {
          fill: $swot-opportunity;
        }
      }

      &.strength {
        color: $swot-strength;
        .circle {
          fill: $swot-strength;
        }
      }

      &.threat {
        color: $swot-threat;
        .circle {
          fill: $swot-threat;
        }
      }

      &.weakness {
        color: $swot-weakness;
        .circle {
          fill: $swot-weakness;
        }
      }

      .circle {
        margin-right: 0.3rem;
        width: 0.7rem;
      }
    }
  }

  .adore-score {
    @include flex("block", "row", "end", "start");
  }

  .vol {
    @include flex("block", "row", "end", "start");
  }
}
</style>
