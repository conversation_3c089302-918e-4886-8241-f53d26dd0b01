<template>
  <section class="insights-top-indicators" :class="[side]">
    <section class="badge">
      <insights-top-indicators-benchmark-badge :color="isBenchmark ? 'purple' : 'blue'" :label="textBadgeLabel" />
    </section>

    <section class="dataset-label">
      <div class="left" :class="{ presentation }">
        <span class="strong">Top Indicators</span> -
        <span v-tooltip.bottom="{ class: 'tooltip-insights-label', content: textDatasetLabel, delay: 1 }">
          {{ textDatasetLabel }}
        </span>
      </div>
      <div v-if="!presentation" class="right">
        <section class="dropdown-btn"
                 v-tooltip.bottom.end.notrigger="{
                   html: tooltipId,
                   class: 'tooltip-insights-dropdown',
                   delay: 0,
                   visible: dropdownOpen,
                 }"
                 @click.stop="onClickDropdown">
          •••
        </section>
        <insights-top-indicators-edit-tooltip :dataset-id="datasetId"
                                              :id="tooltipId"
                                              @close="dropdownOpen = false"/>
      </div>
    </section>

    <section class="header-list">
      <span class="header-item header-right header-number">#</span>
      <span class="header-item">Theme</span>
      <span class="header-item header-right">AdoreScore</span>
      <span class="header-item header-right">Vol</span>
    </section>

    <section class="top-indicators-list">
      <insights-top-indicators-item v-for="(item, i) in topIndicatorList"
                                    :key="i"
                                    :index="i"
                                    :item="item"
                                    class="list-item">
      </insights-top-indicators-item>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import InsightsTopIndicatorsBenchmarkBadge from '@/components/InsightsTopIndicators/InsightsTopIndicatorsBenchmarkBadge';
import InsightsTopIndicatorsEditTooltip from '@/components/InsightsTopIndicators/InsightsTopIndicatorsEditTooltip';
import InsightsTopIndicatorsItem from '@/components/InsightsTopIndicators/InsightsTopIndicatorsItem';

export default {
  name: 'insights-top-indicators',

  components: {
    InsightsTopIndicatorsBenchmarkBadge,
    InsightsTopIndicatorsEditTooltip,
    InsightsTopIndicatorsItem,
  },

  props: {
    datasetId: {
      type: Number,
      required: true,
    },

    presentation: {
      type: Boolean,
      default: false,
    },

    side: {
      type: String,
      default: 'left',
    },
  },

  data() {
    return {
      dropdownOpen: false,
    };
  },

  computed: {
    ...mapGetters('datasets', ['get']),

    ...mapGetters('datasetsInsights', ['topIndicators']),

    ...mapState('datasetsInsights', ['datasetBenchmark', 'editTopIndicatorsDropdownId']),

    isBenchmark() {
      return this.datasetId === this.datasetBenchmark;
    },

    tooltipId() {
      return `insights-top-indicators-edit-btn-${this.isBenchmark ? 'benchmark' : 'comparison'}`;
    },

    textBadgeLabel() {
      return this.isBenchmark ? 'Benchmark' : 'Comparison';
    },

    textDatasetLabel() {
      return this.get(this.datasetId).label;
    },

    topIndicatorList() {
      return this.topIndicators(this.datasetId);
    },
  },

  watch: {
    editTopIndicatorsDropdownId() {
      if (this.editTopIndicatorsDropdownId !== this.datasetId) {
        this.dropdownOpen = false;
      }
    },
  },

  beforeDestroy() {
    this.setEditTopIndicatorsDropdownId({ value: null });
  },

  methods: {
    ...mapActions('datasetsInsights', ['setEditTopIndicatorsDropdownId']),

    onClickDropdown() {
      this.dropdownOpen = !this.dropdownOpen;
      this.setEditTopIndicatorsDropdownId({ value: this.datasetId });
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.insights-top-indicators {
  &.left {
    .badge {
      margin-left: 30px;
    }

    .dataset-label, .header-list, .top-indicators-list .list-item {
      padding-left: 30px;
    }
  }

  &.right {
    .badge {
      margin-right: 30px;
    }

    .dataset-label, .header-list, .top-indicators-list .list-item {
      padding-right: 30px;
    }
  }

  .badge {
    margin: 1rem;
  }

  .dataset-label {
    @include flex("block", "row", "between", "center");

    margin-top: 1rem;
    padding: 0 1rem;

    .left {
      @include truncate;
      font-size: $font-size-sm;
      max-width: 310px;

       &.presentation {
         max-width: 360px;
       }

      .strong {
        font-weight: $font-weight-bold;
      }
    }

    .right {
      .dropdown-btn {
        background-color: clr('white');
        border: 1px solid $insights-bdr-key;
        border-radius: $border-radius-medium;
        cursor: pointer;
        font-size: $font-size-xs;
        padding: 0.1rem 0.4rem;
        transition: all $interaction-transition-time;
        user-select: none;

        &:hover {
          border-color: $body-copy;
        }
      }
    }
  }

  .header-list {
    display: grid;
    font-size: $font-size-xs;
    font-weight: $font-weight-bold;
    grid-template-columns: 1.5rem auto 4rem 3rem;
    margin-top: 1rem;
    padding: 0 1rem;
    text-transform: uppercase;
    width: 100%;

    .header-right {
      @include flex("block", "row", "end", "start");
    }

    .header-number {
      @include flex("block", "row", "center", "center");

      margin-right: 0.5rem;
    }
  }

  .top-indicators-list {
    margin-top: 1rem;

    .list-item {
      border-bottom: 1px solid $border-color;
      margin-bottom: 1rem;
      padding-bottom: 0.5rem;

      &:last-child {
        border-bottom: none;
        margin-bottom: 0;
      }
    }
  }
}
</style>
