<template>
  <section class="insights-top-indicators-edit-tooltip">
    <h3>Top Indicators</h3>
    <section class="item" @click="onClickEdit">
      <edit-2-icon class="icon"/>
      <span class="text">Edit</span>
    </section>
  </section>
</template>

<script>
import { Edit2Icon } from 'vue-feather-icons';
import { mapActions, mapGetters } from 'vuex';

import BlurCloseable from '@/components/Mixins/BlurCloseable';
import InsightsTopIndicatorsEditModal from '@/components/InsightsTopIndicators/InsightsTopIndicatorsEditModal';

export default {
  name: 'insights-top-indicators-edit-tooltip',

  components: {
    Edit2Icon,
  },

  props: {
    datasetId: {
      type: Number,
      required: true,
    },
  },

  data() {
    return {
      InsightsTopIndicatorsEditModal,
    };
  },

  mixins: [BlurCloseable],

  computed: {
    ...mapGetters('datasetsInsights', ['topIndicators']),
  },

  methods: {
    ...mapActions('datasetsInsights', ['setEditTopIndicatorsDatasetId', 'setEditTopIndicatorsList']),

    ...mapActions('modal', ['setModalComponent']),

    onClickEdit() {
      this.setEditTopIndicatorsDatasetId({ value: this.datasetId });
      this.setEditTopIndicatorsList({ value: this.topIndicators(this.datasetId) });
      this.setModalComponent({ component: InsightsTopIndicatorsEditModal });
      this.$emit('close');
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-top-indicators-edit-tooltip {
  @include panel;
  padding: 0.8rem;

  h3 {
    font-size: 0.7rem;
    font-weight: $font-weight-bold;
    padding: 0 0.2rem;
    text-transform: uppercase;
  }

  .item {
    @include flex("block", "row", "start", "center");
    border-radius: $border-radius-medium;
    cursor: pointer;
    padding: 0.2rem 0.4rem;
    margin-top: 0.3rem;

    &:hover {
      background-color: lighten($body-copy, 75%);
    }

    .icon {
      height: $font-size-xs;
      width: $font-size-xs;
    }

    .text {
      font-size: 0.7rem;
      margin-left: 0.4rem;
    }
  }
}
</style>
