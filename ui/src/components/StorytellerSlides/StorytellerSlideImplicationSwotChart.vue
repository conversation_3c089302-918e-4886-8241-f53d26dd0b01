<template>
  <section class="storyteller-slide-implication-swot-chart">
    <section v-if="potentialSwot" class="double-emotions">
      <section class="swot" :class="{
        opportunity: swot === 'Opportunity',
        strength: swot === 'Strength',
        threat: swot === 'Threat',
        weakness: swot === 'Weakness',}"
      >
        <span class="swot-1">{{swot}}</span>
        <span class="swot-2">{{swot}}</span>
        <span class="swot-3">{{swot}}</span>
      </section>
      <section class="potential-swot" :class="{
        opportunity: potentialSwot === 'Opportunity',
        strength: potentialSwot === 'Strength',
        threat: potentialSwot === 'Threat',
        weakness: potentialSwot === 'Weakness',}"
      >
        <span class="swot-1">{{potentialSwot}}</span>
        <span class="swot-2">{{potentialSwot}}</span>
        <span class="swot-3">{{potentialSwot}}</span>
        <span class="swot-4">{{potentialSwot}}</span>
      </section>
    </section>
    <section v-else class="single-emotion">
      <section class="swot" :class="{
        opportunity: swot === 'Opportunity',
        strength: swot === 'Strength',
        threat: swot === 'Threat',
        weakness: swot === 'Weakness',}"
      >
        <span class="swot-1">{{swot}}</span>
        <span class="swot-2">{{swot}}</span>
        <span class="swot-3">{{swot}}</span>
        <span class="swot-4">{{swot}}</span>
        <span class="swot-5">{{swot}}</span>
        <span class="swot-6">{{swot}}</span>
      </section>
    </section>
  </section>
</template>

<script>
export default {
  name: 'storyteller-slide-implication-swot-chart',

  props: {
    slideData: {
      type: Object,
      required: true,
    },
  },

  computed: {
    potentialSwot() {
      return this.slideData.implicationSlideData.potentialSwot;
    },

    swot() {
      return this.slideData.implicationSlideData.swot;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-implication-swot-chart {
  @include flex("block", "column", "center", "center");

  font-weight: 600;
  height: 100%;
  width: 100%;

  .double-emotions {
    @include flex("block", "column", "center", "center");

    height: 100%;
    width: 100%;

    .swot {
      @include flex("block", "column", "center", "center");

      .swot-1 {
        font-size: 0.32em;
        padding-bottom: 0.8em;
      }

      .swot-2 {
        font-size: 0.42em;
        opacity: 0.36;
        padding-bottom: 0.5em;
      }

      .swot-3 {
        font-size: 0.7em;
        opacity: 0.15;
      }
    }

    .potential-swot {
      @include flex("block", "column", "center", "center");

      .swot-1 {
        font-size: 1.58em;
        opacity: 0.11;
      }

      .swot-2 {
        font-size: 1.9em;
        opacity: 0.5;
      }

      .swot-3 {
        font-size: 2.28em;
        opacity: 0.7;
      }

      .swot-4 {
        font-size: 3em;
      }
    }
  }

  .single-emotion {
    @include flex("block", "column", "center", "center");

    font-size: 2em;
    height: 100%;
    width: 100%;

    .swot {
      @include flex("block", "column", "center", "center");

      margin-bottom: 1em;

      .swot-1 {
        opacity: 0.04;
      }

      .swot-2 {
        opacity: 0.1;
      }

      .swot-3 {
        opacity: 0.3;
      }

      .swot-4 {
        opacity: 0.5;
      }

      .swot-5 {
        opacity: 0.6;
      }

      .swot-6 {
        opacity: 1;
      }
    }
  }

  .opportunity {
    color: $storyteller-swot-opportunity;
    font-size: 0.8em;
  }

  .strength {
    color: $storyteller-swot-strength;
  }

  .threat {
    color: $storyteller-swot-threat;
    font-size: 1.6em;
  }

  .weakness {
    color: $storyteller-swot-weakness;
  }
}
</style>
