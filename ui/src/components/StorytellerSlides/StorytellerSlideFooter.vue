<template>
  <section class="storyteller-slide-footer" :style="style" v-if="displayOrgLogo || displayAdoreboardLogo">
    <img :src="localOrgLogo" v-if="orgLogo && displayOrgLogo" class="organisation-logo" alt="organisation-logo"/>
    <img :src="require('@/assets/logo/logo-power-by-adoreboard.svg')" v-if="displayAdoreboardLogo" class="power-by-logo" alt="power-by-logo"/>
  </section>
</template>

<script>

import { mapState } from 'vuex';

export default {
  name: 'storyteller-slide-footer',

  props: {
    slideWidth: {
      type: Number,
      required: true,
    },
  },

  data() {
    return {
      style: {},
    };
  },

  computed: {
    ...mapState('organisation', ['localOrgLogo', 'organisation']),

    ...mapState('storyteller', ['activeReport']),

    displayAdoreboardLogo() {
      return this.activeReport.settings.displaySettings.displayAdoreboardLogo;
    },

    displayOrgLogo() {
      return this.activeReport.settings.displaySettings.displayOrgLogo;
    },

    orgLogo() {
      return this.organisation.settings.logoUrl;
    },

    ratio() {
      return this.slideWidth / 1920;
    },
  },

  watch: {
    slideWidth() {
      this.setComponentsSize();
    },
  },

  mounted() {
    this.setComponentsSize();
  },

  methods: {
    setComponentsSize() {
      this.style = {
        '--logoAdoreboardWidth': `${330 * this.ratio}px`,
      };
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-footer {
  @include flex("block", "row", "space-between", "center");

  border-top: 1px solid rgba(19, 28, 41, 0.3);
  height: 100%;
  width: 100%;

  .organisation-logo {
    @include flex("block", "row", "start", "center");

    max-height: 50%;
    //max-width: 100%; // this is only to help fix Firefox issue
    max-width: var(--logoAdoreboardWidth);
  }

  .power-by-logo {
    @include flex("block", "row", "end", "center");

    margin-left: auto;
    width: var(--logoAdoreboardWidth);
  }
}
</style>
