<template>
  <section class="storyteller-slide-implication-settings">
    <section class="btn">
      <storyteller-slide-setting-btn @click="onClickDropdown"
        :icon="'fa-light fa-arrow-up-arrow-down icon-arrow'"
        :size="'25px'"
        :text="'Switch Graphic'"
      />
      <storyteller-slide-implication-settings-dropdown
        @close="onCloseDropdown"
        v-if="openDropdown"
        v-click-outside-handler="{ handler: 'onCloseDropdown' }"
      />
    </section>
  </section>
</template>

<script>
import BaseButton from '@/components/Base/BaseButton';
import ClickOutsideHandler from '@/directives/click-outside-handler';
import StorytellerSlideImplicationSettingsDropdown from '@/components/StorytellerSlides/StorytellerSlideImplicationSettingsDropdown';
import StorytellerSlideSettingBtn from '@/components/Storyteller/StorytellerSlideSettingBtn';

export default {
  name: 'storyteller-slide-implication-settings',

  components: {
    BaseButton,
    StorytellerSlideImplicationSettingsDropdown,
    StorytellerSlideSettingBtn,
  },

  directives: {
    ClickOutsideHandler,
  },

  data() {
    return {
      openDropdown: false,
    };
  },

  methods: {
    onClickDropdown() {
      this.openDropdown = !this.openDropdown;
    },

    onCloseDropdown() {
      this.openDropdown = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-implication-settings {
  @include flex("block", "row", "start", "center");

  .btn {
    position: relative;
  }
}
</style>
