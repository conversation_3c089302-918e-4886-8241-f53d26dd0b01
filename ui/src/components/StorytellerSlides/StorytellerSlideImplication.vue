<template>
  <section class="storyteller-slide-implication" :style="{fontSize}">
    <section class="margin-top" />
    <section class="body">
      <span class="header">{{slideData.header}}</span>
      <storyteller-slide-implication-metric     v-if="isMetric"    :editable="editable" :slide-data="slideData" :slide-width="slideWidth"/>
      <storyteller-slide-implication-swot       v-if="isSwot"      :editable="editable" :slide-data="slideData" :slide-width="slideWidth"/>
      <storyteller-slide-implication-no-graphic v-if="isNoGraphic" :editable="editable" :slide-data="slideData" :slide-width="slideWidth"/>
    </section>
    <storyteller-slide-footer :slide-width="slideWidth" />
  </section>
</template>

<script>
import StorytellerSlideFooter from '@/components/StorytellerSlides/StorytellerSlideFooter';
import StorytellerSlideImplicationMetric from '@/components/StorytellerSlides/StorytellerSlideImplicationMetric';
import StorytellerSlideImplicationNoGraphic from '@/components/StorytellerSlides/StorytellerSlideImplicationNoGraphic';
import StorytellerSlideImplicationSwot from '@/components/StorytellerSlides/StorytellerSlideImplicationSwot';

export default {
  name: 'storyteller-slide-implication',

  components: {
    StorytellerSlideFooter,
    StorytellerSlideImplicationMetric,
    StorytellerSlideImplicationNoGraphic,
    StorytellerSlideImplicationSwot,
  },

  props: {
    editable: {
      type: Boolean,
      default: false,
    },
    slideData: {
      type: Object,
      required: true,
    },
    slideWidth: {
      type: Number,
      required: true,
    },
  },

  computed: {
    fontSize() {
      return `${50 * this.ratio}px`;
    },

    isMetric() {
      return this.slideData.implicationSlideData.selectedGraphic === 'metric_improvement';
    },

    isNoGraphic() {
      return this.slideData.implicationSlideData.selectedGraphic === 'no_graphic';
    },

    isSwot() {
      return this.slideData.implicationSlideData.selectedGraphic === 'swot_improvement';
    },

    ratio() {
      return this.slideWidth / 1920;
    },
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-implication {
  background-color: #FFF;
  color: $nps-blue;
  display: grid;
  font-family: Inter, serif;
  font-weight: $font-weight-semi-light;
  grid-template-rows: 13% 74% 13%;
  height: 100%;
  padding: 0 2em;
  position: relative;
  width: 100%;

  strong {
    font-weight: 600;
  }

  .body {
    @include flex("block", "column", "start", "start");

    height: 100%;
    position: relative;
    width: 100%;

    .header {
      font-size: 0.5em;
      font-weight: 800;
      letter-spacing: 0.04em;
      opacity: 0.5;
      text-transform: uppercase;
    }
  }

  &:hover {
    .display-settings {
      visibility: visible;
    }
  }
}
</style>
