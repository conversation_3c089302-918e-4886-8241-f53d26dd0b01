<template functional>
  <section class="storyteller-adorescore-box" :class="[$options.colorClass(props)]" v-bind="data.attrs" :style="$options.style(props)">
    <section class="score">
      {{ $options.scoreText(props) }}
    </section>
    <section class="bar"></section>
  </section>
</template>

<script>
import { kebabCase } from 'lodash-es';

export default {
  name: 'storyteller-adorescore-box',

  props: {
    bucket: {
      type: Object,
      required: true,
    },

    score: {
      type: Number,
      required: true,
    },

    width: {
      type: Number,
      required: true,
    },
  },

  colorClass: props => kebabCase(props.bucket.name),

  style: props => {
    return {
      '--barHeight': `${0.05 * props.width}px`,
      '--fontSize': `${0.4 * props.width}px`,
      '--height': `${0.6125 * props.width}px`,
      '--scoreHeight': `${props.width * 9 / 16}px`,
      '--width': `${props.width}px`,
    };
  },

  scoreText: props => {
    return props.score > 0
      ? `+${props.score}`
      : props.score;
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-adorescore-box {
  @include flex("block", "column", "start", "stretch");
  @include rigid;

  background-color: clr('white');
  border-radius: 0.125em;
  border: 0.5px solid; // set border line to 0.5px for fixing Windows display scaling issue
  font-size: var(--fontSize);
  height: var(--height);
  overflow: hidden;
  width: var(--width);

  &.very-poor {
    border-color: $very-poor-bdr;

    .score {
      color: $very-poor-score-txt;
    }

    .bar {
      background: $very-poor-inner-bg;
    }
  }

  &.poor {
    border-color: $poor-bdr;

    .score {
      color: $poor-score-txt;
    }

    .bar {
      background: $poor-inner-bg;
    }
  }

  &.fair {
    border-color: $fair-bdr;

    .score {
      color: $fair-score-txt;
    }

    .bar {
      background: $fair-inner-bg;
    }
  }

  &.good {
    border-color: $good-bdr;

    .score {
      color: $good-score-txt;
    }

    .bar {
      background: $good-inner-bg;
    }
  }

  &.very-good {
    border-color: $very-good-bdr;

    .score {
      color: $very-good-score-txt;
    }

    .bar {
      background: $very-good-inner-bg;
    }
  }

  .score {
    @include flex("block", "row", "center", "center");

    font-weight: $font-weight-bold;
  }

  .score {
    height: var(--scoreHeight);
  }

  .bar {
    height: var(--barHeight);
  }
}
</style>
