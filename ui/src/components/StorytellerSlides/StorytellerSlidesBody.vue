<template>
  <section class="storyteller-slides-body">
    <section v-if="slides.length > 0" class="slide-wrapper" ref="slideWrapper" id="slide-wrapper" :style="{height:slideHeightPx, width: slideWidthPx}">
      <component class="slide"
        :is="slideComponent"
        :editable="isEditor"
        :slide-data="selectedSlide.slideData"
        :slide-width="slideWidth"
      />
    </section>
  </section>
</template>

<script>
import { debounce } from 'lodash-es';
import { mapGetters, mapState } from 'vuex';

import getSlideComponent from '@/helpers/storyteller-utils';
import ResizeObserver from 'resize-observer-polyfill';

export default {
  name: 'storyteller-slides-body',

  data() {
    return {
      slideWidth: 0,
    };
  },

  computed: {
    ...mapGetters('datasets', ['isEditable']),

    ...mapState('datasets', {
      datasetId: 'active',
    }),

    ...mapState('storyteller', ['selectedSlide', 'slides']),

    isEditor() {
      return this.isEditable(this.datasetId);
    },

    slideComponent() {
      return getSlideComponent(this.selectedSlide.slideType);
    },

    slideHeightPx() {
      return `${this.slideWidth * 9 / 16}px`;
    },

    slideWidthPx() {
      return `${this.slideWidth}px`;
    },
  },

  mounted() {
    this.resizeObserver = new ResizeObserver(this.resizeHandler);
    this.resizeObserver.observe(this.$el);
  },

  beforeDestroy() {
    this.resizeObserver.disconnect();
  },

  methods: {
    resizeHandler: debounce(function resizeHandler(entries) {
      entries.forEach(entry => {
        if (entry.contentRect) {
          this.setSize(entry.contentRect);
        }
      });
    }, 10),

    setSize({ height, width }) {
      const innerWidth = width - 38.4; // 38.4 is 1.2rem padding
      const innerHeight = height - 38.4; // 38.4 is 1.2rem padding
      this.slideWidth = Math.min(innerWidth, 1200, innerHeight * 16 / 9);
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slides-body {
  @include flex("block", "column", "start", "center");

  font-family: Inter, serif; // TODO: make this become common for whole FE
  height: 100%;
  width: 100%;

  .slide-wrapper {
    @include flex("block", "column", "start", "start");

    position: relative;
    top: 1.2rem;

    .slide {
      border-radius: 4px;
      border: 1px solid rgba(157, 83, 243, 0.2);
    }
  }
}
</style>
