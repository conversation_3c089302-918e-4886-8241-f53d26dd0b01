<template>
  <section class="storyteller-slide-implication-metric" @mouseenter="hovered = true" @mouseleave="hovered = false">
    <section class="body-content">
      <section class="text-header">
        <span class="text-title">Implication</span>
        <storyteller-slide-implication-settings v-if="editable && hovered" class="settings" />
      </section>
      <storyteller-slide-text
        :additional-buttons="additionalButtons"
        :editable="editable"
        :hovered="hovered"
        :font-size="fontSizeText"
        :text-align="textAlign"
        :model-value="text"
        @selectAlign="selectAlign"
        @selectVolumeMinus="selectVolumeMinus"
        @selectVolumePlus="selectVolumePlus"
        @stopEditing="stopEditing"
      />
    </section>
    <section class="chart" :style="chartStyle()">
      <img class="arrow-chart" :src="arrowChart" alt="arrow-chart" />
      <img class="grey-chart" :src="greyChart" alt="grey-chart" />
      <section class="green-chart">
        <section class="top-chart-and-text">
          <span class="chart-text">{{metricNumber}}</span>
          <img class="green-top-chart" :src="greenChart" alt="green-top-chart" />
        </section>
        <img class="green-bottom-chart" :src="greenChart" alt="green-bottom-chart" />
      </section>
    </section>
  </section>
</template>

<script>
import arrowChart from '@/assets/storyteller/storyteller-implication-arrow.png';
import greenChart from '@/assets/storyteller/storyteller-implication-green-chart.png';
import greyChart from '@/assets/storyteller/storyteller-implication-grey-chart.png';
import StorytellerSlideFooter from '@/components/StorytellerSlides/StorytellerSlideFooter';
import StorytellerSlideImplicationSettings from '@/components/StorytellerSlides/StorytellerSlideImplicationSettings';
import StorytellerSlideText from '@/components/StorytellerSlideText/StorytellerSlideText';
import UpdateSlide from '@/components/Mixins/UpdateSlide';
import StorytellerRegenerateInsight from '@/components/StorytellerSlideText/StorytellerRegenerateInsight';

export default {
  name: 'storyteller-slide-implication-metric',

  components: {
    StorytellerSlideFooter,
    StorytellerSlideImplicationSettings,
    StorytellerSlideText,
  },

  mixins: [UpdateSlide],

  data() {
    return {
      arrowChart,
      greenChart,
      greyChart,
    };
  },

  computed: {
    additionalButtons() {
      return [StorytellerRegenerateInsight];
    },

    metricNumber() {
      return `+${Math.round(this.optimiseChange * 100)}%`;
    },

    optimiseChange() {
      return this.slideData.implicationSlideData.optimiseChange;
    },
  },

  methods: {
    chartStyle() {
      const arrowPosition = this.optimiseChange * 2.2222 + 5.5778;
      const arrowRotate = this.optimiseChange * -43.3333 - 1.5667;
      const maxGreenChartTopPosition = 5;
      return {
        '--arrowPosition': `${arrowPosition}em`,
        '--arrowRotate': `${arrowRotate}deg`,
        '--greenChartTopPosition': `${this.optimiseChange * maxGreenChartTopPosition}em`,
        '--textChartPosition': `${this.optimiseChange * maxGreenChartTopPosition + 3.8}em`,
      };
    },
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-implication-metric {
  @include flex("block", "column", "start", "start");

  display: grid;
  grid-template-columns: 50% 50%;
  height: 100%;
  width: 100%;

  .body-content {
    @include flex("block", "column", "start", "start");

    margin-top: 1.6em;

    .text-header {
      @include flex("block", "row", "start", "center");

      font-size: 0.7em;

      .text-title {
        font-weight: $font-weight-bold;
        margin-bottom: 1em;
      }

      .settings {
        margin-left: 1rem;
        position: relative;
        top: -0.5em;
      }
    }

    .text-content {
      @include flex("block", "column", "start", "start");
    }
  }

  .chart {
    @include flex("block", "row", "center", "end");

    bottom: -1px;
    height: 100%;
    position: relative;
    width: 100%;

    .arrow-chart {
      bottom: var(--arrowPosition);
      left: 4em;
      position: absolute;
      transform: rotate(var(--arrowRotate));
      width: 6em;
    }

    .grey-chart {
      margin-right: 0.5em;
      width: 6.3em;
    }

    .green-chart {
      @include flex("block", "row", "end", "end");

      margin-left: 0.5em;
      position: relative;
      width: fit-content;

      .top-chart-and-text {
        bottom: var(--greenChartTopPosition);
        position: absolute;
        width: fit-content;
        z-index: 1;

        .chart-text {
          bottom: 0.2em;
          color: #62CC50;
          font-size: 1.7em;
          font-weight: $font-weight-bold;
          left: 0.6em;
          position: relative;
        }

        .green-top-chart {
          width: 6.25em;
        }
      }

      .green-bottom-chart {
        z-index: 2;
        width: 6.25em;
      }
    }
  }
}
</style>
