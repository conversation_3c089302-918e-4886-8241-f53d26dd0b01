<template>
  <section class="storyteller-slides">
    <storyteller-slides-header />
    <storyteller-slides-body />
  </section>
</template>

<script>
import StorytellerSlidesBody from '@/components/StorytellerSlides/StorytellerSlidesBody';
import StorytellerSlidesHeader from '@/components/StorytellerSlidesHeader/StorytellerSlidesHeader';

export default {
  name: 'storyteller-slides',

  components: {
    StorytellerSlidesBody,
    StorytellerSlidesHeader,
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slides {
  @include flex("block", "column", "start", "start");

  background-color: #F4EBFF;
}
</style>
