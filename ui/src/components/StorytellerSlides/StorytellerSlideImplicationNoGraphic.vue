<template>
  <section class="storyteller-slide-implication-no-graphic"  @mouseenter="hovered= true" @mouseleave="hovered= false">
    <section class="body-content">
      <section class="content" :class="{editable}" >
        <section class="text-header">
          <span class="text-title">Implication</span>
          <storyteller-slide-implication-settings v-if="editable && hovered" class="settings" />
        </section>
        <storyteller-slide-text
          :additional-buttons="additionalButtons"
          :editable="editable"
          :hovered="hovered"
          :font-size="fontSizeText"
          :text-align="textAlign"
          :model-value="text"
          @selectAlign="selectAlign"
          @selectVolumeMinus="selectVolumeMinus"
          @selectVolumePlus="selectVolumePlus"
          @stopEditing="stopEditing"
        />
      </section>
    </section>
  </section>
</template>

<script>
import StorytellerRegenerateInsight from '@/components/StorytellerSlideText/StorytellerRegenerateInsight';
import StorytellerSlideFooter from '@/components/StorytellerSlides/StorytellerSlideFooter';
import StorytellerSlideImplicationSettings from '@/components/StorytellerSlides/StorytellerSlideImplicationSettings';
import StorytellerSlideText from '@/components/StorytellerSlideText/StorytellerSlideText';
import UpdateSlide from '@/components/Mixins/UpdateSlide';

export default {
  name: 'storyteller-slide-implication-no-graphic',

  components: {
    StorytellerSlideFooter,
    StorytellerSlideImplicationSettings,
    StorytellerSlideText,
  },

  mixins: [UpdateSlide],

  computed: {
    additionalButtons() {
      return [StorytellerRegenerateInsight];
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-implication-no-graphic {
  @include flex("block", "column", "center", "center");

  height: 100%;
  width: 100%;

  .body-content {
    @include flex("block", "column", "start", "start");

    margin: auto;
    width: 80%;

    .content {
      @include flex("block", "column", "start", "start");

      &.editable {
        padding-left: 35px;
      }

      .text-header {
        @include flex("block", "row", "start", "center");

        font-size: 0.7em;

        .text-title {
          font-weight: $font-weight-bold;
          margin-bottom: 0.6em;
        }

        .settings {
          margin-left: 1rem;
          position: relative;
          top: -0.3em;
        }
      }
    }
  }
}
</style>
