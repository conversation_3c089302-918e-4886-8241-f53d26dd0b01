<template>
  <section class="storyteller-slide-insight" :style="{fontSize}">
    <section class="margin-top" />

    <section class="body" @mouseenter="hovered = true" @mouseleave="hovered = false">
      <span class="header">{{headerText}}</span>

      <section class="body-content">
        <section class="content">
          <storyteller-slide-text
            :additional-buttons="additionalButtons"
            :editable="editable"
            :hovered="hovered"
            :font-size="fontSizeText"
            :text-align="textAlign"
            :model-value="text"
            @selectAlign="selectAlign"
            @selectVolumeMinus="selectVolumeMinus"
            @selectVolumePlus="selectVolumePlus"
            @stopEditing="stopEditing"
          />
        </section>
      </section>
    </section>

    <storyteller-slide-footer :slide-width="slideWidth" />
  </section>
</template>

<script>
import StorytellerRegenerateInsight from '@/components/StorytellerSlideText/StorytellerRegenerateInsight';
import StorytellerSlideFooter from '@/components/StorytellerSlides/StorytellerSlideFooter';
import StorytellerSlideText from '@/components/StorytellerSlideText/StorytellerSlideText';
import UpdateSlide from '@/components/Mixins/UpdateSlide';

export default {
  name: 'storyteller-slide-insight',

  components: {
    StorytellerRegenerateInsight,
    StorytellerSlideFooter,
    StorytellerSlideText,
  },

  mixins: [UpdateSlide],

  computed: {
    additionalButtons() {
      return [StorytellerRegenerateInsight];
    },
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-insight {
  background-color: #FFF;
  color: $nps-blue;
  display: grid;
  font-family: Inter, serif;
  font-weight: $font-weight-semi-light;
  grid-template-rows: 13% 74% 13%;
  height: 100%;
  padding: 0 2em;
  width: 100%;

  strong {
    font-weight: 600;
  }

  .body {
    @include flex("block", "column", "center", "center");

    .header {
      font-size: 0.5em;
      font-weight: 800;
      left: 0;
      letter-spacing: 0.04em;
      opacity: 0.5;
      position: absolute;
      text-transform: uppercase;
      top: 0;
    }

    .body-content {
      @include flex("block", "column", "center", "center");

      width: 88%;

      .content {
        @include flex("block", "column", "start", "center");
      }
    }

    height: 100%;
    position: relative;
    width: 100%;
  }
}
</style>
