<template>
  <section class="storyteller-slide-implication-settings-dropdown" @click.stop>
    <section class="title">
      <i class="fa-light fa-arrow-up-arrow-down icon-arrow" />
      <span>Switch Graphic</span>
    </section>
    <section v-if="availableGraphics.includes('metric_improvement')" class="item" @click.stop="onClickGraphic('metric_improvement')">
      <base-radio-with-tick-mark :value="selectedGraphic === 'metric_improvement'" />
      <i class="fa-regular fa-chart-bar icon" />
      <span>Metric Improvement</span>
    </section>
    <section v-if="availableGraphics.includes('swot_improvement')" class="item" @click.stop="onClickGraphic('swot_improvement')">
      <base-radio-with-tick-mark :value="selectedGraphic === 'swot_improvement'" />
      <i class="fa-regular fa-grid-2 icon" />
      <span>SWOT Improvement</span>
    </section>
    <section v-if="availableGraphics.includes('no_graphic')" class="item" @click.stop="onClickGraphic('no_graphic')">
      <base-radio-with-tick-mark :value="selectedGraphic === 'no_graphic'" />
      <i class="fa-regular fa-ban icon" />
      <span>No Graphic</span>
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import BaseRadioWithTickMark from '@/components/Base/BaseRadioWithTickMark';
import StorytellerSlideRequest from '@/services/request/StorytellerSlideRequest';

export default {
  name: 'storyteller-slide-implication-settings-dropdown',

  components: {
    BaseRadioWithTickMark,
  },

  computed: {
    ...mapState('storyteller', ['selectedSlide']),

    availableGraphics() {
      return this.selectedSlide.slideData.implicationSlideData.availableGraphics;
    },

    selectedGraphic() {
      return this.selectedSlide.slideData.implicationSlideData.selectedGraphic;
    },
  },

  methods: {
    onClickGraphic(graphic) {
      this.selectedSlide.slideData.implicationSlideData.selectedGraphic = graphic;
      StorytellerSlideRequest.updateSlideImplication();
      this.$emit('close');
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-implication-settings-dropdown {
  @include flex("block", "column", "start", "start");
  @include panel;

  color: $nps-blue;
  cursor: auto;
  font-size: $font-size-sm;
  left: 0;
  position: absolute;
  top: 32px;
  white-space: nowrap;
  width: 220px;
  z-index: 99;

  .title {
    font-weight: $font-weight-bold;
    padding: 0.5rem;
  }

  .item {
    @include flex("block", "row", "start", "center");

    border-radius: 3px;
    cursor: pointer;
    padding: 0.5rem;
    width: 100%;

    &:last-child {
      border-bottom: none;
    }

    &:hover, &:focus {
      background-color: #E6EAFF;
    }

    .icon {
      margin-left: 0.5rem;
      margin-right: 0.5rem;
    }
  }
}
</style>
