<template>
  <section class="storyteller-slide-implication-swot" @mouseenter="hovered = true" @mouseleave="hovered = false">
    <section class="body-content">
      <section class="text-header">
        <span class="text-title">Implication</span>
        <storyteller-slide-implication-settings v-if="editable && hovered" class="settings" />
      </section>
      <storyteller-slide-text
        :additional-buttons="additionalButtons"
        :editable="editable"
        :hovered="hovered"
        :font-size="fontSizeText"
        :text-align="textAlign"
        :model-value="text"
        @selectAlign="selectAlign"
        @selectVolumeMinus="selectVolumeMinus"
        @selectVolumePlus="selectVolumePlus"
        @stopEditing="stopEditing"
      />
    </section>
    <storyteller-slide-implication-swot-chart :slide-data="slideData" />
  </section>
</template>

<script>
import StorytellerRegenerateInsight from '@/components/StorytellerSlideText/StorytellerRegenerateInsight';
import StorytellerSlideImplicationSettings from '@/components/StorytellerSlides/StorytellerSlideImplicationSettings';
import StorytellerSlideImplicationSwotChart from '@/components/StorytellerSlides/StorytellerSlideImplicationSwotChart';
import StorytellerSlideText from '@/components/StorytellerSlideText/StorytellerSlideText';
import UpdateSlide from '@/components/Mixins/UpdateSlide';

export default {
  name: 'storyteller-slide-implication-swot',

  components: {
    StorytellerSlideImplicationSettings,
    StorytellerSlideImplicationSwotChart,
    StorytellerSlideText,
  },

  mixins: [UpdateSlide],

  computed: {
    additionalButtons() {
      return [StorytellerRegenerateInsight];
    },

    potentialSwot() {
      return this.slideData.implicationSlideData.potentialSwot;
    },

    swot() {
      return this.slideData.implicationSlideData.swot;
    },
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-implication-swot {
  @include flex("block", "column", "start", "start");

  display: grid;
  grid-template-columns: 50% 50%;
  height: 100%;
  width: 100%;

  .body-content {
    @include flex("block", "column", "start", "start");

    margin-top: 1.6em;

    .text-header {
      @include flex("block", "row", "start", "center");

      font-size: 0.7em;

      .text-title {
        font-weight: $font-weight-bold;
        margin-bottom: 1em;
      }

      .settings {
        margin-left: 1rem;
        position: relative;
        top: -0.5em;
      }
    }

    .text-content {
      @include flex("block", "column", "start", "start");
    }
  }
}
</style>
