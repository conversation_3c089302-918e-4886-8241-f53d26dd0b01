<template>
  <section class="storyteller-slide-blank" :style="{fontSize}">
    <section class="margin-top" />
    <section class="body">
      <span class="header" />
      <section class="storyteller-slide-text" />
    </section>
    <storyteller-slide-footer :slide-width="slideWidth" />
  </section>
</template>

<script>
import StorytellerSlideFooter from '@/components/StorytellerSlides/StorytellerSlideFooter';

export default {
  name: 'storyteller-slide-blank',

  components: {
    StorytellerSlideFooter,
  },

  props: {
    slideWidth: {
      type: Number,
      required: true,
    },
  },

  computed: {
    fontSize() {
      return `${50 * this.ratio}px`;
    },

    ratio() {
      return this.slideWidth / 1920;
    },
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-blank {
  background-color: #FFF;
  color: $nps-blue;
  display: grid;
  font-family: Inter, serif;
  font-weight: $font-weight-semi-light;
  grid-template-rows: 13% 74% 13%;
  height: 100%;
  padding: 0 2em;
  width: 100%;

  strong {
    font-weight: 600;
  }

  .body {
    @include flex("block", "column", "start", "start");

    height: 100%;
    position: relative;
    width: 100%;

    .header {
      @include flex("block", "row", "start", "end");

      font-size: 0.5em;
      font-weight: 800;
      letter-spacing: 0.04em;
      opacity: 0.5;
      text-transform: uppercase;
    }
  }
}
</style>
