<template>
  <section class="value-at-risk-footer">
    <section class="btn btn-clear" @click="onClickClear">
      <i class="fa-regular fa-xmark icon" />
      <section class="text">Clear</section>
    </section>
    <loading-blocks-overlay v-if="saving" size="small" />
    <section v-else-if="isEditor" class="btn btn-save" @click="onClickSave" :class="{disabled: hasError}">
      <i class="fa-solid fa-floppy-disk icon" />
      <section class="text">Save & Return</section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import DatasetsRequestV0 from '@/services/request/DatasetsRequestV0';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import Route from '@/enum/route';
import ValueAtRiskRequest from '@/services/request/ValueAtRiskRequest';
import ValueAtRiskUpdateToast from '@/components/ValueAtRiskFooter/ValueAtRiskUpdateToast';

export default {
  name: 'value-at-risk-footer',

  components: {
    LoadingBlocksOverlay,
  },

  data() {
    return {
      saving: false,
    };
  },

  computed: {
    ...mapGetters('datasets', ['isEditable']),

    ...mapState('datasets', {
      datasetId: 'active',
    }),

    ...mapState('storyteller', ['activeReport']),

    ...mapState('valueAtRisk', [
      'datasetId',
      'previousRoute',
      'varInfoError',
      'varInfo',
    ]),

    ...mapGetters('valueAtRisk', ['validateVarInfo']),

    hasError() {
      return this.varInfoError?.avgEmployeeSalary ||
          this.varInfoError?.customerSpendAvgAnnual ||
          this.varInfoError?.numberOfCustomers ||
          this.varInfoError?.totalYear;
    },

    isEditor() {
      return this.isEditable(this.datasetId);
    },
  },

  methods: {
    ...mapActions('toast', ['add']),

    ...mapActions('valueAtRisk', ['setClear', 'setVarInfoError']),

    onClickClear() {
      this.setClear({ clear: true });
    },

    async onClickSave() {
      const error = this.validateVarInfo;
      this.setVarInfoError({ error });

      if (this.hasError) {
        return;
      }
      this.saving = true;
      await ValueAtRiskRequest.setVarInfo();
      await DatasetsRequestV0.getDatasets();
      await DatasetsRequestV0.reloadSelected();
      this.saving = false;
      this.add({ toast: { component: ValueAtRiskUpdateToast, id: 'value-at-risk-update-toast' } });
      if (this.previousRoute === Route.STORYTELLER) {
        await this.$router.push({
          name: this.previousRoute,
          query: {
            ids: this.datasetId,
            reportId: this.activeReport.id,
          },
        });
      } else {
        await this.$router.push({ name: this.previousRoute });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.value-at-risk-footer {
  @include flex("block", "row", "space-between", "center");

  border-top: 1px solid $border-color;
  padding: 1.5rem 4rem;

  .btn {
    @include flex("block", "row", "start", "center");

    border-radius: 3px;
    border: $border-standard;
    cursor: pointer;
    font-size: $font-size-xs;
    font-weight: $font-weight-extra-bold;
    height: 30px;
    letter-spacing: 0.4px;
    padding: 0.4rem 1rem;
    text-transform: uppercase;

    .icon {
      margin-right: 0.4rem;
    }

    &.btn-clear {
      border-color: rgba(255, 84, 84, 0.7);
      color: rgba(255, 84, 84, 1);

      &:hover, &:focus {
        border-color: rgba(255, 84, 84, 1);
      }
    }

    &.btn-save {
      background-color: #6C60C9;
      border: none;
      color: white;

      &:hover, &:focus {
        background-color: rgba(19, 28, 41);
      }

      &.disabled {
        opacity: 0.5;
        pointer-events: none;
      }
    }
  }
}
</style>
