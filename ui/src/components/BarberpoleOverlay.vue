<template functional>
  <section class="barberpole-overlay" :class="props.colour"></section>
</template>

<script>
export default {
  name: 'barberpole-overlay',

  props: {
    colour: {
      type: String,
      default: 'loading',
      validator: value => [
        'blue',
        'danger',
        'gray',
        'green',
        'info',
        'light-purple',
        'loading',
        'red',
      ].includes(value),
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

$stripe-width: 10px;

// No sqrt function in sass - need to calculate manually with pythagorean
// stripe-width^2 + stripe-width^2 = horizontal-stripe-width^2
// 200px = horizontal-stripe-width^2
// 14.1421356237px = horizontal-stripe-width
$horizontal-stripe-width: 14.1421356237px;

@keyframes barberpole {
  from {
    background-position: 0 0;
  }
  to {
    background-position: -($horizontal-stripe-width * 2) 0;
  }
}

.barberpole-overlay {
  animation: barberpole 1s linear infinite;
  background-size: ($horizontal-stripe-width * 2) ($horizontal-stripe-width * 2);
  height: 200%;
  left: -50%;
  pointer-events: auto;
  position: absolute;
  top: -50%;
  transition: all $interaction-transition-time;
  width: 200%;
  z-index: 99;

  &.loading, &.gray {
    background: repeating-linear-gradient(
      135deg,
      transparent,
      transparent $stripe-width,
      clr('purple', 'light') $stripe-width,
      clr('purple', 'light') $stripe-width * 2
    );

    opacity: 0.3;
  }

  &.danger, &.red {
    background: repeating-linear-gradient(
      135deg,
      transparent,
      transparent $stripe-width,
      clr('red') $stripe-width,
      clr('red') $stripe-width * 2
    );

    opacity: 0.1;
  }

  &.info, &.blue {
    background: repeating-linear-gradient(
      135deg,
      transparent,
      transparent $stripe-width,
      clr('blue') $stripe-width,
      clr('blue') $stripe-width * 2
    );

    opacity: 0.1;
  }

  &.green {
    background: repeating-linear-gradient(
      135deg,
      transparent,
      transparent $stripe-width,
      clr('green') $stripe-width,
      clr('green') $stripe-width * 2
    );

    opacity: 0.1;
  }

  &.light-purple {
    background: repeating-linear-gradient(
            135deg,
            transparent,
            transparent $stripe-width,
            #EFF1FF $stripe-width,
            #EFF1FF $stripe-width * 2
    );

    opacity: 0.5;

  }
}
</style>
