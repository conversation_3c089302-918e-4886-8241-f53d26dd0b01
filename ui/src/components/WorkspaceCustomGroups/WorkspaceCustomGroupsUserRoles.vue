<template>
  <section class="workspace-custom-groups-user-roles">
    <span v-for="(item, i) in dataList"
          :key="i"
          class="group-role-item"
          :class="{ active: invitingGroupRole === item, notAvailable: !isAvailable(item) }"
          @click="onSelect(item)"
    >
      {{ item.name }}
    </span>
  </section>
</template>

<script>
import WorkspaceInviteUserRole from '@/enum/workspace-invite-user-role';

export default {
  name: 'workspace-custom-groups-user-roles',

  props: {
    currentWorkspaceRole: {
      type: Object,
      default: null,
    },
    invitingGroupRole: {
      type: Object,
      required: true,
    },
  },

  computed: {
    dataList() {
      return WorkspaceInviteUserRole.enumValues
        .filter(t => {
          return ![WorkspaceInviteUserRole.OWNER, WorkspaceInviteUserRole.ADMIN].includes(t);
        });
    },
  },

  methods: {
    isAvailable(item) {
      if (!this.currentWorkspaceRole) {
        return true;
      }

      const currentWorkspaceRole = (typeof this.currentWorkspaceRole === 'string')
        ? WorkspaceInviteUserRole[this.currentWorkspaceRole]
        : this.currentWorkspaceRole;

      return currentWorkspaceRole.rank() <= item.rank();
    },

    onSelect(item) {
      if (this.isAvailable(item)) {
        this.$emit('onSelect', item);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.workspace-custom-groups-user-roles {
  @include flex("block", "column", "start", "start");
  background: #ffffff;
  border: none;
  border-radius: $border-radius-medium;
  box-shadow: 0 0 3px 3px rgba(0, 0, 0, 0.25);

  .group-role-item {
    cursor: pointer;
    font-size: $font-size-xs;
    padding: 0.5rem 1rem;
    width: 100%;

    &:hover {
      background-color: rgba(clr('black'), 0.05);
    }

    &.active {
      color: clr("purple");
    }

    &.notAvailable {
      cursor: not-allowed;
      opacity: 0.5;
    }
  }
}
</style>
