<template>
  <section class="workspace-custom-groups-add-permissions-modal">
    <section class="header">
      <span class="text">
        Adding permissions for {{ textGroupLabel }}
      </span>
    </section>
    <section class="body">
      <section class="body-item select-user"
               v-click-outside-handler="{
                                           handler: 'onClickOutsideUserSearch',
                                           excludedParentClasses: [
                                               'dropdown-item-name',
                                               'dropdown-item-role',
                                               'dropdown-item-status',
                                               'dropdown-item',
                                               'workspace-custom-groups-dropdown-item',
                                               'workspace-custom-groups-dropdown-list',
                                           ],
                                        }"
      >
        <input v-model="searchUser"
               @click="onClickSearch"
               class="text-search"
               placeholder="Select users" />
        <span class="dropdown" @click="onClickDropdown">
          <i class="fa-solid fa-caret-down icon" :class="{ open: showDropdownList }" />
        </span>
      </section>
      <section class="body-item users-list">
        <span v-if="!dataList.length" class="empty-list">Add members to a custom group</span>
        <workspace-custom-groups-adding-member-item v-else v-for="item in dataList"
                                             :key="item.id"
                                             :memberItem="item"
                                             class="adding-member-item"
        />
      </section>
    </section>
    <section class="footer">
      <base-button class="cancel-btn" colour="light" size="small" type="link" @click="onClickCancel">Cancel</base-button>
      <base-button v-if="!proceeding"
                   class="done-btn"
                   size="small"
                   @click="onClickSave"
      >
        Save
      </base-button>
      <loading-blocks-overlay v-if="proceeding" />
    </section>
    <!-- Available members dropdown-list -->
    <workspace-custom-groups-dropdown-list :class="{ hide: !showDropdownList }"
                                           :searchStr="searchUser"/>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';
import BaseButton from '@/components/Base/BaseButton';
import clickOutsideHandler from '@/directives/click-outside-handler';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import WorkspaceCustomGroupsAddingMemberItem from '@/components/WorkspaceCustomGroups/WorkspaceCustomGroupsAddingMemberItem';
import WorkspaceCustomGroupsDropdownList from '@/components/WorkspaceCustomGroups/WorkspaceCustomGroupsDropdownList';
import WorkspaceCustomGroupUpdateType from '@/enum/workspace-custom-group-update-type';
import WorkspaceInviteUserRole from '@/enum/workspace-invite-user-role';

import { workspaceGroupApi } from '@/services/api';

export default {
  name: 'workspace-custom-groups-add-permissions-modal',

  components: {
    BaseButton,
    LoadingBlocksOverlay,
    WorkspaceCustomGroupsDropdownList,
    WorkspaceCustomGroupsAddingMemberItem,
  },

  data() {
    return {
      proceeding: false,
      searchUser: '',
      showDropdownList: false,
    };
  },

  directives: {
    clickOutsideHandler,
  },

  computed: {
    ...mapGetters('workspaces', ['checkGroupMemberStatus', 'getMembersFromWorkspaceByIds']),

    ...mapState('workspaces', [
      'editingCustomGroup',
      'editingMembersList',
      'selectedWorkspace',
    ]),

    dataList() {
      const rs = [];

      const groupOwner = {
        ...this.editingCustomGroup.owner,
        role: WorkspaceInviteUserRole.OWNER,
      };
      rs.push(groupOwner);

      // existing members - and not in Removing list
      rs.push(...this.groupEditorsList);
      rs.push(...this.groupViewersList);
      // new members from Adding list
      rs.push(...this.groupAddingList);

      return rs;
    },

    groupAddingList() {
      const adding = [];
      if (this.editingMembersList?.length) {
        adding.push(...this.editingMembersList.filter(i => i.type === WorkspaceCustomGroupUpdateType.ADD));
      }

      let rs = adding.map(i => i.id);
      rs = this.getMembersFromWorkspaceByIds(rs);
      // map new group-role for memberObj
      rs = rs.map(i => {
        const addingObj = adding.find(o => o.id === i.id);
        let role = addingObj?.role || WorkspaceInviteUserRole.VIEWER;
        role = (typeof role === 'string') ? WorkspaceInviteUserRole[role] : role;

        return {
          ...i,
          role,
        };
      });

      return rs;
    },

    groupEditorsList() {
      const rs = [];
      if (this.editingCustomGroup.editors?.length) {
        rs.push(...this.editingCustomGroup.editors);
      } else if (this.editingCustomGroup.editorIds?.length) {
        rs.push(...this.getMembersFromWorkspaceByIds(this.editingCustomGroup.editorIds));
      }
      return rs.filter(i => {
        const status = this.checkGroupMemberStatus(i.id);
        return !status.removing;
      });
    },

    groupViewersList() {
      const rs = [];
      if (this.editingCustomGroup.viewers?.length) {
        rs.push(...this.editingCustomGroup.viewers);
      } else if (this.editingCustomGroup.viewerIds?.length) {
        rs.push(...this.getMembersFromWorkspaceByIds(this.editingCustomGroup.viewerIds));
      }
      return rs.filter(i => {
        const status = this.checkGroupMemberStatus(i.id);
        return !status.removing;
      });
    },

    textGroupLabel() {
      return this.editingCustomGroup?.label?.trim() || 'Custom Group';
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('workspaces', ['setCustomGroups']),

    async loadAllCustomGroups() {
      if (this.selectedWorkspace) {
        const groups = await workspaceGroupApi.getAllCustomGroups(this.selectedWorkspace.id);
        this.setCustomGroups({ values: groups });
      }
    },

    onClickCancel() {
      this.closeModal();
    },

    onClickDropdown() {
      this.showDropdownList = !this.showDropdownList;
    },

    onClickOutsideUserSearch() {
      this.showDropdownList = false;
    },

    async onClickSave() {
      if (this.proceeding || !this.selectedWorkspace) {
        return;
      }
      this.proceeding = true;

      await workspaceGroupApi.updateMembersOfGroup(
        this.selectedWorkspace.id,
        this.editingCustomGroup.id,
        this.editingMembersList,
      );
      await this.loadAllCustomGroups();

      this.proceeding = false;
      this.closeModal();
    },

    onClickSearch() {
      this.showDropdownList = true;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.workspace-custom-groups-add-permissions-modal {
  @include panel;
  position: relative;
  width: 600px;

  .header {
    @include flex('block', 'row', 'start', 'center');
    border-bottom: $border-light solid $border-color;
    color: #2D1757;
    font-size: $font-size-sm;
    font-weight: $font-weight-bold;
    padding: 1.5rem 1.5rem;

    .text {
      @include truncate;
    }
  }

  .body {
    @include flex('block', 'column', 'start', 'start');
    @include scrollbar-thin;
    max-height: 450px;
    overflow-y: auto;
    padding: 1rem 1.5rem;

    .body-item {
      margin-bottom: 1rem;
      width: 100%;

      &.select-user {
        @include flex('block', 'row', 'start', 'center');
        border: 1px solid rgba(115, 98, 183, 1);
        border-radius: 2px;

        .text-search {
          background: none;
          border: none;
          font-size: $font-size-xs;
          padding-left: 0.5rem;
          width: 100%;

          &:active, &:focus {
            outline: none;
          }
        }

        .dropdown {
          @include flex('block', 'row', 'center', 'center');
          border-left: 1px solid rgba(115, 98, 183, 1);
          cursor: pointer;
          height: 32px;
          margin-left: 1rem;
          padding: 0.4rem;
          width: 32px;

          .icon {
            transition: transform $interaction-transition-time;

            &.open {
              transform: rotate(180deg);
            }
          }
        }
      }

      &.users-list {
        @include scrollbar-thin;
        height: 100%;
        padding-top: 0.5rem;
        width: 100%;

        .empty-list {
          align-items: center;
          display: grid;
          height: inherit;
          justify-content: center;
          opacity: 0.7;
        }

        .adding-member-item {
          border-bottom: 1px solid rgba(242, 241, 251, 1);
          padding: 1rem 0;

          &:last-child {
            border-bottom: none;
          }
        }
      }
    }
  }

  .footer {
    @include flex('block', 'row', 'between', 'center');
    @include rigid;
    border-top: $border-light solid $border-color;
    padding: 1.5rem 1.5rem;

    .base-button {
      font-size: 0.65em;
      font-weight: 600;
      text-transform: uppercase;

      &.cancel-btn {
        padding: 0.5rem 1rem 0.5rem 0;
      }

      &.done-btn {
        background: #2D1757;
        padding: 0.5rem 1.5rem;
      }
    }

    .loading-blocks-overlay {
      height: 28px;
    }
  }

  .workspace-custom-groups-dropdown-list {
    position: absolute;
    left: 25px;
    top: 120px;
    z-index: 2;

    &.hide {
      display: none;
    }
  }
}
</style>
