<template>
  <section class="workspace-custom-groups-delete">
    <section class="header">
      Delete Custom Group
    </section>
    <section class="body">
      Custom group <span class="strong">{{ textGroupName }}</span> will be deleted, this action cannot be undone. Are you sure?
    </section>
    <section class="footer">
      <base-button class="cancel-btn" colour="light" size="small" type="link" @click="onClickCancel">Cancel</base-button>
      <base-button v-if="!proceeding"
                   class="done-btn"
                   size="small"
                   @click="onClickDelete"
      >
        Delete
      </base-button>
      <loading-blocks-overlay v-if="proceeding" />
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import BaseButton from '@/components/Base/BaseButton';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';

import { workspaceGroupApi } from '@/services/api';

export default {
  name: 'workspace-custom-groups-delete',

  components: {
    BaseButton,
    LoadingBlocksOverlay,
  },

  beforeDestroy() {
    this.setEditingCustomGroup({ value: null });
    this.setEditingMembersList({ values: [] });
  },

  data() {
    return {
      proceeding: false,
    };
  },

  computed: {
    ...mapState('workspaces', [
      'customGroups',
      'editingCustomGroup',
      'selectedWorkspace',
    ]),

    textGroupName() {
      return this.editingCustomGroup?.label?.trim() || 'CustomGroup';
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('workspaces', [
      'setCustomGroups',
      'setEditingCustomGroup',
      'setEditingMembersList',
    ]),

    async loadAllCustomGroups() {
      if (this.selectedWorkspace) {
        const groups = await workspaceGroupApi.getAllCustomGroups(this.selectedWorkspace.id);
        this.setCustomGroups({ values: groups });
      }
    },

    onClickCancel() {
      this.closeModal();
    },

    async onClickDelete() {
      if (this.proceeding || !this.selectedWorkspace) {
        return;
      }
      this.proceeding = true;

      await workspaceGroupApi.deleteGroup(this.selectedWorkspace.id, this.editingCustomGroup.id);
      await this.loadAllCustomGroups();

      this.proceeding = false;
      this.closeModal();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.workspace-custom-groups-delete {
  @include panel;
  position: relative;
  width: 600px;

  .header {
    @include flex('block', 'row', 'start', 'center');
    border-bottom: $border-light solid $border-color;
    color: #2D1757;
    font-size: $font-size-sm;
    font-weight: $font-weight-bold;
    padding: 1.5rem 1.5rem;
  }

  .body {
    color: rgba(36, 18, 77, 1);
    font-size: $font-size-xs;
    height: fit-content;
    padding: 2.5rem 1.5rem;

    .strong {
      font-weight: $font-weight-bold;
    }
  }

  .footer {
    @include flex('block', 'row', 'between', 'center');
    @include rigid;
    border-top: $border-light solid $border-color;
    padding: 1.5rem 1.5rem;

    .base-button {
      font-size: 0.65em;
      font-weight: 600;
      text-transform: uppercase;

      &.cancel-btn {
        padding: 0.5rem 1rem 0.5rem 0;
      }

      &.done-btn {
        background: rgba(203, 21, 9, 1);
        padding: 0.5rem 1.5rem;
      }
    }

    .loading-blocks-overlay {
      height: 28px;
    }
  }
}
</style>
