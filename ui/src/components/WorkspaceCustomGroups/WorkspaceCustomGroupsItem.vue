<template>
  <section class="workspace-custom-groups-item">
    <section class="group-label">
      <section class="icon">
        <i class="fa-regular fa-users" />
      </section>
      {{ textGroupLabel }}
    </section>
    <section class="group-description">{{ textGroupDescription }}</section>
    <section class="group-delete" @click="onClickDeleteBtn">
      <i class="fa-solid fa-trash-can" />
    </section>
    <section class="group-members" @click="onClickAddPermissions">
      <section class="left">
        <i class="fa-light fa-users icon-users" />
        {{ textGroupMembers }}
      </section>
      <section class="right">
        <i class="fa-solid fa-caret-right icon-caret" />
      </section>
    </section>
    <section class="edit-btn" @click="onClickEditBtn">
      <i class="fa-regular fa-pen icon" />
      Edit Group
    </section>
  </section>
</template>

<script>
import { mapActions } from 'vuex';
import WorkspaceCustomGroupsAddPermissionsModal from '@/components/WorkspaceCustomGroups/WorkspaceCustomGroupsAddPermissionsModal';
import WorkspaceCustomGroupsDelete from '@/components/WorkspaceCustomGroups/WorkspaceCustomGroupsDelete';
import WorkspaceCustomGroupsEdit from '@/components/WorkspaceCustomGroups/WorkspaceCustomGroupsEdit';

export default {
  name: 'workspace-custom-groups-item',

  props: {
    groupItem: {
      type: Object,
      required: true,
    },
  },

  computed: {
    textGroupDescription() {
      return this.groupItem.description?.trim() || 'Description';
    },

    textGroupLabel() {
      return this.groupItem.label?.trim() || 'Group Label';
    },

    textGroupMembers() {
      // start counting at 1 - Group Owner
      let count = 1;
      if (this.groupItem.editorIds?.length) {
        count += this.groupItem.editorIds?.length;
      }
      if (this.groupItem.viewerIds?.length) {
        count += this.groupItem.viewerIds?.length;
      }
      return `${count} ${count !== 1 ? 'Users' : 'User'}`;
    },
  },

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    ...mapActions('workspaces', ['setEditingCustomGroup', 'setEditingMembersList']),

    onClickAddPermissions() {
      this.setEditingCustomGroup({ value: this.groupItem });
      this.setEditingMembersList({ values: [] });
      this.setModalComponent({ component: WorkspaceCustomGroupsAddPermissionsModal });
    },

    onClickDeleteBtn() {
      this.setEditingCustomGroup({ value: this.groupItem });
      this.setEditingMembersList({ values: [] });
      this.setModalComponent({ component: WorkspaceCustomGroupsDelete });
    },

    onClickEditBtn() {
      this.setEditingCustomGroup({ value: this.groupItem });
      this.setEditingMembersList({ values: [] });
      this.setModalComponent({ component: WorkspaceCustomGroupsEdit });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.workspace-custom-groups-item {
  @include stretch;
  align-items: center;
  display: grid;
  grid-gap: 1rem;
  grid-template-columns: 1fr 1fr 1rem 96px 100px;

  .group-label {
    @include flex("block", "row", "start", "center");
    color: rgba(0, 101, 255, 1);
    font-size: $font-size-xs;
    font-weight: $font-weight-bold;

    .icon {
      border: 1px solid rgba(0, 101, 255, 0.2);
      border-radius: 100px;
      font-size: $font-size-sm;
      margin-right: 0.3rem;
      padding: 0.3rem;
    }
  }

  .group-description {
    font-size: $font-size-xs;
  }

  .group-delete {
    @include flex("block", "row", "center", "center");
    border: 1px solid rgba(203, 21, 9, 0.8);
    border-radius: 50%;
    color: rgba(203, 21, 9, 0.8);
    cursor: pointer;
    font-size: $font-size-xxs;
    height: 22px;
    width: 22px;

    &:hover {
      background: rgba(203, 21, 9, 1);
      color: #FFFFFF;
    }
  }

  .group-members {
    @include flex("block", "row", "between", "center");
    border: 1px solid rgba(0, 101, 255, 0.2);
    border-radius: 2px;
    color: rgba(0, 101, 255, 1);
    cursor: pointer;
    font-size: 0.65rem;
    font-weight: $font-weight-bold;
    height: 1.5rem;
    padding: 0.3rem 0.5rem;
    width: 6.2rem;

    &:hover {
      border: 1px solid rgba(0, 101, 255, 0.5);
    }

    .icon-users {
      font-size: $font-size-xs;
      margin-right: 0.2rem;
    }
  }

  .edit-btn {
    @include flex("block", "row", "center", "center");
    border: 1px solid rgba(75, 114, 240, 0.15);
    border-radius: 2px;
    color: rgba(45, 23, 87, 1);
    cursor: pointer;
    font-size: 0.65rem;
    font-weight: 600;
    height: 1.5rem;
    padding: 0.3rem 0.2rem;
    text-transform: uppercase;
    width: 6.2rem;

    &:hover {
      border: 1px solid rgba(75, 114, 240, 0.5);
    }

    .icon {
      margin-right: 0.2rem;
    }
  }
}
</style>
