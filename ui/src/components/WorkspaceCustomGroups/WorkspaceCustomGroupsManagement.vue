<template>
  <section class="workspace-custom-groups-management">
    <section class="management-label">
      <i class="fa-solid fa-arrow-left-long icon" @click="onClickBackToDataset" />
      <span class="text">Manage Custom Groups</span>
    </section>
    <section class="management-search">
      <section class="search-box">
        <i class="fa-light fa-magnifying-glass icon-search" />
        <input v-model="groupSearch"
               class="search-text"
               placeholder="Search Custom Groups"
        />
        <i class="fa-light fa-x icon-close" />
      </section>
      <section class="new-box" @click="onClickNewCustomGroup">
        <i class="fa-light fa-plus icon" />
        <span class="text">New Custom Group</span>
      </section>
    </section>
    <section v-if="!customGroupList.length && !isSearching" class="empty-list">
      There are no custom groups in this workspace
    </section>
    <section v-else-if="!customGroupList.length && isSearching" class="empty-list">
      Found none group that matches your search.
    </section>
    <workspace-custom-groups-item v-else
                                  v-for="(item, i) in customGroupList"
                                  :key="i"
                                  :group-item="item"
                                  class="group-item"
    />
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';
import WorkspaceCustomGroupsCreateNew from '@/components/WorkspaceCustomGroups/WorkspaceCustomGroupsCreateNew';
import WorkspaceCustomGroupsItem from '@/components/WorkspaceCustomGroups/WorkspaceCustomGroupsItem';

import { workspaceGroupApi } from '@/services/api';

export default {
  name: 'workspace-custom-groups-management',

  components: {
    WorkspaceCustomGroupsItem,
  },

  data() {
    return {
      groupSearch: '',
    };
  },

  async mounted() {
    if (this.selectedWorkspace) {
      let groups;
      if (!this.isAdmin) {
        groups = await workspaceGroupApi.getAllCustomGroups(this.selectedWorkspace.id, this.user.id);
      } else {
        groups = await workspaceGroupApi.getAllCustomGroups(this.selectedWorkspace.id);
      }
      this.setCustomGroups({ values: groups });
    }
  },

  computed: {
    ...mapState('user', ['user']),

    ...mapGetters('user', ['isAdmin']),

    ...mapState('workspaces', ['customGroups', 'selectedWorkspace']),

    customGroupList() {
      let rs = [];
      rs.push(...this.customGroups);
      if (this.groupSearch?.trim().length) {
        const searchStr = this.groupSearch?.trim().toLowerCase();
        rs = rs.filter(g => {
          if (g.label.toLowerCase().indexOf(searchStr) !== -1) {
            return true;
          }
          return g.description?.trim().length && g.description.toLowerCase().indexOf(searchStr) !== -1;
        });
      }
      return rs.sort((a, b) => a.label.toLowerCase().localeCompare(b.label.toLowerCase()));
    },

    isSearching() {
      return this.groupSearch?.trim().length > 0;
    },
  },

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    ...mapActions('workspaces', ['setCustomGroups', 'setShowWorkspaceCustomGroups']),

    onClickBackToDataset() {
      this.setShowWorkspaceCustomGroups({ value: false });
    },

    onClickNewCustomGroup() {
      this.setModalComponent({ component: WorkspaceCustomGroupsCreateNew });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.workspace-custom-groups-management {
  @include flex("block", "column", "start", "stretch");
  @include stretch;
  height: fit-content;
  margin-bottom: 1rem;

  .management-label {
    @include flex("block", "row", "start", "center");
    margin-bottom: 1rem;

    .icon {
      background: rgba(95, 82, 197, 1);
      border-radius: 100px;
      color: #FFFFFF;
      cursor: pointer;
      font-size: $font-size-xs;
      margin-right: 0.5rem;
      padding: 0.4rem;
    }

    .text {
      color: rgba(45, 23, 87, 1);
      font-size: $font-size-sm;
      font-weight: $font-weight-bold;
    }
  }

  .management-search {
    align-items: center;
    display: grid;
    grid-template-columns: 1fr 170px;
    grid-gap: 0.5rem;
    margin-bottom: 0.5rem;

    .search-box {
      @include flex("block", "row", "start", "center");
      border: 1px solid rgba(95, 82, 197, 1);
      border-radius: 3px;
      color: rgba(50, 31, 96, 1);
      font-size: $font-size-xs;
      padding: 0.5rem 0.5rem;

      .icon-search {
        margin-right: 0.2rem;
      }

      .search-text {
        background: none;
        border: none;
        padding: 0 0.5rem;
        width: 100%;

        &:active, &:focus {
          border: none;
          outline: none;
        }
      }

      .icon-close {
        cursor: pointer;
        opacity: 0.3;

        &:hover {
          opacity: 1;
        }
      }
    }

    .new-box {
      @include flex("block", "row", "center", "center");
      background: #2D1757;
      border-radius: 2px;
      color: #FFFFFF;
      cursor: pointer;
      font-size: $font-size-xxs;
      padding: 0.65rem 0.5rem;

      .icon {
        margin-right: 0.5rem;
      }

      .text {
        font-weight: $font-weight-bold;
        text-transform: uppercase;
      }
    }
  }

  .empty-list {
    align-items: center;
    display: grid;
    font-size: $font-size-lg;
    justify-content: center;
    margin-top: 2rem;
    opacity: 0.7;
    width: inherit;
  }

  .group-item {
    border-bottom: 1px solid rgba(37, 16, 167, 0.06);
    padding: 1rem 0;
  }
}
</style>
