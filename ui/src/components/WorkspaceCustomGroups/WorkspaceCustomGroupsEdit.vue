<template>
  <section class="workspace-custom-groups-edit">
    <section class="header">
      Edit Custom Group
    </section>
    <section class="body">
      <section class="body-item">
        <span class="label">
          Group Name
          <span class="warning" v-if="groupLabelExisted"> (* existed already)</span>
        </span>
        <input v-model="groupLabel"
               class="text-input"
               placeholder="Group Name"
        />
      </section>
      <section class="body-item">
        <span class="label">Group Description</span>
        <input v-model="groupDescription"
               class="text-input"
               placeholder="Description"
        />
      </section>
    </section>
    <section class="footer">
      <base-button class="cancel-btn" colour="light" size="small" type="link" @click="onClickCancel">Cancel</base-button>
      <base-button v-if="!proceeding"
                   class="done-btn"
                   :disabled="invalidNewLabel"
                   size="small"
                   @click="onClickDone"
      >
        Done
      </base-button>
      <loading-blocks-overlay v-if="proceeding" />
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import BaseButton from '@/components/Base/BaseButton';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';

import { workspaceGroupApi } from '@/services/api';

export default {
  name: 'workspace-custom-groups-edit',

  components: {
    BaseButton,
    LoadingBlocksOverlay,
  },

  beforeDestroy() {
    this.setEditingCustomGroup({ value: null });
    this.setEditingMembersList({ values: [] });
  },

  data() {
    return {
      groupDescription: '',
      groupLabel: '',
      proceeding: false,
    };
  },

  mounted() {
    this.groupLabel = this.editingCustomGroup?.label || '';
    this.groupDescription = this.editingCustomGroup?.description || '';
  },

  computed: {
    ...mapState('workspaces', [
      'customGroups',
      'editingCustomGroup',
      'selectedWorkspace',
    ]),

    groupLabelExisted() {
      const label = this.groupLabel?.replace(/\s\s+/g, ' ').substring(0, 250).trim() || '';

      if (!label.length) {
        return false;
      }

      return this.customGroups
        .filter(g => g.id !== this.editingCustomGroup.id)
        .map(g => g.label.trim().toLowerCase())
        .includes(label);
    },

    invalidNewLabel() {
      if (!this.groupLabel?.trim().length) {
        return true;
      }

      return this.groupLabelExisted;
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('workspaces', [
      'setCustomGroups',
      'setEditingCustomGroup',
      'setEditingMembersList',
    ]),

    async loadAllCustomGroups() {
      if (this.selectedWorkspace) {
        const groups = await workspaceGroupApi.getAllCustomGroups(this.selectedWorkspace.id);
        this.setCustomGroups({ values: groups });
      }
    },

    onClickCancel() {
      this.closeModal();
    },

    async onClickDone() {
      if (this.invalidNewLabel || this.proceeding || !this.selectedWorkspace) {
        return;
      }
      this.proceeding = true;

      const label = this.groupLabel.replace(/\s\s+/g, ' ').substring(0, 250).trim();
      const description = this.groupDescription?.replace(/\s\s+/g, ' ').substring(0, 250).trim() || '';
      // just call api when there are changes
      if (label !== this.editingCustomGroup.label
          || description !== this.editingCustomGroup.description
      ) {
        await workspaceGroupApi.updateGroup(this.selectedWorkspace.id, this.editingCustomGroup.id, label, description);
        await this.loadAllCustomGroups();
      }

      this.proceeding = false;
      this.closeModal();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.workspace-custom-groups-edit {
  @include panel;
  position: relative;
  width: 600px;

  .header {
    @include flex('block', 'row', 'start', 'center');
    border-bottom: $border-light solid $border-color;
    color: #2D1757;
    font-size: $font-size-sm;
    font-weight: $font-weight-bold;
    padding: 1.5rem 1.5rem;
  }

  .body {
    @include flex('block', 'column', 'start', 'start');
    @include scrollbar-thin;
    height: fit-content;
    padding: 1rem 1.5rem;

    .body-item {
      margin-bottom: 1rem;
      width: 100%;

      .label {
        color: rgba(36, 18, 77, 1);
        font-size: $font-size-xs;

        .warning {
          color: clr("red");
        }
      }

      .text-input {
        background: none;
        border: 1px solid rgba(115, 98, 183, 1);
        border-radius: 2px;
        font-size: $font-size-xs;
        margin-top: 0.3rem;
        padding: 0.3rem 0.5rem;
        width: 100%;

        &:active, &:focus {
          outline: none;
        }
      }
    }
  }

  .footer {
    @include flex('block', 'row', 'between', 'center');
    @include rigid;
    border-top: $border-light solid $border-color;
    padding: 1.5rem 1.5rem;

    .base-button {
      font-size: 0.65em;
      font-weight: 600;
      text-transform: uppercase;

      &.cancel-btn {
        padding: 0.5rem 1rem 0.5rem 0;
      }

      &.done-btn {
        background: #2D1757;
        padding: 0.5rem 1.5rem;
      }
    }

    .loading-blocks-overlay {
      height: 28px;
    }
  }
}
</style>
