<template>
  <section class="memory-page-browser"
    :class="{
      'show-action-builder-panel-and-actions': showActionBuilderPanelLeft,
      'show-actions-panel': !showActionBuilderPanelLeft,
      'show-memory-action-bar-bottom': hasSelectedMemories,
    }">

    <!-- left section -->
    <saved-actions-builder-panel-left v-if="showActionBuilderPanelLeft"/>
    <memory-page-browser-panel />
    <memory-page-action-bar-bottom class="action-bar-bottom" />
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import MemoryPageActionBarBottom from '@/components/MemoryPageBrowser/MemoryPageActionBarBottom';
import MemoryPageBrowserPanel from '@/components/MemoryPageBrowser/MemoryPageBrowserPanel';
import SavedActionsBuilderPanelLeft from '@/components/MemoryPageBrowser/SavedActionsBuilder/SavedActionsBuilderPanelLeft';
import savedActionsRequest from '@/services/request/SavedActionsRequest';

export default {
  name: 'memory-page-browser',

  components: {
    MemoryPageActionBarBottom,
    SavedActionsBuilderPanelLeft,
    MemoryPageBrowserPanel,
  },

  props: {
    autoSelect: {
      type: Boolean,
      default: true,
    },
  },

  computed: {
    ...mapState('savedActions', ['actionsToUpload']),

    ...mapState('savedActionsBuilder', ['showActionBuilderPanelLeft']),

    hasSelectedMemories() {
      return this.actionsToUpload?.length > 0;
    },
  },

  async created() {
    await savedActionsRequest.fetchSavedActionLists()
      .then(savedActionLists => this.setSavedActionLists({ savedActionLists }));

    await savedActionsRequest.fetchSavedActions();
  },

  methods: {
    ...mapActions('savedActions', ['setSavedActionLists', 'reset']),
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.memory-page-browser {
  // animation: FadeIn 0.2s, SlideUp 0.2s;
  background-color: #fff;
  height: auto;
  justify-self: stretch;
  position: relative;

  &.show-actions-panel {
    grid-template-areas: 'saved-actions-panel-right';
    grid-template-columns: 1fr;
  }

  &.show-action-builder-panel-and-actions {
    display: grid;
    border-top: 1px solid #DFE1E4;
    grid-template-areas: 'saved-actions-panel-left saved-actions-panel-right';
    grid-template-columns: 1fr 1fr; // 5px for the v-scrollbar
  }

  &.show-memory-action-bar-bottom {
    padding-bottom: $action-bar-height;
  }

  .action-bar-bottom {
    bottom: 0;
    position: fixed;
    width: 100%;
    z-index: 2;
  }
}
</style>
