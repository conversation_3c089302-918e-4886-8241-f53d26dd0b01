<template>
  <section class="memory-page-browser-panel" :class="{'saved-actions-panel-right': showActionBuilderPanelLeft}">
    <loading-switch :status="networkStatus">
      <template #default>
        <section class="memory-page-panel">
          <section class="search-bar" v-if="showActionBuilderPanelLeft">
            <datasets-memories-search />
          </section>
          <memory-page-browser-list />
        </section>
      </template>
      <template #loading>
        <loading-blocks-overlay>Loading Memory Actions</loading-blocks-overlay>
      </template>
    </loading-switch>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import DatasetsMemoriesSearch from '@/components/MemoryPageBrowser/DatasetsMemoriesSearch';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import LoadingSwitch from '@/components/LoadingSwitch';
import MemoryPageBrowserList from '@/components/MemoryPageBrowser/MemoryPageBrowserList';
import NetworkKeys from '@/enum/network-keys';
import NetworkStatus from '@/enum/network-status';

export default {
  name: 'memory-page-browser-panel',

  components: {
    DatasetsMemoriesSearch,
    LoadingBlocksOverlay,
    LoadingSwitch,
    MemoryPageBrowserList,
  },

  data() {
    return {
      ds: null,
      stopped: false,
    };
  },

  created() {
  },

  mounted() {
  },

  beforeDestroy() {
    this.resetSelectedLists();
    this.setSavedActionsToUpload({ ids: [] });
  },

  computed: {
    ...mapGetters('network', ['status']),

    ...mapState('datasets', ['datasets']),

    ...mapState('savedActions', ['savedActions']),

    ...mapState('savedActionsBuilder', ['showActionBuilderPanelLeft']),

    networkStatus() {
      if (this.savedActions == null || this.savedActions.length === 0) {
        return this.status(NetworkKeys.SAVED_ACTIONS);
      }
      return NetworkStatus.SUCCESS;
    },
  },

  methods: {
    ...mapActions('savedActions', ['resetSelectedLists', 'setSavedActionsToUpload']),
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.memory-page-browser-panel {
  @include flex("block", "column", "start", "stretch");
  @include stretch;

  min-height: 0;
  padding: 0 1rem 1rem 1rem;

  .loading-blocks-overlay {
    height: 100%;
  }

  .memory-page-panel {
    @include flex("block", "column", "start", "stretch");
    @include rigid;

    overflow: hidden;

    .memory-page-browser-empty {
      margin: 6rem 0 1rem 0;
      height: 100%;
      padding-left: 0.5rem;
      padding-right: 0.5rem;
    }

    .memory-page-browser-list {
      @include rigid;

      z-index: 1;
    }

    .memory-page-browser-pagination {
      margin-top: 0.5rem;
    }

    .search-bar {
      @include flex('block', 'row', 'start', 'stretch');

      column-gap: 1rem;
      height: 36px; // to avoid dancing height
      margin-top: 1.5rem;
      width: 100%;
    }
  }

  &.saved-actions-panel-right {
    background-color: clr('white');
    box-shadow: 0 6px 12px 0 rgba(0, 0, 0, 0.32);
    grid-area: saved-actions-panel-right;
    margin-right: 0;
    min-height: 600px;
    overflow: hidden;
    min-width: fit-content;
    padding: 0 30px 1rem 30px;
    position: relative;
  }

}
</style>
