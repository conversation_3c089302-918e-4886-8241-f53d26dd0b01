<template>
  <section class="saved-actions-builder-collection-dropdown"
           :class="{ expand, 'not-selected': !selectedList, 'split-screen': showActionBuilderPanelLeft }"
           v-click-outside-handler="{ handler: 'onClickOutside' }"
  >
    <!-- Button -->
    <section class="header" @click="expand = !expand">
      <section class="header-title">
        <i class="fa-solid fa-album-collection icon-save"></i>
        <span class="header-save">{{ titleText }}&nbsp;</span>
        <span v-if="selectedList" class="header-label">{{ selectedList.label }}</span>
      </section>
      <section class="header-control">
        <i v-if="selectedList"
           class="fa-light fa-xmark icon-x"
           v-tooltip.top="{
              class: 'tooltip-base-dark',
              content: 'Clear selection',
              delay: 0,
           }"
           @click.stop="onClickClose"
        />
        <i class="fa fa-caret-down icon-caret" :class="{ expand }"></i>
      </section>
    </section>
    <!-- Dropdown -->
    <section v-if="expand" class="dropdown">
      <section class="body" v-if="filteredLists.length > 0">
        <section class="collection-list">
          <section v-for="item in filteredLists"
            class="collection-item"
            :key="item.id"
            @click="onClickCollection(item)"
          >
            <template v-if="renamingId !== item.id" >
              <section class="collection-item-label">
                <base-radio-with-tick-mark :value="selectedId === item.id" />
                <span class="text">{{ item.label }}</span>
              </section>
              <section class="collection-item-actions">
                <section v-tooltip.bottom="{ class: 'tooltip-saved-search-controls', content: 'Duplicate Collection', delay: 0 }"
                  class="collection-item-action"
                  @click.stop="onClickCopy(item)"
                >
                  <i class="fa-solid fa-copy icon"></i>
                </section>

                <section v-tooltip.bottom="{ class: 'tooltip-saved-search-controls', content: 'Rename Collection', delay: 0 }"
                  class="collection-item-action"
                  @click.stop="onClickRename(item)"
                >
                  <i class="fa-solid fa-pen icon"></i>
                </section>

                <section v-tooltip.bottom="{ class: 'tooltip-saved-search-controls', content: 'Delete Collection', delay: 0 }"
                  class="collection-item-action trash"
                  @click.stop="onClickDelete(item)"
                >
                  <i class="fa-solid fa-trash icon"></i>
                </section>
              </section>
            </template>
            <template v-else>
              <section class="show-renaming">
                <section class="inner renaming" @click.stop>
                  <section class="renaming-input">
                    <i class="fa-regular fa-pen icon edit"/>
                    <section class="input">
                      <base-input
                          v-model="localRename"
                          :focus="true"
                          @submit="onClickConfirmRename(item)"
                      />
                    </section>
                    <base-tag
                        :background-colour="'#00BD1E'"
                        :colour="'#00A51A'"
                        :colour-hover="'#00A51A'"
                        :text="'Confirm'"
                        :text-colour="'white'"
                        :border-radius="'2rem'"
                        :padding="'0.3rem 0.5rem 0.3rem 0.5rem'"
                        @click="onClickConfirmRename(item)"
                    />
                    <i class="fa-solid fa-x icon x-icon" @click.stop="onClickCancelRename" />
                  </section>
                </section>
              </section>
            </template>
          </section>
        </section>
      </section>

      <section v-else class="body">
        <p class="msg">Create a new collection to save actions.</p>
      </section>

      <section class="footer">
        <section class="view-all-button" @click="onClickViewAllCollection">
          All Collections
        </section>
        <section class="create-button" @click="onClickCreateCollection">
          <i class="fa-regular fa-plus icon-save"></i>
          New Collection
        </section>
      </section>
    </section>
  </section>
</template>

<script>

import BaseInput from '@/components/Base/BaseInput';
import BaseRadio from '@/components/Base/BaseRadio';
import BaseRadioWithTickMark from '@/components/Base/BaseRadioWithTickMark';
import BaseTag from '@/components/Base/BaseTag';
import SaveActionCollectionDuplicationModal from '@/components/MemoryPageBrowser/Modal/SaveActionCollectionDuplicationModal';
import SavedActionBuilderCollectionUnsavedModal from '@/components/MemoryPageBrowser/Modal/SavedActionBuilderCollectionUnsavedModal';
import ThemesBuilderCollectionCreateModal from '@/components/ThemesBuilder/ThemesBuilderCollectionCreateModal';
import WorkspaceMemoryCollectionsModal from '@/components/WorkspaceMemoryCollections/WorkspaceMemoryCollectionsModal';
import clickOutsideHandler from '@/directives/click-outside-handler';
import { mapActions, mapGetters, mapState } from 'vuex';
import { savedActionApi } from '@/services/api';
import { savedActionsRequest } from '@/services/request';

export default {
  name: 'saved-actions-builder-collection-dropdown',

  components: {
    BaseInput,
    BaseRadio,
    BaseRadioWithTickMark,
    BaseTag,
  },

  data() {
    return {
      expand: false,
      loading: false,
      localRename: '',
      open: false,
      renamingId: null,
    };
  },

  directives: {
    clickOutsideHandler,
  },

  computed: {
    ...mapGetters('savedActions', ['savedActionListsByUserId', 'selectedListHasChanges']),

    ...mapState('savedActions', [
      'actionsToUpload',
      'savedActions',
      'selectedList',
    ]),

    ...mapState('savedActionsBuilder', ['showActionBuilderPanelLeft']),

    ...mapState('user', ['activeWorkspace', 'user']),

    filteredLists() {
      return this.savedActionListsByUserId(this.user.id);
    },

    selectedId() {
      return this.selectedList?.id;
    },

    titleText() {
      return this.selectedList ? 'Your Collections:' : 'Select a collection';
    },
  },

  watch: {
    selectedList(newVal, oldVal) {
      if (newVal?.id !== oldVal?.id) {
        this.expand = false;
        this.setSavedActionsToUpload({ ids: newVal?.list });
        savedActionsRequest.fetchSavedActions();
      }
    },

    expand() {
      this.renamingId = null;
    },
  },

  async created() {
    const savedActionLists = await savedActionsRequest.fetchSavedActionLists();

    this.setSavedActionLists({ savedActionLists });
  },

  destroyed() {
    this.setSelectedList({ list: null });
  },

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    ...mapActions('savedActions', [
      'selectSavedAction',
      'setIsApplying',
      'setNextSelectedList',
      'setPage',
      'setSavedActionLists',
      'setSavedActions',
      'setSavedActionsToUpload',
      'setSelectedList',
    ]),

    onClickCancelRename() {
      this.renamingId = null;
    },

    onClickClose() {
      this.setSelectedList({ list: null });
      this.selectSavedAction({ id: null });
    },

    onClickCreateCollection() {
      this.setModalComponent({ component: ThemesBuilderCollectionCreateModal });
    },

    onClickCollection(item) {
      if (item.id !== this.selectedList?.id && this.selectedListHasChanges) {
        this.setNextSelectedList({ list: item });
        this.setModalComponent({ component: SavedActionBuilderCollectionUnsavedModal });
      } else {
        this.setSelectedList({ list: item });
        this.selectSavedAction({ id: null });
        this.setPage({ page: 1 });
      }
      this.expand = false;
    },

    onClickFilter() {
      if (!this.multiDatasets) {
        this.open = !this.open;
      }
    },

    onClickOutside() {
      this.expand = false;
    },

    onClickRename(item) {
      this.renamingId = item.id;
      this.localRename = item.label;
    },

    onClickViewAllCollection() {
      this.setIsApplying({ isApplying: false });
      this.setModalComponent({ component: WorkspaceMemoryCollectionsModal });
    },

    async onClickConfirmRename(item) {
      await savedActionApi.renameSavedActionList(this.activeWorkspace?.id, item.id, this.localRename);
      await savedActionsRequest.fetchSavedActionLists()
        .then(savedActionLists => {
          this.setSavedActionLists({ savedActionLists });
        });
      this.renamingId = null;
    },

    async onClickCopy(item) {
      this.setSelectedList({ list: item });
      this.selectSavedAction({ id: null });
      this.setModalComponent({ component: SaveActionCollectionDuplicationModal });
    },

    async onClickDelete(item) {
      if (this.selectedList?.id === item.id) {
        this.setSelectedList({ list: null });
      }
      await savedActionApi.deleteSavedActionList(this.activeWorkspace?.id, item.id);
      await savedActionsRequest.fetchSavedActionLists()
        .then(savedActionLists => {
          this.setSavedActionLists({ savedActionLists });
        });
      this.selectSavedAction({ id: null });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.saved-actions-builder-collection-dropdown {
  @include flex("block", "column", "center", "start");

  background: #ECEDF0;
  border: solid 1px transparent;
  border-left: solid 1px rgba(0, 0, 0, 0.1);
  position: relative;
  width: 400px;

  &:hover {
    background: rgba(236, 237, 240, 1);
    border: solid 1px transparent;
    border-radius: $border-radius-small;

    .header .header-control .icon-x {
      opacity: 1;
    }
  }

  &.not-selected {
    background: rgba(236, 237, 240, 1);
    border: solid 1px transparent;

    &:hover {
      &:not(.expand) {
        background: rgba(215, 218, 227, 1);
      }
    }
  }

  &.expand {
    background-color: #FFFFFF;
    border: solid 1px rgba(97, 87, 192, 1);
    border-bottom-color: transparent;
    border-radius: 2px 2px 0 0;
    box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.4);

    .dropdown {
      box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.4);
    }
  }

  .header {
    @include flex("block", "row", "between", "center");

    cursor: pointer;
    font-size: 12px;
    padding: 0.5rem 1rem 0.5rem 0.5rem;
    width: inherit;

    .header-title {
      @include flex("block", "row", "start", "center");
      @include truncate;

      .icon-save {
        font-size: 14px;
        margin-left: 0.1rem;
        margin-right: 0.5rem;
      }

      .header-save {
        font-weight: $font-weight-bold;
      }

      .header-label {
        @include truncate;
      }
    }

    .header-control {
      @include flex("block", "row", "end", "center");

      .icon-x {
        @include flex("block", "row", "center", "center");

        border-radius: 50%;
        font-size: $font-size-base;
        height: $font-size-base;
        opacity: 0.5;
        width: $font-size-base;

        &:hover {
          background-color: rgba(97, 87, 192, 0.1);;
          transition: opacity $interaction-transition-time;
        }
      }

      .icon-caret {
        font-size: $font-size-xs;
        font-weight: $font-weight-bold;
        padding-left: 0.4rem;
        transition: transform $interaction-transition-time;

        &.expand {
          transform: rotateX(180deg);
        }
      }
    }
  }

  .dropdown {
    background-color: #FFFFFF;
    border: 1px solid rgba(97, 87, 192, 1);
    border-top: 0.5px solid rgba(97, 87, 192, 0.2);
    left: -1px;
    position: absolute;
    top: 30px;
    width: 400px;
    z-index: 2;

    .body {
      @include scrollbar-thin;

      border-bottom: 0.5px solid rgba(97, 87, 192, 0.2);
      font-size: $font-size-xs;
      max-height: 300px;
      overflow-y: auto;
      padding: 0 0.6rem 0.6rem 0.6rem;
      width: 100%;

      .collection-list {
        .collection-item {
          @include flex("block", "row", "between", "center");

          border-bottom: 1px solid #DEE1E4;
          cursor: pointer;
          padding: 0.6rem 0;

          &:last-child {
            border-bottom: none;
          }

          .collection-item-label {
            @include flex("block", "row", "start", "center");
            @include truncate;

            margin-right: 1rem;

            .base-radio-with-tick-mark {
              margin-right: 0.4rem;
            }

            .text {
              @include truncate;
            }
          }

          .collection-item-actions {
            @include flex("block", "row", "center", "center");

            .collection-item-action {
              @include flex("block", "row", "center", "center");

              background-color: #FFF;
              border-radius: 50%;
              border: 1px solid rgba(#24124D, 0.45);
              color: #2D1757;
              font-size: $font-size-xxs;
              font-weight: $font-weight-bold;
              height: 1.5rem;
              margin-left: 0.5rem;
              padding: 0.2rem 0.4rem;
              width: 1.5rem;

              &:hover, &:active, &:focus {
                border: 1px solid #24124D;
              }

              .icon {
                //margin-right: 0.2rem;
              }

              &.trash {
                color: #CB1509;
                border: 1px solid rgba(#CB1509, 0.45);

                &:hover, &:active, &:focus {
                  border: 1px solid #CB1509;
                }
              }
            }
          }

          .show-renaming {
            .inner {
              grid-template-columns: 1fr;
              grid-template-rows: 30px+10 30px;
              background: rgba(255, 255, 255, 0.35);
              border-radius: 3px;

              .renaming-input {
                display: grid;
                grid-template-columns: 1.5rem 1fr 5.5rem min-content;
                padding-left: 0.3rem;
                padding-right: 0.3rem;

                b {
                  font-size: $font-size-sm;
                  align-self: center;
                  justify-self: center;
                }

                .input {
                  display: grid;
                  overflow-x: hidden;
                  padding-left: 0.3rem;
                  place-items: center stretch;

                  .base-input {
                    background-color: clr('white');
                    border: $border-standard;
                    border-radius: $border-radius-medium;
                    font-size: $font-size-sm;
                    padding: 0.3rem;
                  }
                }

                .icon {
                  align-self: center;
                  cursor: pointer;
                  font-size: $font-size-sm;
                  padding: 0.2rem;
                  justify-self: center;
                  transition: opacity $interaction-transition-time;

                  &:hover {
                    opacity: 0.6;
                  }

                  &.edit {
                    &:hover {
                      opacity: 1;
                    }
                  }
                }
              }
            }
          }

        }
      }

      .msg {
        margin-top: 1rem;
      }
    }

    .footer {
      @include flex("block", "row", "center", "center");
      font-size: $font-size-xxs;
      font-weight: $font-weight-bold;
      padding: 0.8rem 0.6rem 0.8rem 0.6rem;
      width: 100%;

      .create-button {
        @include flex("block", "row", "center", "center");

        background-color: #5F52C5;
        border-radius: $border-radius-small;
        color: clr('white');
        cursor: pointer;
        margin-left: 1rem;
        padding: 0.4rem;
        text-transform: uppercase;
        width: 100%;

        &:hover {
          background-color: #4433B0;
        }

        .icon-save {
          margin-right: 0.4rem;
        }
      }

      .view-all-button {
        @include flex("block", "row", "center", "center");

        background-color: #fff;
        border-radius: $border-radius-small;
        border: 1px solid rgba(95, 82, 197, 0.2);
        color: #5F52C5;
        cursor: pointer;
        padding: 0.4rem;
        text-transform: uppercase;
        width: 100%;

        &:hover {
          border: 1px solid rgba(95, 82, 197);
        }

      }
    }
  }
}
</style>
