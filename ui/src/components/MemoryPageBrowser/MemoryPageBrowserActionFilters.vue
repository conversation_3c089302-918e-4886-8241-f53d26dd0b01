<template>
  <section class="memory-page-browser-action-filters"
           @click.stop="showDropdown = !showDropdown"
           v-click-outside-handler="{
             handler: 'onClickOutside'
           }"
  >
    <section class="dropdown-button">
      <i class="fa-solid fa-eye icon icon-eye"></i>
      <span class="text"><b>Show:</b> {{ typeText }}</span>
      <i class="fa fa-caret-down icon-caret" :class="{ open : showDropdown }"></i>
    </section>
    <section class="filters" v-if="showDropdown" @close="showDropdown = false">
      <section class="dropdown-arrow"></section>
      <section class="actions-toolbar">
          <section class="action" v-for="(action) in actionTypes"
                   :class="[action.color]"
                   @click.stop="onClickSetExcludedType(action.type)"
          >
            <base-checkbox-solid :value="!excludedTypes.includes(action.type)" />
            <span class="title">{{action.title}}</span>
            <i :class="action.icon" class="fa-solid icon" />
          </section>
        </section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseCheckbox from '@/components/Base/BaseCheckbox';
import BaseCheckboxSolid from '@/components/Base/BaseCheckboxSolid';
import BaseDropdown from '@/components/Dropdown/BaseDropdown';
import clickOutsideHandler from '@/directives/click-outside-handler';
import SavedActionsType from '@/enum/saved-actions-type';

export default {
  name: 'memory-page-browser-action-filters',

  components: {
    BaseCheckbox,
    BaseCheckboxSolid,
    BaseDropdown,
  },

  directives: {
    clickOutsideHandler,
  },

  props: {
    doFetchSavedActions: {
      type: Function,
      required: true,
    },
  },

  data() {
    return {
      showDropdown: false,
    };
  },

  computed: {
    ...mapState('savedActions', ['excludedTypes']),

    ...mapGetters('savedActions', [
      'actionsCreateSearchTheme',
      'actionsDeleteTheme',
      'actionsMergeThemes',
      'actionsRenameTheme',
    ]),

    actionTypes() {
      return SavedActionsType.enumValues.map(value => {
        return {
          icon: value.icon(),
          color: value.lowerCase(),
          title: value.upperCase(),
          type: value,
        };
      });
    },

    typeText() {
      if (this.excludedTypes.length === SavedActionsType.enumValues.length) return 'None';
      if (this.excludedTypes.length > 0) return 'Filtered';
      return 'All Types';
    },
  },

  methods: {
    ...mapActions('savedActions', ['toggleExcludedType']),

    activeAction(type) {
      return this.excludedTypes.includes(type);
    },

    onClickOutside() {
      this.showDropdown = false;
    },

    onClickSetExcludedType(type) {
      this.toggleExcludedType({ type });
      this.doFetchSavedActions();
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';
@import '~font-awesome/css/font-awesome.min.css';

.memory-page-browser-action-filters {
  @include flex("block", "row", "start", "center");

  background-color: rgba(28, 28, 28, 0.9);
  border-radius: 3px;
  border: 2px solid rgba(75, 114, 240, 0.15);
  cursor: pointer;
  font-size: $font-size-xxs;
  height: 100%;
  padding: 0.3rem 0.5rem;
  position: relative;
  width: fit-content;

  &:hover {
    background-color: clr("black");
  }

  .filters {
    border-radius: 2px;
    background-color: #ffffff;
    box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.7);
    border: 1px solid #9F9F9F;
    left: 0;
    position: absolute;
    top: 48px;
    z-index: 1;

    .dropdown-arrow {
      border-bottom: 8px solid white;
      border-left: 8px solid transparent;
      border-right: 8px solid transparent;
      border-top: none;
      position: absolute;
      left: 16px;
      filter: drop-shadow(0px -1px 0px rgba(159, 159, 159, 1));
      top: -7px;
    }

    .actions-toolbar {
      @include flex("block", "column", "start", "start");
      background: #FFFFFF;
      color: #9A9A9A;
      font-size: 10px;
      font-weight: 800;
      text-align: center;
      text-transform: uppercase;

      .action {
        @include flex("block", "row", "start", "center");

        cursor: pointer;
        padding: 0.5rem 1rem;
        width: 100%;

        &:not(:first-child) {
          border-top: 1px solid #D3D0D9;
        }

        &.deleted  {
          color: #FF4141;
        }

        &.searched {
          color: #5F52C5;
        }

        &.merged {
          color: #0CBB3D;
        }

        &.renamed {
          color: #C552B3;
        }

        .icon {
          margin-left: 0.3rem;
        }

        .title {
          margin-left: 0.3rem;
        }
      }

    }
  }

  .dropdown-button {
    @include flex("block", "row", "start", "center");
    color: clr('white');
    width: 8.6rem;

    .icon-caret {
      font-weight: $font-weight-bold;
      margin-left: auto;
      margin-right: 0.2rem;
      transition: transform $interaction-transition-time;

      &.open {
        transform: rotateX(180deg);
      }
    }

    .icon-eye {
      margin-right: 0.4rem;
    }

    .text {
      text-transform: uppercase;
      transition: all $interaction-transition-time;
    }
  }
}
</style>
