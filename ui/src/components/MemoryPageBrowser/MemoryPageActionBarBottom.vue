<template>
  <section class="memory-page-action-bar-bottom" :class="{ showBar }">
    <section class="other-btn"></section>
    <section class="btn delete-btn"
             :style="{ display: hasSelected ? 'flex' : 'none'}"
             @click="onClickDelete"
    >
      <i class="fa-solid fa-trash icon" />
      <span class="label">Delete</span>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import MemoryThemeMultiDeleteModal from '@/components/MemoryPageBrowser/Modal/MemoryThemeMultiDeleteModal';

export default {
  name: 'memory-page-action-bar-bottom',

  props: {
    showBottomBar: {
      type: Boolean,
      default: false,
    },
  },

  computed: {
    ...mapState('datasetManagementTabs', ['viewingMemoryPageTab']),

    ...mapState('savedActions', ['actionsToDelete', 'actionsToUpload']),

    hasSelected() {
      return this.actionsToUpload?.length > 0;
    },

    showBar() {
      return this.viewingMemoryPageTab && this.hasSelected;
    },
  },

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    ...mapActions('savedActions', ['addSavedActionsToDelete', 'removeSavedActionsFromDelete']),

    onClickDelete() {
      if (!this.hasSelected) {
        return;
      }

      if (this.actionsToDelete?.length) {
        this.removeSavedActionsFromDelete({ ids: [...this.actionsToDelete] });
      }
      this.addSavedActionsToDelete({ ids: [...this.actionsToUpload] });
      this.setModalComponent({ component: MemoryThemeMultiDeleteModal });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.memory-page-action-bar-bottom {
  align-content: center;
  background-color: #0A0423;
  border-top: 2px solid $action-bar-bdr;
  display: grid;
  grid-template-columns: 1fr auto;
  height: $action-bar-height;
  padding: 0 2rem;
  transform: translateY($action-bar-height);
  transition: all 0.3s ease-in-out;
  visibility: hidden;
  width: inherit;

  &.showBar {
    transform: translateY(0);
    visibility: visible;
  }

  .btn {
    &.delete-btn {
      align-items: center;
      background-color: #FFFFFF;
      border-radius: 2px;
      color: clr("red");
      cursor: pointer;
      display: flex;
      font-size: 0.7rem;
      font-weight: 800;
      justify-content: center;
      padding: 0.4rem 0.8rem 0.4rem 0.6rem;
      text-transform: uppercase;

      &:hover {
        background-color: clr("red");
        color: #FFFFFF;
        opacity: 0.8;
      }

      .icon {
        margin-right: 0.2rem;
        transition: transform $interaction-transition-time;
      }
    }
  }
}
</style>
