<template>
  <section class="saved-action-builder-collection-unsaved-modal">
    <section class="header">
      <h3>This collection has unsaved changes.</h3>
    </section>
    <section class="body">

      <loading-blocks-overlay v-if="loading" />
      <base-button
          v-else
          class="confirm"
          colour="base"
          size="small"
          @click="onConfirm">
        <i class="fa-solid fa-floppy-disk icon" />
        Save & Continue
      </base-button>

      <base-button
          class="cancel"
          colour="light"
          size="small"
          type="outline"
          @click="onContinue">
        Continue Without Saving
        <i class="fa-solid fa-arrow-right icon" />
      </base-button>

    </section>
    <section class="footer">
      <base-button
          class="cancel"
          colour="light"
          size="small"
          type="link"
          @click="closeModal">
        Go back
      </base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import MemoryPageUpdateCollectionToast from '@/components/MemoryPageBrowser/Toast/MemoryPageUpdateCollectionToast';
import ThemesBuilderNotSavingMemoryDropdown from '@/components/ThemesBuilder/ThemesBuilderNotSavingMemoryDropdown';

import { savedActionApi } from '@/services/api';
import { savedActionsRequest } from '@/services/request';

export default {
  name: 'saved-action-builder-collection-unsaved-modal',

  components: {
    BaseButton,
    BaseInput,
    LoadingBlocksOverlay,
    ThemesBuilderNotSavingMemoryDropdown,
  },

  data() {
    return {
      inputValue: 'Default Collection',
      loading: false,
    };
  },

  computed: {
    ...mapState('savedActions', ['nextSelectedList', 'selectedList']),

    ...mapState('user', ['activeWorkspace']),
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('savedActions', [
      'setNextSelectedList',
      'setSavedActionLists',
      'setSavedActionsToUpload',
      'setSelectedList',
    ]),

    ...mapActions('toast', {
      addToast: 'add',
    }),

    async onConfirm() {
      this.loading = true;

      const updated = { ...this.selectedList.list, list: this.selectedList.actions };

      await savedActionApi.updateSavedActionList(this.activeWorkspace?.id, updated);

      await savedActionsRequest.fetchSavedActionLists()
        .then(savedActionLists => {
          this.setSavedActionLists({ savedActionLists });
        });

      this.addToast({ toast: { component: MemoryPageUpdateCollectionToast, id: 'memory-page-update-collection-toast' } });

      this.loading = false;

      this.setSelectedList({ list: this.nextSelectedList });
      this.setNextSelectedList({ list: null });
      this.closeModal();
    },

    onContinue() {
      this.setSelectedList({ list: this.nextSelectedList });
      this.closeModal();
    },

  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.saved-action-builder-collection-unsaved-modal {
  @include modal;

  width: 440px;

  .header{
    @include flex("block", "row", "start", "center");

    background-color: clr('white');
    padding: 1.5em 2em 1.5em 2em;

    h3 {
      color: #2D1757;
      font-size: 12px;
      font-weight: 700;
      line-height: 19px;
    }
  }

  .body {
    @include flex("block", "row", "space-between", "stretch");

    background-color: clr('white');
    padding: 1.5em 2em 1.5em 2em;
    text-transform: uppercase;

    .cancel {
      @include flex("block", "row", "center", "center");

      color: #2D1757;
      font-size: 10px;
      letter-spacing: 0.3px;
      line-height: 12px;
      text-align: center;

      .icon {
        margin-left: 0.5rem;
      }

      &:hover,
      &:focus {
        background-color: clr("white");
        border: $border-light solid clr("purple", "rich");
        color: #2D1757;
        outline: none;
      }
    }

    .confirm {
      @include flex("block", "row", "center", "center");

      background-color: #2D1757;
      color: #FFFFFF;
      font-size: 10px;
      letter-spacing: 0.3px;
      line-height: 12px;
      padding: 0.6rem 1rem;
      text-align: center;

      &:hover {
        background-color: #2D1757;
        filter: brightness(125%);
      }

      .icon {
          margin-right: 0.5rem;
      }
    }

  }

  .footer {
    padding: 1rem 2em 1rem 1.5rem;

  }

  .loading-blocks-overlay {
    height: 2rem;
  }
}
</style>
