<template>
  <section class="saved-action-builder-no-collection-saving-modal">
    <section class="header">
      <h3 v-if="hasCollection">Select a Collection or Create a new one</h3>
      <h3 v-else>No Collections Exist - Create a Collection</h3>
    </section>

    <section class="body">
      <section v-if="hasCollection">
        <span class="content">Actions must be saved to a collection. Select one below, or create a new collection to continue.</span>
        <section class="selection existing"
                 @click="onSelectExisting"
                 v-click-outside-handler="{
                   handler: 'onClickOutside',
                 }"
        >
          <base-radio :value="selectExisting" radio-size="small" />

          <section class="selection-item" :class="{ inactive: !selectExisting, invalid: invalidCollection }">
            <span v-if="localSelectedCollection" class="label">{{ localSelectedCollection.label }}</span>
            <span v-else class="label">Select a collection</span>
            <i class="fa fa-caret-down icon" :class="{ open: showExistingCollections }" />
          </section>
        </section>

        <section class="selection new" @click="selectExisting = false">
          <base-radio :value="!selectExisting" radio-size="small" />
          <base-input v-model="inputValue" class="collection-name" :class="{ inactive: selectExisting, invalid: invalidLabel }" @submit="onConfirm" placeholder="Collection Name"/>
        </section>
      </section>

      <section v-else>
        <span class="content">Actions must be saved to a collection. Please name your first collection to continue.</span>
        <section class="selection">
          <base-input v-model="inputValue" class="collection-name" :class="{ invalid: invalidLabel }" @submit="onConfirm" placeholder="Collection Name"/>
        </section>
      </section>
    </section>

    <section class="footer">
      <base-button
        class="cancel"
        colour="light"
        size="small"
        type="link"
        @click="closeModal">
        Cancel
      </base-button>

      <loading-blocks-overlay v-if="loading" />
      <base-button
        v-else
        class="confirm"
        colour="base"
        size="small"
        @click="onConfirm">
        Submit
      </base-button>
    </section>
    <!-- Collection list dropdown -->
    <saved-actions-builder-collection-simple-dropdown :class="{ hide: !showExistingCollections }"
                                                      :excludedDefault="true"
                                                      :selectedCollection="localSelectedCollection"
                                                      @onSelect="onSelectCollection"
    />
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import BaseRadio from '@/components/Base/BaseRadio';
import clickOutsideHandler from '@/directives/click-outside-handler';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import SavedActionsBuilderCollectionSimpleDropdown from '@/components/MemoryPageBrowser/SavedActionsBuilderCollectionSimpleDropdown';

import { savedActionApi } from '@/services/api';
import savedActionsRequest from '@/services/request/SavedActionsRequest';

export default {
  name: 'saved-action-builder-no-collection-saving-modal',

  components: {
    BaseButton,
    BaseInput,
    BaseRadio,
    LoadingBlocksOverlay,
    SavedActionsBuilderCollectionSimpleDropdown,
  },

  directives: {
    clickOutsideHandler,
  },

  data() {
    return {
      selectExisting: true,
      inputValue: '',
      invalidLabel: false,
      invalidCollection: false,
      loading: false,
      localSelectedCollection: null,
      showExistingCollections: false,
    };
  },

  computed: {
    ...mapState('savedActions', ['savedActionLists', 'selectedList']),

    ...mapState('user', ['user', 'activeWorkspace']),

    hasCollection() {
      return this.savedActionLists.filter(sal => !sal.default).length > 0;
    },

    textSelectionName() {
      if (this.localSelectedCollection) return this.localSelectedCollection.label;

      return 'Select a collection';
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('savedActions', ['setSavedActionLists', 'setSelectedList']),

    ...mapActions('savedActionsBuilder', ['setProceeding']),

    onClickOutside() {
      this.showExistingCollections = false;
    },

    async onConfirm() {
      if (this.loading) return;

      if ((!this.hasCollection || !this.selectExisting) && !this.inputValue?.trim().length > 0) {
        this.invalidLabel = true;
        return;
      }

      if (this.hasCollection && this.selectExisting && !this.localSelectedCollection) {
        this.invalidCollection = true;
        return;
      }

      this.loading = true;

      if (this.hasCollection && this.selectExisting) {
        this.setSelectedList({ list: this.localSelectedCollection });
      } else {
        const createdId = await savedActionApi.createSavedActionList(this.activeWorkspace?.id, this.inputValue.trim(), null);

        await savedActionsRequest.fetchSavedActionLists()
          .then(savedActionLists => {
            this.setSavedActionLists({ savedActionLists });
            this.setSelectedList({ list: savedActionLists.find(list => list.id === createdId) });
          });
      }

      this.loading = false;

      this.setProceeding({ proceeding: true });
      this.closeModal();
    },

    onSelectCollection(item) {
      this.localSelectedCollection = item;
    },

    onSelectExisting() {
      this.selectExisting = true;
      this.showExistingCollections = true;
    },
  },

  watch: {
    inputValue() {
      this.invalidLabel = false;
    },

    localSelectedCollection() {
      this.invalidCollection = false;
    },

    selectExisting() {
      this.invalidLabel = false;
      this.invalidCollection = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.saved-action-builder-no-collection-saving-modal {
  @include modal;
  position: relative;
  width: 600px;

  .header {
    @include flex("block", "row", "start", "start");
    border-bottom: none;
    color: #2D1757;
    padding: 1.5rem 2rem 1rem 2rem;
  }

  .body {
    background-color: clr('white');
    padding: 0 2rem 3rem 2rem;

    .content {
      color: #2D1757;
      font-size: 12px;
      font-weight: 400;
      line-height: 19px;
      margin-bottom: 1rem;
    }

    .selection {
      @include flex("block", "row", "start", "center");
      margin-top: 1rem;

      .base-radio {
        cursor: pointer;
        margin-right: 0.5rem;
        min-width: 14px;
        width: fit-content;
      }

      .base-input {
        &.inactive {
          opacity: 0.5;
        }

        &.invalid {
          border: 1px solid clr("red");
        }
      }

      .selection-item {
        @include flex("block", "row", "between", "center");
        border: 1px solid clr("purple");
        border-radius: 4px;
        padding: 0.5rem;
        width: 96%;

        &.inactive {
          border: 1px solid $border-color;
          opacity: 0.5;
        }

        &.invalid {
          border: 1px solid clr("red");
        }

        .label {
          @include truncate;
        }

        .icon {
          margin-left: 0.5rem;
          transition: all $interaction-transition-time;

          &.open {
            transform: rotate(180deg);
          }
        }
      }
    }
  }

  .footer {

    .confirm {
      background-color: #2D1757;
      height: 2rem;

      &:hover {
        background-color: #2D1757;
        filter: brightness(125%);
      }
    }

    .loading-blocks-overlay {
      height: 2rem;
    }
  }

  .saved-actions-builder-collection-simple-dropdown {
    position: absolute;
    right: 32px;
    top: 160px;
    z-index: 2;

    &.hide {
      display: none;
    }
  }
}
</style>
