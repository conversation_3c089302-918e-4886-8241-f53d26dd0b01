<template>
  <section class="memory-theme-multi-delete-modal">
    <section class="header">
      Delete memory {{ textCount === 1 ? 'action' : 'actions' }}
    </section>
    <section class="body">
      <span class="text">Deleting {{ textCount }} memory {{ textCount === 1 ? 'action' : 'actions' }}. Are you sure you want to proceed?</span>
    </section>
    <section class="footer">
      <base-button class="cancel" colour="light" type="link" @click="onCancel">Cancel</base-button>
      <base-button v-if="!deleting" class="delete" colour="danger" @click="onDelete">Delete</base-button>
      <loading-blocks-overlay v-else />
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import intercomEvent from '@/services/IntercomEvent';
import intercomEvents from '@/enum/intercom-events';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import SavedActionDeletedToast from '@/components/MemoryPageBrowser/Toast/SavedActionDeletedToast';

import { savedActionApi } from '@/services/api';
import { savedActionsRequest } from '@/services/request';

export default {
  name: 'memory-theme-multi-delete-modal',

  components: {
    BaseButton,
    LoadingBlocksOverlay,
    SavedActionDeletedToast,
  },

  beforeDestroy() {
    if (this.actionsToDelete?.length) {
      this.removeSavedActionsFromDelete({ ids: [...this.actionsToDelete] });
    }
  },

  computed: {
    ...mapState('savedActions', [
      'actionsToDelete',
      'actionsToUpload',
      'limit',
      'savedActions',
      'selected',
    ]),

    ...mapState('user', ['activeWorkspace']),

    textCount() {
      return this.actionsToDelete?.length || 0;
    },
  },

  data() {
    return {
      deleting: false,
    };
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('savedActions', [
      'removeSavedActionsFromDelete',
      'selectSavedAction',
      'setPage',
      'setSavedActionLists',
      'setSavedActionsToUpload',
    ]),

    ...mapActions('toast', ['add']),

    async loadSavedActions() {
      await savedActionsRequest.fetchSavedActions();
      await savedActionsRequest.fetchSavedActionLists()
        .then(savedActionLists => this.setSavedActionLists({ savedActionLists }));
    },

    onCancel() {
      this.closeModal();
    },

    async onDelete() {
      if (this.deleting) {
        return;
      }
      this.deleting = true;

      await savedActionApi.deleteSavedActions(this.activeWorkspace.id, [...this.actionsToDelete]);
      intercomEvent.send(intercomEvents.DELETE_MEMORY_ACTION);
      this.resetLocalData();
      await this.loadSavedActions();

      this.add({ toast: { component: SavedActionDeletedToast, id: 'saved-action-deleted-toast' } });
      this.deleting = false;
      this.closeModal();
    },

    resetLocalData() {
      if (this.actionsToDelete?.length >= this.savedActions?.length || this.actionsToDelete?.length >= this.limit) {
        this.setPage({ page: 1 });
      }
      if (this.selected && this.actionsToDelete.includes(this.selected)) {
        this.selectSavedAction({ id: null });
      }

      const remainingIds = this.actionsToUpload.filter(id => !this.actionsToDelete.includes(id));
      this.setSavedActionsToUpload({ ids: remainingIds });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.memory-theme-multi-delete-modal {
  @include modal;

  .header {
    font-size: 1.25rem;
    font-weight: $font-weight-medium;
    padding: 2rem 1.5rem 1rem;
    text-transform: capitalize;
  }

  .body {
    @include flex("block", "column", "start", "stretch");
    padding: 1.5rem;

    .text {
      font-size: $font-size-sm;
    }
  }

  .footer {
    padding: 1rem 1.5rem;

    .cancel {
      padding-left: 0;
    }

    .loading-blocks-overlay {
      height: 2rem;
    }
  }
}
</style>
