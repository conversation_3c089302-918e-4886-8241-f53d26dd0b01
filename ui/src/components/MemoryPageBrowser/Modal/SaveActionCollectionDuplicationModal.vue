<template>
  <section class="save-action-collection-duplication-modal">
    <section class="header">
      <i class="fa-solid fa-copy icon"></i>
      <h3>Duplicate a Collection</h3>
    </section>
    <section class="body">
      <base-input placeholder="Collection Name" v-model="inputValue" :focus="true" @submit="onConfirm"/>
    </section>

    <section class="footer">
      <base-button
        class="cancel"
        colour="light"
        size="small"
        type="link"
        @click="closeModal"
        >Cancel</base-button
      >

      <loading-blocks-overlay v-if="loading" />
      <base-button
        v-else
        class="confirm"
        colour="base"
        :disabled="disabled"
        icon="save"
        size="small"
        @click="onConfirm"
        >Save</base-button
      >
    </section>
  </section>
</template>

<script>
import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import MemoryPageCreateCollectionToast from '@/components/MemoryPageBrowser/Toast/MemoryPageCreateCollectionToast';
import { mapActions, mapState } from 'vuex';
import { savedActionApi } from '@/services/api';
import savedActionsRequest from '@/services/request/SavedActionsRequest';

export default {
  name: 'save-action-collection-duplication-modal',

  components: {
    BaseButton,
    BaseInput,
    LoadingBlocksOverlay,
    MemoryPageCreateCollectionToast,
  },

  data() {
    return {
      inputValue: '',
      loading: false,
    };
  },

  computed: {
    ...mapState('user', ['activeWorkspace']),

    ...mapState('savedActions', ['selectedList']),

    disabled() {
      return this.inputValue === '';
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal', 'reset']),

    ...mapActions('savedActions', ['setSavedActionLists', 'setSelectedList']),

    ...mapActions('toast', {
      addToast: 'add',
    }),

    async onConfirm() {
      if (this.inputValue === '') return;

      this.loading = true;

      const createdId = await savedActionApi.duplicateSavedActionList(this.activeWorkspace?.id, this.selectedList.id, this.inputValue);

      await savedActionsRequest.fetchSavedActionLists()
        .then(savedActionLists => {
          this.setSavedActionLists({ savedActionLists });
          this.setSelectedList({ list: savedActionLists.find(list => list.id === createdId) });
        });

      this.loading = false;
      this.closeModal();
      this.reset();

      this.addToast({ toast: { component: MemoryPageCreateCollectionToast, id: 'memory-page-create-collection-toast' } });
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.save-action-collection-duplication-modal {
  @include modal;

  .header{
    @include flex("block", "row", "start", "center");

    h3 {
      font-weight: $font-weight-bold;
    }

    .icon {
      margin-right: 0.5em;
    }
  }

  .body {
    background-color: clr('white');
    padding: 2rem;

    h3 {
      font-weight: $font-weight-bold;
      margin-bottom: 1rem;
    }
  }

  .footer {
    .confirm {
      background-color: #2D1757;
      padding: 0.6rem 1rem;

      &:hover {
        background-color: #2D1757;
        filter: brightness(125%);
      }
    }

    padding: 1rem 2em 1rem 1.5rem;
  }

  .loading-blocks-overlay {
    height: 2rem;
  }
}
</style>
