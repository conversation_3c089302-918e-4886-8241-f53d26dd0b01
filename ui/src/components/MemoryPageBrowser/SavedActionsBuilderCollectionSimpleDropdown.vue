<template>
  <section class="saved-actions-builder-collection-simple-dropdown">
    <span v-for="(item, i) in dataList"
          :key="i"
          class="collection-item"
          :class="{ active: selectedCollection === item }"
          @click="onSelect(item)"
    >
      {{ item.label }}
    </span>
  </section>
</template>

<script>

import { mapState } from 'vuex';

export default {
  name: 'saved-actions-builder-collection-simple-dropdown',

  props: {
    selectedCollection: {
      type: Object,
      default: null,
    },
    excludedDefault: {
      type: Boolean,
      default: false,
      required: false,
    },
  },

  computed: {
    ...mapState('savedActions', ['savedActionLists']),

    dataList() {
      if (this.savedActionLists) {
        const list = this.savedActionLists.sort((a, b) => a.label.toLowerCase().localeCompare(b.label.toLowerCase()));
        if (this.excludedDefault) {
          return list.filter(t => !t.default);
        }
        return list;
      }
      return [];
    },
  },

  methods: {
    onSelect(item) {
      this.$emit('onSelect', item);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.saved-actions-builder-collection-simple-dropdown {
  @include flex("block", "column", "start", "start");
  @include scrollbar-thin;
  background: #ffffff;
  border: none;
  border-radius: $border-radius-medium;
  box-shadow: 0 0 3px 3px rgba(0, 0, 0, 0.25);
  max-width: 500px;
  max-height: 200px;
  min-width: 220px;
  overflow-y: auto;

  .collection-item {
    @include truncate;
    cursor: pointer;
    font-size: $font-size-xs;
    min-height: 2rem;
    padding: 0.5rem 1rem;
    width: 100%;

    &.active {
      color: clr("purple");
    }

    &:hover {
      background-color: rgba(clr('black'), 0.05);
    }
  }
}
</style>
