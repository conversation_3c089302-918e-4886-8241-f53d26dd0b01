<template>
  <section class="memory-page-browser-list">
    <section class="collection" v-if="!showCreateActionsPage" :class="{'split-screen': showActionBuilderPanelLeft}">
      <section class="collection-dropdown" :class="{'split-screen': showActionBuilderPanelLeft}">
        <saved-actions-builder-collection-dropdown v-if="savedActions.length > 0 || savedActionLists.length > 0" />
        <section class="collection-action" v-if="selectedListHasChanges">
          <base-button class="save" colour="dark" size="small" @click="onSave">
            <i class="fa-regular fa-floppy-disk icon"></i>
            Save
          </base-button>
          <base-button class="clear" colour="light" size="small" @click="onClearSelected">
            <i class="fa-solid fa-rotate-left icon"></i>
            Reset
          </base-button>
        </section>
      </section>
      <section class="collection-action">
        <memory-page-browser-action-filters :do-fetch-saved-actions="doFetchSavedActions" />
        <section class="page-size">
          <memory-page-browser-pagination-size />
        </section>
      </section>
    </section>
    <section class="body">
      <loading-blocks-overlay v-if="loading" />
      <section v-else>
        <memory-page-browser-empty @clear-filters="doFetchSavedActions" @clear-search="doFetchSavedActions" v-if="!hasResult" />
        <memory-theme-list v-if="hasResult" />
        <memory-page-browser-pagination v-if="hasResult" />
      </section>
    </section>
  </section>
</template>

<script>
import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import DatasetsMemoriesSearch from '@/components/MemoryPageBrowser/DatasetsMemoriesSearch';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import MemoryPageBrowserActionFilters from '@/components/MemoryPageBrowser/MemoryPageBrowserActionFilters';
import MemoryPageBrowserEmpty from '@/components/MemoryPageBrowser/MemoryPageBrowserEmpty';
import MemoryPageBrowserPagination from '@/components/MemoryPageBrowser/MemoryPageBrowserPagination';
import MemoryPageBrowserPaginationSize from '@/components/MemoryPageBrowser/MemoryPageBrowserPaginationSize';
import MemoryPageUpdateCollectionToast from '@/components/MemoryPageBrowser/Toast/MemoryPageUpdateCollectionToast';
import MemoryThemeList from '@/components/MemoryPageBrowser/MemoryTheme/MemoryThemeList';
import SavedActionsBuilderCollectionDropdown from '@/components/MemoryPageBrowser/SavedActionsBuilderCollectionDropdown';
import SavedActionSort from '@/enum/saved-action-sort';
import SavedActionsType from '@/enum/saved-actions-type';

import { mapActions, mapGetters, mapState } from 'vuex';
import { savedActionApi } from '@/services/api';
import { savedActionsRequest } from '@/services/request';

export default {
  name: 'memory-page-browser-list',

  components: {
    BaseButton,
    BaseInput,
    DatasetsMemoriesSearch,
    LoadingBlocksOverlay,
    MemoryPageBrowserActionFilters,
    MemoryPageBrowserEmpty,
    MemoryPageBrowserPagination,
    MemoryPageBrowserPaginationSize,
    MemoryThemeList,
    SavedActionsBuilderCollectionDropdown,
  },

  data() {
    return {
      creatingNew: false,
      loading: false,
      newLabel: '',
      SavedActionsType,
      SavedActionSort,
      saving: false,
      actionType: {
        title: '',
      },
    };
  },

  computed: {
    ...mapState('savedActions', [
      'actionsToUpload',
      'excludedTypes',
      'savedActionLists',
      'savedActions',
      'search',
      'selectedList',
      'sort',
      'sortDirection',
    ]),

    ...mapGetters('savedActions', ['selectedListHasChanges']),

    ...mapState('savedActionsBuilder', ['showActionBuilderPanelLeft']),

    ...mapState('user', ['activeWorkspace']),

    hasFilters() {
      return this.excludedTypes?.length > 0;
    },

    hasResult() {
      return this.savedActions.length;
    },

    hasSearch() {
      return this.search?.trim().length > 0;
    },

    showCreateActionsPage() {
      return !this.hasResult && !this.hasFilters && !this.hasSearch;
    },
  },

  methods: {
    ...mapActions('savedActions', [
      'addSavedActionsToUpload',
      'removeSavedActionsFromUpload',
      'selectSavedAction',
      'setSavedActionLists',
      'setSavedActionsToUpload',
      'setSelectedList',
      'toggleExcludedType',
    ]),

    ...mapActions('toast', {
      addToast: 'add',
    }),

    async doFetchSavedActions() {
      this.loading = true;
      await savedActionsRequest.fetchSavedActions();
      this.loading = false;
    },

    onClearSelected() {
      this.setSavedActionsToUpload({ ids: this.selectedList.list });
    },

    async onSave() {
      this.saving = true;

      const updated = { ...this.selectedList, list: this.actionsToUpload };

      await savedActionApi.updateSavedActionList(this.activeWorkspace?.id, updated);

      await savedActionsRequest.fetchSavedActionLists()
        .then(savedActionLists => {
          this.setSavedActionLists({ savedActionLists });
          this.setSelectedList({ list: savedActionLists.find(list => list.id === this.selectedList.id) });
        });

      this.setSavedActionsToUpload({ ids: this.selectedList.list });

      this.saving = false;

      this.addToast({ toast: { component: MemoryPageUpdateCollectionToast, id: 'memory-page-update-collection-toast' } });
    },
  },

  watch: {
    savedActionLists() {
      if (this.selectedList) {
        this.setSelectedList({ list: this.savedActionLists.find(t => t.id === this.selectedList.id) });
        this.setSavedActionsToUpload({ ids: this.selectedList.list });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.memory-page-browser-list {
  @include flex("block", "column", "start", "stretch");
  width: 100%;

  .body {

    margin-top: 0.5rem;

    .loading-blocks-overlay {
      margin-top: 8rem;
    }
  }

  .collection {
    @include flex("block", "row", "space-between", "stretch");

    &.split-screen {
      @include flex("block", "column", "space-between", "stretch");
      margin-top: 1.5rem;
    }

    .collection-dropdown {
      @include flex("block", "row", "start", "stretch");

      height: 2.1rem;
      margin-bottom: 1rem;

      .collection-action {
        @include flex("block", "row", "start", "start");

        text-transform: uppercase;

        .save {
          background: #5F52C5;
          height: 100%;
          margin-left: 1rem;
        }

        .clear {
          background: #FFFFFF;
          border: 1px solid rgba(255, 65, 65, 0.29);
          color: #FF4141;
          height: 100%;
          margin-left: 1rem;
        }

        .icon {
          margin-right: 0.5rem;
        }
      }

      &.split-screen {
        .collection-action .base-button {
          margin-left: 6px;
        }
      }
    }

    .collection-action {
      @include flex("block", "row", "start", "start");

      height: 2.1rem;

      .page-size {
        color: #5F52C5;
        font-size: 0.75rem;
        height: 100%;
        margin-left: 1rem;
        text-transform: uppercase;
      }
    }
  }
}
</style>
