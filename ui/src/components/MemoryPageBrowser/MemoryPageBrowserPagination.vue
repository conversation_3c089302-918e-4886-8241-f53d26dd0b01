<template>
  <section class="memory-page-browser-pagination" :class="{ hasSelected }">
    <span>Page:</span>
    <section
        v-for="(item, index) in pages"
        :key="index"
        class="paging-item"
        :class="item.class"
        @click="onClick(item.page)">
      <span>{{ item.page }}</span>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import PaginationHelper from '@/helpers/pagination-helper';
import { savedActionsRequest } from '@/services/request';

const paginationHelper = new PaginationHelper();

export default {
  name: 'memory-page-browser-pagination',

  data() {
    return {
    };
  },

  computed: {
    ...mapState('savedActions', {
      actionsToUpload: state => state.actionsToUpload,
      currentPage: state => state.page,
      limit: state => state.limit,
      total: state => state.total,
    }),

    hasSelected() {
      return this.actionsToUpload?.length > 0;
    },

    pages() {
      const { page, pages } = paginationHelper.getPagesWithMinPage(
        this.currentPage,
        this.total,
        this.limit,
        10,
      );
      if (page !== this.currentPage) {
        this.setPage({ page });
      }

      return pages.map(d => {
        return {
          page: d,
          class: this.getClass(d),
        };
      });
    },
  },

  methods: {
    ...mapActions('savedActions', ['setPage']),

    getClass(item) {
      if (typeof item === 'number') {
        if (item === this.currentPage) {
          return 'current-page';
        }
        return 'clickable';
      }
      return 'non-clickable';
    },

    async onClick(page) {
      if (typeof page === 'number' && page !== this.currentPage) {
        this.setPage({ page });
        await savedActionsRequest.fetchSavedActions();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';
@import '~font-awesome/css/font-awesome.min.css';

.memory-page-browser-pagination {
  @include flex('block', 'row', 'start', 'center');
  @include rigid;

  bottom: 0;
  color: $dataimport;
  font-size: $font-size-xs;
  font-weight: $font-weight-bold;
  height: $header_height;
  padding: 1rem 0 1rem 1.5rem;
  position: sticky;
  text-transform: capitalize;
  width: 100%;

  &.hasSelected {
    margin-bottom: 50px; // for the memory-page action-bar at bottom
  }

  .paging-item {
    margin-left: 0.5rem;

    &.clickable {
      cursor: pointer;
    }

    &.clickable:hover {
      color: clr("purple");
      font-size: $font-size-xs;
    }

    &.current-page {
      color: clr("purple");
      font-size: $font-size-xs;
      text-decoration: underline;
    }
  }
}
</style>
