<template>
  <section class="memory-page-browser-empty">
    <section class="body">
      <i v-if="!hasSearch && !hasFilters" class="icon fa-light fa-floppy-disk"></i>
      <h2 v-if="!hasSearch && !hasFilters">Create Memory Actions</h2>

      <section v-if="hasSearch" class="content search">
        <p>There are no memory actions matching this search query - <span class="clear-btn" @click="onClearSearch">Clear Search</span></p>
      </section>
      <section v-else-if="hasFilters" class="content filters">
        <p>There are no memory actions to display for these filters - <span class="clear-btn" @click="onClearFilters">Clear Filters</span></p>
      </section>
      <section v-else class="content new">
        <p>
          Create actions that you can apply to datasets, which will automatically create, merge, rename, or delete themes. These can be applied while uploading datasets, or after the dataset has been analysed.
        </p>
      </section>

      <base-button v-if="!hasSearch && !hasFilters" class="create" icon="plus" @click="onClickCreateAction" :disabled="showActionBuilderPanelLeft">Create Action</base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';

export default {
  name: 'memory-page-browser-empty',

  components: {
    BaseButton,
  },

  computed: {
    ...mapState('savedActions', ['excludedTypes', 'search']),

    ...mapState('savedActionsBuilder', ['showActionBuilderPanelLeft']),

    hasFilters() {
      return this.excludedTypes?.length > 0;
    },

    hasSearch() {
      return this.search?.trim().length > 0;
    },
  },

  methods: {
    ...mapActions('savedActions', [
      'resetExcludedTypes',
      'setPage',
      'setSearch',
    ]),

    ...mapActions('savedActionsBuilder', ['setShowActionBuilderPanelLeft']),

    ...mapActions('datasetManagementTabs', ['setViewingMemoryPageTab']),

    onClickCreateAction() {
      this.setShowActionBuilderPanelLeft({ value: true });
      this.setViewingMemoryPageTab();
    },

    onClearFilters() {
      this.resetExcludedTypes();
      this.$emit('clear-filters');
    },

    onClearSearch() {
      this.setSearch({ term: '' });
      this.setPage({ page: 1 });
      this.$emit('clear-search');
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.memory-page-browser-empty {
  @include flex("block", "column", "start", "stretch");

  margin-top: 3rem;
  width: 100%;

  .body {
    @include flex("block", "column", "start", "center");
    @include stretch;

    .icon {
      font-weight: 300;
      font-size: 39px;
    }

    h2 {
      margin-top: 2rem;
      font-weight: $font-weight-bold;
    }

    .base-button {
      margin-top: 1rem;
      padding: 0.7rem 1.1rem 0.7rem 0.7rem;

      &.create {
        background-color: clr('purple', 'rich');
        text-transform: uppercase;
        font-weight: 800;
        font-size: 10px;
        text-align: center;;
      }
    }

    .content {
      @include flex("block", "column", "center", "center");

      p {
        font-size: $font-size-sm;
        margin-top: 1rem;
        text-align: center;

        .clear-btn {
          color: clr('purple', 'rich');
          cursor: pointer;
          font-weight: $font-weight-medium;
          text-decoration: underline;
        }
      }

      &.search, &.filters {
        margin-bottom: 4rem;
        margin-top: 4rem;
      }

      &.new {
        p {
          line-height: 22px;
          max-width: 600px;
        }
      }
    }
  }
}
</style>
