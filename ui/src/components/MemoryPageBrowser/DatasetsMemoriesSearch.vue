<template>
  <section class="datasets-memories-search">
    <section class="container" :class="{ active }">
      <search-icon class="icon search-icon" />

      <input
        v-model="value"
        placeholder="Type to search Actions..."
        @focus="onInputFocus"
        @blur="onInputBlur"
      />

      <x-icon class="icon x-icon"
              v-if="search && search.trim().length"
              @click="clearSearch"
      />
    </section>
  </section>
</template>

<script>
import { debounce } from 'lodash-es';
import { SearchIcon, XIcon } from 'vue-feather-icons';
import { mapActions, mapState } from 'vuex';
import savedActionsRequest from '@/services/request/SavedActionsRequest';

export default {
  name: 'datasets-memories-search',

  components: {
    SearchIcon,
    XIcon,
  },

  data() {
    return {
      active: false,
    };
  },

  computed: {
    ...mapState('savedActions', ['search']),

    value: {
      get() {
        return this.search;
      },
      set(term) {
        this.debounceSetSearch(term);
      },
    },
  },

  methods: {
    ...mapActions('savedActions', ['setPage', 'setSearch']),

    async clearSearch() {
      await this.doSearch('');
    },

    debounceSetSearch: debounce(async function debounceSetSearch(term) {
      await this.doSearch(term);
    }, 500),

    onInputBlur() {
      this.active = false;
    },

    onInputFocus() {
      this.active = true;
    },

    async doSearch(term) {
      this.setSearch({ term });
      this.setPage({ page: 1 });

      await savedActionsRequest.fetchSavedActions();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.datasets-memories-search {
  @include flex("block", "row", "start", "center");
  width: 100%;
  .container {
    @include flex("block", "row", "start", "center");

    background-color: clr("white");
    border: 1px solid #7362B7;
    border-radius: $border-radius-small;
    margin: 1px;
    padding: 0.4rem 0.5rem;
    width: 100%;

    &.active {
      border: 2px solid clr("purple");
      margin: 0;

      .search-icon {
        color: clr("purple");
      }
    }

    .icon {
      color: $body-copy-light;
      height: $font-size-lg;

      &.search-icon {
        margin-right: 0.5rem;
      }

      &.x-icon {
        cursor: pointer;
        margin-left: 0.5rem;

        &:hover {
          color: clr("red");
        }
      }
    }

    input {
      border: none;
      outline: none;
      width: 100%;

      &:active,
      &:focus {
        border: none;
        outline: none;
      }

      &::placeholder {
        font-size: $font-size-sm;
      }
    }
  }
}
</style>
