<template>
  <section class="saved-action-deleted-toast">
    <section class="toast">
      <section class="left">
        <check-icon class="icon"/>
        <span class="text">Action successfully deleted.</span>
      </section>
      <section class="right">
        <base-button class="dismiss" colour="light" @click="onDismiss">Dismiss</base-button>
      </section>
    </section>
  </section>
</template>

<script>
import { CheckIcon } from 'vue-feather-icons';
import { mapActions } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import Toast from '@/components/Mixins/Toast';

export default {
  name: 'saved-action-deleted-toast',

  components: {
    BaseButton,
    CheckIcon,
  },

  mixins: [Toast],

  beforeDestroy() {
    this.remove({ id: 'saved-action-deleted-toast' });
  },

  created() {
    this.countWatcher = setInterval(() => {
      this.count -= 1;
      if (this.count === 0) {
        clearInterval(this.countWatcher);
        this.close();
      }
    }, 1000);
  },

  data() {
    return {
      count: 3,
      countWatcher: null,
    };
  },

  methods: {
    ...mapActions('toast', ['remove']),

    onDismiss() {
      this.remove({ id: 'saved-action-deleted-toast' });
      this.close();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins/index";

.saved-action-deleted-toast {
  @include flex("block", "row", "end", "start");

  .toast {
    @include toast;

    border-radius: $border-radius-medium;
    padding: 1rem 1.5rem;

    .base-button {
      padding: 0.6rem 1.6rem;
    }

    .left {
      @include flex("block", "row", "start", "center");

      margin-right: 4rem;

      span {
        font-weight: $font-weight-normal;
        margin-left: 0.3rem;
      }
    }
  }
}
</style>
