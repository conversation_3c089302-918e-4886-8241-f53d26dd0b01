<template>
  <section class="saved-action-builder-finish-adding-themes-toast">
    <section class="toast-content">
      <section class="left">
        <i class="fa-solid fa-floppy-disk icon" />
        <section class="text">
          Added New Action to {{selectedList.label}}.
        </section>
      </section>
      <section class="right">
        <base-button class="dismiss" colour="light" @click="onDismiss">Dismiss</base-button>
      </section>
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import Toast from '@/components/Mixins/Toast';

export default {
  name: 'saved-action-builder-finish-adding-themes-toast',

  components: {
    BaseButton,
  },

  mixins: [Toast],

  computed: {
    ...mapState('savedActions', ['selectedList']),

  },

  created() {
    setTimeout(this.close, 3000);
  },

  methods: {
    onDismiss() {
      this.close();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.saved-action-builder-finish-adding-themes-toast {
  @include flex("block", "row", "end", "start");

  .toast-content {
    @include toast;

    .left {
      @include flex("block", "row", "start", "center");

      .icon {
        margin-right: 0.5rem;
      }

      .text {
        @include truncate;
        display: inline-block;
        max-width: 450px;
      }
    }

    .right {
      margin-left: 2rem;
    }
  }
}
</style>
