<template>
  <section class="memory-page-update-collection-toast">
    <section class="toast-content">
      <section class="left">
        <i class="fa-solid fa-plus icon" />
        <section class="text">
          Collection Updated.
        </section>
      </section>
      <section class="right">
        <base-button class="dismiss" colour="light" @click="onDismiss">Dismiss</base-button>
      </section>
    </section>
  </section>
</template>

<script>
import BaseButton from '@/components/Base/BaseButton';
import Toast from '@/components/Mixins/Toast';

export default {
  name: 'memory-page-update-collection-toast',

  components: {
    BaseButton,
  },

  mixins: [Toast],

  computed: {

  },

  created() {
    setTimeout(this.close, 3000);
  },

  methods: {
    onDismiss() {
      this.close();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.memory-page-update-collection-toast {
  @include flex("block", "row", "end", "start");

  .toast-content {
    @include toast;

    .left {
      @include flex("block", "row", "start", "center");

      .icon {
        margin-right: 0.5rem;
      }

      .text {
        @include truncate;
        display: inline-block;
        max-width: 450px;
      }
    }

    .right {
      margin-left: 2rem;
    }
  }
}
</style>
