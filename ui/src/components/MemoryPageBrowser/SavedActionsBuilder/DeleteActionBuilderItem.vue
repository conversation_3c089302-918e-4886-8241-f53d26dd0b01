<template>
  <section class="delete-action-builder-item">
    <section class="theme-label">
      <span class="label">
        DELETED THEME LABEL
        <span v-show="localItem.invalidLabel || localItem.duplicatedLabel"> *</span>
      </span>
      <section class="input" :class="{ invalid: localItem.invalidLabel || localItem.duplicatedLabel }">
        <input ref="labelInput" v-model="inputLabel"/>
      </section>
    </section>
    <section class="close" :class="{ hide: !isRemovable }">
      <span class="label">&nbsp;</span>
      <i class="fa-regular fa-times icon" @click="onClickRemove" />
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';
import { v4 as uuid } from 'uuid';
import SavedActionsType from '@/enum/saved-actions-type';
import bus from '@/helpers/bus';

export default {
  name: 'delete-action-builder-item',

  components: {

  },

  props: {
    id: {
      type: [Number, String],
      required: true,
    },
    index: {
      type: Number,
      required: true,
    },
    item: {
      type: Object,
      required: false,
    },
  },

  computed: {
    ...mapState('savedActionsBuilder', [
      'buildingActionList',
      'validating',
      'validatingCount',
    ]),

    ...mapGetters('savedActionsBuilder', [
      'buildingActionsByType',
      'getBuildingAction',
      'isLabelValid',
      'validateBuildingItem',
    ]),

    inputLabel: {
      get() {
        return this.localItem.label || '';
      },
      set(val) {
        this.doSetLabel(val);
      },
    },

    isRemovable() {
      if (this.buildingActionsByType.length > 1) {
        return true;
      }
      return !this.localItem.brandNew;
    },
  },

  beforeDestroy() {
    bus.$off('theme-builder-remove-invalid-theme-parent', this.doRemoveInvalid);
  },

  created() {
    let actionItem = this.getBuildingAction(this.id);
    if (this.item) {
      actionItem = this.item;
    }

    this.localItem = {
      ...actionItem,
      label: actionItem.label || '',
      type: SavedActionsType.DELETE_THEME.name,
    };

    bus.$on('theme-builder-remove-invalid-theme-parent', this.doRemoveInvalid);
  },

  data() {
    return {
      focusedInput: null,
      localItem: null,
    };
  },

  methods: {
    ...mapActions('savedActionsBuilder', [
      'addBuildingAction',
      'removeBuildingAction',
      'setValidatingCount',
      'updateBuildingAction',
    ]),

    doSetLabel(val) {
      const { invalidLabel, duplicatedLabel } = this.isLabelValid(val, this.id);
      this.localItem = {
        ...this.localItem,
        brandNew: false,
        label: val,
        duplicatedLabel,
        invalidLabel,
      };
      this.onEditAction({ item: this.localItem });
    },

    onClickRemove() {
      if (this.isRemovable) {
        this.removeBuildingAction({ id: this.id });
      }
      if (!this.buildingActionsByType.length) {
        this.addBuildingAction({
          action: {
            brandNew: true,
            id: uuid(),
            type: SavedActionsType.DELETE_THEME.name,
          },
        });
      }
    },

    doRemoveInvalid(val) {
      if (this.id === val) {
        this.onClickRemove();
      }
    },

    onEditAction({ item }) {
      this.updateBuildingAction({
        id: this.id,
        action: { ...item },
      });
      this.$emit('updateItem');
    },

    validateDeleteTheme() {
      const validatedItem = this.validateBuildingItem(this.id);
      if (validatedItem !== null) {
        this.localItem = { ...validatedItem };
        this.onEditAction({ item: this.localItem });
      }
      this.setValidatingCount({ value: Number(this.validatingCount + 1) });
    },
  },

  watch: {
    validating() {
      if (this.validating) {
        this.validateDeleteTheme();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.delete-action-builder-item {
  @include flex("block", "row", "start", "center");
  @include stretch;
  background: rgba(97, 87, 192, 0.1);
  border-radius: 3px;
  line-height: 1.5rem;
  margin-bottom: 1rem;
  padding: 12px 1rem 20px 1rem;

  &:hover {
    .close {
      &:not(.hide) .icon {
        cursor: pointer;
        opacity: 0.5;
      }
    }
  }

  .theme-label {
    @include flex("block", "column", "center", "start");
    width: 100%;

    .label {
      color: rgba(97, 87, 192, 1);
      font-size: 10px;
      font-weight: 800;
      text-transform: uppercase;
    }

    .input {
      @include flex("block", "row", "start", "center");
      background: #FFFFFF;
      border: 1px solid rgba(97, 87, 192, 1);
      border-radius: 2px;
      height: 30px;
      padding: 0.2rem 0.5rem;
      width: inherit;

      &.invalid {
        border: 1px solid clr("red");
      }

      input {
        border: none;
        color: rgba(20, 20, 20, 1);
        font-size: 12px;
        width: 100%;

        &:focus, &:active {
          outline: none;
        }
      }
    }
  }

  .close {
    @include flex("block", "column", "center", "start");
    margin-left: 0.5rem;

    .label {
      opacity: 0;
    }

    .icon {
      opacity: 0;
    }
  }
}

</style>
