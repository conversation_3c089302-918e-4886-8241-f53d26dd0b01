<template>
  <section class="saved-actions-builder-panel-left">
    <section class="top">
      <section class="left">
        <span class="title" v-if="editMode">
          <span class="strong">Editing Action: </span>
          <span class="label"> {{editAction.label}} </span>
        </span>
        <span class="title" v-else-if="selectedList != null">
          <span class="strong">Create New Actions </span>
          <span class="label"> {{selectedList.label}} </span>
        </span>
        <span class="title" v-else>
          <span class="strong">Create New Actions</span>
        </span>
      </section>
      <section class="right" @click="onClickClose">
        <i class="fa-regular fa-times icon" />
      </section>
    </section>
    <section class="body">
      <section class="actions-toolbar">
        <section v-for="(action, index) in actionTypes"
          v-if="displayTab(action.type)"
          class="action"
          :data="action"
          :key="index"
          @click="setActionType(action.type)"
          :class="[action.color,{ active : buildingType == action.type.name}]"
        >
          <i :class="action.icon" class="fa-solid"> </i>
          <span>{{action.title}}</span>
        </section>
      </section>

      <search-action-builder-item v-for="(id, index) in dataList"
        :id="id"
        :index="index"
        :key="id"
        :edit-mode="editMode"
        @updateItem="onUpdateItem"
        v-if="buildingType === SavedActionsType.CREATE_SEARCH_THEME.name"
      />

      <rename-action-builder-item v-for="(id, index) in dataList"
        :id="id"
        :index="index"
        :key="id"
        @updateItem="onUpdateItem"
        v-if="buildingType === SavedActionsType.RENAME_THEME.name"
      />

      <delete-action-builder-item v-for="(id, index) in dataList"
        :id="id"
        :index="index"
        :key="id"
        @updateItem="onUpdateItem"
        v-if="buildingType === SavedActionsType.DELETE_THEME.name"
      />

      <merge-action-builder-item v-for="(id, index) in dataList"
        :id="id"
        :index="index"
        :key="id"
        @updateItem="onUpdateItem"
        v-if="buildingType === SavedActionsType.MERGE_THEMES.name"
      />
    </section>
    <section class="bottom">
      <section class="left">
        <section class="btn add-new-theme-btn"
                 @click="onClickAddBuildingAction"
                 v-show="!editMode"
        >
          <i class="fa-solid fa-plus icon" />
          Add new action
        </section>
        <section v-show="editMode"
                 class="btn cancel-btn"
                 @click="onClickCancel"
        >
          Cancel
        </section>
      </section>
      <section class="right">
        <section v-if="showErrorMsg" class="error">
          <span class="text" v-if="buildForSearchOnly">
            {{ textErrorMsg }} <span v-if="textRemoveBtn" class="remove-btn" @click="removeInvalidItems">{{ textRemoveBtn }}</span>
          </span>
          <span class="text" v-else>
            {{ textErrorMsg }}<br v-if="textRemoveBtn"/><span v-if="textRemoveBtn" class="remove-btn" @click="removeInvalidItems">{{ textRemoveBtn }}</span>
          </span>
        </section>
        <loading-blocks-overlay v-if="proceeding" />
        <section v-if="!proceeding && !editMode"
                 class="btn add-themes-to-dataset-btn"
                 @click="onClickAddThemesToDataset"
        >
          <i class="fa-solid fa-floppy-disk icon" />
              Save action to memory
        </section>
        <section v-if="!proceeding && editMode"
                 class="btn add-themes-to-dataset-btn"
                 @click="onClickAddThemesToDataset"
        >
          <i class="fa-solid fa-floppy-disk icon" />
              Update action
        </section>
      </section>
    </section>
  </section>
</template>

<script>
import { v4 as uuid } from 'uuid';
import { mapActions, mapGetters, mapState } from 'vuex';

import bus from '@/helpers/bus';
import DeleteActionBuilderItem from '@/components/MemoryPageBrowser/SavedActionsBuilder/DeleteActionBuilderItem';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import MergeActionBuilderItem from '@/components/MemoryPageBrowser/SavedActionsBuilder/MergeActionBuilderItem';
import RenameActionBuilderItem from '@/components/MemoryPageBrowser/SavedActionsBuilder/RenameActionBuilderItem';
import SavedActionAdaptor from '@/helpers/saved-action-adaptor';
import SavedActionBuilderFinishAddingThemesToast from '@/components/MemoryPageBrowser/Toast/SavedActionBuilderFinishAddingThemesToast';
import SavedActionBuilderFinishUpdatingThemesToast from '@/components/MemoryPageBrowser/Toast/SavedActionBuilderFinishUpdatingThemesToast';
import SavedActionBuilderNoCollectionSavingModal from '@/components/MemoryPageBrowser/Modal/SavedActionBuilderNoCollectionSavingModal';
import SavedActionsType from '@/enum/saved-actions-type';
import SearchActionBuilderItem from '@/components/MemoryPageBrowser/SavedActionsBuilder/SearchAction/SearchActionBuilderItem';
import ThemesBuilderNotSavingMemoryModal from '@/components/ThemesBuilder/ThemesBuilderNotSavingMemoryModal';

import { savedActionsRequest } from '@/services/request';

const savedActionAdaptor = new SavedActionAdaptor();

export default {
  name: 'saved-actions-builder-panel-left',

  components: {
    DeleteActionBuilderItem,
    LoadingBlocksOverlay,
    MergeActionBuilderItem,
    RenameActionBuilderItem,
    SearchActionBuilderItem,
    ThemesBuilderNotSavingMemoryModal,
  },

  computed: {
    ...mapGetters('savedActionsBuilder', [
      'actionsCreateSearchTheme',
      'actionsDeleteTheme',
      'actionsMergeThemes',
      'actionsRenameTheme',
      'getBuildingAction',
      'buildingActionsByType',
    ]),

    ...mapState('datasets', {
      datasetId: state => state.active,
    }),

    ...mapState('savedActions', [
      'selectedList',
      'selected',
      'savedActions',
    ]),

    ...mapState('savedActionsBuilder', [
      'buildForSearchOnly',
      'buildingActionList',
      'buildingType',
      'showThemeBuilderPanelLeft',
      'validating',
      'validatingCount',
    ]),

    ...mapState('savedActionsBuilder', {
      forceProceed: 'proceeding',
    }),

    actionTypes() {
      return SavedActionsType.enumValues.map(value => {
        return {
          icon: value.icon(),
          color: value.lowerCase(),
          title: value.upperCase(),
          type: value,
        };
      });
    },

    dataList() {
      return this.buildingActionsByType.map(t => t.id);
    },

    editMode() {
      return this.selected != null;
    },

    textErrorMsg() {
      if (this.hasErrorEmptyField) {
        return 'Please complete highlighted* fields or';
      }
      if (this.hasErrorDuplicatedLabel) {
        return 'There are duplicate Labels - please modify or';
      }
      return 'Please complete a Theme before submitting.';
    },

    textRemoveBtn() {
      if (this.hasErrorEmptyField) {
        return 'remove incomplete entries.';
      }
      if (this.hasErrorDuplicatedLabel) {
        return 'remove the duplicates.';
      }
      return null;
    },
  },

  beforeDestroy() {
    this.resetBuilder();
  },

  created() {
    this.resetBuildingActions();

    if (this.editMode) {
      this.editAction = savedActionAdaptor.adaptSavedAction(this.selected);

      if (this.selected.childActions) {
        this.editAction.subtopics = this.selected.childActions
          .map(child => savedActionAdaptor.adaptSavedAction(child));
      }

      this.setBuildingType({ type: this.selected.type });
      this.addBuildingAction({
        action: {
          ...this.editAction,
          brandNew: false,
        },
      });
    } else if (!this.buildingActionsByType.length) {
      this.onClickAddBuildingAction();
    }
  },

  data() {
    return {
      hasErrorEmptyField: false,
      hasErrorDuplicatedLabel: false,
      invalidList: [],
      proceeding: false,
      showErrorMsg: false,
      validList: [],
      SavedActionsType,
      editAction: null,
    };
  },

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    ...mapActions('savedActions', [
      'selectSavedAction',
      'setSavedActionLists',
      'setSavedActions',
      'setSelectedList',
      'setSavedActionsToUpload',
    ]),

    ...mapActions('savedActionsBuilder', [
      'addBuildingAction',
      'resetBuildingActions',
      'setBuildingType',
      'setProceeding',
      'setShowActionBuilderPanelLeft',
      'setValidating',
      'setValidatingCount',
    ]),

    ...mapActions('toast', { addToast: 'add' }),

    ...mapActions('savedActionsBuilder', { resetBuilder: 'reset' }),

    displayTab(type) {
      if (this.editMode) {
        return this.selected.type === type.name;
      }
      return true;
    },

    isValidItem(item, toggleErrorFlag) {
      if (toggleErrorFlag && !this.hasErrorEmptyField) {
        this.hasErrorEmptyField = item.invalidLabel || item.invalidQuery || item.invalidOriginalLabel;
      }
      if (toggleErrorFlag && !this.hasErrorDuplicatedLabel) {
        this.hasErrorDuplicatedLabel = item.duplicatedLabel;
      }
      return !item.invalidLabel && !item.duplicatedLabel && !item.invalidQuery && !item.invalidOriginalLabel;
    },

    isValidSubtopicList(list) {
      if (!list?.length) {
        return true;
      }
      return list.every(t => this.isValidItem(t, true));
    },

    onClickAddBuildingAction() {
      this.addBuildingAction({ action: {
        id: uuid(),
        brandNew: true,
        type: this.buildingType,
      } });
    },

    onClickAddThemesToDataset() {
      if (this.proceeding) {
        return;
      }

      if (this.buildingActionsByType?.length && !this.validating) {
        this.setValidatingCount({ value: 0 });
        this.setValidating({ value: true });
      }
    },

    onClickClose() {
      this.selectSavedAction({ id: null });
      this.resetBuilder();
      this.setShowActionBuilderPanelLeft({ value: false });
    },

    onClickCancel() {
      this.onClickClose();
    },

    onUpdateItem() {
      // hide the errorMsg only
      // not show it back when editing item
      if (this.showErrorMsg) {
        this.separateTheResults();
      }
    },

    finishValidating() {
      this.separateTheResults();

      if (!this.validList.length && !this.invalidList.length) {
        this.showErrorMsg = true;
      } else if (this.validList.length && !this.invalidList.length) {
        if (!this.selectedList && !this.editMode) {
          this.setModalComponent({ component: SavedActionBuilderNoCollectionSavingModal });
        } else {
          this.proceedAddThemesToDataset();
        }
      }
    },

    async proceedAddThemesToDataset() {
      this.proceeding = true;
      let component;

      if (this.selected) {
        await savedActionsRequest.updateSavedActionsFromBuilder();
        component = SavedActionBuilderFinishUpdatingThemesToast;
      } else {
        await savedActionsRequest.createSavedActionsFromBuilder();
        component = SavedActionBuilderFinishAddingThemesToast;
      }

      setTimeout(async () => {
        await savedActionsRequest.fetchSavedActions();
      }, 1000);

      this.proceeding = false;
      this.onClickClose();

      setTimeout(async () => {
        await savedActionsRequest.fetchSavedActionLists()
          .then(savedActionLists => this.setSavedActionLists({ savedActionLists }));
      }, 1000);

      this.addToast({
        toast: {
          component,
          id: 'saved-action-builder-finish',
        },
      });
    },

    removeInvalidItems() {
      this.invalidList.forEach(t => {
        if (!this.isValidItem(t)) {
          this.removeInvalidParentThemes(t);
        } else {
          this.removeInvalidSubtopics(t);
        }
      });

      this.separateTheResults();
      if (!this.buildingActionsByType.length) {
        this.onClickAddBuildingAction();
      }
    },

    removeInvalidParentThemes(theme) {
      // make it 2 rounds - remove empty entries first - duplicate labels later
      if (this.hasErrorEmptyField && (theme.invalidLabel || theme.invalidQuery || theme.invalidOriginalLabel)) {
        setTimeout(() => {
          bus.$emit('theme-builder-remove-invalid-theme-parent', theme.id);
        }, 1);
      } else if (!this.hasErrorEmptyField && this.hasErrorDuplicatedLabel && theme.duplicatedLabel) {
        setTimeout(() => {
          bus.$emit('theme-builder-remove-invalid-theme-parent', theme.id);
        }, 1);
      }
    },

    removeInvalidSubtopics(theme) {
      const invalidSubtopicIds = theme?.subtopics
        .filter(s => {
          // make it 2 rounds - remove empty entries first - duplicate labels later
          if (this.hasErrorEmptyField) {
            return !s.brandNew && (s.invalidLabel || s.invalidQuery);
          }
          return this.hasErrorDuplicatedLabel && !s.brandNew && s.duplicatedLabel;
        })
        .map(s => s.id);
      invalidSubtopicIds.forEach(sId => {
        setTimeout(() => {
          bus.$emit('theme-builder-remove-invalid-subtopic', sId);
        }, 1);
      });
    },

    resetErrorFlag() {
      this.hasErrorEmptyField = false;
      this.hasErrorDuplicatedLabel = false;
    },

    separateTheResults() {
      this.invalidList = [];
      this.validList = [];
      this.resetErrorFlag();

      this.buildingActionsByType.forEach(t => {
        let valid = this.isValidItem(t, true);
        if (t.type === SavedActionsType.CREATE_SEARCH_THEME.name) {
          const subtopics = t.subtopics.filter(s => !s.brandNew);
          if (valid) {
            valid = this.isValidSubtopicList(subtopics);
          }
        }

        if (!valid) {
          this.invalidList.push(t);
        } else if (valid && !t.brandNew) {
          this.validList.push(t);
        }
      });

      this.showErrorMsg = this.invalidList.length > 0;
    },

    setActionType(type) {
      this.setBuildingType({ type: type.name });
      if (!this.dataList.length) {
        this.onClickAddBuildingAction();
      }
    },
  },

  watch: {
    forceProceed() {
      if (this.forceProceed) {
        this.proceedAddThemesToDataset();
        this.setProceeding({ proceeding: false });
      }
    },

    selected() {
      this.resetBuildingActions();

      if (this.editMode) {
        this.editAction = savedActionAdaptor.adaptSavedAction(this.selected);

        this.setBuildingType({ type: this.selected.type });

        this.addBuildingAction({
          action: {
            ...this.editAction,
            brandNew: false,
          },
        });
      }
    },

    validatingCount() {
      if (this.validatingCount === this.buildingActionsByType.length) {
        this.finishValidating();
        this.setValidatingCount({ value: 0 });
        this.setValidating({ value: false });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.saved-actions-builder-panel-left {
  @include stretch;

  background-color: #fcfcfc;
  border: none;
  display: grid;
  grid-area: saved-actions-panel-left;
  grid-template-rows: min-content minmax(0, min-content) min-content;
  min-height: 600px;
  min-width: 705px; //the builder will break when reach this width
  overflow: hidden;
  padding-left: 30px;
  padding-right: 30px;
  position: relative;

  .top {
    @include flex("block", "row", "between", "center");

    padding: 0.5rem 0 1rem;
    margin-top: 1rem;

    .left {
      @include flex("block", "row", "start", "center");

      .title {
        @include flex("block", "row", "start", "center");

        font-size: 16px;

        .strong {
          color: rgba(97, 87, 192, 1);
          font-weight: 700;
          min-width: fit-content;
        }

        .label {
          @include truncate;

          align-items: center;
          color: #6157C0;
          font-weight: 400;
          margin-left: 0.5rem;
        }
      }
    }

    .right {
      @include flex("block", "row", "center", "center");
      background-color: rgba(97, 87, 192, 0.1);
      border-radius: 50%;
      cursor: pointer;
      height: 24px;
      min-width: 24px;

      &:hover {
        background-color: rgba(97, 87, 192, 0.2);
      }
    }
  }

  .body {
    @include scrollbar-thin;
    overflow-x: hidden;
    overflow-y: auto;
    padding: 0;

    .actions-toolbar {
      @include flex("block", "row", "start", "stretch");

      color: #9A9A9A;
      font-size: 10px;
      font-weight: 800;
      margin-bottom: 1rem;
      text-align: center;
      text-transform: uppercase;

      .action {
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        cursor: pointer;
        padding: 0 0.5rem 0.5rem;

        &.active {
          &.deleted {
            color: #FF4141;
            border-bottom: 2px solid #FF4141;
          }

          &.searched {
            color: #5F52C5;
            border-bottom: 2px solid #5F52C5;
          }

          &.merged {
            color: #0CBB3D;
            border-bottom: 2px solid #0CBB3D;
          }

          &.renamed {
            color: #C552B3;
            border-bottom: 2px solid #C552B3;
          }
        }
      }
    }

    .search-action-builder-item {
      margin-bottom: 1rem;
    }
  }

  .bottom {
    @include flex("block", "row", "between", "center");
    padding: 1rem 0;

    .left {
      @include rigid;
      margin-right: 1rem;

      .add-new-theme-btn {
        border-radius: 2px;
        border: 1px solid rgba(45, 23, 87, 1);
        color: rgba(45, 23, 87, 1);
        padding: 0.5rem;

        .icon {
          margin-right: 0.2rem;
        }
      }

      .btn {
        cursor: pointer;
        font-size: 10px;
        font-weight: 800;
        text-transform: uppercase;
      }

      .cancel-btn {
        color: #141414;
        line-height: 12px;
        text-align: center;
        text-transform: uppercase;
      }
    }

    .right {
      @include flex("block", "row", "end", "center");
      width: 100%;

      .error {
        @include flex("block", "row", "center", "center");
        margin-right: 1rem;
        width: 100%;

        .text {
          color: rgba(45, 23, 87, 1);
          font-size: 12px;
          text-align: center;

          .remove-btn {
            color: clr("red");
            cursor: pointer;
            font-size: 12px;
            font-weight: $font-weight-bold;
            margin-left: 0.1rem;
            text-decoration: underline;
          }
        }
      }

      .loading-blocks-overlay {
        height: 1rem;
      }

      .add-themes-to-dataset-btn {
        @include rigid;

        background: rgba(97, 87, 192, 1);
        border-radius: 2px;
        color: #FFFFFF;
        cursor: pointer;
        font-size: 10px;
        font-weight: 800;
        padding: 0.5rem;
        text-transform: uppercase;

        .icon {
          margin-right: 0.2rem;
        }
      }
    }
  }
}
</style>
