<template>
  <section class="search-action-builder-item-theme-parent">
    <section class="theme-label">
      <span class="label">
        Theme Label
        <span v-show="localItem.invalidLabel || localItem.duplicatedLabel"> *</span>
      </span>
      <section class="input" :class="{ invalid: localItem.invalidLabel || localItem.duplicatedLabel }">
        <input ref="labelInput" v-model="inputLabel" @keydown.tab.prevent="onTabLabel" />
      </section>
    </section>
    <section class="query query-input-wrapper">
      <span class="label">
        Query
        <span v-show="localItem.invalidQuery"> *</span>
      </span>
      <section class="input query-input-wrapper"
               :class="{ 'has-dropdown': showQueryOption, invalid: localItem.invalidQuery }"
      >
        <i class="fa-regular fa-magnifying-glass icon" />
        <search-action-builder-query-input ref="queryInput"
                                    :id="id"
                                    :text="localItem.query"
                                    @inputQuery="doSetQuery"
                                    @openDropdown="onOpenOptionDropdown"
                                    @onTab="onTabQuery"
        />
      </section>
      <!-- Options Dropdown -->
      <themes-builder-query-option :distinct="localItem.distinctContent"
                                   :exact="localItem.exact"
                                   :open="showQueryOption"
                                   @clickOutside="onClickOutsideQueryOption"
                                   @selectOperator="onSelectOperator"
                                   @toggleExactSearch="onToggleExactSearch"
                                   @toggleRemoveDuplicates="onToggleRemoveDuplicates"
      />
    </section>
    <section class="close" :class="{ hide: !isRemovable }">
      <span class="label">&nbsp;</span>
      <i class="fa-regular fa-times icon" @click="onClickRemove" />
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';
import bus from '@/helpers/bus';
import ThemesBuilderQueryOption from '@/components/ThemesBuilderItem/ThemesBuilderQueryOption';
import SearchActionBuilderQueryInput from '@/components/MemoryPageBrowser/SavedActionsBuilder/SearchAction/SearchActionBuilderQueryInput';
import ThemesBuilderQueryInput from '@/components/ThemesBuilderItem/ThemesBuilderQueryInput';

export default {
  name: 'search-action-builder-item-theme-parent',

  components: {
    SearchActionBuilderQueryInput,
    ThemesBuilderQueryInput,
    ThemesBuilderQueryOption,
  },

  props: {
    focus: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [Number, String],
      required: true,
    },
  },

  computed: {
    ...mapGetters('savedActionsBuilder', [
      'getBuildingAction',
      'isLabelValid',
      'isQueryValid',
      'validateBuildingItem',
      'buildingActionsByType',
    ]),

    ...mapState('savedActionsBuilder', [
      'buildingActionList',
      'showQueryOptionDropdown',
      'validating',
      'validatingCount',
    ]),

    inputLabel: {
      get() {
        return this.localItem.label || '';
      },
      set(val) {
        this.doSetLabel(val);
      },
    },

    isQueryEmpty() {
      return !this.localItem.query?.trim().length;
    },

    isRemovable() {
      if (this.buildingActionsByType?.length > 1) {
        return true;
      }
      return !this.localItem.brandNew;
    },

    showQueryOption() {
      return this.showQueryOptionDropdown === this.id;
    },
  },

  beforeDestroy() {
    bus.$off('theme-builder-remove-invalid-theme-parent', this.doRemoveInvalid);
  },

  created() {
    bus.$on('theme-builder-remove-invalid-theme-parent', this.doRemoveInvalid);

    const item = this.getBuildingAction(this.id);
    this.localItem = {
      ...item,
      query: item.query || '',
    };
  },

  data() {
    return {
      localItem: null,
    };
  },

  mounted() {
    this.$refs.labelInput.focus();
  },

  methods: {
    ...mapActions('savedActionsBuilder', ['setPressedTab', 'setShowQueryOptionDropdown']),

    doRemoveInvalid(val) {
      if (this.id === val) {
        this.$emit('removeParentTheme');
      }
    },

    doSetLabel(val) {
      const { invalidLabel, duplicatedLabel } = this.isLabelValid(val, this.id);
      this.localItem = {
        ...this.localItem,
        brandNew: false,
        duplicatedLabel,
        invalidLabel,
        label: val,
      };
      this.$emit('editParentTheme', { item: this.localItem });
    },

    doSetQuery({ val }) {
      this.localItem = {
        ...this.localItem,
        brandNew: false,
        excludeContentIds: [],
        invalidQuery: !this.isQueryValid(val),
        query: val,
      };
      this.$emit('editParentTheme', { item: this.localItem });
    },

    onClickOutsideQueryOption() {
      if (this.showQueryOption) {
        this.setShowQueryOptionDropdown({ id: null });
      }
    },

    onClickRemove() {
      if (this.isRemovable) {
        this.$emit('removeParentTheme');
      }
    },

    onOpenOptionDropdown() {
      this.setShowQueryOptionDropdown({ id: this.id });
    },

    onSelectOperator(val) {
      bus.$emit(`select-operator-for-theme-builder-${this.id}`, val);
    },

    onTabLabel(e) {
      e.preventDefault();
      this.$refs.queryInput.onFocusEditor();
    },

    onTabQuery() {
      if (this.showQueryOption) {
        this.setShowQueryOptionDropdown({ id: null });
      }
    },

    onToggleExactSearch(val) {
      this.localItem = {
        ...this.localItem,
        brandNew: false,
        exact: val,
      };
      this.$emit('editParentTheme', { item: this.localItem });
    },

    onToggleRemoveDuplicates(val) {
      this.localItem = {
        ...this.localItem,
        brandNew: false,
        distinctContent: val,
      };
      this.$emit('editParentTheme', { item: this.localItem });
    },

    validateParentTheme() {
      const validatedItem = this.validateBuildingItem(this.id);
      if (validatedItem !== null) {
        this.localItem = { ...validatedItem };
        this.$emit('editParentTheme', { item: this.localItem });
      }
      this.$emit('finishValidating');
    },
  },

  watch: {
    focus() {
      this.$refs.labelInput.focus();
      this.setPressedTab({ tab: null });
    },

    validating() {
      if (this.validating) {
        this.validateParentTheme();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.search-action-builder-item-theme-parent {
  @include flex("block", "row", "start", "center");
  @include stretch;
  line-height: 1.5rem;

  &:hover {
    .close {
      &:not(.hide) .icon {
        cursor: pointer;
        opacity: 0.5;
      }
    }
  }

  .theme-label {
    @include flex("block", "column", "center", "start");
    width: 160px;

    .label {
      color: rgba(97, 87, 192, 1);
      font-size: 10px;
      font-weight: 800;
      text-transform: uppercase;
    }

    .input {
      @include flex("block", "row", "start", "center");
      background: #FFFFFF;
      border: 1px solid rgba(97, 87, 192, 1);
      border-radius: 2px;
      height: 30px;
      padding: 0.2rem 0.5rem;
      width: inherit;

      &.invalid {
        border: 1px solid clr("red");
      }

      input {
        border: none;
        color: rgba(20, 20, 20, 1);
        font-size: 12px;
        width: 100%;

        &:focus, &:active {
          outline: none;
        }
      }
    }
  }

  .query {
    @include flex("block", "column", "center", "start");
    @include stretch;
    margin-left: 0.5rem;
    position: relative;
    width: 100%;

    .label {
      color: rgba(97, 87, 192, 1);
      font-size: 10px;
      font-weight: 800;
      text-transform: uppercase;
    }

    .input {
      @include flex("block", "row", "start", "center");
      background: #FFFFFF;
      border: 1px solid rgba(97, 87, 192, 1);
      border-radius: 2px;
      padding-left: 0.5rem;
      width: inherit;

      &.has-dropdown {
        border-radius: 2px 2px 0 0;
      }

      &.invalid {
        border: 1px solid clr("red");
      }

      .icon {
        color: #2D1757;
        font-size: 14px;
        margin-right: 0.5rem;
      }

      .search-action-builder-query-input {
        height: 28px;
      }

    }

    .themes-builder-query-option {
      border-radius: 0 0 2px 2px;
      border-top: none;
      top: 55px;
    }
  }

  .close {
    @include flex("block", "column", "center", "start");
    margin-left: 0.5rem;

    .label {
      opacity: 0;
    }

    .icon {
      opacity: 0;
    }
  }
}
</style>
