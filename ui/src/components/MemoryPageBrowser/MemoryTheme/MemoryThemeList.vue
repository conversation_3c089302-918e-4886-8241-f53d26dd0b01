<template>
  <section class="memory-theme-list">
    <section class="header">
      <section class="sort-title" @click="toggleAll">
        <base-checkbox-solid :value="isAllSelected" />
      </section>
      <section class="sort-title-action">
        <section class="sort-title">
          <common-sort-item :key="SavedActionSort.LABEL.name"
                            class="sort-item name"
                            :current="currentSortItem"
                            :font-size-xs="true"
                            :header="SavedActionSort.LABEL"
                            @sort-event="sortEvent"
          />
        </section>
        <section class="actions-count">
          <section class="action">
            <span>{{total}} ACTIONS</span>
          </section>
          <section class="action" v-for="(action, index) in actionTypes"
                  :data="action"
                  :key="index"
                  :class="[action.color, { active : actionType.title == action.title}, {hide : excludedTypes.includes(action.type)}]"
                   v-tooltip.bottom="{ class: 'tooltip-saved-search-controls', content: `${action.count} ${action.type.titleCase()} Themes `, delay: 0 }"
          >
            <i class="icon fa-light fa-slash-forward"> </i>
            <i :class="action.icon" class="icon fa-light"> </i>
            <span>{{action.count}}</span>
          </section>
        </section>
      </section>

      <section class="sort-title">
      </section>

      <section class="sort-title">
        <common-sort-item :key="SavedActionSort.TYPE.name"
                          class="sort-item"
                          :class="SavedActionSort.TYPE.name"
                          :current="currentSortItem"
                          :font-size-xs="true"
                          :header="SavedActionSort.TYPE"
                          @sort-event="sortEvent"
        />
      </section>

      <section class="sort-title">
      </section>

      <span>
      </span>
    </section>

    <section class="list">
        <recycle-scroller
            class="scroller"
            :items="previewSavedActions"
            :item-size="44"
            v-slot="{ item }"
        >
          <memory-theme-item :item="item"></memory-theme-item>
        </recycle-scroller>
    </section>

  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { RecycleScroller } from 'vue-virtual-scroller';

import BaseCheckbox from '@/components/Base/BaseCheckbox';
import BaseCheckboxSolid from '@/components/Base/BaseCheckboxSolid';
import CommonSortItem from '@/components/CommonComponent/CommonSortItem';
import MemoryThemeItem from '@/components/MemoryPageBrowser/MemoryTheme/MemoryThemeItem';
import SavedActionAdaptor from '@/helpers/saved-action-adaptor';
import SavedActionSort from '@/enum/saved-action-sort';
import SavedActionsType from '@/enum/saved-actions-type';

import { savedActionsRequest } from '@/services/request';

const savedActionAdaptor = new SavedActionAdaptor();

export default {
  name: 'memory-theme-list',

  components: {
    BaseCheckbox,
    BaseCheckboxSolid,
    CommonSortItem,
    MemoryThemeItem,
    RecycleScroller,
  },

  data() {
    return {
      SavedActionsType,
      SavedActionSort,
      actionType: {
        title: '',
      },
    };
  },

  computed: {
    ...mapState('savedActions', [
      'actionsToUpload',
      'excludedTypes',
      'savedActions',
      'sortDirection',
      'sortedBy',
      'total',
      'totalDeletedCount',
      'totalMergedCount',
      'totalRenamedCount',
      'totalSearchedCount',
    ]),

    actionTypes() {
      return SavedActionsType.enumValues.map(value => {
        return {
          icon: value.icon(),
          color: value.lowerCase(),
          title: value.upperCase(),
          count: this.getCount(value),
          type: value,
        };
      });
    },

    currentSortItem() {
      return {
        sort: this.sortedBy,
        direction: this.sortDirection,
      };
    },

    isAllSelected() {
      return this.savedActions.every(item => this.actionsToUpload.includes(item.id));
    },

    previewSavedActions() {
      return this.savedActions
        .map(s => {
          return savedActionAdaptor.adaptSavedAction(s);
        });
    },
  },

  methods: {
    ...mapActions('savedActions', [
      'addSavedActionsToUpload',
      'removeSavedActionsFromUpload',
      'setSort',
      'setSortDirection',
    ]),

    getCount(type) {
      switch (type) {
        case SavedActionsType.CREATE_SEARCH_THEME:
          return this.totalSearchedCount;
        case SavedActionsType.MERGE_THEMES:
          return this.totalMergedCount;
        case SavedActionsType.RENAME_THEME:
          return this.totalRenamedCount;
        case SavedActionsType.DELETE_THEME:
          return this.totalDeletedCount;
        default:
          return this.total;
      }
    },

    isActive(item) {
      return this.actionsToUpload.includes(item.id);
    },

    onClickEdit() {
      this.$emit('click-edit', this.actionType);
    },

    onSelect(item) {
      if (this.isActive(item)) {
        this.removeSavedActionsFromUpload({ ids: [item.id] });
      } else {
        this.addSavedActionsToUpload({ ids: [item.id] });
      }
    },

    async sortEvent(item) {
      this.setSort({ by: item.sort });
      this.setSortDirection({ direction: item.direction });
      await savedActionsRequest.fetchSavedActions();
    },

    toggleAll() {
      if (this.isAllSelected) {
        this.savedActions.forEach(item => this.removeSavedActionsFromUpload({ ids: [item.id] }));
      } else {
        const inactiveList = this.savedActions.filter(item => !this.isActive(item));
        inactiveList.forEach(item => this.addSavedActionsToUpload({ ids: [item.id] }));
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.memory-theme-list {
  @include stretch;
  display: grid;
  grid-template-rows: min-content minmax(0, 50vh);
  overflow-x: hidden;
  overflow-y: auto;
  position: relative;

  .header {
    align-items: center;
    display: grid;
    grid-template-columns: 2rem 1fr 0.25fr 0.25fr 5rem;
    margin: 1rem 0;
    padding: 0 0.5rem 0 1rem ;

    .sort-title-action {
      @include flex("block", "row", "start", "center");

      .actions-count {
        @include flex("block", "row", "start", "center");

        column-gap: 0.5rem;
        margin-left:0.25rem;

        .action {
          @include flex("block", "row", "start", "center");

          color: #5F52C5;
          font-size: 10px;
          font-weight: 400;
          text-align: center;
          text-transform: uppercase;

          &.hide {
            display: none;
          }

          span {
            margin-left: 0.25rem;
          }

          .icon {
            &.fa-slash-forward {
              color: rgba(95, 82, 197, 0.2);
              margin-right: 0.5rem;
            }
          }
        }
      }
    }

    .sort-title {
      @include flex("block", "row", "start", "center");

      align-items: center;
      color: #5F52C5;
      cursor: pointer;
      display: flex;
      font-size: 10px;
      font-weight: 800;
      letter-spacing: 0.3px;
      line-height: 22px;
      text-align: center;
      text-transform: uppercase;

      &:hover {
        opacity: 0.8;
      }

      .icon {
        padding-left: 2px;
        transition: transform $interaction-transition-time;

        &.sortAsc {
          transform: rotateX(180deg) translateY(-2px);
        }
      }

      .sort-item {
        &.name {
          margin-left: 0.5rem;
        }
      }
    }
  }

  .list {
    @include scrollbar-thin(8px, 5px);
    overflow-x: hidden;
    overflow-y: auto;
    padding: 0;

    .memory-theme-item {
      padding-left: 1rem ;
    }

    &::-webkit-scrollbar {
      height: 7px;
      width: 7px;
    }
  }
}
</style>
