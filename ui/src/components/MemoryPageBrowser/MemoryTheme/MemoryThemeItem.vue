<template>
    <section class="memory-theme-item" :class="{active : isSelected}" @click="onSelect">
      <section>
        <base-checkbox-solid :value="isInCollection"/>
      </section>

      <section class="saved-actions-name">
        {{ item.parentLabel ? `${item.parentLabel} ▶` : '' }}

        {{ item.label }}
      </section>
      <section class="saved-actions-content search" v-if="item.type === 'CREATE_SEARCH_THEME'">
        <searched-theme-item-content
              :data="item"
            />
        <section class="right">
          <section class="exact-distinct">
            <crosshair-icon
                class="icon"
                v-tooltip.bottom="{ class: 'tooltip-saved-search-controls', content: 'Exact Search', delay: 0 }"
                :style="{ visibility: item.exact ? 'visible': 'hidden' }"
            />

            <filter-icon
                class="icon"
                v-tooltip.bottom="{ class: 'tooltip-saved-search-controls', content: 'Remove Duplicates', delay: 0 }"
                :style="{ visibility: item.distinctContent ? 'visible': 'hidden' }"
            />
          </section>
        </section>
      </section>
      <section class="saved-actions-content" v-if="item.type === 'MERGE_THEMES'">
          <span>
            {{ item.mergeLabels.join(', ') }}
          </span>
      </section>
      <section class="saved-actions-content" v-if="item.type === 'RENAME_THEME'">
          <span>
            {{ item.originalLabel }}
          </span>
      </section>
      <section class="saved-actions-content" v-if="item.type === 'DELETE_THEME'">
          <span>
            {{ item.originalLabel }}
          </span>
      </section>

      <themes-builder-saved-action-type :type="item.type" />

      <section class="controls">
        <section class="icon" @click.stop="onClickEdit" v-tooltip.bottom="{ class: 'tooltip-saved-search-controls', content: 'Edit Action', delay: 0 }">
          <i class="fa-solid fa-pen"></i>
        </section>
        <section class="icon trash" @click.stop="onClickDelete" v-tooltip.bottom="{ class: 'tooltip-saved-search-controls', content: 'Delete Action', delay: 0 }">
          <i class="fa-solid fa-trash"></i>
        </section>
      </section>
    </section>
</template>

<script>
import { CrosshairIcon, FilterIcon } from 'vue-feather-icons';
import { mapActions, mapState } from 'vuex';

import BaseCheckboxSolid from '@/components/Base/BaseCheckboxSolid';
import CommonCloseModalButton from '@/components/CommonComponent/CommonCloseModalButton';
import MemoryThemeMultiDeleteModal from '@/components/MemoryPageBrowser/Modal/MemoryThemeMultiDeleteModal';
import savedActionsRequest from '@/services/request/SavedActionsRequest';
import SavedActionsType from '@/enum/saved-actions-type';
import SearchedThemeItemContent from '@/components/UploadsDatasetConfiguration/MemorySearchedTheme/SearchedThemeItemContent';
import ThemesBuilderSavedActionType from '@/components/ThemesBuilder/ThemesBuilderSavedActionType';

export default {
  name: 'memory-theme-item',

  components: {
    BaseCheckboxSolid,
    CommonCloseModalButton,
    CrosshairIcon,
    FilterIcon,
    SearchedThemeItemContent,
    ThemesBuilderSavedActionType,
  },

  props: {
    item: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapState('savedActions', [
      'actionsToDelete',
      'actionsToUpload',
      'selected',
      'selectedList',
    ]),

    isInCollection() {
      return this.actionsToUpload.includes(this.item.id);
    },

    isSelected() {
      return this.item.id === this.selected?.id;
    },
  },

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    ...mapActions('savedActions', [
      'addSavedActionsToDelete',
      'addSavedActionsToUpload',
      'removeSavedActionsFromDelete',
      'removeSavedActionsFromUpload',
      'selectSavedAction',
      'setSavedActionLists',
      'setSavedActions',
      'updateSavedAction',
    ]),

    ...mapActions('savedActionsBuilder', ['setShowActionBuilderPanelLeft']),

    onSelect() {
      if (this.isActive(this.item)) {
        this.removeSavedActionsFromUpload({ ids: [this.item.id] });
      } else {
        this.addSavedActionsToUpload({ ids: [this.item.id] });
      }
    },

    toggleAll() {
      if (this.isAllSelected) {
        this.filteredList.forEach(item => this.removeSavedActionsFromUpload({ ids: [item.id] }));
      } else {
        const inactiveList = this.filteredList.filter(item => !this.isActive(item));
        inactiveList.forEach(item => this.addSavedActionsToUpload({ ids: [item.id] }));
      }
    },

    async onClickDelete() {
      if (this.actionsToDelete?.length) {
        this.removeSavedActionsFromDelete({ ids: [...this.actionsToDelete] });
      }
      this.addSavedActionsToDelete({ ids: [this.item.id] });

      this.setModalComponent({ component: MemoryThemeMultiDeleteModal });
    },

    async onClickEdit() {
      this.selectSavedAction({ id: this.item.id });

      if (this.selected.type === SavedActionsType.CREATE_SEARCH_THEME.name && !this.selected.components.parentId) {
        const savedAction = await savedActionsRequest.getSavedAction();
        this.updateSavedAction({ savedAction });
        this.selectSavedAction({ id: this.item.id });
      }
      this.setShowActionBuilderPanelLeft({ value: true });
    },

    isActive(item) {
      return this.actionsToUpload.includes(item.id);
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.memory-theme-item {

  align-items: center;
  background-color: #fff;
  border-top: 1px solid rgba(114, 101, 164, 0.1);
  cursor: pointer;
  display: grid;
  grid-area: memory-theme-item;
  grid-template-columns: 2rem 0.5fr 0.75fr 0.25fr 5rem;
  height: 44px;

  &:hover,
  &.active {
    background-color: rgba(97, 87, 192, 0.2);
    outline: none;
  }

  .saved-actions-name {
    @include truncate;
    color: #2D1757;
    font-size: $font-size-xs;
    font-weight: 700;
    line-height: 19px;
    margin-left: 0.5rem;
    max-width: 95%;
  }

  .saved-actions-content {
    @include truncate;

    color: #352691;
    font-size: $font-size-xs;
    font-style: italic;
    font-weight: 400;
    line-height: 19px;
    max-width: 95%;

    &.search {
      @include flex('block', 'row', 'start', 'center');

      .right {
        @include flex("block", "row", "end", "center");

        flex: 1 0 0;
        transition: all $interaction-transition-time;

        .exact-distinct {
          @include flex("block", "row", "start", "center");

          color: #352691;

          .icon {
            cursor: pointer;
            height: $font-size-sm;
            margin-left: 0.4rem;
            opacity: 1;
            width: $font-size-sm;
          }
        }
      }
    }
  }

  .type {
    @include flex("block", "row", "start", "center");

    color: #FF4141;
    font-size: 10px;
    font-weight: 800;
    line-height: 12px;
    text-align: center;
    width: 100%;
  }

  .controls {
    @include flex("block", "row", "center", "center");

    .icon {
      @include flex("block", "row", "center", "center");

      background-color: #FFF;
      border-radius: 50%;
      border: 1px solid rgba(#24124D, 0.45);
      color: #2D1757;
      cursor: pointer;
      font-size: $font-size-xxs;
      font-weight: $font-weight-bold;
      height: 1.5rem;
      margin-right: 0.5rem;
      padding: 0.2rem 0.4rem;
      text-transform: uppercase;
      width: 1.5rem;

      &:hover {
        border: 1px solid #24124D;
      }

      &.trash {
        color: #CB1509;
        border: 1px solid rgba(#CB1509, 0.45);

        &:hover, &:active, &:focus {
          border: 1px solid #CB1509;
        }

      }
    }
  }

}
</style>
