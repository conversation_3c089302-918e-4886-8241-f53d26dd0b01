<template>
  <section class="timeline-controls-options">
    <timeline-controls-dropdown
      v-if="timeColumns(active).length > 1"
      :button-text="metadataText"
      :items="metadataItems"
      @select="onSelectMetadata"
    >Metadata</timeline-controls-dropdown>

    <timeline-controls-dropdown
      :button-text="chartType.titleCase()"
      :items="chartTypeItems"
      @select="onSelectChartType"
    >Type</timeline-controls-dropdown>

    <timeline-controls-dropdown
      v-if="showScoreType"
      :button-text="yPath.titleCase()"
      :items="scoreTypeItems"
      @select="onSelectYPath"
    >Filter</timeline-controls-dropdown>

    <timeline-controls-dropdown
      v-if="showVolumeType"
      :button-text="yPath.titleCase()"
      :items="volumeTypeItems"
      @select="onSelectYPath"
    >Filter</timeline-controls-dropdown>

    <timeline-controls-dropdown
      :button-text="grouping.titleCase()"
      :items="groupingItems"
      @select="onSelectGrouping"
    >Grouping</timeline-controls-dropdown>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import TimelineChartGrouping from '@/enum/timeline-chart-grouping';
import TimelineChartScoreType from '@/enum/timeline-chart-score-type';
import TimelineChartType from '@/enum/timeline-chart-type';
import TimelineChartVolumeType from '@/enum/timeline-chart-volume-type';
import TimelineControlsDropdown from '@/components/TimelineChart/TimelineControlsDropdown';

const mapEnumValues = enumType => {
  return enumType.enumValues.map(e => {
    return {
      value: e,
      content: e.titleCase(),
    };
  });
};

export default {
  name: 'timeline-controls-options',

  components: {
    TimelineControlsDropdown,
  },

  computed: {
    ...mapState('datasets', ['active']),

    ...mapGetters('datasets', ['get', 'timeColumns']),

    ...mapState('timeSeries', [
      'chartType',
      'grouping',
      'selectedMetadata',
      'yPath',
    ]),

    chartTypeItems() {
      return mapEnumValues(TimelineChartType);
    },

    groupingItems() {
      return mapEnumValues(TimelineChartGrouping);
    },

    metadataItems() {
      const dataset = this.get(this.active);

      return this.timeColumns(this.active).map(index => {
        return {
          content: dataset.metadataHeaders[index],
          value: index,
        };
      });
    },

    metadataText() {
      const dataset = this.get(this.active);

      if (this.selectedMetadata != null) return dataset.metadataHeaders[this.selectedMetadata];

      return dataset.metadataHeaders[this.timeColumns(this.active)[0]];
    },

    scoreTypeItems() {
      return mapEnumValues(TimelineChartScoreType);
    },

    showScoreType() {
      return this.chartType === TimelineChartType.SCORE;
    },

    showVolumeType() {
      return this.chartType === TimelineChartType.VOLUME;
    },

    volumeTypeItems() {
      return mapEnumValues(TimelineChartVolumeType);
    },
  },

  methods: {
    ...mapActions('timeSeries', [
      'setChartType',
      'setGrouping',
      'setSelectedMetadata',
      'setYPath',
    ]),

    onSelectChartType(item) {
      this.setChartType({ type: item.value });
    },

    onSelectGrouping(item) {
      this.setGrouping({ grouping: item.value });
    },

    onSelectMetadata(item) {
      this.setSelectedMetadata({ index: item.value });
    },

    onSelectYPath(item) {
      this.setYPath({ yPath: item.value });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.timeline-controls-options {
  @include flex("block", "row", "start", "center");
}
</style>
