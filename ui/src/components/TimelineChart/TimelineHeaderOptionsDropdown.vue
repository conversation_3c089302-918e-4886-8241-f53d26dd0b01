<template>
  <section class="timeline-header-options-dropdown">
    <h4 class="clickable" @click="onClickGuide">
      Options Guide
      <help-circle-icon class="icon"></help-circle-icon>
    </h4>

    <h4>Polynomial Degree - {{ degrees }}</h4>
    <section class="option">
      <vue-slider v-model="degrees" v-bind="sliderDegrees" :lazy="true" />
    </section>

    <h4>Window Size - {{ window }}%</h4>
    <section class="option">
      <vue-slider v-model="window" v-bind="sliderPercent" :lazy="true" />
    </section>

    <section class="conditional" v-if="showTimeSeriesEvents">
      <h4>Sample Cutoff - {{ cutoff }}%</h4>
      <section class="option">
        <vue-slider v-model="cutoff" v-bind="sliderPercent" :lazy="true" />
      </section>
    </section>

    <section class="conditional" v-if="showTimeSeriesEvents">
      <h4>Sample Cutoff Mode</h4>
      <section class="option">
        <section class="cutoff-mode cutoff-mode-normal" @click="switchCutoffMode(TimelineChartLimitCalculation.CUTOFF)">
          <base-radio :value="isCutoffNormalMode" radio-size="small"></base-radio>
          <span>Normal</span>
        </section>
        <section class="cutoff-mode" @click="switchCutoffMode(TimelineChartLimitCalculation.SAMPLING)">
          <base-radio :value="!isCutoffNormalMode" radio-size="small"></base-radio>
          <span>Random</span>
        </section>
      </section>
    </section>

    <section class="conditional" v-if="showTimeSeriesEvents">
      <h4>Event Opacity</h4>
      <section class="option">
        <vue-slider v-model="opacity" v-bind="sliderOpacity" :lazy="true" />
      </section>
    </section>

    <section class="conditional">
      <h4>Chart Display</h4>
      <section class="option" @click="onClickLabels" v-if="showTimeSeriesEvents">
        <section class="chart-display">
          <base-checkbox :value="showLabels"></base-checkbox>
          <span>Show all event labels</span>
        </section>
      </section>
      <section class="option" @click="onToggleShowTrendLine">
        <section class="chart-display">
          <base-checkbox :value="showTrendLine"></base-checkbox>
          <span>Show Trendline</span>
        </section>
      </section>
      <section class="option" @click="onToggleShowMean">
        <section class="chart-display">
          <base-checkbox :value="showMean"></base-checkbox>
          <span>Show Mean</span>
        </section>
      </section>
      <section class="option" @click="onToggleShowUcl">
        <section class="chart-display">
          <base-checkbox :value="showUcl"></base-checkbox>
          <span>Show UCL</span>
        </section>
      </section>
      <section class="option" @click="onToggleShowLcl">
        <section class="chart-display">
          <base-checkbox :value="showLcl"></base-checkbox>
          <span>Show LCL</span>
        </section>
      </section>
    </section>

    <h4>Y-Scale Domain</h4>
    <section class="option">
      <section class="domain-inputs">
        <label>
          Lower
          <base-input :value="domainLower" @change="updateDomainLower"/>
        </label>
        <label>
          Upper
          <base-input :value="domainUpper" @change="updateDomainUpper"/>
        </label>
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';
import { HelpCircleIcon } from 'vue-feather-icons';

import VueSlider from 'vue-slider-component';

import BaseCheckbox from '@/components/Base/BaseCheckbox';
import BaseInput from '@/components/Base/BaseInput';
import BaseRadio from '@/components/Base/BaseRadio';
import BlurCloseable from '@/components/Mixins/BlurCloseable';
import sliderDefaultStyles from '@/helpers/slider-default-styles';
import TimelineHeaderOptionsModal from '@/components/TimelineChart/TimelineHeaderOptionsModal';
import TimelineChartLimitCalculation from '@/enum/timeline-chart-limit-calculation';

export default {
  name: 'timeline-header-options-dropdown',

  mixins: [BlurCloseable],

  components: {
    BaseCheckbox,
    BaseInput,
    BaseRadio,
    HelpCircleIcon,
    VueSlider,
  },

  data() {
    return {
      TimelineChartLimitCalculation,
    };
  },

  computed: {
    ...mapState('timeSeries', [
      'cutoffMode',
      'cutoffPercent',
      'df',
      'eventOpacity',
      'showLabels',
      'showLcl',
      'showMean',
      'showTrendLine',
      'showUcl',
      'windowPercent',
      'yDomain',
    ]),

    ...mapGetters('user', ['showTimeSeriesEvents']),

    cutoff: {
      get() {
        return this.cutoffPercent;
      },
      set(percent) {
        this.setCutoffPercent({ percent });
      },
    },

    degrees: {
      get() {
        return this.df;
      },
      set(df) {
        this.setDF({ df });
      },
    },

    domainLower: {
      get() {
        return this.yDomain[0].toString();
      },
      set(val) {
        this.setYDomain({ yDomain: [Number(val), this.yDomain[1]] });
      },
    },

    domainUpper: {
      get() {
        return this.yDomain[1].toString();
      },
      set(val) {
        this.setYDomain({ yDomain: [this.yDomain[0], Number(val)] });
      },
    },

    isCutoffNormalMode() {
      return this.cutoffMode === TimelineChartLimitCalculation.CUTOFF;
    },

    opacity: {
      get() {
        return this.eventOpacity;
      },
      set(opacity) {
        this.setEventOpacity({ opacity });
      },
    },

    window: {
      get() {
        return this.windowPercent;
      },
      set(percent) {
        this.setWindowPercent({ percent });
      },
    },

    sliderBase() {
      return {
        ...sliderDefaultStyles,
        width: '150px',
      };
    },

    sliderDegrees() {
      return {
        ...this.sliderBase,
        min: 1,
        max: 10,
      };
    },

    sliderDomain() {
      return {
        ...this.sliderBase,
        min: -100,
        max: 100,
      };
    },

    sliderPercent() {
      return {
        ...this.sliderBase,
        min: 1,
        max: 100,
      };
    },

    sliderOpacity() {
      return {
        ...this.sliderBase,
        interval: 0.1,
        min: 0,
        max: 0.5,
      };
    },
  },

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    ...mapActions('timeSeries', [
      'setCutoffMode',
      'setCutoffPercent',
      'setDF',
      'setEventOpacity',
      'setShowLabels',
      'setShowLcl',
      'setShowMean',
      'setShowTrendLine',
      'setShowUcl',
      'setWindowPercent',
      'setYDomain',
    ]),

    onClickGuide() {
      this.setModalComponent({ component: TimelineHeaderOptionsModal });
    },

    onClickLabels() {
      this.setShowLabels({ show: !this.showLabels });
    },

    onToggleShowLcl() {
      this.setShowLcl({ show: !this.showLcl });
    },

    onToggleShowMean() {
      this.setShowMean({ show: !this.showMean });
    },

    onToggleShowTrendLine() {
      this.setShowTrendLine({ show: !this.showTrendLine });
    },

    onToggleShowUcl() {
      this.setShowUcl({ show: !this.showUcl });
    },

    switchCutoffMode(val) {
      this.setCutoffMode({ mode: val });
    },

    updateDomainLower(val) {
      if (!Number.isNaN(Number(val))) {
        this.setYDomain({ yDomain: [Number(val), this.yDomain[1]] });
      }
    },

    updateDomainUpper(val) {
      if (!Number.isNaN(Number(val))) {
        this.setYDomain({ yDomain: [this.yDomain[0], Number(val)] });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.timeline-header-options-dropdown {
  @include flex("block", "column", "start", "stretch");
  @include stretch;
  @include panel;

  font-size: $font-size-xs;
  padding: 0.3rem 0.8rem 0.5rem;
  position: absolute;
  right: 0;
  top: 1.5rem;
  z-index: 99;

  h4 {
    @include flex("block", "row", "start", "center");

    color: $body-copy-light;
    font-size: $font-size-xxs;
    letter-spacing: $letter-spacing-sm;
    margin: 1em 0;
    text-transform: uppercase;
    white-space: nowrap;

    &.clickable {
      cursor: pointer;
      transition: color $interaction-transition-time;

      &:hover {
        color: clr("blue");
      }
    }

    .icon {
      height: $font-size-sm;
      margin-left: 0.5rem;
      width: $font-size-sm;
    }
  }

  .option {
    @include flex("block", "column", "start", "stretch");

    cursor: pointer;
    margin: 0 0.5rem 0.5rem 0.5rem;
    width: 150px;
    position: relative;

    .chart-display {
      @include flex("block", "row", "start", "stretch");

      .base-checkbox {
        margin-right: 0.5em;
        pointer-events: none;
      }
    }

    span {
      font-size: $font-size-xs;
    }

    .cutoff-mode {
      @include flex("block", "row", "start", "stretch");

      span {
        margin-left: 0.25rem;
      }
    }

    .cutoff-mode-normal {
      margin-bottom: 0.25rem;
    }

    .domain-inputs {
      @include flex("block", "row", "start", "stretch");

      label {
        color: $body-copy-light;
        font-size: $font-size-xs;
        margin: 0 0.2rem;

        .base-input {
          margin-top: 0.2rem;
        }
      }
    }
  }
}
</style>
