<template>
  <section class="timeline-header-options">
    <section class="text" @click.stop="open = !open">
      <span>CHART OPTIONS</span>
      <menu-icon class="icon" />
    </section>

    <timeline-header-options-dropdown v-if="open" @close="open = false" />
  </section>
</template>

<script>
import { MenuIcon } from 'vue-feather-icons';

import TimelineHeaderOptionsDropdown from '@/components/TimelineChart/TimelineHeaderOptionsDropdown';

export default {
  name: 'timeline-header-options',

  components: {
    MenuIcon,
    TimelineHeaderOptionsDropdown,
  },

  data() {
    return {
      open: false,
    };
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.timeline-header-options {
  @include rigid;

  font-size: $font-size-xs;
  position: relative;

  .text {
    @include flex("block", "row", "center", "center");
    @include stretch;

    color: $body-copy-light;
    cursor: pointer;
    transition: all $interaction-transition-time;

    &:hover {
      color: $body-copy;
    }

    span {
      font-size: $font-size-xxs;
      font-weight: $font-weight-medium;
      letter-spacing: $letter-spacing-sm;
      margin-right: 1em;
      margin-top: 0.1rem;
    }

    .icon {
      height: 1.3em;
    }
  }
}
</style>
