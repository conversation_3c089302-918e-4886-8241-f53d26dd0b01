<template>
  <section class="timeline-topics-loader">
    <loading-blocks-overlay class="loader"/>
  </section>
</template>

<script>
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';

export default {
  name: 'timeline-topics-loader',

  components: {
    LoadingBlocksOverlay,
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.timeline-topics-loader {
  @include flex("block", "row", "center", "center");
  @include rigid;

  height: 40px;

  .text {
    color: $body-copy-light;
    font-size: $font-size-xs;
  }

  .loader {
    @include rigid;

    height: 100%;

    .container {
      margin: 0.8rem 0.5rem;
    }
  }
}
</style>
