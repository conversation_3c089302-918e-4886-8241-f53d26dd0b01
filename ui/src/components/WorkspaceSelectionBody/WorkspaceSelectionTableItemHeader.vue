<template>
  <section class="workspace-selection-table-item-header">
    <section class="name">
      {{ organisation.workspaces.length }}
      {{ organisation.workspaces.length === 1 ? 'WORKSPACE' : 'WORKSPACES' }}
    </section>
    <section class="number-users">
      <span>USERS</span>
    </section>
    <section class="settings">
      <span>SETTINGS</span>
    </section>
    <section class="workspace-role">
      <span>ROLE</span>
    </section>
    <section class="view-button">
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';

export default {
  name: 'workspace-selection-table-item-header',

  computed: {
    ...mapState('organisation', ['organisation']),
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.workspace-selection-table-item-header {
  @include stretch;

  align-items: center;
  color: rgba(19, 28, 41, 0.6);
  display: grid;
  font-size: $font-size-xs;
  font-weight: $font-weight-bold;
  grid-template-columns: 300px 150px 150px 120px 80px;
  margin-top: 2rem;
  padding: 1rem 0;
}
</style>
