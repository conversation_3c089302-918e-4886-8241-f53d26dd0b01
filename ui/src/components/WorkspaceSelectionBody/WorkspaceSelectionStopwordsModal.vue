<template>
  <section class="workspace-selection-stopwords-modal">
    <section class="header">
      <span class="text">{{workspaceLabel}} Settings</span>
    </section>
    <section class="body">
      <section class="label">
        Add Workspace-Level Stop Words
      </section>
      <section class="input">
        <stop-words-input :read-only="readOnly" :stop-words="stopWords" @update="onUpdateStopWords" />
      </section>
      <section class="description">
        <p>
          Enter or paste stop words, with tags separated by a comma, or
          <span class="stop-word-list" @click="onClickAddList">add a stop word list</span>.
        </p>
      </section>
      <section class="editable-stopwords" @click="toggleEditableStopWords">
        <base-checkbox-solid :value="localEditableStopWords" />
        <p>
          Allow users to remove these stop words when uploading or reanalysing datasets
        </p>
      </section>
    </section>
    <section class="footer">
      <base-button class="cancel-btn" colour="light" size="small" type="link" @click="onClickCancel">Cancel</base-button>
      <base-button v-if="!proceeding"
                   class="done-btn"
                   size="small"
                   @click="onClickSave"
      >
        Save
      </base-button>
      <loading-blocks-overlay v-if="proceeding" />
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseCheckboxSolid from '@/components/Base/BaseCheckboxSolid';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import StopWordsInput from '@/components/StopWords/StopWordsInput';
import StopWordsModal from '@/components/StopWords/StopWordsModal';

import { stopWordApi, workspaceApi } from '@/services/api';

export default {
  name: 'workspace-selection-stopwords-modal',

  components: {
    BaseButton,
    BaseCheckboxSolid,
    LoadingBlocksOverlay,
    StopWordsInput,
  },

  data() {
    return {
      localEditableStopWords: false,
      proceeding: false,
      readOnly: false,
    };
  },

  async mounted() {
    // Set Stop Words Local
    this.readOnly = true;
    await stopWordApi.fetchWorkspaceStopWords(this.selectedWorkspace.id);
    const stopWords = [...this.getWorkspaceStopWords(this.selectedWorkspace.id).map(s => s.stopWord)];
    this.setLocal({ id: this.selectedWorkspace.id, stopWords });
    this.readOnly = false;

    // Set Editable Stop Word value local
    this.localEditableStopWords = this.editableStopWords;
  },

  computed: {
    ...mapGetters('stopWords', ['getLocal', 'getWorkspaceStopWords']),

    ...mapState('workspaces', ['selectedWorkspace']),

    editableStopWords() {
      return this.selectedWorkspace.settings?.allowToEditStopwords;
    },

    workspaceLabel() {
      return this.selectedWorkspace.label;
    },

    stopWords: {
      get() {
        return this.getLocal(this.selectedWorkspace.id) || [];
      },
      set(stopWords) {
        this.setLocal({ id: this.selectedWorkspace.id, stopWords });
      },
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal', 'setModalComponent']),

    ...mapActions('stopWords', ['setLocal']),

    onClickAddList() {
      this.closeModal();
      this.setModalComponent({ component: StopWordsModal });
    },

    onClickCancel() {
      this.closeModal();
    },

    onClickSave() {
      if (this.proceeding) {
        return;
      }

      this.proceeding = true;
      stopWordApi.updateWorkspaceStopWords(this.selectedWorkspace.id, this.getLocal(this.selectedWorkspace.id) || []);
      workspaceApi.update(this.selectedWorkspace.id, this.localEditableStopWords);

      this.closeModal();
    },

    onUpdateStopWords(stopWords) {
      this.stopWords = stopWords;
    },

    toggleEditableStopWords() {
      this.localEditableStopWords = !this.localEditableStopWords;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.workspace-selection-stopwords-modal {
  @include panel;
  position: relative;
  width: 600px;

  .header {
    @include flex('block', 'row', 'start', 'center');
    border-bottom: $border-light solid $border-color;

    .text {
      @include truncate;
      color: rgba(45, 23, 87, 1);
      font-size: $font-size-sm;
      font-weight: $font-weight-bold;
      padding: 1.5rem 1.5rem 1rem;
    }
  }

  .body {
    @include flex('block', 'column', 'start', 'start');
    @include scrollbar-thin;
    max-height: 450px;
    overflow-y: auto;
    padding: 1rem 1.5rem;

    .label {
      color: rgba(95, 82, 197, 1);
      font-size: 12px;
      font-weight: $font-weight-bold;
      margin-bottom: 1rem;
      text-transform: uppercase;
    }

    .input {
      @include scrollbar-thin;
      max-height: 8rem;
      overflow-y: auto;
      width: 100%;
    }

    .description {
      @include flex('block', 'column', 'start', 'stretch');
      margin-top: 1rem;

      p {
        color: rgba(45, 23, 87, 1);
        font-size: 12px;

        .stop-word-list {
          cursor: pointer;
          font-weight: $font-weight-bold;
          text-decoration: underline;
        }
      }
    }

    .editable-stopwords {
      @include flex('block', 'row', 'start', 'center');

      cursor: pointer;
      font-size: 12px;
      margin-top: 1rem;

      .base-checkbox-solid {
        margin-right: 0.4rem;
      }
    }
  }

  .footer {
    @include flex('block', 'row', 'between', 'center');
    @include rigid;
    border-top: $border-light solid $border-color;
    padding: 1.5rem 1.5rem;

    .base-button {
      font-size: 0.65em;
      font-weight: 600;
      text-transform: uppercase;

      &.cancel-btn {
        padding: 0.5rem 1rem 0.5rem 0;
      }

      &.done-btn {
        background: #2D1757;
        padding: 0.5rem 1.5rem;
      }
    }

    .loading-blocks-overlay {
      height: 28px;
    }
  }
}
</style>
