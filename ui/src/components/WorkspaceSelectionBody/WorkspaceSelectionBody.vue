<template>
  <section class="workspace-selection-body">
    <section class="body-wrapper">
      <workspace-selection-table-header />
      <workspace-selection-table-item-header />
      <workspace-selection-table-item v-for="(item) in organisation.workspaces" :item="item" :key="item.id" />
      <workspace-selection-table-footer />
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import WorkspaceSelectionTableFooter from '@/components/WorkspaceSelectionBody/WorkspaceSelectionTableFooter';
import WorkspaceSelectionTableHeader from '@/components/WorkspaceSelectionBody/WorkspaceSelectionTableHeader';
import WorkspaceSelectionTableItem from '@/components/WorkspaceSelectionBody/WorkspaceSelectionTableItem';
import WorkspaceSelectionTableItemHeader from '@/components/WorkspaceSelectionBody/WorkspaceSelectionTableItemHeader';

export default {
  name: 'workspace-selection-body',

  components: {
    WorkspaceSelectionTableFooter,
    WorkspaceSelectionTableHeader,
    WorkspaceSelectionTableItem,
    WorkspaceSelectionTableItemHeader,
  },

  computed: {
    ...mapState('organisation', ['organisation']),
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.workspace-selection-body {
  @include flex("block", "column", "center", "center");

  margin-top: 1.6rem;
  width: 100%;

  .body-wrapper {
    width: 800px; // TODO: make this to be common variable
  }
}
</style>
