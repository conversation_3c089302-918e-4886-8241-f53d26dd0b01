<template>
  <section class="workspace-selection-table-footer">
    <i class="fa-light fa-circle-question icon-question"></i>
    <a href="mailto:<EMAIL>">Need help with your workspaces? Contact Us</a>
  </section>
</template>

<script>
import { mapState } from 'vuex';

export default {
  name: 'workspace-selection-table-footer',

  components: {
  },

  computed: {
    ...mapState('organisation', ['organisation']),

    orgLabel() {
      return `${this.organisation.settings.label} Workspaces`;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.workspace-selection-table-footer {
  @include flex("block", "row", "center", "center");

  color: $nps-blue;
  font-size: $font-size-xs;
  margin-top: 2rem;
  opacity: 0.8;

  .icon-question {
    margin-right: 0.4rem;
  }

  a {
    color: $nps-blue;
  }

  &:hover, &:focus {
    color: #3981F7;

    a {
      color: #3981F7;
      outline: none;
      text-decoration: none;
    }
  }
}
</style>
