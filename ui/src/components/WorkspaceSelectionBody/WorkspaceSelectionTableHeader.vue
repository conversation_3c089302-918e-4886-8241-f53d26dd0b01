<template>
  <section class="workspace-selection-table-header">
    <section class="name">
      <i class="fa-solid fa-briefcase icon-workspace"></i>
      <span class="org-label">{{orgLabel}}</span>
    </section>
<!--    <section class="org-setting">-->
<!--      <i class="fa-solid fa-gear"></i>-->
<!--      <span>Organisation Settings</span>-->
<!--    </section>-->
  </section>
</template>

<script>
import { mapState } from 'vuex';

export default {
  name: 'workspace-selection-table-header',

  components: {
  },

  computed: {
    ...mapState('organisation', ['organisation']),

    orgLabel() {
      return `${this.organisation.settings.label} Workspaces`;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.workspace-selection-table-header {
  @include flex("block", "row", "space-between", "center");

  .name {
    @include flex("block", "row", "start", "center");

    .icon-workspace {
      font-size: 13px;
      margin-right: 0.8rem;
    }

    .org-label {
      font-size: $font-size-lg;
      font-weight: $font-weight-bold;
    }
  }

  .org-setting {
    background-color: rgba(231, 231, 231, 0.3);
    border-radius: 2px;
    border: 1px solid rgba(19, 28, 41, 0.05);
    cursor: pointer;
    font-size: 10px;
    font-weight: $font-weight-bold;
    padding: 0.4rem;
    text-transform: uppercase;
  }
}
</style>
