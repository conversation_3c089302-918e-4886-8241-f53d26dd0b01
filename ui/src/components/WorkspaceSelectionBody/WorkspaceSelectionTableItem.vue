<template>
  <section class="workspace-selection-table-item" @click.stop="onClickItem">
    <section class="name">
      <i class="fa-regular fa-globe icon-workspace"></i>
      <span>{{item.label}}</span>
      <span v-if="isCurrentWorkspace" class="current-button">CURRENT</span>
    </section>
    <section class="number-users" @click.stop="onClickNumberUsers">
      <i class="fa-solid fa-users"></i>
      <span>{{item.numberUsers}} USERS</span>
    </section>
    <section class="settings" @click.stop="onClickSettings">
      <i class="fa-solid fa-gear"></i>
      <span>SETTINGS</span>
    </section>
    <section class="workspace-role" :class="[workspaceRole]">
      {{workspaceRole}}
    </section>
    <section v-if="!isCurrentWorkspace" class="view-button">
      <span>VIEW</span>
      <i class="fa-solid fa-caret-right"></i>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import Route from '@/enum/route';
import WorkspaceSelectionStopwordsModal from '@/components/WorkspaceSelectionBody/WorkspaceSelectionStopwordsModal';
import WorkspaceUsersManagementModal from '@/components/WorkspaceUsersManagement/WorkspaceUsersManagementModal';

import { workspaceApi } from '@/services/api';

export default {
  name: 'workspace-selection-table-item',

  props: {
    item: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapState('user', ['activeWorkspaceId', 'user']),

    isCurrentWorkspace() {
      return this.activeWorkspaceId === this.item.id;
    },

    workspaceRole() {
      if (this.item.ownerId === this.user.id) return 'owner';
      if (this.item.administratorIds.includes(this.user.id)) return 'admin';
      if (this.item.editorIds.includes(this.user.id)) return 'editor';
      if (this.item.viewerIds.includes(this.user.id)) return 'viewer';
      return '';
    },
  },

  methods: {
    ...mapActions('datasets', ['reset']),

    ...mapActions('modal', ['setModalComponent']),

    ...mapActions('user', ['setActiveWorkspace']),

    ...mapActions('workspaces', ['setSelectedWorkspace']),

    async onClickItem() {
      this.reset();

      await this.$router.push({ name: Route.DATASETS });

      const workspace = await workspaceApi.getWorkspace(this.item.id);
      this.setActiveWorkspace({ workspace });
    },

    async onClickNumberUsers() {
      const selectedWorkspace = await workspaceApi.getWorkspace(this.item.id);
      this.setSelectedWorkspace({ workspace: selectedWorkspace });
      this.setModalComponent({ component: WorkspaceUsersManagementModal });
    },

    async onClickSettings() {
      const selectedWorkspace = await workspaceApi.getWorkspace(this.item.id);
      this.setSelectedWorkspace({ workspace: selectedWorkspace });
      this.setModalComponent({ component: WorkspaceSelectionStopwordsModal });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.workspace-selection-table-item {
  @include stretch;

  align-items: center;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  color: rgba(19, 28, 41, 0.8);
  cursor: pointer;
  display: grid;
  grid-template-columns: 300px 150px 150px 120px 80px;
  padding: 1rem 0;

  &:hover {
    .icon-workspace {
      background-color: $nps-blue;
    }

    .icon-workspace {
      background-color: $nps-blue;
    }

    .view-button {
      background-color: $nps-blue;
    }
  }

  .name {
    @include flex("block", "row", "start", "center");

    cursor: pointer;

    .current-button {
      background-color: white;
      border-radius: 20px;
      border: 1px solid rgba(19, 28, 41, 0.2);
      color: black;
      font-size: $font-size-xxs;
      font-weight: $font-weight-bold;
      margin-left: 0.3rem;
      padding: 0.4rem 0.6rem;

      &:hover {
        border: 1px solid rgba(19, 28, 41, 0.5);
      }
    }
  }

  .icon-workspace {
    background-color: rgba(19, 28, 41, 0.8);
    border-radius: 50%;
    color: clr("white");
    margin-right: 0.4rem;
    padding: 0.4rem;
  }

  .number-users {
    border-radius: 2px;
    border: 1px solid rgba(19, 28, 41, 0.2);
    cursor: pointer;
    font-size: $font-size-xxs;
    font-weight: $font-weight-bold;
    padding: 0.3rem 0.6rem;
    width: 90px;

    &:hover {
      border: 1px solid rgba(115, 98, 183, 1);
    }
  }

  .settings {
    background-color: rgba(231, 231, 231, 0.3);
    border-radius: 2px;
    border: 1px solid rgba(19, 28, 41, 0.05);
    cursor: pointer;
    font-size: $font-size-xxs;
    font-weight: $font-weight-bold;
    padding: 0.3rem 0.6rem;
    width: fit-content;

    &:hover {
      background-color: rgba(231, 231, 231, 0.5);
      border: 1px solid rgba(19, 28, 41, 0.2);
    }
  }

  .workspace-role {
    background-color: rgba(0, 71, 255, 0.2);
    border-radius: 0.7rem;
    font-size: 0.7rem;
    font-weight: $font-weight-bold;
    padding: 0.3rem 0.5rem;
    text-transform: uppercase;
    width: fit-content;

    &.owner {
      background-color: rgba(0, 71, 255, 0.2);
    }

    &.admin {
      background-color: #D6EBFF;
    }
  }

  .view-button {
    background-color: rgba(19, 28, 41, 0.8);
    border-radius: 2px;
    color: clr("white");
    cursor: pointer;
    font-size: $font-size-xxs;
    font-weight: $font-weight-bold;
    margin-left: auto;
    padding: 0.4rem 0.6rem;
  }
}
</style>
