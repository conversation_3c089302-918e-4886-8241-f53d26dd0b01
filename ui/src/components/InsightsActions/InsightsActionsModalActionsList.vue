<template>
  <section class="insights-actions-modal-actions-list">
    <section class="list">
      <section class="header">Actions <span class="description">(up to 3)</span></section>
      <section class="header">Status</section>
    </section>

    <section class="items">
      <section v-for="(item, index) in list" :key="index" class="item">
        <section class="text">{{ item.summary }}</section>
        <section class="status">
          <insights-actions-dropdown-status :index="index" :status="item.status" @select="onSelectStatus"/>
        </section>
        <section class="delete">
          <section class="btn" @click="removeItem(index)">
            <trash-2-icon class="icon"/>
          </section>
        </section>
      </section>
    </section>

    <section v-if="list.length < 3" class="input">
      <base-input v-model="newAction" placeholder="Add a new action..." @submit="addItem"></base-input>
      <base-button :disabled="addDisabled" colour="dark" size="small" type="outline" @click="addItem">Add Action</base-button>
    </section>
  </section>
</template>

<script>
import { Trash2Icon } from 'vue-feather-icons';
import { mapActions } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import InsightsActionsDropdownStatus from '@/components/InsightsActions/InsightsActionsDropdownStatus';
import InsightsActionStatus from '@/enum/insights-action-status';

export default {
  name: 'insights-actions-modal-actions-list',

  components: {
    BaseButton,
    BaseInput,
    InsightsActionsDropdownStatus,
    Trash2Icon,
  },

  props: {
    plan: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      list: [],
      newAction: '',
    };
  },

  computed: {
    addDisabled() {
      return this.newAction === '' || this.list.length === 3;
    },
  },

  created() {
    const list = this.plan.todoList || [];

    this.list = list.map(item => {
      return {
        status: InsightsActionStatus.enumValueOf(item.status),
        summary: item.summary,
      };
    });
  },

  methods: {
    ...mapActions('datasetsInsights', ['updateActionPlan']),

    addItem() {
      if (this.newAction === '') return;

      this.list.push({ summary: this.newAction, status: InsightsActionStatus.IN_PROGRESS });

      this.updatePlan();

      this.newAction = '';
    },

    onSelectStatus(item, index) {
      const action = this.list[index];

      action.status = item.value;

      this.updatePlan();
    },

    removeItem(index) {
      this.list = this.list.filter((el, i) => i !== index);

      this.updatePlan();
    },

    updatePlan() {
      this.updateActionPlan({
        actionPlan: {
          ...this.plan,
          todoList: this.list.map(item => {
            return {
              status: item.status.name,
              summary: item.summary,
            };
          }),
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-actions-modal-actions-list {
  @include flex("block", "column", "start", "stretch");

  margin: 2rem 0;

  .list {
    border-bottom: $border-standard;
    display: grid;
    grid-template-columns: 1fr 150px 2.5rem;

    .header {
      color: $insights-blue;
      font-size: $font-size-sm;
      font-weight: $font-weight-medium;
      margin-bottom: 0.5rem;

      .description {
        color: $body-copy;
        font-size: $font-size-xs;
        opacity: 0.5;
      }
    }
  }

  .items {
    @include flex("block", "column", "start", "stretch");

    .item {
      align-items: center;
      display: grid;
      grid-column-gap: 1rem;
      grid-template-columns: 1fr 150px auto;
      padding: 0.5rem 0;

      .text {
        @include truncate;

        font-size: $font-size-xs;
      }

      .delete {
        @include flex("block", "row", "center", "center");

        .btn {
          @include flex("block", "row", "center", "center");

          background-color: clr('red');
          border-radius: $border-radius-medium;
          cursor: pointer;
          padding: 0.3rem;
          transition: all $interaction-transition-time;

          &:hover {
            opacity: 0.7;
          }

          .icon {
            color: clr('white');
            height: 1rem;
            width: 1rem;
          }
        }
      }
    }
  }

  .input {
    @include flex("block", "row", "start", "center");

    margin-top: 0.5rem;

    .base-input {
      font-size: $font-size-xs;
      height: 26px;
    }

    .base-button {
      margin-left: 1rem;
      padding: 0.3rem 0.6rem;
    }
  }
}
</style>
