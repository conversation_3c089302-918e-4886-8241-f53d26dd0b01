<template>
  <section class="insights-actions-modal-actions-outcome">
    <h3>Desired Outcome</h3>
    <section class="outcome">
      <section class="input">
        <section class="column">
          <h4>Outcome Metric</h4>
          <base-dropdown :component="InsightsKeyAreaDropdownEmotionItem"
            :data="dataList"
            :open="dropdownOpen"
            :search="false"
            @close="dropdownOpen = false"
            @select="onSelect"
          >
            <section class="selected" @click.stop="dropdownOpen = !dropdownOpen">
              <span class="label">
                <span class="text">{{ selectedMetric }}</span>
              </span>
              <span class="icon">
                <i class="fa fa-caret-down" :class="{ open: dropdownOpen }"></i>
              </span>
            </section>
          </base-dropdown>
        </section>

        <section class="column">
          <h4>Value</h4>
          <base-input v-model="metricValue"/>
        </section>
      </section>
      <section class="preview">
        <section class="column">
          <h4>Preview</h4>
          <insights-actions-outcome :name="selectedMetric" :value="metricValue"/>
        </section>
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseDropdown from '@/components/Dropdown/BaseDropdown';
import BaseInput from '@/components/Base/BaseInput';
import InsightsKeyAreaDropdownEmotionItem from '@/components/InsightsKeyArea/InsightsKeyAreaDropdownEmotionItem';
import InsightsActionsOutcome from '@/components/InsightsActions/InsightsActionsOutcome';

export default {
  name: 'insights-actions-modal-actions-outcome',

  components: {
    BaseDropdown,
    BaseInput,
    InsightsActionsOutcome,
  },

  props: {
    plan: {
      type: Object,
      required: true,
    },

    theme: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      dropdownOpen: false,
      InsightsKeyAreaDropdownEmotionItem,
      metricValue: null,
      selectedMetric: 'Adorescore',
    };
  },

  computed: {
    ...mapGetters('datasets', ['get']),

    ...mapState('datasetsInsights', ['datasetBenchmark']),

    ...mapGetters('datasetsInsights', ['getThemeMetadataOptimise']),

    dataset() {
      return this.get(this.datasetBenchmark);
    },

    dataList() {
      const { metadataHeaders, metadataTypes } = this.dataset;

      const rs = [{ content: 'Adorescore' }];

      for (let i = 0; i < metadataHeaders?.length; i += 1) {
        if (metadataTypes[i] === 'SCORE') {
          rs.push({ content: metadataHeaders[i] });
        }
      }

      return rs;
    },

    isUp() {
      return this.optimiseChange >= 0;
    },

    optimiseChange() {
      const optimiseObj = this.getThemeMetadataOptimise(this.theme.id);

      return Math.round(optimiseObj?.optimiseChange * 100) || 0;
    },
  },

  watch: {
    selectedMetric() {
      this.updateMetricName();

      if (this.selectedMetric !== 'Adorescore') {
        this.metricValue = this.optimiseChange.toString();
      }
    },

    metricValue() {
      this.updateMetricValue();
    },
  },

  created() {
    if (this.plan.desiredOutcome?.value == null) {
      this.metricValue = `${Math.round(this.theme.polarity * 100)}`;
      this.updateMetricValue();
    }

    if (this.plan.desiredOutcome?.name == null) {
      this.updateMetricName();
    }

    if (this.plan.desiredOutcome?.name != null) {
      this.selectedMetric = this.plan.desiredOutcome.name;
    }

    if (this.plan.desiredOutcome?.value != null) {
      this.metricValue = this.plan.desiredOutcome.value;
    }
  },

  methods: {
    ...mapActions('datasetsInsights', ['updateActionPlan']),

    onSelect(item) {
      this.selectedMetric = item.content;
    },

    updateMetricName() {
      this.updateActionPlan({
        actionPlan: {
          ...this.plan,
          desiredOutcome: {
            ...this.plan.desiredOutcome,
            name: this.selectedMetric,
          },
        },
      });
    },

    updateMetricValue() {
      this.updateActionPlan({
        actionPlan: {
          ...this.plan,
          desiredOutcome: {
            ...this.plan.desiredOutcome,
            value: this.metricValue,
          },
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-actions-modal-actions-outcome {
  @include flex("block", "column", "start", "stretch");

  margin-top: 2rem;

  h3 {
    color: $insights-blue;
    font-size: $font-size-sm;
    margin-bottom: 0.5rem;
  }

  .outcome {
    @include flex("block", "row", "start", "stretch");

    .input {
      @include flex("block", "row", "between", "center");
      @include stretch;

      background-color: rgba($insights-blue, 0.05);
      border: $border-standard;
      border-right: none;
      border-top-left-radius: $border-radius-medium;
      border-bottom-left-radius: $border-radius-medium;
      padding: 1rem;

      .column {
        @include flex("block", "column", "start", "stretch");
        @include size-evenly;

        &:last-child {
          margin-left: 1rem;
        }

        h4 {
          font-size: $font-size-xs;
          font-weight: $font-weight-bold;
          text-transform: uppercase;
          margin-bottom: 0.5rem;
        }

        .base-input {
          height: 28px;
          font-size: $font-size-xs;
        }

        .base-dropdown {
          background-color: clr('white');
          height: 30px;

          .selected {
            @include flex("block", "row", "start", "center");
            @include stretch;

            border: 1px solid $border-color;
            border-radius: $border-radius-medium;
            cursor: pointer;
            height: 1.8rem;
            transition: opacity $interaction-transition-time;

            &:hover {
              opacity: 0.7;
            }

            .label {
              @include flex("block", "row", "start", "center");
              @include stretch;

              padding: 0 1rem;

              .text {
                @include truncate;

                display: inline-block;
                font-size: $font-size-xs;
                max-width: 210px;
              }
            }

            .icon {
              @include flex("block", "row", "center", "center");

              border-left: 1px solid $border-color;
              height: 1.8rem;
              width: 1.8rem;

              .fa {
                stroke-width: 3px;
                transition: all $interaction-transition-time;

                &.open{
                  transform: rotate(180deg);
                }
              }
            }
          }
        }
      }
    }

    .preview {
      @include flex("block", "row", "center", "stretch");
      @include stretch;

      background-color: rgba($insights-blue, 0.1);
      border: $border-standard;
      border-left: none;
      border-top-right-radius: $border-radius-medium;
      border-bottom-right-radius: $border-radius-medium;
      padding: 1rem;

      .column {
        @include flex("block", "column", "start", "stretch");

        h4 {
          font-size: $font-size-xs;
          font-weight: $font-weight-bold;
          text-transform: uppercase;
          margin-bottom: 0.5rem;
        }
      }
    }
  }
}
</style>
