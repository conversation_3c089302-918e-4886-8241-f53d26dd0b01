<template>
  <section class="insights-actions">
    <insights-actions-header :action-plan="actionPlan" :presentation="presentation"/>

    <section class="list-header">
      <span class="item">Insights</span>
      <span class="item">Desired Outcome</span>
      <span class="item">Actions to Take</span>
    </section>

    <section class="list">
      <insights-actions-item v-for="item in actionList"
        :item="item"
        :key="item.themeId"
        :theme="getTheme(item.themeId)"
      />
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import InsightsActionsHeader from '@/components/InsightsActions/InsightsActionsHeader';
import InsightsActionsItem from '@/components/InsightsActions/InsightsActionsItem';

export default {
  name: 'insights-actions',

  components: {
    InsightsActionsHeader,
    InsightsActionsItem,
  },

  props: {
    presentation: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      dropdownOpen: false,
    };
  },

  computed: {
    ...mapState('datasetsInsights', [
      'datasetBenchmark',
      'scorecards',
      'themeSets',
    ]),

    actionList() {
      const list = this.actionPlan?.actionList;

      if (list == null) return [];

      const sorted = Array.prototype.slice.call(list).sort((a, b) => a.actionIndex - b.actionIndex);

      if (this.presentation) return sorted.filter(item => this.isValid(item));

      return sorted;
    },

    actionPlan() {
      return this.scorecard != null ? this.scorecard.insightsActionPlan : {};
    },

    scorecard() {
      return this.scorecards.find(s => s.datasetId === this.datasetBenchmark);
    },

    themes() {
      const themeSet = this.themeSets.find(t => t.id === this.datasetBenchmark);

      return themeSet == null ? [] : themeSet.themes;
    },
  },

  methods: {
    getTheme(id) {
      return this.themes.find(t => t.id === id);
    },

    isValid(item) {
      return item.desiredOutcome != null
        && item.desiredOutcome.name != null
        && item.desiredOutcome.value != null
        && item.todoList != null
        && item.todoList.length > 0;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-actions {
  @include flex("block", "column", "start", "stretch");

  margin-bottom: 1rem;

  .list-header {
    border-bottom: $border-standard;
    display: grid;
    grid-auto-flow: row;
    grid-template-columns: 25% 22% 53%;
    margin-top: 1rem;

    .item {
      font-size: 0.7rem;
      font-weight: $font-weight-bold;
      margin-bottom: 0.5rem;
      text-transform: uppercase;
    }
  }

  .list {
    @include flex("block", "column", "start", "stretch");

    .insights-actions-item:not(:last-child) {
      border-bottom: $border-standard;
    }
  }
}
</style>
