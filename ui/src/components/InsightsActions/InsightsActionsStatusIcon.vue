<template>
  <section class="insights-actions-status-icon" :class="[status.kebabCase()]">
    <component :is="iconComponent" class="icon"/>
  </section>
</template>

<script>
import { CheckIcon, MinusIcon, SlashIcon, XIcon } from 'vue-feather-icons';

import InsightsActionStatus from '@/enum/insights-action-status';

export default {
  name: 'insights-actions-status-icon',

  props: {
    status: {
      type: Object,
      required: true,
    },
  },

  computed: {
    iconComponent() {
      switch (this.status) {
        case InsightsActionStatus.IN_PROGRESS:
          return MinusIcon;
        case InsightsActionStatus.FINISHED:
          return CheckIcon;
        case InsightsActionStatus.CANCELLED:
          return SlashIcon;
        case InsightsActionStatus.FAILED:
          return XIcon;
        default:
          return MinusIcon;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-actions-status-icon {
  @include flex("block", "row", "center", "center");

  border-radius: 0.5rem;
  height: 1rem;
  width: 1rem;

  &.finished {
    border: 1px solid $very-good-bdr;
    background-color: $very-good-inner-bg;
  }

  &.in-progress {
    border: 1px solid $poor-bdr;
    background-color: $poor-inner-bg;
  }

  &.cancelled, &.failed {
    border: 1px solid $very-poor-bdr;
    background-color: $very-poor-inner-bg;
  }

  .icon {
    border-left: none;
    color: clr('white');
    height: 0.7rem;
    width: 0.7rem;
  }
}
</style>
