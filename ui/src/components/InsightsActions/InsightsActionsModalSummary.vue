<template>
  <section class="insights-actions-modal-summary">
    <section class="header">
      <section class="left">
        <h2>{{ hasActionPlan ? 'Edit' : 'Create' }} Action Plan Summary</h2>
      </section>

      <section class="right">
        <base-button class="remove-btn"
          colour="danger"
          icon="trash2"
          size="small"
          type="outline"
          @click="onClickRemove"
        >Remove Action Plan</base-button>

        <x-icon class="icon" @click="onClickCancel"/>
      </section>
    </section>

    <section class="body">
      <section class="list">
        <h2>
          Select Themes Required for Action (up to 3)
          <span v-if="activeThemes.length === 0" class="requirement">(required)</span>
        </h2>
        <insights-theme-selector :active-themes="activeThemes" :amount-selectable="3" @select="onSelectThemes"/>
      </section>

      <section class="summary">
        <h3 class="name-header">
          Action Plan Name
          <span v-if="name == null || name === ''" class="requirement">(required)</span>
        </h3>

        <base-input v-model="name" :placeholder="'Enter a relevant title for your Action Plan'"/>

        <h3 class="summary-header">Action Plan Summary</h3>

        <base-input v-model="summary" :placeholder="'Add a brief description of your Action Plan'"/>
      </section>
    </section>

    <section class="footer">
      <base-button colour="light" size="small" type="link" @click="onClickCancel">Cancel</base-button>
      <base-button :disabled="saveDisabled" size="small" @click="onClickConfirm">Save</base-button>
    </section>
  </section>
</template>

<script>
import { XIcon } from 'vue-feather-icons';
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import InsightsThemeSelector from '@/components/Insights/InsightsThemeSelector';

import { datasetInsightApiV0 as insightApi } from '@/services/api';

export default {
  name: 'insights-actions-modal-summary',

  components: {
    BaseButton,
    BaseInput,
    InsightsThemeSelector,
    XIcon,
  },

  data() {
    return {
      name: '',
      summary: '',
      activeThemes: [],
    };
  },

  computed: {
    ...mapGetters('datasets', ['get']),

    ...mapState('datasetsInsights', [
      'datasetBenchmark',
      'scorecards',
      'themeSets',
    ]),

    ...mapGetters('datasetsInsights', ['hasActionPlan']),

    actionPlan() {
      return this.scorecard != null ? this.scorecard.insightsActionPlan : {};
    },

    dataset() {
      return this.get(this.datasetBenchmark);
    },

    saveDisabled() {
      return this.name == null || this.name === '' || this.activeThemes.length === 0;
    },

    scorecard() {
      return this.scorecards.find(s => s.datasetId === this.datasetBenchmark);
    },

    themes() {
      const themeSet = this.themeSets.find(t => t.id === this.datasetBenchmark);

      return themeSet == null ? [] : themeSet.themes;
    },
  },

  watch: {
    actionPlan() {
      this.updateLocalValues();
    },
  },

  created() {
    this.updateLocalValues();
  },

  methods: {
    ...mapActions('datasetsInsights', ['updateScorecard']),

    ...mapActions('modal', ['closeModal']),

    async getScorecard() {
      const scorecard = await insightApi.getScorecard(this.datasetBenchmark);

      this.updateScorecard({ scorecard });
    },

    async onClickCancel() {
      this.closeModal();
    },

    async onClickConfirm() {
      const actionList = this.themes
        .filter(t => this.activeThemes.includes(t.id))
        .map(t => {
          const item = this.actionPlan?.actionList?.find(i => i.themeId === t.id);

          if (item != null) {
            return {
              ...item,
              actionIndex: this.activeThemes.indexOf(t.id),
            };
          }

          return {
            actionIndex: this.activeThemes.indexOf(t.id),
            themeId: t.id,
            themeName: t.topicLabel,
          };
        });

      await insightApi.updateActionPlan(this.datasetBenchmark, {
        name: this.name,
        summary: this.summary,
        actionList,
      });

      await this.getScorecard();

      this.closeModal();
    },

    async onClickRemove() {
      await insightApi.updateActionPlan(this.datasetBenchmark, {
        name: null,
        summary: null,
        list: null,
      });

      await this.getScorecard();

      this.closeModal();
    },

    onSelectThemes(themes) {
      this.activeThemes = themes;
    },

    updateLocalValues() {
      const { actionList, name, summary } = this.actionPlan;

      if (actionList != null) this.activeThemes = actionList.map(t => t.themeId);
      if (name != null) this.name = name;
      if (summary != null) this.summary = summary;

      if (name == null) this.name = `${this.dataset.label} Action Plan`;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-actions-modal-summary {
  @include modal;

  .header {
    @include modal-header;

    .remove-btn {
      padding: 0.2rem 0.4rem 0.2rem 0.2rem;
      margin-right: 1rem;
    }
  }

  .body {
    @include flex("block", "column", "start", "stretch");

    padding: 0;

    .list {
      @include flex("block", "column", "start", "stretch");
      @include stretch;

      background-color: clr('white');
      border-bottom: $border-standard;
      overflow-y: auto;
      padding: 1rem;

      h2 {
        font-size: $font-size-sm;
        margin-bottom: 0.5rem;

        .requirement {
          color: clr('red');
          font-size: $font-size-xs;
        }
      }
    }

    .summary {
      padding: 1rem;

      h3 {
        font-size: $font-size-xs;
        font-weight: $font-weight-bold;
        margin-bottom: 0.3rem;

        &.summary-header {
          margin-top: 0.5rem;
        }

        .requirement {
          color: clr('red');
          font-weight: normal;
        }
      }

      .base-input {
        font-size: $font-size-xs;
        padding: 0.4rem;
        margin-bottom: 0.3rem;
      }
    }
  }

  .footer {
    padding: 1rem;
  }
}
</style>
