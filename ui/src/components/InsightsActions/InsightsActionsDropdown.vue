<template>
  <section class="insights-actions-dropdown">
    <h3>Edit Action Plan</h3>

    <section class="item" @click="onClickPlan">
      <edit-2-icon class="icon"/>
      <span class="text">Edit Action Plan</span>
    </section>

    <section v-for="(item, index) in actionList" :key="item.themeId" class="item" @click="onClickItem(item)">
      <edit-2-icon class="icon"/>
      <span class="text">Edit Item {{ index + 1 }}</span>
    </section>
  </section>
</template>

<script>
import { Edit2Icon } from 'vue-feather-icons';
import { mapActions } from 'vuex';

import BlurCloseable from '@/components/Mixins/BlurCloseable';
import InsightsActionsModalSummary from '@/components/InsightsActions/InsightsActionsModalSummary';
import InsightsActionsModalActions from '@/components/InsightsActions/InsightsActionsModalActions';

export default {
  name: 'insights-actions-dropdown',

  mixins: [BlurCloseable],

  components: {
    Edit2Icon,
  },

  props: {
    actionPlan: {
      type: Object,
      required: true,
    },
  },

  computed: {
    actionList() {
      const list = this.actionPlan?.actionList;

      if (list == null) return [];

      return Array.prototype.slice.call(list).sort((a, b) => a.actionIndex - b.actionIndex);
    },
  },

  methods: {
    ...mapActions('datasetsInsights', ['setEditActionPlan']),

    ...mapActions('modal', ['setModalComponent']),

    onClickItem(item) {
      this.setEditActionPlan({ actionPlan: item });
      this.setModalComponent({ component: InsightsActionsModalActions });
      this.$emit('close');
    },

    onClickPlan() {
      this.setModalComponent({ component: InsightsActionsModalSummary });
      this.$emit('close');
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-actions-dropdown {
  @include panel;

  padding: 0.8rem;

  h3 {
    font-size: 0.7rem;
    font-weight: $font-weight-bold;
    padding: 0 0.2rem;
    text-transform: uppercase;
  }

  .item {
    @include flex("block", "row", "start", "center");

    border-radius: $border-radius-medium;
    cursor: pointer;
    padding: 0.2rem 0.4rem;
    margin-top: 0.3rem;

    &:hover {
      background-color: lighten($body-copy, 75%);
    }

    .icon {
      height: $font-size-xs;
      width: $font-size-xs;
    }

    .text {
      font-size: 0.7rem;
      margin-left: 0.4rem;
    }
  }
}
</style>
