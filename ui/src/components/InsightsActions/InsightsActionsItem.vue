<template>
  <section class="insights-actions-item">
    <insights-actions-item-insight :item="item" :swot="swot" :theme="theme"/>

    <section v-if="!isValid" class="empty">
      <section class="left">
        Add Actions to Select Desired Outcome
      </section>
      <section class="right" @click="onClickCreate">
        <i class="fa fa-plus"></i>
        Create Actions
      </section>
    </section>

    <section v-if="isValid" class="outcome">
      <insights-actions-outcome :name="desiredOutcome.name" :value="desiredOutcome.value"/>
    </section>

    <section v-if="isValid" class="actions">
      <section v-for="(listItem, index) in item.todoList" :key="index" class="item">
        <section class="text">{{ listItem.summary }}</section>
        <section class="status">
          <insights-actions-status-icon :status="itemStatus(listItem)"/>
        </section>
      </section>

      <section v-if="item.assignee != null || item.dueDate != null" class="assignment">
        <section v-if="item.dueDate != null" class="due-date">
          <i class="fa fa-calendar"></i>
          {{ formattedDate }}
        </section>
        <section v-if="item.assignee != null" class="assignee">
          <i class="fa fa-user"></i>
          {{ item.assignee }}
        </section>
      </section>
    </section>
  </section>
</template>

<script>
import { format, parseISO } from 'date-fns';
import { mapActions } from 'vuex';

import InsightsActionsItemInsight from '@/components/InsightsActions/InsightsActionsItemInsight';
import InsightsActionsModalActions from '@/components/InsightsActions/InsightsActionsModalActions';
import InsightsActionsOutcome from '@/components/InsightsActions/InsightsActionsOutcome';
import InsightsActionsStatusIcon from '@/components/InsightsActions/InsightsActionsStatusIcon';
import InsightsActionStatus from '@/enum/insights-action-status';

export default {
  name: 'insights-actions-item',

  components: {
    InsightsActionsItemInsight,
    InsightsActionsOutcome,
    InsightsActionsStatusIcon,
  },

  props: {
    item: {
      type: Object,
      required: true,
    },

    theme: {
      type: Object,
      required: true,
    },
  },

  computed: {
    desiredOutcome() {
      return this.item.desiredOutcome;
    },

    formattedDate() {
      if (this.item.dueDate == null || this.item.dueDate === '') return '';

      return format(parseISO(this.item.dueDate), 'd MMM y');
    },

    isValid() {
      return this.desiredOutcome != null
        && this.desiredOutcome.name != null
        && this.desiredOutcome.value != null
        && this.item.todoList != null
        && this.item.todoList.length > 0;
    },

    swot() {
      return this.theme.swot?.attribute;
    },
  },

  methods: {
    ...mapActions('datasetsInsights', ['setEditActionPlan']),

    ...mapActions('modal', ['setModalComponent']),

    onClickCreate() {
      this.setEditActionPlan({ actionPlan: this.item });
      this.setModalComponent({ component: InsightsActionsModalActions });
    },

    itemStatus(item) {
      return InsightsActionStatus.enumValueOf(item.status);
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-actions-item {
  display: grid;
  grid-template-columns: 25% 22% 53%;
  padding: 1rem 0;

  .empty {
    @include flex("block", "row", "center", "center");

    font-size: 0.7rem;
    font-weight: $font-weight-bold;
    grid-column: 2 / 4;
    text-transform: uppercase;

    .left {
      border: $border-standard;
      border-right: none;
      border-top-left-radius: $border-radius-medium;
      border-bottom-left-radius: $border-radius-medium;
      padding: 0.3rem 1rem;
    }

    .right {
      background-color: $insights-blue;
      border: 1px solid $insights-blue;
      border-left: none;
      border-top-right-radius: $border-radius-medium;
      border-bottom-right-radius: $border-radius-medium;
      color: clr('white');
      cursor: pointer;
      padding: 0.3rem 1rem;
      transition: opacity $interaction-transition-time;

      &:hover {
        opacity: 0.7;
      }
    }
  }

  .actions {
    @include flex("block", "column", "start", "stretch");

    .item {
      @include flex("block", "row", "between", "center");

      border-bottom: $border-standard;
      padding-bottom: 0.5rem;
      margin-bottom: 0.5rem;

      .text {
        @include truncate;

        font-size: $font-size-xs;
      }
    }

    .assignment {
      @include flex("block", "row", "start", "center");

      color: $insights-blue;
      font-size: $font-size-xs;
      font-weight: $font-weight-bold;

      .assignee {
        margin-left: 1rem;
      }
    }
  }
}
</style>
