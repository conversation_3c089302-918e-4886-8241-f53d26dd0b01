<template>
  <section class="insights-actions-dropdown-status-item">
    <insights-actions-status-icon :status="data.value"/>
    <section class="text">{{ this.data.label }}</section>
  </section>
</template>

<script>
import InsightsActionsStatusIcon from '@/components/InsightsActions/InsightsActionsStatusIcon';

export default {
  name: 'insights-actions-dropdown-status-item',

  components: {
    InsightsActionsStatusIcon,
  },

  props: {
    data: {
      type: Object,
      required: true,
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-actions-dropdown-status-item {
  @include flex("block", "row", "start", "center");

  cursor: pointer;
  padding: 0.5rem;

  &:hover {
    opacity: 0.7;
  }

  .text {
    margin-left: 0.5rem;
  }
}
</style>
