<template>
  <section class="insights-actions-modal-actions">
    <section class="header">
      <section class="left">
        <h2>Edit Desired Outcomes</h2>
      </section>
    </section>
    <section v-if="theme" class="body">
      <insights-actions-modal-actions-summary :plan="editActionPlan" :theme="theme"/>
      <insights-actions-modal-actions-outcome :plan="editActionPlan" :theme="theme"/>
      <insights-actions-modal-actions-list :plan="editActionPlan"/>
      <insights-actions-modal-actions-assign :plan="editActionPlan" :theme="theme"/>
    </section>
    <section class="footer">
      <base-button colour="light" size="small" type="link" @click="onClickCancel">Cancel</base-button>
      <section
        v-tooltip.top="{
          class: 'tooltip-insights-modal-save-changes',
          content: 'You need to add an Action before you can save changes',
          visible: disabledSave,
        }">
        <base-button size="small" @click="onClickConfirm" :disabled="disabledSave">Save Changes</base-button>
      </section>
    </section>
  </section>
</template>

<script>
import { XIcon } from 'vue-feather-icons';
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import InsightsActionsModalActionsAssign from '@/components/InsightsActions/InsightsActionsModalActionsAssign';
import InsightsActionsModalActionsList from '@/components/InsightsActions/InsightsActionsModalActionsList';
import InsightsActionsModalActionsOutcome from '@/components/InsightsActions/InsightsActionsModalActionsOutcome';
import InsightsActionsModalActionsSummary from '@/components/InsightsActions/InsightsActionsModalActionsSummary';

import { datasetInsightApiV0 as insightApi } from '@/services/api';

export default {
  name: 'insights-actions-modal-actions',

  components: {
    BaseButton,
    InsightsActionsModalActionsAssign,
    InsightsActionsModalActionsList,
    InsightsActionsModalActionsOutcome,
    InsightsActionsModalActionsSummary,
    XIcon,
  },

  computed: {
    ...mapState('datasetsInsights', [
      'datasetBenchmark',
      'editActionPlan',
      'scorecards',
      'themeSets',
    ]),

    actionPlan() {
      return this.scorecard != null ? this.scorecard.insightsActionPlan : {};
    },

    disabledSave() {
      return !this.editActionPlan.todoList || this.editActionPlan.todoList.length === 0;
    },

    scorecard() {
      return this.scorecards.find(s => s.datasetId === this.datasetBenchmark);
    },

    theme() {
      return this.themes.find(t => t.id === this.editActionPlan.themeId);
    },

    themes() {
      const themeSet = this.themeSets.find(t => t.id === this.datasetBenchmark);

      return themeSet == null ? [] : themeSet.themes;
    },
  },

  create() {
    this.updateActionPlan({ actionPlan: this.editActionPlan });
  },

  methods: {
    ...mapActions('datasetsInsights', ['updateActionPlan', 'updateScorecard']),

    ...mapActions('modal', ['closeModal']),

    onClickCancel() {
      this.closeModal();
    },

    async onClickConfirm() {
      const newList = [
        ...this.actionPlan.actionList
          .filter(theme => theme.themeId !== this.editActionPlan.themeId), this.editActionPlan,
      ];

      const newPlan = {
        ...this.actionPlan,
        actionList: newList,
      };

      await insightApi.updateActionPlan(this.datasetBenchmark, newPlan);

      const scorecard = await insightApi.getScorecard(this.datasetBenchmark);

      this.updateScorecard({ scorecard });

      this.closeModal();
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-actions-modal-actions {
  @include modal;

  .header {
    @include modal-header;

    .remove-btn {
      padding: 0.2rem 0.4rem 0.2rem 0.2rem;
      margin-right: 1rem;
    }
  }

  .base-button {
    &.disabled-true {
      cursor: not-allowed;
    }
  }

  .body {
    background-color: clr('white');
  }

  .footer {
    padding: 1rem;
  }
}
</style>
