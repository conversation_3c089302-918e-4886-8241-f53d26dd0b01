<template>
  <section class="insights-actions-modal-actions-summary">
    <h3>Insight Summary</h3>
    <base-input v-model="summaryText"/>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseInput from '@/components/Base/BaseInput';

export default {
  name: 'insights-actions-modal-actions-summary',

  components: {
    BaseInput,
  },

  props: {
    plan: {
      type: Object,
      required: true,
    },

    theme: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      summaryText: '',
    };
  },

  computed: {
    ...mapGetters('datasets', ['get']),

    ...mapState('datasetsInsights', ['datasetBenchmark']),

    adoreScore() {
      return Math.round(this.theme.polarity * 100);
    },

    dataset() {
      return this.get(this.datasetBenchmark);
    },

    defaultText() {
      return `Contributes ${this.volume}% of the volume of ${this.dataset.label} with a score of ${this.adoreScore}`;
    },

    volume() {
      return Math.round((this.theme.numOfDocuments / this.dataset.documentCount) * 100);
    },
  },

  watch: {
    summaryText() {
      this.updateActionPlan({
        actionPlan: {
          ...this.plan,
          summary: this.summaryText,
        },
      });
    },
  },

  created() {
    if (this.plan.summary == null || this.plan.summary === '') this.summaryText = this.defaultText;
    else this.summaryText = this.plan.summary;
  },

  methods: {
    ...mapActions('datasetsInsights', ['updateActionPlan']),
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-actions-modal-actions-summary {
  @include flex("block", "column", "start", "stretch");

  h3 {
    color: $insights-blue;
    font-size: $font-size-sm;
    margin-bottom: 0.5rem;
  }

  .base-input {
    padding: 0.3rem;
    font-size: $font-size-xs;
  }
}
</style>
