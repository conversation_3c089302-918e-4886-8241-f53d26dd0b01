<template>
  <section class="insights-actions-dropdown-status">
    <base-dropdown :component="InsightsActionsDropdownStatusItem"
      :data="dataList"
      :open="open"
      :search="false"
      @close="open = false"
      @select="onSelect"
    >
      <section class="selected" @click.stop="open = !open">
        <span class="label">
          <insights-actions-status-icon :status="status"/>
          <span class="text">{{ status.titleCase() }}</span>
        </span>
        <span class="icon">
          <i class="fa fa-caret-down" :class="{ open }"></i>
        </span>
      </section>
    </base-dropdown>
  </section>
</template>

<script>
import BaseDropdown from '@/components/Dropdown/BaseDropdown';

import InsightsActionStatus from '@/enum/insights-action-status';
import InsightsActionsDropdownStatusItem from '@/components/InsightsActions/InsightsActionsDropdownStatusItem';
import InsightsActionsStatusIcon from '@/components/InsightsActions/InsightsActionsStatusIcon';

export default {
  name: 'insights-actions-dropdown-status',

  components: {
    BaseDropdown,
    InsightsActionsStatusIcon,
  },

  props: {
    index: {
      type: Number,
      required: true,
    },

    status: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      InsightsActionsDropdownStatusItem,
      open: false,
    };
  },

  computed: {
    dataList() {
      return InsightsActionStatus.enumValues.map(e => {
        return {
          label: e.titleCase(),
          value: e,
        };
      });
    },
  },

  methods: {
    onSelect(item) {
      this.$emit('select', item, this.index);
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-actions-dropdown-status {
  background-color: clr('white');
  height: 30px;

  .selected {
    @include flex("block", "row", "start", "center");
    @include stretch;

    border: 1px solid $border-color;
    border-radius: $border-radius-medium;
    cursor: pointer;
    height: 1.8rem;
    transition: opacity $interaction-transition-time;

    &:hover {
      opacity: 0.7;
    }

    .label {
      @include flex("block", "row", "start", "center");
      @include stretch;

      padding: 0 0.5rem;

      .text {
        @include truncate;

        display: inline-block;
        font-size: $font-size-xs;
        margin-left: 0.5rem;
      }
    }

    .icon {
      @include flex("block", "row", "center", "center");
      @include rigid;

      border-left: 1px solid $border-color;
      height: 1.8rem;
      width: 1.8rem;

      .fa {
        stroke-width: 3px;
        transition: all $interaction-transition-time;

        &.open{
          transform: rotate(180deg);
        }
      }
    }
  }
}
</style>
