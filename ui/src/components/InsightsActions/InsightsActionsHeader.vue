<template>
  <section class="insights-actions-header">
    <section class="top">
      <section class="left">
        <i class="fa fa-bolt icon"></i>
        <h2>{{ actionPlan.name }}</h2>
      </section>

      <section v-if="!presentation" class="right">
        <section class="edit-btn"
          v-tooltip.bottom.end.notrigger="{
            html: 'insights-actions-dropdown',
            class: 'tooltip-insights-dropdown',
            delay: 0,
            visible: dropdownOpen,
          }"
          @click.stop="dropdownOpen = !dropdownOpen">
          •••
        </section>
        <insights-actions-dropdown id="insights-actions-dropdown" :action-plan="actionPlan" @close="dropdownOpen = false"/>
      </section>
    </section>

    <section v-if="actionPlan.summary" class="bottom">
      <h4>{{ actionPlan.summary }}</h4>
    </section>
  </section>
</template>

<script>
import InsightsActionsDropdown from '@/components/InsightsActions/InsightsActionsDropdown';

export default {
  name: 'insights-actions-header',

  components: {
    InsightsActionsDropdown,
  },

  props: {
    actionPlan: {
      type: Object,
      required: true,
    },

    presentation: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      dropdownOpen: false,
    };
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-actions-header {
  @include flex("block", "column", "start", "stretch");

  .top {
    @include flex("block", "row", "between", "stretch");

    .left {
      @include flex("block", "row", "start", "center");

      h2 {
        color: $insights-blue;
        font-size: $font-size-base;
      }

      .icon {
        color: $insights-blue;
        height: $font-size-sm;
        margin-right: 0.5rem;
        width: $font-size-sm;
      }
    }

    .right {
      .edit-btn {
        border: 1px solid $insights-bdr-key;
        border-radius: $border-radius-medium;
        cursor: pointer;
        font-size: $font-size-xs;
        padding: 0.1rem 0.4rem;
        transition: all $interaction-transition-time;
        user-select: none;

        &:hover {
          border-color: $body-copy;
        }
      }
    }
  }

  .bottom {
    margin-top: 1rem;

    h4 {
      font-size: $font-size-xs;
      font-weight: $font-weight-normal;
    }
  }
}
</style>
