<template>
  <section class="insights-actions-outcome" :class="{ [scoreTypeText]: name === 'Adorescore' }">
    <span class="top">{{ name === 'Adorescore' ? 'Adorescore' : 'Increase' }}</span>

    <span class="bottom">
      <span v-if="name === 'Adorescore'" class="score-type">
        <span class="score">{{ value }}</span>
        <span class="text" :class="[`score-${scoreTypeText}`]">{{ scoreType }}</span>
      </span>

      <span v-else class="potential">
        <i class="fa fa-arrow-up icon"/> {{ value }}%
      </span>
    </span>
  </section>
</template>

<script>
import { kebabCase } from 'lodash-es';
import { mapGetters } from 'vuex';

export default {
  name: 'insights-actions-outcome',

  props: {
    name: {
      type: String,
      required: true,
    },

    value: {
      type: [String, Number],
      required: true,
    },
  },

  computed: {
    ...mapGetters('benchmark', ['classifyAdorescore']),

    scoreType() {
      return this.classifyAdorescore(Number(this.value)).name;
    },

    scoreTypeText() {
      return kebabCase(this.scoreType.toLowerCase());
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-actions-outcome {
  @include flex("block", "column", "start", "center");

  background-color: clr('white');
  border: 1px solid $border-color-active;
  border-radius: $border-radius-medium;
  height: 60px;
  width: 140px;

  &.very-good { border: 1px solid $very-good-bdr; }
  &.good { border: 1px solid $good-bdr; }
  &.fair { border: 1px solid $fair-bdr; }
  &.poor { border: 1px solid $poor-bdr; }
  &.very-poor { border: 1px solid $very-poor-bdr; }

  .top {
    @include flex("block", "row", "center", "center");

    border-bottom: 1px solid $border-color;
    font-size: $font-size-xxs;
    font-weight: $font-weight-bold;
    height: 36px;
    text-transform: uppercase;
    width: 100%;
  }

  .bottom {
    @include flex("block", "row", "center", "center");

    font-size: $font-size-lg;
    height: 100%;

    .icon {
      font-size: $font-size-xs;
      font-weight: $font-weight-light;
      padding-right: 0.3rem;
      padding-top: 0.1rem;
    }

    .potential {
      @include flex("block", "row", "center", "center");
    }

    .attribute {
      @include flex("block", "row", "center", "center");

      font-size: $font-size-sm;
      font-weight: $font-weight-bold;
    }

    .score-type {
      @include flex("block", "row", "center", "center");

      .score {
        font-size: $font-size-xs;
        font-weight: $font-weight-bold;
        margin-right: 0.5rem;
      }

      .text {
        border-radius: 2px;
        font-size: 0.7rem;
        font-weight: $font-weight-bold;
        padding: 0.2rem 0.5rem;
        text-transform: uppercase;

        &.score-fair {
          background-color: $fair-inner-bg;
          color: $fair-inner-txt;
        }

        &.score-good {
          background-color: $good-inner-bg;
          color: $good-inner-txt;
        }

        &.score-poor {
          background-color: $poor-bdr;
          color: $poor-score-txt;
        }

        &.score-very-good {
          background-color: $very-good-inner-bg;
          color: $very-good-inner-txt;
        }

        &.score-very-poor {
          background-color: $very-poor-bdr;
          color: $very-poor-inner-txt;
        }
      }
    }
  }
}
</style>
