<template>
  <section class="insights-actions-item-insight">
    <section class="left">
      <section class="index">{{ item.actionIndex + 1 }}</section>
    </section>

    <section class="right">
      <section class="name">{{ item.themeName }}</section>
      <section class="text">{{ summary }}</section>
      <section v-if="swot !== 'NONE'" class="swot" :class="[swot.toLowerCase()]">
        <section class="indicator" :class="[swot.toLowerCase()]"></section>
        <span class="attribute">{{ swot }}</span>
      </section>
    </section>
  </section>
</template>

<script>
import { mapGetters, mapState } from 'vuex';

export default {
  name: 'insights-actions-item-insight',

  props: {
    item: {
      type: Object,
      required: true,
    },

    swot: {
      type: String,
      required: true,
    },

    theme: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapGetters('datasets', ['get']),

    ...mapState('datasetsInsights', ['datasetBenchmark']),

    adoreScore() {
      return Math.round(this.theme.polarity * 100);
    },

    dataset() {
      return this.get(this.datasetBenchmark);
    },

    summary() {
      if (this.item.summary != null && this.item.summary !== '') return this.item.summary;

      return `Contributes ${this.volume}% of the volume of ${this.dataset.label} with a score of ${this.adoreScore}`;
    },

    volume() {
      return Math.round((this.theme.numOfDocuments / this.dataset.documentCount) * 100);
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-actions-item-insight {
  @include flex("block", "row", "start", "stretch");

  padding-right: 0.5rem;

  .left {
    margin-right: 0.5rem;

    .index {
      @include flex("block", "row", "center", "center");
      @include rigid;

      background: $insights-blue;
      border-radius: $border-radius-medium;
      color: clr('white');
      font-size: 0.7rem;
      height: 1rem;
      width: 1rem;
    }
  }

  .right {
    @include flex("block", "column", "start", "stretch");

    .name {
      color: $insights-blue;
      font-size: $font-size-sm;
      font-weight: $font-weight-medium;
    }

    .text {
      font-size: $font-size-xs;
      line-height: 1rem;
      margin: 0.5rem 0;
    }

    .swot {
      @include flex("block", "row", "start", "center");

      &.strength { color: $swot-strength; }
      &.weakness { color: $swot-weakness; }
      &.opportunity { color: $swot-opportunity; }
      &.threat { color: $swot-threat; }

      .indicator {
        border-radius: 0.3rem;
        height: 0.6rem;
        margin-right: 0.5rem;
        width: 0.6rem;

        &.strength { background-color: $swot-strength; }
        &.weakness { background-color: $swot-weakness; }
        &.opportunity { background-color: $swot-opportunity; }
        &.threat { background-color: $swot-threat; }
      }

      .attribute {
        font-size: $font-size-xs;
        font-weight: $font-weight-bold;
      }
    }
  }
}
</style>
