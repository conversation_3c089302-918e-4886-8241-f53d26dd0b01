<template>
  <section class="insights-actions-modal-actions-assign">
    <section class="column">
      <h3>Assigned To</h3>
      <base-input v-model="assignee"/>
    </section>

    <section class="column">
      <h3>Due Date</h3>

      <date-picker
        v-model="date"
        calendar-class="date-calendar"
        :disabled-dates="{ to: new Date() }"
        input-class="insights-date-input"
        wrapper-class="insights-date-picker"
      />
    </section>
  </section>
</template>

<script>
import DatePicker from 'vuejs-datepicker';

import { formatISO, parseISO } from 'date-fns';
import { mapActions } from 'vuex';

import BaseInput from '@/components/Base/BaseInput';

export default {
  name: 'insights-actions-modal-actions-assign',

  components: {
    BaseInput,
    DatePicker,
  },

  props: {
    plan: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      assignee: '',
      date: null,
    };
  },

  watch: {
    assignee() {
      this.updateActionPlan({
        actionPlan: { ...this.plan, assignee: this.assignee },
      });
    },

    date() {
      this.updateActionPlan({
        actionPlan: { ...this.plan, dueDate: formatISO(this.date) },
      });
    },
  },

  created() {
    this.assignee = this.plan.assignee || '';
    this.date = this.plan.dueDate == null
      ? new Date()
      : parseISO(this.plan.dueDate);
  },

  methods: {
    ...mapActions('datasetsInsights', ['updateActionPlan']),
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-actions-modal-actions-assign {
  @include flex("block", "row", "between", "stretch");

  border-top: $border-standard;
  margin: 1rem -1.5rem 0;
  padding: 1rem 1.5rem 0;

  .column {
    @include flex("block", "column", "start", "stretch");
    @include size-evenly;

    &:last-child {
      margin-left: 1rem;
    }

    h3 {
      color: $insights-blue;
      font-size: $font-size-sm;
      margin-bottom: 0.5rem;
    }

    .base-input {
      height: 28px;
      font-size: $font-size-xs;
    }

    .insights-date-picker {
      width: 100%;

      .insights-date-input {
        border: $border-standard;
        height: 27px;
        width: 100%;
      }
    }
  }
}
</style>
