<template>
  <section class="swot-chart-custom-marker-indicator">
    <section class="checkbox">
      <!-- Hide until changes fully affect marker -->
      <!-- <base-checkbox :value="includeIndicator" @input="toggleSelection"/> -->
      <tag-icon class="icon"/>
      <section class="text">Indicator</section>
    </section>
    <section class="label">
      <p><b>{{label}}</b> - Contributes <b>{{percentText}} {{typeQuality}}</b> to Dataset with an Adorescore of <b>{{adoreScore}} ({{adoreScoreType}})</b></p>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';
import { TagIcon } from 'vue-feather-icons';

import BaseCheckbox from '@/components/Base/BaseCheckbox';

import { nameByApiIndex } from '@/helpers/index-utils';

export default {
  name: 'swot-chart-custom-marker-indicator',

  components: {
    BaseCheckbox,
    TagIcon,
  },

  data() {
    return {
      selected: true,
    };
  },

  computed: {
    ...mapGetters('benchmark', ['classifyAdorescore']),

    ...mapState('datasets', ['active', 'overviews']),

    ...mapState('datasetsInsights', [
      'datasetBenchmark',
      'summaries',
      'themeSets',
    ]),

    ...mapState('themes', ['selectedTheme']),

    adoreScore() {
      return Math.round(this.selectedTheme.polarity * 100);
    },

    adoreScoreType() {
      return this.classifyAdorescore(this.adoreScore).name;
    },

    dataset() {
      return this.overviews.find(d => d.id === this.active);
    },

    datasetScore() {
      return this.summary.emotionIndexesAvg[this.themeSet.maxEmotionPosition];
    },

    includeIndicator() {
      // if (this.customSelectedTheme) return this.customSelectedTheme.includeIndicator;
      // return false;

      return true;
    },

    gapScore() {
      return Math.abs(this.themeSet.maxEmotionValue) - Math.abs(this.datasetScore);
    },

    label() {
      return this.selectedTheme.topicLabel.toUpperCase();
    },

    percentText() {
      const percentNumb = Math.round(Math.abs(this.gapScore / this.datasetScore) * 100);
      return `${percentNumb === 0 ? '<1' : percentNumb}%`;
    },

    summary() {
      return this.summaries.find(s => s.id === this.dataset.id);
    },

    themeSet() {
      const themeSet = this.themeSets.find(t => t.id === this.dataset.id);

      return themeSet.themes.find(t => t.id === this.selectedTheme.id);
    },

    typeQuality() {
      return nameByApiIndex(this.themeSet.maxEmotionPosition);
    },
  },

  mounted() {
    this.setThemeIncludeIndicator({ includeIndicator: true });
  },

  methods: {
    ...mapActions('themes', ['setThemeIncludeIndicator']),

    toggleSelection() {
      this.setThemeIncludeIndicator({ includeIndicator: !this.selectedTheme.includeIndicator });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.swot-chart-custom-marker-indicator {
  @include flex("block", "column", "start", "stretch");

  background-color: $emotics-bg;
  border-bottom: $border-standard;
  font-size: $font-size-sm;
  padding: 1rem 1rem;

  .checkbox {
    @include flex("block", "row", "start", "center");

    .base-checkbox {
      cursor: pointer;
      height: $font-size-lg;
      margin-right: 0.5rem;
      width: $font-size-lg;

      &:checked {
        &::after {
          font-size: 1em;
        }
      }
    }

    .icon {
      height: $font-size-base;
      margin-right: 0.5rem;
      width: $font-size-base;
    }

    .text {
      font-weight: $font-weight-bold;
    }
  }

  .label {
    @include flex("block", "row", "start", "center");
    border: $border-standard;
    border-radius: $border-radius-medium;
    font-weight: $font-weight-normal;
    margin-top: 0.6rem;
    padding: 0.6rem;

    p {
      margin: 0;
    }
  }
}
</style>
