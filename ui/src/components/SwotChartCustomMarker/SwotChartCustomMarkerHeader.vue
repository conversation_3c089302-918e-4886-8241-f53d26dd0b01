<template>
  <section class="swot-chart-custom-marker-header">
    <section class="left">
      <h3>Edit Marker for&nbsp;<b>&lsquo;{{ label }}&rsquo;</b></h3>
    </section>
    <section class="right">
      <base-button icon="trash-2" size="small" @click="onClickDelete">
        Delete Marker
      </base-button>
      <x-icon class="icon" @click="closeModal"/>
    </section>
  </section>
</template>

<script>
import { XIcon } from 'vue-feather-icons';
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import CustomChartType from '@/enum/custom-chart-type';

import { datasetsRequestV0 } from '@/services/request';

export default {
  name: 'swot-chart-custom-marker-header',

  components: {
    BaseButton,
    XIcon,
  },

  computed: {
    ...mapState('themes', ['selectedTheme']),

    label() {
      if (this.selectedTheme.topicLabel.length < 25) return this.selectedTheme.topicLabel;
      return (`${this.selectedTheme.topicLabel.substring(0, Math.min(22, this.selectedTheme.topicLabel.length))}...`);
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('themes', ['addCustomThemes']),

    ...mapActions('themes', [
      'setThemeCustomDescription',
      'setThemeCustomLabel',
      'setThemeIncludeIndicator',
      'setThemeIncludeCustomLabel',
    ]),

    async onClickDelete() {
      this.addCustomThemes({ themes: [this.selectedTheme] });

      this.setThemeCustomDescription({ customDescription: null });
      this.setThemeCustomLabel({ customLabel: null });
      this.setThemeIncludeIndicator({ includeIndicator: null });
      this.setThemeIncludeCustomLabel({ includeCustomLabel: null });

      await datasetsRequestV0.persistCustomChart(CustomChartType.SWOT);
      await datasetsRequestV0.retrieveCustomChart(CustomChartType.SWOT);

      this.$emit('delete');
      this.closeModal();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.swot-chart-custom-marker-header {
  @include flex("block", "row", "between", "center");

  border-bottom: $border-standard;
  padding: 1rem 1rem;

  .left {
    @include flex("block", "row", "start", "center");
  }

  .right {
    @include flex("block", "row", "end", "center");

    .base-button {
      background-color: clr('white');
      border: 1px solid $btn-delete-bdr;
      color: clr('red');
      margin-right: 2rem;
      text-transform: uppercase;

      &.size-small {
        font-size: 0.7rem;
        padding: 0.4rem 0.5rem 0.4rem 0;
      }

      &:hover, &:focus {
        background-color: clr('white');
        border: 1px solid clr("red");
      }

      .base-icon {
        height: 1.2em;
        margin-left: 0;
        margin-right: 0;
      }
    }

    .icon {
      cursor: pointer;
      height: 1.25rem;
      opacity: 0.3;

      &:hover {
        opacity: 1;
      }

      &:active,
      &:focus {
        border: none;
        outline: none;
      }
    }
  }
}
</style>
