<template>
  <section class="swot-chart-custom-marker-label">
    <section class="checkbox">
      <!-- Hide until changes affect label -->
      <!-- <base-checkbox :value="includeCustomLabel" @input="toggleSelection"/> -->
      <tag-icon class="icon" />
      <section class="text">Custom Label</section>
    </section>
    <section class="input">
      <h4>Title</h4>
      <base-input class="input-label" v-model="label"/>
      <h4>Description</h4>
      <section class="section-area">
        <textarea rows="3" ref="textarea"
          v-click-outside-handler="{
           handler: 'onClickOutside',
           excludedParentClasses: ['section-area'],
          }"
          v-model="descriptionArea"
          class="text-area"
          :class="{editing}"
          @focus="onFocus"
        >
        </textarea>
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { TagIcon } from 'vue-feather-icons';

import BaseCheckbox from '@/components/Base/BaseCheckbox';
import BaseInput from '@/components/Base/BaseInput';
import clickOutsideHandler from '@/directives/click-outside-handler';

export default {
  name: 'swot-chart-custom-marker-label',

  components: {
    BaseCheckbox,
    BaseInput,
    TagIcon,
  },

  directives: {
    clickOutsideHandler,
  },

  props: {
    description: {
      type: String,
      required: true,
    },
  },

  data() {
    return {
      descriptionArea: '',
      editing: false,
      labelValue: 'Illustrative Comment',
    };
  },

  computed: {
    ...mapState('swotChart', ['theme']),

    ...mapState('themes', ['selectedTheme']),

    includeCustomLabel() {
      // if (this.selectedTheme) return this.selectedTheme.includeCustomLabel;
      // return false;

      return true;
    },

    label: {
      get() {
        return this.labelValue;
      },
      set(value) {
        this.labelValue = value;
        this.setCustomLabel(value);
      },
    },
  },

  mounted() {
    if (this.selectedTheme && this.selectedTheme.customLabel) this.labelValue = this.selectedTheme.customLabel;
    else this.setThemeCustomLabel({ customLabel: this.labelValue });

    this.setThemeIncludeCustomLabel({ includeCustomLabel: true });
  },

  watch: {
    descriptionArea() {
      this.setThemeCustomDescription({ customDescription: this.descriptionArea });
    },

    description() {
      this.descriptionArea = this.description;
    },
  },

  methods: {
    ...mapActions('themes', [
      'setThemeCustomDescription',
      'setThemeCustomLabel',
      'setThemeIncludeCustomLabel',
    ]),

    onClickOutside() {
      this.$refs.textarea.scrollTop = 0;
      this.$emit('updateDescription', this.descriptionArea);
      this.editing = false;
    },

    onFocus() {
      this.editing = true;
    },

    setCustomLabel(value) {
      this.setThemeCustomLabel({ customLabel: value });
    },

    toggleSelection() {
      this.setThemeIncludeCustomLabel({ includeCustomLabel: !this.selectedTheme.includeCustomLabel });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.swot-chart-custom-marker-label {
  @include flex("block", "column", "start", "stretch");

  background-color: $emotics-bg;
  border-bottom: $border-standard;
  font-size: $font-size-sm;
  padding: 1rem 1rem;

  .checkbox {
    @include flex("block", "row", "start", "center");

    .base-checkbox {
      cursor: pointer;
      height: $font-size-lg;
      margin-right: 0.5rem;
      width: $font-size-lg;

      &:checked {
        &::after {
          font-size: 1em;
        }
      }
    }

    .icon {
      height: $font-size-base;
      margin-right: 0.5rem;
      width: $font-size-base;
    }

    .text {
      font-weight: $font-weight-bold;
    }
  }

  .input {
    @include flex("block", "column", "start", "strech");

    h4 {
      margin-top: 1rem;
    }

    .input-label {
      background-color: clr("white");
      border: $border-standard;
      border-radius: $border-radius-medium;
      margin-top: 0.4rem;
      padding: 0.6rem;
    }

    .section-area {
      @include panel;

      .text-area {
        border: none;
        color: $helper-txt-hvr;
        display: -webkit-box;
        line-height: 1.125rem;
        margin-top: 0.4rem;
        outline: none;
        overflow: hidden;
        padding-left: 0.6rem;
        padding-right: 0.6rem;
        resize: none;
        width: 100%;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;

        &.editing {
          overflow-y: auto;
          -webkit-line-clamp: unset;
        }
      }
    }
  }
}
</style>
