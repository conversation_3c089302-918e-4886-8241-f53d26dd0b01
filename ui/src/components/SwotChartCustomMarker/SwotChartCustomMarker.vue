<template>
  <section class="swot-chart-custom-marker">
    <swot-chart-custom-marker-header @delete="onDelete" />
    <swot-chart-custom-marker-indicator />
    <swot-chart-custom-marker-label @updateDescription="onUpdateDescription" :description="description" />
    <swot-chart-custom-marker-snippet-list @updateDescription="onUpdateDescription" />

    <section class="footer">
      <base-button class="cancel" colour="light" type="link" @click="closeModal">Cancel</base-button>
      <base-button class="add" @click="onClickAdd">Add Marker</base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import CustomChartType from '@/enum/custom-chart-type';
import SwotChartCustomMarkerHeader from '@/components/SwotChartCustomMarker/SwotChartCustomMarkerHeader';
import SwotChartCustomMarkerIndicator from '@/components/SwotChartCustomMarker/SwotChartCustomMarkerIndicator';
import SwotChartCustomMarkerLabel from '@/components/SwotChartCustomMarker/SwotChartCustomMarkerLabel';
import SwotChartCustomMarkerSnippetList from '@/components/SwotChartCustomMarker/SwotChartCustomMarkerSnippetList';

import { datasetsRequestV0 } from '@/services/request';

export default {
  name: 'swot-chart-custom-marker',

  components: {
    BaseButton,
    SwotChartCustomMarkerHeader,
    SwotChartCustomMarkerIndicator,
    SwotChartCustomMarkerLabel,
    SwotChartCustomMarkerSnippetList,
  },

  data() {
    return {
      description: '',
      update: false,
      initTheme: {},
    };
  },

  computed: {
    ...mapState('snippets', ['snippets']),

    ...mapState('themes', ['selectedTheme']),
  },

  created() {
    this.initTheme = Object.assign({}, this.selectedTheme);
  },

  mounted() {
    if (this.selectedTheme?.customDescription) this.description = this.selectedTheme.customDescription;
    else if (this.snippets[0]) {
      this.description = this.snippets[0]?.content || '';
      this.setThemeCustomDescription({ customDescription: this.snippets[0]?.content || '' });
    }
  },

  beforeDestroy() {
    if (!this.update) {
      this.addCustomThemes({ themes: [this.initTheme] });
    }
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('themes', ['addCustomThemes', 'setThemeCustomDescription']),

    async onClickAdd() {
      this.update = true;

      await this.addCustomThemes({ themes: [this.selectedTheme] });
      await datasetsRequestV0.persistCustomChart(CustomChartType.SWOT);

      this.closeModal();
    },

    onDelete() {
      this.update = true;
    },

    onUpdateDescription(des) {
      this.description = des;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.swot-chart-custom-marker {
  @include modal;

  overflow: hidden;
  max-height: calc(100vh - #{$header-height});

  .footer {
    border-top: 0;
    font-size: $font-size-md;
    padding: 1rem 1rem;

    .cancel {
      @include flex("block", "row", "start", "center");

      font-weight: $font-weight-normal;
      padding-left: 0;
    }

    .add {
      @include flex("block", "row", "end", "center");

      background-color: $custom-marker-bg-btn;
      margin-left: auto;
      padding: 0.6rem 1.4rem;
      font-weight: $font-weight-medium;
    }
  }
}
</style>
