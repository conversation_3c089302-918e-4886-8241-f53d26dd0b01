<template>
  <section class="swot-chart-custom-marker-snippet-list">
    <section class="title">
      <section class="text">Add Snippet to Custom Label</section>
      <arrow-up-icon class="arrow-icon" />
    </section>
    <section
        class="label"
        v-for="(snippet,index) in snippets"
        :key="index"
    >
      <section
          class="description"
          @click="onClick(snippet)"
          @mouseenter="hoverIndex = index"
          @mouseleave="hoverIndex = null"
      >
        <p>{{ snippet.content }}</p>
        <plus-icon v-if="hoverIndex === index" class="plus-icon"/>
      </section>
    </section>
  </section>
</template>

<script>
import { ArrowUpIcon, PlusIcon } from 'vue-feather-icons';
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';

export default {
  name: 'swot-chart-custom-marker-snippet-list',

  components: {
    ArrowUpIcon,
    BaseButton,
    PlusIcon,
  },

  data() {
    return {
      hoverIndex: null,
    };
  },

  computed: {
    ...mapState('snippets', ['snippets']),
  },

  methods: {
    ...mapActions('themes', ['setThemeCustomDescription']),

    onClick(snippet) {
      this.$emit('updateDescription', snippet.content);
      this.setThemeCustomDescription({ customDescription: snippet.content });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.swot-chart-custom-marker-snippet-list {
  @include flex("block", "column", "start", "stretch");

  height: 100%;
  overflow-y: auto;

  background-color: $emotics-bg;
  border-bottom: $border-standard;
  font-size: $font-size-sm;
  padding: 1rem 1rem;

  .title {
    @include flex("block", "row", "start", "center");

    padding-bottom: 1rem;

    .arrow-icon {
      height: $font-size-base;
      width: $font-size-base;
    }

    .text {
      margin-right: 0.5rem;
      font-weight: $font-weight-bold;
    }
  }

  .label {
    .description {
      @include panel;

      cursor: pointer;
      margin-bottom: 0.6rem;
      position: relative;

      p {
        display: -webkit-box;
        line-height: 1.125rem;
        max-height: 5.8rem;
        overflow: hidden;
        padding-left: 0.6rem;
        padding-right: 4rem;
        text-overflow: ellipsis;
        -webkit-line-clamp: 5;
        -webkit-box-orient: vertical;
      }

      .plus-icon {
        border: 1px solid $help-bdr;
        border-radius: 50%;
        color: $custom-marker-plus-btn;
        padding: 0.2rem;
        position: absolute;
        right: 1rem;
        top: calc(50% - 0.6rem);
      }

      &:hover {
        border: 1px solid $custom-marker-border-hover;
      }
    }
  }
}
</style>
