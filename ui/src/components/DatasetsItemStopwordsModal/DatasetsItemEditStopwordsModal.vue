<template>
  <section class="datasets-item-edit-stopwords-modal">
    <section class="header">
      <span class="text">Edit Dataset Stopwords for {{ textDatasetLabel }}</span>
    </section>
    <section class="body">
      <datasets-item-edit-workspace-stopwords v-if="workspaceStopWords.length > 0" class="workspace-stopwords" @updateWorkspaceStopWords="onUpdateWorkspaceStopWords" />
      <datasets-item-edit-dataset-stopwords @updateDatasetStopWords="onUpdateDatasetStopWords" />
    </section>
    <section class="footer">
      <base-button class="cancel-btn" colour="light" size="small" type="link" @click="onClickCancel">Cancel</base-button>
      <base-button v-if="!proceeding"
                   class="done-btn"
                   size="small"
                   @click="onClickSave"
      >
        Apply Changes & Re-analyse
      </base-button>
      <loading-blocks-overlay v-if="proceeding" />
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import DatasetsItemEditDatasetStopwords from '@/components/DatasetsItemStopwordsModal/DatasetsItemEditDatasetStopwords';
import DatasetsItemEditWorkspaceStopwords from '@/components/DatasetsItemStopwordsModal/DatasetsItemEditWorkspaceStopwords';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';

import { stopWordApi } from '@/services/api';

export default {
  name: 'datasets-item-edit-stopwords-modal',

  components: {
    BaseButton,
    DatasetsItemEditDatasetStopwords,
    DatasetsItemEditWorkspaceStopwords,
    LoadingBlocksOverlay,
  },

  data() {
    return {
      proceeding: false,
    };
  },

  async mounted() {
    await stopWordApi.fetch(this.activeDataset);
    const stopWords = [...this.getStopWords(this.activeDataset)];
    this.setLocal({ id: this.activeDataset, stopWords });
  },

  computed: {
    ...mapGetters('datasets', {
      getDataset: 'get',
    }),

    ...mapGetters('stopWords', {
      getStopWords: 'get',
      getLocalStopWords: 'getLocal',
    }),

    ...mapState('stopWords', ['activeDataset']),

    dataset() {
      return this.getDataset(this.activeDataset);
    },

    datasetStopWords() {
      return this.getLocalStopWords(this.activeDataset)?.filter(s => s.workspaceId === null) || [];
    },

    textDatasetLabel() {
      return this.dataset.label?.trim();
    },

    workspaceStopWords() {
      return this.getLocalStopWords(this.activeDataset)?.filter(s => s.workspaceId !== null) || [];
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('stopWords', ['setActiveDataset', 'setLocal']),

    onClickCancel() {
      this.setActiveDataset({ id: null });
      this.closeModal();
    },

    async onClickSave() {
      if (this.proceeding) {
        return;
      }

      this.proceeding = true;

      let stopWords = this.getLocalStopWords(this.activeDataset) || [];
      stopWords = stopWords.map(s => {
        return {
          workspaceId: s.workspaceId,
          stopWord: s.stopWord,
        };
      });
      await stopWordApi.submit(this.activeDataset, stopWords);

      this.setActiveDataset({ id: null });
      this.closeModal();
    },

    onUpdateWorkspaceStopWords(workspaceStopWords) {
      this.setLocal({ id: this.activeDataset, stopWords: [...this.datasetStopWords, ...workspaceStopWords] });
    },

    onUpdateDatasetStopWords(datasetStopWords) {
      this.setLocal({ id: this.activeDataset, stopWords: [...datasetStopWords, ...this.workspaceStopWords] });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.datasets-item-edit-stopwords-modal {
  @include panel;
  position: relative;
  width: 600px;

  .header {
    @include flex('block', 'row', 'start', 'center');
    border-bottom: $border-light solid $border-color;

    .text {
      @include truncate;
      color: rgba(45, 23, 87, 1);
      font-size: $font-size-sm;
      font-weight: $font-weight-bold;
      padding: 1.5rem 1.5rem 1rem;
    }
  }

  .body {
    @include flex('block', 'column', 'start', 'start');
    @include scrollbar-thin;
    max-height: 450px;
    overflow-y: auto;
    padding: 1rem 1.5rem;

    .workspace-stopwords {
      margin-bottom: 1rem;
    }
  }

  .footer {
    @include flex('block', 'row', 'between', 'center');
    @include rigid;
    border-top: $border-light solid $border-color;
    padding: 1.5rem 1.5rem;

    .base-button {
      font-size: 0.65em;
      font-weight: 600;
      text-transform: uppercase;

      &.cancel-btn {
        padding: 0.5rem 1rem 0.5rem 0;
      }

      &.done-btn {
        background: #2D1757;
        padding: 0.5rem 1.5rem;
      }
    }

    .loading-blocks-overlay {
      height: 28px;
    }
  }
}
</style>
