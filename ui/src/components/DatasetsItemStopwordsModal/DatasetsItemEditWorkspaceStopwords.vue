<template>
  <section class="datasets-item-edit-workspace-stopwords">
    <section class="label">
      Workspace stop words (defined by org admin)
    </section>
    <section class="input">
      <stop-words-input :delete-only="isEditableStopWords" :read-only="!isEditableStopWords" :stop-words="workspaceStopWordsText" @update="onUpdateWorkspaceStopWords" />
    </section>
  </section>
</template>

<script>
import { mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import StopWordsInput from '@/components/StopWords/StopWordsInput';

export default {
  name: 'datasets-item-edit-workspace-stopwords',

  components: {
    BaseButton,
    LoadingBlocksOverlay,
    StopWordsInput,
  },

  data() {
    return {
    };
  },

  computed: {
    ...mapGetters('stopWords', {
      getLocalStopWords: 'getLocal',
    }),

    ...mapState('stopWords', ['activeDataset']),

    ...mapState('user', ['activeWorkspace']),

    isEditableStopWords() {
      return this.activeWorkspace.settings?.allowToEditStopwords;
    },

    workspaceStopWords() {
      return this.getLocalStopWords(this.activeDataset)?.filter(s => s.workspaceId !== null) || [];
    },

    workspaceStopWordsText() {
      return this.workspaceStopWords.map(s => s.stopWord);
    },
  },

  methods: {
    onUpdateWorkspaceStopWords(stopWordsText) {
      const workspaceStopWords = stopWordsText.map(s => {
        return {
          stopWord: s,
          workspaceId: this.activeWorkspace.id,
        };
      });
      this.$emit('updateWorkspaceStopWords', workspaceStopWords);
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.datasets-item-edit-workspace-stopwords {
  width: 100%;

  .label {
    color: rgba(95, 82, 197, 1);
    font-size: 12px;
    font-weight: $font-weight-bold;
    margin-bottom: 1rem;
    text-transform: uppercase;
  }

  .input {
    @include scrollbar-thin;
    max-height: 8rem;
    overflow-y: auto;
    width: 100%;
  }
}
</style>
