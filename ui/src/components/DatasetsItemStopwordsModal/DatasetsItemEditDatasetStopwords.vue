<template>
  <section class="datasets-item-edit-dataset-stopwords">
    <section class="label">
      Add Stop Words
    </section>
    <section class="input">
      <stop-words-input :read-only="false" :stop-words="datasetStopWordsText" @update="onUpdateDatasetStopWords" />
    </section>
    <section class="description">
      <p>
        Enter or paste in your stop words with each tag separated by a comma, or
        <span class="stop-word-list" @click="onClickAddList">add a stop word list</span>.
      </p>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import StopWordsInput from '@/components/StopWords/StopWordsInput';
import StopWordsModal from '@/components/StopWords/StopWordsModal';

export default {
  name: 'datasets-item-edit-dataset-stopwords',

  components: {
    BaseButton,
    LoadingBlocksOverlay,
    StopWordsInput,
  },

  data() {
    return {
    };
  },

  computed: {
    ...mapGetters('stopWords', {
      getLocalStopWords: 'getLocal',
    }),

    ...mapState('stopWords', ['activeDataset']),

    datasetStopWords() {
      return this.getLocalStopWords(this.activeDataset)?.filter(s => s.workspaceId === null) || [];
    },

    datasetStopWordsText() {
      return this.datasetStopWords.map(s => s.stopWord);
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal', 'setModalComponent']),

    onClickAddList() {
      this.closeModal();
      this.setModalComponent({ component: StopWordsModal });
    },

    onUpdateDatasetStopWords(stopWordsText) {
      const datasetStopWords = stopWordsText.map(s => {
        return {
          stopWord: s,
          workspaceId: null,
        };
      });
      this.$emit('updateDatasetStopWords', datasetStopWords);
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.datasets-item-edit-dataset-stopwords {
  width: 100%;

  .label {
    color: rgba(95, 82, 197, 1);
    font-size: 12px;
    font-weight: $font-weight-bold;
    margin-bottom: 1rem;
    text-transform: uppercase;
  }

  .input {
    @include scrollbar-thin;
    max-height: 8rem;
    overflow-y: auto;
    width: 100%;
  }

  .description {
    @include flex('block', 'column', 'start', 'stretch');
    margin-top: 1rem;

    p {
      color: rgba(45, 23, 87, 1);
      font-size: 12px;

      .stop-word-list {
        cursor: pointer;
        font-weight: $font-weight-bold;
        text-decoration: underline;
      }
    }
  }
}
</style>
