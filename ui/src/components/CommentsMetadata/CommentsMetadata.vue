<template>
  <section class="comments-metadata">
    <comments-metadata-item v-for="(item, i) in sortedAndFilteredList"
                            :key="i"
                            :metadata-obj="item"
    />
  </section>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import CommentsMetadataItem from '@/components/CommentsMetadata/CommentsMetadataItem';

export default {
  name: 'comments-metadata',

  components: {
    CommentsMetadataItem,
  },

  props: {
    comment: {
      type: Object,
      required: true,
    },
    datasetId: {
      type: Number,
      required: false,
    },
  },

  computed: {
    ...mapGetters('snippets', ['getFilterMetadataList']),

    ...mapState('datasets', ['active']),

    ...mapState('snippets', ['viewMetadataOpts']),

    dataList() {
      const rs = [];
      const { metadataColumns, metadataHeaders, metadataTypes } = this.getFilterMetadataList({ datasetId: this.datasetId || this.active });

      if (
        !metadataColumns.length
        || !metadataHeaders.length
        || !metadataTypes.length
        || !this.comment.metadata?.length
      ) {
        return rs;
      }

      for (let i = 0; i < this.comment.metadata.length; i += 1) {
        // ignore comment content: -1
        if (metadataColumns[i] !== -1) {
          rs.push({
            column: metadataColumns[i],
            isSortedBy: this.getActiveStatus(metadataColumns[i]),
            name: metadataHeaders[i],
            type: metadataTypes[i],
            val: this.comment.metadata[i],
          });
        }
      }

      return rs;
    },

    hideList() {
      return this.viewMetadataOpts.hide;
    },

    sortedAndFilteredList() {
      const rs = [...this.dataList];
      if (this.viewingOrder?.length) {
        rs.sort((a, b) => this.viewingOrder.indexOf(a.column) - this.viewingOrder.indexOf(b.column));
      }

      return rs.filter(item => {
        return !this.hideList.includes(item.column)
            && item.val?.trim().length;
      });
    },

    viewingOrder() {
      return this.viewMetadataOpts.viewingOrder;
    },
  },

  methods: {
    getActiveStatus(column) {
      return this.viewMetadataOpts.sortBy === column;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-metadata {
  display: flex;
  flex-flow: row wrap;
  width: 100%;

  .comments-metadata-item {
    margin-right: 0.5rem;
    margin-top: 0.2rem;

    &:last-child {
      margin-right: 0;
    }
  }
}
</style>
