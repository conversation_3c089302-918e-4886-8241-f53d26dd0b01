<template>
  <section class="comments-metadata-control-view-and-order"
           v-click-outside-handler="{ handler: 'onClickOutside' }">
    <section class="view-and-order-button"
             @click="onClickButton"
             :class="{ active: hasDisplayedMetadata, open }"
    >
      <span class="text">{{ buttonText }}</span>
      <i class="fa fa-chevron-up icon"></i>
    </section>

    <section class="view-and-order-dropdown" :class="{ hide: !open }">
      <common-sortable-list class="common-sortable-list"
                            @sorting="onSort"
                            :key="datasetId"
      >
        <section class="list">
          <common-sortable-item v-for="(item, i) in sortedList"
                                :key="i"
                                class="common-sortable-item"
          >
            <section class="list-item">
              <comments-metadata-control-view-and-order-item :data="item" @selecting="onSelect"/>
              <common-sortable-handle>
                <i class="fa fa-bars icon"
                   v-tooltip.right="{
                     content: 'Drag & Drop to re-order metadata.',
                     class: 'tooltip-base-dark',
                     delay: 0,
                   }"
                />
              </common-sortable-handle>
            </section>
          </common-sortable-item>
        </section>
      </common-sortable-list>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';
import clickOutsideHandler from '@/directives/click-outside-handler';
import CommentsMetadataControlViewAndOrderItem from '@/components/CommentsMetadata/CommentsMetadataControlViewAndOrderItem';
import CommonSortableHandle from '@/components/CommonSortableList/CommonSortableHandle';
import CommonSortableItem from '@/components/CommonSortableList/CommonSortableItem';
import CommonSortableList from '@/components/CommonSortableList/CommonSortableList';

export default {
  name: 'comments-metadata-control-view-and-order',

  components: {
    CommentsMetadataControlViewAndOrderItem,
    CommonSortableHandle,
    CommonSortableItem,
    CommonSortableList,
  },

  directives: {
    clickOutsideHandler,
  },

  computed: {
    ...mapGetters('snippets', ['getFilterMetadataList']),

    ...mapState('datasets', {
      datasetId: 'active',
    }),

    ...mapState('snippets', ['viewMetadataOpts']),

    buttonText() {
      return 'Metadata';
    },

    dataList() {
      const rs = [];
      const { metadataColumns, metadataHeaders, metadataTypes } = this.getFilterMetadataList({ datasetId: this.datasetId });

      if (
        !metadataColumns.length
        || !metadataHeaders.length
        || !metadataTypes.length
      ) {
        return rs;
      }

      for (let i = 0; i < metadataColumns.length; i += 1) {
        // note: may user wanna name their metadata-column as 'Comments'?
        if (metadataColumns[i] !== -1) {
          rs.push({
            active: !this.viewMetadataOpts.hide.includes(metadataColumns[i]),
            column: metadataColumns[i],
            name: metadataHeaders[i],
            type: metadataTypes[i],
          });
        }
      }

      return rs;
    },

    hasDisplayedMetadata() {
      if (!this.dataList?.length) {
        return false;
      }
      return this.dataList.length > this.viewMetadataOpts.hide.length;
    },

    sortedList() {
      const rs = [...this.dataList];
      // sort 1 time only, on initiation
      if (this.firstInit && this.viewingOrder?.length) {
        rs.sort((a, b) => this.viewingOrder.indexOf(a.column) - this.viewingOrder.indexOf(b.column));
      }
      this.firstInit = false;
      return rs;
    },

    viewingOrder() {
      return this.viewMetadataOpts.viewingOrder;
    },
  },

  data() {
    return {
      firstInit: true,
      open: false,
    };
  },

  methods: {
    ...mapActions('snippets', ['editMetadataViewingOrder', 'toggleViewMetadata']),

    hideAllMetadata() {
      // hide all metadata by default
      const rs = [...this.dataList];
      rs.filter(item => !this.viewMetadataOpts.hide.includes(item.column))
        .forEach(item => {
          this.toggleViewMetadata({ columnNumber: item.column });
        });
    },

    onClickButton() {
      this.open = !this.open;
    },

    onClickOutside() {
      this.open = false;
    },

    onSelect(item) {
      this.toggleViewMetadata({ columnNumber: item.column });
    },

    onSort({ oldIndex, newIndex }) {
      this.editMetadataViewingOrder({ oldIndex, newIndex });
    },
  },

  mounted() {
    this.hideAllMetadata();
  },

  watch: {
    datasetId() {
      this.firstInit = true;
      this.hideAllMetadata();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-metadata-control-view-and-order {
  @include flex("block", "column", "center", "start");
  position: relative;

  .view-and-order-button {
    @include flex("block", "row", "start", "center");
    cursor: pointer;

    &.active, &:hover {
      color: clr("purple");
    }

    &.open {
      color: clr("purple");

      .icon {
        transform: rotateX(180deg);
      }
    }

    .text {
      font-size: $font-size-xs;
      font-weight: $font-weight-medium;
      text-transform: uppercase;
    }

    .icon {
      font-size: 10px;
      margin-left: 0.5rem;
      padding-bottom: 2px;
      transition: all $interaction-transition-time;
    }
  }

  .view-and-order-dropdown {
    @include flex("block", "column", "start", "start");
    background-color: clr("white");
    border: 1px solid $border-color;
    border-radius: $border-radius-medium;
    box-shadow: 0 0 3px 3px rgba(0, 0, 0, 0.05);
    max-height: 400px;
    overflow-x: hidden;
    overflow-y: auto;
    position: absolute;
    right: 0;
    top: 1.6rem;
    width: max-content;
    z-index: 2;

    &.hide {
      display: none;
    }

    .list {
      @include flex("block", "column", "start", "stretch");
      background-color: clr("whtie");
      padding-right: 0.2rem;
      width: inherit;

      .list-item {
        @include flex("block", "row", "between", "center");
        cursor: pointer;
        padding: 0.5rem 1rem;

        &:hover {
          background-color: $list-item-hover-color;
        }

        .sortable-handle {
          margin-left: 1rem;

          &.icon {
            font-size: 10px;
          }
        }
      }
    }
  }
}
</style>
