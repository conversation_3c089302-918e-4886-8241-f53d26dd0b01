<template>
  <section class="comments-metadata-item"
           :class="{ active: metadataObj.isSortedBy }"
           v-tooltip.bottom="{
                     content: textVal,
                     class: 'tooltip-base-metadata-item',
                     delay: 0,
                   }"
  >
    <span class="item-name">{{ textName }}</span>
    <span class="item-separator">:</span>
    <span class="item-val">{{ textVal }}</span>
  </section>
</template>

<script>
import { format, parseISO } from 'date-fns';
import CommentsFilterColumn from '@/enum/comments-filter-column';

export default {
  name: 'comments-metadata-item',

  props: {
    metadataObj: {
      type: Object,
      required: true,
    },
  },

  computed: {
    textName() {
      return this.metadataObj.name;
    },

    textVal() {
      if (this.metadataObj.type === CommentsFilterColumn.DATE.name) {
        return format(parseISO(this.metadataObj.val), 'do MMM yyyy');
      }
      return this.metadataObj.val;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-metadata-item {
  @include flex("block", "row", "start", "center");
  background-color: clr("white");
  border: 1px solid $border-color;
  border-radius: $border-radius-medium;
  cursor: default;
  font-size: 0.67rem;
  padding: 0.2rem 0.5rem 0.1rem;

  &.active {
    border: 1px solid clr("purple");

    .item-name {
      color: clr("purple");
    }
  }

  &:hover {
    background-color: clr("grey", "90");
  }

  .item-name {
    font-weight: $font-weight-bold;
    max-width: 10rem;
    overflow: hidden;
    text-overflow: ellipsis;
    text-transform: uppercase;
    white-space: nowrap;
  }

  .item-separator {
    font-weight: $font-weight-bold;
  }

  .item-val {
    margin-left: 0.2rem;;
    max-width: 10rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
