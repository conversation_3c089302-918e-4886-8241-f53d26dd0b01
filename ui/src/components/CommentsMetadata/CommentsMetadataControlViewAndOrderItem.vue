<template>
  <section class="comments-metadata-control-view-and-order-item" @click="onSelect">
    <base-checkbox :value="data.active"/>
    <span class="text">{{ textName }}</span>
  </section>
</template>

<script>
import BaseCheckbox from '@/components/Base/BaseCheckbox';

export default {
  name: 'comments-metadata-control-view-and-order-item',

  components: {
    BaseCheckbox,
  },

  props: {
    data: {
      type: Object,
      required: true,
    },
  },

  computed: {
    textName() {
      return this.data.name;
    },
  },

  methods: {
    onSelect() {
      this.$emit('selecting', this.data);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.comments-metadata-control-view-and-order-item {
  @include flex("block", "row", "start", "center");
  font-size: $font-size-xxs;
  text-transform: uppercase;

  .text {
    margin-left: 0.5rem;
  }
}
</style>
