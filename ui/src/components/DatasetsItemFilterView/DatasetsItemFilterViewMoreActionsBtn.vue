<template>
  <section v-if="isEditor"
    class="datasets-item-filter-view-more-actions-btn"
    v-click-outside-handler="{ handler: 'onCloseDropdown' }"
  >
    <i class="fa-solid fa-ellipsis dropdown-icon" @click="onClickDropdown" />
    <datasets-item-filter-view-more-actions-dropdown :item="item" v-if="showDropdown"
                                         @closeDropdown="onCloseDropdown"/>
  </section>
</template>

<script>
import { mapGetters } from 'vuex';

import clickOutsideHandler from '@/directives/click-outside-handler';
import DatasetsItemFilterViewMoreActionsDropdown from '@/components/DatasetsItemFilterView/DatasetsItemFilterViewMoreActionsDropdown';

export default {
  name: 'datasets-item-filter-view-more-actions-btn',

  components: {
    DatasetsItemFilterViewMoreActionsDropdown,
  },

  props: {
    item: {
      type: Object,
      required: true,
    },
  },

  directives: {
    clickOutsideHandler,
  },

  data() {
    return {
      showDropdown: false,
    };
  },

  computed: {
    ...mapGetters('datasets', ['isEditable']),

    isEditor() {
      return this.isEditable(this.item.metadataFilterList.parentId);
    },
  },

  methods: {
    onClickDropdown() {
      this.showDropdown = !this.showDropdown;
    },

    onCloseDropdown() {
      this.showDropdown = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.datasets-item-filter-view-more-actions-btn {
  @include flex("block", "row", "end", "center");

  margin-left: auto;
  position: relative;
  width: fit-content;

  .dropdown-icon {
    border: 1px solid transparent;
    border-radius: 3px;
    cursor: pointer;
    padding: 0 5px;

    &:hover {
      border: 1px solid rgba(191, 191, 191, 0.8);
    }
  }
}
</style>
