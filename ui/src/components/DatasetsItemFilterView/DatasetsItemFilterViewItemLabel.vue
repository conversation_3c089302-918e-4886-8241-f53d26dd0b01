<template>
  <section class="dataset-item-filter-view-item-label">
<!--    <base-checkbox :value="isSelected" @input="onSelectCheckbox" />-->
    <section class="label-and-info">
      <span class="label" @click="onClickFilter">{{item.metadataFilterList.label}}</span>
      <section v-if="showCommentCount" class="comment-count">
        <i class="fa-thin fa-comment icon" />
        <span>{{commentCount}}</span>
      </section>
    </section>
    <datasets-item-filter-view-split-count v-if="splitCount" :count="splitCount" @clickSplit="onClickSplit" />
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseCheckbox from '@/components/Base/BaseCheckbox';
import CommentsFilterOperator from '@/enum/comments-filter-operator';
import DatasetsItemFilterViewSplitCount from '@/components/DatasetsItemFilterView/DatasetsItemFilterViewSplitCount';
import Route from '@/enum/route';

import { metadataRequest } from '@/services/request';

export default {
  name: 'dataset-item-filter-view-item-label',

  components: {
    BaseCheckbox,
    DatasetsItemFilterViewSplitCount,
  },

  props: {
    item: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapState('datasets', ['selected']),

    commentCount() {
      return this.item.dataset.documentCount;
    },

    isSelected() {
      return false;
    },

    showCommentCount() {
      return this.item.metadataFilterList.filterList.length > 0;
    },

    splitCount() {
      const { splitBy } = this.item.metadataFilterList;

      if (splitBy) {
        const { textComparisonValues, dateComparisonValues, numericComparisonValues } = splitBy;

        if (textComparisonValues) return textComparisonValues.length;
        if (dateComparisonValues) return dateComparisonValues.length;
        if (numericComparisonValues) return numericComparisonValues.length;
      }

      return 0;
    },
  },

  methods: {
    ...mapActions('datasets', [
      'select',
      'setActive',
      'setActiveViewing',
    ]),

    ...mapActions('snippets', {
      resetSnippet: 'reset',
    }),

    ...mapActions('snippetsFilter', [
      'setLoadingFilters',
      'setLoadingFiltersList',
      'setMetadataFilterList',
      'setSavedFilter',
      'setShowSplitPanel',
    ]),

    navigateToFilterView() {
      // Set Parent Dataset
      this.resetSnippet();
      if (!this.selected.includes(this.item.metadataFilterList.parentId)) {
        this.select({ ids: [this.item.metadataFilterList.parentId] });
      }
      this.setActive({ id: this.item.metadataFilterList.parentId });
      this.setActiveViewing({ id: this.item.dataset.id });

      // Set Filter View
      // TODO: should do refactor the below fragment code
      this.setLoadingFilters({ value: true });
      const filters = [];
      this.item.metadataFilterList.filterList.forEach(filterList => {
        const filterString = filterList.uiMappingObj;
        const filter = JSON.parse(filterString);
        filter.column.defaultOperator = CommentsFilterOperator.enumValueOf(filter.column.defaultOperator.name);
        filter.column.operators = filter.column.operators.map(o => CommentsFilterOperator.enumValueOf(o.name));
        filter.operator = CommentsFilterOperator.enumValueOf(filter.operator.name);
        filters.push(filter);
      });
      this.setMetadataFilterList({ value: this.item.metadataFilterList });
      this.setSavedFilter({ filter: this.item });
      this.setLoadingFiltersList({ value: filters });

      // Navigate
      this.$router.push({
        name: Route.COMMENTS,
        query: {
          ids: this.selected.join(','),
        },
      });
    },

    async onClickFilter() {
      if (this.splitCount) {
        await metadataRequest.getSplitFilterViews(this.item.metadataFilterList.parentId, this.item.metadataFilterList.id);
        this.navigateToFilterView();
      } else {
        this.navigateToFilterView();
      }
    },

    async onClickSplit() {
      await metadataRequest.getSplitFilterViews(this.item.metadataFilterList.parentId, this.item.metadataFilterList.id);
      this.navigateToFilterView();
      this.setShowSplitPanel({ value: true });
    },

    onSelectCheckbox() {
      // TODO
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.dataset-item-filter-view-item-label {
  @include flex("block", "row", "start", "center");

  min-width: 300px;
  width: 300px;

  .base-checkbox {
    margin-left: 0.2rem;
    margin-right: 0.6rem;
  }

  .label {
    @include truncate;

    color: #2D1757;
    cursor: pointer;
    font-size: $font-size-sm;
    font-weight: $font-weight-bold;
    max-width: 280px;
    word-wrap: break-word;

    &:hover {
      color: $selection-blue-dark;
      text-decoration: underline;
    }
  }

  .comment-count {
    color: #2D1757;
    font-size: 11px;
    font-weight: $font-weight-normal;
    margin-top: 0.5rem;
  }

  .datasets-item-filter-view-split-count {
    margin-left: auto;
    margin-right: 0.4rem;
  }
}
</style>
