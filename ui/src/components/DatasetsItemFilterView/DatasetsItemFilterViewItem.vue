<template>
  <section class="dataset-item-filter-view-item">
    <i class="fa-solid fa-filter icon-filter"></i>
    <datasets-item-filter-view-item-label class="item-gap-large" :item="item" />
    <datasets-item-adorescore class="item-same-size item-gap-large" :score="item.dataset.summary.adoreScore" />
    <datasets-item-insight-btn class="item-same-size item-gap-large" :id="item.dataset.id" />
    <datasets-item-filter-view-more-actions-btn :item="item" />
  </section>
</template>

<script>
import DatasetsItemAdorescore from '@/components/DatasetsItem/DatasetsItemAdorescore';
import DatasetsItemFilterViewItemLabel from '@/components/DatasetsItemFilterView/DatasetsItemFilterViewItemLabel';
import DatasetsItemFilterViewMoreActionsBtn from '@/components/DatasetsItemFilterView/DatasetsItemFilterViewMoreActionsBtn';
import DatasetsItemInsightBtn from '@/components/DatasetsItem/DatasetsItemInsightBtn';

export default {
  name: 'dataset-item-filter-view-item',

  components: {
    DatasetsItemAdorescore,
    DatasetsItemFilterViewItemLabel,
    DatasetsItemFilterViewMoreActionsBtn,
    DatasetsItemInsightBtn,
  },

  props: {
    item: {
      type: Object,
      required: true,
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.dataset-item-filter-view-item {
  @include flex("block", "row", "start", "center");

  border-bottom: 1px solid $border-color;
  border-left: 1px solid rgba(165, 159, 217, 1);
  border-right: 1px solid rgba(165, 159, 217, 1);
  font-size: $font-size-base;
  min-width: 600px;
  padding: 1rem 1rem;
  position: relative;
  width: 100%;

  &:last-child {
    border-bottom-left-radius: 3px;
    border-bottom-right-radius: 3px;
    border-bottom: 1px solid rgba(165, 159, 217, 1);
  }

  .icon-filter {
    margin-right: 0.8rem;
  }

  .item-same-size {
    min-width: 100px;
    width: 100px;
  }

  .item-gap-large {
    margin-right: 35px;
  }
}
</style>
