<template>
  <section class="delete-filter-views-modal">
    <section class="header">
      <h2>Delete Filter</h2>
    </section>
    <section class="body">
      <span>The following filter will be deleted:</span>

      <section class="list">
        <delete-filter-views-modal-item v-for="(item, index) in toDelete" :key="index" :item="item"></delete-filter-views-modal-item>
      </section>

      <span>Deleting a filter cannot be undone. Are you sure you want to proceed?</span>
    </section>
    <section class="footer">
      <base-button class="cancel" colour="light" type="link" @click="onCancel">Cancel</base-button>
      <loading-blocks-overlay size="small" v-if="deleting" />
      <base-button v-else class="delete" colour="danger" @click="onDelete">
        <span>Delete</span>
      </base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import DeleteFilterViewsModalItem from '@/components/DatasetsItemFilterView/DeleteFilterViewsModalItem';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';

import { datasetsRequestV0, metadataRequest } from '@/services/request';

export default {
  name: 'delete-filter-views-modal',

  components: {
    BaseButton,
    DeleteFilterViewsModalItem,
    LoadingBlocksOverlay,
  },

  data() {
    return {
      deleting: false,
    };
  },

  computed: {
    ...mapState('snippetsFilter', ['toDelete']),
  },

  methods: {
    ...mapActions('snippetsFilter', ['setToDelete']),

    ...mapActions('modal', ['closeModal']),

    ...mapActions('toast', ['add']),

    onCancel() {
      this.setToDelete({ filters: [] });
      this.closeModal();
    },

    async onDelete() {
      this.deleting = true;

      const filter = this.toDelete[0];

      await metadataRequest.deleteFilterViewById(filter.metadataFilterList.parentId, filter.metadataFilterList.id);
      await datasetsRequestV0.getDatasets();
      await metadataRequest.getFilterViews(filter.metadataFilterList.parentId);
      // TODO: add toast here

      this.deleting = false;

      this.closeModal();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.delete-filter-views-modal {
  @include modal;

  .body {
    @include flex("block", "column", "start", "stretch");

    span {
      font-size: $font-size-sm;
      margin: 0.5rem 0;
    }

    .list {
      overflow: auto;
    }
  }
}
</style>
