<template>
  <section class="datasets-item-filter-view-split-count" @click="onClickSplit">
    <i class="fa-solid fa-split icon-split"></i>
    <span class="text">{{count}}</span>
  </section>
</template>

<script>
export default {
  name: 'datasets-item-filter-view-split-count',

  props: {
    count: {
      type: Number,
      required: true,
    },
  },

  methods: {
    async onClickSplit() {
      this.$emit('clickSplit');
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.datasets-item-filter-view-split-count {
  @include flex("block", "row", "start", "center");

  color: #544B83;
  cursor: pointer;
  font-size: $font-size-xs;
  transition: all $interaction-transition-time;

  &:hover {
    font-weight: $font-weight-bold;
  }

  .text {
    padding: 0 0.2rem;
  }
}
</style>
