<template>
  <section class="dataset-item-filter-view">
    <datasets-item-filter-view-item class="item" v-for="(item, index) in sortedFilterViews" :item="item" :key="index" />
  </section>
</template>

<script>
import DatasetsItemFilterViewItem from '@/components/DatasetsItemFilterView/DatasetsItemFilterViewItem';

export default {
  name: 'dataset-item-filter',

  components: {
    DatasetsItemFilterViewItem,
  },

  props: {
    dataset: {
      type: Object,
      required: true,
    },
  },

  computed: {
    sortedFilterViews() {
      return this.dataset.filterViews.sort((a, b) => a.metadataFilterList.label.localeCompare(b.metadataFilterList.label));
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.dataset-item-filter-view {
  min-width: 600px;
  position: relative;
  width: 100%;
}
</style>
