<template>
  <section class="datasets-item-filter-view-more-actions-dropdown">
    <section class="dropdown-arrow"></section>
    <section class="button" @click="onClickDelete">
      <i class="fa-solid fa-trash-can icon"></i>
      <span class="text">Delete Filter</span>
    </section>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import BlurCloseable from '@/components/Mixins/BlurCloseable';
import DeleteFilterViewsModal from '@/components/DatasetsItemFilterView/DeleteFilterViewsModal';

export default {
  name: 'datasets-item-filter-view-more-actions-dropdown',

  mixins: [BlurCloseable],

  props: {
    item: {
      type: Object,
      required: true,
    },
  },

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    ...mapActions('snippetsFilter', ['setToDelete']),

    onCloseDropdown() {
      this.$emit('closeDropdown');
    },

    onClickDelete() {
      this.setToDelete({ filters: [this.item] });
      this.setModalComponent({ component: DeleteFilterViewsModal });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.datasets-item-filter-view-more-actions-dropdown {
  @include flex("block", "column", "start", "stretch");
  @include panel;

  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
  position: absolute;
  right: -3px;
  top: 25px;
  width: 230px;
  z-index: 2;

  .dropdown-arrow {
    border-bottom: 6px solid #dee1e4;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: none;
    position: absolute;
    right: 9px;
    top: -7px;
  }

  .button {
    @include flex("block", "row", "start", "center");

    border-radius: $border-radius-medium;
    color: clr('red');
    cursor: pointer;
    padding: 0.7rem 1rem;
    transition: all $interaction-transition-time;
    width: 100%;

    .icon {
      font-size: 14px;
      margin-right: 0.5rem;
    }

    .text {
      font-size: 0.7rem;
      font-weight: $font-weight-medium;
      position: relative;
    }

    &:hover {
      color: clr('white');
      background-color: clr("red");
    }
  }
}
</style>
