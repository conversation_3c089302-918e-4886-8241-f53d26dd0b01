<template>
  <section class="delete-filter-views-modal-item">
    <span class="score">
      <span>{{ item.dataset.summary.adoreScore }}</span>
    </span>
    <span class="label">{{ item.metadataFilterList.label }}</span>
  </section>
</template>

<script>
export default {
  name: 'delete-filter-views-modal-item',

  props: {
    item: {
      type: Object,
      required: true,
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.delete-filter-views-modal-item {
  @include flex("block", "row", "start", "center");
  @include panel;

  margin-bottom: 0.5rem;
  padding: 0.5rem;

  .score {
    @include flex("block", "row", "center", "center");
    @include rigid;

    background-color: clr("blue", "lighter");
    border-radius: $border-radius-large;
    font-size: $font-size-xs;
    height: 1.7rem;
    margin-right: 0.6rem;
    width: 1.7rem;
  }

  .label {
    @include stretch;
    @include truncate;

    font-weight: $font-weight-medium;
    margin-right: 0.5rem;
  }
}
</style>
