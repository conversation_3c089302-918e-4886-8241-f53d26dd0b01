<template>
  <section class="filter-panel-footer-create-dropdown" @click="onClickCreate">
    <section class="dropdown-arrow"></section>
    <span>Create New Dataset from Filters</span>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import CommentsFiltersCreateNewDataset from '@/components/CommentsFilters/CommentsFiltersCreateNewDataset';

export default {
  name: 'filter-panel-footer-create-dropdown',

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    onClose() {
      this.$emit('close');
    },

    onClickCreate() {
      this.setModalComponent({ component: CommentsFiltersCreateNewDataset });
      this.$emit('close');
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.filter-panel-footer-create-dropdown {
  @include flex("block", "column", "start", "stretch");

  background-color: clr("white");
  border-radius: $border-radius-medium;
  box-shadow: 0 2px 4px 0 rgba(clr("black"), 0.6);
  color: $nps-blue;
  cursor: pointer;
  font-size: 11px;
  font-weight: $font-weight-normal;
  left: -36px;
  padding: 0.6rem;
  position: absolute;
  top: 36px;
  white-space: nowrap;
  width: fit-content;
  z-index: 999;

  &:hover {
    background-color: clr('purple', 'lighter');
  }

  .dropdown-arrow {
    border-bottom: 7px solid clr("white");
    border-left: 7px solid transparent;
    border-right: 7px solid transparent;
    height: 0;
    position: absolute;
    right: 92px;
    top: -7px;
    width: 0;
  }
}
</style>
