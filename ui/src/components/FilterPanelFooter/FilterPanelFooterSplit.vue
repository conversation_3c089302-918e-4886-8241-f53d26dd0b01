<template>
  <section class="filter-panel-footer-split" @click="onClickSplit" >
    <i class="fa-solid fa-split icon-split"></i>
    <span class="text">Split</span>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import FilterSplitModal from '@/components/FilterSplitModal/FilterSplitModal';

export default {
  name: 'filter-panel-footer-split',

  computed: {
    ...mapGetters('snippets', ['getFilterValidatedList']),

    ...mapState('snippets', [
      'filters',
      'filtersApplied',
      'filterUndefined',
    ]),

    hasErrors() {
      return this.filters.filter(o => o.column).length !== this.getFilterValidatedList.length;
    },
  },

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    ...mapActions('snippets', [
      'applyFilters',
      'deselectAllSnippets',
      'resetSnippets',
      'setFiltersAttempted',
    ]),

    async onClickSplit() {
      if (this.hasErrors) {
        this.setFiltersAttempted({ value: true });
      } else {
        this.deselectAllSnippets();
        this.resetSnippets();
        this.setFiltersAttempted({ value: false });
        this.applyFilters();
        this.setModalComponent({ component: FilterSplitModal });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.filter-panel-footer-split {
  @include flex("block", "row", "start", "center");

  background-color: clr('white');
  border-radius: 2px;
  color: #6149ED;
  cursor: pointer;
  padding: 0.4rem 0.6rem;
  text-transform: uppercase;

  .icon-split {
    font-size: $font-size-xxs;
    margin-right: 0.2rem;
  }

  &:hover {
    background-color: rgba(clr("white"), 0.8);
  }
}
</style>
