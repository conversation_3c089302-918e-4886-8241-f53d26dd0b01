<template>
  <section class="filter-panel-footer-create" v-click-outside-handler="{ handler: 'onCloseDropdown' }">
    <section class="btn-create" @click="open = !open" :class="{open}">
      <span class="text">Create</span>
      <i class="fa fa-caret-down icon-down"></i>
    </section>

    <filter-panel-footer-create-dropdown v-if="open" @close="onCloseDropdown" />
  </section>
</template>

<script>
import clickOutsideHandler from '@/directives/click-outside-handler';
import FilterPanelFooterCreateDropdown from '@/components/FilterPanelFooter/FilterPanelFooterCreateDropdown';

export default {
  name: 'filter-panel-footer-create',

  components: {
    FilterPanelFooterCreateDropdown,
  },

  directives: {
    clickOutsideHandler,
  },

  data() {
    return {
      open: false,
    };
  },

  methods: {
    onCloseDropdown() {
      this.open = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.filter-panel-footer-create {
  position: relative;

  .btn-create {
    @include flex("block", "row", "start", "center");

    border-radius: 2px;
    border: 1px solid clr('white');
    color: clr('white');
    cursor: pointer;
    margin-left: 0.6rem;
    padding: 0.4rem 0.6rem;
    text-transform: uppercase;

    .icon-down {
      margin-left: 0.2rem;
    }

    &:hover, &.open {
      background-color: #333451;
    }
  }
}
</style>
