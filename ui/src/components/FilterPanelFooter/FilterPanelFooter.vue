<template>
  <section class="filter-panel-footer" v-if="isEditor">
    <filter-panel-footer-split />
    <filter-panel-footer-create v-if="filtersApplied.length > 0" />
    <filter-panel-footer-apply />
  </section>
</template>

<script>
import { mapGetters, mapState } from 'vuex';

import FilterPanelFooterApply from '@/components/FilterPanelFooter/FilterPanelFooterApply';
import FilterPanelFooterCreate from '@/components/FilterPanelFooter/FilterPanelFooterCreate';
import FilterPanelFooterSplit from '@/components/FilterPanelFooter/FilterPanelFooterSplit';

export default {
  name: 'filter-panel-footer',

  components: {
    FilterPanelFooterApply,
    FilterPanelFooterCreate,
    FilterPanelFooterSplit,
  },

  computed: {
    ...mapGetters('datasets', ['isEditable']),

    ...mapState('datasets', ['active']),

    ...mapState('snippets', ['filtersApplied']),

    isEditor() {
      return this.isEditable(this.active);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.filter-panel-footer {
  @include flex("block", "row", "start", "center");

  font-size: $font-size-xxs;
  font-weight: $font-weight-bold;
  min-height: fit-content;
  padding: 1rem 1.5rem;
  width: inherit;
}
</style>
