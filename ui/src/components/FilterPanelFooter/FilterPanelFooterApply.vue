<template>
  <section class="filter-panel-footer-apply">
    <loading-blocks-overlay class="loading" size="small" v-if="applying" />
    <section v-else class="btn" @click="onClickApply" :class="{ disabled: filterUndefined }">
      <span class="text">Apply</span>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';

import { metadataRequest, themesRequest } from '@/services/request';

export default {
  name: 'filter-panel-footer-apply',

  components: {
    LoadingBlocksOverlay,
  },

  data() {
    return {
      applying: false,
    };
  },

  computed: {
    ...mapGetters('snippets', ['getFilterValidatedList']),

    ...mapState('snippets', [
      'filters',
      'filtersApplied',
      'filterUndefined',
    ]),

    ...mapState('themes', ['themePanelShowCommentList']),

    allowApplyBtn() {
      return this.getFilterValidatedList.length || this.filtersApplied.length;
    },

    hasErrors() {
      return this.filters.filter(o => o.column).length !== this.getFilterValidatedList.length;
    },
  },

  methods: {
    ...mapActions('snippets', [
      'applyFilters',
      'deselectAllSnippets',
      'resetSnippets',
      'setFiltersAttempted',
    ]),

    ...mapActions('snippetsFilter', ['resetSplitFilterViews']),

    ...mapActions('themes', {
      resetThemes: 'reset',
      setThemePanelShowCommentList: 'setThemePanelShowCommentList',
      setThemes: 'setThemes',
    }),

    async onClickApply() {
      if (this.hasErrors) {
        this.setFiltersAttempted({ value: true });
      } else if (this.allowApplyBtn) {
        this.setFiltersAttempted({ value: false });
        this.applyFilters();

        this.deselectAllSnippets();
        this.resetSnippets();
        this.resetSplitFilterViews();

        this.applying = true;

        await metadataRequest.applyFilterView();
        await themesRequest.fetchAndSetThemes();
        await metadataRequest.filterCommentsOnMetadata();
        await metadataRequest.filterCommentsCountOnMetadata();

        if (!this.themePanelShowCommentList) {
          this.setThemePanelShowCommentList({ val: true });
        }

        this.applying = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.filter-panel-footer-apply {
  @include flex("block", "row", "center", "center");

  margin-left: auto;

  .btn {
    background-color: $cfp-btm-bar-apply-btn-bg;
    border-radius: 2px;
    color: clr("white");
    cursor: pointer;
    padding: 0.4rem 0.6rem;
    text-transform: uppercase;

    &:hover {
      background-color: #333451;
    }
  }
}
</style>

<style lang="scss">
.filter-panel-footer-apply {
  .loading-blocks-overlay {
    margin-right: 1rem;
  }

  .btn {
    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

}
</style>
