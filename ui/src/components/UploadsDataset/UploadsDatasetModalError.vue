<template>
  <section class="uploads-dataset-modal-error">
    <section class="header">
      <h2>{{ isError ? 'Error' : 'Warning' }} Details</h2>
    </section>

    <section class="body">
      <section class="details" v-html="errorMessage"></section>
      <section class="contact">
        <br>
        <b>Still having problems?</b>
        Chat to us, or email
        <a href="mailto:<EMAIL>"><EMAIL></a> and we'll get you back up and running.
      </section>
    </section>

    <section class="footer">
      <base-button colour="light" size="small" type="link" @click="closeModal">Close</base-button>
      <base-button class="btn-chat"
        :icon="'MessageSquareIcon'"
        size="small"
        @click="onClickChat"
      >💬 Start a Live Chat</base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import AcknowledgementType from '@/enum/acknowledgement-type';
import BaseButton from '@/components/Base/BaseButton';
import EmailModal from '@/components/Modal/EmailModal';

export default {
  name: 'uploads-dataset-modal-error',

  components: {
    BaseButton,
  },

  computed: {
    ...mapState('acknowledgements', ['activeAcknowledgement']),

    ...mapGetters('acknowledgements', ['errorMessage', 'type']),

    isError() {
      return this.type(this.activeAcknowledgement) === AcknowledgementType.FAILED;
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    onClickChat() {
      // if (window.Intercom != null) {
      //   window.Intercom('show');
      // }

      this.setModalComponent({ component: EmailModal });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.uploads-dataset-modal-error {
  @include modal;

  .body {
    font-size: $font-size-xs;
    padding: 1rem;

    .contact {
      margin-bottom: 1rem;
    }
  }

  .footer {
    .btn-chat {
      background: $helper-bg;
      color: $helper-txt;
      border: $helper-bdr 1px solid;

      &:hover, &:active, &:focus {
        background: clr('white');
        color: $helper-txt-hvr;
        border: $helper-txt 1px solid;
      }
    }
  }
}
</style>
