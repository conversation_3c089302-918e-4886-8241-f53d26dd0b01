<template>
  <section class="uploads-dataset-preview-format-item">
    <span>{{ label }}</span>
  </section>
</template>

<script>
import datetimeFormat from '@/helpers/datetime-format';

export default {
  name: 'uploads-dataset-preview-format-item',

  props: {
    data: {
      type: Object,
      required: true,
    },
  },

  computed: {
    entry() {
      return datetimeFormat[this.data.value];
    },

    example() {
      return this.entry[1];
    },

    label() {
      return this.entry[0];
    },
  },

};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.uploads-dataset-preview-format-item {
  @include flex("block", "row", "start", "center");

  border-radius: $border-radius-medium;
  color: $body-copy-light;
  cursor: pointer;
  font-size: $font-size-xs;
  margin: 0.2rem;
  padding: 0.3rem 0.5rem;

  &:hover {
    background-color: #E8E8FF;
  }
}
</style>
