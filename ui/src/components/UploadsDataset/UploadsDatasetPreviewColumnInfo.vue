<template>
  <section class="uploads-dataset-preview-column-info">
    <section v-if="!labelEditing" class="label" :class="{ 'is-content': isContent }">
      <section class="text" :title="columnHeader(index)">{{ columnHeader(index) }}</section>
      <section class="button" @click.stop="labelEditing = true">
        <edit-icon class="icon"/>
        <span class="text">Edit</span>
      </section>
    </section>

    <base-input v-else :focus="true" v-model.lazy="localHeader" @blur="onFinishEditing" @submit="onFinishEditing"/>

    <base-dropdown :component="typeComponent"
      :class="{ disabled: columnCount === 1 }"
      :data="typeItems"
      :open="typeOpen"
      @close="typeOpen = false"
      @select="onSelectType"
    >
      <base-dropdown-button :active="typeOpen" :class="{ highlight: highlightType }" @click.stop.prevent="onClickTypeDropdown">
        <section class="dropdown-inner">
          <component v-if="typeDropdownIcon != null" :is="typeDropdownIcon" class="icon"/>
          <span class="text">{{ typeDropdownText }}</span>
        </section>
      </base-dropdown-button>
    </base-dropdown>

    <base-dropdown v-if="isDateType"
      :component="formatComponent"
      :data="formatItems"
      :open="formatOpen"
      @close="formatOpen = false"
      @select="onSelectFormat"
    >
      <base-dropdown-button :active="formatOpen" :class="{ highlight: highlightFormat }" @click.stop.prevent="formatOpen = !formatOpen">
        <span class="text">{{ formatDropdownText }}</span>
      </base-dropdown-button>
    </base-dropdown>
  </section>
</template>

<script>
import { BarChart2Icon, CalendarIcon, ChevronDownIcon, EditIcon, HashIcon, MessageSquareIcon, TypeIcon } from 'vue-feather-icons';
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseDropdown from '@/components/Dropdown/BaseDropdown';
import BaseDropdownButton from '@/components/Dropdown/BaseDropdownButton';
import BaseInput from '@/components/Base/BaseInput';
import datetimeFormat from '@/helpers/datetime-format';
import UploadColumnType from '@/enum/upload-column-type';
import UploadsDatasetPreviewFormatItem from '@/components/UploadsDataset/UploadsDatasetPreviewFormatItem';
import UploadsDatasetPreviewTypeItem from '@/components/UploadsDataset/UploadsDatasetPreviewTypeItem';

import { uploadApi } from '@/services/api';

const typeDescription = value => {
  switch (value) {
    case UploadColumnType.COMMENT:
      return 'Text analysis column.';
    case UploadColumnType.SCORE:
      return 'Used for correlation. No alphabetic characters.';
    case UploadColumnType.NUMERIC:
      return 'Other, non-correlated numeric data. No alphabetic characters.';
    case UploadColumnType.DATE:
      return 'For use in Time Series analysis.';
    case UploadColumnType.TEXT:
      return 'Any other data, including mixed alphabetic and numeric (e.g, ranges).';
    default:
      return null;
  }
};

export default {
  name: 'uploads-dataset-preview-column-info',

  components: {
    BaseDropdown,
    BaseDropdownButton,
    BaseInput,
    ChevronDownIcon,
    EditIcon,
    MessageSquareIcon,
  },

  props: {
    id: {
      type: String,
      required: true,
    },

    index: {
      type: Number,
      required: true,
    },

    values: {
      type: Array,
      required: true,
    },
  },

  data() {
    return {
      formatComponent: UploadsDatasetPreviewFormatItem,
      formatOpen: false,
      labelEditing: false,
      typeComponent: UploadsDatasetPreviewTypeItem,
      typeOpen: false,
      UploadColumnType,
    };
  },

  computed: {
    ...mapState('acknowledgements', ['showWarnings']),

    ...mapGetters('acknowledgements', [
      'columnFormat',
      'columnFormats',
      'columnHeader',
      'columnType',
      'contentColumns',
      'get',
      'isColumnSelected',
    ]),

    acknowledgement() {
      return this.get(this.id);
    },

    columnCount() {
      return this.acknowledgement.columnPreviews.length;
    },

    format() {
      return this.columnFormat(this.index);
    },

    formatDropdownText() {
      const format = datetimeFormat[this.format];

      return format == null ? 'Date Format' : format[0];
    },

    formatItems() {
      return this.columnFormats(this.index).map(value => {
        return { value };
      });
    },

    isContent() {
      return this.contentColumns.includes(this.index);
    },

    isDateType() {
      return this.type === UploadColumnType.DATE;
    },

    isSelected() {
      return this.isColumnSelected(this.index);
    },

    highlightFormat() {
      return !this.showWarnings
        && !this.isContent
        && this.isSelected
        && this.type === UploadColumnType.DATE
        && this.format == null;
    },

    highlightType() {
      return !this.showWarnings
        && !this.isContent
        && this.isSelected
        && this.type == null;
    },

    localHeader: {
      get() {
        return this.columnHeader(this.index);
      },

      set(name) {
        if (name != null && name !== '') {
          this.updateColumn({ id: this.id, index: this.index, name });
        }
      },
    },

    type() {
      return this.columnType(this.index);
    },

    typeDropdownIcon() {
      if (this.isContent) return MessageSquareIcon;

      switch (this.type) {
        case UploadColumnType.COMMENT:
          return MessageSquareIcon;
        case UploadColumnType.SCORE:
          return BarChart2Icon;
        case UploadColumnType.NUMERIC:
          return HashIcon;
        case UploadColumnType.DATE:
          return CalendarIcon;
        case UploadColumnType.TEXT:
          return TypeIcon;
        default:
          return null;
      }
    },

    typeDropdownText() {
      if (this.isContent) return 'Comment';

      return this.type == null ? 'Select Type' : this.type.text();
    },

    typeItems() {
      return UploadColumnType.enumValues.map(e => {
        return {
          value: e,
          content: e.text(),
          description: typeDescription(e),
        };
      });
    },
  },

  watch: {
    formatItems() {
      if (this.formatItems.length === 1) {
        this.updateColumnFormat({ id: this.id, index: this.index, format: this.formatItems[0].value });
      }
    },
  },

  methods: {
    ...mapActions('acknowledgements', [
      'selectColumn',
      'setAcknowledgements',
      'updateColumn',
      'updateColumnFormat',
      'updateContentColumn',
    ]),

    async addOrUpdateMetadata() {
      if (this.acknowledgement.metadataColumns.includes(this.index)) {
        await uploadApi.updateMetadata(this.id, {
          columnIndex: this.index,
          columnName: this.columnHeader(this.index),
          dataType: this.type.upperCase(),
        });
      } else {
        await uploadApi.addMetadata(this.id, {
          columnIndex: this.index,
          columnName: this.columnHeader(this.index),
          dataType: this.type.upperCase(),
        });
      }
    },

    onClickTypeDropdown() {
      if (this.columnCount === 1) return;

      this.typeOpen = !this.typeOpen;
    },

    async onFinishEditing() {
      this.labelEditing = false;

      if (this.acknowledgement.metadataColumns.includes(this.index)) {
        await uploadApi.updateMetadata(this.id, {
          columnIndex: this.index,
          columnName: this.columnHeader(this.index),
        });
      }

      if (this.contentColumns.includes(this.index)) {
        await uploadApi.update(this.id, this.index, this.columnHeader(this.index), null, true);
      }

      const acknowledgements = await uploadApi.getUploads();

      this.setAcknowledgements({ acknowledgements });
    },

    async onSelectType(type) {
      const { columnHeader, id, index } = this;

      this.updateColumn({
        id,
        index,
        name: columnHeader(index),
        type: type.value,
      });

      if (type.value === UploadColumnType.COMMENT) {
        this.updateContentColumn({ id, index, selected: true });

        await uploadApi.update(id, index, columnHeader(index), null, true);
        await uploadApi.removeMetadata(id, { columnIndex: index });
      } else {
        if (this.isContent) {
          this.updateContentColumn({ id, index, selected: false });
          await uploadApi.update(id, index, columnHeader(index), null, false);
        }

        this.selectColumn({ id, index });

        await this.addOrUpdateMetadata();
      }

      const acknowledgements = await uploadApi.getUploads();

      this.setAcknowledgements({ acknowledgements });
    },

    onSelectFormat(format) {
      this.updateColumnFormat({ id: this.id, index: this.index, format: format.value });
    },
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.uploads-dataset-preview-column-info {
  @include flex("block", "column", "start", "stretch");
  @include stretch;
  @include truncate;

  margin-left: 0.8rem;

  .label {
    @include flex("block", "row", "between", "center");
    @include truncate;

    font-size: $font-size-xs;
    font-weight: $font-weight-bold;
    transition: all $interaction-transition-time;

    &.is-content {
      .text {
        color: $uploads-green-dark;
      }
    }

    .text {
      @include truncate;
    }

    .button {
      @include flex("block", "row", "start", "center");
      @include rigid;

      border: 1px solid rgba(40, 23, 80, 0.3);
      border-radius: 2px;
      cursor: pointer;
      padding: 0.1rem 0.2rem 0.2rem;
      text-transform: uppercase;
      transition: all $interaction-transition-time;

      &:hover {
        background-color: clr('purple', 'dark');

        .text, .icon {
          color: clr('white');
        }
      }

      .text {
        font-size: $font-size-xxs;
      }

      .icon {
        height: $font-size-xs;
        margin-right: 0.2rem;
        width: $font-size-xs;
      }
    }
  }

  .base-input {
    background-color: clr('white');
    border: $border-standard;
    border-radius: $border-radius-medium;
    font-size: $font-size-xs;
    padding: 0.2rem;
    transition: all $interaction-transition-time;
    width: 100%;
  }

  .base-dropdown {
    margin-top: 0.5rem;
    max-width: 200px;

    &.disabled {
      cursor: not-allowed;
      opacity: 0.7;

      .base-dropdown-button {
        pointer-events: none;
      }
    }

    .base-dropdown-button {
      &.highlight {
        .base-button {
          border: 1px solid #3C6CFF;
        }
      }

      .text {
        @include truncate;
      }

      .base-button {
        padding: 0.2rem 0.4rem;
      }
    }

    .dropdown-inner {
      @include flex("block", "row", "start", "center");
      .icon {
        height: $font-size-xs;
        margin-right: 0.5rem;
        width: $font-size-xs;
      }
    }
  }
}
</style>
