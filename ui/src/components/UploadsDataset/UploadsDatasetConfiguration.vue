<template>
  <section class="uploads-dataset-configuration">
    <uploads-dataset-configuration-columns :id="id"/>
    <uploads-dataset-configuration-memory :id="id"/>
    <uploads-dataset-configuration-settings :id="id"/>
  </section>
</template>

<script>
import UploadsDatasetConfigurationColumns from '@/components/UploadsDataset/UploadsDatasetConfigurationColumns';
import UploadsDatasetConfigurationMemory from '@/components/UploadsDatasetConfiguration/UploadsDatasetConfigurationMemory';
import UploadsDatasetConfigurationSettings from '@/components/UploadsDataset/UploadsDatasetConfigurationSettings';

export default {
  name: 'uploads-dataset-configuration',

  components: {
    UploadsDatasetConfigurationColumns,
    UploadsDatasetConfigurationMemory,
    UploadsDatasetConfigurationSettings,
  },

  props: {
    id: {
      type: String,
      required: true,
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.uploads-dataset-configuration {
  @include rigid;

  color: clr('white');
  border-radius: $border-radius-medium;
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  margin: 1rem 2rem;
  overflow: hidden;
}
</style>
