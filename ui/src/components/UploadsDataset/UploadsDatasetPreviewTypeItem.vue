<template>
  <section class="uploads-dataset-preview-type-item">
    <section class="left">
      <component :is="iconComponent" class="icon"/>
    </section>
    <section class="right">
      <span class="label">{{ data.content }}</span>
      <span class="description">{{ data.description }}</span>
    </section>
  </section>
</template>

<script>
import { BarChart2Icon, CalendarIcon, HashIcon, MessageSquareIcon, TypeIcon } from 'vue-feather-icons';

import UploadColumnType from '@/enum/upload-column-type';

export default {
  name: 'uploads-dataset-preview-type-item',

  props: {
    data: {
      type: Object,
      required: true,
    },
  },

  computed: {
    iconComponent() {
      switch (this.data.value) {
        case UploadColumnType.COMMENT:
          return MessageSquareIcon;
        case UploadColumnType.TEXT:
          return TypeIcon;
        case UploadColumnType.DATE:
          return CalendarIcon;
        case UploadColumnType.SCORE:
          return BarChart2Icon;
        default:
          return HashIcon;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.uploads-dataset-preview-type-item {
  @include flex("block", "row", "start", "center");
  border-radius: $border-radius-medium;
  color: clr("purple", "rich");
  cursor: pointer;
  font-size: 0.7rem;
  line-height: 0.9rem;
  margin: 0.2rem;
  padding: 0.3rem 0.5rem;
  width: 210px;

  &:hover {
    background-color: #E8E8FF;
  }

  .icon {
    height: $font-size-base;
    margin: 0 0.8rem 0 0.3rem;
    width: $font-size-base;
  }

  .right {
    @include flex("block", "column", "start", "stretch");

    .label {
      font-weight: $font-weight-bold;
      margin-bottom: 0;
    }
  }
}
</style>
