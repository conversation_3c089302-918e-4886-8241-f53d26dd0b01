<template>
  <section class="uploads-dataset-usage">
    <section class="header">
      Uploading {{ count }} Dataset{{ count === 1 ? '' : 's' }}
    </section>

    <section class="info">
      <section class="current">Current usage: <span>{{ currentUsagePercent }}</span></section>
      <section class="post">Usage after upload: <span>{{ postUsagePercent }}</span></section>
    </section>

    <section class="bar">
      <section class="fill current" :style="{ width: currentUsagePercent }"></section>
      <section class="fill upload"
        :class="{ 'danger': overLimit }"
        :style="{ width: uploadUsagePercent }"
      ></section>
    </section>
  </section>
</template>

<script>
import { mapGetters, mapState } from 'vuex';

export default {
  name: 'uploads-dataset-usage',

  computed: {
    ...mapState('acknowledgements', ['acknowledgements']),

    ...mapGetters('acknowledgements', ['totalCharacterCount']),

    ...mapState('usage', ['usage']),

    count() {
      return this.acknowledgements.length;
    },

    currentUsagePercent() {
      if (
        this.usage == null
        || this.usage.currentUsage === 0
        || this.usage.maxUsage === 0
      ) return '0%';

      return `${Math.round((this.usage.currentUsage / this.usage.maxUsage * 10000)) / 100}%`;
    },

    overLimit() {
      if (this.usage == null) return false;

      return this.usage.maxUsage > 0
        && this.usage.currentUsage + this.totalCharacterCount >= this.usage.maxUsage;
    },

    postUsagePercent() {
      if (this.usage == null || this.usage.maxUsage === 0) return '0%';

      if (this.usage.currentUsage + this.totalCharacterCount >= this.usage.maxUsage) return '100%';

      return `${Math.round(((this.usage.currentUsage + this.totalCharacterCount) / this.usage.maxUsage * 10000)) / 100}%`;
    },

    uploadUsagePercent() {
      if (this.usage == null || this.usage.maxUsage === 0) return '0%';

      const count = Math.min(
        this.usage.maxUsage - this.usage.currentUsage, this.totalCharacterCount,
      );

      return `${Math.round(count / this.usage.maxUsage * 10000) / 100}%`;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.uploads-dataset-usage {
  @include flex("block", "column", "start", "stretch");
  background: $comments-bg;
  border: 1px solid $help-bdr;
  border-radius: $border-radius-medium;
  padding: 0.8rem 1.2rem;

  .header {
    font-weight: $font-weight-bold;
    font-size: $font-size-xs;
    text-transform: uppercase;
  }

  .info {
    @include flex("block", "row", "between", "center");

    font-size: $font-size-xs;
    margin-top: 0.2rem;
    padding: 0.1rem 0;

    span {
      font-weight: $font-weight-bold;
    }

    .current {
      border-right: 1px solid lighten($uploads-purple, 15%);
      padding-right: 0.5rem;
      margin-right: 0.5rem;
    }
  }

  .bar {
    @include flex("block", "row", "start", "center");

    background-color: lighten($uploads-purple, 50%);
    border-radius: 4px;
    height: 8px;
    margin-top: 0.5rem;
    overflow: hidden;
    white-space: nowrap;
    width: 100%;

    .fill {
      height: 100%;
    }

    .current {
      background-color: clr('purple', 'dark');
    }

    .upload {
      background-color: lighten($uploads-purple, 15%);
      transition: all $interaction-transition-time;

      &.danger {
        background-color: lighten(clr('red'), 5%);
      }
    }
  }
}
</style>
