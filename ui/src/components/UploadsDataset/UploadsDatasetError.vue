<template>
  <section class="uploads-dataset-error" :class="{ error: isError, warning: isWarning }">
    <span class="classification">
      {{ isError ? 'Error identified:' : 'Warning identified:' }}
    </span>
    <span class="details">
      {{
        isError
          ? 'This dataset cannot be analysed until the error is fixed.'
          : 'You can still analyse this dataset, but some rows may not be analysed.'
      }}
    </span>
    <span class="link" @click="onClickDetails">
      <span>{{ isError ? 'Error' : 'Warning' }} details</span>
      <chevron-right-icon class="icon"/>
    </span>
  </section>
</template>

<script>
import { ChevronRightIcon } from 'vue-feather-icons';
import { mapActions, mapGetters } from 'vuex';

import AcknowledgementType from '@/enum/acknowledgement-type';
import UploadsDatasetModalError from '@/components/UploadsDataset/UploadsDatasetModalError';

export default {
  name: 'uploads-dataset-error',

  components: {
    ChevronRightIcon,
  },

  props: {
    id: {
      type: String,
      required: true,
    },
  },

  computed: {
    ...mapGetters('acknowledgements', ['type']),

    isError() {
      return this.type(this.id) === AcknowledgementType.FAILED;
    },

    isWarning() {
      return this.type(this.id) === AcknowledgementType.WARNING;
    },
  },

  methods: {
    ...mapActions('acknowledgements', ['setActiveAcknowledgement']),

    ...mapActions('modal', ['setModalComponent']),

    onClickDetails() {
      this.setActiveAcknowledgement({ id: this.id });
      this.setModalComponent({ component: UploadsDatasetModalError });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.uploads-dataset-error {
  @include flex("block", "row", "start", "stretch");
  @include rigid;

  border-radius: 1rem;
  font-size: $font-size-xs;
  padding: 0.5rem 1rem;

  &.error {
    background-color: #f86262;
    color: clr('white');
  }

  &.warning {
    background-color: #ffce9a;
  }

  span {
    margin-right: 0.5rem;

    &:last-of-type {
      margin-right: 0;
    }
  }

  .classification {
    font-weight: $font-weight-bold;
  }

  .link {
    cursor: pointer;

    span {
      font-weight: $font-weight-bold;
      text-decoration: underline;
    }

    .icon {
      height: $font-size-base;
      margin: -0.25rem -0.2rem;
      stroke-width: 3;
      width: $font-size-base;
    }
  }
}
</style>
