<template>
  <section class="uploads-dataset-stop-words-lists">
    <h3>Create a new Stop Word list</h3>
    <base-button class="create-btn" icon="plus" @click="onClickCreate">Create</base-button>
    <h3>Add an existing Stop Word list</h3>
    <section class="list-container">
      <stop-words-modal-list @edit="onEdit" @select="onSelect"/>
    </section>

    <base-button class="back-btn" colour="dark" icon="arrow-left" size="small" @click="onClose">Back to Configuration</base-button>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import StopWordsModalList from '@/components/StopWords/StopWordsModalList';

export default {
  name: 'uploads-dataset-stop-words-lists',

  components: {
    BaseButton,
    StopWordsModalList,
  },

  methods: {
    ...mapActions('globalStopWords', ['newList']),

    onClickCreate() {
      this.newList();
      this.$emit('edit');
    },

    onClose() {
      this.$emit('close');
    },

    onEdit() {
      this.$emit('edit');
    },

    onSelect() {
      this.$emit('select');
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.uploads-dataset-stop-words-lists {
  @include flex("block", "column", "start", "stretch");
  @include stretch;

  overflow: hidden;
  padding: 2rem;

  h3 {
    color: $uploads-purple;
    font-size: $font-size-sm;
    font-weight: $font-weight-bold;
  }

  .create-btn {
    background-color: unset;
    border: 2px dashed clr('purple');
    color: clr('purple');
    margin: 1rem 0;
    font-weight: 600;

    &:hover, &:active, &:focus {
      border: 2px solid clr('white');
      color: clr('white');
    }
  }

  .list-container {
    @include stretch;

    margin: 1rem 0;
    overflow-y: auto;
  }

  .back-btn {
    align-self: start;
  }
}
</style>
