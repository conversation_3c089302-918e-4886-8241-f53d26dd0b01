<template>
  <section class="uploads-dataset-preview" :class="{ 'has-multiple': previews.length > 1 }">
    <uploads-dataset-preview-column v-for="(column, index) in previews"
      :key="index" class="column"
      :id="id"
      :index="index"
      :values="column.values"
    />
  </section>
</template>

<script>
import { mapGetters } from 'vuex';

import UploadsDatasetPreviewColumn from '@/components/UploadsDataset/UploadsDatasetPreviewColumn';

export default {
  name: 'uploads-dataset-preview',

  components: {
    UploadsDatasetPreviewColumn,
  },

  props: {
    id: {
      type: String,
      required: true,
    },
  },

  computed: {
    ...mapGetters('acknowledgements', ['previews']),
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.uploads-dataset-preview {
  @include rigid;
  border-radius: $border-radius-medium;
  display: grid;
  grid-auto-columns: minmax(250px, 1fr);
  grid-auto-flow: column;
  margin: 1rem 2rem;
  overflow-x: auto;
  overflow-y: hidden;
}
</style>
