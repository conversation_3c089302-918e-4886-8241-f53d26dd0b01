<template>
  <section class="uploads-dataset-modal-prevent">
    <section class="header">
      <h2>Upload Prevented</h2>
    </section>

    <section v-if="hasContentColumns" class="body">
      <h3>Some columns you have selected aren't configured properly.</h3>
      <p>You can either fix these issues, or deselect the affected columns to proceed with analysis. Affected columns are highlighted in orange.</p>
      <p>Make sure if a column is selected, that the appropriate column type is also selected.</p>
      <p>If any <strong>Date</strong> type columns are selected, make sure you have also selected the appropriate date format.</p>
      <p>When the date format is unambiguous, we will automatically select the appropriate format - but when there are multiple possible formats, you will have to confirm the format using the dropdown menu.</p>
    </section>

    <section v-else class="body">
      <h3><alert-triangle-icon class="icon"/>You need to select a "Comments" column before you can analyse.</h3>
      <p>You can either use the selection button on the preview column, or use the "Select Type" dropdown menu to select the "Comments" type.</p>
    </section>

    <section class="footer">
      <base-button colour="light" size="small" type="link" @click="closeModal">Close</base-button>
    </section>
  </section>
</template>

<script>
import { AlertTriangleIcon } from 'vue-feather-icons';
import { mapActions, mapGetters } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';

export default {
  name: 'uploads-dataset-modal-prevent',

  components: {
    AlertTriangleIcon,
    BaseButton,
  },

  computed: {
    ...mapGetters('acknowledgements', ['contentColumns']),

    hasContentColumns() {
      return this.contentColumns?.length > 0;
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.uploads-dataset-modal-prevent {
  @include modal;

  .header {
    padding: 1rem;
  }

  .body {
    padding: 2rem;

    h3 {
      font-size: $font-size-sm;
      margin-bottom: 1rem;
    }

    p {
      font-size: $font-size-xs;
      margin-bottom: 1rem;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .icon {
    bottom: -3px;
    color: clr('red');
    height: 1rem;
    margin-right: 0.5rem;
    padding-bottom: -8px;
    position: relative;
    width: 1rem;
  }
}
</style>
