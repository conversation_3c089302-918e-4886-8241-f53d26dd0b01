<template>
  <section class="uploads-dataset-info">
    <section class="file">
      <img :src="require(`@/assets/${fileType(id)}-file.svg`)" class="file-icon"/>
      <section class="info">
        <section v-if="!editing" class="label">
          <section class="text">{{ localLabel }}</section>
          <base-button icon="edit" size="small" type="outline" @click="editing = true">Edit Name</base-button>
        </section>

        <base-input v-else :focus="true" v-model.lazy="localLabel" @blur="onFinishEditing" @submit="onFinishEditing"/>

        <section v-if="hasContentColumns" class="stats">
          <section class="comments">{{ documentCount }}</section>
          <section class="divider">•</section>
          <section class="characters">{{ characterCount }}</section>
        </section>

        <section v-else class="no-content">
          Select comments column to view comment and character counts
        </section>
      </section>
    </section>

    <uploads-dataset-usage/>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import UploadsDatasetUsage from './UploadsDatasetUsage';

import { uploadApi } from '@/services/api';

export default {
  name: 'uploads-dataset-info',

  components: {
    BaseButton,
    BaseInput,
    UploadsDatasetUsage,
  },

  props: {
    id: {
      type: String,
      required: true,
    },
  },

  data() {
    return {
      editing: false,
    };
  },

  computed: {
    ...mapState('acknowledgements', ['renames']),

    ...mapGetters('acknowledgements', [
      'characterCount',
      'contentColumns',
      'documentCount',
      'fileType',
      'get',
    ]),

    acknowledgement() {
      return this.get(this.id);
    },

    hasContentColumns() {
      return this.contentColumns?.length > 0;
    },

    localLabel: {
      get() {
        return this.renames[this.id] != null ? this.renames[this.id] : this.acknowledgement.label;
      },

      set(value) {
        this.rename({ id: this.id, name: value });
      },
    },
  },

  methods: {
    ...mapActions('acknowledgements', ['rename']),

    async onFinishEditing() {
      this.editing = false;

      await uploadApi.updateLabel(this.id, this.localLabel);
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.uploads-dataset-info {
  @include flex("block", "row", "between", "center");

  padding: 0 2rem;

  .file {
    @include flex("block", "row", "start", "center");

    .file-icon {
      height: 2rem;
      margin-right: 0.6rem;
      width: 2rem;
    }

    .info {
      @include flex("block", "column", "start", "stretch");

      .label {
        @include flex("block", "row", "start", "center");
        max-width: 1000px;

        .text {
          @include truncate;
          font-weight: $font-weight-bold;
          font-size: $font-size-md;
          max-width: 800px;
        }

        .base-button {
          border: 1px solid rgba(40, 23, 80, 0.3);
          color: clr('purple', 'rich');
          font-size: $font-size-xxs;
          font-weight: $font-weight-bold;
          margin-left: 1rem;
          padding: 0.2rem 0.3rem 0.2rem 0;
          text-transform: uppercase;

          .base-icon {
            margin-right: 0px;
          }

          &:hover, &:focus {
            background-color: clr('purple', 'rich');
            color: clr('white');
          }
        }
      }

      .base-input {
        border: $border-standard;
        border-radius: $border-radius-medium;
        font-size: $font-size-xs;
        padding: 0.5rem;
      }

      .stats {
        @include flex("block", "row", "start", "center");

        font-size: $font-size-xs;
        margin-top: 0.5rem;
        text-transform: uppercase;

        .divider {
          margin: 0 0.3rem;
        }
      }

      .no-content {
        font-size: $font-size-xs;
        font-style: italic;
        margin-top: 0.5rem;
        opacity: 0.8;
      }
    }
  }
}
</style>
