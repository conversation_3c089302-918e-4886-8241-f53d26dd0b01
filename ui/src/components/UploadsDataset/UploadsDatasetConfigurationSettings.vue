<template>
  <section class="uploads-dataset-configuration-settings">
    <section class="header">
      <section class="left">
        <settings-icon class="icon"/>
        Configure Dataset
      </section>
      <section v-if="hasConfiguration" class="right">
        <base-button icon="edit" type="outline" @click="onClickConfigure">Edit</base-button>
      </section>
    </section>
    <section class="body" :class="{ 'has-content': hasConfiguration }">
      <section v-if="!hasConfiguration" class="no-content">
        <span class="text">No Dataset Configuration Applied</span>
        <base-button icon="settings" type="outline" @click="onClickConfigure">Configure Dataset</base-button>
      </section>

      <section v-else class="configuration">
        <section class="label">Dataset Configuration</section>

        <section class="details">
          <section v-if="translation" class="item">
            <section class="type">Language Translation:</section>
            <section class="value">"{{ translation }}"</section>
          </section>
          <section v-if="ackStopWords.length > 0" class="item">
            <section class="type">Stop Words:</section>
            <section class="value">{{ ackStopWords.join(', ') }}</section>
          </section>
        </section>
      </section>
    </section>
  </section>
</template>

<script>
import { SettingsIcon } from 'vue-feather-icons';
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import UploadsDatasetModalConfigure from './UploadsDatasetModalConfigure';

export default {
  name: 'uploads-dataset-configuration-settings',

  components: {
    BaseButton,
    SettingsIcon,
  },

  props: {
    id: {
      type: String,
      required: true,
    },
  },

  computed: {
    ...mapState('acknowledgements', ['stopWords', 'translations']),

    ackStopWords() {
      return this.stopWords[this.id] || [];
    },

    translation() {
      return this.translations[this.id];
    },

    hasConfiguration() {
      return this.ackStopWords.length > 0 || this.translations[this.id] != null;
    },
  },

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    onClickConfigure() {
      this.setModalComponent({ component: UploadsDatasetModalConfigure });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.uploads-dataset-configuration-settings {
  @include uploads-dataset-configuration-panel;

  border-right: none;

  .configuration {
    font-size: $font-size-xs;
    padding: 1rem 2rem;

    .label {
      font-size: $font-size-sm;
      font-weight: $font-weight-bold;
      margin-bottom: 0.5rem;
    }

    .item {
      @include flex("block", "row", "start", "center");

      margin-bottom: 0.3rem;

      .type {
        color: rgba(clr('white'), 0.8);
      }

      .value {
        color: rgba(clr('white'), 0.6);
        margin-left: 0.3rem;
      }
    }
  }
}
</style>
