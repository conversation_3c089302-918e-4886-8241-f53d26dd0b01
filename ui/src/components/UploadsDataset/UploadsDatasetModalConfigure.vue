<template>
  <section class="uploads-dataset-modal-configure">
    <section class="header">
      <h2 v-if="!showStopWordsEdit && !showStopWordsLists">Configure Dataset</h2>
      <h2 v-if="!showStopWordsEdit && showStopWordsLists">Configure Dataset - Add Stop Word List</h2>
      <h2 v-if="showStopWordsEdit">Configure Dataset - Edit Stop Word List</h2>
      <x-icon class="icon" @click="closeModal"/>
    </section>

    <section v-if="!showStopWordsEdit && showStopWordsLists" class="body">
      <uploads-dataset-stop-words-lists
        @close="showStopWordsLists = false"
        @edit="showStopWordsEdit = true"
        @select="onSelectStopWords"
      />
    </section>

    <section v-if="showStopWordsEdit" class="body">
      <uploads-dataset-stop-words-edit @save="showStopWordsEdit = false"/>
    </section>

    <section v-if="!showStopWordsEdit && !showStopWordsLists" class="body">
      <section class="settings text-settings">
        <h3>Text Analysis Settings</h3>

        <section v-if="showTranslation" class="setting setting-translation">
          <h4>Specify Translation Language</h4>
          <section class="lang-select lang-default" @click="onSelectLang('default')">
            <base-radio radio-size="small" :value="isLangDefault"/>
            <span class="text">No translate</span>
          </section>
          <section class="lang-select lang-detection" @click="onSelectLang('detection')">
            <base-radio radio-size="small" :value="isLangDetection"/>
            <span class="text">Automatic language detection</span>
          </section>
          <section class="lang-select lang-input">
            <section class="left" @click="onSelectLang('input')">
              <base-radio radio-size="small" :value="isLangInput"/>
              <span class="text">ISO code</span>
            </section>
            <section class="right">
              <base-input placeholder="en" v-model="translation" :class="{ disabled: !isLangInput }" :disabled="!isLangInput"></base-input>
              <span class="text">
                See <a href="https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes" target="_blank">ISO 639-1 code</a> for references.
              </span>
            </section>
          </section>
        </section>

        <section class="setting setting-stop-words">
          <h4>Add Stop Words</h4>
          <stop-words-input :stop-words="stopWords" @update="onUpdateStopWords"></stop-words-input>
          <p>Enter or paste in your stop words with each tag separated by a comma, or
            <base-button type="link" @click="showStopWordsLists = true">add a stop word list</base-button>.
          </p>
        </section>
      </section>
    </section>

    <section class="footer">
      <base-button colour="light" size="small" type="link" @click="closeModal">Cancel</base-button>

      <base-button class="update" colour="dark" size="small" @click="onClickUpdate">Update Configuration</base-button>
    </section>
  </section>
</template>

<script>
import { XIcon } from 'vue-feather-icons';
import { mapActions, mapGetters, mapState } from 'vuex';

import AcknowledgementType from '@/enum/acknowledgement-type';
import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import BaseRadio from '@/components/Base/BaseRadio';
import StopWordsInput from '@/components/StopWords/StopWordsInput';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import UploadsDatasetStopWordsEdit from '@/components/UploadsDataset/UploadsDatasetStopWordsEdit';
import UploadsDatasetStopWordsLists from '@/components/UploadsDataset/UploadsDatasetStopWordsLists';

import { globalStopWordApi } from '@/services/api';

export default {
  name: 'uploads-dataset-modal-configure',

  components: {
    BaseButton,
    BaseInput,
    BaseRadio,
    LoadingBlocksOverlay,
    StopWordsInput,
    UploadsDatasetStopWordsEdit,
    UploadsDatasetStopWordsLists,
    XIcon,
  },

  data() {
    return {
      langSelection: 'default',
      showStopWordsEdit: false,
      showStopWordsLists: false,
      stopWords: [],
      translation: null,
    };
  },

  computed: {
    ...mapState('acknowledgements', ['activeAcknowledgement', 'translations']),

    ...mapGetters('acknowledgements', ['getStopWords', 'type']),

    ...mapGetters('user', ['hasFeature']),

    id() {
      return this.activeAcknowledgement;
    },

    isLangDefault() {
      return this.langSelection === 'default';
    },

    isLangDetection() {
      return this.langSelection === 'detection';
    },

    isLangInput() {
      return this.langSelection === 'input';
    },

    showTranslation() {
      const hasTranslation = this.hasFeature('multilingual');

      return (
        hasTranslation
        && (this.type(this.id) === AcknowledgementType.SUCCESS
        || this.type(this.id) === AcknowledgementType.WARNING)
      );
    },
  },

  created() {
    const savedStopWords = this.getStopWords(this.id);
    const savedTranslation = this.translations[this.id];

    this.translation = savedTranslation == null
      ? 'en'
      : savedTranslation;

    this.stopWords = savedStopWords == null
      ? []
      : savedStopWords;
  },

  async mounted() {
    await globalStopWordApi.fetch();
  },

  methods: {
    ...mapActions('acknowledgements', ['setTranslation', 'syncStopWords']),

    ...mapActions('modal', ['closeModal']),

    async onClickUpdate() {
      let translation;
      switch (this.langSelection) {
        case 'detection':
          translation = 'multilingual';
          break;
        case 'input':
          translation = this.translation || 'en';
          break;
        default:
          translation = 'en';
          break;
      }

      this.setTranslation({ id: this.id, translation });
      this.syncStopWords({ id: this.id, stopWords: this.stopWords });

      this.closeModal();
    },

    onSelectLang(val) {
      this.langSelection = val;
    },

    onSelectStopWords() {
      this.showStopWordsLists = false;
      this.stopWords = this.getStopWords(this.id);
    },

    onUpdateStopWords(stopWords) {
      this.stopWords = stopWords;
      this.syncStopWords({ id: this.id, stopWords });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.uploads-dataset-modal-configure {
  @include modal;

  width: 800px;

  .header {
    @include flex("block", "row", "between", "center");

    padding: 1.5rem 2rem;

    h2 {
      font-size: $font-size-base;
      font-weight: $font-weight-bold;
    }

    .icon {
      color: $body-copy-light;
      cursor: pointer;

      &:hover {
        color: $body-copy;
      }
    }
  }

  .body {
    @include flex("block", "column", "start", "stretch");

    background-color: clr('white');
    max-height: 600px;
    min-height: 500px;
    padding: 0;

    .stop-words-modal-list {
      margin: 1rem 2rem;
    }

    .settings {
      font-size: $font-size-xs;
      padding: 2rem;

      h3 {
        color: $uploads-purple;
        font-size: $font-size-sm;
        font-weight: $font-weight-bold;
      }

      .setting {
        margin-top: 1rem;

        h4 {
          font-size: $font-size-xs;
          font-weight: $font-weight-bold;
          margin-top: 1.5rem;
        }

        a {
          color: $uploads-purple;
          cursor: pointer;
          font-weight: $font-weight-bold;
          text-decoration: underline;
        }

        .stop-words-input {
          margin: 0.5rem 0;
        }

        .base-input {
          padding: 0.5rem;
        }

        .base-button {
          padding: 0;
          font-size: $font-size-xs;
        }

        &.setting-translation {
          .lang-select {
            @include flex("block", "row", "start", "center");
            cursor: pointer;
            height: 1.5rem;
            margin-top: 0.5rem;

            .base-radio {
              margin-right: 0.5rem;
            }

            &.lang-input {
              .left {
                @include flex("block", "row", "start", "center");
                width: 10rem;
              }

              .right {
                @include flex("block", "row", "start", "center");
                cursor: default;
                width: 100%;

                .base-input {
                  width: 15rem;
                  margin-right: 0.5rem;

                  &.disabled {
                    background-color: $border-color;
                    cursor: not-allowed;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .footer {
    padding: 1rem 2rem;

    .base-button {
      padding: 0.6rem 1.2rem;
    }

    .loading-blocks-overlay {
      height: 2rem;
    }
  }
}
</style>
