<template>
  <section class="uploads-dataset-stop-words-edit">
    <stop-words-modal-edit @save="onSave"/>
  </section>
</template>

<script>
import StopWordsModalEdit from '@/components/StopWords/StopWordsModalEdit';

export default {
  name: 'uploads-dataset-stop-words-edit',

  components: {
    StopWordsModalEdit,
  },

  methods: {
    onSave() {
      this.$emit('save');
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.uploads-dataset-stop-words-edit {
  padding: 2rem;
}
</style>
