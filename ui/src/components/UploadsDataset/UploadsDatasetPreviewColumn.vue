<template>
  <section class="uploads-dataset-preview-column" :class="{
    'is-completed': isCompleted,
    'is-content': isContent,
    'is-misconfigured': isMisconfigured,
    'is-selected': isSelected
  }">
    <section class="header">
      <section class="checkbox" :class="{ active: isSelected, disabled: columnCount === 1 }" @click.stop="onClickCheckbox">
        <check-icon class="icon"/>
      </section>

      <uploads-dataset-preview-column-info :id="id" :index="index" :values="values"/>
    </section>

    <section v-for="(item, i) in values.slice(1)" :key="i" class="item">
      <span v-if="item === ''" class="empty">[Empty Row]</span>
      <span class="v-else" :title="item">{{ item }}</span>
    </section>
  </section>
</template>

<script>
import { CheckIcon } from 'vue-feather-icons';
import { mapActions, mapGetters, mapState } from 'vuex';

import UploadsDatasetPreviewColumnInfo from '@/components/UploadsDataset/UploadsDatasetPreviewColumnInfo';

import { uploadApi } from '@/services/api';
import UploadColumnType from '@/enum/upload-column-type';

export default {
  name: 'uploads-dataset-preview-column',

  components: {
    CheckIcon,
    UploadsDatasetPreviewColumnInfo,
  },

  props: {
    id: {
      type: String,
      required: true,
    },

    index: {
      type: Number,
      required: true,
    },

    values: {
      type: Array,
      required: true,
    },
  },

  computed: {
    ...mapState('acknowledgements', ['showWarnings']),

    ...mapGetters('acknowledgements', [
      'columnFormat',
      'columnHeader',
      'columnType',
      'contentColumns',
      'get',
      'isColumnSelected',
    ]),

    acknowledgement() {
      return this.get(this.id);
    },

    columnCount() {
      return this.acknowledgement.columnPreviews.length;
    },

    isCompleted() {
      if (this.isContent) return false;

      if (
        this.isSelected
        && this.columnType(this.index) === UploadColumnType.DATE
        && this.columnFormat(this.index) != null
      ) return true;

      if (this.isSelected && this.columnType(this.index) === UploadColumnType.DATE) return false;

      if (this.isSelected && this.columnType(this.index) != null) return true;

      return false;
    },

    isContent() {
      return this.contentColumns.includes(this.index);
    },

    isMisconfigured() {
      if (this.isContent || !this.showWarnings) return false;

      if (this.isSelected && this.columnType(this.index) == null) return true;

      if (
        this.isSelected
        && this.columnType(this.index) === UploadColumnType.DATE
        && this.columnFormat(this.index) == null
      ) return true;

      return false;
    },

    isSelected() {
      return this.isColumnSelected(this.index);
    },
  },

  methods: {
    ...mapActions('acknowledgements', [
      'deselectColumn',
      'removeColumn',
      'removeColumnFormat',
      'selectColumn',
      'setAcknowledgements',
      'updateContentColumn',
    ]),

    async onClickCheckbox() {
      if (this.columnCount === 1) return;

      const { columnHeader, id, index } = this;

      if (!this.contentColumns?.length && !this.isSelected) { // first select so automatically select first thing is content
        this.updateContentColumn({ id, index, selected: true });
        await uploadApi.update(id, index, columnHeader(index), null, true);
      } else if (this.isContent && this.isSelected) {
        await this.removeContentColumn();
      } else if (this.isSelected) {
        await this.removeMetadataColumn();
      } else {
        this.selectColumn({ id, index });
      }

      const acknowledgements = await uploadApi.getUploads();

      this.setAcknowledgements({ acknowledgements });
    },

    async removeContentColumn() {
      const { id, index } = this;

      this.deselectColumn({ id, index });
      this.removeColumn({ id, index });
      this.removeColumnFormat({ id, index });

      if (this.isColumnSelected(index)) {
        this.updateContentColumn({ id, index, selected: false });
        await uploadApi.update(id, index, null, null, false);
      }
    },

    async removeMetadataColumn() {
      const { id, index } = this;

      this.deselectColumn({ id, index });
      this.removeColumn({ id, index });
      this.removeColumnFormat({ id, index });

      if (this.isColumnSelected(index)) {
        await uploadApi.removeMetadata(id, { columnIndex: index });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.uploads-dataset-preview-column {
  @include flex("block", "column", "start", "stretch");

  &:first-child {
    .item {
      &:last-child {
        border-bottom-left-radius: $border-radius-medium;
      }
    }
  }

  &:not(:first-child) {
    .header {
      border-left: $border-standard;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }

  &:last-child {
    .item:last-child {
      border-bottom-right-radius: $border-radius-medium;
    }
  }

  &:not(:last-child) {
    .item {
      border-right: none;
    }

    .header {
      border-right: none;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
  }

  &.is-content {
    .header {
      background-color: $uploads-green-light;

      .checkbox.active {
        background-color: $uploads-green;
        border-color: $uploads-green-dark;
      }
    }

    .item {
      background-color: $uploads-green-lighter;
    }
  }

  &.is-misconfigured {
    .header {
      background-color: rgb(255, 222, 187);
    }

    .item {
      background-color: rgb(255, 235, 213);
    }
  }

  &.is-completed {
    .header {
      background-color: $uploads-blue-light;

      .checkbox.active {
        background-color: $uploads-blue;
      }
    }

    .item {
      background-color: $uploads-blue-lighter;
    }
  }

  .header {
    @include flex("block", "row", "start", "stretch");
    @include stretch;

    border: 1px solid clr('purple', 'light');
    border-radius: $border-radius-medium;
    padding: 1.2rem;
    margin-bottom: -2px;
    z-index: 1;

    .checkbox {
      @include flex("block", "row", "center", "center");
      @include rigid;

      border: 1px solid darken($border-color, 30%);
      border-radius: 0.6rem;
      cursor: pointer;
      height: 1.2rem;
      margin-top: 0.6rem;
      transition: all $interaction-transition-time;
      width: 1.2rem;

      &:hover {
        border: 1px solid #3C2964;
      }

      &.active {
        background-color: #3C2964;
        border-color: #3C2964;
      }

      &.disabled {
        cursor: not-allowed;
        opacity: 0.5;
      }

      .icon {
        color: clr('white');
        height: $font-size-xs;
        width: $font-size-xs;
      }
    }
  }

  .item {
    @include truncate;

    border: $border-standard;
    border-top: none;
    font-size: $font-size-xs;
    height: 37px;
    padding: 0.7rem 1.2rem;
    z-index: 0;

    &:nth-child(2) {
      height: 39px;
      padding-top: calc(0.7rem + 2px);
    }

    .empty {
      font-style: italic;
      opacity: 0.5;
    }
  }
}
</style>
