<template>
  <section class="uploads-dataset-guide">
    <span class="text-steps">Steps:</span>

    <section class="item green" :class="{ active: !hasContent }">
      <section class="badge">
        <check-icon v-if="hasContent" class="icon"/>
        <span v-else class="text">1</span>
      </section>
      <span class="text">Select Comments Column</span>
      <arrow-down-icon v-if="!hasContent" class="icon"/>
    </section>

    <section v-if="columnCount > 1" class="item blue" :class="{ active: hasContent }">
      <section class="badge">
        <span class="text">2</span>
      </section>
      <span class="text">Add Additional Data Columns</span>
      <arrow-down-icon v-if="hasContent" class="arrow icon"/>
    </section>

    <section v-if="columnCount > 1" class="help">
      <help-circle-icon class="icon"
        v-tooltip.notrigger="{ html: 'help-guide', class: 'tooltip-upload-dataset-help', delay: 0, visible: tooltipVisible }"
        @click.prevent.stop="tooltipVisible = !tooltipVisible"
      />

      <uploads-dataset-help-guide id="help-guide" @close="tooltipVisible = false"/>
    </section>

    <section class="has-headers"
      v-tooltip="{
        class: 'tooltip-upload-dataset-has-headers',
        content: 'Selecting this option will exclude the first row of the uploaded file from analysis and filters data.',
        delay: 0,
      }"
      @click="onClickHasHeaders">
      <base-checkbox :value="hasHeaders"/> Uploaded File Contains Headers in First Row
    </section>
  </section>
</template>

<script>
import { ArrowDownIcon, CheckIcon, HelpCircleIcon } from 'vue-feather-icons';
import { mapActions, mapGetters } from 'vuex';

import BaseCheckbox from '@/components/Base/BaseCheckbox';
import UploadsDatasetHelpGuide from '@/components/UploadsDataset/UploadsDatasetHelpGuide';

export default {
  name: 'uploads-dataset-guide',

  components: {
    ArrowDownIcon,
    BaseCheckbox,
    CheckIcon,
    HelpCircleIcon,
    UploadsDatasetHelpGuide,
  },

  props: {
    id: {
      type: String,
      required: true,
    },
  },

  data() {
    return {
      tooltipVisible: false,
    };
  },

  computed: {
    ...mapGetters('acknowledgements', [
      'contentColumns',
      'get',
      'hasHeaders',
    ]),

    acknowledgement() {
      return this.get(this.id);
    },

    columnCount() {
      return this.acknowledgement?.columnPreviews?.length || 0;
    },

    hasContent() {
      return this.contentColumns?.length > 0;
    },
  },

  methods: {
    ...mapActions('acknowledgements', ['updateHasHeaders']),

    onClickHasHeaders() {
      this.updateHasHeaders({ id: this.id, hasHeaders: !this.hasHeaders });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.uploads-dataset-guide {
  @include flex("block", "row", "start", "center");

  margin: 1.5rem 2rem 0.5rem;

  .item {
    @include flex("block", "row", "start", "center");

    font-size: 0.7rem;
    margin-left: 1.2rem;
    opacity: 0.5;

    &.active {
      opacity: 1;
    };

    &.blue {
      .badge {
        background-color: $uploads-blue;
      }

      .text, .icon {
        color: #0f16ea;
      }
    }

    &.green {
      .badge {
        background-color: $uploads-green;
      }

      .text, .icon {
        color: $uploads-green-dark;
      }
    }

    .badge {
      @include flex("block", "row", "center", "center");

      border-radius: 0.7rem;
      height: 1.4rem;
      width: 1.4rem;

      .icon {
        color: clr('white');
        height: $font-size-sm;
        margin: 0;
        width: $font-size-sm;
      }

      .text {
        color: clr('white');
        margin: 0;
      }
    }

    .text {
      font-weight: $font-weight-bold;
      margin-left: 0.5rem;
    }

    .icon {
      height: $font-size-base;
      margin-left: 0.5rem;
      opacity: 1;
      width: $font-size-base;

      &.hidden {
        opacity: 0;
      }
    }
  }

  .text-steps {
    font-size: 0.8rem;
    font-weight: 600;
  }

  .help {
    @include flex("block", "row", "center", "center");

    margin-left: 1rem;

    .icon {
      cursor: pointer;
      height: 1.3rem;
      transition: opacity $interaction-transition-time;
      width: 1.3rem;

      &:hover {
        opacity: 0.8;
      }
    }

    .help-guide {
      h1 {
        font-size: $font-size-md;
      }

      h2, h3, p {
        font-size: $font-size-xs;
      }
    }
  }

  .has-headers {
    @include flex("block", "row", "start", "center");

    cursor: pointer;
    font-size: 0.65rem;
    font-weight: 500;
    margin-left: 1rem;
    transition: opacity $interaction-transition-time;
    text-transform: uppercase;

    &:hover {
      opacity: 0.8;
    }

    .base-checkbox {
      font-size: $font-size-sm;
      margin-right: 0.5rem;
      pointer-events: none;
    }
  }
}
</style>
