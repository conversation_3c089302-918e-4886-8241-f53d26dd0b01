<template>
  <section class="uploads-dataset-configuration-columns">
    <section class="header">
      <section class="left">
        <check-icon class="icon"/>
        Selected Columns
      </section>
    </section>
    <section class="body" :class="{ 'has-content': hasApplied }">
      <section v-if="!hasApplied" class="no-content">
        Selected columns will appear here.
      </section>

      <section v-else class="applied-columns">
        <section v-for="column in appliedColumns" :key="column[0]" class="column">
          <section class="label">{{ column[0] }}</section>
          <section class="divider">-</section>
          <section class="type">{{ column[1] }}</section>
        </section>
      </section>
    </section>
  </section>
</template>

<script>
import { CheckIcon } from 'vue-feather-icons';
import { mapGetters } from 'vuex';

export default {
  name: 'uploads-dataset-configuration-columns',

  components: {
    CheckIcon,
  },

  props: {
    id: {
      type: String,
      required: true,
    },
  },

  computed: {
    ...mapGetters('acknowledgements', ['appliedColumns']),

    hasApplied() {
      return this.appliedColumns != null && this.appliedColumns.length > 0;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.uploads-dataset-configuration-columns {
  @include uploads-dataset-configuration-panel;

  .body {
    @include stretch;
  }

  .applied-columns {
    @include flex("block", "column", "start", "stretch");
    @include stretch;

    font-size: $font-size-sm;
    max-width: 100%;
    padding: 1rem 2rem;
    width: 100%;

    .column {
      @include flex("block", "row", "start", "center");

      margin-bottom: 0.5rem;

      .label {
        @include truncate;

        font-weight: $font-weight-bold;
      }

      .divider {
        @include rigid;

        margin: 0 0.4rem;
      }

      .type {
        @include rigid;

        opacity: 0.7;
      }
    }
  }
}
</style>
