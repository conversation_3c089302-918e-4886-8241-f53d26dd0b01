<template>
  <section class="uploads-dataset">
    <uploads-dataset-info :id="id"/>

    <section v-if="showError" class="error">
      <uploads-dataset-error :id="id"/>
    </section>

    <uploads-dataset-guide :id="id"/>

    <uploads-dataset-preview v-if="hasPreviews" :id="id"/>
    <loading-blocks-overlay v-else>Loading Previews...</loading-blocks-overlay>

    <uploads-dataset-configuration :id="id"/>
  </section>
</template>

<script>
import { mapGetters } from 'vuex';

import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import UploadsDatasetConfiguration from './UploadsDatasetConfiguration';
import UploadsDatasetError from './UploadsDatasetError';
import UploadsDatasetGuide from './UploadsDatasetGuide';
import UploadsDatasetInfo from './UploadsDatasetInfo';
import UploadsDatasetPreview from './UploadsDatasetPreview';
import AcknowledgementType from '@/enum/acknowledgement-type';

export default {
  name: 'uploads-dataset',

  components: {
    LoadingBlocksOverlay,
    UploadsDatasetConfiguration,
    UploadsDatasetError,
    UploadsDatasetGuide,
    UploadsDatasetInfo,
    UploadsDatasetPreview,
  },

  props: {
    id: {
      type: String,
      required: true,
    },
  },

  computed: {
    ...mapGetters('acknowledgements', ['get', 'type']),

    acknowledgement() {
      return this.get(this.id);
    },

    hasPreviews() {
      return this.acknowledgement.columnPreviews != null
        && this.acknowledgement.columnPreviews.length > 0;
    },

    showError() {
      return this.type(this.id) === AcknowledgementType.WARNING
        || this.type(this.id) === AcknowledgementType.FAILED;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.uploads-dataset {
  @include flex("block", "column", "start", "stretch");
  @include stretch;

  padding: 1.5rem 0;
  overflow-y: auto;

  .loading-blocks-overlay {
    height: 280px;
  }

  .error {
    @include flex("block", "row", "start", "stretch");

    margin: 0.5rem 2rem 1rem;
  }
}
</style>
