<template>
  <section class="journey-navigation" :class="{ direction, disabled }">
    <chevron-left-icon v-if="direction === 'left'" class="icon"/>
    <chevron-right-icon v-if="direction === 'right'" class="icon"/>
  </section>
</template>

<script>
import { ChevronLeftIcon, ChevronRightIcon } from 'vue-feather-icons';

export default {
  name: 'journey-navigation',

  components: {
    ChevronLeftIcon,
    ChevronRightIcon,
  },

  props: {
    direction: {
      type: String,
      required: true,
      validator: val => val === 'left' || val === 'right',
    },

    disabled: {
      type: Boolean,
      default: false,
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.journey-navigation {
  @include flex("block", "row", "center", "center");
  @include rigid;

  border-radius: $border-radius-medium;
  cursor: pointer;
  padding: 0.5rem;
  transition: all $interaction-transition-time;

  &:hover {
    background-color: rgba(clr('blue'), 0.2);
  }

  &.disabled {
    cursor: not-allowed;

    &:hover {
      background-color: rgba(clr('black'), 0.05);
    }
  }

  .icon {
    color: $body-copy-light;
  }
}
</style>
