<template>
  <div class="filter-split-modal-body-select-value-search">
    <section class="search-wrapper" >
      <i class="fa-regular fa-magnifying-glass icon"></i>
      <input v-model="localSearch" class="input" placeholder="Search Metadata"/>
      <i class="fa-regular fa-xmark icon x-icon" v-if="localSearch" @click="clearSearch"></i>
    </section>
  </div>
</template>

<script>

export default {
  name: 'filter-split-modal-body-select-value-search',

  data() {
    return {
      search: '',
    };
  },

  computed: {
    localSearch: {
      get() {
        return this.search;
      },
      set(search) {
        this.$emit('search', search.toLowerCase());
      },
    },
  },

  methods: {
    clearSearch() {
      this.search = '';
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/mixins";
@import "src/styles/variables";

.filter-split-modal-body-select-value-search {
  @include flex("block", "row", "start", "center");
  @include stretch;

  .search-wrapper {
    @include flex("block", "row", "start", "center");
    @include shrink;

    background-color: rgba(212, 212, 212, 0.4);
    border-radius: 15px;
    flex-grow: 1;
    height: 30px;
    margin-right: 0;
    margin-top: 1rem;
    padding: 0.4rem 0.8rem;
    width: 100%;

    .icon {
      color: $nps-blue;
      cursor: pointer;
      font-size: $font-size-base;

      &.x-icon {
        &:hover {
          color: clr("red");
          transform: scale(1.1);
        }
      }
    }

    .input {
      @include stretch;

      background: unset;
      border: none;
      font-size: $font-size-sm;
      margin: 0 0.5em;
      outline: none;

      &::placeholder {
        color: $nps-blue;
        opacity: 0.4;
      }
    }
  }
}
</style>
