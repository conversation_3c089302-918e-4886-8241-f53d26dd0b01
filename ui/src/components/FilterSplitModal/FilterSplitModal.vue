<template>
  <section class="filter-split-modal">
    <section class="header">
      <i class="fa-solid fa-split icon-split icon"></i>
      <span class="text">Split Dataset View By:</span>
    </section>

    <filter-split-modal-body class="body" />

    <section class="footer">
      <base-button class="cancel" colour="light" size="small" type="link" @click="onCancel">Cancel</base-button>
      <loading-blocks-overlay v-if="splitting" size="small" />
      <base-button v-else class="confirm" colour="base" size="small" @click="onConfirm" :disabled="hasError">Split</base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import FilterSplitModalBody from '@/components/FilterSplitModal/FilterSplitModalBody';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';

import { metadataRequest } from '@/services/request';

export default {
  name: 'filter-split-modal',

  components: {
    LoadingBlocksOverlay,
    BaseButton,
    BaseInput,
    FilterSplitModalBody,
  },

  data() {
    return {
      splitting: false,
    };
  },

  computed: {
    ...mapState('snippetsFilter', ['splitMetadataValues']),

    hasError() {
      return this.splitMetadataValues.length < 2 || this.splitMetadataValues.length > 20;
    },
  },

  created() {
    this.resetSplitMetadata();
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('toast', ['add']),

    ...mapActions('snippetsFilter', ['resetSplitMetadata']),

    onCancel() {
      this.closeModal();
    },

    async onConfirm() {
      if (this.hasError) return;
      this.splitting = true;

      await metadataRequest.splitFilterView();

      this.splitting = false;
      this.closeModal();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.filter-split-modal {
  @include modal;

  width: 450px;

  .header {
    @include flex("block", "row", "start", "center");

    border-bottom: unset;
    font-weight: $font-weight-bold;
    padding: 1rem;

    .icon {
      height: $font-size-sm;
      margin-right: 0.5rem;
      width: $font-size-sm;
    }
  }

  .body {
    @include flex("block", "column", "start", "start");

    background-color: unset;
    padding: 0;
  }

  .footer {
    padding: 1rem 2rem;

    .base-button {
      padding: 0.5rem 1rem;
    }

    .cancel {
      margin-left: -0.6rem;
    }

    .confirm {
      &.disabled-true {
        cursor: not-allowed;
      }
    }
  }
}
</style>
