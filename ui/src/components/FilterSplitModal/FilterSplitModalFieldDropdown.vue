<template>
  <section class="filter-split-modal-field-dropdown"
           v-click-outside-handler="{
             handler: 'onClickOutside',
           }"
  >
    <template v-for="(metadata) in metadataList">
      <section class="item" :class="{ disabled: isDisabled(metadata) }" @click="onSelect(metadata)">
        <span>{{metadata.name}}</span>
        <span v-if="isDisabled(metadata)">&nbsp- Cannot be split while used as filter</span>
      </section>
    </template>
  </section>
</template>

<script>
import { mapGetters, mapState } from 'vuex';

import clickOutsideHandler from '@/directives/click-outside-handler';

export default {
  name: 'filter-split-modal-field-dropdown',

  directives: {
    clickOutsideHandler,
  },

  data() {
    return {
      selected: null,
    };
  },

  computed: {
    ...mapState('datasets', {
      datasetId: 'active',
    }),

    ...mapState('snippets', ['filters']),

    ...mapGetters('snippets', ['getFilterMetadataList']),

    // TODO: this is a copy from CommentsSortControl, maybe we should refactor it to easier to use
    metadataList() {
      const rs = [];
      const { metadataColumns, metadataHeaders, metadataTypes } = this.getFilterMetadataList({ datasetId: this.datasetId });

      if (
        !metadataColumns.length
          || !metadataHeaders.length
          || !metadataTypes.length
      ) {
        return rs;
      }

      for (let i = 0; i < metadataColumns.length; i += 1) {
        // note: may user wanna name their metadata-column as 'Comments'?
        if (metadataColumns[i] !== -1) {
          rs.push({
            column: metadataColumns[i],
            name: metadataHeaders[i],
            type: metadataTypes[i],
          });
        }
      }

      return rs;
    },
  },

  methods: {
    isDisabled(metadata) {
      return this.filters
        .map(o => o.column?.val)
        .includes(metadata.name);
    },

    onClickOutside() {
      this.$emit('close');
    },

    onSelect(metadata) {
      if (this.isDisabled(metadata)) return;

      this.$emit('select', metadata);
      this.$emit('close');
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.filter-split-modal-field-dropdown {
  @include panel;

  background-color: #ffffff;
  border-radius: 4px;
  border: 1px solid #dee1e4;
  box-shadow:  0 2px 4px 0 rgba(0, 0, 0, 0.2);
  left: calc(50% - 185px);
  position: absolute;
  width: 369px;
  z-index: 99;

  .item {
    @include flex('block', 'row', 'start', 'center');

    cursor: pointer;
    padding: 0.5rem 1rem;

    .label {
      width: 100%;
    }

    &.disabled {
      opacity: 0.4;
    }

    &:hover {
      background-color: clr('purple', 'lighter');
    }
  }
}
</style>
