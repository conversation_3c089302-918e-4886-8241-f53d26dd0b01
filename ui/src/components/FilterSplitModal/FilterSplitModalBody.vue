<template>
  <section class="filter-split-modal-body">
    <filter-split-modal-body-select-type class="item-type" />
    <filter-split-modal-body-select-value v-if="splitMetadataType.name" class="item-value" />
  </section>
</template>

<script>
import { mapState } from 'vuex';

import FilterSplitModalBodySelectType from '@/components/FilterSplitModal/FilterSplitModalBodySelectType';
import FilterSplitModalBodySelectValue from '@/components/FilterSplitModal/FilterSplitModalBodySelectValue';

export default {
  name: 'filter-split-modal-body',

  components: {
    FilterSplitModalBodySelectType,
    FilterSplitModalBodySelectValue,
  },

  computed: {
    ...mapState('snippetsFilter', ['splitMetadataType']),
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.filter-split-modal-body {
  font-size: $font-size-xs;
  width: 100%;

  .item-type {
    padding: 2rem 1rem;
  }

  .item-value {
    border-top: $border-light solid #E6E6E6;
    padding: 2rem 1rem;
  }
}
</style>
