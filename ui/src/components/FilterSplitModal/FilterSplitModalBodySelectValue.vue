<template>
  <section class="filter-split-modal-body-select-value">
    <section class="description">
      <section class="number">
        2
      </section>
      <span class="text">Select '{{splitMetadataType.name}}' Metadata</span>
    </section>
    <section class="instruction">
      <span class="instruction-text">Select 2 or more Metadata items to create a split</span>
    </section>
    <section class="value">
      <filter-split-modal-body-select-value-search @search="onSearch" />
      <section
          v-for="(item, index) in filteredValues"
          :key="index"
          class="item"
          @click="onSelect(item)"
      >
        <base-checkbox-solid :value="isSelected(item)" />
        <span class="content">{{ item.content }}</span>
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseCheckboxSolid from '@/components/Base/BaseCheckboxSolid';
import FilterSplitModalBodySelectValueSearch from '@/components/FilterSplitModal/FilterSplitModalBodySelectValueSearch';

import { metadataApi } from '@/services/api';

export default {
  name: 'filter-split-modal-body-select-value',

  components: {
    BaseCheckboxSolid,
    FilterSplitModalBodySelectValueSearch,
  },

  data() {
    return {
      metadataValues: [],
      search: '',
    };
  },

  computed: {
    ...mapGetters('snippets', ['getFilterMetadataInfo']),

    ...mapState('datasets', ['active']),

    ...mapState('snippetsFilter', ['splitMetadataType', 'splitMetadataValues']),

    columnSplitMetadata() {
      return this.splitMetadataType.column;
    },

    filteredValues() {
      return this.metadataValues.filter(mdata => {
        if (!mdata.content) return false;
        return mdata.content.toLowerCase().includes(this.search);
      }).sort((a, b) => {
        if (!Number.isNaN(parseFloat(a.content)) && !Number.isNaN(parseFloat(b.content))) {
          return parseFloat(a.content) - parseFloat(b.content);
        }
        return a.content.localeCompare(b.content);
      });
    },

    splitMetadataObj() {
      return this.getFilterMetadataInfo({ byColumn: this.columnSplitMetadata });
    },
  },

  async created() {
    if (this.columnSplitMetadata != null) this.metadataValues = await metadataApi.getDistinctValuesForTextData(this.active, this.splitMetadataObj.index);
  },

  watch: {
    async columnSplitMetadata() {
      if (this.columnSplitMetadata != null) this.metadataValues = await metadataApi.getDistinctValuesForTextData(this.active, this.splitMetadataObj.index);
    },
  },

  methods: {
    ...mapActions('snippetsFilter', ['addSplitMetadataValue', 'removeSplitMetadataValue']),

    onSearch(search) {
      this.search = search;
    },

    onSelect(item) {
      if (this.isSelected(item)) {
        this.removeSplitMetadataValue({ value: item.content });
      } else {
        this.addSplitMetadataValue({ value: item.content });
      }
    },

    isSelected(item) {
      return this.splitMetadataValues.includes(item.content);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.filter-split-modal-body-select-value {
  @include scrollbar-thin;

  max-height: 360px;
  overflow-y: auto;
  width: 100%;

  .description {
    @include flex("block", "row", "start", "center");

    color: #1F2734;
    font-weight: $font-weight-bold;

    .number {
      @include flex("block", "row", "center", "center");

      background-color: #DADADA;
      border-radius: 50%;
      font-size: $font-size-xxs;
      height: 1rem;
      width: 1rem;
      margin-right: 0.5rem;
    }

    .text {
      color: #8F9399;
      text-transform: uppercase;
    }
  }

  .instruction {
    @include flex("block", "row", "start", "center");

    color: #1F2734;
    margin-left: 1.5rem;
    margin-top: 1rem;
    opacity: 0.8;

    .instruction-text {
      font-size: $font-size-xxs;
    }
  }
  .value {
    padding-left: 1.5rem;
    padding-right: 1.5rem;

    .item {
      @include flex("block", "row", "start", "center");

      cursor: pointer;
      padding-top: 1rem;

      .content {
        margin-left: 0.6rem;
      }
    }
  }
}
</style>
