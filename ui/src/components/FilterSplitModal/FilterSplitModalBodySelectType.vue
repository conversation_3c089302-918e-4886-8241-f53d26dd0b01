<template>
  <section class="filter-split-modal-body-select-type">
    <section class="description">
      <section class="number">
        1
      </section>
      <span class="text">Select Metric Type</span>
    </section>
    <section class="input" @click.stop="open = !open" >
      <base-input :readonly=true v-model="splitMetadataType.name" :placeholder="'Select metric type'"/>
      <section class="dropdown-icon" :class="{ open }" >
        <i class="fa fa-caret-down icon-dropdown" :class="{ open }" />
      </section>
    </section>
    <filter-split-modal-field-dropdown v-if="open" @close="open = false" @select="onSelect" />
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import FilterSplitModalFieldDropdown from '@/components/FilterSplitModal/FilterSplitModalFieldDropdown';

export default {
  name: 'filter-split-modal-body-select-type',

  components: {
    BaseButton,
    BaseInput,
    FilterSplitModalFieldDropdown,
  },

  data() {
    return {
      open: false,
    };
  },

  computed: {
    ...mapState('datasets', {
      datasetId: 'active',
    }),

    ...mapState('snippetsFilter', ['splitMetadataType']),
  },

  methods: {
    ...mapActions('snippetsFilter', ['setSplitMetadataType']),

    onSelect(metadata) {
      this.setSplitMetadataType({ value: metadata });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.filter-split-modal-body-select-type {
  width: 100%;

  .description {
    @include flex("block", "row", "start", "center");

    color: #1F2734;
    font-weight: $font-weight-bold;

    .number {
      @include flex("block", "row", "center", "center");

      background-color: #DADADA;
      border-radius: 50%;
      font-size: $font-size-xxs;
      height: 1rem;
      width: 1rem;
      margin-right: 0.5rem;
    }

    .text {
      color: #8F9399;
      text-transform: uppercase;
    }
  }

  .input {
    @include flex('inline', 'row', 'start', 'center');

    margin-top: 0.8rem;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    width: 100%;

    .base-input {
      @include flex('inline', 'row', 'start', 'center');

      background-color: clr("white");
      border: $border-standard;
      border-radius: $border-radius-medium 0 0 $border-radius-medium;
      cursor: pointer;
      height: 2.1rem;
      padding: 0.5rem 1rem;
    }

    .dropdown-icon {
      @include panel;
      @include flex('inline', 'row', 'start', 'center');

      border-left: 0;
      border-radius: 0 $border-radius-medium $border-radius-medium 0;
      box-shadow: unset;
      cursor: pointer;
      font-size: 1.1rem;
      height: 2.1rem;
      padding-left: 0.6rem;
      width: 2.1rem;

      &:hover {
        background-color: rgba(0, 0, 0, 0.05);
      }

      &.open {
        background-color: rgba(0, 0, 0, 0.05);
      }

      .icon-dropdown {
        transition: all $interaction-transition-time;

        &.open {
          transform: rotate(180deg);
        }
      }
    }
  }
}
</style>
