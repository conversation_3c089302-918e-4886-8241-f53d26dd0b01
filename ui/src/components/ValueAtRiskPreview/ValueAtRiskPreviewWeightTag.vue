<template>
  <section class="value-at-risk-preview-weight-tag" :class="{ low: isLow, med: isMed, high: isHigh }">
    <label>{{label}}</label>
  </section>
</template>

<script>
import { startCase } from 'lodash-es';
import { mapState } from 'vuex';

export default {
  name: 'value-at-risk-preview-weight-tag',

  computed: {
    ...mapState('valueAtRisk', ['varInfo']),

    label() {
      if (this.isLow) return 'Low Risk';
      if (this.isHigh) return 'High Risk';
      return 'Balanced Risk';
    },

    isLow() {
      return this.weight === 'Low';
    },

    isMed() {
      return this.weight === 'Medium';
    },

    isHigh() {
      return this.weight === 'High';
    },

    weight() {
      return this.varInfo.valueAtRiskWeight ? startCase(this.varInfo?.valueAtRiskWeight.toLowerCase()) : 'Medium';
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.value-at-risk-preview-weight-tag {
  @include flex("block", "row", "start", "center");

  border-radius: 14px;
  height: 14px;

  &.low {
    background-color: #36B422;
  }

  &.med {
    background-color: rgba(58, 70, 114, 0.9);
  }

  &.high {
    background-color: #EB534C;
  }

  label {
    @include flex("block", "row", "center", "center");

    color: white;
    font-size: 8px;
    font-weight: $font-weight-bold;
    line-height: 14px;
    padding: 0 0.4rem;
    text-transform: uppercase;
  }
}
</style>
