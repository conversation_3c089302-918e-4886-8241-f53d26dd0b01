<template>
  <section class="distribution-options-dropdown">
    <h4>Background Visibility</h4>
    <section class="option">
      <vue-slider v-model="backgroundOpacity" v-bind="sliderOpacity" :lazy="true" />
    </section>

    <h4>Line Visibility</h4>
    <section class="option">
      <vue-slider v-model="lineOpacity" v-bind="sliderOpacity" :lazy="true" />
    </section>

    <h4>Line Width</h4>
    <section class="option">
      <vue-slider v-model="lineSize" v-bind="sliderWidth" :lazy="true" />
    </section>

    <h4>Area Visibility</h4>
    <section class="option">
      <vue-slider v-model="areaOpacity" v-bind="sliderOpacity" :lazy="true" />
    </section>

    <h4>Dot Visibility</h4>
    <section class="option">
      <vue-slider v-model="dotOpacity" v-bind="sliderOpacity" :lazy="true" />
    </section>

    <h4>Dot Size</h4>
    <section class="option">
      <vue-slider v-model="dotSize" v-bind="sliderWidth" :lazy="true" />
    </section>

    <h4>Monochromaticity</h4>
    <section class="option" @click="onClickLineGreyscale">
      <base-checkbox :value="radarLineGreyscale"></base-checkbox>
      <span>Line</span>
    </section>
    <section class="option" @click="onClickAreaGreyscale">
      <base-checkbox :value="radarAreaGreyscale"></base-checkbox>
      <span>Area</span>
    </section>
    <section class="option" @click="onClickDotGreyscale">
      <base-checkbox :value="radarDotGreyscale"></base-checkbox>
      <span>Dot</span>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import VueSlider from 'vue-slider-component';

import BaseCheckbox from '@/components/Base/BaseCheckbox';
import BlurCloseable from '@/components/Mixins/BlurCloseable';
import sliderDefaultStyles from '@/helpers/slider-default-styles';

export default {
  name: 'distribution-options-dropdown',

  components: {
    BaseCheckbox,
    VueSlider,
  },

  mixins: [BlurCloseable],

  computed: {
    ...mapState('chartOptions', {
      radarAreaGreyscale: state => state.radarAreaGreyscale,
      radarAreaOpacity: state => state.radarAreaOpacity,
      radarBackgroundOpacity: state => state.radarBackgroundOpacity,
      radarDotGreyscale: state => state.radarDotGreyscale,
      radarDotOpacity: state => state.radarDotOpacity,
      radarDotSize: state => state.radarDotSize,
      radarLineGreyscale: state => state.radarLineGreyscale,
      radarLineOpacity: state => state.radarLineOpacity,
      radarLineSize: state => state.radarLineSize,
    }),

    areaOpacity: {
      get() {
        return this.radarAreaOpacity;
      },
      set(opacity) {
        this.setRadarAreaOpacity({ opacity });
      },
    },

    backgroundOpacity: {
      get() {
        return this.radarBackgroundOpacity;
      },
      set(opacity) {
        this.setRadarBackgroundOpacity({ opacity });
      },
    },

    dotOpacity: {
      get() {
        return this.radarDotOpacity;
      },
      set(opacity) {
        this.setRadarDotOpacity({ opacity });
      },
    },

    dotSize: {
      get() {
        return this.radarDotSize;
      },
      set(size) {
        this.setRadarDotSize({ size });
      },
    },

    lineOpacity: {
      get() {
        return this.radarLineOpacity;
      },
      set(opacity) {
        this.setRadarLineOpacity({ opacity });
      },
    },

    lineSize: {
      get() {
        return this.radarLineSize;
      },
      set(size) {
        this.setRadarLineSize({ size });
      },
    },

    sliderBase() {
      return {
        ...sliderDefaultStyles,
        width: '150px',
      };
    },

    sliderOpacity() {
      return {
        ...this.sliderBase,
        min: 0,
        max: 1,
        interval: 0.01,
      };
    },

    sliderWidth() {
      return {
        ...this.sliderBase,
        min: 1,
        max: 5,
      };
    },
  },

  methods: {
    ...mapActions('chartOptions', [
      'setRadarAreaGreyscale',
      'setRadarAreaOpacity',
      'setRadarBackgroundOpacity',
      'setRadarDotGreyscale',
      'setRadarDotOpacity',
      'setRadarDotSize',
      'setRadarLineGreyscale',
      'setRadarLineOpacity',
      'setRadarLineSize',
    ]),

    onClickAreaGreyscale() {
      this.setRadarAreaGreyscale({ greyscale: !this.radarAreaGreyscale });
    },

    onClickDotGreyscale() {
      this.setRadarDotGreyscale({ greyscale: !this.radarDotGreyscale });
    },

    onClickLineGreyscale() {
      this.setRadarLineGreyscale({ greyscale: !this.radarLineGreyscale });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.distribution-options-dropdown {
  @include flex("block", "column", "start", "stretch");
  @include rigid;
  @include panel;

  font-size: $font-size-xs;
  padding: 0.3rem 0.8rem 0.5rem;
  position: absolute;
  right: 0;
  top: 1.5rem;
  z-index: 99;

  h4 {
    color: $body-copy-light;
    font-size: $font-size-xxs;
    letter-spacing: $letter-spacing-sm;
    margin: 1em 0;
    text-transform: uppercase;
  }

  .option {
    @include flex("block", "row", "start", "center");

    cursor: pointer;
    margin: 0 0.5rem 0.5rem 0.5rem;
    width: 150px;
    position: relative;

    .base-checkbox {
      margin-right: 0.5em;
      pointer-events: none;
    }

    span {
      font-size: $font-size-xs;
    }
  }
}
</style>
