<template>
  <svg
    :id="`distribution-chart-${type}`"
    class="distribution-chart"
    :width="width"
    :height="height"
  >
    <defs>
      <linearGradient
        v-for="(section, index) in emotionSections"
        :id="`radar-background-gradient-${index}`"
        :key="`radar-background-gradient-${index}`"
        :x1="`${section.x1}`"
        :y1="`${section.y1}`"
        :x2="`${section.x2}`"
        :y2="`${section.y2}`"
      >
        <stop
          offset="0"
          :style="`
          stop-color:${getRadarColor(index)};
          stop-opacity:${radarBackgroundOpacity};
        `"
        />
        <stop
          offset="0.4"
          :style="`
          stop-color:${getRadarColor(index)};
          stop-opacity:${radarBackgroundOpacity * 0.75};
        `"
        />
        <stop
          offset="0.6"
          :style="`
          stop-color:${getRadarColor(index + 1)};
          stop-opacity:${radarBackgroundOpacity * 0.75};
        `"
        />
        <stop
          offset="1"
          :style="`
          stop-color:${getRadarColor(index + 1)};
          stop-opacity:${radarBackgroundOpacity};
        `"
        />
      </linearGradient>
    </defs>

    <g :transform="`translate(${width/2},${height/2})`">
      <g class="axis-wrapper">
        <!-- Outer bounding circle -->
        <polygon
          fill="none"
          :points="getPolygonPoints(0)"
          :stroke="colours.grey"
          stroke-width="1"
        />
        <!-- Colored emotion sections -->
        <polygon
          v-for="(section, index) in emotionSections"
          :fill="`url(#radar-background-gradient-${index})`"
          :points="`${section.points}`"
          :stroke="null"
          stroke-width="0"
        />
        <!-- Inner axis markers -->
        <polygon
          v-for="c in innerCircles"
          :key="`axis-circle-${c}`"
          fill="none"
          :points="getPolygonPoints(c)"
          :stroke="colours.grey"
          stroke-dasharray="2"
          stroke-width="1"
        />
        <!-- Inner axis text -->
        <text
          v-for="c in innerCircles"
          :key="`axis-text-${c}`"
          :opacity="maxRate ? 1 : 0"
          style="font-size: 8px;"
          text-anchor="middle"
          dy="0.35rem"
          x="0"
          :y="-(radius / (innerCircles + 1) * c + 8)"
        >{{ getInnerCircleTexts(c) }}%</text>

        <!-- Axis lines -->
        <line
          v-for="index in axisNames.length"
          :key="`axis-line-${index}`"
          :x1="0"
          :y1="0"
          :x2="radius * getAngleX(index)"
          :y2="radius * getAngleY(index)"
          :stroke="colours.grey"
          stroke-dasharray="2 4"
          stroke-width="1"
        />
        <distribution-chart-emotion-button
          v-for="{name, index, pOrder} in axisNames"
          :key="`axis-text-${name}`"
          :name="name"
          :index="index"
          :emotionName="getEmotionName(name)"
          :coordinate="getPointCoord(0, pOrder)"
          :rotatedPosition="getRotatedPosition(pOrder + rotation)"
        />
      </g>

      <!-- Data - Circles & paths -->
      <g v-for="(dataset, key) in data" :key="key" :class="`area-wrapper-${key}`">
        <path
          :d="radarLine(pathData(key))"
          :stroke="radarLineGreyscale ? colours.greyscale : colour(Number(key))"
          :stroke-width="radarLineSize"
          :stroke-opacity="lineOpacity(Number(key))"
          :fill="radarAreaGreyscale ? colours.greyscale : colour(Number(key))"
          :fill-opacity="areaOpacity(Number(key))"
        />
        <circle
          v-for="(data, index) in pathData(key)"
          :key="`path-circle-${key}-${index}`"
          :r="radarDotSize"
          :cx="rScale(data) * getAngleX(index)"
          :cy="rScale(data) * getAngleY(index)"
          :fill="radarDotGreyscale ? colours.greyscale : colour(Number(key))"
          :fill-opacity="dotOpacity(Number(key))"
        />
      </g>
    </g>
  </svg>
</template>

<script>
import { curveCardinalClosed, lineRadial, max, scaleLinear } from 'd3';
import { mapGetters, mapState } from 'vuex';

import DistributionChartEmotionButton from '@/components/DistributionChart/DistributionChartEmotionButton';
import indexes from '@/helpers/indexes';

export default {
  name: 'distribution-chart',

  components: {
    DistributionChartEmotionButton,
  },

  props: {
    data: {
      type: Object,
      required: true,
    },
    hidden: {
      type: Array,
      required: true,
    },
    type: {
      type: String,
      required: true,
    },
    totalScores: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      colours: {
        grey: '#aba6b4',
        greyscale: '#0083ff',
      },
      height: 230,
      innerCircles: 3,
      radarColors: {
        0: '#c3c300',
        1: '#009b00',
        2: '#009b56',
        3: '#0079cd',
        4: '#00509b',
        5: '#9b009b',
        6: '#cd0000',
        7: '#cd6800',
      },
      rotation: 0,
      margin: 40,
      width: 330,
    };
  },

  computed: {
    ...mapGetters('datasets', ['colour']),

    ...mapState('chartOptions', {
      radarAreaGreyscale: state => state.radarAreaGreyscale,
      radarAreaOpacity: state => state.radarAreaOpacity,
      radarBackgroundOpacity: state => state.radarBackgroundOpacity,
      radarDotGreyscale: state => state.radarDotGreyscale,
      radarDotOpacity: state => state.radarDotOpacity,
      radarDotSize: state => state.radarDotSize,
      radarLineGreyscale: state => state.radarLineGreyscale,
      radarLineOpacity: state => state.radarLineOpacity,
      radarLineSize: state => state.radarLineSize,
    }),

    angleSlice() {
      return Math.PI * 2 / this.axisNames.length;
    },

    axisNames() {
      return indexes
        .sort((a, b) => {
          if (a.plutchikWheelOrder > b.plutchikWheelOrder) {
            return 1;
          }
          if (b.plutchikWheelOrder > a.plutchikWheelOrder) {
            return -1;
          }
          return 0;
        })
        .map(i => {
          return { name: i.name, index: i.position.display, pOrder: i.plutchikWheelOrder };
        });
    },

    emotionSections() {
      const sections = [];
      for (let i = 0; i < indexes.length; i += 1) {
        const current = this.getPointCoord(0, i);
        const next = this.getPointCoord(0, i + 1 < indexes.length ? i + 1 : 0);
        const section = this.getGradientDirection(this.getRotatedPosition(i + this.rotation));
        section.points = `${current.x},${current.y} ${next.x},${next.y} 0,0`;

        sections[i] = section;
      }
      return sections;
    },

    outBoundVal() {
      if (!this.maxRate) {
        return 100;
      }

      let rs;
      if (this.maxRate < 20) {
        rs = Math.ceil(this.maxRate / 2) * 2;
      } else {
        rs = Math.ceil(this.maxRate / 5) * 5;
      }
      return rs;
    },

    maxRate() {
      const scoreRates = Object.entries(this.data).reduce((obj, [i, val]) => {
        if (this.totalScores[i]) {
          obj[i] = Math.round((max(Object.values(val)) / this.totalScores[i]) * 1000) / 10;
        }
        return obj;
      }, {});

      return max(Object.values(scoreRates));
    },

    radarLine() {
      return lineRadial()
        .radius(d => {
          return this.rScale(d);
        })
        .angle((d, i) => {
          return (this.getRotatedPosition(i + this.rotation)) * this.angleSlice;
        })
        .curve(curveCardinalClosed.tension(0.25));
    },

    radius() {
      return Math.min(this.width, this.height) / 2 - this.margin;
    },

    rScale() {
      return scaleLinear()
        .range([0, this.radius * 0.95])
        .domain([0, this.outBoundVal]);
    },
  },

  methods: {
    areaOpacity(id) {
      if (this.hidden.includes(id)) return 0;

      return this.radarAreaOpacity;
    },

    dotOpacity(id) {
      if (this.hidden.includes(id)) return 0;

      return this.radarDotOpacity;
    },

    getAngleX(i) {
      return Math.cos(this.angleSlice * (this.getRotatedPosition(i + this.rotation)) - Math.PI / 2);
    },

    getAngleY(i) {
      return Math.sin(this.angleSlice * (this.getRotatedPosition(i + this.rotation)) - Math.PI / 2);
    },

    getGradientDirection(i) {
      const rs = {};
      if (i === 0) {
        rs.x1 = -0.25;
        rs.y1 = -0.25;
        rs.x2 = 1.25;
        rs.y2 = 0.75;
      } else if (i === 1) {
        rs.x1 = 0.25;
        rs.y1 = 0;
        rs.x2 = 1;
        rs.y2 = 1.35;
      } else if (i === 2) {
        rs.x1 = 1;
        rs.y1 = -0.25;
        rs.x2 = 0.25;
        rs.y2 = 1;
      } else if (i === 3) {
        rs.x1 = 0.5;
        rs.y1 = 0;
        rs.x2 = -0.25;
        rs.y2 = 0.5;
      } else if (i === 4) {
        rs.x1 = 1.25;
        rs.y1 = 1;
        rs.x2 = 0;
        rs.y2 = 0.25;
      } else if (i === 5) {
        rs.x1 = 0.75;
        rs.y1 = 1;
        rs.x2 = 0;
        rs.y2 = -0.25;
      } else if (i === 6) {
        rs.x1 = -0.1;
        rs.y1 = 1;
        rs.x2 = 0.5;
        rs.y2 = 0;
      } else {
        rs.x1 = 0;
        rs.y1 = 0.75;
        rs.x2 = 1.25;
        rs.y2 = 0;
      }

      return rs;
    },

    getEmotionName(axisName) {
      const map = { high: 0, medium: 1, low: 2 };
      const index = indexes.find(i => i.name === axisName);
      const emotion = index.emotions[map[this.type]];

      return emotion.capitalize();
    },

    getInnerCircleTexts(index) {
      return Math.round(this.outBoundVal * 0.25 * index * 10) / 10;
    },

    getPointCoord(posRatio, iVal) {
      const ratio = posRatio === 0 ? 1 : 0.25 * posRatio;
      const x = this.radius * ratio * this.getAngleX(iVal);
      const y = this.radius * ratio * this.getAngleY(iVal);
      return { x, y };
    },

    getPolygonPoints(posRatio) {
      let points = '';
      for (let i = 0; i < indexes.length; i += 1) {
        const { x, y } = this.getPointCoord(posRatio, i);
        points += ` ${x},${y}`;
      }
      return points.trim();
    },

    getRadarColor(i) {
      i = i < indexes.length ? i : 0;
      return this.radarColors[i];
    },

    getRotatedPosition(i) {
      if (i < 0) {
        return this.getRotatedPosition(8 + i);
      }
      if (i > 7) {
        return this.getRotatedPosition(i - 8);
      }

      return i;
    },

    maxValue(id) {
      return max(Object.values(this.data[id]));
    },

    lineOpacity(id) {
      if (this.hidden.includes(id)) return 0;

      return this.radarLineOpacity;
    },

    pathData(id) {
      let rs = this.axisNames.map(i => this.data[id][i.name]);
      rs = Object.entries(rs).reduce((obj, [k, v]) => {
        if (this.totalScores[id]) {
          obj[k] = Math.round(v / this.totalScores[id] * 1000) / 10;
        }
        return obj;
      }, []);
      return rs;
    },
  },
};
</script>
