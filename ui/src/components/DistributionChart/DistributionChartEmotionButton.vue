<template>
  <g
    class="emotion-container"
    @mouseenter="isHovered = true"
    @mouseleave="isHovered = false"
    @click="onClickName"
  >
    <!-- button's back stroke -->
    <rect
      class="emotion-btn-back-stroke"
      :class="{ active: isActive }"
      :height="getButtonHeight + 4"
      :rx="getButtonHeight / 2 + 2"
      :ry="getButtonHeight / 2 + 2"
      :width="getButtonWidth + 4"
      :x="crdXButton - 2"
      :y="crdYButton - 2"
      v-if="isActive || isHovered"
    ></rect>
    <!-- button -->
    <rect
      class="emotion-btn"
      :class="{ active: isActive }"
      :height="getButtonHeight"
      :rx="getButtonHeight / 2"
      :ry="getButtonHeight / 2"
      :width="getButtonWidth"
      :x="crdXButton"
      :y="crdYButton"
    ></rect>
    <!-- text -->
    <text
      class="emotion-text"
      :class="{ active: isActive }"
      dy="0.35rem"
      ref="emotionText"
      text-anchor="middle"
      :x="crdXText"
      :y="crdYText"
    >{{ emotionName }}</text>
  </g>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import Index from '@/enum/index';
import { themesRequest } from '@/services/request';

export default {
  name: 'distribution-chart-emotion-button',

  props: {
    coordinate: {
      type: Object,
      required: true,
    },
    emotionName: {
      type: String,
      required: true,
    },
    index: {
      type: Number,
      required: true,
    },
    name: {
      type: String,
      required: true,
    },
    rotatedPosition: {
      type: Number,
      required: true,
    },
  },

  data() {
    return {
      isHovered: false,
    };
  },

  computed: {
    ...mapGetters('chartOptions', ['enabledIndices']),

    ...mapState('datasets', {
      datasetId: state => state.active,
    }),

    ...mapState('themes', {
      themeIndex: state => state.index,
      themeState: state => state,
    }),

    crdXButton() {
      return this.crdXText - (this.getButtonWidth / 2);
    },

    crdYButton() {
      return this.crdYText - (this.getButtonHeight / 3);
    },

    crdXText() {
      let x = this.coordinate && this.coordinate.x ? this.coordinate.x * 1.2 : 0;
      if (this.rotatedPosition === 1 || this.rotatedPosition === 3) {
        x += (this.getButtonWidth * 0.4);
      } else if (this.rotatedPosition === 2) {
        x += (this.getButtonWidth * 0.37);
      } else if (this.rotatedPosition === 5 || this.rotatedPosition === 7) {
        x -= (this.getButtonWidth * 0.42);
      } else if (this.rotatedPosition === 6) {
        x -= (this.getButtonWidth * 0.37);
      }
      return x;
    },

    crdYText() {
      let y = this.coordinate && this.coordinate.y ? this.coordinate.y * 1.2 : 0;
      if (this.rotatedPosition === 6 || this.rotatedPosition === 2) {
        y -= 3;
      } else if (this.rotatedPosition === 0) {
        y -= 3;
      }
      return y;
    },

    getButtonHeight() {
      return this.$refs.emotionText ?
        this.$refs.emotionText.getBBox().height * 2 :
        18;
    },

    isActive() {
      return this.getEnumIndex(this.index) === this.themeIndex;
    },

    getButtonWidth() {
      return this.$refs.emotionText ?
        this.$refs.emotionText.getComputedTextLength() + 10 :
        (this.emotionName.length * 6) + 10;
    },
  },

  methods: {
    ...mapActions('snippets', ['resetSnippets']),

    ...mapActions('themes', [
      'selectFirstTheme',
      'selectTheme',
      'setIndex',
      'setThemes',
    ]),

    getEnumIndex(indexVal) {
      return Index.enumValueOf(this.enabledIndices[indexVal]);
    },

    async onClickName() {
      this.resetSnippets();
      this.selectTheme({ theme: null });
      this.setIndex({ index: this.getEnumIndex(this.index) });

      // clear the list first, so vuejs can detect changes within child-objects
      this.setThemes({ themes: [] });
      const themes = await themesRequest.fetchThemes();

      this.setThemes({ themes });
      this.selectFirstTheme();
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";

.emotion-container {
  cursor: pointer;
}

.emotion-btn {
  fill: clr("grey");
  opacity: 0.3;

  &.active {
    fill: clr("purple");
    opacity: 1;
  }
}

.emotion-btn-back-stroke {
  fill: none;
  opacity: 0.3;
  stroke: clr("grey");
  stroke-width: 1px;

  &.active {
    opacity: 1;
    stroke: clr("purple");
  }
}

.emotion-text {
  font-size: 9px;
  fill: clr("black");

  &.active {
    fill: clr("white");
  }
}
</style>
