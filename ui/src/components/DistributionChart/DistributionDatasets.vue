<template>
  <section class="distribution-datasets">
    <section
      v-for="dataset in selectedLimited"
      :key="dataset"
      class="dataset"
      :class="{ hidden: hidden.includes(dataset) }"
      @click="toggleHidden(dataset)"
    >
      <circle-icon
        class="icon-list"
        :color="colour(dataset)"
        :fill="colour(dataset)"
        size="0.8x"
      ></circle-icon>
      <span>{{ getFromSelected(dataset).label || get(dataset).label }}</span>
      <minus-icon v-if="hidden.includes(dataset)" class="icon" size="0.8x" />
      <check-icon v-else class="icon" size="0.8x" />
    </section>
  </section>
</template>

<script>
import chroma from 'chroma-js';

import { CheckIcon, CircleIcon, MinusIcon } from 'vue-feather-icons';
import { mapGetters } from 'vuex';

export default {
  name: 'distribution-datasets',

  components: {
    CheckIcon,
    CircleIcon,
    MinusIcon,
  },

  props: {
    hidden: {
      type: Array,
      required: true,
    },
  },

  computed: {
    ...mapGetters('datasets', [
      'colour',
      'get',
      'getFromSelected',
      'selectedLimited',
    ]),
  },

  methods: {
    colourShadow(id) {
      return chroma(this.colour(id))
        .darken(1)
        .hex();
    },

    toggleHidden(id) {
      this.$emit('toggle', id);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.distribution-datasets {
  @include flex("block", "row", "start", "center", "wrap");

  margin: 1em;
  margin-top: 0;

  .dataset {
    @include flex("block", "row", "center", "center");

    background-color: rgba(clr("grey"), 0.5);
    border-radius: $border-radius-medium;
    cursor: pointer;
    margin: 0 0.5rem 0.5rem 0;
    padding: 0.3rem 0.4rem 0.3rem 0.5rem;
    transition: opacity $interaction-transition-time;
    user-select: none;

    &:hover {
      background-color: rgba(clr("grey"), 0.75);
    }

    &.hidden {
      background-color: rgba(clr("grey"), 0.25);

      &:hover {
        background-color: rgba(clr("grey"), 0.75);
      }
    }

    span {
      color: clr("black");
      font-size: $font-size-xxs;
      font-weight: bold;
      max-width: 125px;
      margin-top: 1px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .icon {
      background-color: clr("blue");
      border-radius: $border-radius-medium;
      color: clr("white");
      margin-left: 0.5rem;
      padding: 0;
    }

    .icon-list {
      margin: 0 0.25rem 0 0;
    }
  }
}
</style>
