<template>
  <section class="distribution-export">
    <section class="text" @click.stop="open = !open">
      <span>EXPORT</span>
      <download-icon class="icon" />
    </section>

    <distribution-export-dropdown v-if="open" @close="onClose" />
  </section>
</template>

<script>
import { DownloadIcon } from 'vue-feather-icons';

import DistributionExportDropdown from '@/components/DistributionChart/DistributionExportDropdown';

export default {
  name: 'distribution-export',

  components: {
    DistributionExportDropdown,
    DownloadIcon,
  },

  data() {
    return {
      open: false,
    };
  },

  methods: {
    onClose() {
      this.open = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.distribution-export {
  margin-right: 2rem;
  position: relative;

  .text {
    @include flex("block", "row", "center", "center");

    color: $body-copy-light;
    cursor: pointer;
    transition: all $interaction-transition-time;

    &:hover {
      color: $body-copy;
    }

    span {
      font-size: $font-size-xxs;
      font-weight: $font-weight-medium;
      letter-spacing: $letter-spacing-sm;
      margin-right: 1em;
    }

    .icon {
      height: 1.3em;
    }
  }
}
</style>
