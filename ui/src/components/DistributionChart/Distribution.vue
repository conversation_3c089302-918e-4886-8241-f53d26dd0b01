<template>
  <section class="distribution">
    <section class="header">
      <section class="title">
        <h3>Emotion Distribution</h3>
        <p>Shows distribution of high, medium, and low activation emotions</p>
      </section>
      <section class="options">
        <distribution-export />
        <distribution-options />
      </section>
    </section>

    <distribution-datasets :hidden="hidden" @toggle="toggleHidden"></distribution-datasets>

    <section class="graphs">
      <section class="graph" v-for="[type, data] in chartData" :key="type">
        <h3>{{ type }} activation emotions</h3>
        <distribution-chart
          :data="data"
          :hidden="hidden"
          :type="type"
          :totalScores="totalScores"
        ></distribution-chart>
      </section>
    </section>
  </section>
</template>

<script>
import { without } from 'lodash-es';
import { mapActions, mapGetters, mapState } from 'vuex';

import DistributionChart from '@/components/DistributionChart/DistributionChart';
import DistributionDatasets from '@/components/DistributionChart/DistributionDatasets';
import DistributionExport from '@/components/DistributionChart/DistributionExport';
import DistributionOptions from '@/components/DistributionChart/DistributionOptions';
import intercomEvent from '@/services/IntercomEvent';
import intercomEvents from '@/enum/intercom-events';
import ResultsCharts from '@/enum/results-charts';
import SortDirection from '@/enum/sort-direction';
import ThemesType from '@/enum/themes-type';
import ThemesSort from '@/enum/themes-sort';

export default {
  name: 'distribution',

  components: {
    DistributionChart,
    DistributionDatasets,
    DistributionExport,
    DistributionOptions,
  },

  data() {
    return {
      chart: ResultsCharts.EMOTION_DISTRIBUTION,
      hidden: [],
    };
  },

  computed: {
    ...mapGetters('datasets', [
      'colour',
      'selectedLimited',
      'spreadCounts',
    ]),

    ...mapState('themes', {
      emoIndex: state => state.index,
    }),

    chartData() {
      return Object.entries({ high: this.high, medium: this.medium, low: this.low });
    },

    high() {
      return Object.entries(this.spreadCountsMap).reduce(this.activationReducer('high'), {});
    },

    low() {
      return Object.entries(this.spreadCountsMap).reduce(this.activationReducer('low'), {});
    },

    medium() {
      return Object.entries(this.spreadCountsMap).reduce(this.activationReducer('medium'), {});
    },

    spreadCountsMap() {
      return this.selectedLimited.reduce((acc, id) => {
        acc[id] = this.spreadCounts(id);

        return acc;
      }, {});
    },

    totalScores() {
      const group =
        [
          this.high,
          this.medium,
          this.low,
        ];
      let rs;

      Object.values(group).reduce((currentObj, dataset) => {
        rs = Object.entries(dataset).reduce((acc, [i, data]) => {
          acc[i] = Object.values(data).reduce((total, item) => {
            return total + item;
          }, 0);
          return acc;
        }, {});
        return null;
      }, null);

      return Object.entries(rs).reduce((acc, [k, v]) => {
        if (!this.hidden.includes(Number(k))) {
          acc[k] = v;
        }
        return acc;
      }, {});
    },
  },

  watch: {
    emoIndex() {
      this.setDefaultSort();
    },

    selectedLimited() {
      this.hidden.forEach(h => {
        if (!this.selectedLimited.includes(h)) {
          this.hidden = without(this.hidden, h);
        }
      });
    },
  },

  created() {
    this.setType({ type: ThemesType.OVERVIEW });
    this.setDefaultSort();

    intercomEvent.send(intercomEvents.VIEW_DISTRIBUTION_CHART);
  },

  methods: {
    ...mapActions('themes', [
      'setSort',
      'setSortDirection',
      'setType',
    ]),

    activationReducer(level) {
      return (acc, [k, v]) => {
        acc[k] = v[level];

        return acc;
      };
    },

    onSelectChart(chart) {
      this.$emit('select-chart', chart);
    },

    setDefaultSort() {
      if (this.emoIndex) {
        this.setSort({ by: ThemesSort.ADORESCORE });
        if (this.emoIndex.isPositiveEmotion()) {
          this.setSortDirection({ direction: SortDirection.DESC });
        } else {
          this.setSortDirection({ direction: SortDirection.ASC });
        }
      }
    },

    toggleHidden(id) {
      if (this.hidden.includes(id)) this.hidden = without(this.hidden, id);
      else this.hidden.push(id);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.distribution {
  @include stretch;

  .header {
    @include flex("block", "row", "between", "center");

    padding: 1em;
    width: 100%;

    .title {
      @include panel-header-title;
    }

    .options {
      @include flex("block", "row", "center", "center");

      color: $body-copy-light;
      font-size: $font-size-xs;
    }
  }

  .graphs {
    @include flex("block", "row", "around", "center");

    flex-wrap: wrap;
    margin: $panel-margin;
    margin-top: 0;

    .graph {
      @include flex("block", "column", "center", "center");

      h3 {
        font-size: $font-size-xs;
        text-transform: uppercase;
      }
    }
  }
}
</style>
