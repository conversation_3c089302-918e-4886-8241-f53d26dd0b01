<template>
  <section class="distribution-options">
    <section class="text" @click.stop="open = !open">
      <span>CHART OPTIONS</span>
      <menu-icon class="icon" />
    </section>

    <distribution-options-dropdown v-if="open" @close="open = false" />
  </section>
</template>

<script>
import { MenuIcon } from 'vue-feather-icons';

import DistributionOptionsDropdown from '@/components/DistributionChart/DistributionOptionsDropdown';

export default {
  name: 'distribution-options',

  components: {
    DistributionOptionsDropdown,
    MenuIcon,
  },

  data() {
    return {
      open: false,
    };
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.distribution-options {
  position: relative;

  .text {
    @include flex("block", "row", "center", "center");

    color: $body-copy-light;
    cursor: pointer;
    transition: all $interaction-transition-time;

    &:hover {
      color: $body-copy;
    }

    span {
      font-size: $font-size-xxs;
      font-weight: $font-weight-medium;
      letter-spacing: $letter-spacing-sm;
      margin-right: 1em;
    }

    .icon {
      height: 1.3em;
    }
  }
}
</style>
