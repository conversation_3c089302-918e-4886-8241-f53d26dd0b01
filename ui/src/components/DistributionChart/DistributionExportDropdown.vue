<template>
  <section class="distribution-export-dropdown">
    <h4>Image Scale (for PNG)</h4>
    <section class="option">
      <vue-slider v-model="scale" v-bind="sliderScale" />
    </section>

    <span class="image-size">{{ 280 * scale }}px × {{ 230 * scale }}px per chart</span>

    <h4>Include</h4>
    <section class="option" @click="onClickHigh">
      <base-checkbox :value="exportHigh"></base-checkbox>
      <span>High Activation</span>
    </section>
    <section class="option" @click="onClickMedium">
      <base-checkbox :value="exportMedium"></base-checkbox>
      <span>Medium Activation</span>
    </section>
    <section class="option" @click="onClickLow">
      <base-checkbox :value="exportLow"></base-checkbox>
      <span>Low Activation</span>
    </section>

    <span
      class="help"
    >Use the chart options menu to configure the look of your charts before exporting.</span>

    <base-button :disabled="disabled" size="small" @click="exportImage('png')">
      <span>Export as PNG</span>
    </base-button>

    <base-button :disabled="disabled" size="small" @click="exportImage('svg')">
      <span>Export as SVG</span>
    </base-button>
  </section>
</template>

<script>
import VueSlider from 'vue-slider-component';

import { mapActions, mapState } from 'vuex';

import BaseCheckbox from '@/components/Base/BaseCheckbox';
import BaseButton from '@/components/Base/BaseButton';
import BlurCloseable from '@/components/Mixins/BlurCloseable';
import intercomEvent from '@/services/IntercomEvent';
import intercomEvents from '@/enum/intercom-events';
import sliderDefaultStyles from '@/helpers/slider-default-styles';

export default {
  name: 'distribution-export-dropdown',

  components: {
    BaseButton,
    BaseCheckbox,
    VueSlider,
  },

  mixins: [BlurCloseable],

  data() {
    return {
      creating: false,
    };
  },

  computed: {
    ...mapState('chartOptions', [
      'exportHigh',
      'exportMedium',
      'exportLow',
      'imageScale',
    ]),

    ...mapState('themes', {
      themeIndex: state => state.index,
    }),

    disabled() {
      return (!this.exportHigh && !this.exportMedium && !this.exportLow) || this.creating;
    },

    scale: {
      get() {
        return this.imageScale;
      },

      set(value) {
        this.setImageScale({ scale: value });
      },
    },

    sliderBase() {
      return {
        ...sliderDefaultStyles,
        width: '150px',
      };
    },

    sliderScale() {
      return {
        ...this.sliderBase,
        steps: [
          1,
          2,
          3,
          4,
          5,
        ],
        max: 5,
        min: 0.5,
        tooltipFormatter: '{value}x',
        interval: 0.5,
      };
    },
  },

  methods: {
    ...mapActions('chartOptions', [
      'createImage',
      'setExportHigh',
      'setExportMedium',
      'setExportLow',
      'setImageScale',
    ]),

    ...mapActions('themes', ['setIndex']),

    async exportImage(type) {
      const index = this.themeIndex;
      this.setIndex({ index: null });
      this.creating = true;

      const high = async () => {
        await this.createImage({ elementId: 'distribution-chart-high', name: 'distribution-high', type });
      };
      const medium = async () => {
        await this.createImage({ elementId: 'distribution-chart-medium', name: 'distribution-medium', type });
      };
      const low = async () => {
        await this.createImage({ elementId: 'distribution-chart-low', name: 'distribution-low', type });
      };

      const promises = [];
      if (this.exportHigh) promises.push(high);
      if (this.exportMedium) promises.push(medium);
      if (this.exportLow) promises.push(low);

      this.$nextTick(() => {
        Promise.all(promises.map(p => p()))
          .then(() => {
            this.creating = false;
            this.setIndex({ index });
          });
      });

      intercomEvent.send(intercomEvents.EXPORT_DISTRIBUTION_CHART);
    },

    onClickHigh() {
      this.setExportHigh({ value: !this.exportHigh });
    },

    onClickMedium() {
      this.setExportMedium({ value: !this.exportMedium });
    },

    onClickLow() {
      this.setExportLow({ value: !this.exportLow });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.distribution-export-dropdown {
  @include flex("block", "column", "start", "stretch");
  @include rigid;
  @include panel;

  font-size: $font-size-xs;
  padding: 0.3rem 0.8rem 0.5rem;
  position: absolute;
  right: 0;
  top: 1.5rem;
  z-index: 99;

  .image-size {
    font-size: $font-size-xxs;
    margin: 0.2rem 0 0.5rem;
  }

  h4 {
    color: $body-copy-light;
    font-size: $font-size-xxs;
    letter-spacing: $letter-spacing-sm;
    margin: 1em 0;
    text-transform: uppercase;
  }

  .option {
    @include flex("block", "row", "start", "center");

    cursor: pointer;
    margin: 0 0.5rem 0.5rem 0.5rem;
    width: 150px;
    position: relative;

    .base-checkbox {
      margin-right: 0.5em;
      pointer-events: none;
    }

    span {
      font-size: $font-size-xs;
    }
  }

  .help {
    font-size: $font-size-xxs;
    margin-top: 0.5rem;
  }

  .base-button {
    margin-top: 1rem;

    .icon {
      height: $font-size-sm;
      width: $font-size-sm;
    }
  }
}
</style>
