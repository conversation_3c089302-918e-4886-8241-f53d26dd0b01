<template>
  <section class="journey-key-theme-modal-item">
    <section class="label" :title="theme.topicLabel">
      {{ theme.topicLabel }}
    </section>
    <section class="score">
      {{ score }}
    </section>
  </section>
</template>

<script>
import ThemesSort from '@/enum/themes-sort';

export default {
  name: 'journey-key-theme-modal-item',

  props: {
    sort: {
      type: Object,
      required: true,
    },

    strength: {
      type: Number,
      required: true,
    },

    theme: {
      type: Object,
      required: true,
    },
  },

  computed: {
    score() {
      switch (this.sort) {
        case ThemesSort.IMPACT:
          return Math.round(this.strength);
        case ThemesSort.VOLUME:
          return this.theme.numOfDocuments;
        case ThemesSort.ADORESCORE:
        default:
          return Math.round(this.theme.polarity * 100);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.journey-key-theme-modal-item {
  @include flex("block", "row", "between", "center");
  @include panel;

  cursor: pointer;
  padding: 0.5rem;
  margin-bottom: 0.5rem;
  transition: all $interaction-transition-time;

  &:hover {
    border: 1px solid clr('blue');
  }

  &:last-child {
    margin-bottom: 0;
  }

  .label {
    @include truncate;

    padding-right: 1rem;
  }
}
</style>
