<template>
  <section class="journey-key-theme-modal">
    <section class="header">
      <h2>Select Key Theme</h2>
    </section>

    <section class="body">
      <section class="controls">
        <section class="search">
          <search-icon class="icon"/>
          <base-input v-model="search" placeholder="Search Themes..."/>
        </section>
      </section>

      <section class="list-headers">
        <h3>THEME</h3>
        <base-dropdown :data="sortItems"
          :open="sortOpen"
          @close="sortOpen = false"
          @select="onSelectSort">
          <h3 class="sort-header" @click.stop="onClickSort">
            {{ sort.name }}
            <chevron-down-icon class="icon" :class="{ active: sortOpen }"/>
          </h3>
        </base-dropdown>
      </section>

      <section class="list">
        <journey-key-theme-modal-item v-for="theme in filtered" :key="theme.id"
          :sort="sort"
          :strength="themeStrength(theme)"
          :theme="theme"
          @click.native.stop="onSelectTheme(theme)"
        />
      </section>
    </section>

    <section class="footer">
      <base-button colour="light" size="small" type="link" @click="onClickCancel">Cancel</base-button>
    </section>
  </section>
</template>

<script>
import { ChevronDownIcon, SearchIcon } from 'vue-feather-icons';
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseDropdown from '@/components/Dropdown/BaseDropdown';
import BaseInput from '@/components/Base/BaseInput';
import JourneyKeyThemeModalItem from '@/components/JourneyKeyThemeModal/JourneyKeyThemeModalItem';
import ThemesSort from '@/enum/themes-sort';

import { journeyApi } from '@/services/api';

export default {
  name: 'journey-key-theme-modal',

  components: {
    BaseButton,
    BaseDropdown,
    BaseInput,
    ChevronDownIcon,
    JourneyKeyThemeModalItem,
    SearchIcon,
  },

  data() {
    return {
      search: '',
      sort: ThemesSort.VOLUME,
      sortOpen: false,
    };
  },

  computed: {
    ...mapState('datasets', ['active']),

    ...mapState('journey', ['editThemeList', 'stageToUpdate']),

    filtered() {
      if (this.search === '') return this.sorted;

      return this.sorted.filter(t => t.topicLabel.toLowerCase().includes(this.search.toLowerCase()));
    },

    maxTopicStrength() {
      return Math.max(...this.editThemeList.map(t => t.topicStrength));
    },

    sorted() {
      switch (this.sort) {
        case ThemesSort.IMPACT:
          return [...this.editThemeList].sort((t1, t2) => {
            const score1 = this.themeStrength(t1);
            const score2 = this.themeStrength(t2);

            return score2 - score1;
          });
        case ThemesSort.VOLUME:
          return [...this.editThemeList].sort((t1, t2) => t2.numOfDocuments - t1.numOfDocuments);
        case ThemesSort.ADORESCORE:
        default:
          return [...this.editThemeList].sort((t1, t2) => t2.polarity - t1.polarity);
      }
    },

    sortItems() {
      return ThemesSort.enumValues
        .filter(t => ![ThemesSort.NAME].includes(t))
        .map((ts) => {
          return {
            content: ts.titleCase(),
            value: ts,
          };
        });
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    onClickCancel() {
      this.closeModal();
    },

    onClickSort() {
      this.sortOpen = !this.sortOpen;
    },

    onSelectSort(sort) {
      this.sort = sort.value;
    },

    async onSelectTheme(theme) {
      this.closeModal();

      const stage = this.stageToUpdate;
      if (stage.label && stage.label.trim() !== '') {
        await journeyApi.updateStageBasic(
          this.active,
          stage.journeyId,
          stage.id,
          stage.label,
          stage.position,
          theme.id,
          stage.exemplarSnippetId,
        );
      }
    },

    themeStrength(theme) {
      return theme.topicStrength / this.maxTopicStrength * 100;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.journey-key-theme-modal {
  @include modal;

  .body {
    @include flex("block", "column", "start", "stretch");

    overflow: hidden;
  }

  .controls {
    @include flex("block", "row", "start", "stretch");

    padding: 0.5rem 0 1rem;

    .search {
      @include flex("block", "row", "start", "center");
      @include stretch;

      .icon {
        height: $font-size-base;
        margin-right: 0.3rem;
        width: $font-size-base;
      }
    }
  }

  .list-headers {
    @include flex("block", "row", "between", "center");

    padding-bottom: 0.5rem;

    h3 {
      @include panel-header-title;

      color: $body-copy-light;
      font-size: $font-size-xs;

      &.sort-header {
        @include flex("block", "row", "center", "center");

        border-bottom: $border-standard;
        cursor: pointer;
        transition: opacity $interaction-transition-time;

        &:hover {
          opacity: 0.7;
        }

        .icon {
          height: $font-size-base;
          transition: transform $interaction-transition-time;
          width: $font-size-base;

          &.active {
            transform: rotate(180deg);
          }
        }
      }
    }
  }

  .list {
    @include flex("block", "column", "start", "stretch");
    @include stretch;

    overflow-y: auto;
  }
}
</style>
