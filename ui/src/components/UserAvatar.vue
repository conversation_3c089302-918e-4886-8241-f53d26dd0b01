<template>
  <section class="user-avatar">
    <img v-if="hasAvatar" :src="user.avatar">

    <img v-if="!hasAvatar && gravatar" :src="gravatar">

    <section v-else class="no-avatar">
      <i class="fa-regular fa-face-smile icon"></i>
    </section>
  </section>
</template>

<script>
import md5 from 'crypto-js/md5';

import { mapState } from 'vuex';

import api from '@/helpers/api';

export default {
  name: 'user-avatar',

  components: {
  },

  data() {
    return {
      gravatar: false,
    };
  },

  computed: {
    ...mapState('user', ['user']),

    hasAvatar() {
      return this.user.avatar != null;
    },

    hash() {
      return md5(this.user.email).toString();
    },
  },

  async created() {
    try {
      const image = await api.instanceExternal().get(`https://www.gravatar.com/avatar/${this.hash}?d=404&s=100`);
      this.gravatar = image.config.url;
    } catch (error) {
      this.gravatar = false;
    }
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.user-avatar {
  @include flex("block", "row", "center", "center");

  height: 100%;
  width: 100%;

  img {
    border-radius: 50px;
    height: 100%;
    width: 100%;
  }

  .no-avatar {
    @include flex("block", "row", "center", "center");

    background-color: #BCFFB1;
    border-radius: 50%;
    color: $nps-blue;
    height: 21px;
    width: 21px;

    .icon {
      font-size: $font-size-xs;
    }
  }
}
</style>
