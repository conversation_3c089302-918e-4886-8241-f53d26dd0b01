<template>
  <section class="storyteller-slide-themes-insights-item" :style="style">
    <section class="item name">
      <span class="order">{{order}}</span>
      <span class="label">{{theme.insightsTopicsModel.topicLabel}}</span>
    </section>
    <section class="item score" v-if="displaySettings.score">
      <storyteller-adorescore-box class="score-box" :bucket="bucket" :score="adorescore" :width="scoreWidth"/>
      <section class="score-type" :style="{ color: bucketColor }">&nbsp;{{bucketName}}</section>
    </section>
    <span class="item volume" v-if="displaySettings.volume">{{volumePercent}}</span>
    <span class="item swot" v-if="displaySettings.swot" :class="{ isStrength, isOpportunity, isThreat, isWeakness }">{{swot}}</span>
    <span class="item var" v-if="displaySettings.valueAtRisk">{{varCurrency}}{{varAmount}}</span>
    <section class="item predicted" v-if="displaySettings.predictImprovement">
      <span>{{metricNumber}}</span>
    </section>
  </section>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import { capitalize } from 'lodash-es';
import formatCurrencyNumber from '@/helpers/number-utils';

import StorytellerAdorescoreBox from '@/components/StorytellerSlides/StorytellerAdorescoreBox';

export default {
  name: 'storyteller-slide-themes-insights-item',

  props: {
    displaySettings: {
      type: Object,
      required: true,
    },
    displaySettingsCount: {
      type: Number,
      required: true,
    },
    maxWidthEachCols: {
      type: Array,
      default: [],
    },
    order: {
      type: Number,
      required: true,
    },
    slideWidth: {
      type: Number,
      required: true,
    },
    theme: {
      type: Object,
      required: true,
    },
  },

  components: {
    StorytellerAdorescoreBox,
  },

  data() {
    return {
      scoreWidth: 0,
      style: {},
    };
  },

  computed: {
    ...mapGetters('benchmark', ['classifyAdorescore', 'color']),

    ...mapGetters('datasets', ['get']),

    ...mapState('datasets', ['active']),

    ...mapState('storyteller', ['activeReport']),

    adorescore() {
      return Math.round(this.theme.insightsTopicsModel.polarity * 100);
    },

    bucket() {
      return this.classifyAdorescore(this.adorescore);
    },

    bucketColor() {
      return this.color(this.adorescore);
    },

    bucketName() {
      let { name } = this.bucket;
      if (name === 'Very Good') name = 'V Good';
      if (name === 'Very Poor') name = 'V Poor';
      return name;
    },

    isOpportunity() {
      return this.swot === 'Opportunity';
    },

    isStrength() {
      return this.swot === 'Strength';
    },

    isThreat() {
      return this.swot === 'Threat';
    },

    isWeakness() {
      return this.swot === 'Weakness';
    },

    metricNumber() {
      const { predictImprovementBy } = this.activeReport.settings.insightSettings;
      const firstKey = Object.keys(predictImprovementBy)[0];
      const metricText = predictImprovementBy[firstKey];
      const number = Math.round(this.theme.optimiseChange * 100);
      if (number) return `+${number}% ${metricText}`;
      return null;
    },

    ratio() {
      return this.slideWidth / 1920;
    },

    swot() {
      return capitalize(this.theme.insightsTopicsModel.swot.attribute);
    },

    varAmount() {
      return formatCurrencyNumber(this.theme.insightsTopicsModel.valueAtRiskAmount);
    },

    varCurrency() {
      return this.activeReport.settings.valueAtRiskInfo.currencySymbol;
    },

    volumePercent() {
      const value = Math.round(this.theme.insightsTopicsModel.numOfDocuments / this.get(this.active).documentCount * 100);
      return value === 0 ? '<1%' : `${value}%`;
    },
  },

  watch: {
    slideWidth() {
      this.setComponentsSize();
    },

    displaySettingsCount() {
      this.setComponentsSize();
    },
  },

  mounted() {
    this.setComponentsSize();
  },

  methods: {

    setComponentsSize() {
      const columnFontSizeMap = {
        5: 30,
        4: 33,
        3: 35,
      };
      const itemThemeFontSize = columnFontSizeMap[this.displaySettingsCount] || 35;
      const itemSwotFontSize = 25;
      const itemFontSize = 30;

      let customGridTemplateColumns = [];
      let sum = 0;
      this.maxWidthEachCols.forEach(m => {
        customGridTemplateColumns.push(`${m}%`);
        sum += m;
      });

      const labelWidth = `${Number(100 - sum)}%`;
      customGridTemplateColumns = [labelWidth, ...customGridTemplateColumns].join(' ');

      this.style = {
        '--gridTemplateColumns': `${customGridTemplateColumns}`,
        '--itemSwotFontSize': `${itemSwotFontSize * this.ratio}px`,
        '--itemThemeFontSize': `${itemThemeFontSize * this.ratio}px`,
        '--textFontSize': `${50 * this.ratio}px`,
        '--textItemFontSize': `${itemFontSize * this.ratio}px`,
      };
      this.scoreWidth = 70 * this.ratio;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-themes-insights-item {
  border-bottom: 1px solid rgba(19, 28, 41, 0.09);
  display: grid;
  font-size: var(--textFontSize);
  grid-template-columns: var(--gridTemplateColumns);
  max-width: 100%;
  overflow: hidden;
  width: 100%;

  .item {
    @include flex("block", "row", "start", "center");

    font-size: var(--textItemFontSize);
    padding: 0.57em 0;

    &.name {
      font-weight: 300;

      .order {
        @include flex("block", "row", "center", "center");

        background-color: rgba(217, 217, 217, 0.3);
        border-radius: 50%;
        font-size: 0.57em;
        font-weight: $font-weight-bold;
        margin-right: 0.75em;
        min-height: var(--textItemFontSize);
        min-width: var(--textItemFontSize);
      }

      .label {
        @include truncate;
        font-size: var(--itemThemeFontSize);
        max-width: 90%;
      }
    }

    &.score {
      @include flex("block", "row", "start", "center");

      .score-type {
        padding-left: 0.2em;
        font-size: 0.6em;
        font-weight: $font-weight-extra-bold;
        text-transform: uppercase;
      }
    }

    &.volume {
      @include flex("block", "row", "start", "center");
      font-weight: 300;
    }

    &.predicted {
      @include flex("block", "row", "center", "center");
      @include truncate;

      background-color: rgba(129, 129, 129, 0.05);
      color: #64B656;
      font-weight: 600;
      padding-left: 0.4em;
      padding-right: 0.4em;

      span {
        @include truncate;
      }
    }

    &.swot {
      @include flex("block", "row", "start", "center");

      font-size: var(--itemSwotFontSize);
      font-weight: 600;

      &.isOpportunity {
        color: $swot-opportunity;
      }

      &.isStrength {
        color: $swot-strength;
      }

      &.isThreat {
        color: $swot-threat;
      }

      &.isWeakness {
        color: $swot-weakness;
      }
    }
  }

  &:last-child {
    border-bottom: none;
  }
}
</style>
