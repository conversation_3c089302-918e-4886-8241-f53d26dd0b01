<template>
  <section class="storyteller-slide-themes-insights" :style="style">
    <section class="margin-top" />
    <section class="body">
      <section class="title-header">
        <span class="text-header">{{slideData.header}}</span>
        <storyteller-slide-setting-btn v-if="editable" :text="'Table Display Settings'" @click="onClickDisplaySettings" />
      </section>
      <section class="selected-themes-header">
        <span class="header">Themes</span>
        <span class="header" v-if="displaySettings.score">Adorescore</span>
        <span class="header volume" v-if="displaySettings.volume">Volume</span>
        <span class="header swot" v-if="displaySettings.swot">SWOT</span>
        <span class="header var" v-if="displaySettings.valueAtRisk">Revenue at Risk</span>
        <span class="header predicted" v-if="displaySettings.predictImprovement">Predicted <br> Improvement</span>
      </section>
      <section class="selected-themes-list">
        <storyteller-slide-themes-insights-item class="selected-themes-item" v-for="(item, index) in selectedThemesInsights"
          :key="index"
          :max-width-each-cols="maxWidthEachCols"
          :order="index+1"
          :slide-width="slideWidth"
          :theme="item"
          :display-settings="displaySettings"
          :display-settings-count="displaySettingsCount"
        />
      </section>
    </section>
    <storyteller-slide-footer :slide-width="slideWidth" />
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import StorytellerSlideFooter from '@/components/StorytellerSlides/StorytellerSlideFooter';
import StorytellerSlideSettingBtn from '@/components/Storyteller/StorytellerSlideSettingBtn';
import StorytellerSlideThemesInsightsItem from '@/components/StorytellerSlideThemesInsights/StorytellerSlideThemesInsightsItem';
import StorytellerSlideThemesInsightsModal from '@/components/StorytellerSlideThemesInsights/StorytellerSlideThemesInsightsModal';

export default {
  name: 'storyteller-slide-themes-insights',

  components: {
    StorytellerSlideFooter,
    StorytellerSlideSettingBtn,
    StorytellerSlideThemesInsightsItem,
  },

  props: {
    editable: {
      type: Boolean,
      default: false,
    },
    slideData: {
      type: Object,
      required: true,
    },
    slideWidth: {
      type: Number,
      required: true,
    },
  },

  data() {
    return {
      maxWidthEachCols: [],
    };
  },

  computed: {
    ...mapState('storyteller', ['activeReport']),

    displaySettings() {
      const { insightSettings } = this.activeReport.settings;

      return {
        score: insightSettings.displayScore,
        valueAtRisk: insightSettings.displayValueAtRisk,
        volume: insightSettings.displayVolume,
        predictImprovement: insightSettings.displayPredictImprovement,
        swot: insightSettings.displaySwot,
      };
    },

    // count total display settings are true
    displaySettingsCount() {
      return Object.values(this.displaySettings).reduce((count, value) => {
        return count + (value ? 1 : 0);
      }, 0);
    },

    ratio() {
      return this.slideWidth / 1920;
    },

    selectedThemesInsights() {
      return this.slideData.insightThemesData;
    },

    style() {
      this.getFixedPercentEachCols();

      let customGridTemplateColumns = [];
      let sum = 0;
      this.maxWidthEachCols.forEach(m => {
        customGridTemplateColumns.push(`${m}%`);
        sum += m;
      });

      const labelWidth = `${Number(100 - sum)}%`;
      customGridTemplateColumns = [labelWidth, ...customGridTemplateColumns].join(' ');

      return {
        '--gridTemplateColumns': `${customGridTemplateColumns}`,
        '--textFontSize': `${50 * this.ratio}px`,
        '--textHeaderFontSize': `${19 * this.ratio}px`,
      };
    },
  },

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    getFixedPercentEachCols() {
      this.maxWidthEachCols = [];
      if (this.displaySettings.score) {
        this.maxWidthEachCols.push(10);
      }
      if (this.displaySettings.volume) {
        this.maxWidthEachCols.push(7);
      }
      if (this.displaySettings.swot) {
        this.maxWidthEachCols.push(10);
      }
      if (this.displaySettings.valueAtRisk) {
        this.maxWidthEachCols.push(11);
      }
      if (this.displaySettings.predictImprovement) {
        this.maxWidthEachCols.push(13);
      }
    },

    onClickDisplaySettings() {
      this.setModalComponent({ component: StorytellerSlideThemesInsightsModal });
    },
  },

  watch: {
    displaySettings() {
      this.maxWidthEachCols = [];
      this.getFixedPercentEachCols();
    },
  },
};
</script>

<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slide-themes-insights {
  background-color: #FFF;
  color: $nps-blue;
  display: grid;
  font-family: Inter, serif;
  font-size: var(--textFontSize);
  font-weight: $font-weight-semi-light;
  grid-template-rows: 13% 74% 13%;
  height: 100%;
  padding: 0 2em;
  width: 100%;

  &:hover {
    .body .title-header .storyteller-slide-setting-btn {
      display: flex;
    }
  }

  strong {
    font-weight: 600;
  }

  .body {
    @include flex("block", "column", "start", "start");

    height: 100%;
    width: 100%;

    .title-header {
      @include flex("block", "row", "start", "center");

      position: relative;
      width: fit-content;

      .text-header {
        font-size: 0.5em;
        font-weight: 800;
        letter-spacing: 0.04em;
        opacity: 0.5;
        text-transform: uppercase;
      }

      .storyteller-slide-setting-btn {
        display: none;
        position: absolute;
        right: -1.5em;
      }
    }

    .selected-themes-header {
      @include flex("block", "row", "start", "center");

      display: grid;
      grid-template-columns: var(--gridTemplateColumns);
      width: 100%;

      .header {
        color: rgba(19, 28, 41, 0.3);
        font-size: var(--textHeaderFontSize);
        font-weight: $font-weight-extra-bold;
        letter-spacing: 0.5px;
        padding-bottom: 1em;
        padding-top: 1.2em;
        text-transform: uppercase;

        &.volume {
          @include flex("block", "row", "start", "center");
        }

        &.var {
          @include flex("block", "row", "start", "center");
        }

        &.predicted {
          @include flex("block", "row", "center", "center");
          background-color: rgba(129, 129, 129, 0.05);
          color: rgba(19, 28, 41, 0.5);
          text-align: center;
        }

        &.swot {
          @include flex("block", "row", "start", "center");
        }
      }
    }

    .selected-themes-list {
      @include flex("block", "column", "start", "stretch");

      height: 100%;
      margin-bottom: 0.8em;
      width: 100%;

      .selected-themes-item {
        flex: 1;
      }
    }
  }
}
</style>
