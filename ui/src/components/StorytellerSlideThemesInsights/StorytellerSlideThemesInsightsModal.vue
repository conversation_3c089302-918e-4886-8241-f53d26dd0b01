<template>
  <section class="storyteller-slide-themes-insights-modal">
    <section class="header">
      <section class="header-title">
        <i class="fa-solid fa-table icon-display" />
        <span class="text-title">Table Display Settings</span>
      </section>
      <span class="text-desc">Select the columns you want to display.</span>
    </section>
    <section class="body">
      <section class="settings-item" v-if="availableDisplays.score" @click.stop="toggleScore">
        <base-checkbox-solid :value="activeReport.settings.insightSettings.displayScore" />
        <span class="settings-item-text">Score</span>
      </section>
      <section class="settings-item" v-if="availableDisplays.volume" @click.stop="toggleVolume">
        <base-checkbox-solid :value="activeReport.settings.insightSettings.displayVolume" />
        <span class="settings-item-text">Volume</span>
      </section>
      <section class="settings-item" v-if="availableDisplays.swot" @click.stop="toggleSwot">
        <base-checkbox-solid :value="activeReport.settings.insightSettings.displaySwot" />
        <span class="settings-item-text">SWOT</span>
      </section>
      <section class="settings-item" v-if="availableDisplays.valueAtRisk" @click.stop="toggleValueAtRisk">
        <base-checkbox-solid :value="activeReport.settings.insightSettings.displayValueAtRisk" />
        <span class="settings-item-text">Revenue at Risk</span>
      </section>
      <section class="settings-item" v-if="availableDisplays.predictImprovement" @click.stop="togglePredictedImprovement">
        <base-checkbox-solid :value="activeReport.settings.insightSettings.displayPredictImprovement" />
        <span class="settings-item-text">Predicted Improvement</span>
      </section>
    </section>
    <section class="footer">
      <base-button class="cancel-btn" colour="light" size="small" type="link" @click="onClickCancel">Cancel</base-button>
      <loading-blocks-overlay v-if="saving" size="small" />
      <base-button v-else class="done-btn" size="small" @click="onClickDone">
        <span>Done</span>
      </base-button>
    </section>
  </section>
</template>

<script>
import { cloneDeep } from 'lodash-es';
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseCheckboxSolid from '@/components/Base/BaseCheckboxSolid';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import StorytellerRequest from '@/services/request/StorytellerRequest';

export default {
  name: 'storyteller-slide-themes-insights-modal',

  components: {
    BaseButton,
    BaseCheckboxSolid,
    LoadingBlocksOverlay,
  },

  data() {
    return {
      initialSettings: {},
      saving: false,
    };
  },

  computed: {
    ...mapGetters('storyteller', ['availableDisplays']),

    ...mapState('storyteller', ['activeReport']),
  },

  created() {
    this.initialSettings = cloneDeep(this.activeReport.settings);
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    onClickCancel() {
      this.activeReport.settings = cloneDeep(this.initialSettings);
      this.closeModal();
    },

    async onClickDone() {
      this.saving = true;
      await StorytellerRequest.updateReportSettings();
      this.saving = false;
      this.closeModal();
    },

    togglePredictedImprovement() {
      this.activeReport.settings.insightSettings.displayPredictImprovement = !this.activeReport.settings.insightSettings.displayPredictImprovement;
    },

    toggleScore() {
      this.activeReport.settings.insightSettings.displayScore = !this.activeReport.settings.insightSettings.displayScore;
    },

    toggleSwot() {
      this.activeReport.settings.insightSettings.displaySwot = !this.activeReport.settings.insightSettings.displaySwot;
    },

    toggleValueAtRisk() {
      this.activeReport.settings.insightSettings.displayValueAtRisk = !this.activeReport.settings.insightSettings.displayValueAtRisk;
    },

    toggleVolume() {
      this.activeReport.settings.insightSettings.displayVolume = !this.activeReport.settings.insightSettings.displayVolume;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-slide-themes-insights-modal {
  @include panel;

  color: $nps-blue;
  position: relative;
  width: 366px;

  .header {
    @include flex("block", "column", "center", "start");

    padding: 1.75rem 1.75rem 0.5rem 1.75rem;
    width: 100%;

    .header-title {
      @include flex("block", "row", "start", "center");

      font-size: $font-size-sm;
      font-weight: $font-weight-bold;
      width: 100%;

      .icon-display {
        margin-right: 0.4rem;
      }
    }

    .text-desc {
      color: rgba(19, 28, 41, 0.7);
      font-size: $font-size-xs;
      margin-top: 1rem;
    }
  }

  .body {
    @include flex("block", "column", "start", "start");

    padding: 0 1.75rem;
    width: 100%;

    .settings-item {
      @include flex("block", "row", "start", "center");

      border-bottom: 1px solid #F2F1FB;
      cursor: pointer;
      padding: 1rem 0;
      width: 100%;

      &:last-child {
        border-bottom: none;
      }

      .settings-item-text {
        color: #2D1757;
        font-size: $font-size-xs;
        margin-left: 0.5rem;
      }
    }
  }

  .footer {
    @include flex("block", "row", "space-between", "center");

    border-top: 1px solid rgba(19, 28, 41, 0.2);
    padding: 1.2rem 1.75rem;
    width: 100%;

    .base-button {
      font-size: $font-size-xxs;
      font-weight: 600;
      text-transform: uppercase;

      &.cancel-btn {
        padding-left: 0;
      }

      &.done-btn {
        background-color: #2D1757;
        padding: 0.5rem 0.8rem;
        width: 90px;
      }

      &:hover, &:focus {
        &.done-btn {
          background-color: rgba(19, 28, 41);
        }
      }
    }
  }
}
</style>
