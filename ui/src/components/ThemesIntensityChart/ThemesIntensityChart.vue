<template>
  <section ref="chart" :id="bcId" class="themes-intensity-chart"></section>
</template>

<script>
import { at } from 'lodash-es';
import { mapActions, mapGetters, mapState } from 'vuex';

import CustomChartType from '@/enum/custom-chart-type';
import ResetThemesIntensityChartModal from '@/components/ThemesIntensityChartPanel/ResetThemesIntensityChartModal';
import ThemesChartPath from '@/enum/themes-chart-path';
import themesChartPaths from '@/helpers/themes-chart-paths';
import ThemesIntensityAnnotationModal from '@/components/ThemesIntensityChartPanel/ThemesIntensityAnnotationModal';
import ThemesIntensityChartD3 from '@/components/ThemesIntensityChart/ThemesIntensityChartD3';
import ThemesIntensityLevel from '@/enum/themes-intensity-level';
import ThemesIntensityRange from '@/enum/themes-intensity-range';
import ThemesIntensityThemeStateModal from '@/components/ThemesIntensityChartPanel/ThemesIntensityThemeStatsModal';

import { datasetsRequestV0 } from '@/services/request';

export default {
  name: 'themes-intensity-chart',

  props: {
    bcId: {
      type: String,
      required: true,
    },
  },

  data() {
    return {
      chart: null,
      filteredDataMaxScore: 100,
      filteredDataMinScore: -100,
      showedDataNegativeMax: 1,
      showedDataPositiveMax: 1,
      showedDataNegativeMin: 0,
      showedDataPositiveMin: 0,
    };
  },

  computed: {
    ...mapState('datasets', ['active']),

    ...mapGetters('datasets', ['get']),

    ...mapState('themes', [
      'customThemes',
      'hoverTheme',
      'selectedTheme',
      'themes',
      'customChartTitles',
      'customChartDescriptions',
    ]),

    ...mapGetters('themes', ['isCustomTheme']),

    ...mapState('themesChart', {
      chartTheme: 'theme',
      chartThemes: 'themes',
      chartYPath: 'yPath',
    }),

    ...mapGetters('themes', ['getDefaultCustomThemes']),

    ...mapState('themesIntensity', [
      'annotationMode',
      'chartOptions',
      'level',
      'range',
      'updatingAnnotation',
    ]),

    ...mapState('themesIntensity', {
      annotationBg: state => state.chartOptions.annotationBg,
      bubbleSize: state => state.chartOptions.bubbleSize,
      showAnnotations: state => state.chartOptions.showAnnotations,
      showAxisLabel: state => state.chartOptions.showAxisLabel,
      showBubbleLabels: state => state.chartOptions.showBubbleLabels,
      showConnectorLines: state => state.chartOptions.showConnectorLines,
      showDescription: state => state.chartOptions.showDescription,
      showTitle: state => state.chartOptions.showTitle,
      xAxisLabel: state => state.chartOptions.xAxisLabel,
      yAxisLabel: state => state.chartOptions.yAxisLabel,
    }),

    defaultDesc() {
      const intensityByLevel = (theme, level) => {
        if (level === ThemesIntensityLevel.MEDIUM) {
          return at(theme, 'medIntensityPercent');
        }
        if (level === ThemesIntensityLevel.LOW) {
          return at(theme, 'lowIntensityPercent');
        }
        return at(theme, 'highIntensityPercent');
      };

      const intensity = intensityByLevel(this.selectedTheme, this.level)[0];

      if (intensity) return `${this.selectedTheme.topicLabel} has ${Math.round(intensity * 100)}% ${this.level.text()} Intensity and ${Math.round(this.selectedTheme.polarity * 100)} Adorescore`;

      return '';
    },

    defaultTitle() {
      return `Theme Intensity Chart for ${this.selectedDataset.label}`;
    },

    flipped() {
      return [ThemesChartPath.SURPRISE, ThemesChartPath.ANGER].includes(this.chartYPath) ? -1 : 1;
    },

    filteredThemes() {
      const attrPath = themesChartPaths[this.chartYPath];
      return this.themes
        .filter(t => {
          const themeScoreIsNegative = (at(t, attrPath) * this.flipped) < 0;
          if (this.range === ThemesIntensityRange.POSITIVE && themeScoreIsNegative) {
            return false;
          }
          if (this.range === ThemesIntensityRange.NEGATIVE && !themeScoreIsNegative) {
            return false;
          }
          return t.originalTopicLabel !== 'Other Topics';
        });
    },

    isChanged() {
      if (this.selectedTitle !== this.defaultTitle) return true;
      if (this.selectedDesc !== this.defaultDesc) return true;
      const defaultThemes = this.getDefaultCustomThemes(CustomChartType.THEME_ANALYSIS);
      if (defaultThemes.length !== this.customThemes.length) return true;
      for (let i = 0; i < defaultThemes.length; i += 1) {
        const theme = this.customThemes.find(c => c.id === defaultThemes[i].id);
        if (!theme) return true;
        if (theme.showTitle || theme.showDescription || theme.showThemeName || theme.showIntensity || theme.showVolume
            || theme.showScore) return true;
      }
      return false;
    },

    selectedDataset() {
      return this.get(this.active);
    },

    selectedTitle() {
      return this.customChartTitles[CustomChartType.THEME_ANALYSIS.name] !== this.selectedDataset.label ?
        this.customChartTitles[CustomChartType.THEME_ANALYSIS.name] : this.defaultTitle;
    },

    selectedDesc() {
      if (this.isUndefinedOrNull(this.customChartDescriptions[CustomChartType.THEME_ANALYSIS.name])) return this.defaultDesc;
      return this.customChartDescriptions[CustomChartType.THEME_ANALYSIS.name];
    },

    totalVolume() {
      return this.get(this.active).documentCount;
    },
  },

  watch: {
    annotationBg() {
      this.chart.updateAnnotationBg({ annotationBg: this.annotationBg });
    },

    annotationMode() {
      this.chart.annotateChart({ annotationMode: this.annotationMode });
    },

    bubbleSize() {
      this.chart.updateBubbleSize({ bubbleSize: this.bubbleSize });
    },

    chartYPath() {
      this.setIntensityMaxAndMinByLevel();
      this.updateFilteredMaxAndMinScore();
      this.updateShowedMaxAndMinIntensity();
      this.chart.updatePath({
        name: this.chartYPath.titleCase(),
        path: themesChartPaths[this.chartYPath],
        filteredDataMaxScore: this.filteredDataMaxScore,
        filteredDataMinScore: this.filteredDataMinScore,
        intensityNegativeMax: this.showedDataNegativeMax,
        intensityNegativeMin: this.showedDataNegativeMin,
        intensityPositiveMax: this.showedDataPositiveMax,
        intensityPositiveMin: this.showedDataPositiveMin,
      });
    },

    customThemes() {
      this.updateCustomThemeData();
    },

    filteredThemes() {
      this.updateFilteredMaxAndMinScore();
      this.chart.updateData({
        data: this.filteredThemes,
        filteredDataMaxScore: this.filteredDataMaxScore,
        filteredDataMinScore: this.filteredDataMinScore,
      });

      if (this.filteredThemes != null && this.filteredThemes.length !== 0) {
        const maxTheme = this.filteredThemes.reduce((prev, current) => {
          return (prev.numOfDocuments > current.numOfDocuments) ? prev : current;
        });

        this.onSelect(maxTheme);
      }
    },

    hoverTheme() {
      this.chart.updateHoverTheme({ hoverTheme: this.hoverTheme });
    },

    isChanged() {
      this.chart.updateIsChanged({ isChanged: this.isChanged });
    },

    level() {
      this.setIntensityMaxAndMinByLevel();
      this.updateShowedMaxAndMinIntensity();
      this.chart.updateIntensity({
        level: this.level,
        intensityNegativeMax: this.showedDataNegativeMax,
        intensityNegativeMin: this.showedDataNegativeMin,
        intensityPositiveMax: this.showedDataPositiveMax,
        intensityPositiveMin: this.showedDataPositiveMin,
      });
    },

    range() {
      this.chart.updateRange({ range: this.range });
    },

    selectedTheme() {
      this.chart.updateSelected({ selected: this.selectedTheme });
    },

    selectedTitle() {
      this.chart.updateTitle({ title: this.selectedTitle });
    },

    selectedDesc() {
      this.chart.updateDesc({ desc: this.selectedDesc });
    },

    showAnnotations() {
      this.chart.updateShowAnnotations({ showAnnotations: this.showAnnotations });
    },

    showAxisLabel() {
      this.chart.updateShowAxisLabel({ showAxisLabel: this.showAxisLabel });
    },

    showBubbleLabels() {
      this.chart.updateShowBubbleLabels({ showBubbleLabels: this.showBubbleLabels });
    },

    showConnectorLines() {
      this.chart.updateShowConnectorLines({ showConnectorLines: this.showConnectorLines });
    },

    showDescription() {
      this.chart.updateShowDescription({ showDescription: this.showDescription });
    },

    showTitle() {
      this.chart.updateShowTitle({ showTitle: this.showTitle });
    },

    totalVolume() {
      this.chart.updateTotalVolume({ totalVolume: this.totalVolume });
    },

    updatingAnnotation() {
      if (this.updatingAnnotation) {
        this.updateCustomThemeData();
        this.setUpdatingAnnotation({ updatingAnnotation: false });
      }
    },

    xAxisLabel() {
      this.chart.updateXAxisLabel({ xAxisLabel: this.xAxisLabel });
    },

    yAxisLabel() {
      this.chart.updateYAxisLabel({ yAxisLabel: this.yAxisLabel });
    },
  },

  mounted() {
    this.updateFilteredMaxAndMinScore();
    this.updateShowedMaxAndMinIntensity();
    this.createChart();
  },

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    ...mapActions('themes', [
      'addCustomThemes',
      'removeCustomThemesCommentAnnotation',
      'removeCustomThemesStatsAnnotation',
      'selectTheme',
      'setCustomChartTitles',
      'setCustomChartDescriptions',
    ]),

    ...mapActions('themesIntensity', [
      'setAnnotationMode',
      'setIntensityMaxAndMinByLevel',
      'setUpdatingAnnotation',
    ]),

    createChart() {
      this.setAnnotationMode({ annotationMode: false });
      this.chart = new ThemesIntensityChartD3({
        bcId: this.bcId,
        chartOptions: this.chartOptions,
        commentAnnotationCallBack: this.commentAnnotationCallBack,
        customThemeData: this.customThemes,
        data: this.filteredThemes,
        finishAnnotationCallBack: this.finishAnnotation,
        filteredDataMaxScore: this.filteredDataMaxScore,
        filteredDataMinScore: this.filteredDataMinScore,
        height: this.height,
        hoverTheme: {},
        isChanged: this.isChanged,
        intensityLevel: this.level,
        intensityNegativeMax: this.showedDataNegativeMax,
        intensityNegativeMin: this.showedDataNegativeMin,
        intensityPositiveMax: this.showedDataPositiveMax,
        intensityPositiveMin: this.showedDataPositiveMin,
        isCustom: this.isCustomTheme,
        labelTitle: this.selectedTitle,
        labelTitleCallback: this.onClickSaveLabelTitle,
        labelDesc: this.selectedDesc,
        labelDescCallback: this.onClickSaveLabelDesc,
        resetChartCallback: this.resetChart,
        selectCallback: this.onSelect,
        selected: this.selectedTheme,
        selectedDataset: this.selectedDataset.label,
        themeStatsAnnotationCallBack: this.themeStatsAnnotationCallBack,
        totalVolume: this.totalVolume,
        width: this.width,
        xPath: 'numOfDocuments',
        yName: this.chartYPath.titleCase(),
        yPath: themesChartPaths[this.chartYPath],
      });
    },

    commentAnnotationCallBack(theme, action) {
      if (action === 'edit') {
        this.selectTheme({ theme });
        this.setModalComponent({ component: ThemesIntensityAnnotationModal });
      }
      if (action === 'remove') {
        this.removeCustomThemesCommentAnnotation({ themeId: theme.id });
        this.updateCustomThemeData();
      }
      if (action === 'movePosition') {
        const customTheme = this.customThemes.find(c => c.id === theme.id);
        customTheme.commentPositionX = theme.commentPositionX;
        customTheme.commentPositionY = theme.commentPositionY;
        this.addCustomThemes({ themes: [customTheme] });
      }
    },

    finishAnnotation() {
      this.setAnnotationMode({ annotationMode: false });
    },

    isUndefinedOrNull(value) {
      return value === undefined || value === null;
    },

    onSelect(theme) {
      if (this.bcId === 'themes-intensity-chart') {
        // TODO: scroll to theme
      }
      this.selectTheme({ theme });
    },

    resetChart() {
      this.setModalComponent({ component: ResetThemesIntensityChartModal });
    },

    themeStatsAnnotationCallBack(theme, action) {
      if (action === 'edit') {
        this.selectTheme({ theme });
        this.setModalComponent({ component: ThemesIntensityThemeStateModal });
      }
      if (action === 'remove') {
        this.removeCustomThemesStatsAnnotation({ themeId: theme.id });
        this.updateCustomThemeData();
      }
      if (action === 'movePosition') {
        const customTheme = this.customThemes.find(c => c.id === theme.id);
        customTheme.themeStatsPositionX = theme.themeStatsPositionX;
        customTheme.themeStatsPositionY = theme.themeStatsPositionY;
        this.addCustomThemes({ themes: [customTheme] });
      }
    },

    onClickSaveLabelTitle(label) {
      if (label !== this.selectedTitle) {
        datasetsRequestV0.persistCustomChart(CustomChartType.THEME_ANALYSIS, label, this.selectedDesc);
        this.setCustomChartTitles({ key: CustomChartType.THEME_ANALYSIS.name, title: label });
      }
    },

    onClickSaveLabelDesc(labelDesc) {
      if (labelDesc !== this.defaultDesc) {
        datasetsRequestV0.persistCustomChart(CustomChartType.THEME_ANALYSIS, this.selectedTitle, labelDesc);
        this.setCustomChartDescriptions({ key: CustomChartType.THEME_ANALYSIS.name, desc: labelDesc });
      }
    },

    updateCustomThemeData() {
      this.updateShowedMaxAndMinIntensity();
      this.chart.updateCustomThemeData({
        data: this.customThemes,
        intensityNegativeMax: this.showedDataNegativeMax,
        intensityNegativeMin: this.showedDataNegativeMin,
        intensityPositiveMax: this.showedDataPositiveMax,
        intensityPositiveMin: this.showedDataPositiveMin,
      });
    },

    updateFilteredMaxAndMinScore() {
      const attrPath = themesChartPaths[this.chartYPath];
      const scores = this.filteredThemes
        .map(t => {
          return at(t, attrPath) * this.flipped * 100;
        });

      this.filteredDataMaxScore = scores?.length > 0 ? Math.max(...scores) : 100;
      this.filteredDataMinScore = scores?.length > 0 ? Math.min(...scores) : -100;
    },

    updateShowedMaxAndMinIntensity() {
      const intensityByLevel = (theme, level) => {
        if (level === ThemesIntensityLevel.MEDIUM) {
          return at(theme, 'medIntensityPercent');
        }
        if (level === ThemesIntensityLevel.LOW) {
          return at(theme, 'lowIntensityPercent');
        }
        return at(theme, 'highIntensityPercent');
      };

      const themeScoreByPath = (theme, path) => {
        const attrPath = themesChartPaths[path];
        const flipped = [ThemesChartPath.SURPRISE, ThemesChartPath.ANGER].includes(path) ? -1 : 1;
        return at(theme, attrPath) * flipped || 0;
      };

      const negativeIntensities = this.customThemes
        .filter(t => themeScoreByPath(t, this.chartYPath) < 0)
        .map(t => intensityByLevel(t, this.level));

      const positiveIntensities = this.customThemes
        .filter(t => themeScoreByPath(t, this.chartYPath) >= 0)
        .map(t => intensityByLevel(t, this.level));

      this.showedDataNegativeMin = Math.min(...negativeIntensities);
      this.showedDataNegativeMax = Math.max(...negativeIntensities);
      this.showedDataPositiveMin = Math.min(...positiveIntensities);
      this.showedDataPositiveMax = Math.max(...positiveIntensities);
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-intensity-chart {
  @include flex("block", "row", "center", "center");
  @include stretch;

  max-width: 960px;
  max-height: 540px;

  svg {
    @include stretch;

    height: 100%;
    max-width: 100%;

    .domain {
      display: none;
    }

    .tick {
      color: $body-copy-light;
    }

    .legend text {
      fill: $body-copy-light;
    }

    circle {
      cursor: pointer;
      opacity: 0.5;
      transition: opacity $interaction-transition-time;

      &:hover {
        opacity: 1;
      }
    }

    .label-text {
      text-overflow: ellipsis;
    }

    .caption-container {
      @include flex("block", "column", "flex-start", "center");

      .caption-input-container {
        @include flex("block", "row", "flex-start", "center");

        position: relative;

        .caption-input {
          color: #131C29;
          cursor: pointer;
          display: inline-block;
          font-family: 'Inter',serif;
          font-size: 18px;
          font-weight: 700;
          height: 100%;
          min-width: 200px;
          position: relative;
        }

        .edit-button {
          @include flex("block", "row", "center", "center");

          background-color: #FFF;
          border-radius: 50%;
          border: 1px solid rgba(#24124D, 0.45);
          color: #2D1757;
          cursor: pointer;
          font-size: $font-size-xxs;
          font-weight: $font-weight-bold;
          height: 1.5rem;
          margin-left: 0.5rem;
          padding: 0.4rem 0.3rem;
          text-transform: uppercase;
          visibility: hidden;
          width: 1.5rem;
        }

        &:hover .edit-button {
          visibility: visible;
        }

      }

      .done-button{
        background: #2D59FF;
        border-radius: 2px;
        color: #FFFFFF;
        font-size: $font-size-xxs;
        font-weight: $font-weight-bold;
        padding: 0.3rem 0.5rem;
        margin-left: 0.5em;
      }

      .label-title, .label-description {
        @include flex("block", "row", "flex-start", "center");

        height: 1.5rem;

        &.active {
          cursor: text;

          .caption-input {
            border-bottom: 1px solid #2D59FF;
            color: #2D59FF;
            font-family: 'Inter',serif;
            font-size: 18px;
            font-style: normal;
            font-weight: 700;
            line-height: 22px;
            outline: none;
            pointer-events: none;
          }

          .edit-button {
            display: none;
          }

          .done-button{
            @include flex("block", "row", "center", "center");
            cursor: pointer;
          }
        }

      }

      .label-description {
        margin-top: 0.5em;
      }
    }

  }
}
</style>
