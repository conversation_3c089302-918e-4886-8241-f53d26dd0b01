import {
  axisBottom,
  axisLeft,
  drag,
  easeSin,
  scaleLinear,
  scalePow,
  select,
  transition,
} from 'd3';
import { at, isNaN } from 'lodash-es';
import ThemesIntensityLevel from '@/enum/themes-intensity-level';
import ThemesIntensityRange from '@/enum/themes-intensity-range';
import ScoreRateHelper from '@/helpers/score-rate-helper';
import dialColors from '@/enum/colors';
import dialColorsLight from '@/enum/colors-light';
import ThemesChartPath from '@/enum/themes-chart-path';
import ThemesIntensityAnnotationBg from '@/enum/themes-intensity-annotation-bg';

const scoreRate = new ScoreRateHelper();

export default class ThemesIntensityChartD3 {
  constructor({
    bcId,
    chartOptions,
    commentAnnotationCallBack,
    customThemeData,
    data,
    filteredDataMaxScore,
    filteredDataMinScore,
    finishAnnotationCallBack,
    height,
    hoverTheme,
    isChanged,
    intensityLevel,
    intensityNegativeMax,
    intensityNegativeMin,
    intensityPositiveMax,
    intensityPositiveMin,
    isCustom,
    labelTitle,
    labelTitleCallback,
    labelDesc,
    labelDescCallback,
    resetChartCallback,
    selectCallback,
    selected,
    selectedDataset,
    showAnnotations,
    themeStatsAnnotationCallBack,
    totalVolume,
    xPath,
    yName,
    yPath,
  }) {
    /* Parameter assignment */
    Object.assign(this, {
      // custom chart & symbol when adding to custom
      bcId,
      chartOptions,
      commentAnnotationCallBack,
      customThemeData,
      data,
      filteredDataMaxScore, // todo - can use getXValueMax & getYValueMax instead, remove later
      filteredDataMinScore, // todo - can use getXValueMax & getYValueMax instead, remove later
      finishAnnotationCallBack,
      height,
      hoverTheme,
      isChanged,
      intensityLevel,
      intensityNegativeMax,
      intensityNegativeMin,
      intensityPositiveMax,
      intensityPositiveMin,
      isCustom,
      labelTitle,
      labelTitleCallback,
      labelDesc,
      labelDescCallback,
      resetChartCallback,
      selectCallback,
      selected,
      selectedDataset,
      showAnnotations,
      themeStatsAnnotationCallBack,
      totalVolume,
      xPath,
      yName,
      yPath,
    });

    /* Chart constants */
    this.height = 540;
    this.width = 960;

    this.hMarginRatio = 0.06;
    this.wMarginRatio = 0.06;

    this.hMarginCaption = 10;

    this.arrowHeight = 5;
    this.bubbleLabelColor = '#131C29';
    this.bubbleLabelOpacity = 0.8;
    this.bubbleMaxOpacity = 1;
    this.bubbleMinOpacity = 0.2;
    this.fontSize = 10;
    this.fontSizeXS = 12;
    this.fontSizeSM = 14;

    this.annotationBlockHeight = 60;
    this.annotationBlockWidth = 150;
    this.annotationLineThick = 1.5;

    // Theme stats
    this.themeStatsBlockHeight = 100;
    this.themeStatsBlockOpacity = 1;
    this.themeStatsBlockWidth = 214;
    this.themeStatsLineOpacity = 1;
    this.themeStatsPadding = 15;
    this.themeStatsPaddingTop = 5;
    this.themeStatsTextGap = 10;

    // Comment blocks
    this.commentBlockDescHeightDefault = 115;
    this.commentBlockTitleHeightDefault = 40;
    this.commentBlockPading = 15;
    this.commentBlockWidth = 230;

    this.symbolOpacity = 1;
    this.symbolScale = 0.3;
    this.symbolThick = 0.1;

    this.xScale = this.getXScale;
    this.yScale = this.getYScale;

    // Intensity Caption variables
    this.captionPaddingTop = 15;

    // Intensity Legend variables
    this.legendIntensityBarHeight = 16;
    this.legendNoteLineLength = 10;
    this.legendPadding = 16;
    this.legendPaddingTop = 30;
    this.legendBlockIntensityHeight = 48;
    this.legendSectionTitleHeight = 75;

    this.intensityRange = ThemesIntensityRange.ALL;

    // Chart Options
    this.annotationBg = this.chartOptions.annotationBg;
    this.bubbleSize = this.chartOptions.bubbleSize;
    this.showAnnotations = this.chartOptions.showAnnotations;
    this.showAxisLabel = this.chartOptions.showAxisLabel;
    this.showBubbleLabels = this.chartOptions.showBubbleLabels;
    this.showConnectorLines = this.chartOptions.showConnectorLines;
    this.showDescription = this.chartOptions.showDescription;
    this.showTitle = this.chartOptions.showTitle;
    this.xAxisLabel = this.chartOptions.xAxisLabel;
    this.yAxisLabel = this.chartOptions.yAxisLabel;

    // Annotation mode
    this.annotationMode = false;
    this.annotationModePaddingTop = 33;

    this.labelTitleEditMode = false;
    this.labelDescEditMode = false;

    // Annotation dropdown
    this.annotationTextWeight = 400;
    this.annotationTextWeightBold = 600;

    this.createChart();
  }

  /* INTERNAL METHODS */

  createChart() {
    // Intensity Caption
    const container = select(`#${this.bcId}`).style('display', 'flex').style('flex-direction', 'column');

    this.svg = container.append('svg')
      .attr('id', `${this.bcId}-svg`)
      .attr('height', this.height)
      .attr('width', this.width)
      .attr('viewBox', `0 0 ${this.width} ${this.height}`)
      .attr('preserveAspectRatio', 'xMidYMid meet');

    this.captionContainer = this.svg.append('g').attr('id', `${this.bcId}-caption`);
    this.captionLabels = this.captionContainer.append('g').attr('class', 'labels');
    this.captionAnnotationSvg = this.captionContainer
      .append('svg')
      .attr('id', `${this.bcId}-caption-annotation-svg`)
      .style('display', 'none');
    this.captionAnnotationLabels = this.captionAnnotationSvg.append('g').attr('class', 'caption-annotation-labels');

    // Intensity Legend
    this.legendContainer = this.svg.append('g').attr('id', `${this.bcId}-legend`);
    this.setLinearGradientNegative();
    this.setLinearGradientPositive();
    this.legendBackground = this.legendContainer.append('g').attr('class', 'legend-background');
    this.legendLabels = this.legendContainer.append('g').attr('class', 'legend-labels');
    this.legendLabelsLine = this.legendContainer.append('g').attr('class', 'legend-labels-line');
    this.legendNegativeIntensity = this.legendContainer.append('g').attr('class', 'legend-negative-intensity');
    this.legendPositiveIntensity = this.legendContainer.append('g').attr('class', 'legend-positive-intensity');

    // Intensity Chart

    this.chartContainer = this.svg.append('g').attr('id', `${this.bcId}-chart`);
    this.chartBackground = this.chartContainer.append('g').attr('class', 'chart-background');

    this.xAxis = this.chartContainer.append('g').attr('class', 'axis x');
    this.yAxis = this.chartContainer.append('g').attr('class', 'axis y');
    this.xLegend = this.chartContainer.append('g').attr('class', 'legend x');
    this.yLegend = this.chartContainer.append('g').attr('class', 'legend y');
    this.bubbles = this.chartContainer.append('g').attr('class', 'bubbles');
    this.bubbleLabels = this.chartContainer.append('g').attr('class', 'bubble-labels');
    // Theme stats annotation
    this.themeStatsLines = this.chartContainer.append('g').attr('class', 'theme-stats-lines');
    this.themeStatsBlocks = this.chartContainer.append('g').attr('class', 'theme-stats-blocks');
    // Comment annotation
    this.commentLines = this.chartContainer.append('g').attr('class', 'comment-lines');
    this.commentBlocks = this.chartContainer.append('g').attr('class', 'comment-blocks');
    this.annotationBlocks = this.chartContainer.append('g').attr('class', 'annotation-blocks');
    this.symbols = this.chartContainer.append('g').attr('class', 'symbols');
    this.clip = this.chartContainer
      .append('defs')
      .append('SVG:clipPath')
      .attr('id', 'clip')
      .append('rect');

    this.updateChart();
  }

  /* Container methods */

  updateContainer() {
    this.captionLabels.style('display', this.annotationMode ? 'none' : null);
    this.captionAnnotationSvg.style('display', !this.annotationMode ? 'none' : null);
  }

  /* Caption Intensity methods */

  setCaptionAnnotationLabels() {
    this.captionAnnotationLabels
      .selectAll('g')
      .data([{}])
      .join(
        enter => {
          const selection = enter
            .append('g')
            .attr('class', 'caption-annotation caption-annotation-labels')
            .attr('opacity', 1);

          selection
            .append('text')
            .attr('class', 'label-title')
            .attr('dx', 14)
            .attr('dy', this.annotationModePaddingTop)
            .attr('pointer-events', 'none')
            .attr('font-family', 'Inter, sans-serif')
            .attr('font-weight', 700)
            .attr('font-size', 18)
            .attr('text-anchor', 'start')
            .attr('fill', '#131C29')
            .text('Select Theme Bubbles to Annotate the Chart.');

          selection
            .append('rect')
            .attr('class', 'annotation-button')
            .attr('x', 430)
            .attr('y', this.annotationModePaddingTop - 23)
            .attr('width', 70)
            .attr('height', 25)
            .attr('stroke', 'none')
            .attr('fill', '#222')
            .attr('rx', 2)
            .attr('ry', 2)
            .attr('cursor', 'pointer')
            .on('mousedown', this.finishAnnotationCallBack);

          selection
            .append('text')
            .attr('class', 'annotation-text-icon')
            .attr('fill', '#FFF')
            .attr('font-family', 'FontAwesome')
            .attr('font-size', 12)
            .attr('font-weight', 400)
            .attr('pointer-events', 'none')
            .attr('dx', 440)
            .attr('dy', this.annotationModePaddingTop - 6)
            .text('\uf00c');

          selection
            .append('text')
            .attr('class', 'annotation-text')
            .attr('dx', 455)
            .attr('dy', this.annotationModePaddingTop - 6)
            .attr('fill', '#FFF')
            .attr('font-family', 'Inter, sans-serif')
            .attr('font-size', 12)
            .attr('font-weight', 800)
            .attr('pointer-events', 'none')
            .attr('text-anchor', 'start')
            .text('DONE');

          selection
            .append('rect')
            .attr('class', 'reset-button')
            .attr('x', 860)
            .attr('y', this.annotationModePaddingTop - 23)
            .attr('width', 70)
            .attr('height', 25)
            .attr('stroke', 'none')
            .attr('fill', '#F93442')
            .attr('rx', 2)
            .attr('ry', 2)
            .attr('cursor', 'pointer')
            .attr('opacity', () => {
              return this.isChanged ? 1 : 0;
            })
            .on('mousedown', this.resetChartCallback);

          selection
            .append('text')
            .attr('class', 'reset-text-icon')
            .attr('fill', '#FFF')
            .attr('font-family', 'FontAwesome')
            .attr('font-size', 12)
            .attr('font-weight', 400)
            .attr('pointer-events', 'none')
            .attr('dx', 868)
            .attr('dy', this.annotationModePaddingTop - 6)
            .attr('opacity', () => {
              return this.isChanged ? 1 : 0;
            })
            .text('\uf021');

          selection
            .append('text')
            .attr('class', 'reset-text')
            .attr('dx', 883)
            .attr('dy', this.annotationModePaddingTop - 6)
            .attr('fill', '#FFF')
            .attr('font-family', 'Inter, sans-serif')
            .attr('font-size', 12)
            .attr('font-weight', 800)
            .attr('pointer-events', 'none')
            .attr('text-anchor', 'start')
            .attr('opacity', () => {
              return this.isChanged ? 1 : 0;
            })
            .text('RESET');

          selection
            .append('text')
            .attr('class', 'label-description')
            .attr('dx', 14)
            .attr('dy', this.annotationModePaddingTop + 30)
            .attr('pointer-events', 'none')
            .style('font-family', 'Inter, sans-serif')
            .style('font-size', `${this.fontSizeXS}px`)
            .style('font-weight', 400)
            .attr('text-anchor', 'start')
            .attr('fill', '#131C29')
            .text('Alternatively, you can reposition bubble labels by clicking and dragging them.');
        },
        update => {
          update
            .attr('opacity', 1);

          update.select('.reset-button')
            .attr('opacity', () => {
              return this.isChanged ? 1 : 0;
            });

          update.select('.reset-text-icon')
            .attr('opacity', () => {
              return this.isChanged ? 1 : 0;
            });

          update.select('.reset-text')
            .attr('opacity', () => {
              return this.isChanged ? 1 : 0;
            });
        },
        exit => exit.remove(),
      );
  }

  setCaptionLabels() {
    this.captionLabels
      .selectAll('foreignObject')
      .data([{}])
      .join(
        enter => {
          const selection = enter
            .append('foreignObject')
            .classed('caption-container', true)
            .attr('opacity', 1)
            .attr('x', 14)
            .attr('y', this.captionPos)
            .attr('height', this.captionHeight)
            .attr('width', this.width);

          selection
            .append('xhtml:div')
            .attr('xmlns', 'http://www.w3.org/1999/xhtml')
            .classed('label-title', true);
          selection
            .select('.label-title')
            .append('xhtml:div')
            .attr('xmlns', 'http://www.w3.org/1999/xhtml')
            .classed('caption-input-container', true)
            .style('cursor', this.showTitle ? 'pointer' : 'default')
            .style('max-width', '850px')
            .on('click', () => {
              if (this.showTitle) {
                this.onClickEditTitle();
              }
            })
            .on('mouseenter', () => {
              if (this.showTitle) {
                this.captionLabels.select('.label-title').select('.edit-button').style('visibility', 'visible');
              }
            })
            .on('mouseleave', () => {
              this.captionLabels.select('.label-title').select('.edit-button').style('visibility', 'hidden');
            })
            .on('keydown', event => {
              if (this.labelTitleEditMode) {
                if (event.keyCode === 13) {
                  this.onClickSaveTitle();
                }
              }
            });
          selection
            .select('.caption-input-container')
            .append('xhtml:div')
            .classed('caption-input', true)
            .attr('xmlns', 'http://www.w3.org/1999/xhtml')
            .attr('contenteditable', false)
            .style('visibility', this.labelVisibilityFn(this.showTitle))
            .style('color', '#131C29')
            .style('display', 'inline-block')
            .style('font-family', 'Inter, sans-serif')
            .style('font-size', '18px')
            .style('font-weight', 700)
            .style('width', 'fit-content')
            .style('text-overflow', 'ellipsis')
            .style('overflow', 'hidden')
            .style('white-space', 'nowrap')
            .style('max-width', '850px')
            .style('min-width', this.labelTitle?.length > 0 ? '100px' : '850px')
            .text(this.labelTitle);

          // Add a class to the caption container

          selection
            .select('.caption-input-container')
            .append('xhtml:div')
            .classed('edit-button', true)
            .style('visibility', 'hidden')
            .html('<i class="fa-solid fa-pen icon"></i>')
            .on('click', () => {
              if (this.showTitle) {
                this.onClickEditTitle(true);
              }
            }); // Add a class to the caption container

          selection
            .select('.label-title')
            .append('xhtml:div')
            .classed('done-button', true)
            .style('display', 'none')
            .html('DONE')
            .on('click', () => {
              this.onClickSaveTitle();
            }); // Add a class to the caption container

          const descriptionContainer = enter.select('.caption-container');

          descriptionContainer
            .append('xhtml:div')
            .attr('xmlns', 'http://www.w3.org/1999/xhtml')
            .classed('label-description', true);

          descriptionContainer
            .select('.label-description')
            .append('xhtml:div')
            .attr('xmlns', 'http://www.w3.org/1999/xhtml')
            .classed('caption-input-container', true)
            .style('cursor', this.showDescription ? 'pointer' : 'default')
            .style('max-width', '850px')
            .on('click', () => {
              if (this.showDescription) {
                this.onClickEditDescription();
              }
            })
            .on('mouseenter', () => {
              if (this.showDescription) {
                this.captionLabels.select('.label-description').select('.edit-button').style('visibility', 'visible');
              }
            })
            .on('mouseleave', () => {
              this.captionLabels.select('.label-description').select('.edit-button').style('visibility', 'hidden');
            })
            .on('keydown', event => {
              if (this.labelDescEditMode) {
                if (event.keyCode === 13) {
                  this.onClickSaveDescription();
                }
              }
            });

          descriptionContainer
            .select('.label-description')
            .select('.caption-input-container')
            .append('xhtml:div')
            .classed('caption-input', true)
            .attr('xmlns', 'http://www.w3.org/1999/xhtml')
            .attr('contenteditable', false)
            .attr('y', this.descriptionPos)
            .style('color', '#131C29')
            .style('display', 'inline-block')
            .style('font-family', 'Inter, sans-serif')
            .style('font-size', `${this.fontSizeXS}px`)
            .style('font-weight', 400)
            .style('visibility', this.labelVisibilityFn(this.showDescription))
            .style('width', 'fit-content')
            .style('text-overflow', 'ellipsis')
            .style('overflow', 'hidden')
            .style('white-space', 'nowrap')
            .style('max-width', '850px')
            .style('min-width', this.labelDesc?.length > 0 ? '100px' : '850px')
            .text(this.labelDesc);

          descriptionContainer
            .select('.label-description')
            .select('.caption-input-container')
            .append('xhtml:div')
            .classed('edit-button', true)
            .style('visibility', 'hidden')
            .html('<i class="fa-solid fa-pen icon"></i>')
            .on('click', () => {
              if (this.showDescription) {
                this.onClickEditDescription(true);
              }
            }); // Add a class to the caption container

          descriptionContainer
            .select('.label-description')
            .append('xhtml:div')
            .classed('done-button', true)
            .style('display', 'none')
            .html('DONE')
            .on('click', () => {
              this.onClickSaveDescription();
            }); // Add a class to the caption container
        },
        update => {
          update
            .attr('opacity', 1);

          update.select('.label-title')
            .select('.caption-input')
            .text(this.labelTitle)
            .style('visibility', this.labelVisibilityFn(this.showTitle))
            .style('min-width', this.labelTitle?.length > 0 ? '100px' : '850px');

          update.select('.label-title')
            .select('.caption-input-container')
            .style('cursor', this.showTitle ? 'pointer' : 'default');

          update.select('.label-description')
            .select('.caption-input')
            .text(this.labelDesc)
            .style('visibility', this.labelVisibilityFn(this.showDescription))
            .style('min-width', this.labelDesc?.length > 0 ? '100px' : '850px');

          update.select('.label-description')
            .select('.caption-input-container')
            .style('cursor', this.showDescription ? 'pointer' : 'default');
        },
        exit => exit.remove(),
      );
  }

  setCaptionContainer() {
    this.captionContainer
      .attr('height', this.captionHeight)
      .attr('width', this.width)
      .style('transform', `translateY(${this.hMarginCaption / 2}px)`);
  }

  setCaptionAnnotationSvg() {
    this.captionAnnotationSvg
      .attr('height', this.captionHeight)
      .attr('width', this.width);
  }

  annotationX(d) {
    return this.xScale(this.xVal(d)) - this.annotationBlockWidth / 2;
  }

  annotationY(d) {
    const newCircleY = this.yScale(this.yVal(d));
    const pos = newCircleY + Number(this.bubbleSize) + this.arrowHeight;

    if (pos + this.annotationBlockHeight < this.innerHeight) return pos;

    return newCircleY - Number(this.bubbleSize) - this.annotationBlockHeight - this.arrowHeight;
  }

  annotationBgColor() {
    if (ThemesIntensityAnnotationBg.enumValueOf(this.annotationBg)) {
      return ThemesIntensityAnnotationBg.enumValueOf(this.annotationBg).bgColor();
    }
    return ThemesIntensityAnnotationBg.LIGHT.bgColor();
  }

  annotationTextColor() {
    if (ThemesIntensityAnnotationBg.enumValueOf(this.annotationBg)) {
      return ThemesIntensityAnnotationBg.enumValueOf(this.annotationBg).textColor();
    }
    return ThemesIntensityAnnotationBg.LIGHT.textColor();
  }

  annotationVisibilityFn() {
    return d => {
      return this.isCustom(d) ? 'visible' : 'hidden';
    };
  }

  bubbleLabelX(d) {
    return this.xScale(this.xVal(d));
  }

  bubbleLabelY(d) {
    const gapBetweenLabelAndBubble = 5;
    return this.yScale(this.yVal(d)) - this.bubbleSize - gapBetweenLabelAndBubble;
  }

  bubbleStrokeStype(d) {
    if (this.isSelectedBubble(d)) {
      return '#3858FF';
    }
    const opacity = this.opacityScaler(d);
    return this.yVal(d) >= 0 ? `rgba(63, 129, 56, ${opacity})` : `rgba(181, 55, 46, ${opacity})`;
  }

  bubbleStrokeWidthStyle(d) {
    if (this.isSelectedBubble(d)) return '2px';
    return '1px';
  }

  bubbleVisibilityFn() {
    return d => {
      return this.isShowBubble(d) ? 'visible' : 'hidden';
    };
  }

  dragThemeStatsStarted(event, d) {
    if (this.annotationMode && this.showThemeStats(d)) {
      this.themeStatsBlocks.select(`.theme-stats-${d.id}`)
        .attr('cursor', 'grabbing');
      // pointer position
      let translateVal =
        this.themeStatsBlocks.select(`.theme-stats-${d.id}`).attr('transform');
      translateVal = translateVal.substring(translateVal.indexOf('(') + 1, translateVal.indexOf(')')).split(',');
      this.pointerX = translateVal[0] - event.x;
      this.pointerY = translateVal[1] - event.y;
    }
  }

  draggingThemeStats(event, d) {
    if (this.annotationMode && this.showThemeStats(d)) {
      this.themeStatsBlocks.selectAll(`.theme-stats-${d.id}`)
        .attr('transform', `translate(${event.x + this.pointerX}, ${event.y + this.pointerY})`);

      this.themeStatsLines.select(`.link-line-${d.id}`)
        .attr('x1', () => {
          const startX = this.xScale(this.xVal(d));
          const startY = this.yScale(this.yVal(d));
          const endX = event.x + this.pointerX + (this.themeStatsBlockWidth / 2);
          const endY = event.y + this.pointerY + (this.themeStatsBlockHeight / 2);
          const angle = Math.atan2(Math.abs(endY - startY), Math.abs(endX - startX));
          if ((endX - startX) >= 0) return startX + Math.cos(angle) * this.zScaler(d);
          return startX - Math.cos(angle) * this.zScaler(d);
        })
        .attr('y1', () => {
          const startX = this.xScale(this.xVal(d));
          const startY = this.yScale(this.yVal(d));
          const endX = event.x + this.pointerX + (this.themeStatsBlockWidth / 2);
          const endY = event.y + this.pointerY + (this.themeStatsBlockHeight / 2);
          const angle = Math.atan2(Math.abs(endY - startY), Math.abs(endX - startX));
          if (endY - startY >= 0) return startY + Math.sin(angle) * this.zScaler(d);
          return startY - Math.sin(angle) * this.zScaler(d);
        })
        .attr('x2', event.x + this.pointerX + (this.themeStatsBlockWidth / 2))
        .attr('y2', event.y + this.pointerY + (this.themeStatsBlockHeight / 2));
    }
  }

  dragThemeStatsEnded(event, d) {
    if (this.annotationMode && this.showThemeStats(d)) {
      this.themeStatsBlocks.selectAll('.theme-stats')
        .attr('cursor', 'grab');
      // invert to get value - use in new zoom in/out
      d.themeStatsPositionX = this.xScale.invert(event.x + this.pointerX);
      d.themeStatsPositionY = this.yScale.invert(event.y + this.pointerY);
      this.themeStatsAnnotationCallBack(d, 'movePosition');
    }
  }

  isSelectedBubble(d) {
    return this.selected && this.selected.id === d.id;
  }

  isHoveredBubble(d) {
    return this.hoverTheme && this.hoverTheme.id === d.id;
  }

  isShowBubble(d) {
    return this.isCustom(d) || this.isHoveredBubble(d);
  }

  labelVisibilityFn(condition) {
    return condition ? 'visible' : 'hidden';
  }

  intensityPercent(d) {
    const percent = d.highIntensityPercent || 0;
    switch (this.intensityLevel) {
      case ThemesIntensityLevel.HIGH:
        return d.highIntensityPercent || 0;
      case ThemesIntensityLevel.MEDIUM:
        return d.medIntensityPercent || 0;
      case ThemesIntensityLevel.LOW:
        return d.lowIntensityPercent || 0;
      default:
    }
    return percent;
  }

  onClickEditTitle(collapseToEnd) {
    this.labelTitleEditMode = true;
    this.captionLabels.select('.label-title').classed('active', this.labelTitleEditMode);
    this.captionLabels.select('.label-title')
      .select('.caption-input')
      .attr('contenteditable', this.labelTitleEditMode)
      .style('color', '#2D59FF')
      .style('text-overflow', 'unset');

    const inputNode = this.captionLabels.select('.label-title').select('.caption-input').node();
    this.onSetFocus(inputNode, collapseToEnd);

    this.captionLabels.select('.label-title').select('.done-button').style('display', 'block');
  }

  onClickEditDescription(collapseToEnd) {
    this.labelDescEditMode = true;
    this.captionLabels.select('.label-description').classed('active', this.labelDescEditMode);
    this.captionLabels.select('.label-description')
      .select('.caption-input')
      .attr('contenteditable', this.labelDescEditMode)
      .style('color', '#2D59FF')
      .style('text-overflow', 'unset');
    const inputNode = this.captionLabels.select('.label-description').select('.caption-input').node();
    this.onSetFocus(inputNode, collapseToEnd);
    this.captionLabels.select('.label-description').select('.done-button').style('display', 'block');
  }

  onClickSaveTitle() {
    this.labelTitleEditMode = !this.labelTitleEditMode;
    this.captionLabels.select('.label-title').classed('active', this.labelTitleEditMode);
    this.captionLabels.select('.label-title').select('.caption-input')
      .style('min-width', this.captionLabels.select('.label-title').select('.caption-input').text()?.length > 0 ? '100px' : '850px')
      .attr('contenteditable', this.labelTitleEditMode)
      .style('color', '#131C29')
      .style('text-overflow', 'ellipsis');
    this.captionLabels.select('.label-title').select('.done-button').style('display', this.labelTitleEditMode ? 'block' : 'none');
    this.labelTitleCallback(this.captionLabels.select('.label-title').select('.caption-input').text());
  }

  onClickSaveDescription() {
    this.labelDescEditMode = !this.labelDescEditMode;
    this.captionLabels.select('.label-description').classed('active', this.labelDescEditMode);
    this.captionLabels.select('.label-description')
      .select('.caption-input')
      .style('min-width', this.captionLabels.select('.label-description').select('.caption-input').text()?.length > 0 ? '100px' : '850px')
      .attr('contenteditable', this.labelDescEditMode)
      .style('color', '#131C29')
      .style('text-overflow', 'ellipsis');
    this.captionLabels.select('.label-description').select('.done-button').style('display', this.labelDescEditMode ? 'block' : 'none');
    this.labelDescCallback(this.captionLabels.select('.label-description').select('.caption-input').text());
  }

  onSetFocus(inputNode, collapseToEnd) {
    inputNode.focus();
    if (collapseToEnd) {
      getSelection().selectAllChildren(inputNode);
      getSelection().collapseToEnd();
    }
  }

  opacityScaler(d) {
    let percent = this.intensityPercent(d);

    let rs;
    if (this.yVal(d) >= 0) {
      if (this.intensityRange === ThemesIntensityRange.NEGATIVE) return 0;
      if (percent < this.intensityPositiveMin) percent = this.intensityPositiveMin; // when hover a theme, percent can be smaller than min
      rs = this.opacityPositiveScale(percent);
    } else {
      if (this.intensityRange === ThemesIntensityRange.POSITIVE) return 0;
      if (percent < this.intensityNegativeMin) percent = this.intensityNegativeMin; // when hover a theme, percent can be smaller than min
      rs = this.opacityNegativeScale(percent);
    }

    if (isNaN(rs)) return 1; // when all positive or negative bubbles are hide, the result can be NaN then we should return 1

    return rs;
  }

  scoreColour(d) {
    if (this.annotationBg === ThemesIntensityAnnotationBg.LIGHT.name) {
      return dialColorsLight[scoreRate.getAdoreScoreType(d.polarity)];
    }
    return dialColors[scoreRate.getAdoreScoreType(d.polarity)];
  }

  editButtonColour() {
    if (this.annotationBg === ThemesIntensityAnnotationBg.LIGHT.name) {
      return dialColorsLight.edit_button;
    }
    return dialColors.edit_button;
  }

  deleteButtonColour() {
    if (this.annotationBg === ThemesIntensityAnnotationBg.LIGHT.name) {
      return dialColorsLight.delete_button;
    }
    return dialColors.delete_button;
  }

  setAnnotationBlocks() {
    this.annotationBlocks
      .selectAll('g')
      .data(this.data)
      .join(
        enter => {
          const selection = enter
            .append('g')
            .attr('class', d => `annotation annotation-${d.id}`)
            .attr('transform', d => `translate(${this.annotationX(d)}, ${this.annotationY(d)})`)
            .style('visibility', 'hidden');

          selection
            .append('rect')
            .attr('class', 'annotation-bg')
            .attr('width', this.annotationBlockWidth)
            .attr('height', this.annotationBlockHeight)
            .attr('stroke', '#000')
            .attr('stroke-width', 1)
            .attr('fill', '#fff')
            .attr('rx', 3)
            .attr('ry', 3);

          selection
            .append('text')
            .attr('class', 'annotation-arrow fa fa-triangle fa-sharp fa-regular') // Give it the font-awesome class
            .attr('dominant-baseline', 'central')
            .attr('x', d => {
              if (this.annotationY(d) > this.yScale(this.yVal(d))) {
                return 0;
              }
              return this.annotationBlockHeight;
            })
            .attr('y', d => {
              if (this.annotationY(d) > this.yScale(this.yVal(d))) {
                return this.annotationBlockWidth / 2;
              }
              return -this.annotationBlockWidth / 2;
            })
            .attr('fill', '#fff')
            .attr('stroke', '#000')
            .attr('stroke-width', 1)
            .attr('transform', d => {
              if (this.annotationY(d) > this.yScale(this.yVal(d))) {
                return 'rotate(270)';
              }
              return 'rotate(90)';
            })
            .attr('text-anchor', 'start')
            .style('font-family', 'FontAwesome')
            .style('font-size', '0.8rem')
            .text('\uf04b');

          selection
            .append('rect')
            .attr('class', 'annotation-arrow-bottom')
            .attr('fill', '#fff')
            .attr('height', 3)
            .attr('x', this.annotationBlockWidth / 2 - 5)
            .attr('y', d => {
              if (this.annotationY(d) > this.yScale(this.yVal(d))) {
                return -1;
              }
              return this.annotationBlockHeight - 1;
            })
            .attr('width', 10)
            .attr('rx', 2)
            .attr('ry', 2);

          selection
            .append('rect')
            .attr('class', 'annotation-text-bg')
            .attr('cursor', 'pointer')
            .attr('fill', '#fff')
            .attr('height', this.annotationBlockHeight / 2)
            .attr('opacity', 0)
            .attr('stroke', '#000')
            .attr('stroke-width', 1)
            .attr('width', this.annotationBlockWidth)
            .on('mouseenter', (event, d) => {
              this.annotationBlocks.select(`.annotation-${d.id} .annotation-text`).attr('font-weight', this.annotationTextWeightBold);
            })
            .on('mouseleave', (event, d) => {
              this.annotationBlocks.select(`.annotation-${d.id} .annotation-text`).attr('font-weight', this.annotationTextWeight);
            })
            .on('mousedown', (_, d) => {
              this.commentAnnotationCallBack(d, 'edit');
              this.annotationBlocks.select(`.annotation-${d.id}`).style('visibility', 'hidden');
            });

          selection
            .append('text')
            .attr('class', 'annotation-text')
            .attr('cursor', 'pointer')
            .attr('dx', 10)
            .attr('dy', this.fontSizeSM / 2 + this.annotationBlockHeight / 4)
            .attr('fill', '#131C29')
            .attr('font-family', 'Inter, sans-serif')
            .attr('font-weight', this.annotationTextWeight)
            .attr('font-size', this.fontSizeSM)
            .attr('text-anchor', 'start')
            .text('+ Annotation')
            .on('mouseenter', (event) => {
              select(event.target)
                .attr('font-weight', this.annotationTextWeightBold);
            })
            .on('mouseleave', (event) => {
              select(event.target)
                .attr('font-weight', this.annotationTextWeight);
            })
            .on('mousedown', (_, d) => {
              this.commentAnnotationCallBack(d, 'edit');
              this.annotationBlocks.select(`.annotation-${d.id}`).style('visibility', 'hidden');
            });

          selection
            .append('rect')
            .attr('class', 'theme-stats-bg')
            .attr('cursor', 'pointer')
            .attr('fill', '#fff')
            .attr('height', this.annotationBlockHeight / 2)
            .attr('opacity', 0.1)
            .attr('stroke', '#000')
            .attr('stroke-width', 1)
            .attr('width', this.annotationBlockWidth)
            .attr('y', this.annotationBlockHeight / 2)
            .on('mouseenter', (event, d) => {
              this.annotationBlocks.select(`.annotation-${d.id} .theme-stats-text`).attr('font-weight', this.annotationTextWeightBold);
            })
            .on('mouseleave', (event, d) => {
              this.annotationBlocks.select(`.annotation-${d.id} .theme-stats-text`).attr('font-weight', this.annotationTextWeight);
            })
            .on('mousedown', (_, d) => {
              this.themeStatsAnnotationCallBack(d, 'edit');
              this.annotationBlocks.select(`.annotation-${d.id}`).style('visibility', 'hidden');
            });

          selection
            .append('text')
            .datum(d => d)
            .attr('class', 'theme-stats-text')
            .attr('cursor', 'pointer')
            .attr('dx', 10)
            .attr('dy', this.annotationBlockHeight / 2 + this.fontSizeSM / 2 + this.annotationBlockHeight / 4)
            .attr('fill', '#131C29')
            .attr('font-family', 'Inter, sans-serif')
            .attr('font-weight', this.annotationTextWeight)
            .attr('font-size', this.fontSizeSM)
            .attr('text-anchor', 'start')
            .text('+ Theme Stats')
            .on('mouseenter', (event) => {
              select(event.target)
                .attr('font-weight', this.annotationTextWeightBold);
            })
            .on('mouseleave', (event) => {
              select(event.target)
                .attr('font-weight', this.annotationTextWeight);
            })
            .on('mousedown', (_, d) => {
              this.themeStatsAnnotationCallBack(d, 'edit');
              this.annotationBlocks.select(`.annotation-${d.id}`).style('visibility', 'hidden');
            });
        },
        update => {
          const selection = update
            .attr('class', d => `annotation annotation-${d.id}`)
            .attr('transform', d => `translate(${this.annotationX(d)}, ${this.annotationY(d)})`)
            .style('visibility', 'hidden')
            .call(u => u.transition(this.transition));

          selection
            .select('.annotation-arrow')
            .attr('x', d => {
              if (this.annotationY(d) > this.yScale(this.yVal(d))) {
                return 0;
              }
              return this.annotationBlockHeight;
            })
            .attr('y', d => {
              if (this.annotationY(d) > this.yScale(this.yVal(d))) {
                return this.annotationBlockWidth / 2;
              }
              return -this.annotationBlockWidth / 2;
            })
            .attr('transform', d => {
              if (this.annotationY(d) > this.yScale(this.yVal(d))) {
                return 'rotate(270)';
              }
              return 'rotate(90)';
            });

          selection
            .select('.annotation-arrow-bottom')
            .attr('x', this.annotationBlockWidth / 2 - 5)
            .attr('y', d => {
              if (this.annotationY(d) > this.yScale(this.yVal(d))) {
                return -1;
              }
              return this.annotationBlockHeight - 1;
            });

          selection
            .select('.annotation-text')
            .on('mousedown', (_, d) => {
              this.commentAnnotationCallBack(d, 'edit');
              this.annotationBlocks.select(`.annotation-${d.id}`).style('visibility', 'hidden');
            });

          selection
            .select('.theme-stats-text')
            .on('mousedown', (_, d) => {
              this.themeStatsAnnotationCallBack(d, 'edit');
              this.annotationBlocks.select(`.annotation-${d.id}`).style('visibility', 'hidden');
            });
        },
        exit => exit.remove(),
      );
  }

  setBubbles() {
    this.bubbles
      .selectAll('circle')
      .data(this.data)
      .join(
        enter =>
          enter
            .append('circle')
            .attr('class', d => `bubble-${d.id}`)
            .attr('cx', d => this.xScale(this.xVal(d)))
            .attr('cy', d => this.yScale(this.yVal(d)))
            .attr('r', d => this.zScaler(d))
            .style('cursor', this.annotationMode ? 'pointer' : 'default')
            .style('fill', d => {
              const opacity = this.opacityScaler(d);
              return this.yVal(d) >= 0 ? `rgba(81, 167, 69, ${opacity})` : `rgba(235, 83, 76, ${opacity})`;
            })
            .style('opacity', 1)
            .style('stroke', d => this.bubbleStrokeStype(d))
            .style('stroke-width', d => this.bubbleStrokeWidthStyle(d))
            .style('visibility', this.bubbleVisibilityFn())
            .on('mouseenter', (event, d) => {
              select(event.target)
                .style('stroke', '#000')
                .style('stroke-width', '1.5px');
              if (this.annotationMode) {
                this.symbols.select(`.symbol-${d.id}`).attr('opacity', this.symbolOpacity);
              }
            })
            .on('mouseleave', (event, d) => {
              if (this.selected === null || this.selected.id !== d.id) {
                // this.themeStatsBlocks.select(`.theme-stats-${d.id}`).attr('opacity', this.showThemeStats ? this.annotationBlockOpacity : 0);
                // this.themeStatsLines.select(`.theme-stats-line-${d.id}`).attr('opacity', this.showThemeStats ? this.annotationLineOpacity : 0);
              }
              select(event.target)
                .style('stroke', this.bubbleStrokeStype(d))
                .style('stroke-width', this.bubbleStrokeWidthStyle(d));
              this.symbols.select(`.symbol-${d.id}`).attr('opacity', 0);
            })
            .on('mousedown', (e, d) => {
              if (this.annotationMode) {
                const annotationBlock = this.annotationBlocks.select(`.annotation-${d.id}`);
                const isVisible = annotationBlock.style('visibility') === 'visible';
                this.setAnnotationBlocks();
                annotationBlock.style('visibility', isVisible ? 'hidden' : 'visible');
              } else {
                this.selectCallback(d);
              }
            }),
        update =>
          update
            .attr('class', d => `bubble-${d.id}`)
            .attr('cx', d => this.xScale(this.xVal(d)))
            .attr('cy', d => this.yScale(this.yVal(d)))
            .attr('r', d => this.zScaler(d))
            .style('cursor', this.annotationMode ? 'pointer' : 'default')
            .style('fill', d => {
              const opacity = this.opacityScaler(d);
              return this.yVal(d) >= 0 ? `rgba(81, 167, 69, ${opacity})` : `rgba(235, 83, 76, ${opacity})`;
            })
            .style('stroke', d => this.bubbleStrokeStype(d))
            .attr('stroke-dasharray', d => {
              return this.isHoveredBubble(d) ? [3, 2] : 'unset';
            })
            .style('stroke-width', d => this.bubbleStrokeWidthStyle(d))
            .style('visibility', this.bubbleVisibilityFn())
            .on('mousedown', (e, d) => {
              if (this.annotationMode) {
                const annotationBlock = this.annotationBlocks.select(`.annotation-${d.id}`);
                const isVisible = annotationBlock.style('visibility') === 'visible';
                this.setAnnotationBlocks();
                annotationBlock.style('visibility', isVisible ? 'hidden' : 'visible');
              } else {
                this.selectCallback(d);
              }
            })
            .call(u => u.transition(this.transition)),
        exit => exit.remove(),
      );
  }

  setBubbleLabels() {
    this.bubbleLabels
      .selectAll('g')
      .data(this.data)
      .join(
        enter => {
          const selection = enter
            .append('g')
            .attr('class', d => `bubble-label bubble-label-${d.id}`)
            .attr('opacity', this.showBubbleLabels ? this.bubbleLabelOpacity : 0)
            .attr('transform', d => `translate(${this.labelX(d)}, ${this.labelY(d)})`)
            .call(this.dragLabel)
            .style('visibility', this.bubbleVisibilityFn());

          selection
            .append('text')
            .attr('class', 'bubble-label-text')
            .attr('fill', this.bubbleLabelColor)
            .attr('font-family', 'Inter, sans-serif')
            .attr('font-size', this.fontSizeXS)
            .attr('font-weight', 400)
            .attr('text-anchor', 'middle')
            .attr('x', d => this.bubbleLabelX(d))
            .attr('y', d => this.bubbleLabelY(d))
            .attr('pointer-events', null)
            .style('cursor', this.annotationMode ? 'all-scroll' : 'default')
            .text(d => this.truncateText(d.topicLabel));
        },
        update => {
          const selection = update
            .attr('class', d => `bubble bubble-label-${d.id}`)
            .attr('opacity', this.showBubbleLabels ? this.bubbleLabelOpacity : 0)
            .style('cursor', 'pointer')
            .style('visibility', this.bubbleVisibilityFn())
            .call(u => u.transition(this.transition));

          selection
            .select('.bubble-label-text')
            .attr('x', d => this.bubbleLabelX(d))
            .attr('y', d => this.bubbleLabelY(d))
            .attr('pointer-events', null)
            .style('cursor', this.annotationMode ? 'all-scroll' : 'default')
            .text(d => this.truncateText(d.topicLabel));
        },
        exit => exit.remove(),
      );
  }

  setChartContainer() {
    this.chartContainer
      // Set hard-coded so that the caption height doesn't affect the chart placement, and the user
      // can add the title/description themselves after they export the chart
      // In phase 2, the user will be able to update the title/description themselves, so we can
      // make the value dynamic then
      .style('transform', `translateY(${this.captionHeight - this.hMarginCaption}px)`);
  }

  setChartBackground() {
    this.chartBackground
      .selectAll('g')
      .data([{}])
      .join(
        enter => {
          const selection = enter
            .append('g')
            .attr('class', 'chart-background');

          selection
            .append('rect')
            .attr('class', 'chart-background')
            .attr('dx', 0)
            .attr('dy', 0)
            .attr('width', this.width)
            .attr('height', this.chartHeight)
            .attr('fill', 'transparent')
            .on('mousedown', () => {
              if (this.annotationMode) {
                this.setAnnotationBlocks();
              }
            });
        },
        update => {
          update
            .attr('class', 'chart-background')
            .select('.chart-background')
            .on('mousedown', () => {
              if (this.annotationMode) {
                this.setAnnotationBlocks();
              }
            });
        },
        exit => exit.remove(),
      );
  }

  setClip() {
    this.clip
      .attr('width', this.innerWidth)
      .attr('height', this.innerHeight)
      .attr('x', 0)
      .attr('y', 0);

    this.bubbles
      // .attr('clip-path', 'url(#clip)')
      .attr('transform', `translate(${this.wMargin}, ${this.hMargin})`);
    this.bubbleLabels
      // .attr('clip-path', 'url(#clip)')
      .attr('transform', `translate(${this.wMargin}, ${this.hMargin})`);
    this.annotationBlocks
      // .attr('clip-path', 'url(#clip)')
      .attr('transform', `translate(${this.wMargin}, ${this.hMargin})`);
    this.themeStatsBlocks
      // .attr('clip-path', 'url(#clip)')
      .attr('transform', `translate(${this.wMargin}, ${this.hMargin})`);
    this.themeStatsLines
      // .attr('clip-path', 'url(#clip)')
      .attr('transform', `translate(${this.wMargin}, ${this.hMargin})`);
    this.commentBlocks
      .attr('transform', `translate(${this.wMargin}, ${this.hMargin})`);
    this.commentLines
      .attr('transform', `translate(${this.wMargin}, ${this.hMargin})`);
    this.symbols
      // .attr('clip-path', 'url(#clip)')
      .attr('transform', `translate(${this.wMargin}, ${this.hMargin})`);
  }

  setLegendBackground() {
    this.legendBackground
      .selectAll('g')
      .data([{}])
      .join(
        enter => {
          const selection = enter
            .append('g')
            .attr('class', 'legend-background');

          selection
            .append('rect')
            .attr('class', 'legend-background')
            .attr('dx', 0)
            .attr('dy', 0)
            .attr('width', this.legendWidth)
            .attr('height', this.legendHeight)
            .attr('stroke', 'none')
            .attr('fill', 'rgba(19, 28, 41, 0.07)')
            .attr('rx', 4)
            .attr('ry', 4);
        },
        update => {
          update
            .attr('class', 'legend-background')
            .select('.legend-background')
            .attr('height', this.legendHeight);
        },
        exit => exit.remove(),
      );
  }

  setLegendLabels() {
    this.legendLabels
      .selectAll('g')
      .data([{}])
      .join(
        enter => {
          const selection = enter
            .append('g')
            .attr('class', 'legend-labels')
            .attr('opacity', 1);

          selection
            .append('text')
            .attr('class', 'label-title')
            .attr('dx', this.legendPadding)
            .attr('dy', this.legendPaddingTop)
            .attr('pointer-events', 'none')
            .attr('font-family', 'Inter, sans-serif')
            .attr('font-weight', 800)
            .attr('font-size', this.fontSizeXS)
            .attr('text-anchor', 'start')
            .attr('fill', '#131C29')
            .text('LEGEND');

          selection
            .append('text')
            .attr('class', 'label-description')
            .attr('dx', this.legendPadding)
            .attr('dy', this.legendPaddingTop + 25)
            .attr('pointer-events', 'none')
            .attr('font-family', 'Inter, sans-serif')
            .attr('font-size', this.fontSizeXS)
            .attr('text-anchor', 'start')
            .attr('fill', '#131C29')
            .text(`${this.intensityLevel.text()} Intensity Themes`);
        },
        update => {
          update
            .attr('class', 'legend-label')
            .attr('opacity', 1);

          update.select('.label-description')
            .text(`${this.intensityLevel.text()} Intensity Themes`);
        },
        exit => exit.remove(),
      );
  }

  setLegendLabelsLine() {
    this.legendLabelsLine
      .selectAll('g')
      .data([{}])
      .join(
        enter => {
          const selection = enter
            .append('g')
            .attr('class', 'legend-labels-line')
            .attr('opacity', 1);

          selection
            .append('line')
            .attr('class', 'link-line')
            .attr('stroke', 'rgba(19, 28, 41, 0.1)')
            .style('stroke-linecap', 'round')
            .attr('stroke-width', this.annotationLineThick)
            .attr('x1', 0)
            .attr('y1', this.legendSectionTitleHeight)
            .attr('x2', this.legendWidth)
            .attr('y2', this.legendSectionTitleHeight);
        },
        update => {
          const selection = update
            .attr('class', 'legend-labels-line')
            .attr('opacity', 1);

          selection
            .select('.link-line')
            .attr('x2', this.legendWidth);
        },
        exit => exit.remove(),
      );
  }

  setLegendNegativeIntensity() {
    const percentMin = Math.round(this.intensityNegativeMin * 100);
    const percentMax = Math.round(this.intensityNegativeMax * 100);

    this.legendNegativeIntensity
      .selectAll('g')
      .data([{}])
      .join(
        enter => {
          const selection = enter
            .append('g')
            .attr('class', 'legend-negative-intensity')
            .attr('opacity', 1);

          selection
            .append('text')
            .attr('class', 'label-title')
            .attr('dx', this.legendPadding)
            .attr('dy', this.legendNegativeTextPos)
            .attr('pointer-events', 'none')
            .attr('font-family', 'Inter, sans-serif')
            .attr('font-weight', 700)
            .attr('font-size', this.fontSize)
            .attr('fill', '#131C29')
            .text('NEGATIVE INTENSITY');

          selection
            .append('rect')
            .attr('class', 'label-bar')
            .attr('x', this.legendPadding)
            .attr('y', this.legendNegativeBarPos)
            .attr('width', this.legendWidth - this.legendPadding * 2)
            .attr('height', this.legendIntensityBarHeight)
            .attr('stroke', '#353535')
            .attr('rx', 1)
            .attr('ry', 1)
            .style('fill', 'url(#gradientNegative)');

          selection
            .append('line')
            .attr('class', 'note-line-start')
            .attr('stroke', 'rgba(19, 28, 41, 0.2)')
            .style('stroke-linecap', 'round')
            .attr('stroke-width', 1)
            .attr('x1', this.legendPadding * 2)
            .attr('y1', this.legendNegativeBarPos + this.legendIntensityBarHeight)
            .attr('x2', this.legendPadding * 2)
            .attr('y2', this.legendNegativeBarPos + this.legendIntensityBarHeight + this.legendNoteLineLength);

          selection
            .append('line')
            .attr('class', 'note-line-end')
            .attr('stroke', 'rgba(19, 28, 41, 0.2)')
            .style('stroke-linecap', 'round')
            .attr('stroke-width', 1)
            .attr('x1', this.legendWidth - this.legendPadding * 2)
            .attr('y1', this.legendNegativeBarPos + this.legendIntensityBarHeight)
            .attr('x2', this.legendWidth - this.legendPadding * 2)
            .attr('y2', this.legendNegativeBarPos + this.legendIntensityBarHeight + this.legendNoteLineLength);

          selection
            .append('text')
            .attr('class', 'label-note-start')
            .attr('dx', this.legendPadding * 2)
            .attr('dy', this.legendNegativeBarPos + this.legendIntensityBarHeight + this.legendNoteLineLength + 10)
            .attr('pointer-events', 'none')
            .attr('font-family', 'Inter, sans-serif')
            .attr('font-weight', 400)
            .attr('font-size', this.fontSize)
            .attr('text-anchor', 'middle')
            .attr('fill', '#131C29')
            .text(`${percentMin}%`);

          selection
            .append('text')
            .attr('class', 'label-note-end')
            .attr('dx', this.legendWidth - this.legendPadding * 2)
            .attr('dy', this.legendNegativeBarPos + this.legendIntensityBarHeight + this.legendNoteLineLength + 10)
            .attr('pointer-events', 'none')
            .attr('font-family', 'Inter, sans-serif')
            .attr('font-weight', 400)
            .attr('font-size', this.fontSize)
            .attr('text-anchor', 'middle')
            .attr('fill', '#131C29')
            .text(`${percentMax}%`);

          selection
            .append('line')
            .attr('class', 'link-line')
            .attr('stroke', 'rgba(19, 28, 41, 0.1)')
            .style('stroke-linecap', 'round')
            .attr('stroke-width', this.annotationLineThick)
            .attr('x1', 0)
            .attr('y1', this.legendSectionTitleHeight + this.legendSectionIntensityHeight)
            .attr('x2', this.legendWidth)
            .attr('y2', this.legendSectionTitleHeight + this.legendSectionIntensityHeight);
        },
        update => {
          const selection = update
            .attr('class', 'legend-negative-intensity')
            .attr('opacity', 1);

          if (!this.hasNegativeRange) {
            selection
              .attr('display', 'none');
          } else {
            selection
              .select('.label-title')
              .attr('dx', this.legendPadding)
              .attr('dy', this.legendNegativeTextPos);

            selection
              .select('.label-bar')
              .attr('x', this.legendPadding)
              .attr('y', this.legendNegativeBarPos)
              .attr('width', this.legendWidth - this.legendPadding * 2)
              .attr('height', this.legendIntensityBarHeight);

            selection
              .select('.note-line-start')
              .attr('x1', this.legendPadding * 2)
              .attr('y1', this.legendNegativeBarPos + this.legendIntensityBarHeight)
              .attr('x2', this.legendPadding * 2)
              .attr('y2', this.legendNegativeBarPos + this.legendIntensityBarHeight + this.legendNoteLineLength);

            selection
              .select('.note-line-end')
              .attr('x1', this.legendWidth - this.legendPadding * 2)
              .attr('y1', this.legendNegativeBarPos + this.legendIntensityBarHeight)
              .attr('x2', this.legendWidth - this.legendPadding * 2)
              .attr('y2', this.legendNegativeBarPos + this.legendIntensityBarHeight + this.legendNoteLineLength);

            selection
              .select('.link-line')
              .attr('stroke-width', this.annotationLineThick)
              .attr('x1', 0)
              .attr('y1', this.legendSectionTitleHeight + this.legendSectionIntensityHeight)
              .attr('x2', this.legendWidth)
              .attr('y2', this.legendSectionTitleHeight + this.legendSectionIntensityHeight)
              .attr('display', 'block');
            if (!this.hasPositiveRange) {
              selection
                .select('.link-line')
                .attr('display', 'none');
            }

            selection
              .select('.label-note-start')
              .attr('dx', this.legendPadding * 2)
              .attr('dy', this.legendNegativeBarPos + this.legendIntensityBarHeight + this.legendNoteLineLength + 10)
              .text(`${percentMin}%`);

            selection
              .select('.label-note-end')
              .attr('dx', this.legendWidth - this.legendPadding * 2)
              .attr('dy', this.legendNegativeBarPos + this.legendIntensityBarHeight + this.legendNoteLineLength + 10)
              .text(`${percentMax}%`);

            selection
              .attr('display', 'block');
          }
        },
        exit => exit.remove(),
      );
  }

  setLegendPositiveIntensity() {
    const percentMin = Math.round(this.intensityPositiveMin * 100);
    const percentMax = Math.round(this.intensityPositiveMax * 100);
    this.legendPositiveIntensity
      .selectAll('g')
      .data([{}])
      .join(
        enter => {
          const selection = enter
            .append('g')
            .attr('class', 'legend-positive-intensity')
            .attr('opacity', 1);

          selection
            .append('text')
            .attr('class', 'label-title')
            .attr('dx', this.legendPadding)
            .attr('dy', this.legendPositiveTextPos)
            .attr('pointer-events', 'none')
            .attr('font-family', 'Inter, sans-serif')
            .attr('font-weight', 700)
            .attr('font-size', this.fontSize)
            .attr('text-anchor', 'start')
            .attr('fill', '#131C29')
            .text('POSITIVE INTENSITY');

          selection
            .append('rect')
            .attr('class', 'label-bar')
            .attr('x', this.legendPadding)
            .attr('y', this.legendPositiveBarPos)
            .attr('width', this.legendWidth - this.legendPadding * 2)
            .attr('height', this.legendIntensityBarHeight)
            .attr('stroke', '#353535')
            .attr('rx', 1)
            .attr('ry', 1)
            .style('fill', 'url(#gradientPositive)');

          selection
            .append('line')
            .attr('class', 'note-line-start')
            .attr('stroke', 'rgba(19, 28, 41, 0.2)')
            .style('stroke-linecap', 'round')
            .attr('stroke-width', 1)
            .attr('x1', this.legendPadding * 2)
            .attr('y1', this.legendPositiveBarPos + this.legendIntensityBarHeight)
            .attr('x2', this.legendPadding * 2)
            .attr('y2', this.legendPositiveBarPos + this.legendIntensityBarHeight + this.legendNoteLineLength);

          selection
            .append('line')
            .attr('class', 'note-line-end')
            .attr('stroke', 'rgba(19, 28, 41, 0.2)')
            .style('stroke-linecap', 'round')
            .attr('stroke-width', 1)
            .attr('x1', this.legendWidth - this.legendPadding * 2)
            .attr('y1', this.legendPositiveBarPos + this.legendIntensityBarHeight)
            .attr('x2', this.legendWidth - this.legendPadding * 2)
            .attr('y2', this.legendPositiveBarPos + this.legendIntensityBarHeight + this.legendNoteLineLength);

          selection
            .append('text')
            .attr('class', 'label-note-start')
            .attr('dx', this.legendPadding * 2)
            .attr('dy', this.legendPositiveBarPos + this.legendIntensityBarHeight + this.legendNoteLineLength + 10)
            .attr('pointer-events', 'none')
            .attr('font-family', 'Inter, sans-serif')
            .attr('font-weight', 400)
            .attr('font-size', this.fontSize)
            .attr('text-anchor', 'middle')
            .attr('fill', '#131C29')
            .text(`${percentMin}%`);

          selection
            .append('text')
            .attr('class', 'label-note-end')
            .attr('dx', this.legendWidth - this.legendPadding * 2)
            .attr('dy', this.legendPositiveBarPos + this.legendIntensityBarHeight + this.legendNoteLineLength + 10)
            .attr('pointer-events', 'none')
            .attr('font-family', 'Inter, sans-serif')
            .attr('font-weight', 400)
            .attr('font-size', this.fontSize)
            .attr('text-anchor', 'middle')
            .attr('fill', '#131C29')
            .text(`${percentMax}%`);
        },
        update => {
          const selection = update
            .attr('class', 'legend-positive-intensity')
            .attr('opacity', 1);

          if (!this.hasPositiveRange) {
            selection
              .attr('display', 'none');
          } else {
            selection
              .select('.label-bar')
              .attr('width', this.legendWidth - this.legendPadding * 2);

            selection
              .select('.link-line')
              .attr('x2', this.legendWidth);

            selection
              .select('.note-line-end')
              .attr('x1', this.legendWidth - this.legendPadding * 2)
              .attr('x2', this.legendWidth - this.legendPadding * 2);

            selection
              .select('.label-note-start')
              .text(`${percentMin}%`);

            selection
              .select('.label-note-end')
              .attr('dx', this.legendWidth - this.legendPadding * 2)
              .text(`${percentMax}%`);

            selection
              .attr('display', 'block');
          }
        },
        exit => exit.remove(),
      );
  }

  setLegendContainer() {
    this.legendContainer
      .attr('height', this.legendSectionTitleHeight + this.legendSectionIntensityHeight * 2)
      .attr('width', this.legendWidth)
      .style('transform', `translateX(${this.width - this.legendWidth - this.wMargin / 2}px) translateY(${this.hMarginCaption * 2 + this.captionHeight}px)`);
  }

  setLinearGradientNegative() {
    const gradientNegative = this.legendContainer
      .append('defs')
      .append('linearGradient')
      .attr('id', 'gradientNegative')
      .attr('x1', '0%')
      .attr('y1', '0%')
      .attr('x2', '100%')
      .attr('y2', '0%');

    gradientNegative.append('stop')
      .attr('offset', '1.89%')
      .style('stop-color', '#FFE8E8');

    gradientNegative.append('stop')
      .attr('offset', '97.64%')
      .style('stop-color', '#E74B3C');

    return gradientNegative;
  }

  setLinearGradientPositive() {
    const gradientPositive = this.legendContainer
      .append('defs')
      .append('linearGradient')
      .attr('id', 'gradientPositive')
      .attr('x1', '0%')
      .attr('y1', '0%')
      .attr('x2', '100%')
      .attr('y2', '0%');

    gradientPositive.append('stop')
      .attr('offset', '1.89%')
      .style('stop-color', '#EBF2EA');

    gradientPositive.append('stop')
      .attr('offset', '97.64%')
      .style('stop-color', '#7DBD74');

    return gradientPositive;
  }

  setThemeStatsBlocks() {
    this.themeStatsBlocks
      .selectAll('g')
      .data(this.data)
      .join(
        enter => {
          const selection = enter
            .append('g')
            .attr('class', d => `theme-stats theme-stats-${d.id}`)
            .attr('transform', d => `translate(${this.themeStatsX(d)}, ${this.themeStatsY(d)})`)
            .attr('opacity', d => {
              return this.showThemeStats(d) ? this.themeStatsBlockOpacity : 0;
            })
            .attr('pointer-events', d => {
              return this.showThemeStats(d) ? null : 'none';
            })
            .attr('cursor', this.annotationMode ? 'grab' : 'default')
            .style('visibility', this.annotationVisibilityFn())
            .call(this.dragThemeStats)
            .on('mouseenter', (event, d) => {
              if (this.annotationMode) {
                this.themeStatsBlocks.select(`.theme-stats-${d.id}`).selectAll('.theme-stats-action-buttons').attr('visibility', 'visible');
              }
            })
            .on('mouseleave', (event, d) => {
              if (this.annotationMode) {
                this.themeStatsBlocks.select(`.theme-stats-${d.id}`).selectAll('.theme-stats-action-buttons').attr('visibility', 'hidden');
              }
            });

          selection
            .append('rect')
            .attr('class', 'annotation-bg')
            .attr('dx', 0)
            .attr('dy', 0)
            .attr('fill', this.annotationBgColor())
            .attr('height', d => this.themeStatsBlockHeightByOptions(this.themeStatsChartOptions(d)))
            .attr('rx', 3)
            .attr('stroke', 'rgba(0, 0, 0, 0.25)')
            .attr('width', this.themeStatsBlockWidth);

          selection
            .append('circle')
            .attr('class', 'theme-stats-action-buttons action-buttons edit-button-circle')
            .attr('cursor', 'pointer')
            .attr('cx', this.themeStatsBlockWidth - 50)
            .attr('cy', 18)
            .attr('fill', this.annotationBgColor())
            .attr('r', 11)
            .attr('stroke', d => this.editButtonColour(d).stroke)
            .attr('visibility', 'hidden')
            .on('mousedown', (_, d) => {
              this.themeStatsAnnotationCallBack(d, 'edit');
            });

          selection
            .append('text')
            .attr('class', 'theme-stats-action-buttons fa edit-button-icon') // Give it the font-awesome class
            .attr('cursor', 'pointer')
            .attr('dominant-baseline', 'central')
            .attr('dx', this.themeStatsBlockWidth - 50)
            .attr('dy', 18)
            .attr('fill', d => this.editButtonColour(d).fill)
            .attr('text-anchor', 'middle')
            .attr('visibility', 'hidden')
            .style('font-family', 'FontAwesome')
            .style('font-size', '0.6em')
            .text('\uf304')
            .on('mousedown', (_, d) => {
              this.themeStatsAnnotationCallBack(d, 'edit');
            });

          selection
            .append('circle')
            .attr('class', 'theme-stats-action-buttons action-buttons delete-button-circle')
            .attr('cursor', 'pointer')
            .attr('cx', this.themeStatsBlockWidth - 20)
            .attr('cy', 18)
            .attr('fill', this.annotationBgColor())
            .attr('r', 11)
            .attr('stroke', this.deleteButtonColour().stroke)
            .attr('visibility', 'hidden')
            .on('mousedown', (_, d) => {
              this.themeStatsAnnotationCallBack(d, 'remove');
            });

          selection
            .append('text')
            .attr('class', 'theme-stats-action-buttons fa delete-button-icon') // Give it the font-awesome class
            .attr('cursor', 'pointer')
            .attr('dominant-baseline', 'central')
            .attr('dx', this.themeStatsBlockWidth - 20)
            .attr('dy', 18)
            .attr('fill', this.deleteButtonColour().fill)
            .attr('text-anchor', 'middle')
            .attr('visibility', 'hidden')
            .style('font-family', 'FontAwesome')
            .style('font-size', '0.6em')
            .text('\uf1f8')
            .on('mousedown', (_, d) => {
              this.themeStatsAnnotationCallBack(d, 'remove');
            });

          selection
            .append('text')
            .attr('class', 'annotation-text')
            .attr('dx', this.themeStatsPadding)
            .attr('dy', d => this.themeStatsDy(this.themeStatsChartOptions(d), 0))
            .attr('fill', this.annotationTextColor)
            .attr('opacity', d => this.themeStatsOpacity(this.themeStatsChartOptions(d), 0))
            .attr('pointer-events', 'none')
            .attr('text-anchor', 'left')
            .attr('font-family', 'Inter, sans-serif')
            .attr('font-size', 12)
            .attr('font-weight', 700)
            .attr('line-height', 18)
            .text(d => this.truncateText(d.topicLabel));

          selection
            .append('text')
            .datum(d => d)
            .attr('class', 'annotation-intensity')
            .attr('dx', this.themeStatsPadding)
            .attr('dy', d => this.themeStatsDy(this.themeStatsChartOptions(d), 1))
            .attr('opacity', d => this.themeStatsOpacity(this.themeStatsChartOptions(d), 1))
            .attr('pointer-events', 'none')
            .attr('text-anchor', 'left')
            .attr('font-family', 'Inter, sans-serif')
            .attr('font-size', 11)
            .attr('font-weight', 700)
            .attr('fill', this.annotationTextColor)
            .text('Intensity: ')
            .append('tspan')
            .attr('font-size', 11)
            .attr('font-weight', 400)
            .text(d => `${this.intensityLevel.name} - ${Math.round(this.intensityPercent(d) * 100)}%`);

          selection
            .append('text')
            .datum(d => d)
            .attr('class', 'annotation-volume')
            .attr('dx', this.themeStatsPadding)
            .attr('dy', d => this.themeStatsDy(this.themeStatsChartOptions(d), 2))
            .attr('opacity', d => this.themeStatsOpacity(this.themeStatsChartOptions(d), 2))
            .attr('pointer-events', 'none')
            .attr('text-anchor', 'left')
            .attr('font-family', 'Inter, sans-serif')
            .attr('font-size', 11)
            .attr('font-weight', 700)
            .attr('fill', this.annotationTextColor)
            .text('Volume: ')
            .append('tspan')
            .attr('font-size', 11)
            .attr('font-weight', 400)
            .text(d => `${d.numOfDocuments} (${Math.round(d.numOfDocuments / this.totalVolume * 100)}% of dataset)`);

          selection
            .append('rect')
            .datum(d => d)
            .attr('class', 'annotation-score-bg')
            .attr('fill', '#FFF')
            .attr('height', '16')
            .attr('opacity', d => this.themeStatsOpacity(this.themeStatsChartOptions(d), 3))
            .attr('pointer-events', 'none')
            .attr('stroke', d => this.scoreColour(d).base)
            .attr('text-anchor', 'left')
            .attr('width', '26')
            .attr('x', this.themeStatsPadding)
            .attr('y', d => this.themeStatsDy(this.themeStatsChartOptions(d), 3) - 10);

          selection
            .append('text')
            .datum(d => d)
            .attr('class', 'annotation-score')
            .attr('dx', this.themeStatsPadding + 6)
            .attr('dy', d => this.themeStatsDy(this.themeStatsChartOptions(d), 3) + this.themeStatsTextGap - 10)
            .attr('fill', this.annotationTextColor)
            .attr('font-family', 'Inter, sans-serif')
            .attr('font-size', 8)
            .attr('font-weight', 700)
            .attr('opacity', d => this.themeStatsOpacity(this.themeStatsChartOptions(d), 3))
            .attr('pointer-events', 'none')
            .attr('text-anchor', 'left')
            .text(d => this.themeStatsTextScore(Math.round(this.yVal(d))));

          selection
            .append('text')
            .datum(d => d)
            .attr('class', 'annotation-score-text')
            .attr('dx', this.themeStatsPadding + 32)
            .attr('dy', d => this.themeStatsDy(this.themeStatsChartOptions(d), 3) + 2.5)
            .attr('fill', d => this.scoreColour(d).scoreText)
            .attr('font-family', 'Inter, sans-serif')
            .attr('font-size', '10')
            .attr('font-weight', '700')
            .attr('opacity', d => this.themeStatsOpacity(this.themeStatsChartOptions(d), 3))
            .attr('pointer-events', 'none')
            .attr('text-anchor', 'left')
            .style('text-transform', 'uppercase')
            .text(d => scoreRate.getAdoreScoreType(d.polarity));

          selection
            .append('rect')
            .datum(d => d)
            .attr('class', 'annotation-score-bg-bottom')
            .attr('fill', d => this.scoreColour(d).darker)
            .attr('height', '3')
            .attr('opacity', d => this.themeStatsOpacity(this.themeStatsChartOptions(d), 3))
            .attr('pointer-events', 'none')
            .attr('width', '26')
            .attr('x', this.themeStatsPadding)
            .attr('y', d => this.themeStatsDy(this.themeStatsChartOptions(d), 3) + 2.5);
        },
        update => {
          const selection = update
            .attr('class', d => `theme-stats theme-stats-${d.id}`)
            .attr('cursor', this.annotationMode ? 'grab' : 'default')
            .attr('opacity', d => {
              return this.showThemeStats(d) ? this.themeStatsBlockOpacity : 0;
            })
            .attr('pointer-events', d => {
              return this.showThemeStats(d) ? null : 'none';
            })
            .attr('transform', d => `translate(${this.themeStatsX(d)}, ${this.themeStatsY(d)})`)
            .style('visibility', this.annotationVisibilityFn())
            .on('mouseenter', (event, d) => {
              if (this.annotationMode) {
                this.themeStatsBlocks.select(`.theme-stats-${d.id}`).selectAll('.theme-stats-action-buttons').attr('visibility', 'visible');
              }
            })
            .on('mouseleave', (event, d) => {
              if (this.annotationMode) {
                this.themeStatsBlocks.select(`.theme-stats-${d.id}`).selectAll('.theme-stats-action-buttons').attr('visibility', 'hidden');
              }
            })
            .call(u => u.transition(this.transition));

          selection.select('.annotation-bg')
            .attr('fill', this.annotationBgColor())
            .attr('height', d => this.themeStatsBlockHeightByOptions(this.themeStatsChartOptions(d)))
            .attr('width', this.themeStatsBlockWidth);
          selection.select('.annotation-text')
            .attr('dy', d => this.themeStatsDy(this.themeStatsChartOptions(d), 0))
            .attr('fill', this.annotationTextColor())
            .attr('opacity', d => this.themeStatsOpacity(this.themeStatsChartOptions(d), 0))
            .text(d => this.truncateText(d.topicLabel));
          selection.select('.annotation-intensity')
            .attr('dy', d => this.themeStatsDy(this.themeStatsChartOptions(d), 1))
            .attr('fill', this.annotationTextColor())
            .attr('opacity', d => this.themeStatsOpacity(this.themeStatsChartOptions(d), 1))
            .text(d => `${this.intensityLevel.name} - ${Math.round(this.intensityPercent(d) * 100)}%`);

          selection.select('.annotation-volume')
            .attr('dy', d => this.themeStatsDy(this.themeStatsChartOptions(d), 2))
            .attr('fill', this.annotationTextColor())
            .attr('opacity', d => this.themeStatsOpacity(this.themeStatsChartOptions(d), 2))
            .select('tspan')
            .text(d => `${d.numOfDocuments} (${Math.round(d.numOfDocuments / this.totalVolume * 100)}% of dataset)`);

          // Score
          selection.select('.annotation-score-bg')
            .attr('opacity', d => this.themeStatsOpacity(this.themeStatsChartOptions(d), 3))
            .attr('stroke', d => this.scoreColour(d).base)
            .attr('y', d => this.themeStatsDy(this.themeStatsChartOptions(d), 3) - 10);
          selection.select('.annotation-score')
            .attr('dy', d => this.themeStatsDy(this.themeStatsChartOptions(d), 3) + this.themeStatsTextGap - 10)
            .attr('opacity', d => this.themeStatsOpacity(this.themeStatsChartOptions(d), 3));
          selection.select('.annotation-score-text')
            .attr('dy', d => this.themeStatsDy(this.themeStatsChartOptions(d), 3) + 2.5)
            .attr('opacity', d => this.themeStatsOpacity(this.themeStatsChartOptions(d), 3))
            .attr('fill', d => this.scoreColour(d).scoreText)
            .text(d => scoreRate.getAdoreScoreType(d.polarity));
          selection.select('.annotation-score-bg-bottom')
            .attr('y', d => this.themeStatsDy(this.themeStatsChartOptions(d), 3) + 2.5)
            .attr('opacity', d => this.themeStatsOpacity(this.themeStatsChartOptions(d), 3))
            .attr('fill', d => this.scoreColour(d).darker);

          selection.selectAll('.action-buttons').attr('fill', this.annotationBgColor());
          selection.select('.delete-button-circle').attr('stroke', this.deleteButtonColour().stroke);
          selection.select('.delete-button-icon').attr('fill', this.deleteButtonColour().fill);
          selection.select('.edit-button-circle').attr('stroke', this.editButtonColour().stroke);
          selection.select('.edit-button-icon').attr('fill', this.editButtonColour().fill);
        },
        exit => exit.remove(),
      );
  }

  setThemeStatsLines() {
    this.themeStatsLines
      .selectAll('g')
      .data(this.data)
      .join(
        enter => {
          const selection = enter
            .append('g')
            .attr('class', d => `theme-stats-line theme-stats-line-${d.id}`)
            .attr('opacity', d => {
              return this.showThemeStats(d) && this.showConnectorLines ? this.themeStatsLineOpacity : 0;
            })
            .attr('pointer-events', 'none')
            .style('visibility', this.annotationVisibilityFn());

          selection
            .append('line')
            .attr('class', d => `link-line link-line-${d.id}`)
            .attr('fill', 'none')
            .attr('stroke', 'black')
            .style('stroke-linecap', 'round')
            .attr('stroke-width', 1)
            .attr('x1', d => {
              const startX = this.xScale(this.xVal(d)); // midpoint circle X
              const startY = this.yScale(this.yVal(d)); // midpoint circle Y
              const endX = this.themeStatsX(d) + (this.themeStatsBlockWidth / 2);
              const endY = this.themeStatsY(d) + (this.themeStatsBlockHeight / 2);
              const angle = Math.atan2(Math.abs(endY - startY), Math.abs(endX - startX));
              if ((endX - startX) >= 0) return startX + Math.cos(angle) * this.zScaler(d);
              return startX - Math.cos(angle) * this.zScaler(d);
            })
            .attr('y1', d => {
              const startX = this.xScale(this.xVal(d)); // midpoint circle X
              const startY = this.yScale(this.yVal(d)); // midpoint circle Y
              const endX = this.themeStatsX(d) + (this.themeStatsBlockWidth / 2);
              const endY = this.themeStatsY(d) + (this.themeStatsBlockHeight / 2);
              const angle = Math.atan2(Math.abs(endY - startY), Math.abs(endX - startX));
              if (endY - startY >= 0) return startY + Math.sin(angle) * this.zScaler(d);
              return startY - Math.sin(angle) * this.zScaler(d);
            })
            .attr('x2', d => {
              return this.themeStatsX(d) + (this.themeStatsBlockWidth / 2);
            })
            .attr('y2', d => {
              return this.themeStatsY(d) + (this.themeStatsBlockHeight / 2);
            });
        },
        update => {
          const selection = update
            .attr('class', d => `theme-stats-line theme-stats-line-${d.id}`)
            .attr('opacity', d => {
              return this.showThemeStats(d) && this.showConnectorLines ? this.themeStatsLineOpacity : 0;
            })
            .attr('pointer-events', 'none')
            .style('visibility', this.annotationVisibilityFn())
            .call(u => u.transition(this.transition));

          selection
            .select('.link-line')
            .attr('class', d => `link-line link-line-${d.id}`)
            .attr('x1', d => {
              const startX = this.xScale(this.xVal(d)); // midpoint circle X
              const startY = this.yScale(this.yVal(d)); // midpoint circle Y
              const endX = this.themeStatsX(d) + (this.themeStatsBlockWidth / 2);
              const endY = this.themeStatsY(d) + (this.themeStatsBlockHeight / 2);
              const angle = Math.atan2(Math.abs(endY - startY), Math.abs(endX - startX));
              if ((endX - startX) >= 0) return startX + Math.cos(angle) * this.zScaler(d);
              return startX - Math.cos(angle) * this.zScaler(d);
            })
            .attr('y1', d => {
              const startX = this.xScale(this.xVal(d)); // midpoint circle X
              const startY = this.yScale(this.yVal(d)); // midpoint circle Y
              const endX = this.themeStatsX(d) + (this.themeStatsBlockWidth / 2);
              const endY = this.themeStatsY(d) + (this.themeStatsBlockHeight / 2);
              const angle = Math.atan2(Math.abs(endY - startY), Math.abs(endX - startX));
              if (endY - startY >= 0) return startY + Math.sin(angle) * this.zScaler(d);
              return startY - Math.sin(angle) * this.zScaler(d);
            })
            .attr('x2', d => {
              return this.themeStatsX(d) + (this.themeStatsBlockWidth / 2);
            })
            .attr('y2', d => {
              return this.themeStatsY(d) + (this.themeStatsBlockHeight / 2);
            });
        },
        exit => exit.remove(),
      );
  }

  setSymbols() {
    this.symbols
      .selectAll('g')
      .data(this.data)
      .join(
        enter => {
          const selection = enter
            .append('g')
            .attr('class', d => `symbol symbol-${d.id}`)
            .attr('pointer-events', 'none')
            .attr('opacity', 0)
            .attr('transform', d => `translate(${this.symbolX(d)}, ${this.symbolY(d)})`);

          selection
            .append('line')
            .attr('class', 'symbol-ver-line')
            .attr('x1', 0)
            .attr('y1', d => {
              return 0 - this.symbolSize(d);
            })
            .attr('x2', 0)
            .attr('y2', d => this.symbolSize(d))
            .style('stroke', '#FFF')
            .style('stroke-linecap', 'round')
            .style('stroke-width', d => this.symbolStroke(d))
            .style('fill', 'none');

          selection
            .append('line')
            .attr('class', 'symbol-hor-line')
            .attr('x1', d => {
              return 0 - this.symbolSize(d);
            })
            .attr('y1', 0)
            .attr('x2', d => this.symbolSize(d))
            .attr('y2', 0)
            .style('stroke', '#FFF')
            .style('stroke-linecap', 'round')
            .style('stroke-width', d => this.symbolStroke(d))
            .style('fill', 'none');
        },
        update => {
          const selection = update
            .attr('class', d => `symbol symbol-${d.id}`)
            .attr('pointer-events', 'none')
            .attr('opacity', 0)
            .attr('transform', d => `translate(${this.symbolX(d)}, ${this.symbolY(d)})`)
            .call(u => u.transition(this.transition));

          selection
            .select('.symbol-ver-line')
            .attr('y1', d => {
              return 0 - this.symbolSize(d);
            })
            .attr('y2', d => this.symbolSize(d))
            .style('stroke-width', d => this.symbolStroke(d));

          selection
            .select('.symbol-hor-line')
            .attr('x1', d => {
              return 0 - this.symbolSize(d);
            })
            .attr('x2', d => this.symbolSize(d))
            .style('stroke-width', d => this.symbolStroke(d));
        },
        exit => exit.remove(),
      );
  }

  symbolSize(d) {
    return Math.abs(this.zScaler(d) * this.symbolScale);
  }

  symbolStroke(d) {
    return Math.abs(this.zScaler(d) * this.symbolThick);
  }

  symbolX(d) {
    return this.xScale(this.xVal(d));
  }

  symbolY(d) {
    return this.yScale(this.yVal(d));
  }

  truncateText(topicLabel) {
    if (topicLabel.length > 20) {
      return `${topicLabel.substring(0, 20)}...`;
    }
    return topicLabel;
  }

  setAxisLegends() {
    this.yLegend
      .attr('transform', `translate(${this.wMargin / 2}, ${this.hMargin + this.innerHeight / 2}) rotate(-90)`);

    this.yLegend
      .selectAll('text')
      .data([this.yAxisLabel])
      .join(
        enter => enter
          .append('text')
          .text(this.yAxisLabel)
          .style('fill', '#131C29')
          .style('font-family', 'Inter, sans-serif')
          .style('font-size', this.fontSize)
          .style('font-weight', 'bold')
          .attr('dx', function calcPos() {
            return -(this.getComputedTextLength() / 2);
          })
          .style('text-transform', 'uppercase')
          .style('visibility', this.labelVisibilityFn(this.showAxisLabel)),
        update => update
          .text(this.yAxisLabel)
          .attr('dx', function calcPos() {
            return -(this.getComputedTextLength() / 2);
          })
          .style('visibility', this.labelVisibilityFn(this.showAxisLabel)),
      );

    this.xLegend
      .attr(
        'transform',
        `translate(${this.wMargin + this.innerWidth / 2}, ${this.hMargin + this.innerHeight + this.hMargin / 1.2})`,
      );

    this.xLegend
      .selectAll('text')
      .data([this.xAxisLabel])
      .join(
        enter => enter
          .append('text')
          .text(this.xAxisLabel)
          .style('fill', '#131C29')
          .style('font-family', 'Inter, sans-serif')
          .style('font-size', this.fontSize)
          .style('font-weight', 'bold')
          .attr('dx', function calcPos() {
            return -(this.getComputedTextLength() / 2);
          })
          .style('text-transform', 'uppercase')
          .style('visibility', this.labelVisibilityFn(this.showAxisLabel)),
        update => update
          .text(this.xAxisLabel)
          .attr('dx', function calcPos() {
            return -(this.getComputedTextLength() / 2);
          })
          .style('visibility', this.labelVisibilityFn(this.showAxisLabel)),
      );
  }

  setXAxis() {
    this.xAxis
      .transition(this.transition)
      .attr(
        'transform',
        `translate(${this.wMargin}, ${this.hMargin + this.innerHeight})`,
      )
      .call(axisBottom(this.xScale).tickSize(-this.innerHeight))
      .selectAll('.tick line')
      .style('display', (d, i) => (i === 0 ? 'block' : 'none'));

    this.xAxis
      .selectAll('.tick text')
      .attr('font-family', 'Inter, sans-serif')
      .style('display', (d, i) => (i === 0 || i === this.xAxis.selectAll('.tick text').size() - 1 ? 'block' : 'none'));

    this.xAxis.select('.domain').style('display', 'none');
  }

  setYAxis() {
    this.yAxis.selectAll('*').remove();
    this.yAxis
      .transition(this.transition)
      .attr('transform', `translate(${this.wMargin}, ${this.hMargin})`)
      .call(axisLeft(this.yScale).tickSize(-this.innerWidth));

    const lineCount = this.yAxis.selectAll('.tick line').size() - 1;

    this.yAxis.selectAll('.tick line')
      .style('stroke-dasharray', (_, i) => (i === lineCount ? 'none' : 2))
      .style('opacity', (_, i) => (i === lineCount ? 1 : 0.2))
      .style('stroke', '#131C29');

    this.yAxis
      .selectAll('.tick text')
      .attr('font-family', 'Inter, sans-serif')
      .style('display', (d, i) => {
        return (i === 0 || i === lineCount / 2 || i === lineCount)
          ? 'block'
          : 'none';
      });

    this.yAxis.select('.domain').style('display', 'none');
  }

  updateChart() {
    // Intensity Caption & Annotation Caption
    this.setCaptionContainer();
    this.setCaptionLabels();
    this.setCaptionAnnotationSvg();
    this.setCaptionAnnotationLabels();

    // Intensity Legend
    this.setLegendContainer();
    this.setLegendBackground();
    this.setLegendLabels();
    this.setLegendLabelsLine();
    this.setLegendNegativeIntensity();
    this.setLegendPositiveIntensity();

    // Intensity Chart
    this.setChartContainer();
    this.setChartBackground();
    this.setClip();
    this.setBubbles();
    this.setBubbleLabels();
    this.setThemeStatsLines();
    this.setThemeStatsBlocks();
    this.setCommentLines();
    this.setCommentBlocks();
    this.setAnnotationBlocks();

    this.setSymbols();
    this.setXAxis();
    this.setYAxis();
    this.setAxisLegends();
  }

  xVal(d) {
    return at(d, this.xPath);
  }

  yVal(d) {
    const flipped = [ThemesChartPath.SURPRISE.titleCase(), ThemesChartPath.ANGER.titleCase()].includes(this.yName) ? -1 : 1;
    return at(d, this.yPath) * flipped * 100;
  }

  zScaler() {
    return this.bubbleSize;
  }

  /**
   * COMMENT ANNOTATIONS
   */

  labelX(d) {
    if (d && d.labelValX) {
      return d.labelValX;
    }
    return 0;
  }

  labelY(d) {
    if (d && d.labelValY) {
      return d.labelValY;
    }
    return 0;
  }

  commentX(d) {
    const customTheme = this.getCustomTheme(d);
    if (customTheme && customTheme.commentPositionX) {
      return this.xScale(customTheme.commentPositionX);
    }
    return this.xScale(this.xVal(d)) - this.commentBlockWidth / 2;
  }

  commentY(d) {
    const customTheme = this.getCustomTheme(d);
    if (customTheme && customTheme.commentPositionY) return this.yScale(customTheme.commentPositionY);

    const newCircleY = this.yScale(this.yVal(d));
    const pos = newCircleY + Number(this.bubbleSize) + this.arrowHeight;

    if (pos + this.getCommentBlockHeight(d) < this.chartHeight) return pos;

    return newCircleY - Number(this.bubbleSize) - this.getCommentBlockHeight(d) - this.arrowHeight;
  }

  get dragComment() {
    return drag()
      .on('start', this.dragCommentStarted.bind(this))
      .on('drag', this.draggingComment.bind(this))
      .on('end', this.dragCommentEnded.bind(this));
  }

  dragCommentStarted(event, d) {
    if (this.annotationMode && this.showComment(d)) {
      this.commentBlocks.selectAll(`.comment-${d.id}`)
        .attr('cursor', 'grabbing');
      // pointer position
      let translateVal =
        this.commentBlocks.selectAll(`.comment-${d.id}`).attr('transform');
      translateVal = translateVal.substring(translateVal.indexOf('(') + 1, translateVal.indexOf(')')).split(',');
      this.pointerX = translateVal[0] - event.x;
      this.pointerY = translateVal[1] - event.y;
    }
  }

  draggingComment(event, d) {
    if (this.annotationMode && this.showComment(d)) {
      this.commentBlocks.selectAll(`.comment-${d.id}`)
        .attr('transform', `translate(${event.x + this.pointerX}, ${event.y + this.pointerY})`);

      this.commentLines.select(`.link-line-${d.id}`)
        .attr('x1', () => {
          const startX = this.xScale(this.xVal(d)); // midpoint circle X
          const startY = this.yScale(this.yVal(d)); // midpoint circle Y
          const endX = event.x + this.pointerX + this.commentBlockWidth / 2;
          const endY = event.y + this.pointerY + this.getCommentBlockHeight(d) / 2;
          const angle = Math.atan2(Math.abs(endY - startY), Math.abs(endX - startX));
          if ((endX - startX) >= 0) return startX + Math.cos(angle) * this.zScaler(d);
          return startX - Math.cos(angle) * this.zScaler(d);
        })
        .attr('y1', () => {
          const startX = this.xScale(this.xVal(d)); // midpoint circle X
          const startY = this.yScale(this.yVal(d)); // midpoint circle Y
          const endX = event.x + this.pointerX + this.commentBlockWidth / 2;
          const endY = event.y + this.pointerY + this.getCommentBlockHeight(d) / 2;
          const angle = Math.atan2(Math.abs(endY - startY), Math.abs(endX - startX));
          if (endY - startY >= 0) return startY + Math.sin(angle) * this.zScaler(d);
          return startY - Math.sin(angle) * this.zScaler(d);
        })
        .attr('x2', event.x + this.pointerX + this.commentBlockWidth / 2)
        .attr('y2', event.y + this.pointerY);
    }
  }

  dragCommentEnded(event, d) {
    if (this.annotationMode && this.showComment(d)) {
      this.commentBlocks.selectAll('.comment')
        .attr('cursor', 'grab');
      // invert to get value - use in new zoom in/out
      d.commentPositionX = this.xScale.invert(event.x + this.pointerX);
      d.commentPositionY = this.yScale.invert(event.y + this.pointerY);
      this.commentAnnotationCallBack(d, 'movePosition');
      this.commentBlocks.select(`.comment-${d.id}`).selectAll('.comment-action-buttons').attr('visibility', 'hidden'); // work around to fix sometimes action edit and delete appear after dragging
    }
  }

  dragLabelStarted(event, d) {
    if (this.annotationMode) {
      this.bubbleLabels.selectAll(`.bubble-label-${d.id}`)
        .attr('cursor', 'grabbing');
      // pointer position
      let translateVal =
        this.bubbleLabels.selectAll(`.bubble-label-${d.id}`).attr('transform');
      translateVal = translateVal.substring(translateVal.indexOf('(') + 1, translateVal.indexOf(')')).split(',');
      this.pointerX = translateVal[0] - event.x;
      this.pointerY = translateVal[1] - event.y;
    }
  }

  draggingLabel(event, d) {
    if (this.annotationMode) {
      this.bubbleLabels.selectAll(`.bubble-label-${d.id}`)
        .attr('transform', `translate(${event.x + this.pointerX}, ${event.y + this.pointerY})`);
    }
  }

  dragLabelEnded(event, d) {
    if (this.annotationMode) {
      this.bubbleLabels.selectAll(`.bubble-label-${d.id}`)
        .attr('cursor', 'grab');
      // invert to get value - use in new zoom in/out
      d.labelValX = this.xScale.invert(event.x + this.pointerX);
      d.labelValY = this.yScale.invert(event.y + this.pointerY);
    }
  }

  get dragLabel() {
    return drag()
      .on('start', this.dragLabelStarted.bind(this))
      .on('drag', this.draggingLabel.bind(this))
      .on('end', this.dragLabelEnded.bind(this));
  }

  getCustomTheme(theme) {
    return this.customThemeData.find(t => t.id === theme.id);
  }

  getCommentDescriptionHeight(d) {
    const additionalPadding = this.showCommentTitle(d) ? 10 : 0;
    const text = document.getElementById(`annotation-description-text-${d.id}`);
    return (text && text.offsetHeight < this.commentBlockDescHeightDefault) ?
      text.offsetHeight + additionalPadding : this.commentBlockDescHeightDefault;
  }

  getCommentBlockHeight(d) {
    if (this.showCommentTitle(d) && this.showCommentDesc(d)) {
      return this.getCommentDescriptionHeight(d) + this.commentBlockTitleHeightDefault;
    }
    if (this.showCommentTitle(d)) {
      return this.commentBlockTitleHeightDefault;
    }
    return this.getCommentDescriptionHeight(d) + this.commentBlockPading * 2;
  }

  showComment(d) {
    return this.showAnnotations && this.isCustom(d) && (this.showCommentTitle(d) || this.showCommentDesc(d));
  }

  showCommentDesc(theme) {
    const commentAnnotation = this.customThemeData.find(t => t.id === theme.id);
    if (commentAnnotation) return commentAnnotation.showDescription;
    return false;
  }

  showCommentTitle(theme) {
    const commentAnnotation = this.customThemeData.find(t => t.id === theme.id);
    if (commentAnnotation) return commentAnnotation.showTitle;
    return false;
  }

  setCommentBlocks() {
    this.commentBlocks
      .selectAll('g')
      .data(this.data)
      .join(
        enter => {
          const selection = enter
            .append('g')
            .attr('class', d => `comment comment-${d.id}`)
            .attr('transform', d => `translate(${this.commentX(d)}, ${this.commentY(d)})`)
            .attr('opacity', d => {
              return this.showComment(d) ? 1 : 0;
            })
            .attr('pointer-events', d => {
              return this.showComment(d) ? null : 'none';
            })
            .attr('cursor', this.annotationMode ? 'grab' : 'default')
            .call(this.dragComment)
            .on('mouseenter', (event, d) => {
              if (this.annotationMode) {
                this.commentBlocks.select(`.comment-${d.id}`).selectAll('.comment-action-buttons').attr('visibility', 'visible');
              }
            })
            .on('mouseleave', (event, d) => {
              if (this.annotationMode) {
                this.commentBlocks.select(`.comment-${d.id}`).selectAll('.comment-action-buttons').attr('visibility', 'hidden');
              }
            });

          selection
            .append('rect')
            .attr('class', 'comment-bg')
            .attr('dx', 0)
            .attr('dy', 0)
            .attr('fill', this.annotationBgColor())
            .attr('height', 100)
            .attr('rx', 3)
            .attr('stroke', 'rgba(0, 0, 0, 0.25)')
            .attr('width', this.commentBlockWidth);

          selection
            .append('text')
            .attr('class', 'annotation-title')
            .attr('dx', this.commentBlockPading)
            .attr('dy', 25)
            .attr('fill', this.annotationTextColor)
            .attr('opacity', d => (this.showCommentTitle(d) ? 1 : 0))
            .attr('pointer-events', 'none')
            .attr('text-anchor', 'left')
            .attr('font-family', 'Inter, sans-serif')
            .attr('font-size', 12)
            .attr('font-weight', 700)
            .attr('line-height', 18)
            .text(d => {
              const comment = this.getCustomTheme(d);
              if (comment && comment.title) return this.truncateText(comment.title);
              return '';
            });

          selection
            .append('foreignObject')
            .attr('class', 'annotation-description')
            .attr('opacity', d => (this.showCommentDesc(d) ? 1 : 0))
            .attr('x', this.commentBlockPading)
            .attr('y', d => {
              if (this.showCommentTitle(d)) return 32;
              return this.commentBlockPading;
            })
            .attr('width', this.commentBlockWidth - this.commentBlockPading * 1.8)
            .attr('height', this.commentBlockDescHeightDefault)
            .append('xhtml:div')
            .attr('font-family', 'Inter, sans-serif')
            .attr('font-size', 10)
            .attr('line-height', 16)
            .attr('text-rendering', 'optimizeLegibility')
            .attr('fill', '#000')
            .attr('color', this.annotationTextColor())
            .html(d => {
              const comment = this.getCustomTheme(d);
              if (comment) {
                return (`<div id='annotation-description-text-${d.id}'>${comment.description}</div>`);
              }
              return '';
            });

          selection
            .append('circle')
            .attr('class', 'comment-action-buttons edit-button-circle')
            .attr('cursor', 'pointer')
            .attr('cx', this.commentBlockWidth - 50)
            .attr('cy', 18)
            .attr('fill', 'transparent')
            .attr('r', 11)
            .attr('stroke', this.editButtonColour().stroke)
            .attr('visibility', 'hidden')
            .on('mousedown', (_, d) => {
              this.commentAnnotationCallBack(d, 'edit');
            });

          selection
            .append('text')
            .attr('class', 'comment-action-buttons fa edit-button-icon')
            .attr('cursor', 'pointer')
            .attr('dominant-baseline', 'central')
            .attr('dx', this.commentBlockWidth - 50)
            .attr('dy', 18)
            .attr('fill', this.editButtonColour().fill)
            .attr('text-anchor', 'middle')
            .attr('visibility', 'hidden')
            .style('font-family', 'FontAwesome')
            .style('font-size', '0.6em')
            .text('\uf304')
            .on('mousedown', (_, d) => {
              this.commentAnnotationCallBack(d, 'edit');
            });

          selection
            .append('circle')
            .attr('class', 'comment-action-buttons delete-button-circle')
            .attr('cursor', 'pointer')
            .attr('cx', this.commentBlockWidth - 20)
            .attr('cy', 18)
            .attr('fill', 'transparent')
            .attr('r', 11)
            .attr('stroke', this.deleteButtonColour().stroke)
            .attr('visibility', 'hidden')
            .on('mousedown', (_, d) => {
              this.commentAnnotationCallBack(d, 'remove');
            });

          selection
            .append('text')
            .attr('class', 'comment-action-buttons fa delete-button-icon')
            .attr('cursor', 'pointer')
            .attr('dominant-baseline', 'central')
            .attr('dx', this.commentBlockWidth - 20)
            .attr('dy', 18)
            .attr('fill', this.deleteButtonColour().fill)
            .attr('text-anchor', 'middle')
            .attr('visibility', 'hidden')
            .style('font-family', 'FontAwesome')
            .style('font-size', '0.6em')
            .text('\uf1f8')
            .on('mousedown', (_, d) => {
              this.commentAnnotationCallBack(d, 'remove');
            });
        },
        update => {
          const selection = update
            .attr('class', d => `comment comment-${d.id}`)
            .attr('cursor', this.annotationMode ? 'grab' : 'default')
            .attr('opacity', d => {
              return this.showComment(d) ? 1 : 0;
            })
            .attr('pointer-events', d => {
              return this.showComment(d) ? null : 'none';
            })
            .attr('transform', d => `translate(${this.commentX(d)}, ${this.commentY(d)})`)
            .on('mouseenter', (event, d) => {
              if (this.annotationMode) {
                this.commentBlocks.select(`.comment-${d.id}`).selectAll('.comment-action-buttons').attr('visibility', 'visible');
              }
            })
            .on('mouseleave', (event, d) => {
              if (this.annotationMode) {
                this.commentBlocks.select(`.comment-${d.id}`).selectAll('.comment-action-buttons').attr('visibility', 'hidden');
              }
            })
            .call(u => u.transition(this.transition));

          selection.select('.annotation-title')
            .attr('fill', this.annotationTextColor())
            .attr('opacity', d => (this.showCommentTitle(d) ? 1 : 0))
            .text(d => {
              const comment = this.getCustomTheme(d);
              if (comment && comment.title) return this.truncateText(comment.title);
              return '';
            });

          selection
            .select('.annotation-description')
            .attr('opacity', d => (this.showCommentDesc(d) ? 1 : 0))
            .attr('y', d => {
              if (this.showCommentTitle(d)) return 32;
              return this.commentBlockPading;
            })
            .attr('font-family', 'Inter, sans-serif')
            .attr('font-size', 10)
            .attr('line-height', 16)
            .attr('color', this.annotationTextColor())
            .html(d => {
              const comment = this.getCustomTheme(d);
              if (comment) {
                return (`<div id='annotation-description-text-${d.id}'>${comment.description}</div>`);
              }
              return '';
            })
            .attr('height', d => this.getCommentDescriptionHeight(d));

          selection.select('.comment-bg')
            .attr('fill', this.annotationBgColor())
            .attr('height', d => this.getCommentBlockHeight(d))
            .attr('stroke', 'rgba(0, 0, 0, 0.25)')
            .attr('width', this.commentBlockWidth);

          selection.select('.delete-button-circle').attr('stroke', this.deleteButtonColour().stroke);
          selection.select('.delete-button-icon').attr('fill', this.deleteButtonColour().fill);
          selection.select('.edit-button-circle').attr('stroke', this.editButtonColour().stroke);
          selection.select('.edit-button-icon').attr('fill', this.editButtonColour().fill);
        },
        exit => exit.remove(),
      );
  }

  setCommentLines() {
    this.commentLines
      .selectAll('g')
      .data(this.data)
      .join(
        enter => {
          const selection = enter
            .append('g')
            .attr('class', d => `comment-lines comment-line-${d.id}`)
            .attr('opacity', d => {
              return this.showComment(d) && this.showConnectorLines ? 1 : 0;
            })
            .attr('pointer-events', 'none');

          selection
            .append('line')
            .attr('class', d => `link-line link-line-${d.id}`)
            .attr('fill', 'none')
            .attr('stroke', 'black')
            .style('stroke-linecap', 'round')
            .attr('stroke-width', 1)
            .attr('x1', d => {
              const startX = this.xScale(this.xVal(d)); // midpoint circle X
              const startY = this.yScale(this.yVal(d)); // midpoint circle Y
              const endX = this.commentX(d) + this.commentBlockWidth / 2;
              const endY = this.commentY(d) + this.getCommentBlockHeight(d) / 2;
              const angle = Math.atan2(Math.abs(endY - startY), Math.abs(endX - startX));
              if ((endX - startX) >= 0) return startX + Math.cos(angle) * this.zScaler(d);
              return startX - Math.cos(angle) * this.zScaler(d);
            })
            .attr('y1', d => {
              const startX = this.xScale(this.xVal(d)); // midpoint circle X
              const startY = this.yScale(this.yVal(d)); // midpoint circle Y
              const endX = this.commentX(d) + this.commentBlockWidth / 2;
              const endY = this.commentY(d) + this.getCommentBlockHeight(d) / 2;
              const angle = Math.atan2(Math.abs(endY - startY), Math.abs(endX - startX));
              if (endY - startY >= 0) return startY + Math.sin(angle) * this.zScaler(d);
              return startY - Math.sin(angle) * this.zScaler(d);
            })
            .attr('x2', d => {
              return this.commentX(d) + this.commentBlockWidth / 2;
            })
            .attr('y2', d => {
              return this.commentY(d);
            });
        },
        update => {
          const selection = update
            .attr('class', d => `comment-lines comment-line-${d.id}`)
            .attr('opacity', d => {
              return this.showComment(d) && this.showConnectorLines ? 1 : 0;
            })
            .attr('pointer-events', 'none')
            .call(u => u.transition(this.transition));

          selection
            .select('.link-line')
            .attr('class', d => `link-line link-line-${d.id}`)
            .attr('x1', d => {
              const startX = this.xScale(this.xVal(d)); // midpoint circle X
              const startY = this.yScale(this.yVal(d)); // midpoint circle Y
              const endX = this.commentX(d) + this.commentBlockWidth / 2;
              const endY = this.commentY(d) + this.getCommentBlockHeight(d) / 2;
              const angle = Math.atan2(Math.abs(endY - startY), Math.abs(endX - startX));
              if ((endX - startX) >= 0) return startX + Math.cos(angle) * this.zScaler(d);
              return startX - Math.cos(angle) * this.zScaler(d);
            })
            .attr('y1', d => {
              const startX = this.xScale(this.xVal(d)); // midpoint circle X
              const startY = this.yScale(this.yVal(d)); // midpoint circle Y
              const endX = this.commentX(d) + this.commentBlockWidth / 2;
              const endY = this.commentY(d) + this.getCommentBlockHeight(d) / 2;
              const angle = Math.atan2(Math.abs(endY - startY), Math.abs(endX - startX));
              if (endY - startY >= 0) return startY + Math.sin(angle) * this.zScaler(d);
              return startY - Math.sin(angle) * this.zScaler(d);
            })
            .attr('x2', d => {
              return this.commentX(d) + this.commentBlockWidth / 2;
            })
            .attr('y2', d => {
              return this.commentY(d);
            });
        },
        exit => exit.remove(),
      );
  }

  /**
   * THEME STATS ANNOTATIONS
   */
  themeStatsChartOptions(theme) {
    const defaultChartOptions = {
      showThemeName: true,
      showIntensity: true,
      showVolume: true,
      showScore: true,
    };

    const themeAnnotation = this.getCustomTheme(theme);
    if (!themeAnnotation) return defaultChartOptions;
    return {
      showThemeName: themeAnnotation.showThemeName,
      showIntensity: themeAnnotation.showIntensity,
      showVolume: themeAnnotation.showVolume,
      showScore: themeAnnotation.showScore,
    };
  }

  themeStatsOpacity(chartOptions, pos) {
    const { showThemeName, showIntensity, showVolume, showScore } = chartOptions;
    const options = [
      showThemeName,
      showIntensity,
      showVolume,
      showScore,
    ];
    return options[pos] ? 1 : 0;
  }

  themeStatsDy(chartOptions, pos) {
    let calPos = pos;
    if (pos >= 1 && !chartOptions.showThemeName) {
      calPos -= 1;
    }
    if (pos >= 2 && !chartOptions.showIntensity) {
      calPos -= 1;
    }
    if (pos >= 3 && !chartOptions.showVolume) {
      calPos -= 1;
    }
    return this.themeStatsTextPosByOptions(chartOptions) + (this.fontSize * calPos) + (this.themeStatsTextGap * calPos) - 10;
  }

  themeStatsTextPosByOptions(chartOptions) {
    const count = this.themeStatsOptionsCount(chartOptions);
    return (this.themeStatsBlockHeight - (this.fontSize * count)) / 2;
  }

  themeStatsBlockHeightByOptions(chartOptions) {
    const count = 4 - this.themeStatsOptionsCount(chartOptions);
    return this.themeStatsBlockHeight - (this.fontSize * count);
  }

  themeStatsOptionsCount(chartOptions) {
    const { showThemeName, showIntensity, showVolume, showScore } = chartOptions;
    return [
      showThemeName,
      showIntensity,
      showVolume,
      showScore,
    ].filter(Boolean).length;
  }

  themeStatsTextScore(score) {
    return score > 0
      ? `+${score}`
      : score;
  }

  themeStatsX(d) {
    const customTheme = this.getCustomTheme(d);
    if (customTheme && customTheme.themeStatsPositionX) {
      return this.xScale(customTheme.themeStatsPositionX);
    }
    return this.xScale(this.xVal(d)) - this.themeStatsBlockWidth / 2;
  }

  themeStatsY(d) {
    const customTheme = this.getCustomTheme(d);
    if (customTheme && customTheme.themeStatsPositionY) return this.yScale(customTheme.themeStatsPositionY);

    const newCircleY = this.yScale(this.yVal(d));
    const pos = newCircleY + Number(this.bubbleSize) + this.arrowHeight;

    if (pos + this.themeStatsBlockHeight < this.chartHeight) return pos;

    return newCircleY - Number(this.bubbleSize) - this.themeStatsBlockHeight - this.arrowHeight;
  }

  showThemeStats(d) {
    return this.showAnnotations && this.isCustom(d) && (
      this.themeStatsChartOptions(d).showThemeName || this.themeStatsChartOptions(d).showIntensity
      || this.themeStatsChartOptions(d).showVolume || this.themeStatsChartOptions(d).showScore
    );
  }

  /* INTERNAL GETTERS */

  get getXScale() {
    return scalePow()
      .exponent(0.5)
      .domain([0, this.xValueMax + this.xRunOff])
      .range([0, this.innerWidth]);
  }

  get getYScale() {
    const bot = Math.floor(this.filteredDataMinScore / 20) * 20;
    const top = Math.ceil(this.filteredDataMaxScore / 20) * 20;

    return scaleLinear()
      .domain([top, bot])
      .range([0, this.innerHeight]);
  }

  get opacityNegativeScale() {
    return scaleLinear()
      .domain([this.intensityNegativeMin, this.intensityNegativeMax])
      .range([this.bubbleMinOpacity, this.bubbleMaxOpacity]);
  }

  get opacityPositiveScale() {
    return scaleLinear()
      .domain([this.intensityPositiveMin, this.intensityPositiveMax])
      .range([this.bubbleMinOpacity, this.bubbleMaxOpacity]);
  }

  get dragThemeStats() {
    return drag()
      .on('start', this.dragThemeStatsStarted.bind(this))
      .on('drag', this.draggingThemeStats.bind(this))
      .on('end', this.dragThemeStatsEnded.bind(this));
  }

  get captionHeight() {
    return 80;
  }

  get captionPos() {
    return this.captionPaddingTop;
  }

  get descriptionPos() {
    return this.captionPos + 30;
  }

  get chartHeight() {
    return this.height - this.captionHeight;
  }

  get hMargin() {
    return this.chartHeight * this.hMarginRatio;
  }

  get wMargin() {
    return this.width * this.wMarginRatio;
  }

  get innerHeight() {
    return this.chartHeight - this.hMargin * 2;
  }

  get innerWidth() {
    return this.width * 0.8 - this.wMargin * 1.6;
  }

  get legendWidth() {
    return this.width * 0.2 - this.wMargin * 0.5;
  }

  get legendHeight() {
    return this.legendSectionTitleHeight + this.legendSectionIntensityHeight * 2;
  }

  get themeStatsTextPos() {
    return (this.themeStatsBlockHeight - (this.fontSize * 4 + this.themeStatsPaddingTop * 3)) / 2;
  }

  get transition() {
    return transition()
      .duration(200)
      .ease(easeSin);
  }

  get xRunOff() {
    return this.xValueMax / 10;
  }

  get xValueMax() {
    return Math.max(10, ...this.data.map(d => Math.abs(this.xVal(d))));
  }

  get yValueMax() {
    return Math.max(0, ...this.data.map(d => Math.abs(this.yVal(d))));
  }

  // Intensity Legend

  get legendFirstPos() {
    return this.legendSectionTitleHeight + this.legendPaddingTop;
  }

  get legendSecondPos() {
    return this.legendFirstPos + this.legendSectionIntensityHeight;
  }

  get legendNegativeTextPos() {
    return this.hasPositiveRange ? this.legendSecondPos : this.legendFirstPos;
  }

  get legendNegativeBarPos() {
    return this.legendNegativeTextPos + 10;
  }

  get legendPositiveTextPos() {
    return this.legendFirstPos;
  }

  get legendPositiveBarPos() {
    return this.legendPositiveTextPos + 10;
  }

  get hasNegativeRange() {
    return this.intensityRange !== ThemesIntensityRange.POSITIVE
      && (Math.abs(this.intensityNegativeMin) !== Infinity && Math.abs(this.intensityNegativeMax) !== Infinity);
  }

  get hasPositiveRange() {
    return this.intensityRange !== ThemesIntensityRange.NEGATIVE
      && (Math.abs(this.intensityPositiveMin) !== Infinity && Math.abs(this.intensityPositiveMax) !== Infinity);
  }

  get legendSectionIntensityHeight() {
    if (!this.hasNegativeRange || !this.hasPositiveRange) {
      return this.legendBlockIntensityHeight;
    }
    return this.legendBlockIntensityHeight * 2;
  }

  /* EXTERNAL METHODS */

  annotateChart({ annotationMode }) {
    this.annotationMode = annotationMode;
    this.updateContainer();
    this.updateChart();
  }

  updateIntensity({
    level,
    intensityNegativeMax,
    intensityNegativeMin,
    intensityPositiveMax,
    intensityPositiveMin,
  }) {
    this.intensityLevel = level;
    this.intensityNegativeMax = intensityNegativeMax;
    this.intensityNegativeMin = intensityNegativeMin;
    this.intensityPositiveMax = intensityPositiveMax;
    this.intensityPositiveMin = intensityPositiveMin;
    this.updateChart();
  }

  updateRange({ range }) {
    this.intensityRange = range;
    this.updateChart();
  }

  updateAnnotationBg({ annotationBg }) {
    this.annotationBg = annotationBg;

    this.setThemeStatsLines();
    this.setThemeStatsBlocks();
    this.setCommentLines();
    this.setCommentBlocks();
  }

  updateBubbleSize({ bubbleSize }) {
    this.bubbleSize = bubbleSize;

    this.setBubbles();
    this.setBubbleLabels();
  }

  updateData({ data, filteredDataMaxScore, filteredDataMinScore }) {
    this.data = data;
    this.filteredDataMaxScore = filteredDataMaxScore;
    this.filteredDataMinScore = filteredDataMinScore;

    this.xScale = this.getXScale;
    this.yScale = this.getYScale;

    this.updateChart();
  }

  updateCustomThemeData({ data, intensityNegativeMax, intensityNegativeMin, intensityPositiveMax, intensityPositiveMin }) {
    this.customThemeData = [...data];
    this.intensityNegativeMax = intensityNegativeMax;
    this.intensityNegativeMin = intensityNegativeMin;
    this.intensityPositiveMax = intensityPositiveMax;
    this.intensityPositiveMin = intensityPositiveMin;

    this.setBubbles();
    this.setBubbleLabels();
    this.setThemeStatsLines();
    this.setThemeStatsBlocks();
    this.setCommentLines();
    this.setCommentBlocks();

    this.setLegendContainer();
    this.setLegendBackground();
    this.setLegendLabels();
    this.setLegendLabelsLine();
    this.setLegendNegativeIntensity();
    this.setLegendPositiveIntensity();
  }

  updateHoverTheme({ hoverTheme }) {
    this.hoverTheme = hoverTheme;

    this.setBubbles();
    this.setBubbleLabels();
  }

  updateIsChanged({ isChanged }) {
    this.isChanged = isChanged;
    this.setCaptionAnnotationLabels();
  }

  updatePath({
    name,
    path,
    filteredDataMaxScore,
    filteredDataMinScore,
    intensityNegativeMax,
    intensityNegativeMin,
    intensityPositiveMax,
    intensityPositiveMin,
  }) {
    this.yName = name;
    this.yPath = path;
    this.filteredDataMaxScore = filteredDataMaxScore;
    this.filteredDataMinScore = filteredDataMinScore;
    this.intensityNegativeMax = intensityNegativeMax;
    this.intensityNegativeMin = intensityNegativeMin;
    this.intensityPositiveMax = intensityPositiveMax;
    this.intensityPositiveMin = intensityPositiveMin;

    this.xScale = this.getXScale;
    this.yScale = this.getYScale;

    // reset position for comment blocks and theme stats blocks
    this.customThemeData.forEach(d => {
      d.commentPositionX = null;
      d.commentPositionY = null;
      d.themeStatsPositionX = null;
      d.themeStatsPositionY = null;
    });

    this.updateChart();
  }

  updateSelected({ selected }) {
    this.selected = selected;

    this.setBubbles();
    this.setBubbleLabels();
    this.setThemeStatsLines();
    this.setThemeStatsBlocks();
    this.setCommentLines();
    this.setCommentBlocks();
    this.setSymbols();
  }

  updateShowAnnotations({ showAnnotations }) {
    this.showAnnotations = showAnnotations;
    this.setThemeStatsLines();
    this.setThemeStatsBlocks();
    this.setCommentLines();
    this.setCommentBlocks();
  }

  updateShowConnectorLines({ showConnectorLines }) {
    this.showConnectorLines = showConnectorLines;
    this.setThemeStatsLines();
    this.setCommentLines();
  }

  updateShowBubbleLabels({ showBubbleLabels }) {
    this.showBubbleLabels = showBubbleLabels;
    this.setBubbleLabels();
  }

  updateTotalVolume({ totalVolume }) {
    this.totalVolume = totalVolume;
    this.setThemeStatsLines();
    this.setThemeStatsBlocks();
    this.setCommentLines();
    this.setCommentBlocks();
  }

  updateTitle({ title }) {
    this.labelTitle = title;
    this.setCaptionLabels();
  }

  updateDesc({ desc }) {
    this.labelDesc = desc;
    this.setCaptionLabels();
  }

  updateShowTitle({ showTitle }) {
    this.showTitle = showTitle;
    this.updateChart();
  }

  updateShowDescription({ showDescription }) {
    this.showDescription = showDescription;
    this.updateChart();
  }

  updateShowAxisLabel({ showAxisLabel }) {
    this.showAxisLabel = showAxisLabel;
    this.setAxisLegends();
  }

  updateXAxisLabel({ xAxisLabel }) {
    this.xAxisLabel = xAxisLabel;
    this.setAxisLegends();
  }

  updateYAxisLabel({ yAxisLabel }) {
    this.yAxisLabel = yAxisLabel;
    this.setAxisLegends();
  }
}
