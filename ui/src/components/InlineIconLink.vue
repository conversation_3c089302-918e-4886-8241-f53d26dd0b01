<template>
  <span class="inline-icon-link" @click="$emit('click')">
    <span class="icon">
      <slot name="icon"></slot>
    </span>
    <span class="text">
      <slot name="text"></slot>
    </span>
  </span>
</template>

<script>
export default {
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';

.inline-icon-link {
  cursor: pointer;
  color: clr('purple');

  .icon, .text {
    display: inline-block;
  }

  .icon {
    margin-right: -0.2em;
    vertical-align: middle;
  }

  .text {
    vertical-align: text-bottom;
  }
}
</style>
