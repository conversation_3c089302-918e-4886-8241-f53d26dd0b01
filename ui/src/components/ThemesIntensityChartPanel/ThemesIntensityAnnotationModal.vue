<template>
  <section class="themes-intensity-annotation-modal">
    <section v-if="!selectingComment" class="annotation">
      <section class="header">
        <section class="left">
          <i class="fa-solid fa-message-plus icon"></i>
        <div v-if="editMode">
          Edit Annotation for '{{selectedTheme.topicLabel}}' Theme
        </div>
        <div v-else>
          Add Annotation for '{{selectedTheme.topicLabel}}' Theme
        </div>
        </section>
        <section class="right ">
          <section class="remove">
            <base-button colour="danger" size="small" type="base" @click="onClickRemove" v-if="editMode">
              <i class="fa-regular fa-ban icon"></i>
              Remove Annotation
            </base-button>
          </section>
        </section>
      </section>
      <section class="body">
        <section class="themes-intensity-theme-state-left">
          <section class="intensity-chart-option">
            <section class="title-display title">
              <span class="title">Title</span>
              <span class="show">Show?</span>
            </section>

            <section class="input-annotation-content">
              <base-input :placeholder="titlePlaceholder" v-model="localTheme.title" @tab="onTabTitle"/>
              <base-checkbox-solid :value="localTheme.showTitle" @input="localTheme.showTitle = !localTheme.showTitle" />
            </section>

            <section class="title-display description">
              <span class="title">Description</span>
            </section>

            <section class="input-annotation-content">
              <section class="section-area" :class="{ focus: isFocusedArea }">
              <textarea ref="textarea"
                  rows="6"
                  v-model="localTheme.description"
                  class="text-area"
                  :placeholder="descriptionPlaceholder"
                  @focus="onFocusArea"
                  @blur="onBlurArea"
              />
                <base-button class="insert-comment-button" @click="selectingComment = true">
                  <i class="fa-solid fa-comment-plus"></i>
                  Insert Comment
                </base-button>
              </section>
              <base-checkbox-solid :value="localTheme.showDescription" @input="localTheme.showDescription = !localTheme.showDescription;" />
            </section>
          </section>
        </section>

        <section class="themes-intensity-theme-state-right">
          <section class="preview">
            <i class="fa-solid fa-eye icon"></i>
            Preview
          </section>
          <section class="preview-block">
            <section class="preview-block-content">
              <section class="preview-content title" v-if="localTheme.showTitle">
                {{ previewTitle }}
              </section>

              <section class="preview-content description" v-if="localTheme.showDescription">
                {{ previewDescription }}
              </section>
            </section>
          </section>
        </section>

      </section>
      <section class="footer intensity-chart-option">
        <section class="cancel">
          <base-button colour="light" size="small" type="link"  @click="onClose">
            Cancel
          </base-button>
        </section>

        <section class="add" @click="onClickSave">
          <base-button :disabled="addDisable" colour="light" size="small" type="base" v-if="editMode">
            <i class="fa-regular fa-check icon"></i>
            Done
          </base-button>
          <base-button :disabled="addDisable" colour="light" size="small" type="base" v-else>
            <i class="fa-regular fa-plus icon"></i>
            Add Annotation
          </base-button>
        </section>
      </section>
    </section>
    <themes-intensity-annotation-modal-comment v-else @close="selectingComment = false" @selectComment="selectComment" />
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseCheckboxSolid from '@/components/Base/BaseCheckboxSolid';
import BaseInput from '@/components/Base/BaseInput';
import DatasetsItemAdorescore from '@/components/DatasetsItem/DatasetsItemAdorescore';
import ThemesIntensityAnnotationModalComment from '@/components/ThemesIntensityChartPanel/ThemesIntensityAnnotationModalComment';

export default {
  name: 'themes-intensity-annotation-modal',

  components: {
    BaseButton,
    BaseCheckboxSolid,
    BaseInput,
    DatasetsItemAdorescore,
    ThemesIntensityAnnotationModalComment,
  },

  data() {
    return {
      descriptionPlaceholder: 'Add Description',
      isFocusedArea: false,
      localTheme: {},
      selectingComment: false,
      titlePlaceholder: 'Add Title',
    };
  },

  created() {
    this.loadCommentAnnotation();
  },

  computed: {

    ...mapState('datasets', ['active']),

    ...mapGetters('datasets', ['get']),

    ...mapState('themes', ['customThemes', 'selectedTheme']),

    addDisable() {
      const { title, description, showTitle, showDescription } = this.localTheme;
      const hasTitle = title || title?.trim().length > 0;
      const hasDesc = description || description?.trim().length > 0;
      if ((!hasTitle && !hasDesc) || (showTitle && !hasTitle) || (showDescription && !hasDesc) || (!showTitle && !showDescription)) {
        return true;
      }

      return false;
    },

    editMode() {
      const theme = this.themeCommentAnnotation;
      return (
        theme &&
        ![
          theme.showTitle,
          theme.showDescription,
          theme.title,
          theme.description,
        ]
          .every(value => this.isUndefinedOrNull(value))
      );
    },

    previewDescription() {
      if (this.localTheme.description.trim() === '') return this.descriptionPlaceholder;
      return this.localTheme.description;
    },

    previewTitle() {
      if (this.localTheme.title.trim() === '') return this.titlePlaceholder;
      return this.localTheme.title;
    },

    themeCommentAnnotation() {
      return this.customThemes.find(t => t.id === this.selectedTheme.id);
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal', 'setModalComponent']),

    ...mapActions('themes', ['addCustomThemes', 'removeCustomThemesCommentAnnotation']),

    ...mapActions('themesIntensity', ['setUpdatingAnnotation']),

    isUndefinedOrNull(value) {
      return value === undefined || value === null;
    },

    loadCommentAnnotation() {
      const theme = this.themeCommentAnnotation;
      if (theme) {
        this.localTheme = {
          ...theme,
          showTitle: this.isUndefinedOrNull(theme.showTitle) ? true : theme.showTitle,
          showDescription: this.isUndefinedOrNull(theme.showDescription) ? true : theme.showDescription,
          title: this.isUndefinedOrNull(theme.title) ? '' : theme.title,
          description: this.isUndefinedOrNull(theme.description) ? '' : theme.description,
        };
      }
    },

    onClickSave() {
      if (this.addDisable) return;
      this.addCustomThemes({ themes: [this.localTheme] });
      this.setUpdatingAnnotation({ updatingAnnotation: true });
      this.closeModal();
    },

    onClickRemove() {
      this.removeCustomThemesCommentAnnotation({ themeId: this.localTheme.id });
      this.setUpdatingAnnotation({ updatingAnnotation: true });
      this.closeModal();
    },

    onClose() {
      this.closeModal();
    },

    onFocusArea() {
      this.isFocusedArea = true;
    },

    onBlurArea() {
      this.isFocusedArea = false;
    },

    onTabTitle() {
      this.$refs.textarea.focus();
    },

    selectComment(comment) {
      this.localTheme.description = `"${comment.content}"`;
      this.localTheme.docId = comment.docId;
      this.localTheme.title = 'Illustrative Comment';
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-intensity-annotation-modal {
  @include panel;
  font-family: 'Inter', serif;
  position: relative;
  width: 800px;

  .header {
    @include flex('block', 'row', 'space-between', 'center');

    border-bottom: $border-light solid rgba(19, 28, 41, 0.4);
    padding: 0 1.5rem;

    height: 70px;

    .left {
      align-items: center;
      color: #131C29;
      display: flex;
      font-family: 'Inter',serif;
      font-size: $font-size-base;
      font-weight: $font-weight-bold;
      line-height: 18px;
    }

    .right {
      @include flex('block', 'column', 'center', 'end');

      .remove {
        .base-button {
          background-color: transparent;
          border-radius: 2px;
          border: 1px solid rgba(251, 56, 47);
          color: rgba(251, 56, 47);
          font-size: $font-size-xxs;
          font-weight: 800;
          text-transform: uppercase;

          &:hover {
            border: 1px solid rgba(251, 56, 47, 0.5);
          }
        }
      }
    }
  }

  .body {
    @include flex('block', 'row', 'start', 'start');

    height: 340px;
    font-size: $font-size-xs;

    .themes-intensity-theme-state-left {
      background: #FFFFFF;
      height: 100%;
      width: 60%;

      .intensity-chart-option {
        @include flex("block", "column", "center", "stretch");

        padding: 1em 1.5em 1em 2em;

        .title-display {
          @include flex("block", "row", "space-between", "center");

          margin-bottom: 0.3rem;
          margin-top: 0.3rem;
          position: relative;
          text-transform: uppercase;

          &.description {
            margin-top: 1rem;
          }

          .title {
            color: $nps-blue;
            font-size: $font-size-xxs;
            font-weight: 800;
            letter-spacing: $letter-spacing-xs;
            margin: 0;
          }

          .show {
            color: #2114FF;
            font-size: $font-size-xxs;
            font-weight: $font-weight-bold;
            letter-spacing: 0.3px;
            line-height: 12px;
            opacity: 0.7;
            position: absolute;
            right: 0;
            text-align: center;
          }
        }

        .input-annotation-content {
          @include flex("block", "row", "space-between", "start");

          cursor: pointer;
          padding: 0.3rem 0 0.2rem 0;
          position:relative;

          .base-input {
            color: $nps-blue;
            margin-right: 1.5rem;

            &::placeholder {
              opacity: 0.5;
            }
          }

          .base-checkbox-solid {
            margin-right: 0.8rem;
          }

          .section-area {
            @include flex("block", "column", "start", "start");
            @include panel;

            margin-right: 1.5rem;
            width: 100%;

            &:hover {
              border: $border-light solid mix($border-color, clr("purple"), 75%);
            }

            &.focus {
              border: $border-light solid clr("purple");
              outline: none;
            }

            .text-area {
              border: none;
              color: $nps-blue;
              display: -webkit-box;
              line-height: 1.125rem;
              margin-top: 0.4rem;
              outline: none;
              overflow: hidden;
              padding-left: 0.6rem;
              padding-right: 0.6rem;
              resize: none;
              width: 100%;

              &::placeholder {
                opacity: 0.5;
              }

              &.editing {
                overflow-y: auto;
                -webkit-line-clamp: unset;
              }
            }

            .insert-comment-button {
              background-color: rgba(19, 28, 41, 0.1);
              border-radius: 12px;
              color: $nps-blue;
              font-size: $font-size-xs;
              font-weight: $font-weight-bold;
              height: 24px;
              margin: 1rem 1rem 1rem auto;
              padding: 0 0.6rem;
              text-transform: uppercase;

              .fa-comment-plus {
                margin-right: 0.2rem;
              }

              &:hover {
                color: clr('white');
                background: rgba(19, 28, 41, 0.5);
              }
            }
          }
        }
      }
    }

    .themes-intensity-theme-state-right {

      @include flex('block', 'column', 'center', 'center');

      background: rgba(237, 218, 255, 0.4);
      height: 100%;
      position: relative;
      width: 40%;

      .preview {
        color: #131C29;
        font-family: 'Inter',serif;
        font-size: $font-size-xxs;
        font-weight: $font-weight-bold;
        left: 0;
        letter-spacing: 0.3px;
        line-height: 12px;
        margin: 17px;
        opacity: 0.88;
        position: absolute;
        text-transform: uppercase;
        top: 0;

        .icon {
          margin-right: 0.2rem;
        }
      }

      .preview-block {
        @include flex('block', 'row', 'start', 'center');

        background: #131C29;
        border-radius: 3px;
        padding: 1rem 0.4rem;
        max-height: 248px;
        width: 250px;

        .preview-block-content {
          color: #FFFFFF;
          max-height: 100%;
          overflow: hidden;

          .preview-content {
            font-size: 11px;
            padding: 0.3rem 0 0.2rem 0.5rem;
            word-wrap: break-word;

            &.title {
              font-weight: $font-weight-bold;
            }

            &.description {

            }
          }
        }
      }
    }
  }

  .footer {
    @include flex('block', 'row', 'between', 'center');
    @include rigid;

    height: 70px;
    border-top: $border-light solid $border-color;
    padding: 0 1.5rem;
    text-transform: uppercase;

    .cancel {
      .base-button {
        padding: 0;
      }
    }

    .add {
      .base-button {
        background: #131C29;
        border: 1px solid rgba(0, 0, 0, 0.3);
        color: #FFFFFF;

        &:hover {
          border: 1px solid #FFFFFF;
        }
      }
    }
  }

  .icon {
    margin-right: 0.3rem;
  }
}
</style>
