<template>
  <section class="themes-intensity-chart-filter-applied-toast">
    <section class="toast-content">
      <section class="left">
        <i class="fa fa-filter icon"/>
        <section class="text">
          {{message}}
        </section>
      </section>
      <section class="right">
        <base-button class="dismiss" colour="light" @click="onDismiss">Dismiss</base-button>
      </section>
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import Toast from '@/components/Mixins/Toast';

export default {
  name: 'themes-intensity-chart-filter-applied-toast',

  components: {
    BaseButton,
  },

  mixins: [Toast],

  computed: {
    ...mapState('themesIntensity', ['filterType']),

    message() {
      return this.filterType === null ? 'Filter reset' : ` Filter - ${this.filterType.message()} - Applied`;
    },
  },

  created() {
    setTimeout(this.close, 3000);
  },

  methods: {
    onDismiss() {
      this.close();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-intensity-chart-filter-applied-toast {
  @include flex("block", "row", "end", "start");

  .toast-content {
    @include toast;

    background-color: #7F0C8A;

    .left {
      @include flex("block", "row", "start", "center");

      .icon {
        margin-right: 0.5rem;
      }

      .text {
        @include flex("block", "row", "start", "center");
      }
    }

    .right {
      margin-left: 2rem;
    }
  }
}
</style>
