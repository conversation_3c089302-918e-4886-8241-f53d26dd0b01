<template>
  <section class="reset-themes-intensity-chart-modal">
    <section class="header">
      <h2>Reset Chart to Default?</h2>
    </section>
    <section class="body">
      <span>All chart modifications will be lost.</span>
    </section>
    <section class="footer">
      <base-button class="cancel" colour="light" type="link" @click="onCancel">Cancel</base-button>
      <loading-blocks-overlay v-if="resetting" />
      <base-button v-else class="delete" colour="danger" @click="onReset">
        <span>Reset</span>
      </base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import CustomChartType from '@/enum/custom-chart-type';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';

import { datasetsRequestV0 } from '@/services/request';

export default {
  name: 'reset-themes-intensity-chart-modal',

  components: {
    BaseButton,
    LoadingBlocksOverlay,
  },

  data() {
    return {
      resetting: false,
    };
  },

  computed: {
    ...mapState('themes', ['customThemes']),

    ...mapGetters('themes', ['getDefaultCustomThemes']),
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    ...mapActions('themes', [
      'deselectThemes',
      'selectThemes',
      'setCustomThemes',
    ]),

    ...mapActions('themesIntensity', ['setAnnotationMode']),

    onCancel() {
      this.closeModal();
    },

    async onReset() {
      this.resetting = true;

      await datasetsRequestV0.deleteCustomChart(CustomChartType.THEME_ANALYSIS);
      await datasetsRequestV0.retrieveCustomChart(CustomChartType.THEME_ANALYSIS);

      const defaultThemes = this.getDefaultCustomThemes(CustomChartType.THEME_ANALYSIS);
      this.deselectThemes({ ids: this.customThemes.map(t => t.id) });
      this.selectThemes({ ids: defaultThemes.map(t => t.id) });
      this.setCustomThemes({ themes: defaultThemes });
      this.setAnnotationMode({ annotationMode: false });

      this.resetting = false;
      this.closeModal();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.reset-themes-intensity-chart-modal {
  @include modal;

  .body {
    @include flex("block", "column", "start", "stretch");

    span {
      font-size: $font-size-sm;
      margin: 0.5rem 0;
    }
  }

  .icon {
    height: $font-size-base;
    width: $font-size-base;
  }
}
</style>
