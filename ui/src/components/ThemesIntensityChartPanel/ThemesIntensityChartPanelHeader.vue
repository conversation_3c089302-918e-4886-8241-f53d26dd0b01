<template>
  <section class="themes-intensity-chart-panel-header">
    <section class="themes-intensity-chart-panel-header-panel">
      <section class="intensity-chart-configurations-left">
        <i class="fa-solid fa-arrows-left-right-to-line icon-mode" @click="onClickExpand"></i>
        <section class="filters-button" @click="onClickFilters">
          <i class="fa-solid fa-filter icon-filter"></i>
          <span class="text">Chart Filters</span>
          <section class="circle" v-show="appliedFilter">
            <i class="fa fa-check icon-check" :class="{ open : showFilters }"></i>
          </section>
          <i class="fa fa-caret-down icon-caret" :class="{ open : showFilters }"></i>
        </section>
      </section>

      <section class="intensity-chart-configurations-right">

        <!-- Phase 3 -->
        <!-- <span class="text">View</span>
        <section class="view-button left" @click="onClickChartBubbleView">
          <i class="fa-solid fa-chart-scatter-bubble"></i>
        </section>
        <section class="view-button right" @click="onClickChartBarView">
          <i class="fa-solid fa-chart-bar"></i>
        </section> -->

        <section>
          <section class="option-button" @click.stop="onClickOptionButton">
            <i class="fa-regular fa-square-sliders-vertical icon-option"></i>
            <span class="option-button-text">Options</span>
            <i class="fa fa-caret-down icon-caret" :class="{ open : showOption }"></i>
          </section>
          <themes-intensity-chart-option-dropdown v-if="showOption" @close="showOption = false"/>
        </section>
        <section class="annotate-button" :class="{active: annotationMode}" @click="onClickAnnotateButton">
          <i class="fa-solid fa-plus icon-plus"></i>
          <span class="annotate-button-text">Edit Chart</span>
          <i class="fa fa-caret-down icon-caret" :class="{ open : showAnnotate }"></i>
        </section>

        <section class="download-button" :class="{disabled: annotationMode}"
          @click.prevent.stop="onClickDownloadButton"
          v-tooltip.bottom.end.notrigger="{
            html: 'themes-intensity-chart-download-dropdown',
            class: 'tooltip-themes-intensity-download-action',
            delay: 0,
            visible: showDownload,
          }"
        >
          <i class="fa-solid fa-download icon-download"></i>
          <span class="download-button-text">Download</span>
          <i class="fa fa-caret-down icon-caret" :class="{ open : showDownload }"></i>
        </section>
      </section>
    </section>
    <theme-intensity-chart-filter-panel v-if="showFilters" @closeFilter="closeFilter"/>

    <!--    Dropdowns-->
    <themes-intensity-chart-download-dropdown id="themes-intensity-chart-download-dropdown" @close="showDownload = false" />
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import ThemeIntensityChartFilterPanel from '@/components/ThemesIntensityChartPanel/ThemesIntensityChartFilter/ThemeIntensityChartFilterPanel';
import ThemesIntensityChartDownloadDropdown from '@/components/ThemesIntensityChartPanel/ThemesIntensityChartDownloadDropdown';
import ThemesIntensityChartOptionDropdown
from '@/components/ThemesIntensityChartPanel/ThemesIntensityChartOptionDropdown';

export default {
  name: 'themes-intensity-chart-panel-header',

  components: {
    BaseButton,
    ThemeIntensityChartFilterPanel,
    ThemesIntensityChartDownloadDropdown,
    ThemesIntensityChartOptionDropdown,
  },

  data() {
    return {
      showAnnotate: false,
      showDownload: false,
      showFilters: false,
      showOption: false,
    };
  },

  computed: {
    ...mapGetters('themesIntensity', ['appliedFilter']),

    ...mapState('themesIntensity', [
      'annotationMode',
      'level',
      'range',
    ]),

    ...mapState('themesChart', ['showChartMaxWidth', 'yPath']),

  },

  created() {
    this.setShowChartMaxWidth({ value: false });
  },

  methods: {
    ...mapActions('themesChart', ['setShowChartMaxWidth']),

    ...mapActions('themesIntensity', ['setAnnotationMode']),

    onClickAnnotateButton() {
      this.setAnnotationMode({ annotationMode: !this.annotationMode });
    },

    onClickChartBarView() {
      // TODO
    },

    onClickChartBubbleView() {
      // TODO
    },

    onClickExpand() {
      this.setShowChartMaxWidth({ value: !this.showChartMaxWidth });
    },

    onClickDownloadButton() {
      if (!this.annotationMode) {
        this.showDownload = !this.showDownload;
      }
    },

    onClickFilters() {
      this.showFilters = !this.showFilters;
    },

    onClickOptionButton() {
      this.showOption = !this.showOption;
    },

    onClickShowFilters() {
      this.showFilters = !this.showFilters;
    },

    closeFilter() {
      this.showFilters = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-intensity-chart-panel-header {

  .themes-intensity-chart-panel-header-panel {
    @include flex("block", "row", "between", "stretch");

    border-bottom: 1px solid #DEE1E4;
    height: 50px;
    padding: 0.75rem 1rem;

    .intensity-chart-configurations-left {
      @include flex("block", "row", "center", "center");

      .icon-mode {
        cursor: pointer;
        margin-right: 0.8rem;
      }

      .filters-button {
        @include flex("block", "row", "start", "center");

        background-color: #131C29;
        border-radius: 3px;
        border: 2px solid rgba(75, 114, 240, 0.15);
        color: clr('white');
        cursor: pointer;
        font-size: $font-size-xxs;
        font-weight: $font-weight-bold;
        height: 100%;
        padding: 0.3rem 0.5rem;
        position: relative;

        .icon-filter {
          margin-right: 0.4rem;
        }

        .icon-caret {
          font-weight: $font-weight-bold;
          margin-right: 0.2rem;
          transition: transform $interaction-transition-time;

          &.open {
            transform: rotateX(180deg);
          }
        }

        .circle {
          @include flex("block", "row", "center", "center");

          background: #DCC3FF;
          border-radius: 50%;
          width: 12px;
          height: 12px;
          margin-right: 0.4rem;

          .icon-check {
            color: #131C29;
            font-size: 8px;
            font-weight: 900;
            height: 8px;
            letter-spacing: 0.3px;
            text-align: center;
            text-transform: uppercase;
            width: 8px;
          }
        }

        .text {
          margin-right: 0.4rem;
          text-transform: uppercase;
          transition: all $interaction-transition-time;
        }
      }
    }

    .intensity-chart-configurations-right {
      @include flex("block", "row", "center", "center");

      column-gap: 0.25rem;
      padding-right: 1rem;

      .text {
        color: #131C29;
        font-size: $font-size-xxs;
        font-weight: $font-weight-bold;
        margin-right: 0.8rem;
        opacity: 0.7;
        text-transform: uppercase;
      }

      .view-button {
        @include flex("block", "row", "start", "center");

        background-color: clr('white');
        border-radius: 2px;
        border: 1px solid rgba(19, 28, 41, 0.3);
        color: #131C29;
        cursor: pointer;
        font-size: $font-size-xs;
        padding: 0 0.55rem;
        position: relative;
        height: 100%;

        &.right {
          left: -1px;
          margin-right: 0.4rem;
        }
      }

      .option-button {
        @include flex("block", "row", "start", "center");

        background-color: rgba(19, 28, 41, 0.7);
        border-radius: 3px;
        border: 2px solid rgba(75, 114, 240, 0.15);
        color: clr('white');
        cursor: pointer;
        font-size: $font-size-xxs;
        font-weight: $font-weight-bold;
        height: 100%;
        margin-right: 0.4rem;
        padding: 0.3rem 0.5rem;
        position: relative;

        .icon-option {
          margin-right: 0.4rem;
        }

        .icon-caret {
          font-weight: $font-weight-bold;
          margin-right: 0.2rem;
          transition: transform $interaction-transition-time;

          &.open {
            transform: rotateX(180deg);
          }
        }

        .option-button-text {
          margin-right: 0.4rem;
          text-transform: uppercase;
          transition: all $interaction-transition-time;
        }
      }

      .annotate-button {
        @include flex("block", "row", "start", "center");

        background-color: #EDDAFF;
        border-radius: 3px;
        color: clr('black');
        cursor: pointer;
        font-size: $font-size-xxs;
        font-weight: $font-weight-bold;
        height: 100%;
        margin-right: 0.4rem;
        padding: 0.3rem 0.5rem;
        position: relative;

        .icon-plus {
          margin-right: 0.4rem;
        }

        .icon-caret {
          font-weight: $font-weight-bold;
          margin-right: 0.2rem;
        }

        .annotate-button-text {
          margin-right: 0.4rem;
          text-transform: uppercase;
          transition: all $interaction-transition-time;
        }

        &.active {
          background-color: #A94FFF;
          color: clr('white');

          .icon-caret {
            transform: rotateX(180deg);
          }
        }
      }

      .download-button {
        @include flex("block", "row", "start", "center");

        background-color: #0E41F3;
        border-radius: 3px;
        color: clr('white');
        cursor: pointer;
        font-size: $font-size-xxs;
        font-weight: $font-weight-bold;
        height: 100%;
        margin-right: 0.4rem;
        padding: 0.3rem 0.5rem;
        position: relative;

        .download-button-text {
          margin-right: 0.4rem;
          text-transform: uppercase;
          transition: all $interaction-transition-time;
        }

        .icon-download {
          margin-right: 0.4rem;
        }

        .icon-caret {
          font-weight: $font-weight-bold;
          margin-right: 0.2rem;
          transition: transform $interaction-transition-time;

          &.open {
            transform: rotateX(180deg);
          }
        }
      }

      .disabled {
        cursor: default;
        opacity: 0.5;
      }
    }
  }

}
</style>
