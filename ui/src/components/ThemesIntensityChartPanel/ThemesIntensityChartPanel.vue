<template>
  <section class="themes-intensity-chart-panel" :class="{ expanded: showChartMaxWidth }">
    <themes-intensity-chart-panel-header />

    <section class="chart-wrapper" :class="{ 'annotation-mode': annotationMode }">
      <section class="dropdown-arrow"></section>
      <loading-switch :status="themesStatus">
        <template #default>
          <themes-intensity-chart bcId="themes-intensity-chart" />
        </template>
        <template #loading>
          <loading-blocks-overlay>Loading Themes</loading-blocks-overlay>
        </template>
      </loading-switch>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import LoadingSwitch from '@/components/LoadingSwitch';
import NetworkKeys from '@/enum/network-keys';
import ThemesIntensityChart from '@/components/ThemesIntensityChart/ThemesIntensityChart';
import ThemesIntensityChartPanelHeader from '@/components/ThemesIntensityChartPanel/ThemesIntensityChartPanelHeader';

export default {
  name: 'themes-intensity-chart-panel',

  components: {
    LoadingBlocksOverlay,
    LoadingSwitch,
    ThemesIntensityChart,
    ThemesIntensityChartPanelHeader,
  },

  computed: {
    ...mapGetters('network', ['status']),

    ...mapState('themes', ['themes']),

    ...mapState('themesChart', ['showChartMaxWidth']),

    ...mapState('themesIntensity', ['annotationMode']),

    themesStatus() {
      return this.status(NetworkKeys.THEMES);
    },
  },

  methods: {
    ...mapActions('themesIntensity', ['setIntensityMaxAndMinByLevel', 'setLevel']),
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-intensity-chart-panel {
  @include flex("block", "column", "start", "stretch");
  @include stretch;

  &.expanded {
    grid-area: themes-chart-panel;
  }

  .chart-wrapper {
    @include flex("block", "row", "center", "center");

    border: 2px solid #FFF;
    position: relative;
    width: 100%;

    .dropdown-arrow {
      border-bottom: 8px solid #A94FFF;
      border-left: 5px solid transparent;
      border-right: 5px solid transparent;
      border-top: none;
      position: absolute;
      right: 215px;
      top: -8px;
      visibility: hidden;
    }

    &.annotation-mode {
      background-color: rgba(237, 218, 255, 0.4);
      border-color: #A94FFF;

      .dropdown-arrow {
        visibility: visible;
      }
    }

    .themes-intensity-chart {
      align-self: center;
    }
  }

  .loading-blocks-overlay {
    height: 100%;
  }
}
</style>
