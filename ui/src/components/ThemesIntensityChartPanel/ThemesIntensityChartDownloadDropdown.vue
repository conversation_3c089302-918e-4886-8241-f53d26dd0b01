<template>
  <section class="themes-intensity-chart-download-dropdown">
    <loading-blocks-overlay v-if="downloading">Downloading Slide</loading-blocks-overlay>
    <section v-else>
      <section class="item" @click="onDownload('png')">
        <i class="fa-regular fa-download icon" />
        <span class="text">Download Slide (.png)</span>
      </section>

      <section class="item" @click="onDownload('svg')">
        <i class="fa-regular fa-download icon" />
        <span class="text">Download Slide (.svg)</span>
      </section>
    </section>
  </section>
</template>

<script>
import { saveSvg, saveSvgAsPng } from 'save-svg-as-png';

import BlurCloseable from '@/components/Mixins/BlurCloseable';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';

import { mapGetters, mapState } from 'vuex';

export default {
  name: 'themes-intensity-chart-download-dropdown',

  mixins: [BlurCloseable],

  components: {
    LoadingBlocksOverlay,
  },

  data() {
    return {
      downloading: false,
    };
  },

  computed: {
    ...mapState('datasets', ['active']),

    ...mapState('themesIntensity', {
      showDescription: state => state.chartOptions.showDescription,
      showTitle: state => state.chartOptions.showTitle,
    }),

    ...mapGetters('datasets', ['get']),

    selectedDataset() {
      return this.get(this.active);
    },
  },

  methods: {
    async onDownload(type) {
      this.downloading = true;

      const chartSvg = document.getElementById('themes-intensity-chart-svg');
      const label = `${this.selectedDataset.label}-Theme_Intensity_Chart.${type}`;

      const options = {
        backgroundColor: 'white',
        encoderOptions: 1,
        // I'm not sure if this is actually doing anything, but this is how the library specifies to use a custom font
        fonts: [
          {
            url: 'https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap',
            text: `@font-face {
              font-family: 'Inter';
              font-display: swap;
              src: url(https://fonts.gstatic.com/s/inter/v12/UcCo3FwrK3iLTcviYwY.woff2) format('woff2');
            }`,
          },
        ],
        scale: 2,
      };

      await new Promise(resolve => setTimeout(resolve, 100));

      if (type === 'png') {
        await saveSvgAsPng(chartSvg, label, options);
      } else if (type === 'svg') {
        await saveSvg(chartSvg, label, options);
      }

      this.$emit('close');
      this.downloading = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.themes-intensity-chart-download-dropdown {
  @include flex("block", "column", "center", "stretch");

  height: 4rem;
  width: 12.5rem;

  .item {
    @include flex("block", "row", "start", "center");

    cursor: pointer;
    padding: 0.5rem 1rem;
    transition: all $interaction-transition-time;

    &:hover {
      color: clr('purple');
    }

    &:not(:first-child) {
      border-top: 1px solid rgba(0, 0, 0, 0.1);
    }

    .icon {
      color: inherit;
      font-size: $font-size-sm;
      height: $font-size-sm;
      margin-right: 0.5rem;
      width: $font-size-sm;
    }

    .text {
      color: inherit;
      font-size: $font-size-xs;
      font-weight: 400;

      .strong {
        font-weight: 600;
      }
    }
  }
}
</style>
<style lang="scss">
@import 'src/styles/variables';
@import 'src/styles/mixins';

.themes-intensity-chart-download-dropdown {
  .loading-blocks-overlay {
    .text {
      font-size: $font-size-xs;
    }

    .container {
      margin: 0;
    }
  }
}
</style>
