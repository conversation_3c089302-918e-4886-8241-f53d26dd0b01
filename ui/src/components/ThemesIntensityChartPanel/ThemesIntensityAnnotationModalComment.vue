<template>
  <section class="themes-intensity-annotation-modal-comment">
    <section class="header">
      <base-button class="back-button" @click="onClickBack">
        <i class="fa-solid fa-arrow-left"></i>
        Back
      </base-button>
    </section>
    <section class="body">
      <section class="title">
        Select a Comment
        <i class="fa-solid fa-arrow-down"></i>
      </section>
      <section class="search">
        <i class="fa-regular fa-magnifying-glass icon-search"></i>
        <base-input class="input" v-model="search" placeholder="Search for a comment"/>
      </section>
      <section class="comments">
        <section v-for="(item) in filteredSnippets" class="comment"
          @mouseenter="selectedId = item.docId"
          @mouseleave="selectedId = null"
          @click="onSelectComment(item)"
        >
          <base-radio-with-tick-mark :value="selectedId === item.docId" />
          <span>{{item.content}}</span>
          <section class="topic-count"
                   @click.stop="onClickTopicCount(item)"
                   v-click-outside-handler="onClickOutsideTopicCount"
          >
            <section class="topic-count-info"
             :class="{ active: showTopicsDropdown === item.docId }"
            >
              <i class="fa-light fa-chart-network icon"></i>
              <section class="text">
                {{ getTopicCount(item) }}
              </section>
            </section>
          </section>
<!--          <comments-list-item-topics-dropdown-->
<!--              class="comments-list-item-topics-dropdown"-->
<!--              v-if="showTopicsDropdown === item.docId"-->
<!--              :topicIds="getSnippetTopicIds(item)"-->
<!--          />-->
        </section>
      </section>
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';
import BaseRadioWithTickMark from '@/components/Base/BaseRadioWithTickMark';
import clickOutsideHandler from '@/directives/click-outside-handler';
import CommentsListItemTopicsDropdown from '@/components/CommentsList/CommentsListItemTopicsDropdown';

export default {
  name: 'themes-intensity-annotation-modal-comment',

  components: {
    BaseButton,
    BaseInput,
    BaseRadioWithTickMark,
    CommentsListItemTopicsDropdown,
  },

  directives: {
    clickOutsideHandler,
  },

  data() {
    return {
      search: '',
      selectedId: null,
      showTopicsDropdown: false,
    };
  },

  computed: {
    ...mapState('themes', ['selectedTheme']),

    ...mapState('snippets', ['snippets']),

    filteredSnippets() {
      return this.snippets.filter(item => {
        return item.content.toLowerCase().includes(this.search.toLowerCase());
      });
    },
  },

  methods: {
    getSnippetTopicIds(snippet) {
      return snippet.topicIds || [];
    },

    getTopicCount(snippet) {
      return snippet.topicIds?.length || [];
    },

    onClickBack() {
      this.$emit('close');
    },

    onSelectComment(item) {
      this.$emit('selectComment', item);
      this.$emit('close');
    },

    onClickOutsideTopicCount() {
      this.showTopicsDropdown = null;
    },

    onClickTopicCount(item) {
      if (this.showTopicsDropdown === item.docId) {
        this.showTopicsDropdown = null;
      } else {
        this.showTopicsDropdown = item.docId;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-intensity-annotation-modal-comment {
  color: $nps-blue;

  .header {
    @include flex('block', 'row', 'start', 'center');
    border-bottom: $border-light solid rgba(19, 28, 41, 0.4);
    padding: 0 1.5rem;
    height: 70px;

    .back-button {
      background-color: rgba(19, 28, 41, 0.1);
      border-radius: 12px;
      color: $nps-blue;
      font-size: $font-size-xs;
      font-weight: $font-weight-bold;
      height: 24px;
      padding: 0 0.6rem;
      text-transform: uppercase;

      &:hover {
        color: clr('white');
        background: rgba(19, 28, 41, 0.5);
      }

      .fa-arrow-left {
        margin-right: 0.2rem;
      }
    }
  }

  .body {
    @include flex('block', 'column', 'start', 'start');

    font-size: $font-size-xs;
    height: 410px;
    line-height: $font-size-md;
    margin: 0 1.5rem;

    .title {
      font-size: $font-size-base;
      font-weight: $font-weight-bold;
      margin-top: 1rem;
    }

    .search {
      @include flex("block", "row", "start", "center");
      @include shrink;

      background: clr("white");
      border-radius: 3px;
      border: $border-light solid rgba(19, 28, 41, 0.4);
      margin-top: 1rem;
      padding: 0 0.4rem;
      width: 100%;

      .icon-search {
        color: $nps-blue;
        font-size: $font-size-sm;
      }

      .input {
        @include stretch;

        border: none;
        font-size: $font-size-xs;
        outline: none;

        &::placeholder {
          color: $nps-blue;
          opacity: 0.5;
        }
      }
    }
  }

  .comments {
    @include scrollbar-thin;

    margin: 1rem 0;
    overflow-y: auto;
    width: 100%;

    .comment {
      @include flex("block", "row", "start", "start");

      cursor: pointer;
      margin-bottom: 1rem;
      position: relative;
      width: 100%;

      span {
        margin-left: 0.4rem;
        margin-right: 0.6rem;
      }

      .topic-count {
        font-size: $font-size-xs;
        margin-left: auto;
        margin-right: 0.4rem;
        position: relative;

        .topic-count-info {
          @include flex("block", "row", "start", "center");

          border: 1px solid rgba(75, 75, 75, 0.14);
          border-radius: 2px;
          cursor: pointer;
          padding: 0 0.3rem;

          //&:hover, &.active {
          //  border: 1px solid $adoreboard-purple;
          //
          //  .icon, .text {
          //    color: $adoreboard-purple;
          //  }
          //}

          .icon {
            font-size: $font-size-xxs;
          }

          .text {
            font-size: $font-size-xxs;
            font-weight: $font-weight-bold;
            padding-left: 0.2rem;
          }
        }
      }

      //.comments-list-item-topics-dropdown {
      //  right: 7px;
      //  top: 26px;
      //}
    }
  }
}
</style>

<style lang="scss">
.themes-intensity-annotation-modal-comment {
  .base-radio-with-tick-mark {
    height: 1.2rem;
    width: 1.2rem;

    .input-checkbox {
      border: 1px solid #131C29;
      height: 17px;
      opacity: 0.17;
      width: 17px;

      &:checked {
        &.background {
          background-color: transparent;
          border: 1px solid #131C29;
          opacity: 1;
        }
      }
    }

    .checked-icon {
      color: #3858FF;
      font-size: 0.6rem;
    }
  }
}
</style>
