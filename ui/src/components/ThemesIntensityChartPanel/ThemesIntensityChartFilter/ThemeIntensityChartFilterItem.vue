<template>
  <section class="theme-intensity-chart-filter-item"
           :class="{ active, disabled }">
    <base-radio-with-tick-mark :value="active"></base-radio-with-tick-mark>
    <span class="text">{{ textColumn }}</span>
  </section>
</template>

<script>
import BaseRadioWithTickMark from '@/components/Base/BaseRadioWithTickMark';
import { mapState } from 'vuex';

export default {
  name: 'theme-intensity-chart-filter-item',
  components: { BaseRadioWithTickMark },
  props: {
    data: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapState('themesChart', ['yPath']),

    textColumn() {
      return `${this.data.content}`;
    },

    active() {
      return this.data.active;
    },

    disabled() {
      return this.data?.disable ? this.data.disable : false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.theme-intensity-chart-filter-item {
  @include flex("block", "row", "start", "center");
  @include stretch;

  border-bottom: 1px solid #DEE1E4;
  background: #FFFFFF;
  border-radius: 2px;
  color: #131C29;
  cursor: pointer;
  font-size: $font-size-xs;
  font-weight: 400;
  line-height: 15px;
  margin: 0 1rem;
  min-height: 1.8rem;
  min-width: 8rem;
  padding: 0.5rem;

  .base-radio-with-tick-mark {
    margin-right: 0.4rem;
  }

  &:hover, &.active {
    //border: 1px solid #131C29;
  }

  &:last-child {
    border-bottom: none;
  }

  &.disabled {
    pointer-events: none;
    color: grey;
  }
}
</style>
