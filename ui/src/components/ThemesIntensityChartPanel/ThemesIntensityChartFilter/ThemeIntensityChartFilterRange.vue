<template>
  <section class="theme-intensity-chart-filter-range"  @click.stop>
    <base-dropdown tooltip-class="tooltip-theme-intensity-dropdown"
                   :data="items"
                   :open="open"
                   @close="open = false"
                   :component="ThemeIntensityChartFilterItem"
                   @select="onSelect" >
      <section class="dropdown-text" @click="open = !open">
        <section class="left">
          <span class="title">Range: </span>
          <span class="text" :class="range.name.toLowerCase()">{{ range.title() }}</span>
        </section>
        <i class="fa fa-caret-down icon icon-right" :class="{ open }"></i>
      </section>
    </base-dropdown>
  </section>
</template>

<script>
import BaseButton from '@/components/Base/BaseButton';
import BaseDropdown from '@/components/Dropdown/BaseDropdown';
import BaseDropdownButton from '@/components/Dropdown/BaseDropdownButton';
import ThemeIntensityChartFilterItem
from '@/components/ThemesIntensityChartPanel/ThemesIntensityChartFilter/ThemeIntensityChartFilterItem';
import { mapActions, mapState } from 'vuex';
import ThemesIntensityRange from '@/enum/themes-intensity-range';
import ThemesIntensityChartFilterAppliedToast
from '@/components/ThemesIntensityChartPanel/Toast/ThemesIntensityChartFilterAppliedToast';

export default {
  name: 'theme-intensity-chart-filter-range',
  components: {
    BaseButton,
    BaseDropdown,
    BaseDropdownButton,
  },

  data() {
    return {
      open: false,
      ThemeIntensityChartFilterItem,
    };
  },

  computed: {
    ...mapState('themesIntensity', ['range']),

    ...mapState('themesChart', ['yPath']),

    items() {
      return ThemesIntensityRange.enumValues.map(e => {
        if (this.range !== ThemesIntensityRange.ALL
            && !this.range.chartPaths().includes(this.yPath)) {
          this.setRange({ range: ThemesIntensityRange.ALL });
        }
        return {
          value: e,
          content: e.text(),
          active: this.range === e,
          disable: e === ThemesIntensityRange.ALL ? false : !e.chartPaths().includes(this.yPath),
        };
      });
    },
  },

  methods: {
    ...mapActions('themesIntensity', ['setRange', 'setFilterType']),

    ...mapActions('toast', {
      addToast: 'add',
    }),

    onSelect(item) {
      this.setRange({ range: item.value });
      this.setFilterType({ filterType: item.value });

      if (ThemesIntensityRange.ALL !== item) {
        this.addToast({
          toast: {
            component: ThemesIntensityChartFilterAppliedToast,
            id: 'themes-intensity-chart-filter-applied-toast',
          },
        });
      }
    },

  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";
.theme-intensity-chart-filter-range {

  @include flex("block", "column", "start", "stretch");

  color: clr("white");
  cursor: pointer;
  font-size: $font-size-xxs;
  font-weight: $font-weight-normal;

  .base-dropdown {

    text-transform: uppercase;
    width: 150px;

    .dropdown-text {
      @include flex("block", "row", "space-between", "center");
      @include stretch();

      background: rgba(19, 28, 41, 0.7);
      border-radius: 2px;
      border: 1px solid #131C29;
      font-size: $font-size-xxs;
      font-weight: 400;
      height: 24px;
      line-height: 15px;
      padding: 0.5rem;

      &:focus, &:hover {
        background: rgba(19, 28, 41, 1);

        .icon {
          opacity: 1;
        }
      }

      .left{
        @include flex("block", "row", "start", "center");

        .title{
          font-weight: bold;
        }

        .text {
          padding: 0.5rem;

          &.positive {
            color:#BCFFB2
          }

          &.negative {
            color:#FFA8A8
          }
        }
      }

      .icon {
        color: white;
        transition: 0.15s all;

        &.open{
          transform: rotate(180deg);
        }
      }

      .icon-right {
        @include flex("block", "row", "end", "end");

        margin-left: 0.3rem;
      }
    }
  }
}
</style>
