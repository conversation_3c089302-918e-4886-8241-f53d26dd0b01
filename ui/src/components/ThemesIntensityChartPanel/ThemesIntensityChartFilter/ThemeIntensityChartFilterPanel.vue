<template>
  <section class="theme-intensity-chart-filter-panel">
  <section class="theme-intensity-chart-filters-arrow"></section>
  <section class="theme-intensity-chart-filters">
    <section class="theme-intensity-chart-filters-left">
        <theme-intensity-chart-filter-type />
        <theme-intensity-chart-filter-range />
        <theme-intensity-chart-filter-intensity />
        <section class="reset-button" v-if="appliedFilter" @click="onClickResetFilters">
          <i class="fa-solid fa-undo icon-undo"></i>
          <span class="reset-button-text">Reset Filters</span>
        </section>
    </section>
    <section class="theme-intensity-chart-filters-right" @click="closeFilter">
      <i
         class="fa-light fa-xmark icon-x"
         v-tooltip.top="{
              class: 'tooltip-base-dark',
              content: 'Close Filter',
              delay: 0,
           }"
      />
    </section>
  </section>
</section>
</template>

<script>

import ThemeIntensityChartFilterType from '@/components/ThemesIntensityChartPanel/ThemesIntensityChartFilter/ThemeIntensityChartFilterType';
import ThemeIntensityChartFilterIntensity
from '@/components/ThemesIntensityChartPanel/ThemesIntensityChartFilter/ThemeIntensityChartFilterIntensity';
import BaseButton from '@/components/Base/BaseButton';
import { mapActions, mapGetters, mapState } from 'vuex';
import ThemesIntensityLevel from '@/enum/themes-intensity-level';
import ThemeIntensityChartFilterRange
from '@/components/ThemesIntensityChartPanel/ThemesIntensityChartFilter/ThemeIntensityChartFilterRange';
import ThemesChartPath from '@/enum/themes-chart-path';
import ThemesIntensityRange from '@/enum/themes-intensity-range';

export default {
  name: 'theme-intensity-chart-filter-panel',

  components: {
    BaseButton,
    ThemeIntensityChartFilterIntensity,
    ThemeIntensityChartFilterRange,
    ThemeIntensityChartFilterType,
  },

  computed: {
    ...mapGetters('themes', [
      'highIntensityPercentNegativeMax',
      'highIntensityPercentNegativeMin',
      'highIntensityPercentPositiveMax',
      'highIntensityPercentPositiveMin',
    ]),

    ...mapGetters('themesIntensity', ['appliedFilter']),

    ...mapState('themes', ['themes']),

    ...mapState('themesIntensity', ['level', 'range']),

    ...mapState('themesChart', ['yPath']),
  },

  methods: {

    ...mapActions('themesIntensity', [
      'setFilterType',
      'setIntensityMaxAndMinByLevel',
      'setIntensityNegativeMax',
      'setIntensityNegativeMin',
      'setIntensityPositiveMax',
      'setIntensityPositiveMin',
      'setLevel',
      'setRange',
    ]),

    ...mapActions('themesChart', ['setYPath']),

    closeFilter() {
      this.$emit('closeFilter');
    },

    onClickResetFilters() {
      this.setFilterType({ filterType: null });
      this.setLevel({ level: ThemesIntensityLevel.HIGH });
      this.setRange({ range: ThemesIntensityRange.ALL });
      this.setYPath({ path: ThemesChartPath.ADORESCORE });
      this.setIntensityMaxAndMinByLevel();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.theme-intensity-chart-filter-panel {

  @include flex("block", "row", "center", "center");

  background-color: #5A6069;
  height: 50px;
  padding: 0 1rem;
  position: relative;

  .theme-intensity-chart-filters {
    @include flex("block", "row", "between", "center");

    margin: 20px 0;
    width: 100%;

    .theme-intensity-chart-filters-left{
      @include flex("block", "row", "start", "center");

      column-gap: 1rem;
      width: 100%;

      .reset-button {
        @include flex("block", "row", "start", "start");

        color: #FFC3F9;
        cursor: pointer;
        letter-spacing: 0.3px;
        text-align: left;
        text-transform: uppercase;

        .icon-undo {
          font-size: 10px;
          font-weight: 400;
          line-height: 10px;
          margin-right: 0.5em;
        }

        .reset-button-text {
          font-size: 0.625rem;
          font-weight: 800;
          line-height: 12px;
        }
      }
    }

    .theme-intensity-chart-filters-right {
      @include flex("block", "row", "end", "center");

      cursor: pointer;
      height: $font-size-base;
      padding-right: 1rem;
      width: max-content;

      .icon-x {
        @include flex("block", "row", "center", "center");

        border-radius: 50%;
        color: #FFFFFF;
        font-size: $font-size-lg;
        height: $font-size-lg;
        margin-right: 0.4rem;
        opacity: 0.4;
        width: $font-size-lg;

        &:hover {
          opacity: 1;
          transition: opacity $interaction-transition-time;
        }
      }
    }
  }

  .theme-intensity-chart-filters-arrow {
    border-bottom-color: #5A6069;
    border-left-color: transparent;
    border-right-color: transparent;
    border-style: solid;
    border-top-color: transparent;
    border-width: 0 10px 10px;
    content: "";
    height: 0;
    left: 155px;
    margin: 5px;
    position: absolute;
    top: -10px;
    width: 0;
  }
}
</style>
