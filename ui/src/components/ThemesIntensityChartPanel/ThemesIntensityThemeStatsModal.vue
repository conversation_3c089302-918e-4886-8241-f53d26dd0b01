<template>
  <section class="themes-intensity-theme-state-modal">
    <section class="header">
      <section class="left">
        <i class="fa-solid fa-message-plus icon"></i>
        <div v-if="editMode">
          Edit Theme Stats for '{{selectedTheme.topicLabel}}' Theme
        </div>
        <div v-else>
          Add Theme Stats for '{{selectedTheme.topicLabel}}' Theme
        </div>
      </section>
      <section class="right ">
        <section class="remove">
          <base-button colour="danger" size="small" type="base" @click="onClickRemove" v-if="editMode">
            <i class="fa-regular fa-ban icon"></i>
            Remove Annotation
          </base-button>
        </section>

      </section>
    </section>
    <section class="body">
      <section class="themes-intensity-theme-state-left">
        <section class="intensity-chart-option">
          <section class="title-display">
            <h4>Show/Hide</h4>
            <span>Show?</span>
          </section>

          <section class="label-display border-bottom" @click="onClickThemeName">
            <span>Theme Name</span>
            <base-checkbox :value="localTheme.showThemeName"></base-checkbox>
          </section>

          <section class="label-display border-bottom" @click="onClickIntensity">
            <span>Intensity</span>
            <base-checkbox :value="localTheme.showIntensity"></base-checkbox>
          </section>

          <section class="label-display border-bottom" @click="onClickVolume">
            <span>Volume</span>
            <base-checkbox :value="localTheme.showVolume"></base-checkbox>
          </section>

          <section class="label-display" @click="onClickScore">
            <span>Adorescore</span>
            <base-checkbox :value="localTheme.showScore"></base-checkbox>
          </section>
        </section>

      </section>

      <section class="themes-intensity-theme-state-right">
        <section class="preview">
          <i class="fa-solid fa-eye icon"></i>
          Preview
        </section>
        <section class="preview-block">
          <section class="preview-block-content">
            <section class="label-display theme-name" v-if="localTheme.showThemeName">
                {{selectedTheme.topicLabel}}
            </section>

            <section class="label-display intensity" v-if="localTheme.showIntensity">
              Intensity:
              <span> {{intensityText}} </span>
            </section>

            <section class="label-display volume"  v-if="localTheme.showVolume">
              Volume:
              <span> {{volumeText}} </span>
            </section>

            <section class="label-display score"  v-if="localTheme.showScore" >
              <datasets-item-adorescore :score="Math.round(selectedTheme.polarity * 100)" :truncateScore="false" :theme="'dark'" />
            </section>
          </section>
        </section>
      </section>

    </section>
    <section class="footer intensity-chart-option">
      <section class="reset">
        <base-button colour="light" size="small" type="link"  @click="onClose">
          Cancel
        </base-button>
      </section>

      <section class="add" @click="onClickSave">
        <base-button :disabled="addDisable" colour="light" size="small" type="base" v-if="editMode">
          <i class="fa-regular fa-check icon"></i>
          Done
        </base-button>
        <base-button colour="light" size="small" type="base" :disabled="addDisable" v-else>
          <i class="fa-regular fa-plus icon"></i>
          Add Annotation
        </base-button>
      </section>
    </section>

  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseCheckbox from '@/components/Base/BaseCheckbox';
import DatasetsItemAdorescore from '@/components/DatasetsItem/DatasetsItemAdorescore';
import ThemesIntensityLevel from '@/enum/themes-intensity-level';

export default {
  name: 'themes-intensity-theme-state-modal',

  components: {
    BaseButton,
    BaseCheckbox,
    DatasetsItemAdorescore,
  },

  data() {
    return {
      localTheme: null,
    };
  },

  created() {
    this.loadThemeStatsAnnotation();
  },

  computed: {

    ...mapState('datasets', ['active']),

    ...mapGetters('datasets', ['get']),

    ...mapState('themesIntensity', ['level']),

    ...mapState('themes', ['customThemes', 'selectedTheme']),

    addDisable() {
      const { showThemeName, showIntensity, showVolume, showScore } = this.localTheme;
      return [
        showThemeName,
        showIntensity,
        showVolume,
        showScore,
      ].filter(Boolean).length === 0;
    },

    editMode() {
      const theme = this.themeStatsAnnotation;
      return (
        theme &&
        ![
          theme.showIntensity,
          theme.showScore,
          theme.showThemeName,
          theme.showVolume,
        ]
          .every(value => this.isUndefinedOrNull(value))
      );
    },

    intensityPercent() {
      const percent = this.selectedTheme.highIntensityPercent;
      switch (this.level) {
        case ThemesIntensityLevel.HIGH:
          return this.selectedTheme.highIntensityPercent;
        case ThemesIntensityLevel.MEDIUM:
          return this.selectedTheme.medIntensityPercent;
        case ThemesIntensityLevel.LOW:
          return this.selectedTheme.lowIntensityPercent;
        default:
      }
      return percent;
    },

    intensityText() {
      return `${this.level.titleCase()} - ${Math.round(this.intensityPercent * 100)}%`;
    },

    volumeText() {
      return `${this.selectedTheme.numOfDocuments} (${Math.round(this.selectedTheme.numOfDocuments / this.totalVolume * 100)}% of dataset)`;
    },

    themeStatsAnnotation() {
      return this.customThemes.find(t => t.id === this.selectedTheme.id);
    },

    totalVolume() {
      return this.get(this.active).documentCount;
    },
  },

  methods: {
    ...mapActions('modal', ['closeModal', 'setModalComponent']),

    ...mapActions('themes', ['addCustomThemes', 'removeCustomThemesStatsAnnotation']),

    ...mapActions('themesIntensity', ['setUpdatingAnnotation']),

    isUndefinedOrNull(value) {
      return value === undefined || value === null;
    },

    loadThemeStatsAnnotation() {
      const theme = this.themeStatsAnnotation;
      if (theme) {
        this.localTheme = {
          ...theme,
          showIntensity: this.isUndefinedOrNull(theme.showIntensity) ? true : theme.showIntensity,
          showScore: this.isUndefinedOrNull(theme.showScore) ? true : theme.showScore,
          showThemeName: this.isUndefinedOrNull(theme.showThemeName) ? true : theme.showThemeName,
          showVolume: this.isUndefinedOrNull(theme.showVolume) ? true : theme.showVolume,
        };
      }
    },

    onClickIntensity() {
      let updatedTheme = { ...this.localTheme };
      updatedTheme = { ...updatedTheme, showIntensity: !updatedTheme.showIntensity };
      this.localTheme = updatedTheme;
    },

    onClickScore() {
      let updatedTheme = { ...this.localTheme };
      updatedTheme = { ...updatedTheme, showScore: !updatedTheme.showScore };
      this.localTheme = updatedTheme;
    },

    onClickThemeName() {
      let updatedTheme = { ...this.localTheme };
      updatedTheme = { ...updatedTheme, showThemeName: !updatedTheme.showThemeName };
      this.localTheme = updatedTheme;
    },

    onClickVolume() {
      let updatedTheme = { ...this.localTheme };
      updatedTheme = { ...updatedTheme, showVolume: !updatedTheme.showVolume };
      this.localTheme = updatedTheme;
    },

    onClickSave() {
      if (this.addDisable) return;
      this.addCustomThemes({ themes: [this.localTheme] });
      this.setUpdatingAnnotation({ updatingAnnotation: true });
      this.closeModal();
    },

    onClickRemove() {
      this.removeCustomThemesStatsAnnotation({ themeId: this.localTheme.id });
      this.setUpdatingAnnotation({ updatingAnnotation: true });
      this.closeModal();
    },

    onClose() {
      this.closeModal();
    },

  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-intensity-theme-state-modal {
  @include panel;
  position: relative;
  width: 800px;

  .header {
    @include flex('block', 'row', 'space-between', 'center');
    border-bottom: $border-light solid $border-color;
    padding: 1.5rem 1.5rem 1rem;

    .left {
      align-items: center;
      color: #131C29;
      display: flex;
      font-family: 'Inter',serif;
      font-size: $font-size-base;
      font-weight: $font-weight-bold;
      line-height: 18px;
    }

    .right {
      @include flex('block', 'column', 'center', 'end');

      .remove {
        .base-button {
          background-color: transparent;
          border-radius: 2px;
          border: 1px solid rgba(251, 56, 47);
          color: rgba(251, 56, 47);
          font-size: $font-size-xxs;
          font-weight: 800;
          text-transform: uppercase;

          &:hover {
            border: 1px solid rgba(251, 56, 47, 0.5);
          }
        }
      }
    }
  }

  .body {
    @include flex('block', 'row', 'start', 'start');

    background: rgba(237, 218, 255, 0.4);
    height: 250px;

    .themes-intensity-theme-state-left {
      background: #FFFFFF;
      height: 100%;
      width: 60%;

      .border-bottom {
        border-bottom: $border-standard;
      }

      .intensity-chart-option  {
        @include flex("block", "column", "center", "stretch");

        padding: 1em 1.5em;

        .title-display{
          @include flex("block", "row", "space-between", "center");

          margin-bottom: 0.5rem;
          margin-top: 0.5rem;
          position: relative;

          h4 {
            color: #131C29;
            font-size: $font-size-sm;
            font-weight: $font-weight-bold;
            letter-spacing: $letter-spacing-xs;
            margin: 0;
          }

          span {
            color: #2114FF;
            font-size: $font-size-xxs;
            font-weight: $font-weight-bold;
            letter-spacing: 0.3px;
            line-height: 12px;
            opacity: 0.7;
            position: absolute;
            right: 0;
            text-align: center;
            text-transform: uppercase;
          }
        }

        .label-display {
          @include flex("block", "row", "start", "center");

          cursor: pointer;
          padding: 0.3rem 0 0.2rem 0.5rem;
          position:relative;

          .base-checkbox {

            border-radius: 2px;
            border: 1px solid #3981F7;
            margin-right: 0.5em;
            pointer-events: none;
            position:absolute;
            right: 0;

            &:checked {
              &.background {
                background-color: #3858FF;
              }
            }
          }

          span {
            color: #131C29;
            font-family: 'Inter',serif;
            font-size: $font-size-xs;
            font-weight: $font-weight-normal;
            line-height: 36px;
          }

        }
      }

    }

    .themes-intensity-theme-state-right {

      @include flex('block', 'column', 'center', 'center');

      background: rgba(237, 218, 255, 0.4);
      height: 100%;
      position: relative;
      width: 40%;

      .preview {
        color: #131C29;
        font-family: 'Inter',serif;
        font-size: $font-size-xxs;
        font-weight: $font-weight-bold;
        left: 0;
        letter-spacing: 0.3px;
        line-height: 12px;
        margin: 20px;
        opacity: 0.88;
        position: absolute;
        text-transform: uppercase;
        top: 0;

        .icon {
          margin-right: 1em;
        }
      }

      .preview-block {
        @include flex('block', 'row', 'start', 'center');

        background: #131C29;
        border-radius: 3px;
        max-height: 112px;
        width: 214px;

        .preview-block-content {
          color: #FFFFFF;
          margin: 1rem 0.3rem;

          .label-display{
            font-size: 11px;
            font-weight: $font-weight-bold;
            padding: 0.3rem 0 0.2rem 0.5rem;

            span {
              font-weight: $font-weight-normal;
            }
          }
        }
      }
    }
  }

  .footer {
    @include flex('block', 'row', 'between', 'center');
    @include rigid;

    border-top: $border-light solid $border-color;
    padding: 1.5rem 1.5rem;
    text-transform: uppercase;

    .add {
      .base-button {
        background: #131C29;
        border: 1px solid rgba(0, 0, 0, 0.3);
        color: #FFFFFF;

        &:hover {
          border: 1px solid #FFFFFF;
        }
      }
    }
  }

  .icon {
    margin-right: 1em;
  }
}
</style>
