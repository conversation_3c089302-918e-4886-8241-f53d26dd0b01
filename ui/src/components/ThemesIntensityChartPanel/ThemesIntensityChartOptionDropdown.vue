<template>
  <section class="themes-intensity-chart-option-dropdown">
    <section class="dropdown-arrow"></section>

    <section class="intensity-chart-option-header border-bottom ">
      <i class="fa-regular fa-square-sliders-vertical icon-slider"/>
      <span class="header">&nbsp;Chart View Options:&nbsp;</span>
      <span class="text">Theme Intensity</span>
    </section>

    <section class="intensity-chart-options">
      <section class="intensity-chart-option border-bottom">
        <section class="title-display">
          <h4>Show/Hide</h4>
          <span>Show?</span>
        </section>

        <section class="label-display" @click="onClickTitle">
          <i class="fa-regular fa-font icon"></i>
          <span>Title</span>
          <base-checkbox :value="showTitle"></base-checkbox>
        </section>
        <!-- Phrase 2 -->
        <section class="label-display" @click="onClickDescription">
          <i class="fa-regular fa-font-case icon"></i>
          <span>Description</span>
          <base-checkbox :value="showDescription"></base-checkbox>
        </section>

        <section class="label-display" @click="onClickBubbleLabels">
          <i class="fa-regular fa-tag icon"></i>
          <span>Theme Label</span>
          <base-checkbox :value="showBubbleLabels"></base-checkbox>
        </section>

        <section class="label-display" @click="onClickAxisLabel">
          <i class="fa-regular fa-font icon"></i>
          <span>Axis Labels</span>
          <base-checkbox :value="showAxisLabel"></base-checkbox>
        </section>

        <section class="label-display" @click="onClickAnnotations">
          <i class="fa-regular fa-message-lines icon"></i>
          <span>Annotations</span>
          <base-checkbox :value="showAnnotations"></base-checkbox>
        </section>

        <section class="label-display" @click="onClickConnectorLines">
          <i class="fa-regular fa-slash icon"></i>
          <span>Annotations: Connector Line</span>
          <base-checkbox :disabled="!showAnnotations" :value="showAnnotations && showConnectorLines"></base-checkbox>
        </section>
      </section>
      <section class="intensity-chart-option border-bottom">
        <h4 class="slider-title">Display Setting  </h4>
        <section class="slider">
          <span class="slider-title">Bubble Size  </span>
          <vue-slider v-model="bubbleSizeModel" v-bind="bubbleSizeSlider" :lazy="true"/>
          <div class="slider-input">
            <base-input :disabled="false" v-model="bubbleSizePercentage" minlength="1" maxlength = "3"></base-input>
            <span>%</span>
          </div>
        </section>
      </section>
      <section class="intensity-chart-option border-bottom">
        <section class="title-display">
          <h4>Axis Labels</h4>
        </section>
        <section class="label-display margin-bottom">
          <span>X-Axis</span>
          <base-input  v-model="xAxisModal" class="axis-input"></base-input>
        </section>
        <section class="label-display margin-bottom">
          <span>Y-Axis</span>
          <base-input  v-model="yAxisModal" class="axis-input"></base-input>
        </section>
      </section>
      <section class="intensity-chart-option border-bottom">
        <section class="title-display">
          <h4>Chart Styling</h4>
        </section>
        <section class="label-display margin-bottom annotation-title">
          <span>Annotation Background</span>
        </section>
        <section class="annotation-buttons">
          <section class="annotation-button" :class="{active: light}">
            <base-button colour="light" size="base" type="base"  @click="onClickAnnotationBg(ThemesIntensityAnnotationBg.LIGHT)">
              Light
            </base-button>
          </section>
          <section class="annotation-button" :class="{active: dark}">
            <base-button colour="light" size="base" type="base" :class="{dark}" @click="onClickAnnotationBg(ThemesIntensityAnnotationBg.DARK)">
              Dark
            </base-button>
          </section>
        </section>
      </section>
    </section>

     <section class="intensity-chart-option action-buttons" >
      <section class="action-buttons reset" @click="onReset">
        <base-button colour="light" size="small" type="base" >
          <i class="fa-regular fa-arrow-rotate-left icon"></i>
          Reset
        </base-button>
      </section>

      <section class="action-buttons done" @click="onClose">
        <base-button colour="light" size="small" type="base">
          <i class="fa-regular fa-check icon"></i>
          Done
        </base-button>
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { MinusIcon, PlusIcon } from 'vue-feather-icons';

import BaseButton from '@/components/Base/BaseButton';
import BaseCheckbox from '@/components/Base/BaseCheckbox';
import BaseInput from '@/components/Base/BaseInput';
import BlurCloseable from '@/components/Mixins/BlurCloseable';
import VueSlider from 'vue-slider-component';
import sliderIntensityStyles from '@/helpers/slider-intensity-styles';
import ThemesIntensityAnnotationBg from '@/enum/themes-intensity-annotation-bg';

export default {
  name: 'themes-intensity-chart-option-dropdown',

  components: {
    BaseButton,
    BaseCheckbox,
    BaseInput,
    MinusIcon,
    PlusIcon,
    VueSlider,
  },

  mixins: [BlurCloseable],

  props: {
  },

  data() {
    return {
      bubbleSizeMin: 5,
      bubbleSizeMax: 25,
      bubbleSizeInterval: 1,
      ThemesIntensityAnnotationBg,
    };
  },

  computed: {
    ...mapState('themesIntensity', {
      bubbleSize: state => state.chartOptions.bubbleSize,
      showAnnotations: state => state.chartOptions.showAnnotations,
      showAxisLabel: state => state.chartOptions.showAxisLabel,
      showBubbleLabels: state => state.chartOptions.showBubbleLabels,
      showConnectorLines: state => state.chartOptions.showConnectorLines,
      showDescription: state => state.chartOptions.showDescription,
      showTitle: state => state.chartOptions.showTitle,
      xAxisLabel: state => state.chartOptions.xAxisLabel,
      yAxisLabel: state => state.chartOptions.yAxisLabel,
      annotationBg: state => state.chartOptions.annotationBg,
    }),

    bubbleSizePercentage: {
      get() {
        return Math.round(this.bubbleSizeModel * 4);
      },

      set(bubbleSizePercent) {
        if (this.validateInputNumber(bubbleSizePercent)) {
          const size = Math.round(bubbleSizePercent / 4);
          this.bubbleSizeModel = Math.min(Math.max(size, this.bubbleSizeMin), this.bubbleSizeMax);
        }
      },
    },

    bubbleSizeModel: {
      get() {
        return +this.bubbleSize;
      },
      set(bubbleSize) {
        this.setBubbleSize({ bubbleSize });
      },
    },

    xAxisModal: {
      get() {
        return this.xAxisLabel;
      },

      set(xAxisLabel) {
        this.setXAxisLabel({ label: xAxisLabel });
      },
    },

    yAxisModal: {
      get() {
        return this.yAxisLabel;
      },

      set(yAxisLabel) {
        this.setYAxisLabel({ label: yAxisLabel });
      },
    },

    bubbleSizeSlider() {
      return {
        ...this.sliderBase,
        min: this.bubbleSizeMin,
        max: this.bubbleSizeMax,
        interval: this.bubbleSizeInterval,
      };
    },

    sliderBase() {
      return {
        ...sliderIntensityStyles,
        width: '150px',
      };
    },

    light() {
      return this.annotationBg === ThemesIntensityAnnotationBg.LIGHT.name;
    },

    dark() {
      return this.annotationBg === ThemesIntensityAnnotationBg.DARK.name;
    },
  },

  methods: {
    ...mapActions('themesIntensity', {
      resetChartOptions: 'resetChartOptions',
      setAnnotationBg: 'setAnnotationBg',
      setBubbleSize: 'setBubbleSize',
      setShowAnnotations: 'setShowAnnotations',
      setShowAxisLabel: 'setShowAxisLabel',
      setShowBubbleLabels: 'setShowBubbleLabels',
      setShowConnectorLines: 'setShowConnectorLines',
      setShowDescription: 'setShowDescription',
      setShowTitle: 'setShowTitle',
      setXAxisLabel: 'setXAxisLabel',
      setYAxisLabel: 'setYAxisLabel',
    }),

    onClickAnnotationBg(data) {
      this.setAnnotationBg({ annotationBg: data.name });
    },

    onClickAnnotations() {
      this.setShowAnnotations({ show: !this.showAnnotations });
    },

    onClickAxisLabel() {
      this.setShowAxisLabel({ show: !this.showAxisLabel });
    },

    onClickDescription() {
      this.setShowDescription({ show: !this.showDescription });
    },

    onClickBubbleLabels() {
      this.setShowBubbleLabels({ show: !this.showBubbleLabels });
    },

    onClickConnectorLines() {
      this.setShowConnectorLines({ show: !this.showConnectorLines });
    },

    onClickTitle() {
      this.setShowTitle({ show: !this.showTitle });
    },

    onClose() {
      this.$emit('close');
    },

    onReset() {
      this.resetChartOptions();
    },

    validateInputNumber(val) {
      const rg = new RegExp(/^([+-]?\d+)(\.\d+)?$/, 'g');
      return val.match(rg);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-intensity-chart-option-dropdown {
  @include flex("block", "column", "start", "stretch");

  background: #FFFFFF;
  border: 1px solid rgba(19, 28, 41, 0.5);
  color: #131C29;
  font-size: $font-size-xs;
  max-height: 450px;
  padding: 0 0 0.5rem;
  position: absolute;
  right: 50px;
  top: 3rem;
  width: 405px;
  z-index: 99;

  .annotation-title {
    color: $nps-blue;
    cursor: none;
    font-family: 'Inter',serif;
    font-size: $font-size-xs;
    font-style: normal;
    font-weight: $font-weight-bold;
    letter-spacing: $letter-spacing-xs;
    line-height: 15px;
    opacity: 0.7;
    pointer-events: none;
    text-transform: uppercase;
  }

  .annotation-buttons {
    @include flex("block", "row", "start", "center");

    margin-bottom: 0.8rem;

    .base-button {
      font-family: 'Inter',serif;
      font-size: 10px;
      font-style: normal;
      font-weight: 800;
      letter-spacing: 0.3px;
      line-height: 12px;
      padding: 0.4rem 2.5rem;
      text-align: center;
      text-transform: uppercase;
    }

    .annotation-button {

      margin-right: 1rem;

      .base-button {

        background-color: transparent;
        border-radius: 2px;
        border: 1px solid rgba(#131C29, 0.8);
        color: #131C29;
        opacity: 0.5;

        &:hover {
          border: 1px solid #131C29;
        }
      }

      &.active {
        .base-button{
          background-color: rgba(19, 28, 41, 0.7);
          border-radius: 2px;
          border: 1px solid #131C29;
          color: #FFFFFF;
          opacity: 1;

          &:hover{
            background-color: rgba(19, 28, 41);
          }
        }
      }
    }
  }

  .border-bottom {
    border-bottom: $border-standard;
  }

  .dropdown-arrow {
    border-bottom: 7px solid white;
    border-left: 7px solid transparent;
    border-right: 7px solid transparent;
    border-top: none;
    filter: drop-shadow(0px -1px 0px rgba(19, 28, 41, 0.5));
    position: absolute;
    right: 280px;
    top: -7px;
  }

  .label-display {
    @include flex("block", "row", "start", "center");

    cursor: pointer;
    margin: 0.5rem 0 0.5rem 0;
    padding: 0.3rem 0 0.2rem 0.5rem;
    position: relative;

    .axis-input {
      margin-left: 2rem;
      position: absolute;
      right: 0;
      width: 80%
    }

    .base-checkbox {

        border-radius: 2px;
        border: 1px solid #3981F7;
        margin-right: 0.5em;
        pointer-events: none;
        position:absolute;
        right: 0;

        &:checked {
          &.background {
            background-color: #3858FF;
          }
        }
    }

    .icon {
      margin-right: 0.2rem;
      width: 1rem;
    }

    span {
      font-size: $font-size-xs;
    }

  }

  .title-display{
    @include flex("block", "row", "space-between", "center");

    position: relative;
    margin-top: 0.5rem;

    span {
      color: #2114FF;
      font-size: 10px;
      font-weight: 800;
      letter-spacing: 0.3px;
      line-height: 12px;
      opacity: 0.7;
      position: absolute;
      right: 0;
      text-align: center;
      text-transform: uppercase;
    }
  }

  .slider {
    @include flex("block", "row", "space-between", "center");

    cursor: pointer;
    padding: 0.3rem 0 0.2rem 0.5rem;
    margin: 0 0 0.5rem 0;
    position: relative;

    .slider-input {
      @include flex("block", "row", "center", "center");

      margin-right: 0.5em;
      width: 4.5em;

      .base-input {
        border-radius: $border-radius-small;
        border: $border-standard;
        border: 1px solid #dee1e4;
        margin-right: 0.5em;
        padding: 0.3rem;
        text-align: center;
        width: 3rem;
      }

      span {
        margin-right: 1em;
      }

    }

    .icon {
      border-radius: 50%;
      border: 1px solid $toggle-button-ball-border;
      height: 0.8rem;
      margin-right: 0.4rem;
      width: 0.8rem;

      &:hover {
        background-color: $search-dropdown-icon-bg-hover;
        color: darken($insights-btn-c-bg, 10%);
      }
    }
  }

  .slider-title {
    margin: 0.5rem 0 0.3rem;
  }

  .intensity-chart-options {
    @include scrollbar-thin;
    overflow-x: hidden;
    overflow-y: auto;
  }

  .intensity-chart-option {
    padding: 0.3rem 1.3rem 0;

    .margin-bottom {
      margin-bottom: 1rem;
    }
  }

  .action-buttons {
    @include flex("block", "row", "space-between", "center");

    margin: 0.3rem 0;
    text-transform: uppercase;

    .action-button{
      @include flex("block", "row", "center", "center");
    }

    .reset {
      .base-button {
        background: #FFFFFF;
        border: 1px solid rgba(0, 0, 0, 0.3);
        color: #131C29;
        font-size: $font-size-xxs;
        padding: 0.3rem 0.5rem;

        &:hover {
          border: 1px solid  #131C29;
        }
      }
    }

    .done {
      .base-button {
        background: #141414;
        border: 1px solid rgba(0, 0, 0, 0.3);
        color: #FFFFFF;
        font-size: $font-size-xxs;
        padding: 0.3rem 0.5rem;

        &:hover {
          border: 1px solid  #FFFFFF;
        }
      }
    }

    .icon {
      margin-right: 0.2rem;
    }
  }

  .intensity-chart-option-header{
    @include flex("block", "row", "start", "center");

    padding: 1.2rem 2rem;
    color: #131C29;
    font-size: $font-size-base;

    .header {
      font-weight: $font-weight-bold;
    }

    .text {
      font-weight: $font-weight-normal;
    }

    .icon-slider {
      margin-right: 0.2rem;
    }
  }

  h3 {
    color: #131C29;
    font-size: $font-size-xs;
    font-weight: $font-weight-bold;
    letter-spacing: $letter-spacing-xs;
    margin: 1em 0 0 0;
  }

  h4 {
    color: #131C29;
    font-size: $font-size-xs;
    font-weight: $font-weight-bold;
    letter-spacing: $letter-spacing-xs;
    margin: 0;
  }
}
</style>
