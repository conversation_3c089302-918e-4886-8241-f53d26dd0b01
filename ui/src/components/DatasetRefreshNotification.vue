<template>
  <section class="dataset-refresh-notification">
    <h3 v-if="selectedCount > 1">Some selected datasets have pending changes which may affect the scores.</h3>
    <h3 v-else-if="isFinished">This dataset has pending changes which may affect the scores.</h3>
    <h3 v-else>This dataset is being re-processed.</h3>
    <base-button icon="refresh-ccw" size="small" type="link" @click="refreshAll">Refresh</base-button>
  </section>
</template>

<script>
import { RefreshCcwIcon } from 'vue-feather-icons';
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';

import { datasetApi } from '@/services/api';
import { datasetsRequest, datasetsRequestV0, metadataRequest, themesRequest } from '@/services/request';

export default {
  name: 'dataset-refresh-notification',

  components: {
    BaseButton,
    RefreshCcwIcon,
  },

  computed: {
    ...mapGetters('datasets', [
      'getAllPendingChanges',
      'isFinished',
      'selectedCount',
      'selectedLimited',
    ]),

    ...mapState('datasets', { datasetId: 'active' }),

    ...mapGetters('snippetsFilter', ['getActiveChildDatasetId', 'isViewingChildDataset']),

    ...mapState('themes', ['type']),
  },

  methods: {
    ...mapActions('datasets', ['setOverviews']),

    ...mapActions('themes', ['setThemes']),

    async refreshAll() {
      await datasetApi.refresh(this.getAllPendingChanges);
      await datasetsRequestV0.getDatasets();
      await datasetsRequestV0.reloadSelected();

      const overviews = await datasetsRequest.fetchOverviews(this.selectedLimited, this.type.lowerCase());
      const themes = await themesRequest.fetchThemes();

      this.setOverviews({ overviews });
      this.setThemes({ themes });

      // call this after reloadSelected() / getDatasets() / reloadOverviews...
      // reload child filter-views after parent-dataset
      if (this.isViewingChildDataset) {
        await datasetApi.refresh([this.getActiveChildDatasetId]);
        await metadataRequest.getAllFilterViewsIntoLocalDatasetsList(this.datasetId);
      }
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.dataset-refresh-notification {
  @include flex("block", "row", "start", "center");
  @include rigid;

  background-color: clr("orange");
  color: clr("white");
  padding: 0.4em 1.4em;
  position: sticky;
  top: 0px;
  z-index: 9999;

  .base-button {
    &.size-small {
      padding: 0.3rem 0.5rem;
      margin: 0;
    }

    span {
      color: rgba(clr("white"), 0.6);
      font-size: $font-size-xs;
      transition: all $interaction-transition-time;

      &:hover {
        color: clr("white");
      }

      .base-icon {
        height: $font-size-sm;
        margin-right: 0;
      }
    }
  }

  h3 {
    font-size: $font-size-sm;
    margin: 0;
  }
}
</style>
