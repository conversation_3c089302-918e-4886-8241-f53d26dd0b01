<template>
  <section class="storyteller-slides-preview-btn" @click="onClickPreviewBtn">
    <base-button size="small" >
      <i class="fa-solid fa-eye icon-eye" />
      <span class="text">Preview</span>
    </base-button>
  </section>
</template>

<script>
import { mapActions } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import StorytellerSlidesPreview from '@/components/StorytellerSlidesPreview/StorytellerSlidesPreview';

export default {
  name: 'storyteller-slides-preview-btn',

  components: {
    BaseButton,
  },

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    onClickPreviewBtn() {
      this.setModalComponent({ component: StorytellerSlidesPreview });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slides-preview-btn {
  @include flex("block", "row", "start", "center");

  .base-button {
    @include flex("block", "row", "start", "center");

    background-color: rgba(75, 46, 255, 0.9);
    font-size: $font-size-xxs;
    padding: 0.4rem 0.6rem;

    .icon-eye {
      margin-right: 0.4rem;
    }

    .text {
      font-weight: 800;
      letter-spacing: 0.3px;
      text-transform: uppercase;
    }

    &:hover, &:focus {
      background-color: rgba(75, 46, 255, 1);
    }
  }
}
</style>
