<template>
  <section class="storyteller-slides-header-right">
    <storyteller-slides-download-btn class="btn" />
    <storyteller-slides-preview-btn class="btn" />
<!--    <storyteller-slides-share-btn class="btn" />-->
  </section>
</template>

<script>
import StorytellerSlidesDownloadBtn from '@/components/StorytellerSlidesHeader/StorytellerSlidesDownloadBtn';
import StorytellerSlidesPreviewBtn from '@/components/StorytellerSlidesHeader/StorytellerSlidesPreviewBtn';
import StorytellerSlidesShareBtn from '@/components/StorytellerSlidesHeader/StorytellerSlidesShareBtn';

export default {
  name: 'storyteller-slides-header-right',

  components: {
    StorytellerSlidesDownloadBtn,
    StorytellerSlidesPreviewBtn,
    StorytellerSlidesShareBtn,
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slides-header-right {
  @include flex("block", "row", "start", "center");

  .btn {
    margin-left: 0.4rem;
  }
}
</style>
