<template>
  <section class="storyteller-slides-download-dropdown" @click.stop :class="{top: topPosition}">
    <section class="header">
      <section class="title">
        <i class="fa-regular fa-download icon-download" />
        <span>Download Presentation</span>
      </section>
      <section class="options">
        <section class="option option-full" @click="viewing = 'FULL_DECK'" :class="{active: isViewingFullDeck}">
          <i class="fa-light fa-rectangle-history icon-option" />
          <span>Full Deck</span>
        </section>
        <section class="option option-single" @click="viewing = 'SINGLE_SLIDE'" :class="{active: isViewingSingleSlide}">
          <i class="fa-regular fa-rectangle icon-option" />
          <span>Single Slide</span>
        </section>
      </section>
    </section>
    <section class="loading" v-show="downloading">
      <img src="@/assets/adoreboard-loader.gif" alt="loader" class="loader" />
      <section class="loading-text">Hang tight! We’re preparing your download, this may take up to a minute.</section>
    </section>
    <section v-show="!downloading" class="body">
      <section class="item-list" v-if="isViewingFullDeck">
        <section class="item" @click="onClickDownloadPptDeck">
          <i class="fa-light fa-file-ppt"></i>
          <span>Powerpoint Deck</span>
        </section>
        <section class="item" @click="onClickDownloadPngDeck">
          <i class="fa-light fa-file-png" />
          <span>PNG Deck</span>
        </section>
        <section class="item" @click="onClickDownloadPdfDeck">
          <i class="fa-light fa-file-pdf" />
          <span>PDF Deck</span>
        </section>
      </section>
      <section class="item-list" v-if="isViewingSingleSlide">
        <section class="item" @click="onClickDownloadPptSlide">
          <i class="fa-light fa-file-ppt"></i>
          <span>Powerpoint Slide</span>
        </section>
        <section class="item" @click="onClickDownloadPngSlide">
          <i class="fa-light fa-file-png" />
          <span>PNG Slide</span>
        </section>
        <section class="item" @click="onClickDownloadPdfSlide">
          <i class="fa-light fa-file-pdf" />
          <span>PDF Slide</span>
        </section>
      </section>
      <section class="item-list blank" v-if="isViewingSingleSlide">
        <section class="title">Blank Slide</section>
        <section class="item" @click="onClickDownloadBlankPptSlide">
          <i class="fa-light fa-file-ppt"></i>
          <span>Powerpoint Blank</span>
        </section>
        <section class="item" @click="onClickDownloadBlankPngSlide">
          <i class="fa-light fa-file-png" />
          <span>PNG Blank</span>
        </section>
        <section class="item" @click="onClickDownloadBlankPdfSlide">
          <i class="fa-light fa-file-png" />
          <span>PDF Blank</span>
        </section>
      </section>
    </section>
    <section class="hidden-slides" ref="hiddenSlides">
      <component v-for="(slide, index) in localActiveSlides"
        class="slide"
        :id="`png-slide-${slide.id}`"
        :key="index"
        :is="slideComponent(slide)"
        :slide-data="slide.slideData"
        :slide-width="1280"
      />
    </section>
    <vue-html2pdf
      :enable-download="true"
      :filename="reportName"
      :float-layout="true"
      :manual-pagination="true"
      :pdf-quality="2"
      :preview-modal="true"
      :show-layout="false"
      pdf-format="a4"
      pdf-orientation="landscape"
      ref="html2Pdf"
    >
      <section slot="pdf-content" v-for="(slide, index) in currentChunkSlides"
               :key="`slide-${slide.id}`"
      >
        <section class="pdf-content-wrapper"
                 ref="pdfContentWrapper"
                 :ref="`pdfContentWrapper-${index}`"
          :style="{ width: pdfWidth + 'px', height: pdfHeight + 'px', display: 'flex', justifyContent: 'center', alignItems: 'center' }"
        >
          <storyteller-slide-blank v-if="downloadingBlank && isBlank(slide)"
            :style="pdfStyle()"
            :slide-width="pdfSlideWidth"
          />
          <storyteller-slide-intro v-else-if="isIntro(slide)"
            :style="pdfStyle()"
            :slide-data="slide.slideData"
            :slide-width="pdfSlideWidth"
          />
          <storyteller-slide-themes-insights v-else-if="isThemesInsights(slide)"
            :style="pdfStyle()"
            :slide-data="slide.slideData"
            :slide-width="pdfSlideWidth"
          />
          <storyteller-slide-flow v-else-if="isPresentationFlow(slide)"
            :style="pdfStyle()"
            :slide-data="slide.slideData"
            :slide-width="pdfSlideWidth"
          />
          <storyteller-slide-insight v-else-if="isInsight(slide)"
            :style="pdfStyle()"
            :slide-data="slide.slideData"
            :slide-width="pdfSlideWidth"
          />
          <storyteller-slide-did-you-know v-else-if="isDidYouKnow(slide)"
            :style="pdfStyle()"
            :slide-data="slide.slideData"
            :slide-width="pdfSlideWidth"
          />
          <storyteller-slide-implication v-else-if="isImplication(slide)"
            :style="pdfStyle()"
            :slide-data="slide.slideData"
            :slide-width="pdfSlideWidth"
          />
          <storyteller-slide-comments v-else-if="isComments(slide)"
            :style="pdfStyle()"
            :slide-data="slide.slideData"
            :slide-width="pdfSlideWidth"
          />
          <storyteller-slide-action-plans v-else-if="isActionPlans(slide)"
            :style="pdfStyle()"
            :slide-data="slide.slideData"
            :slide-width="pdfSlideWidth"
          />
        </section>
      </section>
    </vue-html2pdf>
  </section>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import { saveAs } from 'file-saver';

import getSlideComponent from '@/helpers/storyteller-utils';
import html2canvas from 'html2canvas';
import html2pdf from 'html2pdf.js';
import { PDFDocument } from 'pdf-lib';
import PptxGenJS from 'pptxgenjs';
import StorytellerSlideActionPlans from '@/components/StorytellerSlideActionPlans/StorytellerSlideActionPlans';
import StorytellerSlideBlank from '@/components/StorytellerSlides/StorytellerSlideBlank';
import StorytellerSlideComments from '@/components/StorytellerSlideComments/StorytellerSlideComments';
import StorytellerSlideDidYouKnow from '@/components/StorytellerSlideDidYouKnow/StorytellerSlideDidYouKnow';
import StorytellerSlideFlow from '@/components/StorytellerSlideFlow/StorytellerSlideFlow';
import StorytellerSlideImplication from '@/components/StorytellerSlides/StorytellerSlideImplication';
import StorytellerSlideInsight from '@/components/StorytellerSlides/StorytellerSlideInsight';
import StorytellerSlideIntro from '@/components/StorytellerSlideIntro/StorytellerSlideIntro';
import StorytellerSlideThemesInsights from '@/components/StorytellerSlideThemesInsights/StorytellerSlideThemesInsights';
import StorytellerSlideType from '@/enum/storyteller-slide-type';
import VueHtml2pdf from 'vue-html2pdf';
import Vue from 'vue';
import StorytellerRequest from '@/services/request/StorytellerRequest';

export default {
  name: 'storyteller-slides-download-dropdown',

  components: {
    StorytellerSlideActionPlans,
    StorytellerSlideBlank,
    StorytellerSlideComments,
    StorytellerSlideDidYouKnow,
    StorytellerSlideFlow,
    StorytellerSlideImplication,
    StorytellerSlideInsight,
    StorytellerSlideIntro,
    StorytellerSlideThemesInsights,
    VueHtml2pdf,
  },

  props: {
    topPosition: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      downloading: false,
      downloadPdfDeck: false,
      viewing: 'FULL_DECK', // FULL_DECK or SINGLE_SLIDE
      blankSlide: {
        id: -1,
        slideData: {
          alias: 'blank',
        },
        slideOrder: 0,
        slideType: 'BLANK',
      },
      downloadingBlank: false,
      currentChunkSlides: [], // Used to display current chunk in vue-html2pdf

    };
  },

  computed: {
    ...mapGetters('datasets', ['get']),

    ...mapState('datasets', ['active']),

    ...mapGetters('storyteller', ['activeSlides']),

    ...mapState('storyteller', ['activeReport', 'selectedSlide']),

    dataset() {
      return this.get(this.active);
    },

    isViewingFullDeck() {
      return this.viewing === 'FULL_DECK';
    },

    isViewingSingleSlide() {
      return this.viewing === 'SINGLE_SLIDE';
    },

    localActiveSlides() {
      if (this.downloadingBlank) {
        this.blankSlide.slideOrder = this.activeSlides.length + 1;
        return [...this.activeSlides, this.blankSlide];
      }
      return this.activeSlides;
    },

    pdfHeight() {
      return 794; // 794 pixels are default height of pdf
    },

    pdfSlideHeight() {
      return this.pdfSlideWidth * 9 / 16;
    },

    pdfSlideWidth() {
      return this.pdfWidth;
    },

    pdfWidth() {
      return 1123; // 1121 pixels are default width of pdf
    },

    reportName() {
      return this.activeReport.settings.reportName;
    },
  },

  methods: {
    pdfStyle() {
      return {
        height: `${this.pdfSlideHeight}px`,
        fontFamily: 'Inter',
        width: `${this.pdfSlideWidth}px`,
      };
    },

    async onClickDownloadPngDeck() {
      await this.onClickDownloadDesk('png');
    },

    async onClickDownloadPngSlide() {
      this.downloading = true;
      await new Promise(resolve => setTimeout(resolve, 100));

      const element = document.getElementById(`png-slide-${this.selectedSlide.id}`);
      const name = `${this.selectedSlide.slideOrder}_Storyteller_Slide_${this.selectedSlide.slideType}_${this.selectedSlide.slideData.alias}.png`;

      const canvas = await html2canvas(element);
      const pngDataUrl = canvas.toDataURL('image/png');
      const downloadLink = document.createElement('a');
      downloadLink.href = pngDataUrl;
      downloadLink.download = name;
      downloadLink.click();

      this.downloading = false;
      this.onClose();
    },

    async onClickDownloadBlankPngSlide() {
      this.downloading = true;
      this.downloadingBlank = true;

      await new Promise(resolve => setTimeout(resolve, 100));

      const element = document.getElementById('png-slide--1');
      const name = `${this.blankSlide.slideOrder}_Storyteller_Slide_${this.blankSlide.slideType}.png`;

      const canvas = await html2canvas(element);
      const pngDataUrl = canvas.toDataURL('image/png');
      const downloadLink = document.createElement('a');
      downloadLink.href = pngDataUrl;
      downloadLink.download = name;
      downloadLink.click();

      this.downloading = false;
      this.downloadingBlank = false;

      this.onClose();
    },

    async onClickDownloadPdfDeck() {
      await this.onClickDownloadDesk('pdf');
    },

    async onClickDownloadPdfSlide() {
      this.downloading = true;
      await new Promise(resolve => setTimeout(resolve, 100));

      await this.$refs.html2Pdf.generatePdf();

      this.downloading = false;
      this.onClose();
    },

    async onClickDownloadBlankPdfSlide() {
      this.downloadingBlank = true;
      await this.onClickDownloadPdfSlide();
      this.downloadingBlank = false;
    },

    async onClickDownloadPptDeck() {
      await this.onClickDownloadDesk('ppt');
    },

    async onClickDownloadPptSlide() {
      this.downloading = true;
      await new Promise(resolve => setTimeout(resolve, 100));

      const pptx = new PptxGenJS();

      const addSlideWithImage = async (element) => {
        const canvas = await html2canvas(element);
        const png = canvas.toDataURL('image/png');
        const slide = pptx.addSlide();
        slide.addImage({
          data: png,
          x: 0,
          y: 0,
          w: '100%',
          h: '100%',
        });
      };

      const selectedEl = document.getElementById(`png-slide-${this.selectedSlide.id}`);
      await addSlideWithImage(selectedEl);

      await pptx.writeFile(this.reportName);

      this.downloading = false;
      this.onClose();
    },

    async onClickDownloadBlankPptSlide() {
      this.downloading = true;
      this.downloadingBlank = true;
      await new Promise(resolve => setTimeout(resolve, 100));

      const pptx = new PptxGenJS();

      const addSlideWithImage = async (element) => {
        const canvas = await html2canvas(element);
        const png = canvas.toDataURL('image/png');
        const slide = pptx.addSlide();
        slide.addImage({
          data: png,
          x: 0,
          y: 0,
          w: '100%',
          h: '100%',
        });
      };

      const selectedEl = document.getElementById('png-slide--1');
      await addSlideWithImage(selectedEl);

      await pptx.writeFile(this.reportName);

      this.downloading = false;
      this.downloadingBlank = false;

      this.onClose();
    },

    async onClickDownloadDesk(type) {
      this.downloading = true;
      this.downloadPdfDeck = true;
      // Step 1: Select the HTML content that will be converted into PDF
      // const element = this.$refs.html2Pdf.$el.innerHTML; // Ensure this contains the entire slide deck content
      try {
      // Step 1: Split slides into chunks of 7
        const slideChunks = this.chunkArray(this.localActiveSlides, 7);
        const pdfBlobs = [];
        for (let i = 0; i < slideChunks.length; i += 1) {
          // eslint-disable-next-line no-await-in-loop
          const pdfBlob = await this.generateChunkPdf(slideChunks[i], i);
          pdfBlobs.push(pdfBlob);
        }
        // Step 2: Merge the PDFs into one
        const mergedPdfBlob = await this.mergePdfBlobs(pdfBlobs);
        // Step 3: Send pdf and download from sever
        await this.sendAndDownload(mergedPdfBlob, type);
      } finally {
        this.downloadPdfDeck = false;
        this.downloading = false;
        this.onClose();
      }
    },

    onClose() {
      this.$emit('close');
    },

    isActionPlans(slide) {
      return slide.slideType === StorytellerSlideType.ACTION_PLANS.name;
    },

    isIntro(slide) {
      return slide.slideType === StorytellerSlideType.INTRO.name;
    },

    isThemesInsights(slide) {
      return slide.slideType === StorytellerSlideType.THEMES_INSIGHTS.name;
    },

    isPresentationFlow(slide) {
      return slide.slideType === StorytellerSlideType.PRESENTATION_FLOW.name;
    },

    isInsight(slide) {
      return slide.slideType === StorytellerSlideType.INSIGHT.name;
    },

    isDidYouKnow(slide) {
      return slide.slideType === StorytellerSlideType.DID_YOU_KNOW.name;
    },

    isImplication(slide) {
      return slide.slideType === StorytellerSlideType.IMPLICATION.name;
    },

    isComments(slide) {
      return slide.slideType === StorytellerSlideType.COMMENTS.name;
    },

    isBlank(slide) {
      return slide.id === -1;
    },

    slideComponent(slide) {
      return getSlideComponent(slide.slideType);
    },

    activeSlidesPngFileName() {
      // map localActiveSlides to slide file names
      return this.localActiveSlides.map((slide) => {
        return `${slide.slideOrder}_Storyteller_Slide_${slide.slideType}_${slide.slideData.alias}.png`;
      });
    },

    chunkArray(array, chunkSize) {
      const result = [];
      for (let i = 0; i < array.length; i += chunkSize) {
        result.push(array.slice(i, i + chunkSize));
      }
      return result;
    },

    async generateChunkPdf(slideChunk, chunkIndex) {
      this.currentChunkSlides = slideChunk;
      await new Promise(resolve => setTimeout(resolve, 100)); // Delay to allow rendering
      await Vue.nextTick(); // Wait for Vue to update the DOM
      // Create a container to hold the elements for the chunk
      const ref = this.$refs.html2Pdf.$el.innerHTML;
      const options = {
        filename: `${this.reportName}_chunk_${chunkIndex + 1}.pdf`,
        image: { type: 'jpeg', quality: 1 },
        html2canvas: { scale: 2 },
        jsPDF: { unit: 'pt', format: 'a4', orientation: 'landscape' },
      };

      return html2pdf()
        .set(options)
        .from(ref)
        .outputPdf('blob');
    },

    async mergePdfBlobs(pdfBlobs) {
      const combinedPdf = await PDFDocument.create();
      // eslint-disable-next-line no-restricted-syntax
      for (const pdfBlob of pdfBlobs) {
        // eslint-disable-next-line no-await-in-loop
        const pdfBytes = await pdfBlob.arrayBuffer();
        // eslint-disable-next-line no-await-in-loop
        const pdfDoc = await PDFDocument.load(pdfBytes);
        // eslint-disable-next-line no-await-in-loop
        const copiedPages = await combinedPdf.copyPages(pdfDoc, pdfDoc.getPageIndices());
        copiedPages.forEach(page => combinedPdf.addPage(page));
      }

      const mergedPdfBytes = await combinedPdf.save();
      return new Blob([mergedPdfBytes], { type: 'application/pdf' });
    },

    async sendAndDownload(mergedPdfBlob, type) {
      // Step 3: Create a FormData object to send the PDF to the server
      const formData = new FormData();
      formData.append('file', mergedPdfBlob, `${this.reportName}`);
      formData.append('extractTo', type);
      formData.append('pngFileNames', this.activeSlidesPngFileName());

      // Step 4: Upload the final PDF
      const datasetId = this.dataset.id;
      const reportId = this.activeReport.id;

      const response = await StorytellerRequest.downloadReport(datasetId, reportId, formData);

      if (response.status === 200) {
        const res = new Blob([response.data], { type: 'application/zip' });
        saveAs(res, `${this.reportName}_${type}.zip`);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.storyteller-slides-download-dropdown {
  @include flex("block", "column", "start", "stretch");

  background-color: clr("white");
  border-radius: $border-radius-medium;
  box-shadow: 0 2px 4px 0 rgba(clr("black"), 0.3);
  color: $nps-blue;
  font-size: $font-size-xs;
  position: absolute;
  right: 0;
  top: 34px;
  width: 238px;
  z-index: 99;

  &.top {
    bottom: 34px;
    top: unset;
  }

  .header {
    @include flex("block", "column", "start", "stretch");

    padding: 1rem;

    .title {
      @include flex("block", "row", "start", "stretch");

      font-size: 13px;
      font-weight: $font-weight-bold;

      .icon-download {
        margin-right: 0.3rem;
      }
    }

    .options {
      @include flex("block", "row", "space-between", "center");

      margin-top: 1rem;

      .option {
        border-radius: 2px;
        border: 1px solid rgba(55, 148, 255, 0.3);
        color: rgba(55, 148, 255, 0.75);
        cursor: pointer;
        font-size: 10px;
        font-weight: $font-weight-bold;
        padding: 0.3rem 0.5rem;
        text-transform: uppercase;

        .icon-option {
          margin-right: 0.2rem;
        }

        &:hover {
          border: 1px solid #3794FF;
        }

        &.active {
          background-color: #3794FF;
          color: clr('white');
        }
      }
    }
  }

  .loading {
    @include flex("block", "column", "start", "center");

    padding-bottom: 1rem;
    width: 100%;

    .loader {
      width: 34px;
    }

    .loading-text {
      color: #939393;
      font-size: $font-size-xs;
      line-height: 17px;
      padding: 1rem;
    }
  }

  .body {
    height: 100%;
    width: 100%;
  }

  .item-list {
    border-top: 1px solid rgba(66, 73, 84, 0.2);
    padding: 1rem;

    &.blank {
      background-color: rgba(241, 241, 241, 0.4);
    }

    .title {
      color: rgba(19, 28, 41, 0.45);
      font-weight: $font-weight-extra-bold;
      margin-bottom: 0.5rem;
      text-transform: uppercase;
    }

    .item {
      border-radius: 2px;
      cursor: pointer;
      padding: 0.5rem;

      &:hover {
        background-color: #E6EAFF;
      }
    }
  }

  .hidden-slides {
    position: absolute;
    right: 9999px;
    bottom: 9999px;

    .slide {
      height: 720px;
      width: 1280px;
    }
  }
}
</style>
