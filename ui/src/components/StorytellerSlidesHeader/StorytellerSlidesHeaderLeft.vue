<template>
  <section class="storyteller-slides-header-left">
    <section class="header-left">
      <span><b>Editing:&nbsp;</b></span>
      <i class="fa-light fa-book"></i>
      <span>&nbsp;Insight Storyteller:&nbsp;</span>
      <i v-if="isIntro" class="fa-light fa-rectangle" />
      <i v-if="isThemesInsights" class="fa-light fa-list" />
      <span>{{alias}}</span>
    </section>
  </section>
</template>

<script>

import { mapState } from 'vuex';
import StorytellerSlideType from '@/enum/storyteller-slide-type';

export default {
  name: 'storyteller-slides-header-left',

  computed: {
    ...mapState('storyteller', ['selectedSlide']),

    alias() {
      return this.selectedSlide.slideData.alias;
    },

    isIntro() {
      return this.selectedSlide.slideType === StorytellerSlideType.INTRO.name;
    },

    isThemesInsights() {
      return this.selectedSlide.slideType === StorytellerSlideType.THEMES_INSIGHTS.name;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slides-header-left {
  @include flex("block", "row", "start", "center");

  overflow: hidden;
  white-space: nowrap;
  font-size: $font-size-sm;
}
</style>
