<template>
  <section class="storyteller-slides-header">
    <storyteller-slides-header-left />
    <storyteller-slides-header-right />
  </section>
</template>

<script>
import StorytellerSlidesHeaderLeft from '@/components/StorytellerSlidesHeader/StorytellerSlidesHeaderLeft';
import StorytellerSlidesHeaderRight from '@/components/StorytellerSlidesHeader/StorytellerSlidesHeaderRight';

export default {
  name: 'storyteller-slides-header',

  components: {
    StorytellerSlidesHeaderLeft,
    StorytellerSlidesHeaderRight,
  },

  computed: {
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slides-header {
  @include flex("block", "row", "space-between", "center");

  background-color: #EDE0FF;
  height: $storyteller-header-height;
  min-height: $storyteller-header-height;
  padding: 0 1.5rem;
  width: 100%;
}
</style>
