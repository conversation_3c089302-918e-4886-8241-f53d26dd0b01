<template>
  <section class="storyteller-slides-download-btn"
    @click="onClickDownloadBtn"
    v-click-outside-handler="{ handler: 'onCloseDropdown' }"
    :class="{top: topPosition}"
  >
    <base-button size="small" >
      <i class="fa-solid fa-download icon-download" />
      <span class="text">Download</span>
      <i class="fa-solid fa-caret-down icon-caret"></i>
    </base-button>
    <storyteller-slides-download-dropdown v-if="openDropdown" :top-position="topPosition" @close="openDropdown = false" />
  </section>
</template>

<script>
import BaseButton from '@/components/Base/BaseButton';
import clickOutsideHandler from '@/directives/click-outside-handler';
import StorytellerSlidesDownloadDropdown from '@/components/StorytellerSlidesHeader/StorytellerSlidesDownloadDropdown';

export default {
  name: 'storyteller-slides-download-btn',

  components: {
    BaseButton,
    StorytellerSlidesDownloadDropdown,
  },

  directives: {
    clickOutsideHandler,
  },

  props: {
    topPosition: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      openDropdown: false,
    };
  },

  methods: {
    onClickDownloadBtn() {
      this.openDropdown = !this.openDropdown;
    },

    onCloseDropdown() {
      this.openDropdown = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slides-download-btn {
  @include flex("block", "row", "start", "center");

  position: relative;

  &.top {
    .icon-caret {
      transform: rotate(180deg);
    }
  }

  .base-button {
    @include flex("block", "row", "start", "center");

    background-color: rgba(88, 61, 255, 0.8);
    font-size: $font-size-xxs;
    padding: 0.4rem 0.6rem;

    .icon-download {
      margin-right: 0.4rem;
    }

    .text {
      font-weight: 800;
      letter-spacing: 0.3px;
      text-transform: uppercase;
    }

    .icon-caret {
      margin-left: 0.4rem;
    }

    &:hover, &:focus {
      background-color: rgba(88, 61, 255, 1);
    }
  }
}
</style>
