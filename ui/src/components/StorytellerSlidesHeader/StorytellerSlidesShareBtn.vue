<template>
  <section class="storyteller-slides-share-btn" @click="onClickShareBtn">
    <base-button size="small" >
      <i class="fa-solid fa-paper-plane-top icon-send"></i>
      <span>Share</span>
    </base-button>
  </section>
</template>

<script>
import BaseButton from '@/components/Base/BaseButton';

export default {
  name: 'storytellerslides-share-btn',

  components: {
    BaseButton,
  },

  methods: {
    onClickShareBtn() {
      // TODO
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.storyteller-slides-share-btn {
  @include flex("block", "row", "start", "center");

  .base-button {
    @include flex("block", "row", "start", "center");

    background-color: rgba(64, 45, 179, 1);
    padding: 0.4rem 0.6rem;

    .icon-send {
      margin-right: 0.4rem;
    }

    &:hover, &:focus {
      background-color: rgba(64, 45, 179, 0.9);
    }
  }
}
</style>
