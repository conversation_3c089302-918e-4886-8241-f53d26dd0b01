<template>
  <section class="swot-chart-options">
    <section class="text" @click.stop="open = !open">
      <base-button icon="sliders" size="small">
        <span>CHART OPTIONS</span>
        <i class="fa fa-caret-down icon" :class="{ open }"></i>
      </base-button>
    </section>

    <swot-chart-option-dropdown v-if="open" @close="open = false" :isCustom="isCustom" />
  </section>
</template>

<script>
import SwotChartOptionDropdown from '@/components/SwotChartOption/SwotChartOptionDropdown';
import BaseButton from '@/components/Base/BaseButton';

export default {
  name: 'swot-chart-options',

  components: {
    BaseButton,
    SwotChartOptionDropdown,
  },

  data() {
    return {
      open: false,
    };
  },

  props: {
    isCustom: {
      type: Boolean,
      required: false,
      default: false,
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.swot-chart-options {
  position: relative;

  .text {
    .base-button {
      background-color: clr("white");
      border: $body-copy 1px solid;
      padding: 0.3rem 0.1rem;
    }

    .base-icon {
      margin-right: 0;
    }

    span {
      color: $body-copy;
      font-size: $font-size-xxs;
      font-weight: $font-weight-bold;
      letter-spacing: $letter-spacing-xs;
      margin-right: 0.5em;
    }

    .icon {
      color: $body-copy;
      height: $font-size-sm;
      font-size: 0.75rem;
      transition: all $interaction-transition-time;
      vertical-align: middle;

      &.open {
        transform: rotate(180deg);
      }
    }
  }
}
</style>
