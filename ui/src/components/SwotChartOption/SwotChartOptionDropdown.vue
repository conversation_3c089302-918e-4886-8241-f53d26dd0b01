<template>
  <section class="swot-chart-option-dropdown">
    <section class="dropdown-arrow"></section>
    <section class="swot-chart-option">
      <h3>Chart View Options</h3>
    </section>

    <section class="swot-chart-option border-bottom">
      <h4>Label Display</h4>
      <section class="label-display" @click="onClickThemeName">
        <base-checkbox :value="showThemeName"></base-checkbox>
        <span>Theme Name</span>
      </section>
      <section class="label-display" @click="onClickScore">
        <base-checkbox :value="showScore"></base-checkbox>
        <span>Adorescore</span>
      </section>
      <section class="label-display" @click="onClickVolume">
        <base-checkbox :value="showVolume"></base-checkbox>
        <span>Volume</span>
      </section>
    </section>

    <section class="swot-chart-option border-bottom">
      <h4 class="slider-title">Pin <PERSON> (Relative)</h4>
      <section class="slider">
        <minus-icon class="icon" @click="onClickMinusBubbleSize"/>
        <base-input :disabled="true" v-model="bubbleSizePercentage"></base-input>
        <plus-icon class="icon" @click="onClickPlusBubbleSize"/>
        <vue-slider v-model="bubbleSizeModel" v-bind="bubbleSizeSlider" :lazy="true"/>
      </section>
    </section>

    <section class="swot-chart-option border-bottom">
      <h4 class="slider-title">Intensity Line Visibility</h4>
      <section class="slider">
        <minus-icon class="icon" @click="onClickMinusLineOpacity"/>
        <base-input :disabled="true" v-model="lineOpacityPercentage"></base-input>
        <plus-icon class="icon" @click="onClickPlusLineOpacity"/>
        <vue-slider v-model="lineOpacityModel" v-bind="lineOpacitySlider" :lazy="true"/>
      </section>
    </section>

    <section class="swot-chart-option border-bottom">
      <h4 class="slider-title">Intensity Line Size</h4>
      <section class="slider">
        <minus-icon class="icon" @click="onClickMinusLineSize"/>
        <base-input :disabled="true" v-model="lineSizeModelPercentage"></base-input>
        <plus-icon class="icon" @click="onClickPlusLineSize"/>
        <vue-slider v-model="lineSizeModel" v-bind="lineSizeSlider" :lazy="true"/>
      </section>
    </section>

    <section class="swot-chart-option">
      <h4 class="slider-title">Background Opacity</h4>
      <section class="slider">
        <minus-icon class="icon" @click="onClickMinusBackgroundOpacity"/>
        <base-input :disabled="true" v-model="backgroundOpacityPercentage"></base-input>
        <plus-icon class="icon" @click="onClickPlusBackgroundOpacity"/>
        <vue-slider v-model="backgroundOpacityModel" v-bind="backgroundOpacitySlider" :lazy="true"/>
      </section>
    </section>

  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { MinusIcon, PlusIcon } from 'vue-feather-icons';

import BaseCheckbox from '@/components/Base/BaseCheckbox';
import BaseInput from '@/components/Base/BaseInput';
import BlurCloseable from '@/components/Mixins/BlurCloseable';
import sliderSwotStyles from '@/helpers/slider-swot-styles';
import VueSlider from 'vue-slider-component';

export default {
  name: 'swot-chart-option-dropdown',

  components: {
    BaseCheckbox,
    BaseInput,
    MinusIcon,
    PlusIcon,
    VueSlider,
  },

  mixins: [BlurCloseable],

  props: {
    isCustom: {
      type: Boolean,
      required: false,
      default: false,
    },
  },

  data() {
    return {
      backgroundOpacityMin: 0,
      backgroundOpacityMax: 0.5,
      backgroundOpacityInterval: 0.005,

      bubbleSizeMin: 0.5,
      bubbleSizeMax: 2,
      bubbleSizeInterval: 0.01,

      lineOpacityMin: 0,
      lineOpacityMax: 1,
      lineOpacityInterval: 0.01,

      lineSizeMin: 0,
      lineSizeMax: 3,
      lineSizeInterval: 0.03,
    };
  },

  computed: {
    ...mapState('swotChart', {
      backgroundOpacity: state => state.chartOptions.backgroundOpacity,
      bubbleSize: state => state.chartOptions.bubbleSize,
      lineOpacity: state => state.chartOptions.intensityLineOpacity,
      lineSize: state => state.chartOptions.intensityLineSize,
      showScore: state => state.chartOptions.showScore,
      showThemeName: state => state.chartOptions.showThemeName,
      showVolume: state => state.chartOptions.showVolume,
    }),

    backgroundOpacityModel: {
      get() {
        return +this.backgroundOpacity;
      },
      set(opacity) {
        this.setBackgroundOpacity({ opacity });
      },
    },

    backgroundOpacityPercentage() {
      return `${Math.round(this.backgroundOpacityModel / this.backgroundOpacityMax * 100)}%`;
    },

    backgroundOpacitySlider() {
      return {
        ...this.sliderBase,
        min: this.backgroundOpacityMin,
        max: this.backgroundOpacityMax,
        interval: this.backgroundOpacityInterval,
      };
    },

    bubbleSizePercentage() {
      return `${Math.round(this.bubbleSizeModel * 100)}%`;
    },

    bubbleSizeModel: {
      get() {
        return +this.bubbleSize;
      },
      set(bubbleSize) {
        this.setBubbleSize({ bubbleSize });
      },
    },

    bubbleSizeSlider() {
      return {
        ...this.sliderBase,
        min: this.bubbleSizeMin,
        max: this.bubbleSizeMax,
        interval: this.bubbleSizeInterval,
      };
    },

    lineOpacityModel: {
      get() {
        return +this.lineOpacity;
      },
      set(lineOpacity) {
        this.setLineOpacity({ lineOpacity });
      },
    },

    lineOpacityPercentage() {
      return `${Math.round(this.lineOpacityModel / this.lineOpacityMax * 100)}%`;
    },

    lineOpacitySlider() {
      return {
        ...this.sliderBase,
        min: this.lineOpacityMin,
        max: this.lineOpacityMax,
        interval: this.lineOpacityInterval,
      };
    },

    lineSizeModel: {
      get() {
        return +this.lineSize;
      },
      set(lineSize) {
        this.setLineSize({ lineSize });
      },
    },

    lineSizeModelPercentage() {
      return `${Math.round(this.lineSizeModel / this.lineSizeMax * 100)}%`;
    },

    lineSizeSlider() {
      return {
        ...this.sliderBase,
        min: this.lineSizeMin,
        max: this.lineSizeMax,
        interval: this.lineSizeInterval,
      };
    },

    sliderBase() {
      return {
        ...sliderSwotStyles,
        width: '100px',
      };
    },
  },

  methods: {
    ...mapActions('swotChart', {
      setBackgroundOpacity: 'setBackgroundOpacity',
      setBubbleSize: 'setBubbleSize',
      setLineOpacity: 'setIntensityLineOpacity',
      setLineSize: 'setIntensityLineSize',
      setShowScore: 'setShowScore',
      setShowThemeName: 'setShowThemeName',
      setShowVolume: 'setShowVolume',
    }),

    onClickMinusBackgroundOpacity() {
      if (this.backgroundOpacityModel <= this.backgroundOpacityMin) return;
      this.setBackgroundOpacity({ opacity: Math.round((this.backgroundOpacityModel - this.backgroundOpacityInterval) * 1000) / 1000 });
    },

    onClickMinusBubbleSize() {
      if (this.bubbleSizeModel <= this.bubbleSizeMin) return;
      this.setBubbleSize({ bubbleSize: Math.round((this.bubbleSizeModel - this.bubbleSizeInterval) * 100) / 100 });
    },

    onClickMinusLineOpacity() {
      if (this.lineOpacityModel <= this.lineOpacityMin) return;
      this.setLineOpacity({ lineOpacity: Math.round((this.lineOpacityModel - this.lineOpacityInterval) * 100) / 100 });
    },

    onClickMinusLineSize() {
      if (this.lineSizeModel <= this.lineSizeMin) return;
      this.setLineSize({ lineSize: Math.round((this.lineSizeModel - this.lineSizeInterval) * 100) / 100 });
    },

    onClickPlusBackgroundOpacity() {
      if (this.backgroundOpacityModel >= this.backgroundOpacityMax) return;
      this.setBackgroundOpacity({ opacity: Math.round((this.backgroundOpacityModel + this.backgroundOpacityInterval) * 1000) / 1000 });
    },

    onClickPlusBubbleSize() {
      if (this.bubbleSizeModel >= this.bubbleSizeMax) return;
      this.setBubbleSize({ bubbleSize: Math.round((this.bubbleSizeModel + this.bubbleSizeInterval) * 100) / 100 });
    },

    onClickPlusLineOpacity() {
      if (this.lineOpacityModel >= this.lineOpacityMax) return;
      this.setLineOpacity({ lineOpacity: Math.round((this.lineOpacityModel + this.lineOpacityInterval) * 100) / 100 });
    },

    onClickPlusLineSize() {
      if (this.lineSizeModel >= this.lineSizeMax) return;
      this.setLineSize({ lineSize: Math.round((this.lineSizeModel + this.lineSizeInterval) * 100) / 100 });
    },

    onClickScore() {
      this.setShowScore({ show: !this.showScore });
    },

    onClickThemeName() {
      this.setShowThemeName({ show: !this.showThemeName });
    },

    onClickVolume() {
      this.setShowVolume({ show: !this.showVolume });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.swot-chart-option-dropdown {
  @include flex("block", "column", "start", "stretch");
  @include rigid;
  @include panel;

  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.8);
  font-size: $font-size-xs;
  padding: 0 0 0.5rem;
  position: absolute;
  right: 0;
  top: 2.4rem;
  z-index: 99;

  .border-bottom {
    border-bottom: $border-standard;
  }

  .dropdown-arrow {
    border-bottom: 7px solid white;
    border-left: 7px solid transparent;
    border-right: 7px solid transparent;
    border-top: none;
    position: absolute;
    filter: drop-shadow(0px -1px 1px rgba(0, 0, 0, 0.2));
    right: 20px;
    top: -7px;
  }

  .label-display {
    @include flex("block", "row", "start", "center");

    cursor: pointer;
    padding: 0.3rem 0 0.5rem;
    margin: 0.5rem 0 0.5rem 0;

    .base-checkbox {
      margin-right: 0.5em;
      pointer-events: none;
    }

    span {
      font-size: $font-size-xs;
    }
  }

  .slider {
    @include flex("block", "row", "start", "center");

    cursor: pointer;
    padding: 0.3rem 0 0.5rem;
    margin: 0 0 0.5rem 0;
    position: relative;

    .base-input {
      border: $border-standard;
      border-radius: $border-radius-small;
      margin-right: 0.4rem;
      text-align: right;
      width: 3.6em;
    }

    .icon {
      border-radius: 50%;
      border: 1px solid $toggle-button-ball-border;
      height: 0.8rem;
      margin-right: 0.4rem;
      width: 0.8rem;

      &:hover {
        background-color: $search-dropdown-icon-bg-hover;
        color: darken($insights-btn-c-bg, 10%);
      }
    }
  }

  .slider-title {
    margin-bottom: 0.3rem;
  }

  .swot-chart-option {
    padding: 0.3rem 0.8rem 0;
  }

  h3 {
    color: $body-copy;
    font-size: $font-size-xs;
    font-weight: $font-weight-bold;
    letter-spacing: $letter-spacing-xs;
    margin: 1em 0 0 0;
  }

  h4 {
    color: $body-copy;
    font-size: $font-size-xs;
    font-weight: $font-weight-bold;
    letter-spacing: $letter-spacing-xs;
    margin: 1em 0;
  }
}
</style>
