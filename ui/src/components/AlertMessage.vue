<template>
  <section class="alert-message" :class="type">
    <section class="border"></section>

    <component :is="icon" class="icon"></component>

    <span class="text">
      <span class="center">
        <slot></slot>
      </span>
    </span>
  </section>
</template>

<script>
import { AlertCircleIcon } from 'vue-feather-icons';

export default {
  name: 'alert-message',

  props: {
    icon: {
      type: Object,
      default: () => AlertCircleIcon,
    },
    type: {
      type: String,
      default: 'info',
      validator: value => [
        'danger',
        'failure',
        'warning',
        'info',
        'success',
      ].includes(value),
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.alert-message {
  @include flex("inline", "row", "start", "stretch");
  @include stretch;

  border-radius: $border-radius-medium;
  overflow: hidden;
  padding-right: 1rem;
  position: relative;

  &.danger,
  &.failure,
  &.warning {
    background-color: lighten(clr("red"), 32%);

    .border {
      border-left-color: clr("red");
    }

    .icon {
      color: clr("red");
    }

    .text {
      color: darken(clr("red"), 30%);
    }
  }

  &.info {
    background-color: lighten(clr("blue"), 40%);

    .border {
      border-left-color: clr("blue");
    }

    .icon {
      color: clr("blue");
    }

    .text {
      color: darken(clr("blue"), 30%);
    }
  }

  &.success {
    background-color: lighten(clr("green"), 45%);

    .border {
      border-left-color: clr("green");
    }

    .icon {
      color: clr("green");
    }

    .text {
      color: darken(clr("green"), 30%);
    }
  }

  .border {
    border-left: 3px solid;
  }

  .icon {
    @include rigid;

    height: $font-size-md;
    margin: 0.4rem 0.3rem;
  }

  .text {
    @include flex("block", "row", "start", "center");

    font-size: $font-size-xs;
    font-weight: $font-weight-medium;
    margin: 0.4rem 0.3rem;
  }
}
</style>
