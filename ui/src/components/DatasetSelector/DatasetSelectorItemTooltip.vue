<template>
  <section class="dataset-selector-item-tooltip">
    <section class="item header">Adorescore</section>
    <section class="item">
      <adorescore-box-mini :bucket="bucket" :score="dataset.adoreScore"/>
      <span class="score-name" :class="[bucket.name.toLowerCase()]">{{ bucketName }}</span>
    </section>
    <section class="item comparison"
             v-if="overviews.length > 1"
             :class="{
                above: avgComparisonScore > 0,
                below: avgComparisonScore < 0,
                equal: avgComparisonScore === 0,
             }"
    >
      <i v-if="avgComparisonScore > 0" class="fa-light fa-arrow-up" />
      <i v-if="avgComparisonScore < 0" class="fa-light fa-arrow-down" />
      <i v-if="avgComparisonScore === 0" class="fa-light fa-minus" />
      <section class="text">
        {{ avgComparisonMsg }}
      </section>
    </section>
    <section class="item key-colour">
      <section class="colour" :style="{ backgroundColor: colour }"></section>
      <i class="fa-light fa-arrow-left number" />
      <section class="text">Key Colour</section>
    </section>
    <section class="item header">Dataset Stats</section>
    <section class="item">
      <i class="fa-light fa-comment" />
      <section class="number">{{ dataset.documentCount }}</section>
      <section class="text">{{ dataset.documentCount > 1 ? 'Comments' : 'Comment' }}</section>
    </section>
    <section class="item">
      <i class="fa-light fa-list" />
      <section class="number">{{ dataset.topicCount }}</section>
      <section class="text">{{ dataset.topicCount > 1 ? 'Themes' : 'Theme' }}</section>
    </section>
    <section class="item">
      <i class="fa-light fa-list-tree" />
      <section class="number">{{ dataset.subTopicCount }}</section>
      <section class="text">{{ dataset.subTopicCount === 1 ? 'Subtopic' : 'Subtopics' }}</section>
    </section>
    <section class="item">
      <i class="fa-light fa-tag" />
      <section class="number">{{ tagIds.length }}</section>
      <section class="text">{{ tagIds.length === 1 ? 'Tag' : 'Tags' }}</section>
    </section>
  </section>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import AdorescoreBoxMini from '@/components/AdorescoreBox/AdorescoreBoxMini';

export default {
  name: 'dataset-selector-item-tooltip',

  components: {
    AdorescoreBoxMini,
  },

  props: {
    avgComparisonScore: {
      type: Number,
      default: 0,
      required: false,
    },

    colour: {
      type: String,
      required: true,
    },

    dataset: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapGetters('benchmark', ['classifyAdorescore']),

    ...mapGetters('datasets', ['get']),

    ...mapState('datasets', ['overviews']),

    avgComparisonMsg() {
      if (this.avgComparisonScore < 0) {
        return `${this.avgComparisonScore} Adorescore below average of selected datasets`;
      }
      if (this.avgComparisonScore > 0) {
        return `+${this.avgComparisonScore} Adorescore above average of selected datasets`;
      }
      return 'Equal to average Adorescore of selected datasets';
    },

    bucket() {
      return this.classifyAdorescore(this.dataset.adoreScore);
    },

    bucketName() {
      if (this.bucket.name === 'Very Poor') {
        return 'V.Poor';
      }
      if (this.bucket.name === 'Very Good') {
        return 'V.Good';
      }
      return this.bucket.name || '';
    },

    tagIds() {
      return this.get(this.dataset.id)?.tagIds || [];
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.dataset-selector-item-tooltip {
  @include flex("block", "column", "start", "stretch");
  padding: 0.5rem 0;

  .item {
    @include flex("block", "row", "start", "center");
    color: rgba(95, 82, 197, 1);
    font-size: $font-size-xxs;
    line-height: 13px;
    padding: 0.3rem 1rem;

    &.comparison {
      @include flex("block", "row", "start", "start");

      &.above {
        color: rgba(77, 170, 66, 1);
      }

      &.below {
        color: rgba(170, 66, 66, 1);
      }

      &.equal {
        color: rgba(95, 82, 197, 1);
      }

      .text {
        margin-left: 0.5rem;
        max-width: 120px;
      }
    }

    &.header {
      font-size: $font-size-xxs;
      font-weight: 800;
      text-transform: uppercase;
    }

    &.key-colour {
      border: 1px solid rgba(95, 82, 197, 0.08);
      border-left: none;
      border-right: none;
      padding: 0.5rem 1rem;
      margin: 0.2rem 0;
    }

    .score-name {
      font-weight: $font-weight-medium;
      margin-left: 0.5rem;

      &.very-poor {
        color: $very-poor-bdr;
      }

      &.poor {
        color: $poor-bdr;
      }

      &.fair {
        color: $fair-bdr;
      }

      &.good {
        color: $good-bdr;
      }

      &.very-good {
        color: $very-good-bdr;
      }
    }

    .colour {
      border-radius: $font-size-xxs;
      height: 0.5rem;
      width: 1.2rem;
    }

    .fa, .fa-light {
      font-size: $font-size-xs;
    }

    .number {
      font-weight: 800;
      margin-left: 0.4rem;
    }

    .text {
      margin-left: 0.2rem;
    }
  }
}
</style>
