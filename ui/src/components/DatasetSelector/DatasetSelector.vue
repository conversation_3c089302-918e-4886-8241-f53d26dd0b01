<template>
  <section class="dataset-selector">
    <section class="count">
      {{ overviews.length }} Selected {{ overviews.length > 1 ? 'Datasets' : 'Dataset' }}
      <i class="fa-regular fa-arrow-right-long icon" />
    </section>

    <section class="list">
      <section v-if="amountToDisplay < overviews.length"
        class="scroll left"
        :class="{ disabled: this.listOffset === 0 }"
        @click="onClickPrev">
        <arrow-left-icon class="icon"/>
      </section>

      <dataset-selector-item v-for="dataset in overviewsWindow"
        :key="dataset.id"
        :active="active === dataset.id"
        :dataset="dataset"
        :index="overviewIndex(dataset)"
        @click.native="onClickDataset(dataset.id)"
        @itemClose="onItemClose"
      />

      <section v-if="amountToDisplay < overviews.length"
        class="scroll right"
        :class="{ disabled: this.listOffset + this.amountToDisplay === this.overviews.length }"
        @click="onClickNext">
        <arrow-right-icon class="icon"/>
      </section>
    </section>

    <section v-if="overviews.length < 2"
             class="back-to-dataset-page"
             @click="onClickBackToDatasetPage">
      Compare more datasets
      <i class="fa fa-caret-right" />
    </section>
  </section>
</template>

<script>
import { debounce } from 'lodash-es';
import { mapActions, mapGetters, mapState } from 'vuex';
import { ArrowLeftIcon, ArrowRightIcon } from 'vue-feather-icons';

import DatasetSelectorItem from '@/components/DatasetSelector/DatasetSelectorItem';
import Route from '@/enum/route';

export default {
  name: 'dataset-selector',

  components: {
    ArrowLeftIcon,
    ArrowRightIcon,
    DatasetSelectorItem,
  },

  data() {
    return {
      countWidth: 170,
      itemWidth: 376,
      listOffset: 0,
      scrollButtonWidth: 32,
      width: 0,
    };
  },

  computed: {
    ...mapGetters('datasets', ['ids']),

    ...mapState('datasets', [
      'active',
      'overviews',
      'selected',
    ]),

    amountToDisplay() {
      return Math.floor((this.width - (this.scrollButtonWidth * 2) - this.countWidth) / this.itemWidth);
    },

    overviewsWindow() {
      return this.overviews.slice(this.listOffset, this.listOffset + this.amountToDisplay);
    },

    selectedDataset() {
      return this.overviews.find(d => d.id === this.active);
    },
  },

  watch: {
    amountToDisplay() {
      this.listOffset = 0;
    },
  },

  mounted() {
    if (this.$el) {
      this.width = this.$el.getBoundingClientRect().width;

      window.addEventListener('resize', this.onResize);
    }
  },

  methods: {
    ...mapActions('datasets', ['setActive']),

    ...mapActions('snippets', {
      resetSnippets: 'reset',
    }),

    ...mapActions('themes', { resetThemes: 'reset' }),

    onClickBackToDatasetPage() {
      this.$router.push({ path: Route.DATASETS.kebabCase() });
    },

    onClickDataset(id) {
      if (id === this.selectedDataset.id) return;
      this.resetThemes();
      this.resetSnippets();
      this.setActive({ id });
    },

    onClickNext() {
      if (this.listOffset + this.amountToDisplay < this.overviews.length) {
        this.listOffset += 1;
      }
    },

    onClickPrev() {
      if (this.listOffset > 0) {
        this.listOffset -= 1;
      }
    },

    onItemClose() {
      if (this.overviewsWindow.length < this.overviews.length
          && this.overviewsWindow.length < this.amountToDisplay
          && this.listOffset > 0
      ) {
        this.listOffset -= 1;
      }
    },

    onResize: debounce(function onResize() {
      this.width = this.$el.getBoundingClientRect().width;
    }, 50),

    overviewIndex(overview) {
      return this.overviews.map(o => o.id).indexOf(overview.id);
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.dataset-selector {
  @include flex("block", "row", "start", "center");
  @include rigid;

  background-color: #F6F7FF;
  height: $dataset-selector-height;
  padding: 0.1rem 1rem;

  .count {
    align-items: center;
    color: rgba(45, 23, 87, 1);
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
    margin-right: 1rem;
    min-width: fit-content;

    .icon {
      font-size: $font-size-xs;
    }
  }

  .list {
    @include flex("block", "row", "start", "center");
    padding: 0.5rem 0;

    .scroll {
      @include flex("block", "row", "center", "center");

      border: 1px solid lighten($body-copy, 10%);
      border-radius: 2rem;
      cursor: pointer;
      height: 2rem;
      padding: 0.5rem;
      transition: all $interaction-transition-time;
      width: 2rem;

      &:hover:not(.disabled) {
        background-color: clr('purple', 'dark');
        border-color: clr('purple', 'darker');

        .icon {
          color: clr('white');
        }
      }

      &.disabled {
        cursor: not-allowed;
        opacity: 0.5;
      }

      &.left {
        margin-right: 0.8rem;
      }

      .icon {
        color: lighten($body-copy, 10%);
        height: 1rem;
        width: 1rem;
      }
    }

    .dataset-selector-item {
      margin-right: 1rem;
    }
  }

  .back-to-dataset-page {
    @include flex("block", "row", "center", "center");
    border: 1px dashed rgba(95, 82, 197, 1);
    border-radius: 2px;
    color: rgba(95, 82, 197, 1);
    cursor: pointer;
    font-size: $font-size-xs;
    font-weight: $font-weight-medium;
    padding: 0.5rem 1rem;
    text-transform: uppercase;

    &:hover {
      border: 1px solid rgba(95, 82, 197, 1);
    }

    .fa {
      font-size: $font-size-sm;
      margin-left: 0.5rem;
    }
  }
}
</style>
