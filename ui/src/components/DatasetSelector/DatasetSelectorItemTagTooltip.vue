<template>
  <section class="dataset-selector-item-tag-tooltip">
    <section class="items">
      <section
          class="item"
          v-for="tag in filteredTags"
          :key="tag.id"
      >
        <base-tag :colour="tag.colour" :max-width="'15rem'" :text="tag.name" />
      </section>
    </section>
  </section>
</template>

<script>
import BaseTag from '@/components/Base/BaseTag';
import { mapGetters, mapState } from 'vuex';

export default {
  name: 'dataset-selector-item-tag-tooltip',

  components: {
    BaseTag,
  },

  props: {
    dataset: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapState('datasetsTag', ['tags']),

    ...mapGetters('datasets', ['get']),

    foundDataset() {
      return this.get(this.dataset.id);
    },

    filteredTags() {
      return this.tags?.filter(t => this.tagIds?.includes(t.id));
    },

    tagIds() {
      return this.foundDataset.tagIds || [];
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.dataset-selector-item-tag-tooltip {
  @include flex("block", "column", "start", "stretch");
  @include scrollbar-thin;

  font-size: $font-size-xxs;
  max-height: 7.4rem;
  overflow-x: hidden;
  overflow-y: auto;

  .item {
    @include flex("block", "row", "start", "center");
    margin: 0.2rem 0;
  }
}
</style>
