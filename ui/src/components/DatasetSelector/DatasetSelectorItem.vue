<template>
  <section class="dataset-selector-item" :class="{ active, selectable }">
    <section class="inner">
      <adorescore-box-mini :bucket="bucket" :score="dataset.adoreScore"/>

      <section class="main"
               v-tooltip.bottom="{ delay: 20, class: 'tooltip-dataset-name', content: dataset.label }">
        <section class="label">{{ dataset.label }}</section>
        <section v-if="overviews.length > 1"
                 class="avg-comparison"
                 :class="{
                    above: avgComparisonScore > 0,
                    below: avgComparisonScore < 0,
                    equal: avgComparisonScore === 0,
                 }"
        >
          <i v-if="avgComparisonScore > 0" class="fa-light fa-arrow-up" />
          <i v-if="avgComparisonScore < 0" class="fa-light fa-arrow-down" />
          <i v-if="avgComparisonScore === 0" class="fa-light fa-minus" />
          {{ avgComparisonMsg }}
        </section>
      </section>
    </section>
    <section class="extra-info" v-tooltip.bottom="{ delay: 20, class: 'tooltip-dataset-selector', html: `dataset-selector-item-${dataset.id}` }">
      <section class="color-key">
        <section class="color-mark" :style="{ backgroundColor: colour(dataset.id) }"></section>
      </section>

      <section class="count comments">
        <i class="fa-light fa-comment" />
        <span class="text">{{ dataset.documentCount }}</span>
      </section>

      <section class="count tags">
        <i class="fa-light fa-tag" />
        <span class="text">{{ tagIds.length }}</span>
      </section>

      <section class="more">
        <i class="fa fa-caret-down" />
      </section>
    </section>
    <section class="close">
      <x-icon class="icon" @click.stop="onClose"/>
    </section>

    <dataset-selector-item-tooltip :id="`dataset-selector-item-${dataset.id}`"
                                   :dataset="dataset"
                                   :colour="colour(dataset.id)"
                                   :avgComparisonScore="avgComparisonScore"
    />
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';
import { XIcon } from 'vue-feather-icons';

import AdorescoreBoxMini from '@/components/AdorescoreBox/AdorescoreBoxMini';
import DatasetSelectorItemTooltip from '@/components/DatasetSelector/DatasetSelectorItemTooltip';

export default {
  name: 'dataset-selector-item',

  components: {
    AdorescoreBoxMini,
    DatasetSelectorItemTooltip,
    XIcon,
  },

  props: {
    active: {
      type: Boolean,
      required: true,
    },

    dataset: {
      type: Object,
      required: true,
    },

    selectable: {
      type: Boolean,
      default: true,
    },
  },

  computed: {
    ...mapGetters('benchmark', ['classifyAdorescore']),

    ...mapGetters('datasets', [
      'average',
      'colour',
      'get',
    ]),

    ...mapState('datasets', ['overviews']),

    avgComparisonMsg() {
      if (this.avgComparisonScore < 0) {
        return `${this.avgComparisonScore} below selection avg`;
      }
      if (this.avgComparisonScore > 0) {
        return `+${this.avgComparisonScore} above selection avg`;
      }
      return 'equal to average';
    },

    avgComparisonScore() {
      let rs = 0;

      this.overviews.forEach(o => {
        rs += o.adoreScore;
      });
      if (this.overviews?.length) {
        rs /= this.overviews.length;
      } else {
        rs = 0;
      }
      return Math.round(this.dataset.adoreScore - rs);
    },

    bucket() {
      return this.classifyAdorescore(this.dataset.adoreScore);
    },

    foundDataset() {
      return this.get(this.dataset.id);
    },

    tagIds() {
      return this.foundDataset.tagIds || [];
    },
  },

  methods: {
    ...mapActions('datasets', ['deselect', 'removeOverviews']),

    onClose() {
      this.deselect({ ids: [this.dataset.id] });
      this.removeOverviews({ ids: [this.dataset.id] });
      this.$emit('itemClose');
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.dataset-selector-item {
  @include flex("block", "row", "stretch", "stretch");
  @include truncate;
  @include rigid;

  background: none;
  border: 1px solid rgba(95, 82, 197, 0.2);
  border-radius: 4px;
  padding: 0.6rem;
  position: relative;
  transition: border-color $interaction-transition-time;
  width: 360px;

  &.selectable {
    cursor: pointer;
    background-color: rgba(255, 255, 255, 0.4);

    &.active {
      background: clr('white');
      border: 1px solid rgba(95, 82, 197, 1);
      box-shadow: 0 0 0 2px rgba(106, 157, 255, 0.32);

      &:hover {
        box-shadow: 0 0 0 2px rgba(106, 157, 255, 1);
      }
    }

    &:not(.active):hover {
      border: 1px solid rgba(95, 82, 197, 1);
    }
  }

  .inner {
    @include flex("block", "row", "start", "center");
    @include truncate;
    @include stretch;

    .adorescore-box-mini {
      font-size: 0.7em;
      height: fit-content;
    }

    .main {
      @include flex("block", "column", "center", "start");
      @include truncate;
      margin-left: 0.5rem;

      .label {
        @include truncate;
        color: #2D1757;
        font-size: $font-size-xs;
        font-weight: $font-weight-bold;
        width: 100%;
      }

      .avg-comparison {
        @include truncate;
        font-size: $font-size-xxs;
        margin-top: 0.2rem;
        width: 100%;

        &.above {
          color: rgba(77, 170, 66, 1);
        }

        &.below {
          color: rgba(170, 66, 66, 1);
        }

        &.equal {
          color: rgba(95, 82, 197, 1);
        }
      }
    }
  }

  .extra-info {
    @include flex("block", "row", "start", "center");
    padding-left: 1rem;

    .color-key {
      margin-top: 1px;

      .color-mark {
        border-radius: 3px;
        height: 0.4em;
        width: 0.8em;
      }
    }

    .count {
      color: rgba(95, 82, 197, 1);
      font-size: $font-size-xxs;
      margin-left: 0.5rem;
    }

    .more {
      color: rgba(95, 82, 197, 1);
      font-size: $font-size-xs;
      padding-left: 0.5rem
    }
  }

  .close {
    @include flex("block", "row", "center", "center");
    padding-left: 0.8rem;

    .icon {
      color: rgba(45, 23, 87, 0.25);
      cursor: pointer;
      height: $font-size-lg;
      transition: opacity $interaction-transition-time;
      width: $font-size-lg;

      &:hover {
        color: rgba(45, 23, 87, 0.75);
      }
    }
  }
}
</style>
