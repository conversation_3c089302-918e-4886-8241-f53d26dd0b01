<template>
  <section class="collections-modal-workspace-collections-tab" :class="{ empty: !dataList.length }">
    <section v-if="dataList.length > 0" class="tab-body">
      <section class="body-item description">
        <span v-if="isApplying">Select Collection(s) to Apply to {{ applyingDatasetIds.length === 1 ? '1 Dataset' : `${applyingDatasetIds.length} Datasets` }}</span>
        <span v-else>Modify a collection by copying it to 'My Collections' using the duplicate button below.</span>
      </section>
      <section class="body-item collection-header">
        <section class="header-label">
          Name
        </section>
        <section class="header-label">
          Created By
        </section>
        <section class="header-label">
        </section>
        <section class="header-label">
        </section>
      </section>
      <section class="body-item collection-list">
        <section v-for="item in dataList" class="collection-item" :key="item.id">
          <section class="collection-item-label" :class="{ disabled: isApplying && item.disabled, hasCheckbox: isApplying || activeTab !== 2 }" @click="onClickCollection(item)">
            <base-checkbox v-if="isApplying" :value="isInSelectedLists(item.id)" :disabled="item.disabled"/>
            <base-radio-with-tick-mark v-else-if="!isApplying && !item.default && activeTab !== 2" :value="isInSelectedLists(item.id)" />
            <span class="text">{{ item.label }}</span>
          </section>

          <section class="collection-item-user">
            <span class="text">{{ item.user.firstName }}</span>
          </section>

          <section class="collection-item-action" @click.stop="onClickCopy(item)">
            <i class="fa-solid fa-copy icon"></i>
            Duplicate
          </section>

          <section class="collection-item-preview" @click.stop="onClickPreview(item)">
            <i class="fa-solid fa-eye icon"></i>
            Preview
          </section>
        </section>
      </section>
    </section>
    <section v-else class="tab-empty">
      No Collections or Memory Actions exist within the Workspace.<br/>
      <span class="empty-link" @click="onClickGoToMemory">
        <span class="link-text">Go to Memory</span>
        <i class="fa-light fa-arrow-right icon"></i>
      </span>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseCheckbox from '@/components/Base/BaseCheckbox';
import BaseRadioWithTickMark from '@/components/Base/BaseRadioWithTickMark';
import SaveActionCollectionDuplicationModal from '@/components/MemoryPageBrowser/Modal/SaveActionCollectionDuplicationModal';
import ThemesBuilderCollectionPreviewModal from '@/components/ThemesBuilder/ThemesBuilderCollectionPreviewModal';

export default {
  name: 'collections-modal-workspace-collections-tab',

  components: {
    BaseCheckbox,
    BaseRadioWithTickMark,
  },

  props: {
    activeTab: {
      type: Number,
      required: true,
    },
  },

  computed: {
    ...mapGetters('savedActions', ['savedActionListsByOtherUserId']),

    ...mapState('datasets', {
      datasetIds: 'selected',
    }),

    ...mapState('savedActions', [
      'isApplying',
      'isApplyingForDatasetId',
      'savedActionLists',
      'selectedLists',
    ]),

    ...mapState('user', ['user']),

    applyingDatasetIds() {
      return this.isApplyingForDatasetId
        ? [this.isApplyingForDatasetId]
        : [...this.datasetIds];
    },

    dataList() {
      return this.savedActionListsByOtherUserId(this.user.id)
        .map(action => {
          action.disabled = !action.list?.length;
          return action;
        });
    },

    defaultId() {
      return this.savedActionLists.find(t => t.default)?.id || -1;
    },
  },

  methods: {
    ...mapActions('modal', ['setModalComponent']),

    ...mapActions('savedActions', [
      'addToSelectedLists',
      'removeFromSelectedLists',
      'resetSelectedLists',
      'setSelectedList',
      'setSelectedPreviewList',
    ]),

    isInSelectedLists(id) {
      return this.selectedLists.includes(id);
    },

    onClickCollection(item) {
      if (!this.isApplying && item.default) return;

      if (this.isApplying) {
        if (item.disabled) {
          return;
        }
        if (this.isInSelectedLists(item.id)) {
          this.removeFromSelectedLists({ id: item.id });
        } else {
          if (item.default) {
            this.resetSelectedLists();
          } else {
            this.removeFromSelectedLists({ id: this.defaultId });
          }
          this.addToSelectedLists({ id: item.id });
        }
      }
    },

    async onClickCopy(item) {
      this.setSelectedList({ list: item });
      this.setModalComponent({ component: SaveActionCollectionDuplicationModal });
    },

    onClickGoToMemory() {
      this.$emit('go-to-memory');
    },

    onClickPreview(item) {
      this.setSelectedPreviewList({ list: item });
      this.setModalComponent({ component: ThemesBuilderCollectionPreviewModal });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.collections-modal-workspace-collections-tab {
  display: grid;
  grid-template-rows: auto auto 1fr;
  height: inherit;
  padding: 1.25rem 1.5rem;

  &.empty {
    align-content: center;
    grid-template-rows: 1fr;
    justify-content: center;
  }

  .tab-body {

    .body-item {
      margin-bottom: 1rem;
      width: 100%;

      &:last-child {
        margin-bottom: 0;
      }

      &.description {
        color: #2D1757;
        font-size: 12px;
        margin: 0.5rem 0 1.5rem;
      }

      &.collection-header {
        display: grid;
        grid-template-columns: auto 100px 100px 100px;

        .header-label{
          align-items: center;
          color: #5F52C5;
          display: flex;
          font-size: 10px;
          font-weight: 800;
          letter-spacing: 0.3px;
          line-height: 12px;
          text-align: center;
          text-transform: uppercase;
          width: 100%;
        }
      }

      &.collection-list {
        @include scrollbar-thin;
        height: 340px;
        overflow-y: auto;
        width: 100%;

        .collection-item {
          align-items: center;
          border-bottom: 1px solid #DEE1E4;
          display: grid;
          font-size: 12px;
          font-weight: 400;
          grid-template-columns: auto 100px 100px 100px;
          height: 42px;
          line-height: 19px;

          &:last-child {
            border-bottom: none;
          }

          .collection-item-action {
            @include flex("block", "row", "center", "center");

            background: #FFFFFF;
            border-radius: 2px;
            border: 1px solid rgba(95, 82, 197, 0.3);
            color: #5F52C5;
            cursor: pointer;
            font-size: 10px;
            font-weight: $font-weight-bold;
            padding: 0.2rem 0.4rem;
            text-transform: uppercase;
            width: fit-content;

            &:hover {
              background: #5F52C5;
              border-color: #FFFFFF;
              color: #FFFFFF;
            }

            .icon {
              margin-right: 0.2rem;
            }
          }

          .collection-item-label {
            @include truncate;
            align-items: center;
            display: grid;
            grid-template-columns: 1fr;
            margin-right: 1rem;

            &.disabled {
              cursor: not-allowed;
              opacity: 0.6;
            }

            &.hasCheckbox {

              &:not(.disabled) {
                cursor: pointer;
              }

              grid-column-gap: 0.2rem;
              grid-template-columns: 2rem 1fr;
            }

            .text {
              @include truncate;
            }
          }

          .collection-item-preview {
            @include flex("block", "row", "end", "center");

            background-color: #C4FFC2;
            border-radius: $border-radius-small;
            color: #03AA00;
            cursor: pointer;
            font-size: $font-size-xxs;
            font-weight: $font-weight-bold;
            padding: 0.2rem 0.4rem;
            text-transform: uppercase;
            width: fit-content;

            &:hover, &:active, &:focus {
              color: #18952F;
            }

            .icon {
              margin-right: 0.2rem;
            }
          }
        }
      }
    }
  }

  .tab-empty {
    align-content: center;
    display: grid;
    font-size: $font-size-base;
    grid-row-gap: 1rem;
    justify-content: center;

    .empty-link {
      cursor: pointer;
      display: grid;
      font-weight: 700;
      grid-template-columns: auto auto;
      justify-content: center;
      transition: opacity $interaction-transition-time;

      &:hover {
        opacity: 0.6;
      }

      .link-text {
        text-decoration: underline;
      }

      .icon {
        margin-left: 0.5rem;
      }
    }
  }
}
</style>
