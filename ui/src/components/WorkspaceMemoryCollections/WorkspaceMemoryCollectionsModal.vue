<template>
  <section class="workspace-memory-collections-modal">
    <section class="header">
      <section class="title-wrapper" :class="{ isApplying }">
        <section class="title">
          <span v-if="isApplying" class="text">
            Apply Memory Collections to Dataset{{ applyingDatasetIds.length === 1 ? '' : 's' }}
          </span>
          <span v-else class="text">Workspace Memory Collections</span>
        </section>
        <section v-if="isApplying" class="sub-title">
          {{ selectedLists.length > 1 ? `${selectedLists.length} Collections Selected` : `${selectedLists.length} Collection Selected` }}
        </section>
      </section>

      <section class="collection-types">
        <section class="collection-type" :class="{ inactive: !isCurrentTab(1) }" @click="activeTab = 1">
          <i class="fa-solid fa-album-collection icon"></i>
          <span>My Collections</span>
        </section>
        <section class="collection-type" :class="{ inactive: !isCurrentTab(2) }" @click="activeTab = 2">
          <i class="fa-solid fa-album-collection icon"></i>
          <span>Workspace Collections</span>
        </section>
      </section>
    </section>

    <section class="body">
      <collections-modal-workspace-collections-tab v-if="activeTab === 2"
                                                   :active-tab="activeTab"
                                                   @go-to-memory="onClickGoToMemory" />
      <collections-modal-my-collections-tab v-else
                                            @go-to-memory="onClickGoToMemory"
                                            @select-item="onSelectItem" />
    </section>
    <section class="footer">
      <base-button class="cancel-btn" colour="light" size="small" type="link" @click="onClickCancel">
        Cancel
      </base-button>

      <loading-blocks-overlay v-if="loadingApply" />

      <section v-else-if="isApplying" class="apply-options">
        <span class="checkbox" @click="excludeAuto = !excludeAuto" v-if="!isUploadPage">
          <base-checkbox :value="excludeAuto" />
          <span>Exclude Adoreboard-generated themes</span>
        </span>

        <base-button
          :disabled="selectedLists.length === 0"
          class="apply-btn"
          colour="dark"
          icon="plus"
          size="small"
          @click="onClickApply"
        >
          Apply to Dataset{{ selected.length === 1 ? '' : 's' }}
        </base-button>
      </section>

      <base-button
          v-else
          :disabled="isContinueBtnDisabled"
          class="apply-btn"
          colour="dark"
          size="small"
          @click="onClickContinue"
      >
        Continue
      </base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseCheckbox from '@/components/Base/BaseCheckbox';
import CollectionsModalWorkspaceCollectionsTab from '@/components/WorkspaceMemoryCollections/CollectionsModalWorkspaceCollectionsTab';
import CollectionsModalMyCollectionsTab from '@/components/WorkspaceMemoryCollections/CollectionsModalMyCollectionsTab';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import NetworkStatus from '@/enum/network-status';
import Route from '@/enum/route';

import { savedActionApi } from '@/services/api';
import { datasetsRequestV0, savedActionsRequest } from '@/services/request';

export default {
  name: 'workspace-memory-collections-modal',

  components: {
    BaseButton,
    BaseCheckbox,
    CollectionsModalWorkspaceCollectionsTab,
    CollectionsModalMyCollectionsTab,
    LoadingBlocksOverlay,
  },

  data() {
    return {
      activeTab: 1,
      excludeAuto: false,
      loadingApply: false,
      selectedItem: null,
    };
  },

  computed: {
    ...mapState('datasets', ['selected']),

    ...mapState('savedActions', [
      'isApplying',
      'isApplyingForDatasetId',
      'savedActionLists',
      'selectedLists',
    ]),

    ...mapState('user', ['activeWorkspace']),

    applyingDatasetIds() {
      // when open this MemoryModal via dataset-more-action-dropdown btn
      // apply to that dataset only, not the whole selected list
      return this.isApplyingForDatasetId
        ? [this.isApplyingForDatasetId]
        : [...this.selected];
    },

    defaultId() {
      return this.savedActionLists.find(t => t.default)?.id || -1;
    },

    isContinueBtnDisabled() {
      return this.selectedLists.length === 0
        || (!this.isApplying && this.activeTab === 2);
    },

    isUploadPage() {
      return this.$route.name === Route.UPLOADS;
    },
  },

  async created() {
    this.resetSelectedLists();
    const savedActionLists = await savedActionApi.getSavedActionLists(this.activeWorkspace?.id);
    this.setSavedActionLists({ savedActionLists });

    savedActionApi.getSavedActions(this.activeWorkspace?.id)
      .then(savedActions => this.setSavedActionsToPreview({ savedActions }));
  },

  destroyed() {
    savedActionsRequest.fetchSavedActions(); // call to fetch limit numbers of saved actions
    this.setIsApplyingForDatasetId({ id: null });
  },

  methods: {
    ...mapActions('datasetManagementTabs', ['setViewingMemoryPageTab']),

    ...mapActions('modal', ['closeModal']),

    ...mapActions('poll', ['setDatasetsState']),

    ...mapActions('savedActions', [
      'resetSelectedLists',
      'setIsApplyingForDatasetId',
      'setSavedActionLists',
      'setSavedActionsToPreview',
      'setSelectedList',
    ]),

    isCurrentTab(tabNo) {
      return this.activeTab === tabNo;
    },

    async onClickApply() {
      // if calling from Upload page, just need to close modal -> the applying step will process from Upload Request.js
      if (!this.isUploadPage) {
        const responses = [];

        this.loadingApply = true;

        this.applyingDatasetIds.forEach(id => {
          responses.push(
            savedActionApi.applyListsToDataset(this.selectedLists, id, this.excludeAuto),
          );
        });

        await Promise.all(responses);

        this.loadingApply = false;

        this.setDatasetsState({ datasetsState: NetworkStatus.LOADING });
        await datasetsRequestV0.getDatasets();
      }

      this.closeModal();
    },

    onClickCancel() {
      this.closeModal();
    },

    onClickContinue() {
      this.setSelectedList({ list: this.selectedItem });
      this.onClickCancel();
    },

    onClickGoToMemory() {
      if (this.$router.currentRoute?.name !== Route.DATASETS) {
        this.$router.push({ name: Route.DATASETS });
      }
      this.setViewingMemoryPageTab();
      this.closeModal();
    },

    onSelectItem(item) {
      this.selectedItem = item;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.workspace-memory-collections-modal {
  @include panel;
  position: relative;
  width: 800px;

  .header {
    @include flex('block', 'column', 'center', 'start');
    border-bottom: $border-light solid $border-color;
    padding: 1.5rem 1.5rem 1rem;

    .title-wrapper {
      display: grid;
      grid-template-columns: 1fr;

      &.isApplying {
        grid-column-gap: 1rem;
        grid-template-columns: auto 1fr;
      }

      .title {
        .text {
          @include truncate;
          color: #2D1757;
          font-size: $font-size-base;
          font-weight: $font-weight-bold;
        }
      }

      .sub-title {
        align-content: center;
        background: rgba(95, 82, 197, 0.12);
        border-radius: 3px;
        color: rgba(95, 82, 197, 1);
        display: grid;
        font-size: 10px;
        font-weight: 700;
        padding: 0.2rem 0.3rem;
        text-transform: uppercase;
      }
    }

    .collection-types {
      @include flex('block', 'row', 'start', 'center');

      color: #2D1757;
      cursor: pointer;
      margin-top: 1.5rem;

      .collection-type {
        @include flex('block', 'row', 'center', 'center');

        &.inactive {
          color: rgba(154, 154, 154, 1);
        }

        &:not(:first-child) {
          margin-left: 2rem;
        }

        span {
          font-size: 10px;
          font-weight: 800;
          letter-spacing: 0.3px;
          line-height: 12px;
          text-align: center;
          text-transform: uppercase;
        }

        .icon {
          margin-right: 0.5rem;
        }
      }
    }
  }

  .body {
    height: 450px;
  }

  .footer {
    @include flex('block', 'row', 'between', 'center');
    @include rigid;
    border-top: $border-light solid $border-color;
    padding: 1.5rem 1.5rem;

    .base-button {
      font-size: 0.65em;
      font-weight: 600;
      text-transform: uppercase;

      &.cancel-btn {
        padding: 0.5rem 1rem 0.5rem 0;
      }

      &.done-btn {
        background: #2D1757;
        padding: 0.5rem 1.5rem;
      }
    }

    .loading-blocks-overlay {
      height: 28px;
    }

    .apply-options {
      @include flex("block", "row", "start", "stretch");

      .checkbox {
        @include flex("block", "row", "center", "center");

        cursor: pointer;
        font-size: $font-size-xs;
        margin-right: 1rem;

        .base-checkbox {
          border-color: clr('purple', 'deep');
          margin-right: 0.5rem;
        }
      }
    }
  }
}
</style>
