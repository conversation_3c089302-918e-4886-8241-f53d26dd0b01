<template>
  <section class="themes-table-list-item" :class="{ active, deleted }">
    <section v-if="!isSwotTable" class="send" @click.capture.stop="onClickCheckbox">
      <arrow-right-circle-icon
        class="icon"
        :class="{ active: isCustomTheme(theme) }"
        v-tooltip.right="{
          class: 'tooltip-send-to-chart',
          content: isCustomTheme(theme) ? 'Remove from Custom Chart' : 'Send to Custom Chart',
          delay: 0,
        }"
      />
    </section>

    <themes-table-list-item-edit v-if="editing" :theme="theme" />

    <themes-table-list-item-score
      v-else
      :active="active"
      :label="theme.topicLabel"
      :score="Math.round(theme.polarity * 100)"
    />

    <span
      class="topics-count"
      :class="{ visible: topicsVisible }"
    >{{ theme.numOfSubTopics || 0 }}</span>

    <span
      class="comments-count"
      :class="{ visible: snippetsVisible }"
    >{{ theme.numOfDocuments || 0 }}</span>

    <themes-table-list-item-index
      v-for="(item, index) in indices"
      :key="item.name"
      :color="indexColor(item)"
      :score="score(item)"
      :visible="isIndexVisible(item)"
      :class="{ lastChild: index === indices.length - 1 }"
    ></themes-table-list-item-index>

    <themes-table-list-item-index
      v-for="(attr, index) in swot"
      :key="attr.name"
      :color="swotColor(attr)"
      :score="swotScore"
      :visible="isSwotVisible(attr)"
      :class="{ lastChild: index === indices.length - 1 }"
    ></themes-table-list-item-index>
  </section>
</template>

<script>
import { ArrowRightCircleIcon } from 'vue-feather-icons';
import { mapActions, mapGetters, mapState } from 'vuex';

import Index from '@/enum/index';
import intercomEvent from '@/services/IntercomEvent';
import intercomEvents from '@/enum/intercom-events';
import Route from '@/enum/route';
import Swot from '@/enum/swot';
import ThemeTableSort from '@/enum/theme-table-sort';
import ThemeTableSortSwot from '@/enum/theme-table-sort-swot';
import ThemesTableListItemEdit from '@/components/ThemesTableList/ThemesTableListItemEdit';
import ThemesTableListItemIndex from '@/components/ThemesTableList/ThemesTableListItemIndex';
import ThemesTableListItemScore from '@/components/ThemesTableList/ThemesTableListItemScore';

import { position, type } from '@/helpers/index-utils';

export default {
  name: 'themes-table-list-item',

  components: {
    ArrowRightCircleIcon,
    ThemesTableListItemEdit,
    ThemesTableListItemIndex,
    ThemesTableListItemScore,
  },

  props: {
    editing: {
      type: Boolean,
      required: true,
    },

    theme: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      ThemeTableSort,
    };
  },

  computed: {
    ...mapState('themes', ['selectedTheme']),

    ...mapGetters('themes', ['isCustomTheme']),

    ...mapState('themesTable', {
      deletes: 'deletes',
      showSwot: 'showSwot',
    }),

    ...mapGetters('themesTable', ['isColumnVisible']),

    active() {
      if (this.selectedTheme == null) return false;

      return this.selectedTheme.id === this.theme.id;
    },

    deleted() {
      return this.deletes.includes(this.theme.id);
    },

    indices() {
      if (this.showSwot) return [];

      return Index.enumValues;
    },

    isSwotTable() {
      return this.$route.name === Route.SWOT_ANALYSIS;
    },

    snippetsVisible() {
      if (this.showSwot) return this.isColumnVisible(ThemeTableSortSwot.COMMENTS);

      return this.isColumnVisible(ThemeTableSort.COMMENTS);
    },

    swot() {
      if (!this.showSwot) return [];

      return Swot.enumValues;
    },

    swotScore() {
      return Math.round(this.theme.swot.intensity * 100);
    },

    topicsVisible() {
      if (this.showSwot) return this.isColumnVisible(ThemeTableSortSwot.SUBTOPICS);

      return this.isColumnVisible(ThemeTableSort.SUBTOPICS);
    },
  },

  methods: {
    ...mapActions('themes', ['addCustomThemes', 'removeCustomThemes']),

    indexColor(index) {
      return type(index) === 'g' ? '#35d61a' : '#ff4343';
    },

    isIndexVisible(index) {
      const indexName = index.name === 'FEAR' ? 'APPREHENSION' : index.name;
      const themeEnum = ThemeTableSort.enumValueOf(indexName);

      return this.isColumnVisible(themeEnum);
    },

    isSwotVisible(attr) {
      const themeEnum = ThemeTableSortSwot.enumValueOf(attr.name);

      return this.isColumnVisible(themeEnum);
    },

    onClickCheckbox() {
      if (this.isCustomTheme(this.theme)) {
        this.removeCustomThemes({ themes: [this.theme] });
      } else {
        this.addCustomThemes({ themes: [this.theme] });

        intercomEvent.send(intercomEvents.ADD_CUSTOM_THEME);
      }
    },

    score(index) {
      return Math.round(Math.abs(this.theme.emotionIndexes[position(index)] * 100));
    },

    swotColor(attr) {
      if (attr === Swot.WEAKNESS) return '#ff4343';
      if (attr === Swot.OPPORTUNITY) return '#0083ff';
      if (attr === Swot.THREAT) return '#fb9137';

      return '#35d61a';
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-table-list-item {
  @include flex("block", "row", "start", "center");

  cursor: pointer;
  font-size: $font-size-xs;

  &.active {
    border-left: 3px solid clr("purple");
    background-color: lighten(clr("purple"), 30%) !important;

    .send {
      padding-left: calc(#{$theme-table-checkbox-padding} - 3px);
    }
  }

  &.deleted {
    border-left: 3px solid clr("red") !important;
  }

  &.striped {
    background-color: clr("blue", "lighter");
  }

  &:hover:not(.active) {
    background-color: lighten(clr("purple"), 32%);
  }

  .send {
    padding: $theme-table-checkbox-padding;

    .icon {
      color: $body-copy-light;
      cursor: pointer;
      transition: all $interaction-transition-time;
      width: $font-size-base;

      &:hover {
        color: clr("blue");
      }

      &:active,
      &:focus {
        outline: none;
      }

      &.active {
        color: clr("blue");

        &:hover {
          color: clr("red");
        }
      }
    }
  }

  .topics-count,
  .comments-count {
    display: none;
    flex: 0 0 auto;
    width: $theme-table-count-width;

    &.visible {
      display: inherit;
    }
  }

  .themes-table-list-item-index {
    &.lastChild {
      margin-right: 0.8rem;
    }
  }
}
</style>
