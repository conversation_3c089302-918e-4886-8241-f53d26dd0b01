<template>
  <section class="themes-table-list-header">
    <section v-if="!isSwotTable" class="send" @click.capture.stop="onClickCheckbox">
      <arrow-left-circle-icon
        v-if="areAllAdded"
        class="icon remove"
        v-tooltip.right="{ class: 'tooltip-send-to-chart', content: 'Remove all from Custom Chart', delay: 0 }"
      />

      <arrow-right-circle-icon
        v-else
        class="icon add"
        v-tooltip.right="{ class: 'tooltip-send-to-chart', content: 'Send all to Custom Chart', delay: 0 }"
      />
    </section>

    <themes-table-list-header-item
      v-for="header in headers"
      :key="header.name"
      :class="[header.lowerCase()]"
      :header="header"
      :visible="isColumnVisible(header)"
    ></themes-table-list-header-item>
  </section>
</template>

<script>
import { differenceBy } from 'lodash-es';
import { ArrowLeftCircleIcon, ArrowRightCircleIcon } from 'vue-feather-icons';
import { mapActions, mapGetters, mapState } from 'vuex';

import Route from '@/enum/route';
import ThemesTableListHeaderItem from '@/components/ThemesTableList/ThemesTableListHeaderItem';
import ThemeTableSort from '@/enum/theme-table-sort';
import ThemeTableSortSwot from '@/enum/theme-table-sort-swot';

export default {
  name: 'themes-table-list-header',

  components: {
    ArrowLeftCircleIcon,
    ArrowRightCircleIcon,
    ThemesTableListHeaderItem,
  },

  computed: {
    ...mapState('themes', ['themes']),

    ...mapState('themesTable', ['showSwot']),

    ...mapGetters('themesTable', ['filtered', 'isColumnVisible']),

    areAllAdded() {
      return differenceBy(this.filtered, this.themes, 'id').length === 0;
    },

    headers() {
      return this.showSwot ? this.headersSwot : this.headersIndex;
    },

    headersIndex() {
      return ThemeTableSort.enumValues;
    },

    headersSwot() {
      return ThemeTableSortSwot.enumValues;
    },

    isSwotTable() {
      return this.$route.name === Route.SWOT_ANALYSIS;
    },
  },

  methods: {
    ...mapActions('themes', ['addCustomThemes', 'removeCustomThemes']),

    onClickCheckbox() {
      if (this.areAllAdded) this.removeCustomThemes({ themes: this.filtered });
      else this.addCustomThemes({ themes: this.filtered });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-table-list-header {
  @include flex("block", "row", "start", "stretch");

  border-bottom: $border-standard;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
  height: 40px;
  z-index: 99;

  .send {
    @include flex("block", "row", "center", "center");

    cursor: pointer;
    padding: 0 $theme-table-checkbox-padding;

    .icon {
      color: $body-copy-light;
      transition: all $interaction-transition-time;
      width: $font-size-base;

      &:hover {
        color: clr("blue");
      }

      &:active,
      &:focus {
        outline: none;
      }

      &.active {
        color: clr("blue");
      }
    }
  }

  .themes-table-list-header-item {
    @include size-evenly;

    padding: 0 0.5rem;

    &.adorescore {
      @include rigid;

      padding: 0 0 0 0.3rem;
      width: $theme-table-score-width;
    }

    &.apprehension {
      min-width: fit-content;
    }

    &.subtopics,
    &.comments {
      @include rigid;

      padding: 0;
      width: $theme-table-count-width;
    }
  }
}
</style>
