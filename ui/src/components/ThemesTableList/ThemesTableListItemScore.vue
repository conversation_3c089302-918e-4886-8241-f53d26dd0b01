<template functional>
  <section class="themes-table-list-item-score">
    <section class="score" :class="{ active: props.active, selected: props.selected }">
      <span>{{ Math.round(props.score) }}</span>
    </section>
    <section class="label">{{ props.label }}</section>
  </section>
</template>

<script>
export default {
  name: 'themes-table-list-item-score',

  functional: true,

  props: {
    active: {
      type: Boolean,
      default: false,
    },

    label: {
      type: String,
      required: true,
    },

    score: {
      type: Number,
      required: true,
    },

    selected: {
      type: <PERSON>olean,
      default: false,
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-table-list-item-score {
  @include flex("block", "row", "start", "center");
  @include truncate;

  flex: 0 0 auto;
  padding: 0 0.3rem;
  width: $theme-table-score-width;

  .score {
    @include flex("block", "row", "center", "center");

    background-color: clr("white");
    border: 1px solid $body-copy;
    border-radius: 0.4rem;
    font-size: $font-size-xxs;
    height: 1.7rem;
    min-height: 1.7rem;
    min-width: 1.7rem;
    transition: all $interaction-transition-time;
    width: 1.7rem;

    &.selected {
      border: 2px solid clr("blue");
    }

    &.active:not(.selected) {
      border: 1px solid clr("purple");
      background-color: clr("purple");
    }

    &.active {
      background-color: clr("blue");
      color: clr("white");
    }

    span {
      margin-top: 1px;
    }
  }

  .label {
    @include truncate;

    font-size: $font-size-xs;
    margin-left: 0.5rem;
  }
}
</style>
