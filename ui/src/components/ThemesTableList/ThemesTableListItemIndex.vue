<template>
  <section class="themes-table-list-item-index" :class="{ visible }">
    <section class="score">{{ score }}</section>
    <section class="bar">
      <section class="inner" :style="{ backgroundColor: color, width: `${score}%` }"></section>
    </section>
  </section>
</template>

<script>
export default {
  name: 'themes-table-list-item-index',

  props: {
    color: {
      type: String,
      default: '#35d61a',
    },

    score: {
      type: Number,
      required: true,
    },

    visible: {
      type: Boolean,
      required: true,
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-table-list-item-index {
  @include flex("block", "row", "center", "center");
  @include stretch;

  display: none;
  padding: 0 0.5rem;
  min-width: 80px;

  &.visible {
    display: flex;
  }

  .score {
    @include rigid;

    width: 1rem;
  }

  .bar {
    @include stretch;

    background-color: clr("blue", "light");
    border-radius: 3px;
    height: 6px;
    overflow: hidden;
    margin: 0 0.5rem;

    .inner {
      background-color: clr("red");
      height: 100%;

      &.positive {
        background-color: clr("green");
      }
    }
  }
}
</style>
