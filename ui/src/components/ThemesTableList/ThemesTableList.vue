<template>
  <section class="themes-table-list">
    <section class="content">
      <themes-table-list-header />
      <section class="list">
        <themes-table-list-item v-for="(theme, index) in list"
          :key="theme.id"
          :class="{ striped: index % 2 === 1 }"
          :editing="editing"
          :theme="theme"
          @click.native="onSelect(theme)"
        ></themes-table-list-item>
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import Route from '@/enum/route';
import ThemesTableListHeader from '@/components/ThemesTableList/ThemesTableListHeader';
import ThemesTableListItem from '@/components/ThemesTableList/ThemesTableListItem';

export default {
  name: 'themes-table-list',

  components: {
    ThemesTableListHeader,
    ThemesTableListItem,
  },

  computed: {
    ...mapState('themesTable', ['editing']),

    ...mapGetters('themesTable', ['filtered', 'filteredSwot']),

    list() {
      if (this.$route.name === Route.SWOT_ANALYSIS) return this.filteredSwot;

      return this.filtered;
    },
  },

  methods: {
    ...mapActions('themes', ['selectTheme']),

    onSelect(theme) {
      this.selectTheme({ theme });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-table-list {
  @include flex("block", "column", "start", "stretch");

  min-height: 350px;

  .content {
    height: 100%;
    overflow-y: hidden;
    position: absolute;
    width: 100%;

    .themes-table-list-item {
      height: 40px;
    }
  }

  .list {
    height: 90%;
    min-width: fit-content;
    overflow-x: auto;
    overflow-y: auto;
  }

  .themes-table-list-header {
    @include rigid;
    min-width: fit-content;
    position: relative;
    width: 100%;
  }
}
</style>
