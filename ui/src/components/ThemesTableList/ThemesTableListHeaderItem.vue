<template>
  <section class="themes-table-list-header-item" :class="{ visible }">
    <span :class="{ active }" @click="onClick">{{ header.titleCase() }}</span>
    <section class="sort">
      <chevron-up-icon class="icon" :class="{ active: active && asc }" @click="setAsc" />
      <chevron-down-icon class="icon" :class="{ active: active && !asc }" @click="setDesc" />
    </section>
  </section>
</template>

<script>
import { ChevronDownIcon, ChevronUpIcon } from 'vue-feather-icons';
import { mapActions, mapState } from 'vuex';

import SortDirection from '@/enum/sort-direction';

export default {
  name: 'themes-table-list-header-item',

  components: {
    ChevronUpIcon, ChevronDownIcon,
  },

  props: {
    header: {
      type: Object,
      required: true,
    },

    visible: {
      type: Boolean,
      required: true,
    },
  },

  computed: {
    ...mapState('themesTable', ['sort', 'sortDirection']),

    active() {
      return this.sort === this.header;
    },

    asc() {
      return this.sortDirection === SortDirection.ASC;
    },
  },

  methods: {
    ...mapActions('themesTable', ['setSort', 'setSortDirection']),

    onClick() {
      if (this.active) {
        this.setSortDirection({ sortDirection: this.sortDirection.inverse });
      } else {
        this.setSort({ sort: this.header });
        this.setSortDirection({ sortDirection: SortDirection.DESC });
      }
    },

    setAsc() {
      this.setSort({ sort: this.header });
      this.setSortDirection({ sortDirection: SortDirection.ASC });
    },

    setDesc() {
      this.setSort({ sort: this.header });
      this.setSortDirection({ sortDirection: SortDirection.DESC });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-table-list-header-item {
  @include flex("block", "row", "start", "center");

  display: none;
  font-size: $font-size-xxs;
  min-width: $theme-table-count-width;;

  &.visible {
    display: flex;
  }

  span {
    cursor: pointer;

    &:hover,
    &.active {
      color: clr("purple");
      font-weight: $font-weight-bold;
    }
  }

  .sort {
    @include flex("block", "column", "start", "center");

    margin: 0 0.2rem;

    .icon {
      color: $body-copy-light;
      cursor: pointer;
      height: $font-size-xs;
      margin: -0.1rem 0;
      stroke-width: 3px;
      width: $font-size-xs;

      &:hover,
      &.active {
        color: clr("purple");
      }
    }
  }
}
</style>
