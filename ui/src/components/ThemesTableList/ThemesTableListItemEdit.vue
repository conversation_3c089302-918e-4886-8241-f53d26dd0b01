<template>
  <section class="themes-table-list-item-edit" @click.stop>
    <section class="wrapper">
      <base-button v-if="isDeleted" colour="light" icon="slash" @click.stop="onClickCancel" />
      <base-button v-else colour="danger" icon="trash-2" @click.stop="onClickDelete" />
      <base-input v-model.lazy="label"></base-input>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import BaseInput from '@/components/Base/BaseInput';

export default {
  name: 'themes-table-list-item-edit',

  components: {
    BaseButton,
    BaseInput,
  },

  props: {
    theme: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapState('themesTable', ['deletes', 'renames']),

    isDeleted() {
      return this.deletes.includes(this.theme.id);
    },

    isRenamed() {
      return this.renames.find(r => r.id === this.theme.id) != null;
    },

    label: {
      get() {
        if (this.isRenamed) return this.rename.label;

        return this.theme.topicLabel;
      },
      set(value) {
        this.addRename({ id: this.theme.id, label: value });
      },
    },

    rename() {
      return this.renames.find(r => r.id === this.theme.id);
    },
  },

  methods: {
    ...mapActions('themesTable', [
      'addRename',
      'addDelete',
      'removeDelete',
    ]),

    onClickCancel() {
      this.removeDelete({ id: this.theme.id });
    },

    onClickDelete() {
      this.addDelete({ id: this.theme.id });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.themes-table-list-item-edit {
  @include flex("block", "row", "start", "stretch");

  height: 100%;
  width: $theme-table-score-width;

  .wrapper {
    @include flex("block", "row", "start", "center");
  }

  .base-button {
    font-size: $font-size-xxs;
    padding: 0;
    padding-left: 0.4rem;
    height: 1.8rem;
    width: 1.8rem;
  }

  .base-input {
    margin: 0 0.5rem;
    height: 60%;
  }
}
</style>
