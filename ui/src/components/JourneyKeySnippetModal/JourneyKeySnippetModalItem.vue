<template>
  <section class="journey-key-snippet-modal-item">
    {{ snippet.content }}
  </section>
</template>

<script>
export default {
  name: 'journey-key-snippet-modal-item',

  props: {
    snippet: {
      type: Object,
      required: true,
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.journey-key-snippet-modal-item {
  @include flex("block", "row", "between", "center");
  @include panel;

  cursor: pointer;
  font-size: $font-size-sm;
  padding: 0.5rem;
  margin-bottom: 0.5rem;
  transition: all $interaction-transition-time;

  &:hover {
    border: 1px solid clr('blue');
  }

  &:last-child {
    margin-bottom: 0;
  }
}
</style>
