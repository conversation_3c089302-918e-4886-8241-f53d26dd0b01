<template>
  <section class="journey-key-snippet-modal">
    <section class="header">
      <h2>Select Snippet</h2>
    </section>

    <section class="body">
      <section class="list">
        <journey-key-snippet-modal-item v-for="snippet in snippets"
          :key="snippet.userDocId"
          :snippet="snippet"
          @click.native.stop="onSelectSnippet(snippet)"/>
        <base-button v-if="displayLoadMore" display="block" size="small" @click="loadMore">Load More...</base-button>
      </section>
    </section>

    <section class="footer">
      <base-button colour="light" size="small" type="link" @click="onClickCancel">Cancel</base-button>
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';
import JourneyKeySnippetModalItem from '@/components/JourneyKeySnippetModal/JourneyKeySnippetModalItem';

import { journeyApi, snippetApi } from '@/services/api';

export default {
  name: 'journey-key-snippet-modal',

  components: {
    BaseButton,
    JourneyKeySnippetModalItem,
  },

  data() {
    return {
      loading: false,
      meta: {},
      page: 0,
      snippets: [],
    };
  },

  computed: {
    ...mapState('datasets', ['active']),

    ...mapState('journey', ['stageToUpdate']),

    displayLoadMore() {
      return this.loading !== true
        && this.meta.count != null
        && this.snippets.length < this.meta.count;
    },
  },

  async created() {
    const stage = this.stageToUpdate;

    this.loading = true;

    try {
      const meta = await snippetApi.fetchKeySnippets(this.active, {
        size: 50,
        page: this.page,
        topicId: stage.exemplarTopicId,
      });

      this.meta = meta;
      this.page += 1;
      this.snippets = [...this.snippets, ...this.meta.snippets];
    } finally {
      this.loading = false;
    }
  },

  methods: {
    ...mapActions('modal', ['closeModal']),

    async loadMore() {
      this.loading = true;

      try {
        const meta = await snippetApi.fetchKeySnippets(this.active, {
          size: 50,
          page: this.page,
          topicId: this.stageToUpdate.exemplarTopicId,
        });

        this.meta = meta;
        this.page += 1;
        this.snippets = [...this.snippets, ...this.meta.snippets];
      } finally {
        this.loading = false;
      }
    },

    onClickCancel() {
      this.closeModal();
    },

    async onSelectSnippet(snippet) {
      this.closeModal();

      const stage = this.stageToUpdate;
      if (stage.label && stage.label.trim() !== '') {
        await journeyApi.updateStageBasic(
          this.active,
          stage.journeyId,
          stage.id,
          stage.label,
          stage.position,
          stage.exemplarTopicId,
          snippet.userDocId,
        );
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.journey-key-snippet-modal {
  @include modal;

  .body {
    @include flex("block", "column", "start", "stretch");

    overflow: hidden;
  }

  .list {
    @include flex("block", "column", "start", "stretch");
    @include stretch;

    overflow-y: auto;
  }
}
</style>
