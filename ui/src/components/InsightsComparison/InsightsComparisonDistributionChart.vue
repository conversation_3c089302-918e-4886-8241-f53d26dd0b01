<template>
  <svg
    :id="`distribution-chart-insights-comparison`"
    class="insights-comparison-distribution-chart"
    :width="width"
    :height="height"
  >
    <g :transform="`translate(${width/2},${height/2})`">
      <g class="axis-wrapper">
        <!-- Outer bounding circle -->
        <polygon
          fill="white"
          :points="getPolygonPoints(0)"
          stroke="#5F52A4"
          stroke-width="1"
        />
        <!-- Inner axis markers -->
        <polygon
          v-for="c in innerCircles"
          :key="`axis-circle-${c}`"
          fill="none"
          :points="getPolygonPoints(c)"
          stroke="#aba6b4"
          stroke-width="1"
        />
        <!-- Inner axis text -->
        <text
          v-for="c in innerCircles"
          :key="`axis-text-${c}`"
          style="font-size: 8px;"
          text-anchor="middle"
          x="0"
          :y="-(radius / (innerCircles + 1) * c + 4)"
        >{{ getInnerCircleTexts(c) }}</text>

        <!-- Axis lines -->
        <line
          v-for="index in axisNames.length"
          :key="`axis-line-${index}`"
          :x1="0"
          :y1="0"
          :x2="radius * getAngleX(index)"
          :y2="radius * getAngleY(index)"
          stroke="#aba6b4"
          stroke-dasharray="2 4"
          stroke-width="1"
        />

        <text
          v-for="(obj, index) in axisNames"
          :key="`axis-text-${obj.name}`"
          :fill="labelColor"
          style="font-size: 11px; font-weight: bold; text-transform: uppercase"
          text-anchor="middle"
          :x="radius * obj.offset * getAngleX(index)"
          :y="radius * obj.offset * getAngleY(index) + 4"
        >{{ obj.name }}</text>
      </g>

      <!-- Paths -->
      <g class="area-wrapper-benchmark">
        <path
          :d="radarLine(pathDataBenchmark)"
          :stroke="benchmarkColor"
          :stroke-width="lineSize"
          :fill="benchmarkColor"
          :fill-opacity="areaOpacity"
        />
      </g>

      <g class="area-wrapper-comparison">
        <path
          :d="radarLine(pathDataComparison)"
          :stroke="comparisonColor"
          :stroke-width="lineSize"
          :fill="comparisonColor"
          :fill-opacity="areaOpacity"
        />
      </g>
    </g>
  </svg>
</template>

<script>
import { curveLinearClosed, lineRadial, scaleLinear } from 'd3';

import DistributionChartEmotionButton from '@/components/DistributionChart/DistributionChartEmotionButton';
import indexes from '@/helpers/indexes';

const indexTextOffsets = {
  joy: 1.1,
  trust: 1.15,
  apprehension: 1.45,
  surprise: 1.2,
  sadness: 1.1,
  disgust: 1.2,
  anger: 1.25,
  interest: 1.2,
};

export default {
  name: 'insights-comparison-distribution-chart',

  components: {
    DistributionChartEmotionButton,
  },

  props: {
    data: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      areaOpacity: 0.3,
      benchmarkColor: '#F419DB',
      comparisonColor: '#5631FF',
      height: 330,
      innerCircles: 3,
      labelColor: '#5A51FF',
      lineOpacity: 0.5,
      lineSize: 3,
      rotation: 0,
      margin: 40,
      width: 500,
    };
  },

  computed: {
    angleSlice() {
      return Math.PI * 2 / this.axisNames.length;
    },

    axisNames() {
      return indexes
        .sort((a, b) => {
          if (a.plutchikWheelOrder > b.plutchikWheelOrder) {
            return 1;
          }
          if (b.plutchikWheelOrder > a.plutchikWheelOrder) {
            return -1;
          }
          return 0;
        })
        .map(i => {
          return {
            name: i.label,
            index: i.position.display,
            offset: indexTextOffsets[i.label.toLowerCase()],
          };
        });
    },

    pathDataBenchmark() {
      return this.axisNames.map(i => this.data.benchmark[i.name]);
    },

    pathDataComparison() {
      return this.axisNames.map(i => this.data.comparison[i.name]);
    },

    radarLine() {
      return lineRadial()
        .radius(d => {
          return this.rScale(d);
        })
        .angle((d, i) => {
          return (this.getRotatedPosition(i + this.rotation)) * this.angleSlice;
        })
        .curve(curveLinearClosed);
    },

    radius() {
      return Math.min(this.width, this.height) / 2 - this.margin;
    },

    rScale() {
      return scaleLinear()
        .range([0, this.radius * 0.95])
        .domain(this.data.range);
    },
  },

  methods: {
    getAngleX(i) {
      return Math.cos(this.angleSlice * (this.getRotatedPosition(i + this.rotation)) - Math.PI / 2);
    },

    getAngleY(i) {
      return Math.sin(this.angleSlice * (this.getRotatedPosition(i + this.rotation)) - Math.PI / 2);
    },

    getInnerCircleTexts(index) {
      const segment = (this.data.range[1] - this.data.range[0]) * 0.25 * index;

      return Math.round(segment * 10) / 10 + this.data.range[0];
    },

    getPointCoord(posRatio, iVal) {
      const ratio = posRatio === 0 ? 1 : 0.25 * posRatio;
      const x = this.radius * ratio * this.getAngleX(iVal);
      const y = this.radius * ratio * this.getAngleY(iVal);

      return { x, y };
    },

    getPolygonPoints(posRatio) {
      let points = '';

      for (let i = 0; i < indexes.length; i += 1) {
        const { x, y } = this.getPointCoord(posRatio, i);
        points += ` ${x},${y}`;
      }

      return points.trim();
    },

    getRotatedPosition(i) {
      if (i < 0) {
        return this.getRotatedPosition(8 + i);
      }
      if (i > 7) {
        return this.getRotatedPosition(i - 8);
      }

      return i;
    },
  },
};
</script>
