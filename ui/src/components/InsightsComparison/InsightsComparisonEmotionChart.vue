<template>
  <section class="insights-comparison-emotion-chart">
    <section class="info">
      <section class="left">
        <section class="header">Emotional Signature Chart</section>
        <section class="description">Chart ranges from {{ minValue }} to {{ maxValue }}</section>
      </section>

      <section class="right">
        <section class="key">
          <section class="text">Key</section>

          <section class="list">
            <section class="item">
              <section class="mark benchmark"></section>
              <section class="label">{{ benchmark.label }}</section>
            </section>

            <section class="item">
              <section class="mark comparison"></section>
              <section class="label">{{ comparison.label }}</section>
            </section>
          </section>
        </section>
      </section>
    </section>

    <section class="chart">
      <insights-comparison-distribution-chart :data="chartData"/>
    </section>
  </section>
</template>

<script>
import { mapGetters, mapState } from 'vuex';

import InsightsComparisonDistributionChart from '@/components/InsightsComparison/InsightsComparisonDistributionChart';

import { nameByApiIndex } from '@/helpers/index-utils';

export default {
  name: 'insights-comparison-emotion-chart',

  components: {
    InsightsComparisonDistributionChart,
  },

  computed: {
    ...mapGetters('datasets', ['get']),

    ...mapState('datasetsInsights', [
      'datasetBenchmark',
      'datasetComparison',
      'summaries',
    ]),

    benchmark() {
      return this.get(this.datasetBenchmark);
    },

    benchmarkIndexScores() {
      return this.benchmarkSummary.indexScores.map(s => Math.abs(s));
    },

    benchmarkSummary() {
      return this.summaries.find(s => s.id === this.datasetBenchmark);
    },

    chartData() {
      const benchmarkData = this.reduceChartData(this.benchmarkIndexScores);
      const comparisonData = this.reduceChartData(this.comparisonIndexScores);

      return {
        benchmark: benchmarkData,
        comparison: comparisonData,
        range: [this.minValue, this.maxValue],
      };
    },

    comparison() {
      return this.get(this.datasetComparison);
    },

    comparisonIndexScores() {
      return this.comparisonSummary.indexScores.map(s => Math.abs(s));
    },

    comparisonSummary() {
      return this.summaries.find(s => s.id === this.datasetComparison);
    },

    maxValue() {
      return Math.ceil(Math.max(...this.benchmarkIndexScores, ...this.comparisonIndexScores) / 10) * 10;
    },

    minValue() {
      return Math.floor(Math.min(...this.benchmarkIndexScores, ...this.comparisonIndexScores) / 10) * 10;
    },
  },

  methods: {
    reduceChartData(scores) {
      return scores.reduce((acc, score, i) => {
        acc[nameByApiIndex(i)] = score;

        return acc;
      }, {});
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-comparison-emotion-chart {
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: 1fr;
  padding: 0 30px;

  .info {
    @include flex("block", "row", "start", "stretch");

    grid-area: 1 / 1 / 2 / 2;

    .left {
      @include size-evenly;

      .header {
        font-size: $font-size-sm;
        font-weight: $font-weight-bold;
      }

      .description {
        font-size: $font-size-xs;
        margin-top: 0.5rem;
      }
    }

    .right {
      @include flex("block", "row", "end", "start");
      @include size-evenly;

      .key {
        @include flex("block", "row", "start", "stretch");

        border: 1px solid $insights-compare-border;
        border-radius: $border-radius-medium;
        font-size: $font-size-xs;

        .text {
          @include flex("block", "row", "center", "center");
          @include stretch;

          border-right: 1px solid $insights-compare-border;
          font-weight: $font-weight-bold;
          padding: 0 0.5rem;
          text-transform: uppercase;
        }

        .list {
          padding: 0.2rem 0.5rem;

          .item {
            @include flex("block", "row", "start", "center");

            max-width: 10rem;
            padding: 0.2rem 0;

            .mark {
              @include rigid;

              height: 0.7rem;
              margin-right: 0.3rem;
              width: 0.7rem;

              &.benchmark {
                border: 2px solid $insights-compare-benchmark;
                background-color: rgba($insights-compare-benchmark, 0.7);
              }

              &.comparison {
                border: 2px solid $insights-compare-comparison;
                background-color: rgba($insights-compare-comparison, 0.7);
              }
            }

            .label {
              @include truncate;

              font-weight: $font-weight-bold;
            }
          }
        }
      }
    }
  }

  .chart {
    @include flex("block", "row", "center", "center");

    grid-area: 1 / 1 / 2 / 2;

    .insights-comparison-distribution-chart {
      margin: -2rem;
    }
  }
}
</style>
