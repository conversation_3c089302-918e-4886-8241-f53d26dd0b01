<template>
  <section class="insights-comparison-top-indicators">
    <section class="right-bg"></section>

    <section class="border">
      <section class="line"></section>
    </section>

    <insights-top-indicators class="indicators-benchmark"
                             :dataset-id="datasetBenchmark"
                             :presentation="presentation"
                             :side="'left'"/>
    <insights-top-indicators class="indicators-comparison"
                             :dataset-id="datasetComparison"
                             :presentation="presentation"
                             :side="'right'"/>
  </section>
</template>

<script>
import { mapState } from 'vuex';
import InsightsTopIndicators from '@/components/InsightsTopIndicators/InsightsTopIndicators';

export default {
  name: 'insights-comparison-top-indicators',

  components: {
    InsightsTopIndicators,
  },

  props: {
    presentation: {
      type: Boolean,
      default: false,
    },
  },

  computed: {
    ...mapState('datasetsInsights', ['datasetBenchmark', 'datasetComparison']),
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-comparison-top-indicators {
  @include stretch;

  display: grid;
  grid-template-columns: 50% 50%;
  grid-template-rows: 1fr;
  height: 100%;

  .border {
    @include flex("block", "row", "center", "center");

    grid-area: 1 / 1 / 2 / 3;

    .line {
      border-right: 1px solid $insights-compare-border;
      height: 100%;
    }
  }

  .right-bg {
    background-color: $insights-compare-comparison-bg;
    grid-area: 1 / 2 / 2 / 3;
  }

  .indicators-benchmark {
    grid-area: 1 / 1 / 2 / 2;
  }

  .indicators-comparison {
    grid-area: 1 / 2 / 2 / 3;
  }
}
</style>
