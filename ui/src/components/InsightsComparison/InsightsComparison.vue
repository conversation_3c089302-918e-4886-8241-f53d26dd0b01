<template>
  <section class="insights-comparison">
    <insights-comparison-selector/>
    <insights-comparison-overview/>
    <insights-comparison-top-indicators/>
  </section>
</template>

<script>
import InsightsComparisonOverview from '@/components/InsightsComparison/InsightsComparisonOverview';
import InsightsComparisonSelector from '@/components/InsightsComparison/InsightsComparisonSelector';
import InsightsComparisonTopIndicators from '@/components/InsightsComparison/InsightsComparisonTopIndicators';

export default {
  name: 'insights-comparison',

  components: {
    InsightsComparisonOverview,
    InsightsComparisonSelector,
    InsightsComparisonTopIndicators,
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-comparison {
  @include flex("block", "column", "start", "stretch");
  @include stretch;

  overflow: auto;
  scrollbar-width: thin;
  width: 800px;

  .insights-comparison-selector {
    height: 50px;
  }
}
</style>
