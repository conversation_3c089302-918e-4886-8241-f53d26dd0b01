<template>
  <section class="insights-comparison-summary">
    <section class="section left">
      <section class="text">
        <span>
          Benchmark
          <span v-if="benchmark.adoreScore > comparison.adoreScore">
            is {{ Math.abs(benchmarkDiff) }} points better than Comparison
          </span>
          <span v-if="benchmark.adoreScore < comparison.adoreScore">
            is {{ Math.abs(benchmarkDiff) }} points worse off than Comparison
          </span>
          <span v-if="benchmark.adoreScore === comparison.adoreScore">
            is performing the same as Comparison
          </span>
        </span>
      </section>
      <adorescore-box :score="benchmark.adoreScore"/>
    </section>

    <section class="section right">
      <adorescore-box :score="comparison.adoreScore"/>
      <section class="text">
        <span>
          Comparison
          <span v-if="comparison.adoreScore > benchmark.adoreScore">
            is {{ Math.abs(benchmarkDiff) }} points better than Benchmark
          </span>
          <span v-if="comparison.adoreScore < benchmark.adoreScore">
            is {{ Math.abs(comparisonDiff) }} points worse off than Benchmark
          </span>
          <span v-if="comparison.adoreScore === benchmark.adoreScore">
            is performing the same as Benchmark
          </span>
        </span>
      </section>
    </section>
  </section>
</template>

<script>
import { mapGetters, mapState } from 'vuex';

import AdorescoreBox from '@/components/AdorescoreBox/AdorescoreBox';

export default {
  name: 'insights-comparison-summary',

  components: {
    AdorescoreBox,
  },

  computed: {
    ...mapGetters('datasets', ['get']),

    ...mapState('datasetsInsights', ['datasetBenchmark', 'datasetComparison']),

    benchmark() {
      return this.get(this.datasetBenchmark);
    },

    benchmarkDiff() {
      return this.benchmark.adoreScore - this.comparison.adoreScore;
    },

    comparison() {
      return this.get(this.datasetComparison);
    },

    comparisonDiff() {
      return this.comparison.adoreScore - this.benchmark.adoreScore;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-comparison-summary {
  @include flex("block", "row", "between", "center");

  padding: 1rem 30px;

  .section {
    @include flex("block", "row", "start", "center");
    @include size-evenly;

    &.left {
      margin-right: 25px;

      .text {
        background-color: $insights-compare-benchmark;
        border: 1px solid $insights-compare-benchmark-dark;
        margin-right: 1rem;
      }
    }

    &.right {
      margin-left: 25px;

      .text {
        background-color: $insights-compare-comparison;
        border: 1px solid $insights-compare-comparison-dark;
        margin-left: 1rem;
      }
    }

    .text {
      @include flex("block", "row", "center", "center");

      border-radius: $border-radius-medium;
      color: clr('white');
      font-size: $font-size-xs;
      height: 3rem;
      line-height: 1rem;
      padding: 0.5rem;

      .label {
        font-weight: $font-weight-bold;
      }
    }
  }
}
</style>
