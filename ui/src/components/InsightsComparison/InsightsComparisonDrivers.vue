<template>
  <section class="insights-comparison-drivers" :class="[side]">
    <section class="header" :class="[side]">Key Drivers</section>

    <section class="list">
      <section v-for="[key, value] in topDrivers" :key="key" class="row">
        <section v-if="side === 'left'" class="label" :class="[side]">{{ key }}</section>
        <section class="value" :class="{ positive: isPositiveEmotion(key), negative: !isPositiveEmotion(key) }">{{ Math.abs(value) }}</section>
        <section v-if="side === 'right'" class="label" :class="[side]">{{ key }}</section>
      </section>
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import { nameByApiIndex } from '@/helpers/index-utils';
import Index from '@/enum/index';

export default {
  name: 'insights-comparison-drivers',

  props: {
    compareId: {
      type: Number,
      required: true,
    },

    id: {
      type: Number,
      required: true,
    },

    side: {
      type: String,
      required: true,
    },
  },

  data() {
    return {
      Index,
    };
  },

  computed: {
    ...mapState('datasetsInsights', ['summaries']),

    topDrivers() {
      const indexScores = this.summary.indexScores.reduce((acc, score, i) => {
        acc[nameByApiIndex(i)] = score;

        return acc;
      }, {});

      return Object.entries(indexScores)
        .sort((a, b) => Math.abs(b[1]) - Math.abs(a[1]))
        .slice(0, 3);
    },

    summary() {
      return this.summaries.find(s => s.id === this.id);
    },
  },

  methods: {
    isPositiveEmotion(key) {
      const localKey = key === 'Apprehension' ? 'Fear' : key;

      return Index[localKey.toUpperCase()].isPositiveEmotion();
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-comparison-drivers {
  @include flex("block", "column", "start", "stretch");
  @include stretch;

  border: 1px solid $insights-compare-border;
  border-radius: $border-radius-medium;

  &.left {
    border-right: none;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    margin-left: 1rem;
  }

  &.right {
    border-left: none;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    margin-right: 1rem;
  }

  .header {
    @include flex("block", "row", "center", "center");

    color: clr('white');
    font-size: $font-size-xs;
    font-weight: $font-weight-bold;
    height: 25px;
    padding: 0 1rem;
    text-transform: uppercase;

    &.left {
      justify-content: right;
      background-color: $insights-compare-benchmark;
    }

    &.right {
      justify-content: left;
      background-color: $insights-compare-comparison;
    }
  }

  .list {
    @include flex("block", "column", "start", "stretch");

    padding: 0.5rem 0;

    .row {
      @include flex("block", "row", "between", "center");

      font-size: $font-size-xs;
      padding: 0.2rem 1rem;

      .label {
        @include stretch;

        &.left {
          margin-right: 0.5rem;
          text-align: right;
        }

        &.right {
          margin-left: 0.5rem;
          text-align: left;
        }
      }

      .value {
        @include flex("block", "row", "center", "center");

        background-color: clr('white');
        border: $border-standard;
        border-radius: $border-radius-small;
        font-weight: $font-weight-bold;
        padding: 0.2rem 0.3rem;
        width: 2.5rem;

        &.positive {
          color: $adored;
        }

        &.negative {
          color: $floored;
        }
      }
    }
  }
}
</style>
