<template>
  <section class="insights-comparison-selector-menu">
    <section class="guide">
      Select Dataset for {{ isBenchmark ? 'Benchmark' : 'Comparison' }}:
    </section>

    <section class="list">
      <section v-for="dataset in datasets" :key="dataset.id" class="item"
        :class="{
          active: isBenchmark && dataset.id === datasetBenchmark || !isBenchmark && dataset.id === datasetComparison
        }"
        @click="onClickItem(dataset.id)"
      >
        <section class="left">
          <section class="radio">
            <i class="fa fa-check"></i>
          </section>
          <section class="label">{{ dataset.label }}</section>
        </section>

        <common-adore-score-square :color-border="true" :footer="true" :score="dataset.adoreScore"/>
      </section>
    </section>
  </section>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';

import BlurCloseable from '@/components/Mixins/BlurCloseable';
import CommonAdoreScoreSquare from '@/components/CommonComponent/CommonAdoreScoreSquare';

export default {
  name: 'insights-comparison-selector-menu',

  mixins: [BlurCloseable],

  components: {
    CommonAdoreScoreSquare,
  },

  props: {
    isBenchmark: {
      type: Boolean,
      required: true,
    },
  },

  computed: {
    ...mapGetters('datasets', ['get']),

    ...mapState('datasetsInsights', [
      'datasetBenchmark',
      'datasetComparison',
      'selectedIds',
    ]),

    datasets() {
      return this.selectedIds.map(id => {
        return this.get(id);
      });
    },
  },

  methods: {
    ...mapActions('datasetsInsights', ['setDatasetBenchmark', 'setDatasetComparison']),

    onClickItem(id) {
      if (this.isBenchmark) this.setDatasetBenchmark({ id });
      else this.setDatasetComparison({ id });

      if (this.isBenchmark && id === this.datasetComparison) this.setDatasetComparison({ id: this.datasetBenchmark });
      if (!this.isBenchmark && id === this.datasetBenchmark) this.setDatasetBenchmark({ id: this.datasetComparison });

      this.$emit('close');
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-comparison-selector-menu {
  @include flex("block", "column", "start", "stretch");

  background-color: clr('white');
  border: 1px solid $insights-compare-border-dark;
  box-shadow: rgba(0, 0, 0, 0.2) 0 2px 4px 0;
  padding: 1rem;

  .guide {
    font-size: $font-size-xs;
    margin-bottom: 0.5rem;
    margin-left: 1rem;
  }

  .list {
    @include stretch;

    overflow-y: auto;

    .item {
      @include flex("block", "row", "between", "center");

      cursor: pointer;
      font-size: 0.9rem;
      padding: 0.5rem 1rem;

      &:hover {
        background-color: $insights-compare-comparison-bg;
      }

      &.active {
        pointer-events: none;

        .left  .radio {
          background-color: #695cf9;
          border-color: $insights-compare-border-dark;
        }
      }

      .left {
        @include flex("block", "row", "start", "center");

        .radio {
          @include flex("block", "row", "center", "center");

          background-color: clr('white');
          border: 1px solid $insights-compare-border;
          border-radius: 0.8rem;
          height: 1.6rem;
          width: 1.6rem;

          .fa {
            color: clr('white');
          }
        }

        .label {
          @include truncate;

          display: block;
          font-weight: $font-weight-bold;
          margin-left: 0.5rem;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 257px;
        }
      }

      .common-adore-score-square {
        background-color: clr('white');
        height: 2rem;
        width: 2rem;
      }
    }
  }
}
</style>
