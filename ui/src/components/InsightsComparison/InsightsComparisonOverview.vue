<template>
  <section class="insights-comparison-overview">
    <section v-if="showInfo" class="right-bg"></section>

    <section v-if="showInfo" class="border">
      <section class="line"></section>
    </section>

    <insights-comparison-summary v-if="showInfo"/>
    <insights-comparison-emotion-chart v-if="showInfo"/>

    <section v-if="showInfo" class="info">
      <insights-comparison-breakdown :id="datasetBenchmark" :compareId="datasetComparison"/>
      <insights-comparison-drivers :id="datasetBenchmark" :compareId="datasetComparison" :side="'left'"/>
      <insights-comparison-drivers :id="datasetComparison" :compareId="datasetBenchmark" :side="'right'"/>
      <insights-comparison-breakdown :id="datasetComparison" :compareId="datasetBenchmark"/>
    </section>

    <section v-if="!showInfo" class="loading">
      <loading-blocks-overlay>Loading Overview...</loading-blocks-overlay>
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import InsightsComparisonBreakdown from '@/components/InsightsComparison/InsightsComparisonBreakdown';
import InsightsComparisonDrivers from '@/components/InsightsComparison/InsightsComparisonDrivers';
import InsightsComparisonEmotionChart from '@/components/InsightsComparison/InsightsComparisonEmotionChart';
import InsightsComparisonSummary from '@/components/InsightsComparison/InsightsComparisonSummary';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';

export default {
  name: 'insights-comparison-overview',

  components: {
    InsightsComparisonBreakdown,
    InsightsComparisonDrivers,
    InsightsComparisonEmotionChart,
    InsightsComparisonSummary,
    LoadingBlocksOverlay,
  },

  computed: {
    ...mapState('datasetsInsights', [
      'datasetBenchmark',
      'datasetComparison',
      'overviews',
      'summaries',
    ]),

    showInfo() {
      return this.overviews.find(o => o.id === this.datasetBenchmark) != null
        && this.overviews.find(o => o.id === this.datasetComparison) != null
        && this.summaries.find(s => s.id === this.datasetBenchmark) != null
        && this.summaries.find(s => s.id === this.datasetComparison) != null;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-comparison-overview {
  border-bottom: 1px solid $insights-compare-border;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto 1fr auto;
  min-height: 550px;

  .border {
    @include flex("block", "row", "center", "center");

    grid-area: 1 / 1 / 4 / 3;

    .line {
      border-right: 1px solid $insights-compare-border;
      height: 100%;
    }
  }

  .right-bg {
    background-color: $insights-compare-comparison-bg;
    grid-area: 1 / 2 / 4 / 3;
  }

  .insights-comparison-summary {
    grid-area: 1 / 1 / 2 / 3;
  }

  .insights-comparison-emotion-chart {
    grid-area: 2 / 1 / 3 / 3;
  }

  .info {
    @include flex("block", "row", "start", "center");

    grid-area: 3 / 1 / 4 / 3;
    padding: 1rem 30px;

    .insights-comparison-breakdown {
      width: 185px;
    }

    .insights-comparison-drivers {
      @include size-evenly;
    }
  }

  .loading {
    @include flex("block", "row", "center", "center");

    grid-area: 1 / 1 / 4 / 3;
  }
}
</style>
