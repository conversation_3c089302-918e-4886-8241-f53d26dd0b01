<template>
  <section class="insights-comparison-selector">
    <section class="border">
      <section class="line"></section>
    </section>

    <section class="dropdown dropdown-benchmark" :class="{ active: benchmarkActive, presentation }" @click.stop="onClickDropdown(true)">
      <section class="left">
        <section class="badge">Benchmark</section>
        <section class="label" :title="labelBenchmark">{{ labelBenchmark }}</section>
      </section>

      <section class="right">
        <section v-if="!presentation" class="dropdown-button">
          <i class="fa fa-caret-down"></i>
        </section>
      </section>
    </section>

    <section class="dropdown dropdown-comparison" :class="{ active: comparisonActive, presentation }" @click.stop="onClickDropdown(false)">
      <section class="left">
        <section class="badge">Comparison</section>
        <section class="label" :title="labelComparison">{{ labelComparison }}</section>
      </section>

      <section class="right">
        <section v-if="!presentation" class="dropdown-button">
          <i class="fa fa-caret-down"></i>
        </section>
      </section>
    </section>

    <insights-comparison-selector-menu v-if="benchmarkActive" class="benchmark" :is-benchmark="true" @close="benchmarkActive = false"/>
    <insights-comparison-selector-menu v-if="comparisonActive" class="comparison" :is-benchmark="false" @close="comparisonActive = false"/>

    <section class="vs">
      <section class="text">vs</section>
    </section>
  </section>
</template>

<script>
import { mapGetters, mapState } from 'vuex';

import InsightsComparisonSelectorMenu from '@/components/InsightsComparison/InsightsComparisonSelectorMenu';

export default {
  name: 'insights-comparison-selector',

  components: {
    InsightsComparisonSelectorMenu,
  },

  props: {
    presentation: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      benchmarkActive: false,
      comparisonActive: false,
    };
  },

  computed: {
    ...mapState('datasetsInsights', ['datasetBenchmark', 'datasetComparison']),

    ...mapGetters('datasets', ['get']),

    labelBenchmark() {
      return this.get(this.datasetBenchmark).label;
    },

    labelComparison() {
      return this.get(this.datasetComparison).label;
    },
  },

  methods: {
    onClickDropdown(isBenchmark) {
      if (isBenchmark) {
        this.benchmarkActive = !this.benchmarkActive;
        this.comparisonActive = false;
      } else {
        this.comparisonActive = !this.comparisonActive;
        this.benchmarkActive = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-comparison-selector {
  border-bottom: 1px solid $insights-compare-border;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 50px minmax(auto, 300px);
  height: 50px;
  width: calc(100% - 1px);

  .border {
    @include flex("block", "row", "center", "center");

    grid-area: 1 / 1 / 2 / 3;
    pointer-events: none;

    .line {
      border-right: 1px solid $insights-compare-border;
      height: 100%;
      margin-left: 1px;
    }
  }

  .dropdown {
    @include flex("block", "row", "between", "center");
    @include truncate;

    border: 1px solid transparent;
    cursor: pointer;
    overflow: hidden;

    &:hover, &.active {
      border-color: $insights-compare-border-dark;

      .dropdown-button {
        background-color: clr('white');
        border: 1px solid $insights-compare-border;
      }
    }

    &.presentation {
      pointer-events: none;
    }

    .left {
      @include flex("block", "row", "start", "center");
      margin-right: 2rem;
      overflow-x: hidden;

      .badge {
        border-radius: 1rem;
        color: clr('white');
        font-size: $font-size-xxs;
        font-weight: $font-weight-bold;
        padding: 0.15rem 0.3rem;
        text-transform: uppercase;
      }

      .label {
        @include truncate;
        display: block;
        font-weight: $font-weight-bold;
        font-size: 0.9rem;
        margin-left: 0.5rem;
        max-width: 250px;
      }
    }

    .dropdown-button {
      @include flex("block", "row", "center", "center");

      border-radius: $border-radius-medium;
      height: 1.4rem;
      width: 1.4rem;
    }
  }

  .dropdown-benchmark {
    border-top: 1px solid $insights-compare-border;
    grid-area: 1 / 1 / 2 / 2;
    padding: 0 25px 0 30px;

    &.presentation {
      border-top-color: transparent;
    }

    .badge {
      background-color: $insights-compare-benchmark;
      border: 1px solid $insights-compare-benchmark-dark;
    }
  }

  .dropdown-comparison {
    background-color: $insights-compare-comparison-bg;
    border: 1px solid $insights-compare-border;
    border-right: 1px solid transparent;
    grid-area: 1 / 2 / 2 / 3;
    padding: 0 30px 0 25px;

    &.presentation {
      border-top-color: transparent;
    }

    .badge {
      background-color: $insights-compare-comparison;
      border: 1px solid $insights-compare-comparison-dark;
    }
  }

  .insights-comparison-selector-menu {
    margin-top: -1px;
    z-index: 1000;

    &.benchmark {
      grid-column: 1 / 2;
    }

    &.comparison {
      grid-column: 2 / 3;
    }
  }

  .vs {
    @include flex("block", "row", "center", "center");

    grid-area: 1 / 1 / 2 / 3;
    pointer-events: none;
    margin-left: 1px;

    .text {
      @include flex("block", "row", "center", "center");

      background-color: clr('white');
      border: 1px solid $insights-compare-border-dark;
      border-radius: 12.5px;
      font-size: $font-size-sm;
      font-weight: $font-weight-bold;
      height: 25px;
      width: 25px;
    }
  }
}
</style>
