<template>
  <section class="insights-comparison-breakdown">
    <section class="header">Emotion Breakdown</section>

    <section class="list">
      <section v-for="([key, value], index) in contributions" :key="key" class="row">
        <section class="left">
          <section class="mark" :class="[key]"></section>
          <section class="percentage">{{ Math.round(value * 100) }}%</section>
          <section class="key">{{ key }}</section>
        </section>

        <section class="diff" :class="{
          positive: key === 'adored' ?  contributionsDiff[index] > 0 : contributionsDiff[index] < 0,
          negative: key === 'adored' ?  contributionsDiff[index] < 0 : contributionsDiff[index] > 0,
        }">
          {{ `${ contributionsDiff[index] >= 0 ? '+' : '' }${ contributionsDiff[index] }` }}%
        </section>
      </section>
    </section>
  </section>
</template>

<script>
import { mapState } from 'vuex';

export default {
  name: 'insights-comparison-breakdown',

  props: {
    compareId: {
      type: Number,
      required: true,
    },

    id: {
      type: Number,
      required: true,
    },
  },

  computed: {
    ...mapState('datasetsInsights', ['overviews']),

    contributions() {
      const { contribution } = this.overview;

      // Object.entries output format
      return [
        ['adored', contribution.positive],
        ['ignored', contribution.neutral],
        ['floored', contribution.negative],
      ];
    },

    contributionsDiff() {
      const { contribution } = this.overview;
      const contributionCompare = this.overviewCompare.contribution;

      const r = c => Math.round(c * 100);

      return [
        r(contribution.positive) - r(contributionCompare.positive),
        r(contribution.neutral) - r(contributionCompare.neutral),
        r(contribution.negative) - r(contributionCompare.negative),
      ];
    },

    overview() {
      return this.overviews.find(o => o.id === this.id);
    },

    overviewCompare() {
      return this.overviews.find(o => o.id === this.compareId);
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.insights-comparison-breakdown {
  @include flex("block", "column", "start", "stretch");

  border: $border-standard;
  border-radius: $border-radius-medium;

  .header {
    @include flex("block", "row", "center", "center");

    border-bottom: $border-standard;
    font-size: 0.7rem;
    font-weight: $font-weight-bold;
    height: 25px;
    padding: 0 1rem;
    text-transform: uppercase;
  }

  .list {
    @include flex("block", "column", "start", "stretch");

    padding: 0.5rem 0;

    .row {
      @include flex("block", "row", "between", "center");

      font-size: $font-size-xs;
      padding: 0.2rem 1rem;

      .left {
        @include flex("block", "row", "start", "center");

        .mark {
          border: 2px solid $border-color-dark;
          border-radius: 0.25rem;
          display: inline-block;
          height: 0.5rem;
          margin-right: 0.3rem;
          width: 0.5rem;

          &.adored {
            border-color: $adored;
          }

          &.ignored {
            border-color: $ignored;
          }

          &.floored {
            border-color: $floored;
          }
        }

        .percentage {
          font-weight: $font-weight-bold;
          margin-right: 0.3rem;
        }

        .key {
          text-transform: capitalize;
        }
      }

      .diff {
        @include flex("block", "row", "center", "center");

        background-color: clr('white');
        border: $border-standard;
        border-radius: $border-radius-small;
        font-weight: $font-weight-bold;
        padding: 0.2rem 0;
        width: 2.5rem;

        &.positive {
          color: $adored;
        }

        &.negative {
          color: $floored;
        }
      }
    }
  }
}
</style>
