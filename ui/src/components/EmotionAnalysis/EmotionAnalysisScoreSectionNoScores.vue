<template>
  <section class="emotion-analysis-score-section-no-scores">
    <loader-icon class="-spin"></loader-icon>
  </section>
</template>

<script>
import { LoaderIcon } from 'vue-feather-icons';

export default {
  name: 'emotion-analysis-score-section-no-scores',

  components: {
    LoaderIcon,
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.emotion-analysis-score-section-no-scores {
  @include flex("block", "row", "center", "center");
}
</style>
