<template>
  <section class="emotion-analysis-over-limit">
    <info-icon class="icon"/>
    <h3>Results comparison is limited to {{ maxComparableData }} datasets.</h3>
  </section>
</template>

<script>
import { InfoIcon } from 'vue-feather-icons';
import { mapGetters } from 'vuex';

export default {
  name: 'emotion-analysis-over-limit',

  components: {
    InfoIcon,
  },

  computed: {
    ...mapGetters('user', ['maxComparableData']),
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.emotion-analysis-over-limit {
  @include flex("block", "row", "start", "center");
  @include rigid;

  background-color: clr("blue");
  color: clr("white");
  padding: 0.4em 1.4em;

  .icon {
    font-weight: bold;
    height: 1.3em;
    margin-right: 0.3em;
  }

  h3 {
    font-size: $font-size-sm;
    margin: 0;
  }
}
</style>
