<template>
  <section class="emotion-analysis-themes-navigator">
    <themes-navigation v-if="!hide">
      Showing themes and comments for:
      <themes-navigation-index v-if="isIndexes" />
      <themes-navigation-range v-if="isIntensity" />
      <themes-navigation-time v-if="isTimeline" />in:
      <themes-navigation-dataset />
    </themes-navigation>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import ResultsTabs from '@/enum/results-tabs';
import ThemesNavigation from '@/components/ThemesNavigation/ThemesNavigation';
import ThemesNavigationDataset from '@/components/ThemesNavigation/ThemesNavigationDataset';
import ThemesNavigationIndex from '@/components/ThemesNavigation/ThemesNavigationIndex';
import ThemesNavigationRange from '@/components/ThemesNavigation/ThemesNavigationRange';
import ThemesNavigationTime from '@/components/ThemesNavigation/ThemesNavigationTime';

export default {
  name: 'emotion-analysis-themes-navigator',

  components: {
    ThemesNavigation,
    ThemesNavigationDataset,
    ThemesNavigationIndex,
    ThemesNavigationRange,
    ThemesNavigationTime,
  },

  props: {
    tab: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapState('datasets', ['active']),

    ...mapState('themes', ['index', 'range']),

    ...mapState('timeSeries', ['time']),

    hide() {
      return this.active == null
        || (this.isIndexes && this.index == null)
        || (this.isIntensity
          && (this.range == null || this.range.lower == null || this.range.upper == null)
        )
        || (this.isTimeline
          && (this.time.from == null || this.time.to == null)
        );
    },

    isIndexes() {
      return this.tab === ResultsTabs.INDEXES || this.tab === ResultsTabs.DISTRIBUTION;
    },

    isIntensity() {
      return this.tab === ResultsTabs.INTENSITY;
    },

    isTimeline() {
      return this.tab === ResultsTabs.TREND_ANALYSIS;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.emotion-analysis-themes-navigator {
  @include flex('block', 'row', 'center', 'center');

  color: $body-copy-light;
  font-size: $font-size-sm;
  letter-spacing: $letter-spacing-base;
  margin-left: $panel-margin;
  padding-top: 1rem;
  text-transform: uppercase;
}
</style>
