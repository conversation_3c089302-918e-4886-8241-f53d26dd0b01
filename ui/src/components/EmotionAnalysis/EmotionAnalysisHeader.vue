<template>
  <section class="emotion-analysis-header">
    <h3>
      <slot></slot>
    </h3>
  </section>
</template>

<script>
export default {
  name: 'emotion-analysis-header',
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.emotion-analysis-header {
  margin: 0 1.5rem;

  h3 {
    @include flex('block', 'row', 'start', 'center');
    @include grow;

    margin: 0 0.5rem 0 0;
    color: $body-copy-light;
    text-transform: uppercase;
    font-size: $font-size-sm;
    letter-spacing: $letter-spacing-base;
  }
}
</style>
