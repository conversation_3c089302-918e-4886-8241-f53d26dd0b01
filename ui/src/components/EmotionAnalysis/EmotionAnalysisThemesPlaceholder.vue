<template>
  <section class="emotion-analysis-themes-placeholder">
    <section class="text">{{ text }}</section>
  </section>
</template>

<script>
export default {
  name: 'emotion-analysis-themes-placeholder',

  props: {
    chart: {
      type: String,
      required: true,
    },
  },

  computed: {
    text() {
      switch (this.chart) {
        case 'Indexes':
          return 'Select an index within a dataset to view themes.';
        case 'Distribution':
          return 'Use the Emotion Indexes or Emotion Intensity Distribution charts to view themes.';
        case 'Intensity':
          return 'Select a score range within a dataset to view themes.';
        default:
          return 'Select a time range to view comments.';
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.emotion-analysis-themes-placeholder {
  @include flex("block", "row", "center", "center");
  @include stretch;

  .text {
    color: $body-copy-light;
    font-size: $font-size-lg;
  }
}
</style>
