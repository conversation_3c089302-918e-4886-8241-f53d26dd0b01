<template>
  <section class="emotion-analysis-score-section">
    <emotion-analysis-score-section-no-scores v-if="overviews.length === 0" />

    <score-card-single v-if="overviews.length === 1" :overview="overviews[0]"/>

    <section class="duo-wrapper" v-if="overviews.length === 2">
      <score-card-duo v-for="overview in overviews" :key="overview.id" :overview="overview"></score-card-duo>
    </section>

    <score-card-list v-if="overviews.length > 2" :overviews="overviews"></score-card-list>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import ScoreCardDuo from '@/components/ScoreCard/ScoreCardDuo';
import ScoreCardList from '@/components/ScoreCard/ScoreCardList';
import ScoreCardSingle from '@/components/ScoreCard/ScoreCardSingle';
import EmotionAnalysisScoreSectionNoScores from '@/components/EmotionAnalysis/EmotionAnalysisScoreSectionNoScores';

export default {
  name: 'emotion-analysis-score-section',

  components: {
    ScoreCardDuo,
    ScoreCardList,
    ScoreCardSingle,
    EmotionAnalysisScoreSectionNoScores,
  },

  computed: {
    ...mapState('datasets', ['overviews']),
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.emotion-analysis-score-section {
  @include rigid;

  margin-right: 1rem;
  max-height: 450px;
  min-height: $score-section-height;
  width: $score-card-width;

  .emotion-analysis-score-section-no-scores,
  .score-card-single,
  .duo-wrapper {
    height: 100%;
    width: 100%;
  }

  .duo-wrapper {
    @include flex("block", "column", "start", "stretch");

    .score-card-duo:last-child {
      margin-top: 1rem;
    }
  }
}
</style>
