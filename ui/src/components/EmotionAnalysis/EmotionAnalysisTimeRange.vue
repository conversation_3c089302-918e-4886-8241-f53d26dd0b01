<template>
  <section class="emotion-analysis-time-range">
    <section v-if="hasTime" class="range">
      <span class="text">Showing Themes from</span>
      <time-picker
        class="picker"
        v-bind="pickerPropsFrom"
        v-model="pickerFromTime"
        @input.native="onFromInput"
      >
        <base-button colour="dropdown" icon="calendar" size="small">
          {{ fromTimeFormat }}
          <chevron-down-icon class="icon" />
        </base-button>
      </time-picker>
      <span class="text">to</span>
      <time-picker
        class="picker"
        v-bind="pickerPropsTo"
        v-model="pickerToTime"
        @input.native="onToInput"
      >
        <base-button colour="dropdown" icon="calendar" size="small">
          {{ toTimeFormat }}
          <chevron-down-icon class="icon" />
        </base-button>
      </time-picker>
    </section>
    <section v-else class="no-range"></section>
  </section>
</template>

<script>
import TimePicker from 'vue-ctk-date-time-picker';

import { addMinutes, format, isValid, parseISO, subMinutes } from 'date-fns';
import { ChevronDownIcon } from 'vue-feather-icons';
import { mapActions, mapGetters, mapState } from 'vuex';

import BaseButton from '@/components/Base/BaseButton';

export default {
  name: 'emotion-analysis-time-range',

  components: {
    BaseButton,
    ChevronDownIcon,
    TimePicker,
  },

  data() {
    return {
      formatter: 'D/M/YY - HH:mm',
      pickerFormatter: 'YYYY-MM-DD HH:mm',
    };
  },

  computed: {
    ...mapState('timeSeries', ['time']),

    ...mapGetters('timeSeries', ['firstTime', 'lastTime']),

    fromTime() {
      if (this.time.from == null) return null;

      return parseISO(this.time.from);
    },

    fromTimeFormat() {
      return format(this.fromTime, this.formatter);
    },

    hasTime() {
      return this.fromTime != null
        && isValid(this.fromTime)
        && this.toTime != null
        && isValid(this.toTime);
    },

    pickerFromTime: {
      get() {
        return format(this.fromTime, this.pickerFormatter);
      },
      set(time) {
        this.updateTime({ from: parseISO(time), to: this.toTime });
      },
    },

    pickerToTime: {
      get() {
        return format(this.toTime, this.pickerFormatter);
      },
      set(time) {
        this.updateTime({ from: this.fromTime, to: parseISO(time) });
      },
    },

    pickerProps() {
      if (!this.hasTime) return { disabled: true };

      return {
        buttonColor: '#a15aef',
        color: '#2d1757',
        format: this.pickerFormatter,
        minDate: format(this.firstTime, this.pickerFormatter),
        maxDate: format(this.lastTime, this.pickerFormatter),
        noButtonNow: true,
        noHeader: true,
        noValueToCustomElem: true,
      };
    },

    pickerPropsFrom() {
      return {
        ...this.pickerProps,
        maxDate: format(subMinutes(this.toTime, 1), this.pickerFormatter),
      };
    },

    pickerPropsTo() {
      return {
        ...this.pickerProps,
        minDate: format(addMinutes(this.fromTime, 1), this.pickerFormatter),
      };
    },

    toTime() {
      if (this.time.to == null) return null;

      return parseISO(this.time.to);
    },

    toTimeFormat() {
      return format(this.toTime, this.formatter);
    },
  },

  methods: {
    ...mapActions('timeSeries', ['updateTime']),

    onFromInput() {
    },

    onToInput() {
    },
  },
};
</script>

<style lang="scss">
@import "src/styles/variables";
@import "src/styles/mixins";

.emotion-analysis-time-range {
  @include flex("block", "row", "start", "center");

  padding: 0 1.5rem;

  .range {
    @include flex("block", "row", "center", "center");

    .text,
    .base-button {
      margin-right: 0.5rem;
    }

    .text {
      color: $body-copy-light;
      font-size: $font-size-sm;
      text-transform: uppercase;
    }

    .picker {
      font-family: $font-stack;
      width: inherit;

      .datepicker {
        border: $border-standard;
        border-radius: $border-radius-large;
      }
    }

    .base-button {
      font-size: $font-size-xs;
      font-weight: $font-weight-bold;
      padding: 0.5rem;

      .icon {
        margin-left: 0.3rem;
        width: $font-size-base;
      }
    }
  }
}
</style>
