[{"id": 59, "version": "14.2.0", "date": "20th Mar 2024", "changes": {"fixed": ["Fixed an issue where split filters could not be loaded by different users in the same workspace.", "The \"View Related Themes\" button on a comment will now correctly display the list of themes when inside a filtered view."]}}, {"id": 58, "version": "14.1.0", "date": "12th Mar 2024", "changes": {"improved": ["We've updated the behaviour of themes and sub-themes to be less confusing - all comments in sub-themes are now represented in the top level theme. Deleting from, or moving comments out of a sub-theme will now be reflected in the top-level theme, and vice-versa."], "fixed": ["Updated the strategy for fetching dataset info; previously, datasets outside the range of the list on the main page would not be fully loaded, leading to issues displaying volume information on themes, or the Insights scorecard.", "Resolved an issue where when deleting single Memory items, other selected Memory items would also be deleted by mistake.", "Fixed an issue where non-standard ISO language codes prevented text from being translated.", "Invited users should now be redirected to the app after signing up through a workspace invite link.", "Download links sent to emails will now redirect the user if not logged in, where previously it remained on a blank screen."]}}, {"id": 57, "version": "14.0.0", "date": "21st Feb 2024", "changes": {"new": [{"headline": "Filters are more dynamic than ever 🔬", "changes": ["You can now save filters, so you can pick up where you left off when diving into your datasets.", "Filtered views now have their own Insights scorecards, so you can create individual reports on different segments of your data.", "You can also select up to 20 metadata values at a time to split them into separate filter views, helping save time when exploring different demographics or segments within your data."]}]}}, {"id": 56, "version": "13.0.0", "date": "20th Dec 2023", "changes": {"new": ["We've added a new 'Organisation' page which will allow account owners to view various workspaces they own, centralise user management, and add stopwords to workspaces at a global level."], "improved": ["Unnecessary whitespace is now trimmed automatically when selecting illustrative comments for the Insights scorecard.", "We've updated references to our customer support email - <EMAIL> is now the central place for guidance, issue reporting, and other queries."], "fixed": ["Fixed some more rare cases where data would not be refreshed after performing theme/comment actions.", "Fixed an issue with logic that would allow memory collections with no actions to be applied to datasets, which would cause an error.", "Fixed various issues with the memory page where memory actions and collections could not be edited as expected.", "Fixed some cases where the 'Adored', 'Ignored', and 'Floored' values could sometimes total more than 100 due to a rounding error."]}}, {"id": 55, "version": "12.1.0", "date": "9th Nov 2023", "changes": {"fixed": ["Fixed an issue where sometimes comments with quotation marks would prevent filters from being applied to datasets.", "Fixed a visual issue where the bottom of the memory page would be obscured.", "Fixed an issue where in certain cases the 'Reset Password' email would not be sent."]}}, {"id": 54, "version": "12.0.0", "date": "3rd Nov 2023", "changes": {"new": [{"headline": "We've added multiple options for Single Sign-On 🔒", "changes": ["You can now use a Google Workspace, Microsoft Teams, or Amazon account to sign in, in lieu of a username and password.", "Other OAuth-based options will be added in the future, as well as other authentication protocols such as SAML for federated user management."]}], "fixed": ["Fixed an issue where in certain specific instances, themes would not display on the SWOT chart.", "Fixed an issue where using 'Select All' on unassigned comments would reassign comments that weren't selected.", "Fixed an uncommon case where datasets would not finish analysing due to an issue with regression modelling."]}}, {"id": 53, "version": "11.3.0", "date": "26th Oct 2023", "changes": {"fixed": ["Fixed an issue where data would not refresh after performing theme/comment actions."]}}, {"id": 52, "version": "11.2.0", "date": "18th Oct 2023", "changes": {"improved": ["Some visual tweaks to the Theme Intensity chart for text readability.", "Added ability to hit the Enter/Return key to submit the dataset name when renaming."], "fixed": ["Fixed an issue where comments could sometimes be duplicated when reassigning to different themes.", "Fixed an issue where sometimes subtheme memory actions couldn't be added to an existing theme memory action.", "Prevented some cases where invalid memory could be saved, which would cause an error when applied to datasets.", "Updated the logic for deleting comments from themes so that theme totals are always reaggregated correctly.", "Fixed an issue preventing filters being applied to very large datasets.", "Fixed an issue where sometimes datasets would not be displayed on the datasets page after filtering."]}}, {"id": 50, "version": "11.0.0", "date": "11th Sep 2023", "changes": {"new": ["Comment filters will now update theme scores in real-time. Use these to see how certain themes perform in different segments of your dataset."], "improved": ["Improved the menu for selecting and editing highlights on the Insights scorecard.", "Added the ability to batch delete Memory actions.", "Various styling and usability improvements for the Theme Intensity chart.", "Various general style and sizing tweaks for layouts and text.", "Added a new datetime format: MM/dd/yyyy.", "Added an option to \"Create a New Dataset\" using only new custom themes via Theme Builder.", "We've updated \"Heartbeat\" to allow users to add multiple questions (and metric questions) to a form.", "We've added optional functionality to \"Heartbeat\" to allow users to collect participant information (email, phone and free text)."], "fixed": ["Fixed an interaction between comment filters, and \"Select All\" comments, which would cause comment operations to be applied incorrectly.", "Fixed some cases where the stats/breakdown sections of the scorecard would be cut off in the exported image.", "Fixed a case where, when switching between single and comparison view on the Insights scorecard, data would not be loaded correctly.", "Fixed an issue with the logic for selecting the most relevant correlations on the Insights scorecard.", "Fixed a rare case where some datasets would error and fail to complete analysis while performing regression modelling for correlations.", "Fixed an issue where Memory collections could not be applied to datasets due to user account configurations.", "Fixed an issue where the \"Copy\" button would not work when \"Select All\" was used with certain datasets.", "Fixed an issue where sometimes subtopics would not exist but still be counted due to some Memory applications.", "Fixed an issue that allowed users to merge themes with empty/blank new theme labels.", "Fixed an issue where users couldn't search for illustrative comments within Insight Scorecard Top Themes.", "Fixed an issue where users couldn't export comments from a merged dataset."]}}, {"id": 49, "version": "10.3.0", "date": "6th Jul 2023", "changes": {"improved": ["We've upgraded our database version. Performance should feel much better across the board.", "We've optimised how search queries are handled in our database. Searching for comments should now be faster in most cases.", "You can now reassign comments to multiple themes at once.", "You can now select which emotion to show in the Top Indicators on comparison scorecards, or hide them completely."], "fixed": ["Fixed an issue where stopwords could not be applied to datasets, both pre- and post-upload.", "Fixed an issue where timestamp columns could not be processed in CSV datasets.", "When filters have been selected and creating a theme, the filters will now be applied correctly.", "When opening a scorecard where a custom theme list has been defined, it should now open with the custom list displaying as default.", "Fixed a styling issue with the key themes on Insights scorecards where the titles were being cut shorter than they needed to be.", "Clicking certain elements within the filter menu on the Comments page will no longer close the filter menu."]}}, {"id": 48, "version": "10.2.0", "date": "8th Jun 2023", "changes": {"new": [{"headline": "A new and improved Theme Analysis chart 🧪", "changes": ["We're making over the Theme Analysis chart to add new features and functionality to better explore your themes.", "Theme Intensity values are now represented in the chart; higher intensity themes will be represented by a darker shade.", "We've also improved how the chart scales to fit different window sizes, without impacting the final download quality.", "We're still working on improving this feature with additional functionality such as text and statistic annotations, deeper integration with theme list controls, and more edit controls for elements on the chart, so watch this space for more updates!"]}], "improved": ["The Memory builder can now expand to be a little wider to better support creating search actions.", "When applying filters on the comments page, we will now open the comments panel if it is not already open, as a matter of convenience and to better indicate the effect of applying filters.", "We've optimised the process for deleting themes, which should now process much more quickly when deleting multiple themes."], "fixed": ["Fixed a rare case where a malformatted search query would prevent Memory from being applied to a dataset.", "Fixed an issue where selecting a column type when uploading would sometimes revert after a short period.", "Subtheme search actions will now be added to the collection when the parent search action is added.", "Fixed an issue where certain elements on the Journey Mapper would not be displayed.", "Fixed an issue where \"potential change\" values would not display correctly on the Top Themes list on the Scorecard."]}}, {"id": 47, "version": "10.1.0", "date": "26th Apr 2023", "changes": {"improved": ["Added \"Theme Intensity\" values to the \"Copy\" button on the themes list.", "Optimised the pagination for Memory items on the Memory tab, and added the option to choose page size."], "fixed": ["Fixed some issues related to displaying custom themes on the Insights scorecard and presentation.", "Fixed edge cases where dragging and dropping themes would not behave correctly.", "Fixed a display issue with height limits and scrolling for certain dropdowns."]}}, {"id": 46, "version": "10.0.0", "date": "18th Apr 2023", "changes": {"new": [{"headline": "A new look and some helpful new features for Memory! ✨", "changes": ["We've added a new \"Memory\" tab to the main page. Here, you can find all of your saved Memory actions and collections, with a more fluid interface for viewing, sorting and filtering Memory actions, and managing Memory collections.", "You can now create Memory actions using the new \"Memory Builder\" panel on the Memory tab, in the style of the Theme Builder, to quickly build up a list of actions to add to a collection.", "We've also updated our process for applying Memory to datasets - you can now apply multiple Memory collections, and even apply Memory after a dataset has been analysed!"]}], "improved": ["Themes created using the Theme Builder will now replace existing themes with the same label. A warning will be displayed to inform users this will happen.", "You can now select subthemes to display in the \"Custom Selection\" list in the Insights scorecard, not just themes.", "The Comments panel will now display the number of comments when \"All Comments\" is selected."], "fixed": ["Updated the way Insights scorecards are generated to prevent issues when there are fewer than 5 themes within a dataset.", "Updated the way excel files are processed to prevent system errors when files with pseudo-blank rows are processed.", "Updated the way CSV files are processed to prevent errors with blank field entries.", "Fixed a case where editing a theme after closing the Insights scorecard would prevent the theme score from updating correctly.", "When renaming the title of the SWOT chart for a dataset, the title will now be correctly reflected in the exported image.", "When a custom theme list is selected for the Insights scorecard, the \"Custom Selection\" tab will be active by default when opening the scorecard.", "Fixed the widths of the items within the modal for selecting Top Indicators when comparing datasets to prevent layout issues.", "Fixed some layout issues on the Theme Builder.", "Clicking the header logo will now take you back to the datasets page."]}}, {"id": 45, "version": "9.5.0", "date": "8th Mar 2023", "changes": {"new": ["We've added a \"Copy\" button to the themes list, which will copy the theme info to your clipboard in CSV format, which can be pasted into your spreadsheet program of choice. In the future, we hope to extend this functionality to allow you to directly download the data in either CSV or Excel format."], "improved": ["When navigating to the theme builder from search, we will now remember which comments were previously selected or unselected.", "You can now bookmark comments directly from the search results.", "We've added a link back to \"All Comments\" when browsing any individual theme's comments to aid in navigation.", "Comments now have a dropdown button which shows you how many and which themes and subtopics a comment is in, and allow you to navigate directly to those themes."], "fixed": ["Fixed an issue where some search options were not carried over when selecting the option to \"Create Theme from Search\".", "We've improved how we handle reassigning comments to existing and new themes, which will now hopefully behave more predictably."]}}, {"id": 44, "version": "9.4.0", "date": "16th Feb 2023", "changes": {"new": [{"headline": "Try our new Theme Builder to easily create your own themes from search queries! 🏗️", "changes": ["Simply click the \"Create\" button above the themes list on the Comments page and select \"Create New Themes\" to access the Theme Builder.", "You can add as many Themes as you like, with as many Subthemes for each entry as you like, and apply the results to your dataset.", "Additionally, you can easily save your newly created list to an existing Memory collection, or create a new collection to save to."]}], "fixed": ["Updated our method of retrieving Time Series charts to avoid a case where sometimes the request was malformed, and would result in the chart data not being fetched.", "Fixed an issue where sometimes, when attempting to download a \"raw\" dataset file for a dataset that had been created as a subset of another, data values were not being put into the correct columns."]}}, {"id": 43, "version": "9.3.0", "date": "27th Jan 2023", "changes": {"improved": ["We've updated the bookmark icon for comments to make it more obvious when a comment is bookmarked."], "fixed": ["Fixed an issue where clicking on \"<X> Datasets\" on the tags page wouldn't take the user back to the datasets page to view the associated datasets.", "Updated the styling on the insights panel so that long, unbroken theme names won't overlap the score information.", "Fixed an issue where the comments wouldn't refresh or show a notification when assigning unassigned comments to a new or existing theme.", "Improved how we handle highlighted themes on the insights panel; there should now be fewer cases where the insights will fail to open."]}}, {"id": 42, "version": "9.2.1", "date": "17th Jan 2023", "changes": {"improved": ["We've optimised how Themes are rendered on the Comments page; navigating themes and comments should now feel more responsive."], "fixed": ["We've tweaked the width break-points for the Themes and Comments panels; the comments panel should no longer get cut off on smaller screen or window sizes.", "Deselecting a selected dataset while an Insights comparison scorecard is open should no longer remove the dataset info from the scorecard."]}}, {"id": 41, "version": "9.2.0", "date": "12th Jan 2023", "changes": {"improved": ["Some styling tweaks to the Datasets page, including a pass on the \"Tags\" section to make it more consistent with the styling of the datasets list.", "Added an \"X\" button to the Themes search bar which clears the current search."], "fixed": ["Fixed an issue where the sidebar menu couldn't be opened or closed.", "Fixed an issue with memory where subtopic searches would prevent the analysis from completing if the requisite parent theme was not created.", "Fixed an issue with memory where \"Merge\" or \"Rename\" actions could interfere with search actions and prevent the analysis from completing.", "Clarified some of the wording on the SWOT themes table.", "The \"All Comments\" button is now displayed when there are no themes on the Comments page.", "Fixed an issue with filtering metadata on the Comments page because the metadata type was not correctly set."]}}, {"id": 40, "version": "9.1.0", "date": "23rd Dec 2022", "changes": {"new": [{"headline": "A new, sleeker design for our Datasets page 💃", "changes": ["We've redesigned the \"Welcome\" section on the page to be collapsable, giving you a little more room to view the datasets.", "Our new table-like design makes better use of the full page width, and enables sortable column headers for more efficient dataset browsing."]}], "improved": ["We've improved our SWOT evaluation algorithms - determining the SWOT designation of a theme is now faster, simpler, and more predictable.", "Some small visual tweaks to the themes list and scorecard."], "fixed": ["It is now not possible for themes to have a SWOT designation of \"NONE\" - themes will always be designated as a Strength, Weakness, Opportunity or Threat depending on their SWOT evaluation. Any themes that were previously designated as \"NONE\" have been retroactively updated with their correct value.", "Pressing \"Shift+Enter\" on the comments page search bar will no longer input a line break, but will instead trigger a search as normal."]}}, {"id": 39, "version": "7.0.0", "date": "10th Nov 2022", "changes": {"new": [{"headline": "We've added an \"Export\" button to the Comments panel! 📒", "changes": ["You can now export your comments and their metadata to an Excel (.xls) file", "Simply open \"All Comments\", or any theme on the Comments page, select the comments you'd like to export (or use the \"Select All\" functionality), and click \"Export\""]}], "improved": ["Updated the date format on these changelog entries to disambiguate the date for different countries"], "fixed": ["Fixed an issue where users couldn't change their password due to an issue with password security checks", "Fixed an issue where users couldn't reset their password as they would not recieve their email", "Fixed an issue where searching on the Comments page would turn on the metadata information for comments, slowing the page down", "Fixed some minor display issues on the Comments and other pages", "Fixed an issue where sometimes the Adorescore would not re-calculate when deleting comments"]}}, {"id": 38, "version": "6.9.0", "date": "12th Oct 2022", "changes": {"fixed": ["Fixed an issue where when selecting datasets for the Comparison Insights view from different pages, the info would not display correctly on the scorecard", "Fixed an issue where when navigating away and back to the \"overview\" slide on the Insights presentation menu, the option to download sections separately would toggle without user input", "When users remove a dataset from the dataset selector on the Comments page and other pages, the \"window\" of datasets in the selector will now be correct, in the case that there were more datasets selected than could be displayed at one time", "Fixed an issue where users may have proceeded with dataset analysis after editing the upload name more quickly than we could register the change", "Updated the comments panel to handle smaller screen sizes more gracefully", "Fixed a case when datasets are uploaded with a single comments column where users were given the option to sort by \"Comments\" metadata on the comments panel, when this is only intended to be used for datasets with multiple comments columns", "Adding a loading state when merging themes to prevent issues where the action could be triggered multiple times", "Fixed some minor page styling issues on the Comments page, and others"]}}, {"id": 37, "version": "6.8.0", "date": "5th Oct 2022", "changes": {"improved": ["Updated the design of the dataset selector on the Comments and various charts pages. The new selector takes up much less page space than the previous version, and now also shows the difference from the average Adorescore when multiple datasets are selected", "When using Memory, users can now choose to only create themes based on Search Actions", "Users can now choose whether to show the Correlations section of the Insights scorecard. The design of the options menu for the \"Overview\" section on the scorecard has also been updated to more visually guide the user through the option choices", "\"Top Indicators\" can now be renamed while editing Comparison scorecards", "Updated the dataset selector on the Uploads page to handle long dataset names more gracefully, and added a delete button to remove datasets without needing to select them", "Further design tweaks for the Search section of the Comments page to add visual clarity, and tooltips to guide the user. The dropdown menu for search controls will now also hide itself when the user searches, to avoid covering the results panel", "When using filters on the Comments or charts pages, the \"Clear All\" button will now immediately fetch the unfiltered list of comments", "Clicking on a theme pin on the SWOT chart will now scroll to that theme in the list", "Improved the handling of the \"Potential Change\" statistic on the Insights scorecard, which should lead to a small performance improvement when loading scorecards that make use of it"], "fixed": ["Fixed an issue where search actions (such as \"Create Theme\", \"Create Dataset\", etc) could not be performed if the search result count could not be retrieved", "Fixed an issue where the \"Save to Memory\" option when editing themes could be toggled on, but not off", "Fixed a case when renaming themes where the capitalisation of a theme could not be changed", "Updated the styling of the \"3 Insights\" on the scorecard to fix an issue where elements would be misaligned if there was less text than expected", "Fixed some minor styling issues with the Theme items on the comments page", "Updated the way that Search Actions are loaded on the Memory panel to increase performance when the user has a large number of actions"]}}, {"id": 36, "version": "6.7.0", "date": "22nd Sep 2022", "changes": {"improved": ["Modified the way that Insights scorecards are initially generated on our server to improve performance", "When a user has selected custom themes for the Insights scorecard, the \"Custom Selection\" tab will now display by default when the Insights are opened", "Updated the design and behaviour of the search bar on the Comments page; users must now click \"Search\" or press Enter/Return to initiate a search, as the search-as-you-type functionality would often trigger multiple useless searches which could hamper the experience of using search. We hope to streamline the search flow further in the future", "Updated the design and layout for theme items; a new colored Adorescore marker which should help visually guide users to stand-out themes, and a different location for the edit buttons, which should no longer cover up the volume and score when hovering on the theme", "Updated the design for the login page"], "fixed": ["Fixed an issue where the \"Reassign selection\" options when viewing \"All Comments\" or searching on the Comments page were unresponsive"]}}, {"id": 35, "version": "6.6.1", "date": "16th Sep 2022", "changes": {"fixed": ["Fixed an issue on the Comments page where, when selecting a theme with the comments panel closed, the comments panel would not display the correct list of comments when it opened", "Fixed an issue where individual comments could not be deleted", "Updated the logic on the Top Themes section of the Insights scorecard. Users can now filter the amount of themes displayed when on the \"Custom Selection\" tab", "Updated the way that comments expand and collapse when clicked to better allow users to select small snippets from a comment", "Fixed an issue where, when creating search actions in Memory, search queries would take a long time to validate and prevent users from creating the action"]}}, {"id": 34, "version": "6.6.0", "date": "13th Sep 2022", "changes": {"improved": ["Improved the way that Adoreboard-generated themes and Memory-related themes are handled; any themes generated by Memory actions now take precedence against Adoreboard-generated themes when the theme labels are the same", "Added the ability to select all comments on the comments page, even if not all comments have been loaded", "Sections of the Insights Scorecard overview can now be downloaded individually in the presentation view"], "fixed": ["Fixed an issue where \"Emotional Analysis\" progress would not be reported while uploading a dataset", "Fixed some layout issues related to expanding or collapsing subtopics on the Comments page", "Fixed an issue where updating the key statistic on the 3 insights would prevent changing the key statistic back to \"Potential Change\"", "Fixed an issue with Memory merge actions that sometimes prevented datasets from being uploaded", "Modified Memory rename action functionality to behave more predictably"]}}, {"id": 33, "version": "6.5.0", "date": "5th Jul 2022", "changes": {"new": [{"headline": "A new and improved Comments page gives you a far more powerful look into your datasets! 🔭", "changes": ["The Comments page now shifts focus to the themes, allowing you to triage much more quickly", "View two or three columns of themes at once instead of just one, even while reading comments - you decide what you want to see more of", "You can now also interact with subtopics in the same way as themes, allowing you to reassign them to different themes, merge them together, and much more!"]}], "improved": ["Improved database performance, especially when working with very large datasets", "Made the effects of reassigning comments more obvious"], "fixed": ["Fixed an issue with the SWOT score placement", "Fixed an issue where 'Comments' would appear as a filter when only one column had been uploaded", "You can now download the raw data from a dataset which was created from themes"]}}, {"id": 32, "version": "6.4.0", "date": "12th Jul 2022", "changes": {"new": [{"headline": "You can now switch the Adorescore on the Insights Scorecard to show your own metric instead 📈", "changes": ["If you upload NPS or another metric alongside your uploaded datasets, you can now display the overall metric score on the Insights Scorecard", "Add your metric range and select the calculation type (average or NPS) to display the correct score", "You can also now rename the Adored, Ignored and Floored percentages"]}], "improved": ["Database performance improvements - restructuring to increase performance across the board", "Removed old references to Folders"], "fixed": ["Fixed an issue with displaying the correct usage percentage", "Fixed an issue with loading comments when using filters", "Fixed an issue with a delay when selecting a column type on upload"]}}, {"id": 31, "version": "6.3.0", "date": "31st Mar 2022", "changes": {"new": ["You can now upload multiple text columns from a file - the comments will be combined and can be filtered to segment by original column", "There is now an option to instantly convert a subtopic into a theme"], "improved": ["Added option for saving to memory when editing themes directly in the theme table", "Updated the custom markers on the SWOT chart to dynamically resize based on the content", "The SWOT chart now has an editable title which is shown when in Presentation mode", "When searching for comments, the query is now both validated and executed in one step, improving performance", "When cancelling a merge after dragging and dropping, the themes are now unselected"], "fixed": ["Fixed an issue with filtering by tag on the datasets page", "Fixed an issue where the wrong count would be displayed when selecting searched comments", "Fixed an issue with permissions after a trial period expires", "Fixed an issue with search parameters", "Fixed an issue with the scorecard layout changing when in presentation mode, caused by the latest Chrome release", "Fixed an issue with translation", "Fixed an issue where themes could be dragged while editing"]}}, {"id": 30, "version": "6.2.0", "date": "2nd Mar 2022", "changes": {"new": ["You can now drag and drop to merge themes.", "Deleting themes can now be saved to memory - the platform can now learn which themes you don't want to see!"], "improved": ["Improvements to the way we handle API requests between the application and our servers", "When all comments are deleted or removed from a theme, the view will be reset to 'All Comments'", "Search queries set up using 'Edit Memory' are now validated correctly", "When memory is applied to a dataset, the same memory is now carried through and applied to child datasets created from the original dataset", "The theme name is now displayed as part of the custom markers on the SWOT chart", "When merging themes, the subtopics are now combined and carried through to the new theme", "When clicking the 'Select All' themes checkbox, now only the shown themes will be selected"], "fixed": ["Fixed an issue with the SWOT calculations", "Fixed an issue affecting certain scorecards"]}}, {"id": 29, "version": "6.1.1", "date": "8th Feb 2022", "changes": {"fixed": ["Fixed a UI spacing issue when a dataset has pending changes", "Fixed issue with applying translation", "Added an additional timestamp format", "Updated visual issue with scorecard dropdown menus", "Fixed issue with applying memory to datasets requiring translation", "Fixed issue with clearing out unnecessary data"]}}, {"id": 28, "version": "6.1.0", "date": "2nd Feb 2022", "changes": {"new": [{"headline": "We have replaced our existing folder system with a new and improved 'tag' system! You can now be far more flexible with how you organise you data. 🏷️", "changes": ["Apply any number of tags to your datasets, or create new tags to signify projects, data sources, timeframes, or anything else you can think of!", "Filter your datasets by existing tags to only see datasets matching the tag(s)."]}], "improved": ["We have added the ability to store combinations of memory actions as 'Collections', allowing you to define 'separate memory banks' for when you would like to only apply a subset of your saved actions.", "We have improved the performance of our translation feature."]}}, {"id": 27, "version": "6.0.2", "date": "16th Dec 2021", "changes": {"fixed": ["Updated log4j libraries to 2.16.0 to fix CVE-2021-45046", "Fixed issue where storing similar merge actions in memory could cause unwanted effects"]}}, {"id": 26, "version": "6.0.1", "date": "15th Dec 2021", "changes": {"improved": ["Stopped action plans being saved with no actions attached", "Updated presentation slides to remove additional whitespace", "Added the ability to edit illustrative comments on the scorecard", "Added percentages to emotion selection dropdown when editing insights"], "fixed": ["Updated log4j libraries to 2.15.0 to fix CVE-2021-44228 (hotfixed on 10th December)", "Fixed visual issue with scorecard when a metric was included but not selected on an insight"]}}, {"id": 25, "version": "6.0.0", "date": "30th Nov 2021", "changes": {"new": [{"headline": "Emotics is learning! We have added a 'Memory', allowing the platform to learn from and quickly repeat your previous actions. 🧠 ", "changes": ["When renaming, merging or creating themes through search, check the 'Save to Memory' option to store the action in memory.", "Then, when uploading future datasets, you will be able to click the 'Apply all actions in memory' checkbox to automatically apply any previous actions to the new upload.", "Therefore as you use Emotics more and more, it will be customised to your specific behaviours - saving you lots of time repeating manual tasks!"]}], "improved": ["Updated libraries to improve performance", "Improved aggregation flow to avoid regenerating themes when re-analysing a dataset"], "fixed": ["Fixed issue with loading and unloading illustrative comments", "Fixed issue with incorrect error responses", "Fixed issue where SWOT quadrants were assigned incorrectly", "Fixed visual issue with 0% potential change"]}}, {"id": 24, "version": "5.4.2", "date": "26th Oct 2021", "changes": {"new": ["Added system to display and rearrange metadata when viewing comments"], "improved": ["Updated the method for deleting search results from a dataset", "Improved user experience when reassigning comments", "Added expand/collapse behaviour to Emotion Analysis/Theme Analysis charts", "Added functionality to enable pressing the 'Enter' key to advance certain actions across the platform", "Improved translation selection experience"], "fixed": ["Fixed issue with selecting themes on Theme Analysis chart", "Fixed an issue with incorrect illustrative comments on the 'Custom Selection' section of the scorecard", "Fixed an issue with search bar appearance when searching", "Fixed issues around SSE connections", "Fixed error when loading avatar from Gravatar"]}}, {"id": 23, "version": "5.4.1", "date": "22nd Sep 2021", "changes": {"improved": ["Reworked and optimised translation implementation for better performance", "Updated search bar interface - separated the operator and saved search dropdowns to avoid overlap with comments", "Updated 'Create Theme' and 'Create Dataset' buttons to reduce confusion", "Several small UI tweaks"], "fixed": ["Fixed issue with refreshing Qualtrics authentication", "Fixed issue where clicking an already loaded dataset could unload themes and comments"]}}, {"id": 22, "version": "5.4.0", "date": "3rd Sep 2021", "changes": {"new": [{"headline": "Add accountability and visibility to your insights using Insight Action Plans 🙌", "changes": ["Attach an action plan to a scorecard to highlight areas to work on with metric-based desired outcomes.", "Outcome statuses (In Progress/Finished/Cancelled/Failed) allow easy visibility into how the plan is progressing.", "Add an assignee and date to add traceability and track outcome progress over time.", "Export alongside the scorecard using the same Presentation mode to quickly and easily share the plan.", "Plans are saved to the dataset, allowing you to revisit weekly/monthly/quarterly."]}], "improved": ["An overhauled visual layout for the scorecard provides you with the information you need in a much more efficient manner", "Edit the emotion associated with an insight on the scorecard to more easily communicate the point you are making", "A completely redesigned onboarding flow gives new users platform access and knowledge in a far quicker fashion"], "fixed": ["Fixed an issue when creating a theme via search", "Minor SWOT chart fixes"]}}, {"id": 21, "version": "5.3.0", "date": "12th Aug 2021", "changes": {"new": [{"headline": "SWOT Analysis has been completely overhauled to provide a better user experience 🤯", "changes": ["You can now show or hide themes on the chart simply by selecting them in the themes list.", "Hover over themes to see where they would appear on the chart + a redesigned layout makes it easy to work with your themes.", "Add custom markers to a theme to highlight a specific comment or to add a note.", "Export and add your chart to your presentation in just a few clicks using Presentation mode."]}]}}, {"id": 20, "version": "5.2.0", "date": "22nd Jun 2021", "changes": {"new": [{"headline": "Supercharge your searches with the new Comments and Search integration 💪", "changes": ["The Search tab has now been integrated into the Comments page for a smoother experience.", "Combine search terms with filters to create more impactful themes and datasets.", "Easily save and load your searches to save time when working across multiple datasets."]}], "fixed": ["Fixed an issue when creating and loading scorecards", "Fixed an issue with loading certain themes for scorecards", "Fixed a visual issue incorrectly displaying negative length values for expired trials"]}}, {"id": 19, "version": "5.1.0", "date": "11th May 2021", "changes": {"new": [{"headline": "Compare your datasets in just a few clicks 📊", "changes": ["Select two datasets on the 'Datasets' page and see the 'Insights' button change to 'Compare'.", "Use this new view to compare the emotional signature of the datasets in an easily understandable way.", "Check the 'Top Indicators' to see which themes are contributing the most to positive and negative emotions."]}], "improved": ["Turn a theme (or themes) into a dataset by selecting and using the new 'Create' button.", "Greater detail over key area editing - you can now select which emotion and metric is highlighted in the insight text", "Improved performance when creating and editing Insights scorecards"], "fixed": ["Fixed an issue with generating unnecessary subtopics", "Fixed a visual issue for datasets with no subtopics"]}}, {"id": 18, "version": "5.0.4", "date": "25th Mar 2021", "changes": {"improved": ["Improved loading transitions across Emotics to avoid seeing 'No comments found' messages when switching between themes/datasets"], "fixed": ["Fixed display issue which would automatically scroll down the page when switching between charts", "Fixed issue where themes would not load correctly when selecting time periods on the Trend Analysis chart", "Fixed issue where loading datasets through Social Search would not enable Trend Analysis information"]}}, {"id": 17, "version": "5.0.3", "date": "9th Mar 2021", "changes": {"fixed": ["Fixed certain filtering performance issues"]}}, {"id": 16, "version": "5.0.2", "date": "8th Mar 2021", "changes": {"fixed": ["Fixed issue with merging themes containing subtopics"]}}, {"id": 15, "version": "5.0.1", "date": "5th Mar 2021", "changes": {"improved": ["We have improved our theme analysis to combine themes with similar labels. Spend less time combining your themes!", "Performance improvements will allow you to analyse your datasets quicker", "Datasets only containing one column will now automatically select the column as the 'Comments' column when uploading", "Added message popup when deleting datasets"], "fixed": ["Fixed an issue with deleting a custom theme list that is associated with a dataset", "Fixed an issue where custom themes were not being loaded in properly", "Fixed an issue where insights could not be edited on certain datasets"]}}, {"id": 14, "version": "5.0.0", "date": "16th Feb 2021", "changes": {"new": [{"headline": "Slice up your data 🍕", "changes": ["Using our new and improved Uploads screen (with the new dataset previews), bolster your text data with additional data such as age/gender/location/NPS Score/anything else your data may include.", "Use these additional data points and the new Filters panel to uncover new insights within your data!", "Create new datasets using these filters to compare comments, Adorescores and emotions across demographics, timeframes or locations (or a combination of all of these - and more!)"]}], "improved": ["We have added a checkbox on upload to enable excluding the first row from the analysis if it contains headers", "When using custom themes, there is now an option to exclude all Emotics-generated themes", "If your dataset contains multiple timestamp or metric columns, you can now switch between these on the Time Series and Insights panels"], "fixed": ["Fixed an issue where newly created datasets were not picked up as in progress on the sidebar"]}}, {"id": 13, "version": "4.8.1", "date": "18th Dec 2020", "changes": {"improved": ["Small visual changes to the Insights panel"], "fixed": ["Fixed issue where highlights would not update on the Insights panel"]}}, {"id": 12, "version": "4.8.0", "date": "17th Dec 2020", "changes": {"improved": ["Updated correlations display on the Insights panel - the selected correlations to display will now be stored", "Uploaded your metadata column with the wrong name? No problem - the metric name is now editable through the Insights panel!"]}}, {"id": 11, "version": "4.7.0", "date": "14th Dec 2020", "changes": {"new": [{"headline": "Put your data under the microscope 🔬", "changes": ["Use custom themes to bucket your data into repeatable, consistent themes. Define once on upload, then attach to all future datasets that require them. Create from a .csv/.xlsx template, or manually within Emotics.", "Subtopics have been redesigned to be more discoverable and easily investigated. Quickly see how many subtopics a theme has - plus the score and volume information for each, allowing you to get to the core of your insights faster."]}], "improved": ["Redesigned uploads page - see how your usage will be affected before uploading, and when uploading multiple datasets, upload one-by-one in the background while configuring the others", "There is now a column for sorting by potential change when selecting highlights on the Insights panel", "You can now search for comments when selecting an illustrative comment to display on the Insights panel"], "fixed": ["Attempting to use a non-numeric column as the Metadata column will now result in an error - this will fix any issues with the Insights panel if a non-numeric column has been selected", "Fixed an issue on the Theme Analysis page where de-selecting a theme would show the 'All Comments' header, but not return all comments", "Standardised insight text for datasets with and without a Metadata column selected"]}}, {"id": 10, "version": "4.6.2", "date": "18th Nov 2020", "changes": {"improved": ["Updated message when viewing unassigned comments if all comments are assigned to a theme", "Improved database performance", "Updated this very changelog to include further previous changes"], "fixed": ["Fixed an issue where selecting multiple datasets and clicking 'Download Analyses' on the multi-select toolbar was not downloading the analysis files"]}}, {"id": 9, "version": "4.6.1", "date": "16th Nov 2020", "changes": {"new": [{"headline": "Added this changelog! 📖", "changes": ["We've now added this changelog to make it easier to keep up to date with all the new features we've been working hard to release."]}], "fixed": ["Fixed an issue where re-assigning lots of comments was not updating the themes correctly"]}}, {"id": 8, "version": "4.6.0", "date": "12th Nov 2020", "changes": {"new": [{"headline": "Insights updates! 🔎", "changes": ["There is now a selector for which correlations should be displayed on the Insights panel.", "We have also added a search bar so themes can be searched when updating highlights on the Insights panel."]}], "improved": ["Updated correlation boundaries on the Insights panel to now include: None/Very Weak/Weak/Moderate/Strong/Very Strong/Perfect", "Allowed regular SWOT charts to be exported along with the custom charts", "Added an alphabetical theme sort when re-assigning comments"]}}, {"id": 7, "version": "4.5.0", "date": "5th Nov 2020", "changes": {"new": [{"headline": "More visual tweaks!", "changes": ["We have made some changes to the themes panel to make it more useful - including swapping the Adorescore and Volume columns, as well as adding the capability to sort themes alphabetically.", "There have also been visual changes made to the Datasets page when there are no datasets or folders - hopefully making the process smoother for new users."]}], "improved": ["Improved theme analysis - the number of unassigned comments in a dataset should now be lower in most cases", "Added feedback when creating themes or datasets through the Search page - along with links to quickly get back to view the created dataset or theme", "Updated the Insights panel to reduce the time it takes to load"], "fixed": ["Fixed an issue with Qualtrics authentication where the connection was dropping off", "Fixed an issue which caused themes which were not currently selected to be included on the 'Merge Themes' window", "Fixed an issue where comments which were used as illustrative comments on the Insights panel couldn't be deleted"]}}, {"id": 6, "version": "4.4.1", "date": "28th Oct 2020", "changes": {"improved": ["Database maintenance and upgrades"]}}, {"id": 5, "version": "4.4.0", "date": "27th Oct 2020", "changes": {"improved": ["Improved how the themes panel is rendered to increase performance", "Other general performance improvements", "Updated naming conventions in the Emotics public API to mirror Emotics"]}}, {"id": 4, "version": "4.3.0", "date": "22th Oct 2020", "changes": {"improved": ["Added video links to the Starter Guide", "Improved the message displayed when performing an action on a demo dataset that is blocked", "Various other visual improvements"], "fixed": ["Fixed an issue with the Insights panel Presentation view when using Safari", "Disable customising the Insights panel for demo datasets"]}}, {"id": 3, "version": "4.2.0", "date": "6th Oct 2020", "changes": {"new": [{"headline": "Onboarding changes for new users!", "changes": ["We have added several features to help new users get to know the platform - a Starter Guide and a Live Chat button, along with an Upload Guide on the Uploads page.", "Several pages have had small redesigns, including the Login and Sign Up pages."]}], "fixed": ["Fixed display issues with the Insights panel highlights", "Various other Insights panel fixes and improvements"]}}, {"id": 2, "version": "4.1.0", "date": "30th Sep 2020", "changes": {"improved": ["Added default sorting for themes on the Emotion Analysis tab - sorted by highest Adorescore for positive indexes and lowest Adorescore for negative indexes", "Improved user experience when customising the Insights panel"], "fixed": ["Fixed SSE issues causing problems refreshing data across a few sections of Emotics"]}}, {"id": 1, "version": "4.0.0", "date": "23th Sep 2020", "changes": {"new": [{"headline": "Big Insights panel changes, plus a way to see unassigned comments!", "changes": ["Insights panel overhaul - the new and expanded version includes predicted change in associated metrics for highlighted themes, as well as customisable titles/highlights, and a Presentation view to enable quick downloading and sharing!", "A 'View Unassigned' toggle has been added to the Comments page - making it easy to see which comments haven't been assigned to a theme, and to then manually assign if necessary."]}], "improved": ["Re-enabled SWOT background colouring by default"], "fixed": ["Fixed issue with uploading .txt files", "Various Insights panel fixes"]}}]