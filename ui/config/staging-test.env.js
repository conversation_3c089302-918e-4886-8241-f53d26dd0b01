const { merge } = require('webpack-merge');
const prodEnv = require('./prod.env');

module.exports = merge(prodEnv, {
  NODE_ENV: '"development"',
  BASE_URL: '"https://staging-test.emotics.io"',
  API_BASE_ROOT_URL: '"https://staging-api-test.emotics.io"',
  API_BASE_URL: '"https://staging-api-test.emotics.io/api"',
  WS_BASE_URL: '"https://staging-api-test.emotics.io/emotics-websocket"',
  API_SM_URL: '"https://staging-api-test.emotics.io"',
  STRIPE_PK: '"pk_test_S6uKDdrzs3Q2FfNtbjdXiaqX"',
  TIER_LITE_ID: 3,
  TIER_PRO_ID: 4,
  HEART_BEAT_URL: '"https://staging-feedback.adoreboard.com"',
  AMAZON_CLIENT_ID: '"amzn1.application-oa2-client.20ddf0fc080b42ac8c84c7fe188a6977"',
});
