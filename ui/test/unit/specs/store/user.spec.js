// import api from '@/helpers/api';
// import actions from '@/store/actions/user';
// import ActionStatus from '@/enum/action-status';
// import * as ActionTypes from '@/store/action-types';
// import * as MutationTypes from '@/store/mutation-types';
// import testAction from '../../helpers/test-action';

// const userPayload = {
//   firstName: 'Farfisa',
// }

// describe('store/user', () => {
//   before(() => {
//     const testAdapter = config => new Promise((resolve, reject) => {
//       const path = config.url.replace(config.baseURL, '');
//       const token = config.headers.Authorization;
//       const expectedToken = 'Basic ZmFyZmlzYTpNYW5hZ2VyMQ==';

//       if (path === '/user/me' && token === expectedToken) {
//         resolve({
//           data: userPayload,
//           status: 200,
//           statusText: 'OK',
//           headers: {},
//           request: {},
//           config,
//         });
//       } else {
//         reject({});
//       }
//     });

//     api.setAdapter(testAdapter);
//   });

//   describe('actions', () => {
//     it('LOG_IN', (done) => {
//       testAction(actions[ActionTypes.LOG_IN], null, {}, [
//         {
//           type: MutationTypes.SET_LOGIN_STATUS,
//           payload: {
//             status: ActionStatus.LOADING,
//           },
//         },
//         {
//           type: MutationTypes.SET_LOGIN_STATUS,
//           payload: {
//             status: ActionStatus.DONE,
//           },
//         },
//         {
//           type: MutationTypes.SET_USER,
//           payload: userPayload,
//         },
//       ], done);
//     });
//   });
// });
