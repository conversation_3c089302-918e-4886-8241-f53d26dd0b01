import api from '@/helpers/api';

describe('helpers/api', () => {
  before(() => {
    const testAdapter = config => new Promise((resolve, reject) => {
      const path = config.url.replace(config.baseURL, '');

      if (path === '/hello') {
        resolve({
          data: { hello: 'world', path },
          status: 200,
          statusText: 'OK',
          headers: {},
          request: {},
          config,
        });
      } else {
        reject({});
      }
    });

    api.setAdapter(testAdapter);
  });

  it('allows adapter stubbing', (done) => {
    api.instance().get('/hello')
      .then((resp) => {
        expect(resp.data.hello).to.equal('world');
        expect(resp.data.path).to.eq('/hello');
        done();
      })
      .catch(() => {
        done(new Error('unexpected failed request'));
      });
  });

  it('fails for unmatched requests', (done) => {
    api.instance().get('/bar')
      .then(() => {
        done(new Error('unexpected successful request'));
      })
      .catch(() => {
        done();
      });
  });
});
