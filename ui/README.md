# ui

> A Vue.js project

## Build Setup

``` bash
# install dependencies
npm install

# serve with hot reload at localhost:8080
npm run dev

# build for production with minification
npm run build

# build for production and view the bundle analyzer report
npm run build --report

# run unit tests
npm run unit

# run e2e tests
npm run e2e

# run all tests
npm test
```

For detailed explanation on how things work, checkout the [guide](http://vuejs-templates.github.io/webpack/) and [docs for vue-loader](http://vuejs.github.io/vue-loader).

### Running on Windows

If running on windows you may experience line []break issues](http://stackoverflow.com/questions/37826449/expected-linebreaks-to-be-lf-but-found-crlf-linebreak-style-in-eslint-using)

Update the `ui/.eslintrc.js` file as follows: -

    ...

    // add your custom rules here
    'rules': {
      'linebreak-style': ['error', 'windows'],       // <- Add this line
      // don't require .vue extension when importing

      ...

    }],

    ...

