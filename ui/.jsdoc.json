{"tags": {"allowUnknownTags": true, "dictionaries": ["jsdoc"]}, "source": {"include": ["src"], "includePattern": "\\.(vue|js)$", "excludePattern": "(node_modules/|docs)"}, "plugins": ["plugins/markdown", "node_modules/jsdoc-vue"], "templates": {"referenceTitle": "Emotics", "disableSort": false, "collapse": true}, "opts": {"destination": "./docs/", "encoding": "utf8", "private": true, "recurse": true, "template": "./node_modules/tui-jsdoc-template"}}