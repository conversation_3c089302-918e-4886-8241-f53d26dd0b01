#!/bin/bash

if [ $# -eq 0 ]
    then
        echo "Error. Please make sure you've indicated correct parameters";
elif [ $1 == "production" ]
    then
        echo "Running production deploy"
        # Public URL: http://app.emotics.io.s3-website-eu-west-1.amazonaws.com
        BUILD_ENV=prod npm run build && aws s3 cp ./dist s3://app.emotics.io --recursive --acl public-read --region us-east-1
elif [ $1 == "prod-test" ]
    then
        echo "Running prod-test deploy"
        # Public URL: http://app-test.emotics.io.s3-website-eu-west-1.amazonaws.com
        BUILD_ENV=prod-test npm run build && aws s3 cp ./dist s3://app-test.emotics.io --recursive --acl public-read --region us-east-1
elif [ $1 == "consulting" ] || [ $1 == "consultancy" ]
    then
        echo "Running consulting deploy"
        # Public URL: http://consulting.emotics.io.s3-website-eu-west-1.amazonaws.com
        BUILD_ENV=consulting npm run build && aws s3 cp ./dist s3://consulting.emotics.io --recursive --acl public-read --region us-east-1
elif [ $1 == "staging" ]
    then
        echo "Running staging deploy"
        # Public URL: http://staging.emotics.io.s3-website-eu-west-1.amazonaws.com
        BUILD_ENV=staging npm run build && aws s3 cp ./dist s3://staging.emotics.io --recursive --acl public-read --region us-east-1
elif [ $1 == "staging-test" ]
    then
        echo "Running staging-test deploy"
        # Public URL: http://staging-test.emotics.io.s3-website-eu-west-2.amazonaws.com
        BUILD_ENV=staging-test npm run build && aws s3 cp ./dist s3://staging-test.emotics.io --recursive --acl public-read --region eu-west-2
elif [ $1 == "test" ]
    then
        echo "Running staging.test deploy"
        # Public URL: http://staging.test.emotics.io.s3-website-eu-west-2.amazonaws.com
        BUILD_ENV=test npm run build && aws s3 cp ./dist s3://staging.test.emotics.io --recursive --acl public-read --region eu-west-2
elif [ $1 == "local" ]
    then
        echo "Running local deploy build"
        npm run build
fi
